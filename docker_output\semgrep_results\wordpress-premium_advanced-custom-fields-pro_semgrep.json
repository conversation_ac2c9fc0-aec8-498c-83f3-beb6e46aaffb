{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "start": {"line": 196, "col": 12, "offset": 5782}, "end": {"line": 196, "col": 46, "offset": 5816}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "start": {"line": 823, "col": 7, "offset": 21920}, "end": {"line": 823, "col": 35, "offset": 21948}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "start": {"line": 1535, "col": 7, "offset": 39962}, "end": {"line": 1535, "col": 53, "offset": 40008}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "start": {"line": 196, "col": 12, "offset": 5723}, "end": {"line": 196, "col": 46, "offset": 5757}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "start": {"line": 823, "col": 7, "offset": 21861}, "end": {"line": 823, "col": 35, "offset": 21889}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "start": {"line": 1535, "col": 7, "offset": 39903}, "end": {"line": 1535, "col": 53, "offset": 39949}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 182, "col": 37, "offset": 4426}, "end": {"line": 182, "col": 54, "offset": 4443}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 873, "col": 27, "offset": 21995}, "end": {"line": 880, "col": 31, "offset": 22154}, "extra": {"message": "`$g\n\t\t\t\t\t\t\t\t\t\t\t.css(\n\t\t\t\t\t\t\t\t\t\t\t\trtl\n\t\t\t\t\t\t\t\t\t\t\t\t\t? 'marginRight'\n\t\t\t\t\t\t\t\t\t\t\t\t\t: 'marginLeft'\n\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t.toString().replace` method will only replace the first occurrence when used with a string argument ('%'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 1915, "col": 20, "offset": 50923}, "end": {"line": 1915, "col": 45, "offset": 50948}, "extra": {"message": "RegExp() called with a `f` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 1915, "col": 20, "offset": 50923}, "end": {"line": 1915, "col": 45, "offset": 50948}, "extra": {"message": "RegExp() called with a `o` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-helper-functions.php", "start": {"line": 654, "col": 11, "offset": 14795}, "end": {"line": 654, "col": 76, "offset": 14860}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/api-helpers.php", "start": {"line": 3762, "col": 2, "offset": 82900}, "end": {"line": 3762, "col": 72, "offset": 82970}, "extra": {"message": "The function `openssl_decrypt` returns either a string of the decrypted data on success or `false` on failure. If the failure case is not handled, this could lead to undefined behavior in your application. Please handle the case where `openssl_decrypt` returns `false`.", "metadata": {"references": ["https://www.php.net/manual/en/function.openssl-decrypt.php"], "cwe": ["CWE-252: Unchecked Return Value"], "owasp": ["A02:2021 - Cryptographic Failures"], "technology": ["php", "openssl"], "category": "security", "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "shortlink": "https://sg.run/kzn7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.unicode.security.bidi.contains-bidirectional-characters", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_AF.l10n.php", "start": {"line": 2, "col": 11214, "offset": 11219}, "end": {"line": 2, "col": 11217, "offset": 11222}, "extra": {"message": "This code contains bidirectional (bidi) characters. While this is useful for support of right-to-left languages such as Arabic or Hebrew, it can also be used to trick language parsers into executing code in a manner that is different from how it is displayed in code editing and review tools. If this is not what you were expecting, please review this code in an editor that can reveal hidden Unicode characters.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "category": "security", "technology": ["unicode"], "references": ["https://trojansource.codes/"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/generic.unicode.security.bidi.contains-bidirectional-characters", "shortlink": "https://sg.run/nK4r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.unicode.security.bidi.contains-bidirectional-characters", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_IR.l10n.php", "start": {"line": 2, "col": 2117, "offset": 2122}, "end": {"line": 2, "col": 2120, "offset": 2125}, "extra": {"message": "This code contains bidirectional (bidi) characters. While this is useful for support of right-to-left languages such as Arabic or Hebrew, it can also be used to trick language parsers into executing code in a manner that is different from how it is displayed in code editing and review tools. If this is not what you were expecting, please review this code in an editor that can reveal hidden Unicode characters.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "category": "security", "technology": ["unicode"], "references": ["https://trojansource.codes/"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/generic.unicode.security.bidi.contains-bidirectional-characters", "shortlink": "https://sg.run/nK4r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.unicode.security.bidi.contains-bidirectional-characters", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_IR.l10n.php", "start": {"line": 2, "col": 27491, "offset": 27496}, "end": {"line": 2, "col": 27494, "offset": 27499}, "extra": {"message": "This code contains bidirectional (bidi) characters. While this is useful for support of right-to-left languages such as Arabic or Hebrew, it can also be used to trick language parsers into executing code in a manner that is different from how it is displayed in code editing and review tools. If this is not what you were expecting, please review this code in an editor that can reveal hidden Unicode characters.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "category": "security", "technology": ["unicode"], "references": ["https://trojansource.codes/"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/generic.unicode.security.bidi.contains-bidirectional-characters", "shortlink": "https://sg.run/nK4r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js:\n ", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "message": "Timeout when running typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method on downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js:\n ", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js:\n ", "path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js"}], "paths": {"scanned": ["downloaded_repos/wordpress-premium_advanced-custom-fields-pro/.gitignore", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/README.md", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/acf.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/acf-logo.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/acf-pro-logo.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/empty-group.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/empty-post-types.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/empty-taxonomies.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/face-sad.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-preview-grid.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-states/checkbox-active.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-states/checkbox-indeterminate.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-states/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-states/radio-active.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-accordion.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-button-group.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-checkbox.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-clone.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-color-picker.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-date-picker.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-date-time-picker.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-default.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-email.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-file.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-flexible-content.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-gallery.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-google-map.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-group.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-icon-picker.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-image.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-link.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-message.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-number.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-oembed.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-page-link.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-password.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-post-object.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-radio.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-range.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-relationship.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-repeater.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-select.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-tab.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-taxonomy.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-text.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-textarea.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-time-picker.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-true-false.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-url.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-user.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/icon-field-wysiwyg.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-icons/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-accordion.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-button-group.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-checkbox.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-clone.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-color-picker.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-date-picker.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-date-time.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-email.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-file.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-flexible-content.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-gallery.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-google-map.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-group.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-icon-picker.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-image.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-link.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-message.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-number.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-oembed.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-page-link.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-password.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-post-object.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-radio-button.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-range.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-relationship.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-repeater.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-select.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-tabs.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-taxonomy.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-text.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-textarea.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-time.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-true-false.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-url.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-user.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/field-preview-wysiwyg.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/field-type-previews/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icon-upgrade-pro.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-add.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-alert-triangle.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-arrow-left.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-arrow-right.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-arrow-up-right.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-check-circle-solid.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-check.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-chevron-down.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-chevron-left-double.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-chevron-left.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-chevron-right-double.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-chevron-right.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-chevron-up.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-close-circle.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-close.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-copy.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-document.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-dots-grid.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-draggable.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-export.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-extended-menu.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-field-groups.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-fields.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-globe.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-help.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-hidden.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-import.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-info-red.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-info-solid.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-info-white.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-info.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-key-solid.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-key.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-layout.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-lock.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-play.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-post-type.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-regenerate.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-search.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-settings.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-sliders.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-stars.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-taxonomies.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-time.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-tools.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-trash.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-updates.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-warning-alt-red.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-warning-alt.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/icon-warning.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/icons/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/pro-chip-locked.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/pro-chip.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/pro-upgrade-grid-bg.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/pro-upgrade-overlay.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/spinner.gif", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/<EMAIL>", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/wp-engine-horizontal-black.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/images/wp-engine-horizontal-white.svg", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/color-picker-alpha/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/color-picker-alpha/wp-color-picker-alpha.js", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/images/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/images/ui-bg_highlight-soft_0_ffffff_1x100.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/images/ui-icons_444444_256x240.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/images/ui-icons_DDDDDD_256x240.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/images/ui-icons_ffffff_256x240.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/jquery-ui.css", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/datepicker/jquery-ui.min.css", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2-spinner.gif", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.css", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.js", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2x2.png", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.css", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.min.css", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.css", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.min.css", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/composer.json", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/Updater/Updater.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/Updater/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/Updater/init.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-bidirectional-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-field-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-field-group-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-form-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-helper-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-hook-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-input-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-internal-post-type-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-meta-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-post-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-post-type-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-taxonomy-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-user-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-utility-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-value-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/acf-wp-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/admin-internal-post-type-list.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/admin-internal-post-type.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/admin-notices.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/admin-options-pages-preview.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/admin-tools.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/admin-upgrade.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/admin.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/post-types/admin-field-group.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/post-types/admin-field-groups.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/post-types/admin-post-type.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/post-types/admin-post-types.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/post-types/admin-taxonomies.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/post-types/admin-taxonomy.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/post-types/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/tools/class-acf-admin-tool-export.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/tools/class-acf-admin-tool-import.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/tools/class-acf-admin-tool.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/tools/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/conditional-logic.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/field.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/fields.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/list-empty.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/location-group.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/location-rule.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/locations.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/options.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-field-group/pro-features.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-post-type/advanced-settings.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-post-type/basic-settings.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-post-type/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-post-type/list-empty.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-taxonomy/advanced-settings.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-taxonomy/basic-settings.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-taxonomy/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/acf-taxonomy/list-empty.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/browse-fields-modal.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/escaped-html-notice.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/global/form-top.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/global/header.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/global/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/global/navigation.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/options-page-preview.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/tools/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/tools/tools.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/upgrade/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/upgrade/network.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/upgrade/notice.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/admin/views/upgrade/upgrade.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/class-acf-ajax-check-screen.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/class-acf-ajax-local-json-diff.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/class-acf-ajax-query-users.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/class-acf-ajax-query.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/class-acf-ajax-upgrade.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/class-acf-ajax-user-setting.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/class-acf-ajax.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/ajax/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/api-helpers.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/api-template.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/api-term.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/assets.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/class-acf-data.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/class-acf-internal-post-type.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/compatibility.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/deprecated.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-accordion.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-button-group.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-checkbox.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-color_picker.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-date_picker.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-date_time_picker.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-email.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-file.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-google-map.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-group.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-icon_picker.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-image.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-link.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-message.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-number.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-oembed.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-output.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-page_link.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-password.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-post_object.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-radio.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-range.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-relationship.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-select.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-separator.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-tab.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-taxonomy.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-text.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-textarea.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-time_picker.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-true_false.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-url.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-user.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field-wysiwyg.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/class-acf-field.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/fields.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-attachment.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-comment.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-customizer.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-front.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-gutenberg.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-nav-menu.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-post.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-taxonomy.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-user.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/form-widget.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/forms/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/legacy/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/legacy/legacy-locations.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/local-fields.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/local-json.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/local-meta.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/abstract-acf-legacy-location.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/abstract-acf-location.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-attachment.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-comment.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-current-user-role.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-current-user.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-nav-menu-item.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-nav-menu.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-page-parent.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-page-template.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-page-type.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-page.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-post-category.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-post-format.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-post-status.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-post-taxonomy.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-post-template.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-post-type.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-post.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-taxonomy.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-user-form.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-user-role.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/class-acf-location-widget.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/locations.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/loop.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/media.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/post-types/class-acf-field-group.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/post-types/class-acf-post-type.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/post-types/class-acf-taxonomy.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/post-types/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/rest-api/acf-rest-api-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/rest-api/class-acf-rest-api.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/rest-api/class-acf-rest-embed-links.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/rest-api/class-acf-rest-request.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/rest-api/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/rest-api.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/revisions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/third-party.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/upgrades.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/validation.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/walkers/class-acf-walker-taxonomy-field.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/walkers/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/wpml.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ar.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ar.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ar.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-bg_BG.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-bg_BG.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-bg_BG.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ca.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ca.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ca.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-cs_CZ.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-cs_CZ.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-cs_CZ.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-da_DK.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-da_DK.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-da_DK.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_CH.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_CH.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_CH.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_DE.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_DE.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_DE.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_DE_formal.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_DE_formal.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-de_DE_formal.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-el.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-el.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-el.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_CA.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_CA.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_CA.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_GB.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_GB.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_GB.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_ZA.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_ZA.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_ZA.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CL.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CL.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CL.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CO.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CO.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CO.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CR.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CR.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_CR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_EC.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_EC.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_EC.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_ES.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_ES.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_ES.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_MX.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_MX.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_MX.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_VE.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_VE.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-es_VE.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_AF.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_AF.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_AF.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_IR.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_IR.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fa_IR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fi.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fi.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fi.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fr_CA.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fr_CA.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fr_CA.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fr_FR.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fr_FR.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-fr_FR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-gl_ES.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-gl_ES.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-gl_ES.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-gu.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-gu.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-gu.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-he_IL.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-he_IL.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-he_IL.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-hr.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-hr.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-hr.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-hu_HU.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-hu_HU.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-hu_HU.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-id_ID.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-id_ID.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-id_ID.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-it_IT.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-it_IT.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-it_IT.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ja.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ja.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ja.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ko_KR.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ko_KR.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ko_KR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nb_NO.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nb_NO.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nb_NO.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_BE.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_BE.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_BE.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_NL.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_NL.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_NL.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_NL_formal.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_NL_formal.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_NL_formal.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pl_PL.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pl_PL.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pl_PL.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_AO.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_AO.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_AO.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_BR.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_BR.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_BR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_PT.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_PT.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-pt_PT.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ro_RO.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ro_RO.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ro_RO.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ru_RU.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ru_RU.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-ru_RU.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-sk_SK.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-sk_SK.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-sk_SK.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-sv_SE.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-sv_SE.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-sv_SE.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-tr_TR.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-tr_TR.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-tr_TR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-uk.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-uk.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-uk.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-vi.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-vi.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-vi.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_CN.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_CN.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_CN.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_TW.l10n.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_TW.mo", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_TW.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-ar.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-bg_BG.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-ca.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-cs_CZ.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-de_CH.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-de_DE.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-de_DE_formal.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-en_GB.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-es_ES.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-fa_IR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-fi.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-fr_CA.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-fr_FR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-he_IL.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-hr.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-hu_HU.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-id_ID.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-it_IT.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-ja.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-nb_NO.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-nl_BE.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-nl_NL.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-nl_NL_formal.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-pl_PL.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-pt_BR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-pt_PT.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-ro_RO.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-ru_RU.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-sk_SK.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-sv_SE.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-tr_TR.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-uk.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-zh_CN.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf-zh_TW.po", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/acf.pot", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/pro/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/acf-pro.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/acf-ui-options-page-functions.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/admin-options-page.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/admin-updates.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/post-types/admin-ui-options-page.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/post-types/admin-ui-options-pages.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/post-types/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/acf-ui-options-page/advanced-settings.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/acf-ui-options-page/basic-settings.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/acf-ui-options-page/create-options-page-modal.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/acf-ui-options-page/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/acf-ui-options-page/list-empty.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/html-options-page.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/html-settings-updates.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/admin/views/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/blocks.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/fields/class-acf-field-clone.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/fields/class-acf-field-flexible-content.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/fields/class-acf-field-gallery.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/fields/class-acf-field-repeater.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/fields/class-acf-repeater-table.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/fields/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/locations/class-acf-location-block.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/locations/class-acf-location-options-page.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/locations/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/options-page.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/post-types/acf-ui-options-page.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/post-types/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/pro/updates.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/readme.txt", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Blocks/Bindings.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Blocks/index.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Meta/Comment.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Meta/MetaLocation.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Meta/Option.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Meta/Post.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Meta/Term.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Meta/User.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Pro/Forms/WC_Order.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Pro/Meta/WooOrder.php", "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/src/Site_Health/Site_Health.php"], "skipped": [{"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/acf-dark.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/acf-field-group.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/acf-global.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/acf-input.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/pro/acf-pro-field-group.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/pro/acf-pro-input.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/css/pro/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/acf-escaped-html-notice.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/acf-field-group.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/acf-input.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/acf-internal-post-type.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/acf.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/pro/acf-pro-blocks.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/pro/acf-pro-field-group.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/pro/acf-pro-input.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/pro/acf-pro-ui-options-page.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/build/js/pro/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/color-picker-alpha/wp-color-picker-alpha.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8155901432037354, "profiling_times": {"config_time": 6.0964367389678955, "core_time": 39.84167742729187, "ignores_time": 0.002652883529663086, "total_time": 45.9416766166687}, "parsing_time": {"total_time": 12.062743902206421, "per_file_time": {"mean": 0.040343625090991375, "std_dev": 0.020795868870209094}, "very_slow_stats": {"time_ratio": 0.3005791206075168, "count_ratio": 0.010033444816053512}, "very_slow_files": [{"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/api-helpers.php", "ftime": 0.32793688774108887}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "ftime": 1.2672860622406006}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.js", "ftime": 2.030586004257202}]}, "scanning_time": {"total_time": 206.8572278022766, "per_file_time": {"mean": 0.13873724198677162, "std_dev": 1.6357613574550096}, "very_slow_stats": {"time_ratio": 0.6894906934470449, "count_ratio": 0.018108651911468814}, "very_slow_files": [{"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/post-types/class-acf-post-type.php", "ftime": 2.7761640548706055}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/post-types/class-acf-taxonomy.php", "ftime": 2.816822052001953}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_CN.l10n.php", "ftime": 3.468747138977051}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_GB.l10n.php", "ftime": 3.8525390625}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_BE.l10n.php", "ftime": 4.25354790687561}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/api-helpers.php", "ftime": 5.228590965270996}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "ftime": 8.46181607246399}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.js", "ftime": 13.224113941192627}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "ftime": 28.887657165527344}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "ftime": 34.55740213394165}]}, "matching_time": {"total_time": 76.98142051696777, "per_file_and_rule_time": {"mean": 0.08553491168551974, "std_dev": 0.04726623727197759}, "very_slow_stats": {"time_ratio": 0.8158810106977291, "count_ratio": 0.18333333333333332}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 1.0392508506774902}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/includes/api/api-helpers.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 1.099276065826416}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.4756739139556885}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-en_GB.l10n.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 1.549699068069458}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.5623300075531006}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-zh_CN.l10n.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 1.5630450248718262}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/lang/acf-nl_BE.l10n.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 1.6910171508789062}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 1.8234870433807373}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 1.9725370407104492}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/3/select2.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 2.1368801593780518}]}, "tainting_time": {"total_time": 49.4296772480011, "per_def_and_rule_time": {"mean": 0.005033572021181376, "std_dev": 0.004079490722945317}, "very_slow_stats": {"time_ratio": 0.7110394257913178, "count_ratio": 0.007433808553971487}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "fline": 1, "rule_id": "javascript.express.security.express-wkhtml-injection.express-wkhtmltoimage-injection", "time": 1.0211429595947266}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 1.073457956314087}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 1.1058051586151123}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 1.1063079833984375}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.1723899841308594}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 1.2570369243621826}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.3468310832977295}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/timepicker/jquery-ui-timepicker-addon.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 1.3755161762237549}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 2.6027369499206543}, {"fpath": "downloaded_repos/wordpress-premium_advanced-custom-fields-pro/assets/inc/select2/4/select2.full.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 3.4348671436309814}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1093763648}, "engine_requested": "OSS", "skipped_rules": []}