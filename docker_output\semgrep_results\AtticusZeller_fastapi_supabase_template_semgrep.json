{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/.env", "start": {"line": 22, "col": 14, "offset": 488}, "end": {"line": 22, "col": 135, "offset": 609}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/Dockerfile", "start": {"line": 51, "col": 1, "offset": 1716}, "end": {"line": 51, "col": 54, "offset": 1769}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"fastapi\", \"run\", \"app/main.py\", \"--port\", \"80\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml", "start": {"line": 34, "col": 19, "offset": 662}, "end": {"line": 34, "col": 22, "offset": 665}}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml", "start": {"line": 36, "col": 18, "offset": 662}, "end": {"line": 36, "col": 21, "offset": 665}}]], "message": "Syntax error at line downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml:34:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml", "spans": [{"file": "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml", "start": {"line": 34, "col": 19, "offset": 662}, "end": {"line": 34, "col": 22, "offset": 665}}, {"file": "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml", "start": {"line": 36, "col": 18, "offset": 662}, "end": {"line": 36, "col": 21, "offset": 665}}]}], "paths": {"scanned": ["downloaded_repos/AtticusZeller_fastapi_supabase_template/.env", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/CODEOWNERS", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/dependabot.yml", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.gitignore", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.pre-commit-config.yaml", "downloaded_repos/AtticusZeller_fastapi_supabase_template/.vscode/launch.json", "downloaded_repos/AtticusZeller_fastapi_supabase_template/CHANGELOG.md", "downloaded_repos/AtticusZeller_fastapi_supabase_template/LICENSE", "downloaded_repos/AtticusZeller_fastapi_supabase_template/README.md", "downloaded_repos/AtticusZeller_fastapi_supabase_template/assets/fastapi-supabase-template-for-llm.png", "downloaded_repos/AtticusZeller_fastapi_supabase_template/assets/logo.png", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/.dockerignore", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/Dockerfile", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/alembic.ini", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/alembic/README", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/alembic/env.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/alembic/script.py.mako", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/alembic/versions/2c0516590c18_initial_commit.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/api/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/api/deps.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/api/main.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/api/routes/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/api/routes/items.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/api/routes/utils.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/core/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/core/auth.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/core/config.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/core/db.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/crud/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/crud/base.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/crud/crud_item.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/main.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/models/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/models/base.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/models/item.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/models/user.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/schemas/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/schemas/auth.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/services/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/utils/__init__.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/utils/init_data.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/app/utils/test_pre_start.py", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/pyproject.toml", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/scripts/format.sh", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/scripts/lint.sh", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/scripts/pre-start.sh", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/scripts/test.sh", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/scripts/tests-start.sh", "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/uv.lock", "downloaded_repos/AtticusZeller_fastapi_supabase_template/pyproject.toml", "downloaded_repos/AtticusZeller_fastapi_supabase_template/scripts/bump.sh", "downloaded_repos/AtticusZeller_fastapi_supabase_template/scripts/update-env.sh", "downloaded_repos/AtticusZeller_fastapi_supabase_template/supabase/.gitignore", "downloaded_repos/AtticusZeller_fastapi_supabase_template/supabase/config.toml", "downloaded_repos/AtticusZeller_fastapi_supabase_template/uv.lock"], "skipped": [{"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/.github/workflows/main.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/api/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/api/api_v1/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/api/api_v1/test_items.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/api/api_v1/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/crud/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/crud/test_item.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/test_main.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AtticusZeller_fastapi_supabase_template/backend/tests/utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.795151948928833, "profiling_times": {"config_time": 6.179802179336548, "core_time": 2.7880287170410156, "ignores_time": 0.0020880699157714844, "total_time": 8.970849990844727}, "parsing_time": {"total_time": 0.3651294708251953, "per_file_time": {"mean": 0.009362294123722957, "std_dev": 0.00011582322599432517}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.141991376876831, "per_file_time": {"mean": 0.013643257177559437, "std_dev": 0.0010530327363377928}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.2699711322784424, "per_file_and_rule_time": {"mean": 0.0009676384669478224, "std_dev": 3.7450588475973614e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.04406380653381348, "per_def_and_rule_time": {"mean": 0.0002059056380084742, "std_dev": 1.1841838186474081e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}