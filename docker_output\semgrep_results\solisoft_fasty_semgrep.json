{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/solisoft_fasty/docker-compose.yml", "start": {"line": 57, "col": 3, "offset": 1086}, "end": {"line": 57, "col": 11, "offset": 1094}, "extra": {"message": "Service 'arangodb' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/solisoft_fasty/docker-compose.yml", "start": {"line": 57, "col": 3, "offset": 1086}, "end": {"line": 57, "col": 11, "offset": 1094}, "extra": {"message": "Service 'arangodb' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 8, "col": 5, "offset": 363}, "end": {"line": 8, "col": 130, "offset": 488}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 9, "col": 5, "offset": 493}, "end": {"line": 9, "col": 118, "offset": 606}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 10, "col": 5, "offset": 611}, "end": {"line": 10, "col": 100, "offset": 706}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 71, "col": 5, "offset": 3287}, "end": {"line": 71, "col": 95, "offset": 3377}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 72, "col": 5, "offset": 3382}, "end": {"line": 72, "col": 98, "offset": 3475}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 75, "col": 5, "offset": 3575}, "end": {"line": 75, "col": 99, "offset": 3669}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 80, "col": 5, "offset": 4416}, "end": {"line": 80, "col": 109, "offset": 4520}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 81, "col": 5, "offset": 4525}, "end": {"line": 81, "col": 117, "offset": 4637}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 118, "col": 5, "offset": 5630}, "end": {"line": 118, "col": 84, "offset": 5709}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 119, "col": 5, "offset": 5714}, "end": {"line": 119, "col": 101, "offset": 5810}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "start": {"line": 120, "col": 5, "offset": 5815}, "end": {"line": 120, "col": 92, "offset": 5902}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "start": {"line": 450, "col": 11, "offset": 20014}, "end": {"line": 450, "col": 47, "offset": 20050}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/login.html", "start": {"line": 7, "col": 5, "offset": 184}, "end": {"line": 7, "col": 112, "offset": 291}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/login.html", "start": {"line": 13, "col": 5, "offset": 474}, "end": {"line": 13, "col": 103, "offset": 572}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/login.html", "start": {"line": 14, "col": 5, "offset": 577}, "end": {"line": 14, "col": 109, "offset": 681}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/app/login.html", "start": {"line": 15, "col": 5, "offset": 686}, "end": {"line": 15, "col": 98, "offset": 779}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/main.js", "start": {"line": 133, "col": 5, "offset": 4404}, "end": {"line": 133, "col": 30, "offset": 4429}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "references": ["https://en.wikipedia.org/wiki/Mass_assignment_vulnerability", "https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html"], "category": "security", "technology": ["express"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.express.security.express-data-exfiltration.express-data-exfiltration", "shortlink": "https://sg.run/pkpL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 231, "col": 35, "offset": 7066}, "end": {"line": 231, "col": 76, "offset": 7107}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "references": ["https://en.wikipedia.org/wiki/Mass_assignment_vulnerability", "https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html"], "category": "security", "technology": ["express"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.express.security.express-data-exfiltration.express-data-exfiltration", "shortlink": "https://sg.run/pkpL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 231, "col": 35, "offset": 7066}, "end": {"line": 231, "col": 76, "offset": 7107}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 282, "col": 35, "offset": 8812}, "end": {"line": 282, "col": 76, "offset": 8853}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "references": ["https://en.wikipedia.org/wiki/Mass_assignment_vulnerability", "https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html"], "category": "security", "technology": ["express"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.express.security.express-data-exfiltration.express-data-exfiltration", "shortlink": "https://sg.run/pkpL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 282, "col": 35, "offset": 8812}, "end": {"line": 282, "col": 76, "offset": 8853}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 323, "col": 35, "offset": 10167}, "end": {"line": 323, "col": 76, "offset": 10208}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "references": ["https://en.wikipedia.org/wiki/Mass_assignment_vulnerability", "https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html"], "category": "security", "technology": ["express"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.express.security.express-data-exfiltration.express-data-exfiltration", "shortlink": "https://sg.run/pkpL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 323, "col": 35, "offset": 10167}, "end": {"line": 323, "col": 76, "offset": 10208}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 416, "col": 53, "offset": 14006}, "end": {"line": 416, "col": 62, "offset": 14015}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 434, "col": 7, "offset": 14500}, "end": {"line": 434, "col": 103, "offset": 14596}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 489, "col": 44, "offset": 16446}, "end": {"line": 489, "col": 58, "offset": 16460}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 507, "col": 7, "offset": 16988}, "end": {"line": 507, "col": 83, "offset": 17064}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 551, "col": 46, "offset": 18655}, "end": {"line": 551, "col": 60, "offset": 18669}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 564, "col": 7, "offset": 19051}, "end": {"line": 564, "col": 145, "offset": 19189}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 625, "col": 42, "offset": 21355}, "end": {"line": 625, "col": 52, "offset": 21365}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 627, "col": 41, "offset": 21428}, "end": {"line": 627, "col": 50, "offset": 21437}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 646, "col": 7, "offset": 21986}, "end": {"line": 646, "col": 69, "offset": 22048}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "start": {"line": 672, "col": 5, "offset": 23072}, "end": {"line": 672, "col": 69, "offset": 23136}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/main.js", "start": {"line": 94, "col": 53, "offset": 3024}, "end": {"line": 94, "col": 62, "offset": 3033}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 8, "col": 5, "offset": 363}, "end": {"line": 8, "col": 130, "offset": 488}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 9, "col": 5, "offset": 493}, "end": {"line": 9, "col": 118, "offset": 606}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 10, "col": 5, "offset": 611}, "end": {"line": 10, "col": 100, "offset": 706}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 71, "col": 5, "offset": 3271}, "end": {"line": 71, "col": 95, "offset": 3361}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 72, "col": 5, "offset": 3366}, "end": {"line": 72, "col": 98, "offset": 3459}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 75, "col": 5, "offset": 3543}, "end": {"line": 75, "col": 99, "offset": 3637}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 80, "col": 5, "offset": 4384}, "end": {"line": 80, "col": 109, "offset": 4488}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 81, "col": 5, "offset": 4493}, "end": {"line": 81, "col": 117, "offset": 4605}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 118, "col": 5, "offset": 5598}, "end": {"line": 118, "col": 84, "offset": 5677}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 119, "col": 5, "offset": 5682}, "end": {"line": 119, "col": 101, "offset": 5778}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "start": {"line": 120, "col": 5, "offset": 5783}, "end": {"line": 120, "col": 92, "offset": 5870}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js", "start": {"line": 14973, "col": 37, "offset": 431940}, "end": {"line": 14973, "col": 42, "offset": 431945}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js.map", "start": {"line": 1, "col": 544355, "offset": 544354}, "end": {"line": 1, "col": 544360, "offset": 544359}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/login.html", "start": {"line": 7, "col": 5, "offset": 184}, "end": {"line": 7, "col": 112, "offset": 291}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/login.html", "start": {"line": 13, "col": 5, "offset": 458}, "end": {"line": 13, "col": 103, "offset": 556}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/login.html", "start": {"line": 14, "col": 5, "offset": 561}, "end": {"line": 14, "col": 109, "offset": 665}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/solisoft_fasty/foxxy/public/login.html", "start": {"line": 15, "col": 5, "offset": 670}, "end": {"line": 15, "col": 98, "offset": 763}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.nginx.security.insecure-redirect.insecure-redirect", "path": "downloaded_repos/solisoft_fasty/nginx.conf", "start": {"line": 45, "col": 7, "offset": 853}, "end": {"line": 45, "col": 37, "offset": 883}, "extra": {"message": "Detected an insecure redirect in this nginx configuration. If no scheme is specified, nginx will forward the request with the incoming scheme. This could result in unencrypted communications. To fix this, include the 'https' scheme.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "category": "security", "technology": ["nginx"], "confidence": "LOW", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/generic.nginx.security.insecure-redirect.insecure-redirect", "shortlink": "https://sg.run/8y14"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.nginx.security.insecure-redirect.insecure-redirect", "path": "downloaded_repos/solisoft_fasty/nginx.conf", "start": {"line": 46, "col": 7, "offset": 891}, "end": {"line": 46, "col": 52, "offset": 936}, "extra": {"message": "Detected an insecure redirect in this nginx configuration. If no scheme is specified, nginx will forward the request with the incoming scheme. This could result in unencrypted communications. To fix this, include the 'https' scheme.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "category": "security", "technology": ["nginx"], "confidence": "LOW", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/generic.nginx.security.insecure-redirect.insecure-redirect", "shortlink": "https://sg.run/8y14"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.nginx.security.header-redefinition.header-redefinition", "path": "downloaded_repos/solisoft_fasty/nginx/nginx.conf", "start": {"line": 45, "col": 7, "offset": 1597}, "end": {"line": 45, "col": 56, "offset": 1646}, "extra": {"message": "The 'add_header' directive is called in a 'location' block after headers have been set at the server block. Calling 'add_header' in the location block will actually overwrite the headers defined in the server block, no matter which headers are set. To fix this, explicitly set all headers or set all headers in the server block.", "metadata": {"cwe": ["CWE-16: CWE CATEGORY: Configuration"], "references": ["https://github.com/yandex/gixy/blob/master/docs/en/plugins/addheaderredefinition.md"], "category": "security", "technology": ["nginx"], "confidence": "LOW", "owasp": ["A06:2017 - Security Misconfiguration", "A05:2021 - Security Misconfiguration"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/generic.nginx.security.header-redefinition.header-redefinition", "shortlink": "https://sg.run/Lwl7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/solisoft_fasty/shared/docker-compose.yml", "start": {"line": 38, "col": 3, "offset": 684}, "end": {"line": 38, "col": 11, "offset": 692}, "extra": {"message": "Service 'cms_prod' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/solisoft_fasty/shared/docker-compose.yml", "start": {"line": 38, "col": 3, "offset": 684}, "end": {"line": 38, "col": 11, "offset": 692}, "extra": {"message": "Service 'cms_prod' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/solisoft_fasty/shared/docker-compose.yml", "start": {"line": 55, "col": 3, "offset": 1061}, "end": {"line": 55, "col": 11, "offset": 1069}, "extra": {"message": "Service 'arangodb' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/solisoft_fasty/shared/docker-compose.yml", "start": {"line": 55, "col": 3, "offset": 1061}, "end": {"line": 55, "col": 11, "offset": 1069}, "extra": {"message": "Service 'arangodb' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/solisoft_fasty/shared/docker-compose.yml", "start": {"line": 70, "col": 3, "offset": 1392}, "end": {"line": 70, "col": 8, "offset": 1397}, "extra": {"message": "Service 'nginx' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/solisoft_fasty/shared/docker-compose.yml", "start": {"line": 70, "col": 3, "offset": 1392}, "end": {"line": 70, "col": 8, "offset": 1397}, "extra": {"message": "Service 'nginx' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/solisoft_fasty/foxxy/public/js/js.js:\n ", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/js.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/solisoft_fasty/foxxy/public/js/js.js:\n ", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/js.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/solisoft_fasty/foxxy/public/js/js.js:\n ", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/js.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js:\n ", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js:\n ", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js:\n ", "path": "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "message": "Timeout when running javascript.browser.security.raw-html-concat.raw-html-concat on downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "message": "Timeout when running javascript.browser.security.raw-html-concat.raw-html-concat on downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "message": "Timeout when running javascript.browser.security.raw-html-concat.raw-html-concat on downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "message": "Timeout when running javascript.browser.security.raw-html-concat.raw-html-concat on downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-response.tainted-html-response on downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js:\n ", "path": "downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/solisoft_fasty/Dockerfile", "start": {"line": 2, "col": 15, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 51}}]], "message": "Syntax error at line downloaded_repos/solisoft_fasty/Dockerfile:2:\n `Bonnaure <<EMAIL>>\nARG DEBIAN_FRONTEND` was unexpected", "path": "downloaded_repos/solisoft_fasty/Dockerfile", "spans": [{"file": "downloaded_repos/solisoft_fasty/Dockerfile", "start": {"line": 2, "col": 15, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 51}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/solisoft_fasty/deploy-cms.sample", "start": {"line": 2, "col": 22, "offset": 0}, "end": {"line": 2, "col": 24, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/solisoft_fasty/deploy-cms.sample:2:\n `-t` was unexpected", "path": "downloaded_repos/solisoft_fasty/deploy-cms.sample", "spans": [{"file": "downloaded_repos/solisoft_fasty/deploy-cms.sample", "start": {"line": 2, "col": 22, "offset": 0}, "end": {"line": 2, "col": 24, "offset": 2}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/solisoft_fasty/shared/Dockerfile", "start": {"line": 2, "col": 15, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 51}}]], "message": "Syntax error at line downloaded_repos/solisoft_fasty/shared/Dockerfile:2:\n `Bonnaure <<EMAIL>>\nARG DEBIAN_FRONTEND` was unexpected", "path": "downloaded_repos/solisoft_fasty/shared/Dockerfile", "spans": [{"file": "downloaded_repos/solisoft_fasty/shared/Dockerfile", "start": {"line": 2, "col": 15, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 51}}]}], "paths": {"scanned": ["downloaded_repos/solisoft_fasty/.arangodb.yml", "downloaded_repos/solisoft_fasty/.circleci/config.yml", "downloaded_repos/solisoft_fasty/.gitignore", "downloaded_repos/solisoft_fasty/CODE_OF_CONDUCT.md", "downloaded_repos/solisoft_fasty/Dockerfile", "downloaded_repos/solisoft_fasty/Dockerfile-armv8", "downloaded_repos/solisoft_fasty/Dockerfile_node", "downloaded_repos/solisoft_fasty/LICENSE", "downloaded_repos/solisoft_fasty/Readme.md", "downloaded_repos/solisoft_fasty/app.moon", "downloaded_repos/solisoft_fasty/applications/assets.moon", "downloaded_repos/solisoft_fasty/applications/services.moon", "downloaded_repos/solisoft_fasty/applications/uploads.moon", "downloaded_repos/solisoft_fasty/config.moon.sample", "downloaded_repos/solisoft_fasty/config.moon.test", "downloaded_repos/solisoft_fasty/cypress/cypress.json", "downloaded_repos/solisoft_fasty/cypress/fixtures/example.json", "downloaded_repos/solisoft_fasty/cypress/integration/admin/apis_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/application_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/aql_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/auth.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/component_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/datatypes_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/helper_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/layout_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/need_a_db.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/page_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/partial_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/redirection_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/script_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/trad_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/admin/user_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/frontend/lang_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/frontend/partial_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/frontend/settings_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/frontend/splat_spec.js", "downloaded_repos/solisoft_fasty/cypress/integration/frontend/tr_spec.js", "downloaded_repos/solisoft_fasty/cypress/plugins/index.js", "downloaded_repos/solisoft_fasty/cypress/support/commands.js", "downloaded_repos/solisoft_fasty/cypress/support/index.js", "downloaded_repos/solisoft_fasty/cypress.json", "downloaded_repos/solisoft_fasty/deploy-all", "downloaded_repos/solisoft_fasty/deploy-cms.sample", "downloaded_repos/solisoft_fasty/deploy-foxxy", "downloaded_repos/solisoft_fasty/docker-compose.yml", "downloaded_repos/solisoft_fasty/foxxy/.arangodb.yml", "downloaded_repos/solisoft_fasty/foxxy/.foxxy.yml", "downloaded_repos/solisoft_fasty/foxxy/.ruby-gemset", "downloaded_repos/solisoft_fasty/foxxy/.ruby-version", "downloaded_repos/solisoft_fasty/foxxy/app/assets/fonts/.keep", "downloaded_repos/solisoft_fasty/foxxy/app/assets/img/.keep", "downloaded_repos/solisoft_fasty/foxxy/app/assets/img/ArangoDB-logo-bg.svg", "downloaded_repos/solisoft_fasty/foxxy/app/assets/img/icons.svg", "downloaded_repos/solisoft_fasty/foxxy/app/assets/img/lapis.jpg", "downloaded_repos/solisoft_fasty/foxxy/app/assets/img/logo.png", "downloaded_repos/solisoft_fasty/foxxy/app/assets/img/square.svg", "downloaded_repos/solisoft_fasty/foxxy/app/css/.keep", "downloaded_repos/solisoft_fasty/foxxy/app/css/css.scss", "downloaded_repos/solisoft_fasty/foxxy/app/index.html", "downloaded_repos/solisoft_fasty/foxxy/app/js/common.js", "downloaded_repos/solisoft_fasty/foxxy/app/js/config.js.sample", "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "downloaded_repos/solisoft_fasty/foxxy/app/js/js.js", "downloaded_repos/solisoft_fasty/foxxy/app/login.html", "downloaded_repos/solisoft_fasty/foxxy/app/vendors/.keep", "downloaded_repos/solisoft_fasty/foxxy/app/vendors/select2.min.css", "downloaded_repos/solisoft_fasty/foxxy/app/vendors/trumbowyg.min.css", "downloaded_repos/solisoft_fasty/foxxy/app/widgets/.keep", "downloaded_repos/solisoft_fasty/foxxy/app/widgets/datasets.html.tag", "downloaded_repos/solisoft_fasty/foxxy/app/widgets/loading.html.tag", "downloaded_repos/solisoft_fasty/foxxy/app/widgets/login.html.tag", "downloaded_repos/solisoft_fasty/foxxy/app/widgets/settings.html.tag", "downloaded_repos/solisoft_fasty/foxxy/app/widgets/uploads.html.tag", "downloaded_repos/solisoft_fasty/foxxy/brunch-config.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/.keep", "downloaded_repos/solisoft_fasty/foxxy/foxx/api/APP/README.md", "downloaded_repos/solisoft_fasty/foxxy/foxx/api/APP/main.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/api/APP/manifest.json", "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/README.md", "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/main.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/manifest.json", "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/scripts/lost-password.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/scripts/send-mail.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/scripts/setup.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/dashboard/APP/README.md", "downloaded_repos/solisoft_fasty/foxxy/foxx/dashboard/APP/main.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/dashboard/APP/manifest.json", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/README.md", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/manifest.json", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/apis.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/aqls.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/components.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/datatypes.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/helpers.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/layouts.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/pages.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/partials.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/redirections.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/scripts.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/spas.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/trads.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/users.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models/widgets.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/models.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/routes/folders.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/scripts/setup.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/config.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/info.txt", "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/main.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/manifest.json", "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/model.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/scripts/setup.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/scripts/teardown.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/README.md", "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/main.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/manifest.json", "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/scripts/riot.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/scripts/setup.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/scripts/tailwindcss.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/scripts/teardown.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/uploads/APP/main.js", "downloaded_repos/solisoft_fasty/foxxy/foxx/uploads/APP/manifest.json", "downloaded_repos/solisoft_fasty/foxxy/foxx/uploads/APP/scripts/setup.js", "downloaded_repos/solisoft_fasty/foxxy/package-lock.json", "downloaded_repos/solisoft_fasty/foxxy/package.json", "downloaded_repos/solisoft_fasty/foxxy/public/css/css.css", "downloaded_repos/solisoft_fasty/foxxy/public/css/css.css.map", "downloaded_repos/solisoft_fasty/foxxy/public/css/vendors.css", "downloaded_repos/solisoft_fasty/foxxy/public/css/vendors.css.map", "downloaded_repos/solisoft_fasty/foxxy/public/img/ArangoDB-logo-bg.svg", "downloaded_repos/solisoft_fasty/foxxy/public/img/icons.svg", "downloaded_repos/solisoft_fasty/foxxy/public/img/lapis.jpg", "downloaded_repos/solisoft_fasty/foxxy/public/img/logo.png", "downloaded_repos/solisoft_fasty/foxxy/public/img/square.svg", "downloaded_repos/solisoft_fasty/foxxy/public/index.html", "downloaded_repos/solisoft_fasty/foxxy/public/js/js.js", "downloaded_repos/solisoft_fasty/foxxy/public/js/js.js.map", "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js", "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js.map", "downloaded_repos/solisoft_fasty/foxxy/public/login.html", "downloaded_repos/solisoft_fasty/foxxy/yarn.lock", "downloaded_repos/solisoft_fasty/lib/aqls.moon", "downloaded_repos/solisoft_fasty/lib/arango.moon", "downloaded_repos/solisoft_fasty/lib/basic_auth.moon", "downloaded_repos/solisoft_fasty/lib/cache.moon", "downloaded_repos/solisoft_fasty/lib/concerns.moon", "downloaded_repos/solisoft_fasty/lib/http_client.moon", "downloaded_repos/solisoft_fasty/lib/service.moon", "downloaded_repos/solisoft_fasty/lib/utils.moon", "downloaded_repos/solisoft_fasty/logs/.keepme", "downloaded_repos/solisoft_fasty/mime.types", "downloaded_repos/solisoft_fasty/nginx/nginx.conf", "downloaded_repos/solisoft_fasty/nginx.conf", "downloaded_repos/solisoft_fasty/package-lock.json", "downloaded_repos/solisoft_fasty/package.json", "downloaded_repos/solisoft_fasty/patch/lapis/nginx/cache.lua", "downloaded_repos/solisoft_fasty/print/.gitkeep", "downloaded_repos/solisoft_fasty/production_setup/deploy.sh", "downloaded_repos/solisoft_fasty/run_test.sh", "downloaded_repos/solisoft_fasty/server_setup/install.sh", "downloaded_repos/solisoft_fasty/setup_test.js", "downloaded_repos/solisoft_fasty/shared/Dockerfile", "downloaded_repos/solisoft_fasty/shared/config.moon", "downloaded_repos/solisoft_fasty/shared/docker-compose.yml", "downloaded_repos/solisoft_fasty/spec/page_spec.moon", "downloaded_repos/solisoft_fasty/static/.keep", "downloaded_repos/solisoft_fasty/static/admin/css/css-08abe541.css", "downloaded_repos/solisoft_fasty/static/admin/css/vendors-01b1908d.css", "downloaded_repos/solisoft_fasty/static/admin/img/ArangoDB-logo-bg.svg", "downloaded_repos/solisoft_fasty/static/admin/img/icons.svg", "downloaded_repos/solisoft_fasty/static/admin/img/lapis.jpg", "downloaded_repos/solisoft_fasty/static/admin/img/logo.png", "downloaded_repos/solisoft_fasty/static/admin/img/square.svg", "downloaded_repos/solisoft_fasty/static/admin/index.html", "downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js", "downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js", "downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js", "downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js", "downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js", "downloaded_repos/solisoft_fasty/static/admin/login.html", "downloaded_repos/solisoft_fasty/static/cache/.keep", "downloaded_repos/solisoft_fasty/sync/Readme.md", "downloaded_repos/solisoft_fasty/sync/bin/fasty", "downloaded_repos/solisoft_fasty/sync/history.md", "downloaded_repos/solisoft_fasty/sync/package.json", "downloaded_repos/solisoft_fasty/views/error_404.etlua", "downloaded_repos/solisoft_fasty/views/error_500.etlua", "downloaded_repos/solisoft_fasty/views/need_a_db.etlua"], "skipped": [{"path": "downloaded_repos/solisoft_fasty/Dockerfile", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/deploy-cms.sample", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/app/vendors/Sortable.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/css/css-08abe541.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/css/vendors-01b1908d.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/img/ArangoDB-logo-bg.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/img/icons.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/img/lapis.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/img/logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/img/square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/js/js-1ed79515.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/js/vendors-32482fc6.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/dist/login.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/foxx/api/APP/test/check.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/foxx/auth/APP/test/example.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/foxx/dashboard/APP/test/example.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/foxx/settings/APP/test/example.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/foxx/sync/APP/test/example.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/foxx/uploads/APP/test/example.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/public/js/js.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/shared/Dockerfile", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.200910806655884, "profiling_times": {"config_time": 7.560245990753174, "core_time": 34.532103538513184, "ignores_time": 0.002610445022583008, "total_time": 42.09684705734253}, "parsing_time": {"total_time": 3.067660331726074, "per_file_time": {"mean": 0.03098646799723307, "std_dev": 0.004532871078731888}, "very_slow_stats": {"time_ratio": 0.14916093481623105, "count_ratio": 0.010101010101010102}, "very_slow_files": [{"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "ftime": 0.45757508277893066}]}, "scanning_time": {"total_time": 169.24442148208618, "per_file_time": {"mean": 0.347524479429335, "std_dev": 7.582896669360037}, "very_slow_stats": {"time_ratio": 0.9161309359614425, "count_ratio": 0.022587268993839837}, "very_slow_files": [{"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "ftime": 4.980938196182251}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/public/js/js.js", "ftime": 5.124847173690796}, {"fpath": "downloaded_repos/solisoft_fasty/static/admin/js/vendors-32482fc6.js", "ftime": 5.214359998703003}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/public/js/vendors.js", "ftime": 5.331099033355713}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "ftime": 5.502707004547119}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/common.js", "ftime": 7.780198097229004}, {"fpath": "downloaded_repos/solisoft_fasty/static/admin/js/js-7ced94dd.js", "ftime": 29.251035928726196}, {"fpath": "downloaded_repos/solisoft_fasty/static/admin/js/js-1ed79515.js", "ftime": 29.80397891998291}, {"fpath": "downloaded_repos/solisoft_fasty/static/admin/js/js-42f57899.js", "ftime": 29.980286836624146}, {"fpath": "downloaded_repos/solisoft_fasty/static/admin/js/js-a4d6b834.js", "ftime": 30.02461314201355}]}, "matching_time": {"total_time": 16.196511268615723, "per_file_and_rule_time": {"mean": 0.024917709644024188, "std_dev": 0.021123688365805617}, "very_slow_stats": {"time_ratio": 0.6991185680168952, "count_ratio": 0.04}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/solisoft_fasty/cypress/integration/admin/datatypes_spec.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2539188861846924}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/api/APP/main.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.28067994117736816}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.34256601333618164}, {"fpath": "downloaded_repos/solisoft_fasty/cypress/support/commands.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.426724910736084}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.5155520439147949}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.7190799713134766}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/common.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.7413270473480225}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/common.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.9522428512573242}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.4334180355072021}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/common.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 2.9537241458892822}]}, "tainting_time": {"total_time": 6.6250269412994385, "per_def_and_rule_time": {"mean": 0.029842463699547024, "std_dev": 0.010944450704382625}, "very_slow_stats": {"time_ratio": 0.6985080671702119, "count_ratio": 0.0990990990990991}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.13498497009277344}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.138962984085083}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.14001083374023438}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/foxx/datasets/index.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.17707395553588867}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.1947460174560547}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.20790314674377441}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.21799087524414062}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/common.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.3795938491821289}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.6119339466094971}, {"fpath": "downloaded_repos/solisoft_fasty/foxxy/app/js/editor.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 1.319336175918579}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}