{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/modxcms_fred/_static/notify.html", "start": {"line": 12, "col": 5, "offset": 325}, "end": {"line": 12, "col": 97, "offset": 417}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/modxcms_fred/_static/notify.html", "start": {"line": 403, "col": 1, "offset": 21780}, "end": {"line": 403, "col": 63, "offset": 21842}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/utils/griddraganddrop.js", "start": {"line": 51, "col": 13, "offset": 2125}, "end": {"line": 51, "col": 87, "offset": 2199}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/modxcms_fred/assets/components/fred/web/elfinder/index.html", "start": {"line": 9, "col": 5, "offset": 251}, "end": {"line": 9, "col": 113, "offset": 359}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/modxcms_fred/core/components/fred/controllers/theme/download.class.php", "start": {"line": 57, "col": 26, "offset": 1743}, "end": {"line": 57, "col": 39, "offset": 1756}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/modxcms_fred/core/components/fred/controllers/theme/download.class.php", "start": {"line": 61, "col": 43, "offset": 1885}, "end": {"line": 61, "col": 56, "offset": 1898}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/modxcms_fred/core/components/fred/controllers/theme/download.class.php", "start": {"line": 71, "col": 75, "offset": 2306}, "end": {"line": 71, "col": 88, "offset": 2319}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/modxcms_fred/core/components/fred/controllers/theme/download.class.php", "start": {"line": 78, "col": 60, "offset": 2731}, "end": {"line": 78, "col": 73, "offset": 2744}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "path": "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax.php", "start": {"line": 60, "col": 29, "offset": 1523}, "end": {"line": 60, "col": 39, "offset": 1533}, "extra": {"message": "<- A new object is created where the class name is based on user input. This could lead to remote code execution, as it allows to instantiate any class in the application.", "metadata": {"cwe": ["CWE-470: Use of Externally-Controlled Input to Select Classes or Code ('Unsafe Reflection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "shortlink": "https://sg.run/7ndw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Generate/Refresh.php", "start": {"line": 81, "col": 17, "offset": 2971}, "end": {"line": 81, "col": 30, "offset": 2984}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "path": "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax.php", "start": {"line": 60, "col": 29, "offset": 1530}, "end": {"line": 60, "col": 39, "offset": 1540}, "extra": {"message": "<- A new object is created where the class name is based on user input. This could lead to remote code execution, as it allows to instantiate any class in the application.", "metadata": {"cwe": ["CWE-470: Use of Externally-Controlled Input to Select Classes or Code ('Unsafe Reflection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "shortlink": "https://sg.run/7ndw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Generate/Refresh.php", "start": {"line": 84, "col": 17, "offset": 3083}, "end": {"line": 84, "col": 30, "offset": 3096}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "message": "Timeout when running php.lang.security.injection.tainted-session.tainted-session on downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnWebPagePrerender.php:\n ", "path": "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnWebPagePrerender.php"}], "paths": {"scanned": ["downloaded_repos/modxcms_fred/.babelrc", "downloaded_repos/modxcms_fred/.editorconfig", "downloaded_repos/modxcms_fred/.github/workflows/build.yml", "downloaded_repos/modxcms_fred/.github/workflows/mkdocs.yml", "downloaded_repos/modxcms_fred/.github/workflows/sync3x.yml", "downloaded_repos/modxcms_fred/.gitignore", "downloaded_repos/modxcms_fred/.mdlrc", "downloaded_repos/modxcms_fred/.npmignore", "downloaded_repos/modxcms_fred/.nvmrc", "downloaded_repos/modxcms_fred/LICENSE", "downloaded_repos/modxcms_fred/README.md", "downloaded_repos/modxcms_fred/TODO.md", "downloaded_repos/modxcms_fred/Writerside/c.list", "downloaded_repos/modxcms_fred/Writerside/cfg/buildprofiles.xml", "downloaded_repos/modxcms_fred/Writerside/fd.tree", "downloaded_repos/modxcms_fred/Writerside/images/media/ace_integration_dialog.png", "downloaded_repos/modxcms_fred/Writerside/images/media/create-element.png", "downloaded_repos/modxcms_fred/Writerside/images/media/fred-in-use.png", "downloaded_repos/modxcms_fred/Writerside/images/media/fred-loaded.png", "downloaded_repos/modxcms_fred/Writerside/images/media/fred-overview.png", "downloaded_repos/modxcms_fred/Writerside/images/media/fred-sidebar.png", "downloaded_repos/modxcms_fred/Writerside/images/media/front-end-element.png", "downloaded_repos/modxcms_fred/Writerside/images/media/toolbar.png", "downloaded_repos/modxcms_fred/Writerside/images/option_chunk.png", "downloaded_repos/modxcms_fred/Writerside/images/option_colorpicker.png", "downloaded_repos/modxcms_fred/Writerside/images/option_colorswatch.png", "downloaded_repos/modxcms_fred/Writerside/images/option_file.png", "downloaded_repos/modxcms_fred/Writerside/images/option_folder.png", "downloaded_repos/modxcms_fred/Writerside/images/option_image.png", "downloaded_repos/modxcms_fred/Writerside/images/option_page.png", "downloaded_repos/modxcms_fred/Writerside/images/option_select.png", "downloaded_repos/modxcms_fred/Writerside/images/option_slider.png", "downloaded_repos/modxcms_fred/Writerside/images/option_tagger.png", "downloaded_repos/modxcms_fred/Writerside/images/option_text.png", "downloaded_repos/modxcms_fred/Writerside/images/option_textarea.png", "downloaded_repos/modxcms_fred/Writerside/images/option_toggle.png", "downloaded_repos/modxcms_fred/Writerside/images/option_togglegroup.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/categories_grid.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/element_panel.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/element_panel_options.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/media-sources.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/option_sets_edit_panel.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/option_sets_grid.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/rte_configs_grid.png", "downloaded_repos/modxcms_fred/Writerside/images/themer/cmp/img/update-media-source.png", "downloaded_repos/modxcms_fred/Writerside/images/tv_properties.png", "downloaded_repos/modxcms_fred/Writerside/topics/Template-Variables-in-Fred.md", "downloaded_repos/modxcms_fred/Writerside/topics/blueprints.md", "downloaded_repos/modxcms_fred/Writerside/topics/collab/collab_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/collab/gitify.md", "downloaded_repos/modxcms_fred/Writerside/topics/collab/gitify_in_action.md", "downloaded_repos/modxcms_fred/Writerside/topics/collab/initial_extract.md", "downloaded_repos/modxcms_fred/Writerside/topics/collab/pr_workflow.md", "downloaded_repos/modxcms_fred/Writerside/topics/credits.md", "downloaded_repos/modxcms_fred/Writerside/topics/developer/developer_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/developer/modx_events.md", "downloaded_repos/modxcms_fred/Writerside/topics/developer/sidebar_plugins.md", "downloaded_repos/modxcms_fred/Writerside/topics/developer/toolbar_plugins.md", "downloaded_repos/modxcms_fred/Writerside/topics/elements.md", "downloaded_repos/modxcms_fred/Writerside/topics/fred_for_existing_modxers.md", "downloaded_repos/modxcms_fred/Writerside/topics/getting_started.md", "downloaded_repos/modxcms_fred/Writerside/topics/index.md", "downloaded_repos/modxcms_fred/Writerside/topics/site_admin/acls/howto.md", "downloaded_repos/modxcms_fred/Writerside/topics/site_admin/acls/permissions.md", "downloaded_repos/modxcms_fred/Writerside/topics/site_admin/acls/policies.md", "downloaded_repos/modxcms_fred/Writerside/topics/templates.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/blueprint_categories.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/cmp_blueprints.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/cmp_elements.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/cmp_themes.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/element_categories.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/media_sources.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/option_sets.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/rebuild.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/rte_configs.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/theme_settings_and_resolvers.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/cmp/themed_templates.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/convert-to-fred.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/elements/attributes.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/elements/elements_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/elements/js_events.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/elements/markup.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/media_sources/media_sources_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/options/import.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/options/options_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/options/override.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/options/settings.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/rte_configs/rte_configs_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/system_settings/system_settings_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/templates/templates_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/themer_index.md", "downloaded_repos/modxcms_fred/Writerside/topics/themer/themes.md", "downloaded_repos/modxcms_fred/Writerside/v.list", "downloaded_repos/modxcms_fred/Writerside/writerside.cfg", "downloaded_repos/modxcms_fred/_static/fred-demo.js", "downloaded_repos/modxcms_fred/_static/notify.html", "downloaded_repos/modxcms_fred/assets/components/fred/connector.php", "downloaded_repos/modxcms_fred/assets/components/fred/index.html", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/css/fred.css", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/css/index.html", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/css/shim.css", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/blueprint/page.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/blueprint/panel.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element/page.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element/panel.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element_option_set/page.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element_option_set/panel.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element_rte_config/page.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element_rte_config/panel.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/fred.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/blueprint.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/blueprint_categories.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/blueprint_category.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/blueprints.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/dependencies.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element_categories.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element_category.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element_option_set.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element_option_sets.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element_rte_config.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element_rte_configs.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/elements.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/media_sources.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/theme.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/theme_build_media_sources.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/theme_build_media_sources.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/theme_build_resolvers.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/theme_build_resolvers.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/themed_template.window.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/themed_templates.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/themes.grid.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/index.html", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/theme/page.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/theme/panel.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/utils/breadcrumbs.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/utils/combos.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/utils/fields.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/utils/griddraganddrop.js", "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/utils/utils.js", "downloaded_repos/modxcms_fred/assets/components/fred/web/elfinder/index.html", "downloaded_repos/modxcms_fred/assets/components/fred/web/elfinder/main.js", "downloaded_repos/modxcms_fred/assets/components/fred/web/endpoints/ajax.php", "downloaded_repos/modxcms_fred/assets/components/fred/web/endpoints/elfinder.php", "downloaded_repos/modxcms_fred/assets/components/fred/web/endpoints/index.html", "downloaded_repos/modxcms_fred/assets/components/fred/web/endpoints/init.2x.php", "downloaded_repos/modxcms_fred/assets/components/fred/web/endpoints/init.3x.php", "downloaded_repos/modxcms_fred/assets/components/fred/web/images/grab.cur", "downloaded_repos/modxcms_fred/assets/components/fred/web/images/grabbing.cur", "downloaded_repos/modxcms_fred/assets/components/fred/web/images/modx-revo-icon-48.svg", "downloaded_repos/modxcms_fred/core/components/fred/bootstrap.php", "downloaded_repos/modxcms_fred/core/components/fred/composer.json", "downloaded_repos/modxcms_fred/core/components/fred/composer.lock", "downloaded_repos/modxcms_fred/core/components/fred/controllers/acls.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/blueprint/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/element/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/element/option_set/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/element/option_set/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/element/rte_config/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/element/rte_config/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/element/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/home.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/theme/download.class.php", "downloaded_repos/modxcms_fred/core/components/fred/controllers/theme/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/docs/changelog.txt", "downloaded_repos/modxcms_fred/core/components/fred/docs/license.txt", "downloaded_repos/modxcms_fred/core/components/fred/docs/readme.txt", "downloaded_repos/modxcms_fred/core/components/fred/elements/plugins/Fred.php", "downloaded_repos/modxcms_fred/core/components/fred/elements/tvs/input/freddropzone.class.php", "downloaded_repos/modxcms_fred/core/components/fred/elements/tvs/input/options/freddropzone.php", "downloaded_repos/modxcms_fred/core/components/fred/elements/tvs/input/tpl/freddropzone.options.tpl", "downloaded_repos/modxcms_fred/core/components/fred/elements/tvs/input/tpl/freddropzone.render.tpl", "downloaded_repos/modxcms_fred/core/components/fred/elements/tvs/output/freddropzone.class.php", "downloaded_repos/modxcms_fred/core/components/fred/index.class.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/ar/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/ar/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/ar/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/de/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/de/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/de/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/en/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/en/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/en/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/es/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/es/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/es/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/fr/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/fr/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/fr/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/it/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/it/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/it/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/mt/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/mt/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/mt/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/nl/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/nl/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/nl/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/pt/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/pt/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/pt/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/pt-br/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/pt-br/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/pt-br/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/ru/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/ru/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/ru/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/sv/default.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/sv/fe.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/lexicon/sv/permissions.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fred.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredblueprint.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredblueprintcategory.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredblueprintcategorytemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredblueprinttemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredcache.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredelement.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredelementcategory.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredelementcategorytemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredelementoptionset.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredelementrteconfig.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredelementtemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredtheme.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/fredthemedtemplate.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/metadata.mysql.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprint.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprint.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprintcategory.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprintcategory.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprintcategorytemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprintcategorytemplateaccess.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprinttemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredblueprinttemplateaccess.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredcache.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredcache.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelement.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelement.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementcategory.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementcategory.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementcategorytemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementcategorytemplateaccess.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementoptionset.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementoptionset.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementrteconfig.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementrteconfig.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementtemplateaccess.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredelementtemplateaccess.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredtheme.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredtheme.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredthemedtemplate.class.php", "downloaded_repos/modxcms_fred/core/components/fred/model/fred/mysql/fredthemedtemplate.map.inc.php", "downloaded_repos/modxcms_fred/core/components/fred/model/schema/fred.mysql.schema.xml", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprint_categories/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprint_categories/ddreorder.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprint_categories/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprint_categories/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprint_categories/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprint_categories/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprints/ddreorder.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprints/get.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprints/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprints/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprints/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/blueprints/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_categories/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_categories/ddreorder.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_categories/duplicate.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_categories/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_categories/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_categories/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_categories/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_option_sets/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_option_sets/duplicate.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_option_sets/get.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_option_sets/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_option_sets/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_option_sets/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_option_sets/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_rte_configs/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_rte_configs/duplicate.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_rte_configs/get.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_rte_configs/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_rte_configs/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_rte_configs/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/element_rte_configs/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/ddreorder.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/duplicate.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/get.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/elements/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/extra/getcategories.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/extra/getinstalledpackages.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/extra/gettemplates.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/extra/getthemedtemplates.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/generate/refresh.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/media_sources/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/media_sources/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themed_templates/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themed_templates/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themed_templates/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themed_templates/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/build.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/create.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/duplicate.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/get.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/getlist.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/remove.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/update.class.php", "downloaded_repos/modxcms_fred/core/components/fred/processors/mgr/themes/updatefromgrid.class.php", "downloaded_repos/modxcms_fred/core/components/fred/schema/fred.mysql.schema.xml", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/Event.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnBeforeDocFormSave.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnDocFormPrerender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnDocFormSave.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnLoadWebDocument.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnMODXInit.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnManagerLogin.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnManagerPageBeforeRender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnTVInputPropertiesList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnTVInputRenderList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnTemplateRemove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnWebLogin.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Elements/Event/OnWebPagePrerender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/BlueprintsCreateBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/BlueprintsCreateCategory.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/CreateResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/DeleteResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/DuplicateResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/ElementReplaceImage.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/Endpoint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/GetBlueprints.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/GetChunks.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/GetElements.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/GetResourceTree.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/GetResources.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/GetTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/GetThemedTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/LoadBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/LoadContent.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/LoadLexicons.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/PublishResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/RenderElement.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/SaveContent.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/TaggerGetGroup.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/TaggerGetTags.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/UndeleteResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax/UnpublishResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Ajax.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/ElFinder/Driver/Driver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/ElFinder/Driver/Utils.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/ElFinder/elFinderVolumeFlysystem.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/ElFinder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Endpoint/Endpoint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Fred.php", "downloaded_repos/modxcms_fred/core/components/fred/src/FredFileVehicle.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredBlueprintCategory.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredBlueprintCategoryTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredBlueprintTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredCache.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredElement.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredElementCategory.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredElementCategoryTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredElementOptionSet.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredElementRTEConfig.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredElementTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredTheme.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/FredThemedTemplate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/metadata.mysql.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredBlueprintCategory.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredBlueprintCategoryTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredBlueprintTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredCache.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredElement.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredElementCategory.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredElementCategoryTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredElementOptionSet.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredElementRTEConfig.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredElementTemplateAccess.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredTheme.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Model/mysql/FredThemedTemplate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/BlueprintCategories/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/BlueprintCategories/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/BlueprintCategories/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/BlueprintCategories/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/BlueprintCategories/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/BlueprintCategories/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Blueprints/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Blueprints/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Blueprints/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Blueprints/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Blueprints/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Blueprints/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementCategories/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementCategories/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementCategories/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementCategories/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementCategories/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementCategories/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementCategories/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementOptionSets/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementOptionSets/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementOptionSets/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementOptionSets/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementOptionSets/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementOptionSets/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementOptionSets/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementRTEConfigs/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementRTEConfigs/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementRTEConfigs/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementRTEConfigs/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementRTEConfigs/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementRTEConfigs/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ElementRTEConfigs/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Elements/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Extra/GetCategories.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Extra/GetInstalledPackages.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Extra/GetTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Extra/GetThemedTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Generate/Refresh.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/MediaSources/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/MediaSources/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ThemedTemplates/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ThemedTemplates/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ThemedTemplates/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/ThemedTemplates/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/Build.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/fix_namespace.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/get_fred.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/get_fred_uninstall.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/halt.validator.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/install.validator.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/link_element_option_set.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/uninstall.validator.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/uninstall_categories.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Processors/Themes/build_helpers/uninstall_templates_tvs.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/RenderResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnBeforeDocFormSave.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnDocFormPrerender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnDocFormSave.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnLoadWebDocument.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnMODXInit.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnManagerLogin.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnManagerPageBeforeRender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnTVInputPropertiesList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnTVInputRenderList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnTemplateRemove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnWebLogin.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnWebPagePrerender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/BlueprintsCreateBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/BlueprintsCreateCategory.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/CreateResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/DeleteResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/ElementReplaceImage.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/Endpoint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/GetBlueprints.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/GetChunks.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/GetElements.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/GetResourceTree.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/GetResources.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/GetTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/GetThemedTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/LoadBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/LoadContent.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/LoadLexicons.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/PublishResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/RenderElement.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/UndeleteResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Endpoint/Ajax/UnpublishResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/BlueprintCategories/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/BlueprintCategories/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/BlueprintCategories/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/BlueprintCategories/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/BlueprintCategories/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Blueprints/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Blueprints/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Blueprints/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Blueprints/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementCategories/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementCategories/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementCategories/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementCategories/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementCategories/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementCategories/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementOptionSets/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementOptionSets/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementOptionSets/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementOptionSets/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementOptionSets/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementRTEConfigs/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementRTEConfigs/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementRTEConfigs/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ElementRTEConfigs/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Elements/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Elements/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Elements/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Elements/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Elements/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Elements/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Extra/GetInstalledPackages.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/MediaSources/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/PermissionCheck.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ThemedTemplates/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ThemedTemplates/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/ThemedTemplates/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Themes/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Themes/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Themes/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Themes/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/Themes/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Processors/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/RenderResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/User.php", "downloaded_repos/modxcms_fred/core/components/fred/src/Utils.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/Event.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnBeforeDocFormSave.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnDocFormPrerender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnDocFormSave.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnLoadWebDocument.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnMODXInit.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnManagerLogin.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnManagerPageBeforeRender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnTVInputPropertiesList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnTVInputRenderList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnTemplateRemove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnWebLogin.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Elements/Event/OnWebPagePrerender.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/BlueprintsCreateBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/BlueprintsCreateCategory.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/CreateResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/DeleteResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/DuplicateResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/ElementReplaceImage.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/Endpoint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/GetBlueprints.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/GetChunks.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/GetElements.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/GetResourceTree.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/GetResources.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/GetTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/GetThemedTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/LoadBlueprint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/LoadContent.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/LoadLexicons.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/PublishResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/RenderElement.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/SaveContent.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/TaggerGetGroup.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/TaggerGetTags.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/UndeleteResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax/UnpublishResource.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Ajax.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/ElFinder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Endpoint/Endpoint.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/BlueprintCategories/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/BlueprintCategories/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/BlueprintCategories/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/BlueprintCategories/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/BlueprintCategories/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/BlueprintCategories/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Blueprints/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Blueprints/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Blueprints/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Blueprints/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Blueprints/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Blueprints/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementCategories/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementCategories/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementCategories/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementCategories/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementCategories/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementCategories/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementCategories/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementOptionSets/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementOptionSets/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementOptionSets/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementOptionSets/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementOptionSets/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementOptionSets/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementOptionSets/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementRTEConfigs/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementRTEConfigs/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementRTEConfigs/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementRTEConfigs/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementRTEConfigs/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementRTEConfigs/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ElementRTEConfigs/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/DDReorder.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Elements/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Extra/GetCategories.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Extra/GetInstalledPackages.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Extra/GetTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Extra/GetThemedTemplates.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Generate/Refresh.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/MediaSources/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/MediaSources/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ThemedTemplates/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ThemedTemplates/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ThemedTemplates/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/ThemedTemplates/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/Build.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/Create.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/Duplicate.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/Get.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/GetList.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/Remove.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/Update.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/UpdateFromGrid.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/fix_namespace.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/get_fred.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/get_fred_uninstall.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/halt.validator.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/install.validator.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/link_element_option_set.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/uninstall.validator.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/uninstall_categories.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/Processors/Themes/build_helpers/uninstall_templates_tvs.resolver.php", "downloaded_repos/modxcms_fred/core/components/fred/src/v2/RenderResource.php", "downloaded_repos/modxcms_fred/core/components/fred/templates/blueprint.tpl", "downloaded_repos/modxcms_fred/core/components/fred/templates/element.tpl", "downloaded_repos/modxcms_fred/core/components/fred/templates/element_option_set.tpl", "downloaded_repos/modxcms_fred/core/components/fred/templates/element_rte_config.tpl", "downloaded_repos/modxcms_fred/core/components/fred/templates/home.tpl", "downloaded_repos/modxcms_fred/core/components/fred/templates/theme.tpl", "downloaded_repos/modxcms_fred/crowdin.yml", "downloaded_repos/modxcms_fred/mkdocs.yml", "downloaded_repos/modxcms_fred/package-lock.json", "downloaded_repos/modxcms_fred/package.json", "downloaded_repos/modxcms_fred/phpcs.xml", "downloaded_repos/modxcms_fred/postcss.config.js", "downloaded_repos/modxcms_fred/tsconfig.json", "downloaded_repos/modxcms_fred/webpack.config.js"], "skipped": [{"path": "downloaded_repos/modxcms_fred/Writerside/images/media/basic-use-2.mp4", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/modxcms_fred/Writerside/images/media/basic-use.mp4", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/arrow-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/arrow-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/browse.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/camera.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/caret-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/caret-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/clone.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/cog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/columns.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/copy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/elements.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/ellipsis-h.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/ellipsis-v.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/exclamation-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/exclamation-triangle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/expand.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/eye.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/file.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/flask.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/grip-horizontal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/grip-vertical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/long-arrow-alt-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/long-arrow-alt-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/mobile-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/mobile.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/modx-revo-icon-48.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/object-group-solid.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/object-group.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/pages.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/plus-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/power-off.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/preview.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/save.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/search.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/settings.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/sitemap.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/sync-solid.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/tablet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/tabletalt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/times.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/trash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/triangle-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/undo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/images/upload-solid.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Actions/blueprints.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Actions/elements.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Actions/fred.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Actions/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Actions/pages.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Actions/tagger.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Actions/themes.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Cache.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/ColorPicker/ColorPicker.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/ColorPicker/utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Components/Sidebar/Blueprints/Blueprints.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Components/Sidebar/Elements/Elements.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Components/Sidebar/More.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Components/Sidebar/PageSettings.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Components/Sidebar/Pages/Pages.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Components/SidebarPlugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Components/UtilitySidebar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Dropzone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Element.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/ElementSettings.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/PartialBlueprints.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/Delete.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/Duplicate.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/ElementScreenshot.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/ElementSettings.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/Move.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/MoveHandle.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/PartialBlueprint.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/RefreshElementCache.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar/ToolbarPlugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Content/Toolbar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Drake.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/EE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Editors/Editor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Editors/IconEditor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Editors/ImageEditor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Editors/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Fetch.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Finder.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Launcher.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Modal.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Sidebar/Sidebar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Sidebar/SidebarView.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/UI/Elements.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/UI/Inputs.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/UI/MultiSelect.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/UI/Tagger.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/UI/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/Utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/js/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_body.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_breakpoints.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_buttons.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_choices-white.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_choices.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_colorPicker.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_dragula.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_dropzone.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_fields.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_flatpickr_override.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_forms.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_grids.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_launcher.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_nouislider_override.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_panels.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_reset.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_safari.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_sidebar.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_tagger.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_tinymce.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_topbar.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_utility.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/_vars.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/assets/sass/fred.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/config.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/gpm.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/gpm_resolvers/gpm.resolve.bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/gpm_resolvers/gpm.resolve.element_property_set.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/gpm_scripts/gpm.script.sync_tables.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/gpm_scripts/gpm.script.tables.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/lit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/resolve.acls.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/resolve.customevents.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/resolve.defaulttheme.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/resolve.mediasources.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/resolve.migration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/resolve.placehold.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/resolvers/resolve.synctables.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/scripts/after.defaulttheme.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/scripts/after.migration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/scripts/before.acls.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/scripts/before.customevents.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/scripts/before.mediasources.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/scripts/requirements.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/_build/validators/requirements.validator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/assets/components/fred/web/vendor/elfinder/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/assets/components/fred/web/vendor/elfinder-themes/material/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnWebPagePrerender.php", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.9171600341796875, "profiling_times": {"config_time": 6.036257266998291, "core_time": 10.161327123641968, "ignores_time": 0.0019965171813964844, "total_time": 16.20054793357849}, "parsing_time": {"total_time": 4.6578943729400635, "per_file_time": {"mean": 0.00846889885989101, "std_dev": 0.0004487206752269618}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 26.655282974243164, "per_file_time": {"mean": 0.014246543545827446, "std_dev": 0.018589552625517605}, "very_slow_stats": {"time_ratio": 0.19966436601814164, "count_ratio": 0.0005344735435595938}, "very_slow_files": [{"fpath": "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnWebPagePrerender.php", "ftime": 5.322110176086426}]}, "matching_time": {"total_time": 7.5369133949279785, "per_file_and_rule_time": {"mean": 0.006765631413759404, "std_dev": 0.00036097414911323875}, "very_slow_stats": {"time_ratio": 0.16833057563453696, "count_ratio": 0.00718132854578097}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element/panel.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10210514068603516}, {"fpath": "downloaded_repos/modxcms_fred/assets/components/fred/web/elfinder/main.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.11772894859313965}, {"fpath": "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/element_categories.grid.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.11812400817871094}, {"fpath": "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/blueprints.grid.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.13593816757202148}, {"fpath": "downloaded_repos/modxcms_fred/core/components/fred/src/Traits/Elements/Event/OnWebPagePrerender.php", "rule_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "time": 0.14023685455322266}, {"fpath": "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/elements.grid.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.17133283615112305}, {"fpath": "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/home/<USER>/theme.window.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.207489013671875}, {"fpath": "downloaded_repos/modxcms_fred/assets/components/fred/mgr/js/element/panel.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.275738000869751}]}, "tainting_time": {"total_time": 2.802157402038574, "per_def_and_rule_time": {"mean": 0.0034594535827636723, "std_dev": 4.4730902175862506e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1098768896}, "engine_requested": "OSS", "skipped_rules": []}