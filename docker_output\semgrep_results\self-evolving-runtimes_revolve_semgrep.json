{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/mongodb/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'mongodb' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/mongodb/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'mongodb' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/docker-compose.yml", "start": {"line": 13, "col": 3, "offset": 497}, "end": {"line": 13, "col": 11, "offset": 505}, "extra": {"message": "Service 'database' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/docker-compose.yml", "start": {"line": 13, "col": 3, "offset": 497}, "end": {"line": 13, "col": 11, "offset": 505}, "extra": {"message": "Service 'database' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/docker-compose.yml", "start": {"line": 33, "col": 3, "offset": 1385}, "end": {"line": 33, "col": 10, "offset": 1392}, "extra": {"message": "Service 'db-seed' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/docker-compose.yml", "start": {"line": 33, "col": 3, "offset": 1385}, "end": {"line": 33, "col": 10, "offset": 1392}, "extra": {"message": "Service 'db-seed' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/__init__.py", "start": {"line": 9, "col": 18, "offset": 255}, "end": {"line": 9, "col": 54, "offset": 291}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 388, "col": 17, "offset": 15632}, "end": {"line": 388, "col": 66, "offset": 15681}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.sqli.psycopg-sqli.psycopg-sqli", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 388, "col": 17, "offset": 15632}, "end": {"line": 388, "col": 66, "offset": 15681}, "extra": {"message": "Detected string concatenation with a non-literal variable in a psycopg2 Python SQL statement. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead. You can use prepared statements by creating a 'sql.SQL' string. You can also use the pyformat binding style to create parameterized queries. For example: 'cur.execute(SELECT * FROM table WHERE name=%s, user_input)'", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://www.psycopg.org/docs/sql.html"], "category": "security", "technology": ["psycopg"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.sqli.psycopg-sqli.psycopg-sqli", "shortlink": "https://sg.run/qrLe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 388, "col": 17, "offset": 15632}, "end": {"line": 388, "col": 66, "offset": 15681}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 393, "col": 17, "offset": 15861}, "end": {"line": 393, "col": 58, "offset": 15902}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.sqli.psycopg-sqli.psycopg-sqli", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 393, "col": 17, "offset": 15861}, "end": {"line": 393, "col": 58, "offset": 15902}, "extra": {"message": "Detected string concatenation with a non-literal variable in a psycopg2 Python SQL statement. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead. You can use prepared statements by creating a 'sql.SQL' string. You can also use the pyformat binding style to create parameterized queries. For example: 'cur.execute(SELECT * FROM table WHERE name=%s, user_input)'", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://www.psycopg.org/docs/sql.html"], "category": "security", "technology": ["psycopg"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.sqli.psycopg-sqli.psycopg-sqli", "shortlink": "https://sg.run/qrLe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 393, "col": 17, "offset": 15861}, "end": {"line": 393, "col": 58, "offset": 15902}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 538, "col": 21, "offset": 21100}, "end": {"line": 541, "col": 22, "offset": 21279}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "start": {"line": 571, "col": 29, "offset": 22539}, "end": {"line": 571, "col": 114, "offset": 22624}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/functions.py", "start": {"line": 24, "col": 13, "offset": 572}, "end": {"line": 24, "col": 34, "offset": 593}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/functions.py", "start": {"line": 31, "col": 24, "offset": 869}, "end": {"line": 31, "col": 38, "offset": 883}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.angular.security.detect-angular-element-taint.detect-angular-element-taint", "message": "Timeout when running javascript.angular.security.detect-angular-element-taint.detect-angular-element-taint on downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js:\n ", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js:\n ", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js:\n ", "path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js"}], "paths": {"scanned": ["downloaded_repos/self-evolving-runtimes_revolve/.gitattributes", "downloaded_repos/self-evolving-runtimes_revolve/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/self-evolving-runtimes_revolve/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/self-evolving-runtimes_revolve/.github/workflows/build.yaml", "downloaded_repos/self-evolving-runtimes_revolve/.github/workflows/publish.yaml", "downloaded_repos/self-evolving-runtimes_revolve/.gitignore", "downloaded_repos/self-evolving-runtimes_revolve/.python-version", "downloaded_repos/self-evolving-runtimes_revolve/LICENSE", "downloaded_repos/self-evolving-runtimes_revolve/README.md", "downloaded_repos/self-evolving-runtimes_revolve/SECURITY.md", "downloaded_repos/self-evolving-runtimes_revolve/contributor-README.md", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/mongodb/docker-compose.yml", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/mongodb/init.js", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/ddl.sql", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/ddl_customer_movies_watchHistory.sql", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/ddl_hospital.sql", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/ddl_pet_owners.sql", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/ddl_satellites.sql", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/ddl_students_cources.sql", "downloaded_repos/self-evolving-runtimes_revolve/db-seeds/postgres/docker-compose.yml", "downloaded_repos/self-evolving-runtimes_revolve/pyproject.toml", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/__init__.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/api.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/data_types.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/__init__.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/adapter.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/mongodb_adapter.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/db/postgres_adapter.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/external.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/functions.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/llm.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/main.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/__init__.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/check_user_request.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/generate_api.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/generate_prompt.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/process_table.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/report.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/router.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/test.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/tool_handler.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/prompts.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/index.html", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/api.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/cors.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/mongodb/db_utils.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/mongodb/service.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/postgres/db_utils.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/postgres/service.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/schemas.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/static.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/source_template/test_api.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/tools.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/App.jsx", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/index.css", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/index.html", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/mock/server.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/package.json", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/vite.config.js", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/utils.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/utils_git.py", "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/workflow_generator.py", "downloaded_repos/self-evolving-runtimes_revolve/uv.lock", "downloaded_repos/self-evolving-runtimes_revolve/workflow.png"], "skipped": [{"path": "downloaded_repos/self-evolving-runtimes_revolve/screenshots/animated.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/dist/assets/index-79811946.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/dist/assets/index-aa45ee70.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/dist/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/self-evolving-runtimes_revolve/src/tests/test_dependency.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.091639995574951, "profiling_times": {"config_time": 9.602342128753662, "core_time": 8.654486656188965, "ignores_time": 0.0021715164184570312, "total_time": 18.260079383850098}, "parsing_time": {"total_time": 0.8867883682250977, "per_file_time": {"mean": 0.020622985307560408, "std_dev": 0.0015413024278428064}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 14.957616806030273, "per_file_time": {"mean": 0.08498645912517203, "std_dev": 0.2103462555614831}, "very_slow_stats": {"time_ratio": 0.4959448516270963, "count_ratio": 0.011363636363636364}, "very_slow_files": [{"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/App.jsx", "ftime": 1.8003919124603271}, {"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/resources/assets/index-ac765696.js", "ftime": 5.617761135101318}]}, "matching_time": {"total_time": 3.814568519592285, "per_file_and_rule_time": {"mean": 0.007265844799223402, "std_dev": 0.0005222054606835367}, "very_slow_stats": {"time_ratio": 0.2823042152672653, "count_ratio": 0.011428571428571429}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/api.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.10771393775939941}, {"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/App.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.1161339282989502}, {"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/utils.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.12958002090454102}, {"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/api.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.17885899543762207}, {"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/ui/App.jsx", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.26679396629333496}, {"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/prompts.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.2777879238128662}]}, "tainting_time": {"total_time": 1.5685701370239258, "per_def_and_rule_time": {"mean": 0.0014728358094121372, "std_dev": 8.704999287720177e-05}, "very_slow_stats": {"time_ratio": 0.26114931312375933, "count_ratio": 0.0018779342723004694}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/nodes/test.py", "fline": 12, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.2043459415435791}, {"fpath": "downloaded_repos/self-evolving-runtimes_revolve/src/revolve/functions.py", "fline": 80, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20528507232666016}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}