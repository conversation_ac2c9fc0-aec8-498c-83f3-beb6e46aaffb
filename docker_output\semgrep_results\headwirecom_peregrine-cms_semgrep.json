{"version": "1.130.0", "results": [{"check_id": "java.lang.security.audit.crypto.weak-random.weak-random", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/AdminResourceHandlerService.java", "start": {"line": 2412, "col": 75, "offset": 119611}, "end": {"line": 2412, "col": 114, "offset": 119650}, "extra": {"message": "Detected use of the functions `Math.random()` or `java.util.Random()`. These are both not cryptographically strong random number generators (RNGs). If you are using these RNGs to create passwords or secret tokens, use `java.security.SecureRandom` instead.", "metadata": {"functional-categories": ["crypto::search::randomness::java.security"], "owasp": ["A02:2021 - Cryptographic Failures"], "cwe": ["CWE-330: Use of Insufficiently Random Values"], "category": "security", "technology": ["java"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/java.lang.security.audit.crypto.weak-random.weak-random", "shortlink": "https://sg.run/NwBp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/toasts.js", "start": {"line": 216, "col": 9, "offset": 6163}, "end": {"line": 216, "col": 40, "offset": 6194}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/about/template.vue", "start": {"line": 31, "col": 17, "offset": 1255}, "end": {"line": 31, "col": 30, "offset": 1268}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/about/template.vue", "start": {"line": 34, "col": 17, "offset": 1379}, "end": {"line": 34, "col": 30, "offset": 1392}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/about/template.vue", "start": {"line": 37, "col": 17, "offset": 1503}, "end": {"line": 37, "col": 30, "offset": 1516}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/about/template.vue", "start": {"line": 40, "col": 17, "offset": 1627}, "end": {"line": 40, "col": 30, "offset": 1640}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createassetfolderwizard/template.vue", "start": {"line": 40, "col": 13, "offset": 1491}, "end": {"line": 40, "col": 25, "offset": 1503}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectdefinitionwizard/template.vue", "start": {"line": 40, "col": 13, "offset": 1517}, "end": {"line": 40, "col": 25, "offset": 1529}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectfolderwizard/template.vue", "start": {"line": 40, "col": 13, "offset": 1491}, "end": {"line": 40, "col": 25, "offset": 1503}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/icon/template.vue", "start": {"line": 4, "col": 3, "offset": 158}, "end": {"line": 4, "col": 58, "offset": 213}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tour/template.vue", "start": {"line": 36, "col": 17, "offset": 1979}, "end": {"line": 36, "col": 44, "offset": 2006}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/texteditor/template.vue", "start": {"line": 32, "col": 5, "offset": 1092}, "end": {"line": 35, "col": 15, "offset": 1241}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/apiImpl.js", "start": {"line": 155, "col": 7, "offset": 4307}, "end": {"line": 155, "col": 25, "offset": 4325}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/perAdminApp.js", "start": {"line": 582, "col": 5, "offset": 15351}, "end": {"line": 582, "col": 25, "offset": 15371}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/utils.js", "start": {"line": 87, "col": 5, "offset": 2497}, "end": {"line": 87, "col": 25, "offset": 2517}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/utils.js", "start": {"line": 111, "col": 5, "offset": 2950}, "end": {"line": 111, "col": 25, "offset": 2970}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/utils/htmlEncoder.js", "start": {"line": 232, "col": 16, "offset": 12334}, "end": {"line": 232, "col": 40, "offset": 12358}, "extra": {"message": "RegExp() called with a `arr1` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/headwirecom_peregrine-cms/docker/Dockerfile", "start": {"line": 48, "col": 1, "offset": 1034}, "end": {"line": 48, "col": 82, "offset": 1115}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT /app/scripts/start.sh ${RUNMODE} && tail -qF /app/sling/logs/error.log", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/renderer.html", "start": {"line": 3, "col": 1, "offset": 49}, "end": {"line": 3, "col": 66, "offset": 114}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/renderer.html", "start": {"line": 4, "col": 1, "offset": 115}, "end": {"line": 4, "col": 85, "offset": 199}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/renderer.html", "start": {"line": 5, "col": 1, "offset": 200}, "end": {"line": 5, "col": 89, "offset": 288}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/peregrineApp.js", "start": {"line": 81, "col": 9, "offset": 2447}, "end": {"line": 81, "col": 29, "offset": 2467}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/peregrineApp.js", "start": {"line": 106, "col": 9, "offset": 3012}, "end": {"line": 106, "col": 29, "offset": 3032}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/components/base/template.vue", "start": {"line": 27, "col": 1, "offset": 980}, "end": {"line": 27, "col": 11, "offset": 990}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/peregrineApp.js", "start": {"line": 84, "col": 9, "offset": 2556}, "end": {"line": 84, "col": 29, "offset": 2576}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/peregrineApp.js", "start": {"line": 109, "col": 9, "offset": 3121}, "end": {"line": 109, "col": 29, "offset": 3141}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.lang.security.audit.command-injection-process-builder.command-injection-process-builder", "path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/process/ProcessRunner.java", "start": {"line": 93, "col": 35, "offset": 3513}, "end": {"line": 93, "col": 62, "offset": 3540}, "extra": {"message": "A formatted or concatenated string was detected as input to a ProcessBuilder call. This is dangerous if a variable is controlled by user input and could result in a command injection. Ensure your variables are not controlled by users or sufficiently sanitized.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "category": "security", "technology": ["java"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/java.lang.security.audit.command-injection-process-builder.command-injection-process-builder", "shortlink": "https://sg.run/gJJe"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/carousel/template.vue", "start": {"line": 52, "col": 11, "offset": 2059}, "end": {"line": 53, "col": 21, "offset": 2100}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/text/template.vue", "start": {"line": 26, "col": 1, "offset": 889}, "end": {"line": 26, "col": 11, "offset": 899}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/toolingpage/renderer.html", "start": {"line": 27, "col": 1, "offset": 925}, "end": {"line": 27, "col": 66, "offset": 990}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/toolingpage/renderer.html", "start": {"line": 28, "col": 1, "offset": 991}, "end": {"line": 28, "col": 46, "offset": 1036}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/template.vue", "start": {"line": 5, "col": 7, "offset": 203}, "end": {"line": 6, "col": 14, "offset": 278}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/template.vue", "start": {"line": 20, "col": 15, "offset": 1289}, "end": {"line": 20, "col": 26, "offset": 1300}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/template.vue", "start": {"line": 24, "col": 15, "offset": 1504}, "end": {"line": 24, "col": 78, "offset": 1567}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlequote/template.vue", "start": {"line": 9, "col": 7, "offset": 422}, "end": {"line": 9, "col": 39, "offset": 454}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/template.vue", "start": {"line": 7, "col": 11, "offset": 291}, "end": {"line": 22, "col": 18, "offset": 1092}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/template.vue", "start": {"line": 31, "col": 13, "offset": 1625}, "end": {"line": 31, "col": 46, "offset": 1658}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextblock/template.vue", "start": {"line": 4, "col": 7, "offset": 112}, "end": {"line": 4, "col": 19, "offset": 124}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextmedia/template.vue", "start": {"line": 8, "col": 7, "offset": 471}, "end": {"line": 8, "col": 41, "offset": 505}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/template.vue", "start": {"line": 4, "col": 7, "offset": 146}, "end": {"line": 4, "col": 53, "offset": 192}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/template.vue", "start": {"line": 5, "col": 7, "offset": 222}, "end": {"line": 5, "col": 53, "offset": 268}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/template.vue", "start": {"line": 24, "col": 15, "offset": 1520}, "end": {"line": 24, "col": 78, "offset": 1583}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/template.vue", "start": {"line": 26, "col": 15, "offset": 1668}, "end": {"line": 26, "col": 57, "offset": 1710}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/template.vue", "start": {"line": 30, "col": 13, "offset": 1834}, "end": {"line": 42, "col": 19, "offset": 2542}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/carousel/template.vue", "start": {"line": 17, "col": 13, "offset": 994}, "end": {"line": 17, "col": 44, "offset": 1025}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/carousel/template.vue", "start": {"line": 18, "col": 13, "offset": 1058}, "end": {"line": 18, "col": 40, "offset": 1085}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/template.html", "start": {"line": 4, "col": 7, "offset": 136}, "end": {"line": 6, "col": 7, "offset": 291}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/template.vue", "start": {"line": 14, "col": 11, "offset": 699}, "end": {"line": 14, "col": 47, "offset": 735}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/template.vue", "start": {"line": 20, "col": 11, "offset": 888}, "end": {"line": 20, "col": 21, "offset": 898}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/header/template.html", "start": {"line": 5, "col": 4, "offset": 135}, "end": {"line": 7, "col": 4, "offset": 290}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/media/template.vue", "start": {"line": 2, "col": 5, "offset": 15}, "end": {"line": 2, "col": 51, "offset": 61}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediavisible/template.vue", "start": {"line": 2, "col": 5, "offset": 15}, "end": {"line": 2, "col": 51, "offset": 61}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/menubuttons/template.vue", "start": {"line": 3, "col": 5, "offset": 82}, "end": {"line": 15, "col": 19, "offset": 823}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/template.vue", "start": {"line": 6, "col": 9, "offset": 229}, "end": {"line": 7, "col": 16, "offset": 336}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/template.vue", "start": {"line": 8, "col": 9, "offset": 364}, "end": {"line": 9, "col": 16, "offset": 477}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/template.vue", "start": {"line": 23, "col": 15, "offset": 1215}, "end": {"line": 37, "col": 22, "offset": 2015}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/template.vue", "start": {"line": 44, "col": 15, "offset": 2422}, "end": {"line": 44, "col": 52, "offset": 2459}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/template.vue", "start": {"line": 14, "col": 9, "offset": 649}, "end": {"line": 14, "col": 60, "offset": 700}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/template.vue", "start": {"line": 15, "col": 9, "offset": 728}, "end": {"line": 15, "col": 63, "offset": 782}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/template.vue", "start": {"line": 16, "col": 9, "offset": 813}, "end": {"line": 16, "col": 52, "offset": 856}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/template.vue", "start": {"line": 23, "col": 9, "offset": 1159}, "end": {"line": 35, "col": 19, "offset": 1873}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/template.vue", "start": {"line": 15, "col": 11, "offset": 741}, "end": {"line": 15, "col": 62, "offset": 792}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/template.vue", "start": {"line": 16, "col": 11, "offset": 822}, "end": {"line": 16, "col": 65, "offset": 876}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/template.vue", "start": {"line": 17, "col": 11, "offset": 909}, "end": {"line": 17, "col": 54, "offset": 952}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/template.vue", "start": {"line": 26, "col": 9, "offset": 1357}, "end": {"line": 38, "col": 19, "offset": 2071}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/textlinks/template.vue", "start": {"line": 4, "col": 7, "offset": 134}, "end": {"line": 4, "col": 77, "offset": 204}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/template.vue", "start": {"line": 5, "col": 7, "offset": 203}, "end": {"line": 6, "col": 14, "offset": 278}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/template.vue", "start": {"line": 20, "col": 15, "offset": 1289}, "end": {"line": 20, "col": 26, "offset": 1300}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/template.vue", "start": {"line": 24, "col": 15, "offset": 1504}, "end": {"line": 24, "col": 78, "offset": 1567}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlequote/template.vue", "start": {"line": 9, "col": 7, "offset": 422}, "end": {"line": 9, "col": 39, "offset": 454}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletabs/template.vue", "start": {"line": 7, "col": 11, "offset": 291}, "end": {"line": 22, "col": 18, "offset": 1092}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletabs/template.vue", "start": {"line": 31, "col": 13, "offset": 1625}, "end": {"line": 31, "col": 46, "offset": 1658}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextblock/template.vue", "start": {"line": 4, "col": 7, "offset": 112}, "end": {"line": 4, "col": 19, "offset": 124}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextmedia/template.vue", "start": {"line": 8, "col": 7, "offset": 471}, "end": {"line": 8, "col": 41, "offset": 505}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletwocolumn/template.vue", "start": {"line": 4, "col": 7, "offset": 146}, "end": {"line": 4, "col": 53, "offset": 192}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletwocolumn/template.vue", "start": {"line": 5, "col": 7, "offset": 222}, "end": {"line": 5, "col": 53, "offset": 268}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/cards/template.vue", "start": {"line": 24, "col": 15, "offset": 1520}, "end": {"line": 24, "col": 78, "offset": 1583}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/cards/template.vue", "start": {"line": 26, "col": 15, "offset": 1668}, "end": {"line": 26, "col": 57, "offset": 1710}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/cards/template.vue", "start": {"line": 30, "col": 13, "offset": 1834}, "end": {"line": 42, "col": 19, "offset": 2542}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/carousel/template.vue", "start": {"line": 17, "col": 13, "offset": 994}, "end": {"line": 17, "col": 44, "offset": 1025}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/carousel/template.vue", "start": {"line": 18, "col": 13, "offset": 1058}, "end": {"line": 18, "col": 40, "offset": 1085}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/footer/template.vue", "start": {"line": 14, "col": 11, "offset": 699}, "end": {"line": 14, "col": 47, "offset": 735}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/footer/template.vue", "start": {"line": 20, "col": 11, "offset": 888}, "end": {"line": 20, "col": 21, "offset": 898}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/menubuttons/template.vue", "start": {"line": 3, "col": 5, "offset": 82}, "end": {"line": 15, "col": 19, "offset": 823}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/template.vue", "start": {"line": 6, "col": 9, "offset": 229}, "end": {"line": 7, "col": 16, "offset": 336}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/template.vue", "start": {"line": 8, "col": 9, "offset": 364}, "end": {"line": 9, "col": 16, "offset": 477}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/template.vue", "start": {"line": 23, "col": 15, "offset": 1215}, "end": {"line": 37, "col": 22, "offset": 2015}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/template.vue", "start": {"line": 44, "col": 15, "offset": 2422}, "end": {"line": 44, "col": 52, "offset": 2459}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/template.vue", "start": {"line": 14, "col": 9, "offset": 649}, "end": {"line": 14, "col": 60, "offset": 700}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/template.vue", "start": {"line": 15, "col": 9, "offset": 728}, "end": {"line": 15, "col": 63, "offset": 782}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/template.vue", "start": {"line": 16, "col": 9, "offset": 813}, "end": {"line": 16, "col": 52, "offset": 856}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/template.vue", "start": {"line": 23, "col": 9, "offset": 1159}, "end": {"line": 35, "col": 19, "offset": 1873}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/template.vue", "start": {"line": 15, "col": 11, "offset": 741}, "end": {"line": 15, "col": 62, "offset": 792}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/template.vue", "start": {"line": 16, "col": 11, "offset": 822}, "end": {"line": 16, "col": 65, "offset": 876}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/template.vue", "start": {"line": 17, "col": 11, "offset": 909}, "end": {"line": 17, "col": 54, "offset": 952}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/template.vue", "start": {"line": 26, "col": 9, "offset": 1357}, "end": {"line": 38, "col": 19, "offset": 2071}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/textlinks/template.vue", "start": {"line": 4, "col": 7, "offset": 134}, "end": {"line": 4, "col": 77, "offset": 204}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js:\n ", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js:\n ", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js:\n ", "path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html", "start": {"line": 39, "col": 41, "offset": 0}, "end": {"line": 39, "col": 42, "offset": 1}}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html", "start": {"line": 40, "col": 43, "offset": 0}, "end": {"line": 40, "col": 44, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html:39:\n `\"` was unexpected", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html", "spans": [{"file": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html", "start": {"line": 39, "col": 41, "offset": 0}, "end": {"line": 39, "col": 42, "offset": 1}}, {"file": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html", "start": {"line": 40, "col": 43, "offset": 0}, "end": {"line": 40, "col": 44, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html", "start": {"line": 80, "col": 41, "offset": 0}, "end": {"line": 80, "col": 42, "offset": 1}}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html", "start": {"line": 81, "col": 43, "offset": 0}, "end": {"line": 81, "col": 44, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html:80:\n `\"` was unexpected", "path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html", "spans": [{"file": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html", "start": {"line": 80, "col": 41, "offset": 0}, "end": {"line": 80, "col": 42, "offset": 1}}, {"file": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html", "start": {"line": 81, "col": 43, "offset": 0}, "end": {"line": 81, "col": 44, "offset": 1}}]}], "paths": {"scanned": ["downloaded_repos/headwirecom_peregrine-cms/.github/dependabot.yml", "downloaded_repos/headwirecom_peregrine-cms/.github/workflows/pr-auto-assign-author.yml", "downloaded_repos/headwirecom_peregrine-cms/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/.travis.yml", "downloaded_repos/headwirecom_peregrine-cms/LICENSE", "downloaded_repos/headwirecom_peregrine-cms/admin-base/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/admin-base/buildmodels.bat", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/package.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/ActionModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/ColModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/ComponentexplorerModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/CreateObjectWizardModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/DebuggerModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/ExplorerModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/ExtensionsModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/IconactionModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/IconlistModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/MultiselectModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/NavModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/PageModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/PathfieldModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/Recyclable.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/SubnavModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/TabModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/TabsWrapperModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/TagModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/models/TourModel.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/AdminResourceHandler.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/AdminResourceHandlerService.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/NodeNameValidation.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/NodeNameValidationService.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/ResourceRelocation.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/ResourceRelocationService.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/security/PackageValidator.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/security/PackageValidatorService.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/AbstractPackageServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/AccessServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/AdminPaths.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/BackupTenantServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ComponentDefinitionServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ContentServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/CopyServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/CreateFolderServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/CreateObjectDefinitionServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/CreateObjectServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/CreatePageServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/CreateTemplateServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/CreateTenantServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/DeleteNodeServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/DeletePageServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/DeleteTenantServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/DownloadBackupTenantServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/GetObjectServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/InsertNodeAt.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/IsReferencedInPublishServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/IsTenantNameAvailableServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ListReplicationStatusServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ListResourceVersionsServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ListServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ListSiteRecyclablesServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ListTenantsServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ManageVersions.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/MoveNodeTo.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/MoveServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/NodeServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/NodesServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ReferenceListerServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ReferenceServletUtils.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ReferencedByListerServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ReplicationListServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ReplicationServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/ReplicationServletBase.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/RestoreRecyclableServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/RestoreTenantServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/RestrictedSearchServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/SearchServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/TenantSetupReplicationServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/UpdateResourceServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/UpdateTenantServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/UploadBackupTenantServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/UploadFilesServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/servlets/UserPreferencesServlet.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/util/AdminConstants.java", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_add-component-modal.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_buttons.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_component-explorer.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_debugger.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_dropdown.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_editor-panel.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_explorer-preview.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_explorer.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_file-upload.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_forms.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_global.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_icon-action.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_icon-browser.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_login.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_modal.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_navbar.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_path-browser.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_quill-editor.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_richtoolbar.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_sourceimagewizard.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_spectrum.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_spinner.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_tabs.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_toast.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_typography.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_variables.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_vue-form-generator.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_vue-multiselect.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_welcome-page.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/custom/_workspace.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/_buttons.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/_mixins.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/_navs-pagination.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/_variables.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/_wizard-card.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/bs_button.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/bs_nav_pills.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/bs_progress_bar.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/bs_variables.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/mixins/_buttons.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/mixins/_transparency.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/mixins/_vendor-prefixes.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/form-wizard/wizard.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/LICENSE", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Bold.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Bold.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Light.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Light.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Medium.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Medium.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Regular.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Regular.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Thin.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/fonts/roboto/Roboto-Thin.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/animation.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/buttons.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/cards.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/carousel.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/character_counter.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/chips.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/collapsible.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.date.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.time.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/dropdown.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/global.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/initial.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/jquery.easing.1.4.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/jquery.hammer.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/materialbox.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/modal.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/parallax.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/pushpin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/scrollFire.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/scrollspy.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/sideNav.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/slider.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/tabs.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/tapTarget.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/toasts.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/tooltip.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/transitions.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/waves.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_badges.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_buttons.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_cards.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_carousel.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_chips.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_collapsible.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_color.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_dropdown.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_global.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_grid.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_icons-material-design.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_materialbox.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_modal.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_navbar.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_normalize.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_preloader.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_pulse.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_roboto.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_sideNav.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_slider.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_table_of_contents.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_tabs.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_tapTarget.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_toast.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_tooltip.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_transitions.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_typography.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_variables.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/_waves.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/date_picker/_default.date.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/date_picker/_default.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/date_picker/_default.time.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_checkboxes.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_file-input.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_forms.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_input-fields.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_radio-buttons.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_range.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_select.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/components/forms/_switches.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/sass/materialize.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/package.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/peregrine.scss", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Bold.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Bold.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Light.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Light.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Medium.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Medium.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Regular.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Regular.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Thin.woff", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/fonts/roboto/Roboto-Thin.woff2", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/materialize/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/src/main/content/jcr_root/etc/felibs/materialize/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/action.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/col.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/componentexplorer.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/debugger.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/explorer.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/iconaction.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/iconlist.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/nav.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/pathfield.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/models/subnav.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/openapi/admin.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/openapi/echo.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/openapi/sessioninfo.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/sling.ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/sling.ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/sling.ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/sling.ui.apps/src/main/content/jcr_root/content/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/sling.ui.apps/src/main/content/jcr_root/index.html", "downloaded_repos/headwirecom_peregrine-cms/admin-base/sling.ui.apps/src/main/content/jcr_root/robots.txt", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/.eslintrc.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/jsdoc.config.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/nodemon.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/rollup.config.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/scripts/nodemon/exit-notification.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/scripts/nodemon/start-notification.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/buildjs/build.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/buildjs/mdtopage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/_oak_index/perComponent/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/_oak_index/perObjectDefinition/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/_oak_index/perPage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/about/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/about/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/action/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/action/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/addcomponentmodal/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/addcomponentmodal/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/askuser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/askuser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/assetbrowser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/assetbrowser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/assetview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/assetview/explorer_dialog.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/assetview/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/col/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/col/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/colorpaletteselector/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/colorpaletteselector/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/componentexplorer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/componentexplorer/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/componentlist/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/componentlist/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/configuretenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/configuretenant/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/confirmdialog/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/confirmdialog/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/container/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/contentview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/contentview/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createassetfolderwizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createassetfolderwizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectdefinitionfilewizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectdefinitionfilewizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectdefinitionfilewizard/templates.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectdefinitionwizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectdefinitionwizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectfolderwizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectfolderwizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectwizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createobjectwizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createpagewizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createpagewizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createtemplatewizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createtemplatewizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createtenantwizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/createtenantwizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/debugger/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/debugger/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/draghandle/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/draghandle/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/dropdowndivider/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/dropdowndivider/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/editor/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/editor/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/editor/template.vue.bak", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorer/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorerpreview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorerpreview/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorerpreviewcontent/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorerpreviewcontent/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorerpreviewnavitem/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/explorerpreviewnavitem/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/extensions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/extensions/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/filedropper/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/filedropper/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/fileeditor/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/fileeditor/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/filepreview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/filepreview/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/fileupload/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/fileupload/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/icon/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/icon/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconaction/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconaction/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconbrowser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconbrowser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconeditpage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconeditpage/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconlist/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconlist/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconopenfolder/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconopenfolder/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconrename/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/iconrename/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/linearpreloader/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/linearpreloader/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/logo/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materialicondropdown/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializedropdown/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializedropdown/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializedropdownfilter/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializedropdownfilter/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializemodal/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializemodal/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializespinner/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializespinner/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/materializeswitch/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/multiselect/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/multiselect/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/nav/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/nav/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/nodetreeitem/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/nodetreeitem/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/notifyuser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/notifyuser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/objectview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/objectview/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pagebrowser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pagebrowser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pageview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pageview/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pathbrowser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pathbrowser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pathfield/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/pathfield/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/promptuser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/promptuser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/publishinginfo/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/publishinginfo/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/publishingmodal/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/publishingmodal/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/querytool/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/querytool/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/recyclebin/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/actions/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/actions/redo.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/actions/undo.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/align/alignCenter.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/align/alignJustify.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/align/alignLeft.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/align/alignRight.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/align/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/alwaysActive/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/alwaysActive/preview.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/alwaysActive/previewInNewTab.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/alwaysActive/viewport.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/boldItalic/bold.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/boldItalic/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/boldItalic/italic.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/icons/icons.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/icons/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/image/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/image/insertImage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/link/editLink.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/link/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/link/insertLink.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/link/removeLink.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/list/bulletedList.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/list/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/list/numberedList.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/removeFormat/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/removeFormat/removeFormat.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/responsiveMenu/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/specialCharacters/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/specialCharacters/specialCharacters.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/superSubScript/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/superSubScript/subscript.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/superSubScript/superscript.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/textFormat/headlines.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/textFormat/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/groups/textFormat/paragraph.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbar/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbarbtn/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbarbtn/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbargroup/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/richtoolbargroup/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/row/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/row/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/separator/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/separator/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/sourceimagewizard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/sourceimagewizard/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/spinner/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/spinner/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/subnav/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/subnav/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tab/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tab/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tabswrapper/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tabswrapper/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/templateview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/templateview/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tenants/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tenants/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/toolingpage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/toolingpage/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/toolingpage/styles.html", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/toolingpage/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/toolingpage/toolingpage.html", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tour/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tour/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/tour/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/workspace/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/components/workspace/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/objects/tag/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/objects/tag/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/runmodes/config/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/admin/runmodes/config/org.apache.sling.jcr.repoinit.RepositoryInitializer-admin.config", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/collection/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/collection/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/headline/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/headline/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/horizontal-line/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/horizontal-line/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/iconbrowser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/iconbrowser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/list/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/list/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/listselection/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/listselection/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-checkbox/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-checkbox/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-checklist/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-checklist/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-datepicker/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-datepicker/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-datetime/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-datetime/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-multiselect/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-multiselect/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-radios/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-radios/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-range/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-range/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-select/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-select/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-textarea/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-textarea/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-timepicker/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/material-timepicker/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/materialswitch/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/materialswitch/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/object-definition-reference/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/object-definition-reference/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/pathbrowser/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/pathbrowser/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/apps/field/texteditor/template.vue", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/broken-image.svg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/broken-image.svg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/empty.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/empty.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla1.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla1.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla2.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla2.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla3.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla3.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla4.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla4.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla5.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/tesla5.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/trumbowyg-icons.svg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/assets/images/trumbowyg-icons.svg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/objects/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/assets/create/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/assets/source/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/config/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/config/components/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/config/felibs/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/config/querytool/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/docs/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/file/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/file/edit/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/graphiql/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/index/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/object-definitions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/object-definitions/create/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/object-definitions/createFile/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/object-definitions/edit/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/objects/create/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/objects/createFolder/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/onboard/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/onboard/sorry/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/onboard/terms/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/onboard/welcome/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/pages/create/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/pages/createtenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/pages/edit/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/recyclebin/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/templates/create/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/templates/edit/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/tenants/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/tenants/configure/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/tools/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/toolsConfig/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/tutorials/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/pages/welcome/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/admin/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/apidocs/cms/index.html", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/docs/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/docs/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/docs/object-definitions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/docs/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/docs/objects/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/docs/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/content/docs/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/css/.gitkeep", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/css.txt", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/js.txt", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/node_modules.txt", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/js/.gitkeep", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/login-image.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/login-image.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/peregrine-logo.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/peregrine-logo.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/peregrine-logo.svg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/peregrine-logo.svg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/polyfills/ResizeObserver.global.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/favicon.ico", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/aboutNavBtn/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/aboutp1/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/aboutp2/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/aboutp3/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/aboutp4/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/allGroups/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/always/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/article/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/asset title/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/assetFromPixabay/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/backToParentDir/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/cancel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/characters/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/close/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/components/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/configuration/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/content/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/copy/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/created/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/created by/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/daily/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/debugData/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/delete/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/deleteAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/deleteComponent/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/deleteObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/description/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/desktop/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/documentation/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/edit/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/editObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/editor/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/emptyExplorerHint/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/emptyExplorerHintAssets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/enter a description for this page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/enterAssetDesc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/enterFullscreen/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/exitFullscreen/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/explore/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/exportModule/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/features/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/fileUpload/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/filterComponents/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/fromTemplateNotifyMsg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/help/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/hideComponentsPanel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/hideDebugData/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/home/<USER>", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/hostname/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/hourly/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/inNewTab/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/info/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/isEmptyFolder/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/last modified/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/last modified by/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/loggingLevel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/logout/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/meta properties/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/mobile/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/monthly/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/moveAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/name/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/never/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/next/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/no/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/noAssetSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/noObjectSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/noPageSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/noTemplateSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/page name/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/page priority/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/paste/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/pathbrowserRelTitle/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/pixabaySearchHint/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/prev/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/preview/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/preview object/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/protocol/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/queryTool/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/references/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/renameAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/renameObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/replicate/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/save/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/saveObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/savePageProperties/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/screenDropdown/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/search/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/searchImageAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/select/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/select option/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/selectDatetime/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/set/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/show/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/showComponentsPanel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/showDebugData/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/siteLanguage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/sites/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/tablet/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/templateComponent/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/tenantsSelect/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/tenantsSelectTitle/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/title/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/view/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/weekly/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/words/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/yearly/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/de/yes/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/aboutNavBtn/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/aboutp1/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/aboutp2/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/aboutp3/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/aboutp4/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/allGroups/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/assetFromPixabay/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/backToParentDir/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/createdBy/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/debugData/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/deleteAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/deleteComponent/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/deleteObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/editObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/emptyExplorerHint/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/emptyExplorerHintAssets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/enterAssetDesc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/enterFullscreen/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/exitFullscreen/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/exportModule/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/fileUpload/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/filterComponents/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/fromTemplateNotifyMsg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/hideComponentsPanel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/hideDebugData/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/inNewTab/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/isEmptyFolder/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/loggingLevel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/moveAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/noAssetSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/noObjectSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/noPageSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/noTemplateSelected/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/pathbrowserRelTitle/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/pixabaySearchHint/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/queryTool/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/renameAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/renameObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/saveObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/savePageProperties/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/screenDropdown/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/searchImageAsset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/selectDatetime/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/showComponentsPanel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/showDebugData/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/siteLanguage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/templateComponent/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/tenantsSelect/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/i18n/admin/en/tenantsSelectTitle/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/live/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/acceptTermsAndConditions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/access/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/asset/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/asset/rename/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/backupTenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/componentDefinition/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/components/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/content/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/copy/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/copyPage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/createFolder/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/createObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/createObjectDefinition/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/createPage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/createPageFromSkeletonPage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/createTemplate/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/createTenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/deleteNode/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/deletePage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/deleteTenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/downloadBackupTenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/getObject/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/getObjects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/insertNodeAt/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/isReferencedInPublish.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/list/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/listRecyclables/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/listRepl/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/listReplicationStatus/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/listTenants/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/listVersions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/manageVersions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/move/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/moveNodeTo/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/nodes/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/object/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/object/rename/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/page/rename/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/readNode/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/ref/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/refBy/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/rename/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/repl/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/restoreRecyclable/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/restoreTenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/tenantSetupReplication/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/tenants/name/available.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/updateResource/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/updateTenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/uploadBackupTenant/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/admin/uploadFiles/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/definitions/admin.yaml", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/index.html", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/api.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/apiImpl.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/constants.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/experiences.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/i18n.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/logger.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/messages.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/NodeNameValidation.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/ReferenceUtil.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/apiMixin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/getBasePath.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/getTenantMixin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/index.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/isValidObjectNameMixin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/nameAvailableMixin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/stateActionsMixin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/toastMixin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/mixins/viewMixin.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/perAdminApp.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/acceptTermsAndConditions.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/addComponentToPath.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/backupTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/cancelPageEdit.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/configureTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/copyFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/copyPage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createAssetFolder.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createAssetFolderWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createFolder.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObject.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObjectDefinition.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObjectDefinitionFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObjectDefinitionFileWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObjectDefinitionWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObjectFolder.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObjectFolderWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createObjectWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createPage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createPageFromSkeletonPage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createPageWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createTemplate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createTemplateWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createTenantWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/createVersion.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteAsset.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteFolder.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteObject.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deletePage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deletePageNode.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteRecyclable.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteTemplate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/deleteVersion.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/downloadBackupTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/editComponent.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/editFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/editObject.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/editPage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/editPreview.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/editTemplate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/fetchExternalAsset.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/loadToolsNodesPath.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/moveAsset.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/moveComponentToPath.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/moveFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/moveObject.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/movePage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/moveTemplate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/publish.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/recycleItem.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/renameAsset.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/renameFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/renameObject.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/renamePage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/renameTemplate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/replicate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/restoreTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/restoreVersion.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/saveAssetProperties.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/saveFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/saveObjectEdit.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/savePageEdit.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/savePageProperties.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/selectAsset.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/selectFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/selectObject.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/selectToolsNodesPath.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/setTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/showPageInfo.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/sourceImageWizard.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/tenantSetupReplication.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/unreplicate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/unselectAsset.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/unselectFile.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/unselectObject.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/unselectPage.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/unselectTemplate.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/uploadBackupTenant.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions/uploadFiles.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/stateActions.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/utils/charMap.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/utils/htmlEncoder.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/utils/notifier.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/utils.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/validators/icon.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/validators/iconLib.js", "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/validators/tabsWrapper.js", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/buildreact.js", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/buildvue.js", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/copyfelibs.js", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/package.json", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/readme.md", "downloaded_repos/headwirecom_peregrine-cms/buildscripts/slang.js", "downloaded_repos/headwirecom_peregrine-cms/docker/.dockerignore", "downloaded_repos/headwirecom_peregrine-cms/docker/Dockerfile", "downloaded_repos/headwirecom_peregrine-cms/docker/README.md", "downloaded_repos/headwirecom_peregrine-cms/docker/builddocker-remote-replication.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/builddocker.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/env.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/files/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/docker/overlay/etc/apt/preferences.d/peregrine.pref", "downloaded_repos/headwirecom_peregrine-cms/docker/pushdocker-travis.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/pushdocker.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/run.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/scripts/healthcheck.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/scripts/install-peregrine.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/scripts/install-pkg.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/scripts/install-sling.sh", "downloaded_repos/headwirecom_peregrine-cms/docker/scripts/start.sh", "downloaded_repos/headwirecom_peregrine-cms/docs/backup-restore.md", "downloaded_repos/headwirecom_peregrine-cms/docs/blog.md", "downloaded_repos/headwirecom_peregrine-cms/docs/img/admin-page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/author-dropdown.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/author-posts-subfolders.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/blog-create-page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/blog-header-edit-working.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/blog-header-edit.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/blog-main-setup.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/create-page-step1.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/create-page-step2.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/create-page-step3.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/created-index-page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/index-page-edit-mode.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/object-create.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/object-edit.png", "downloaded_repos/headwirecom_peregrine-cms/docs/img/objects-tab.png", "downloaded_repos/headwirecom_peregrine-cms/docs/nodejs-package-configuration.md", "downloaded_repos/headwirecom_peregrine-cms/docs/projectStructure.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/distribution/distribution.in.sling.local.configuration.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/distribution/distribution.inter.sling.remote.configuration.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/distribution/distribution.local.file.system.configuration.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/distribution/distribution.local.file.system.result.tree.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/distribution/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/docker/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/1.hatch.create.test.site.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/10.hatch.detail.in.pages.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/11.hatch.detail.select.from-contact.component.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/12.hatch.detail.form-contact.on.page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/13.hatch.detail.enter.component.model.data.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/14.hatch.detail.select.list.for.save.success.page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/15.hatch.detail.save.form-contact.changes.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/16.hatch.detail.enter.first.record.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/17.hatch.mydatalist.component.details.setup.1.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/18.hatch.mydatalist.detail.page.selection.dialog.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/19.hatch.mydatalist.add.email.column.to.table.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/2.hatch.inital.pages.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/20.hatch.mydatalist.table.after.adding.table.columns.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/21.hatch.mydatalist.in.view.mode.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/22.hatch.detail.view.after.jump.from.list.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/23.hatch.mydatalist.after.editing.and.saving.detail.from.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/3.hatch.select.template.for.page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/4.hatch.enter.details.page.name.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/5.hatch.pages.with.details.page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/6.hatch.select.mydatalist.component.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/7.hatch.mydatalist.component.on.page.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/8.hatch.mydatalist.details.with.form.details.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/9.hatch.mydatalist.form.details.dialog.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/hatch/internals.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/renditions/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/renditions/renditions.image.transformation.configuration.greyscale.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/renditions/renditions.image.transformation.configuration.thumbnail.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/renditions/renditions.image.transformation.setup.configuration.greyThumbnail.old.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/renditions/renditions.image.transformation.setup.configuration.greyThumbnail.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/referenced-definition.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/referenced-resources.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/referencing-definition.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/referencing-resources.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/replication-explorer.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/replication-types.jpg", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/resource-deleted.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/resource-life-cycle.jpg", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/resource-new.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/resource-unpublished.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/resource-updated.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/root-site-deleted.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/root-site-new.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/root-site-unpublished.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/root-site-updated.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/root-site.jpg", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/sitemap-deleted.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/sitemap-new.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/replication/sitemap-updated.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-configuration-empty.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-configuration-example.com.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-file-content-builder.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-files-cache.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-resource-change-job-consumer.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-resource-change-listener.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-scheduled-cache-re-builder.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/sitemaps/site-map-structure-cache.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/versioning/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/public/versioning/versioning-panel.png", "downloaded_repos/headwirecom_peregrine-cms/docs/public/write/index.md", "downloaded_repos/headwirecom_peregrine-cms/docs/readme.md", "downloaded_repos/headwirecom_peregrine-cms/docs/workwithvltsync.md", "downloaded_repos/headwirecom_peregrine-cms/jsconfig.json", "downloaded_repos/headwirecom_peregrine-cms/jsdoc.config.json", "downloaded_repos/headwirecom_peregrine-cms/package.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/core/src/main/java/com/peregrine/pagerender/react/models/BaseModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/core/src/main/java/com/peregrine/pagerender/react/models/Container.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/core/src/main/java/com/peregrine/pagerender/react/models/PageModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/core/src/main/java/com/peregrine/pagerender/react/models/PageRenderReactConstants.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/.babelrc", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/jsdoc.config.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/rollup.config.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/components/base/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/components/base/Helper.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/components/base/base.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/components/base/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/components/base/template.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/components/placeholder/template.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/container/container.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/container/template.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/Helper.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/explorer_dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/serviceworker.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/template.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/tracker-bodyend.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/tracker-bodystart.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/tracker-head.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/content/pagerenderer/react/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/content/pagerenderer/react/about/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/etc/felibs/pagerender-react/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/etc/felibs/pagerender-react/css/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/etc/felibs/pagerender-react/js/pace.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/constants.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/index.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/logger.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/merge.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/peregrineApp.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/state.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/js/util.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/helpers/BaseHelper.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/helpers/PageHelper.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/models/BaseModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/models/Container.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/models/FooterModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/models/NavModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/models/PageModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/core/src/main/java/com/peregrine/pagerender/server/models/PageRenderServerConstants.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/jsdoc.config.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/nt/unstructured/unstructured.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/base/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/base/base.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/base/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/container/container.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/footer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/footer/footer.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/nav/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/nav/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/nav/nav.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/explorer_dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/page.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/serviceworker.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/tracker-bodyend.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/tracker-bodystart.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/components/page/tracker-head.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/apps/pagerenderserver/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/assets/peregrine-logo.png", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/objects/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/pages/about/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/pages/index/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/pages/new-empty/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/pages/non-empty-container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/templates/base/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/templates/empty-container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/templates/footer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/content/pagerenderserver/templates/nav-footer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/etc/felibs/pagerenderserver/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/etc/felibs/pagerenderserver/css/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/etc/felibs/pagerenderserver/css/server.css", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/etc/felibs/pagerenderserver/js/pace.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/core/src/main/java/com/peregrine/pagerender/vue/models/BaseModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/core/src/main/java/com/peregrine/pagerender/vue/models/Container.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/core/src/main/java/com/peregrine/pagerender/vue/models/PageModel.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/core/src/main/java/com/peregrine/pagerender/vue/models/PageRenderVueConstants.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/jsdoc.config.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/readme.md", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/rollup.config.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/components/base/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/components/base/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/components/base/template.vue", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/components/ntfile/ntfile.jsp", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/components/placeholder/template.vue", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/scripts/ssr.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/container/template.vue", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/Helper.java", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/explorer_dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/iconsprites.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/og_tag_dialog.json", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/renderer-pre.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/serviceworker.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/ssr.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/styles.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/template.vue", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/tracker-bodyend.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/tracker-bodystart.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/tracker-head.html", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/conf/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/conf/nodejs/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/conf/nodejs/ssr-html/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/object-definitions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/objects/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/pages/about/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/pages/index/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/content/pagerendervue/templates/base/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/etc/felibs/pagerendervue/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/etc/felibs/pagerendervue/css/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/etc/felibs/pagerendervue/js/loader.old", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/constants.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/experiences.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/helper.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/index.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/logger.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/merge.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/peregrineApp.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/state.js", "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/util.js", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/README.txt", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/FormReason.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/impl/AuthenticationFormServlet.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/impl/FormAuthenticationHandler.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/impl/FormLoginModulePlugin.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/impl/TokenStore.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/impl/jaas/FormCredentials.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/impl/jaas/FormLoginModule.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/java/com/peregrine/sling/auth/form/impl/jaas/JaasHelper.java", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/resources/OSGI-INF/metatype/metatype.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/auth/src/main/resources/com/peregrine/sling/auth/form/impl/login.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/platform/base/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/Filter.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/PerAsset.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/PerBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/PerPage.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/PerPageManager.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/impl/PerAssetImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/impl/PerBaseImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/impl/PerPageImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/impl/PerPageManagerImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/adaption/impl/PeregrineAdapterFactory.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/assets/AssetConstants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/graphiql/GraphiQLRequestFilter.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/intra/IntraSlingCaller.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/intra/IntraSlingCallerService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/Constants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/servlet/SchemaProviderServlet.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/PerSchemaContentResolver.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/Property.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/PropertyImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/Schema.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/SchemaImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/SchemaLoader.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/SchemaLoaderImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/SchemaLoaderService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/jsonschema/specification/SchemaLoaderServiceImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/model/api/ImageInfo.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/model/impl/ImageInfoInjector.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/models/ObjectModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/merge/PageMerge.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/merge/RenderContext.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/models/AbstractComponent.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/models/Constants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/models/Container.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/models/IComponent.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/models/NtUnstructuredModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/nodetypes/models/TextModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/process/ExternalProcessException.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/process/ProcessContext.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/process/ProcessContextReader.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/process/ProcessRunner.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/reference/Reference.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/reference/ReferenceLister.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/reference/impl/ReferenceListerService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/reference/impl/TraversingContext.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/render/RenderService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/render/RenderServiceImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/rendition/BaseResourceHandler.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/rendition/BaseResourceHandlerService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/rendition/RenditionsServlet.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/DefaultReplicationMapper.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/DefaultReplicationMapperService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/ImageMetadataSelector.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/PerReplicable.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/Replication.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/ReplicationServiceBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/ReplicationUtil.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/ReplicationsContainer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/ReplicationsContainerWithDefault.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/DistributionEvent.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/DistributionEventHandlerService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/DistributionReplicationService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/FileReplicationServiceBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/ImageMetadataSelectorService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/LocalFileSystemReplicationService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/LocalFileSystemSiteMapReplication.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/LocalReplicationService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/ModPageSpeedCacheInvalidationService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/PerReplicableImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/ReplicationMixinNodeTypesOverride.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/ReplicationsContainerImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/replication/impl/ReplicationsContainerWithDefaultImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/CacheBuilder.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/CacheBuilderBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/ConfigurationFactoryContainer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/ConfigurationFactoryContainerBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/HasName.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/NamedServiceRetriever.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/PageRecognizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/PageRecognizerBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/PageRecognizersAndChain.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/PrefixAndCutUrlExternalizerBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/PropertyProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/PropertyProviderBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapConfiguration.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapConfigurationsContainer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapConstants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapEntry.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapExtractor.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapExtractorBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapExtractorsContainer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapFileContentBuilder.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapFilesCache.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapStructureCache.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/SiteMapUrlBuilder.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/UrlExternalizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/UrlExternalizerBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/VersioningResourceResolverFactory.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/XMLBuilder.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/ApacheRewriteMapServlet.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/ChangeFreqPropertyProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/DefaultSiteMapExtractor.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/DefaultSiteMapExtractorConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/DefaultUrlExternalizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/DirectPropertyProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/DirectPropertyProviderConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/EtcMapUrlExternalizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/LastModPropertyProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/NamedServiceRetrieverImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/NonEmptyPerPageRecognizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/PageContainsPropertyRecognizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/PageContainsPropertyRecognizerConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/PerPageRecognizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/PerPageRecognizerBase.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/PrefixAndCutUrlExternalizer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/PrefixAndCutUrlExternalizerConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/PriorityPropertyProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/ScheduledSiteMapStructureCacheReBuilder.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/ScheduledSiteMapStructureCacheReBuilderConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapConfigurationImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapConfigurationImplConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapConfigurationsContainerImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapExtractorImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapExtractorsContainerImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapFileContentBuilderImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapFileContentBuilderImplConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapFilesCacheImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapFilesCacheImplConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapResourceChangeJobConsumer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapResourceChangeJobConsumerConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapResourceChangeListener.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapResourceChangeListenerConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapServlet.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapStructureCacheImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapStructureCacheImplConfig.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/SiteMapUrlBuilderImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/VersioningResourceResolverFactoryImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/sitemap/impl/XmlNamespaceUtils.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/ImageContext.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/ImageTransformation.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/ImageTransformationConfiguration.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/ImageTransformationConfigurationProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/ImageTransformationProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/ImageTransformationSetup.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/OperationContext.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/operation/AbstractVipsImageTransformation.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/operation/ConvertImageTransformation.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/operation/GreyscaleImageTransformation.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/transform/operation/ThumbnailImageTransformation.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/versions/VersionValueMap.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/versions/VersionedResource.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/versions/VersioningRequestFilter.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/java/com/peregrine/versions/VersioningResourceResolver.java", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.DefaultSiteMapExtractorConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.DirectPropertyProviderConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.PageContainsPropertyRecognizerConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.PrefixAndCutUrlExternalizerConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.ScheduledSiteMapStructureCacheReBuilderConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.SiteMapConfigurationImplConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.SiteMapFileContentBuilderImplConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.SiteMapFilesCacheImplConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.SiteMapResourceChangeJobConsumerConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.SiteMapResourceChangeListenerConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/OSGI-INF/l10n/com.peregrine.sitemap.impl.SiteMapStructureCacheImplConfig.properties", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/apps/per/sitemaps/install/org.apache.sling.serviceusermapping.impl.ServiceUserMapperImpl.amended-sitemaps.json", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/apps/per/sitemaps.json", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/content/startup/index.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/asset.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/component.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/extension.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/npmpackageconfig.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/object.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/objectdefinition.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/page.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/pagecontent.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/replication.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/site.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/main/resources/SLING-INF/nodetypes/versionable.cnd", "downloaded_repos/headwirecom_peregrine-cms/platform/base/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/Asset/Asset.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/Object/Object.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/Object/data.json.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/ObjectDefinition/ObjectDefinition.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/Page/Page.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/Page/data.json.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/Page/mergeddata.json.html", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/per/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.replication.DefaultReplicationMapperService-simple.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.replication.impl.DistributionReplicationService-remote.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.replication.impl.ImageMetadataSelectorService-iccProfile.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.replication.impl.ImageMetadataSelectorService-pngIhdr.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.replication.impl.LocalFileSystemReplicationService-local.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.replication.impl.LocalReplicationService-local.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.replication.impl.ReplicationMixinNodeTypesOverride.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.sitemap.impl.ScheduledSiteMapStructureCacheReBuilder.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.sitemap.impl.SiteMapResourceChangeJobConsumer.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.sitemap.impl.SiteMapResourceChangeListener.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-convertToWebp.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-greyThumbnailPng.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-scaling100Png.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-scaling200Png.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-scaling400Png.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-smallPng.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-thumbnailNoCropPng.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/com.peregrine.transform.ImageTransformationSetup-thumbnailPng.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/org.apache.sling.commons.log.LogManager.factory.config-peregrine.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/org.apache.sling.engine.impl.auth.SlingAuthenticator.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/org.apache.sling.jcr.repoinit.RepositoryInitializer-peregrine.config", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/org.apache.sling.serviceusermapping.impl.ServiceUserMapperImpl.amended-distributionAgentService.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/org.apache.sling.serviceusermapping.impl.ServiceUserMapperImpl.amended-distributionEventHandler.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config/org.apache.sling.serviceusermapping.impl.ServiceUserMapperImpl.amended-peregrine.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.author/com.peregrine.replication.DefaultReplicationMapperService-simple.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.author/org.apache.sling.jcr.repoinit.RepositoryInitializer-peregrineRemote.config", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.dev/org.apache.sling.commons.log.LogManager.factory.config-peregrine.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.it/com.peregrine.transform.ImageTransformationSetup-itPathA.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.it/com.peregrine.transform.ImageTransformationSetup-itPathB.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.it/org.apache.sling.commons.log.LogManager.factory.config-peregrine.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.live/com.peregrine.replication.DefaultReplicationMapperService-simple.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/apps/runmodes/config.publish/com.peregrine.sitemap.impl.DefaultSiteMapExtractor.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/base/ui.apps/src/main/content/jcr_root/perapi/public/schema/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/Chars.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/IOUtils.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/Page.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/ResourceUtils.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/Strings.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/TextUtils.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/concurrent/Callback.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/concurrent/DeBouncer.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/servlets/AbstractBaseServlet.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/servlets/ServletHelper.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/util/PerConstants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/main/java/com/peregrine/commons/util/PerUtil.java", "downloaded_repos/headwirecom_peregrine-cms/platform/commons-test/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/etc/distribution.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/org.apache.sling.distribution.agent.impl.PrivilegeDistributionRequestAuthorizationStrategyFactory-default.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/org.apache.sling.distribution.component.impl.DefaultDistributionConfigurationManager.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/org.apache.sling.jcr.repoinit.RepositoryInitializer-distribution.config", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/org.apache.sling.serviceusermapping.impl.ServiceUserMapperImpl.amended-distributionAgentService.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/resources/services/org.apache.sling.distribution.resources.impl.DistributionServiceResourceProviderFactory-agents.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/resources/services/org.apache.sling.distribution.resources.impl.DistributionServiceResourceProviderFactory-exporters.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/resources/services/org.apache.sling.distribution.resources.impl.DistributionServiceResourceProviderFactory-importers.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/resources/services/org.apache.sling.distribution.resources.impl.DistributionServiceResourceProviderFactory-triggers.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install/resources/settings/org.apache.sling.distribution.resources.impl.DistributionConfigurationResourceProviderFactory-agents.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install.author/org.apache.sling.distribution.transport.impl.UserCredentialsDistributionTransportSecretProvider-default.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install.author/publish/org.apache.sling.distribution.agent.impl.ForwardDistributionAgentFactory-publish.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install.notshared/org.apache.sling.distribution.serialization.impl.vlt.VaultDistributionPackageBuilderFactory-default.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install.publish/org.apache.sling.distribution.packaging.impl.exporter.LocalDistributionPackageExporterFactory-default.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install.publish/org.apache.sling.distribution.packaging.impl.importer.LocalDistributionPackageImporterFactory-default.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/install.shared/org.apache.sling.distribution.serialization.impl.vlt.VaultDistributionPackageBuilderFactory-default.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/service/agent/replicate.html.esp", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/service/html.esp", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/service/list/html.esp", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/services.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/setting/html.esp", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/setting/list/html.esp", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/libs/sling/distribution/settings.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/var/sling/distribution/jcrpackages.json", "downloaded_repos/headwirecom_peregrine-cms/platform/distribution/src/main/resources/SLING-CONTENT/var/sling/distribution/packages.json", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/core/src/main/java/com/peregrine/felib/models/CSSModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/core/src/main/java/com/peregrine/felib/models/FELibModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/core/src/main/java/com/peregrine/felib/models/JCRFile.java", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/core/src/main/java/com/peregrine/felib/models/JSModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/core/src/main/java/com/peregrine/felib/models/SampleRequestModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/apps/felib/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/apps/felib/components/lib/css.html", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/apps/felib/components/lib/js.html", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/apps/felib/components/lib/lib.html", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/apps/felib/components/lib/ssr.html", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/apps/felib/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/apps/felib/rumodes/config/org.apache.sling.jcr.repoinit.RepositoryInitializer-felibs.config", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/etc/felibs/example/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/etc/felibs/example/css.txt", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/etc/felibs/example/file1.css", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/etc/felibs/example/file1.js", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/etc/felibs/example/file2.css", "downloaded_repos/headwirecom_peregrine-cms/platform/felib/ui.apps/src/main/content/jcr_root/etc/felibs/example/js.txt", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/data/AbstractTypeDataFetcher.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/data/AllModelsTypeResolverService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/data/DefaultDataFetcher.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/data/TypeDataFetcher.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/data/component/ComponentTypeDataFetcher.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/data/object/ObjectTypeDataFetcher.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/GraphQLConstants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/SchemaModelBuilder.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/SchemaProvider.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/SchemaProviderService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/component/DialogJsonConstants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/component/PageSchemaModelBuilderService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/AbstractTypeModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/EnumModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/QueryTypeEnum.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/QueryTypeModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/ScalarEnum.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/SchemaModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/TypeFieldModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/TypeModel.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/TypeModelType.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/model/TypeModelTypeImpl.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/object/JSonFormConstants.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/object/JSonFormScalar.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/object/ObjectDefinitionsSchemaModelBuilderService.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/schema/util/Utils.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/core/src/main/java/com/peregrine/graphql/servlets/GraphQLSchemaServlet.java", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/index.js", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/apps/graphql/runmodes/config/com.peregrine.graphql.servlets.GraphQLSchemaServlet-default.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/apps/graphql/runmodes/config/org.apache.sling.graphql.core.GraphQLServlet-peregrine.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/content/graphiql/index.html", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/etc/graphiql/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/etc/graphiql/graphiql2.css", "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/etc/graphiql/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/platform/login/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/platform/login/src/main/resources/org/apache/sling/auth/form/impl/custom_login.html", "downloaded_repos/headwirecom_peregrine-cms/platform/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/resources/.gitignore", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/.properties/settings.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/AllFieldsModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/CarouselItemModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/CarouselModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/ColModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/CollectionModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/DatetestModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/ImageModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/JumbotronModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/NavModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/SampleModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/core/src/main/java/com/example/site/models/TextModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/carousel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/carousel/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/carousel/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/col/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/col/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/col/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/container/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/footer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/footer/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/image/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/image/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/image/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/jumbotron/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/jumbotron/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/jumbotron/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/jumbotron/thumbnail.png", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/nav/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/nav/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/page/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/page/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/row/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/row/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/secondpage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/secondpage/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/secondpage/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/text/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/text/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/text/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/text/thumbnail-v1.png", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/components/text/thumbnail-v2.png", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/config/com.peregrine.sitemap.impl.PageContainsPropertyRecognizer-description.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/config/com.peregrine.sitemap.impl.PrefixAndCutUrlExternalizer-example.com.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/config/com.peregrine.sitemap.impl.SiteMapConfigurationImpl-example.com.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/extensions/adminpagesexplorer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/extensions/adminpagessubnav/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/extensions/adminpagessubnav/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/allfields/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/allfields/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/collection/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/collection/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/datetest/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/datetest/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/sample/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/apps/example/objects/sample/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/conf/example/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/anchored.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/anchored.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/anchored.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/instagram.svg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/instagram.svg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/instagram.svg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/logo.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/logo.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/peregrine-logo.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/assets/images/peregrine-logo.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/object-definitions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/objects/allfields/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/objects/collection/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/objects/sample/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/objects/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/pages/about/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/pages/contact/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/pages/index/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/pages/manifest.json", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/pages/services/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/pages/serviceworker.js", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/content/example/templates/base/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/etc/felibs/examplesite/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/etc/felibs/examplesite/css/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/etc/felibs/examplesite/images/launcher-icon-1x.png", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/etc/felibs/examplesite/images/launcher-icon-2x.png", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/etc/felibs/examplesite/images/launcher-icon-4x.png", "downloaded_repos/headwirecom_peregrine-cms/samples/example-vue-site/ui.apps/src/main/content/jcr_root/etc/felibs/examplesite/js/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/.properties/settings.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/src/main/java/com/experiences/models/ExperienceModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/src/main/java/com/experiences/models/ExperiencesModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/src/main/java/com/experiences/models/PageModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/src/main/java/com/experiences/models/ReferenceModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/src/main/java/com/experiences/models/SubnavModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/src/main/java/com/experiences/models/TextModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/core/src/main/java/com/experiences/models/WorkspaceModel.java", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experience/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experience/model.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experience/template.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experience/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experiences/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experiences/model.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experiences/template.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/experiences/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/reference/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/reference/model.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/reference/template.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/reference/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/subnav/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/subnav/model.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/subnav/template.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/subnav/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/text/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/text/model.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/text/template.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/text/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/workspace/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/workspace/model.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/workspace/template.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/fragments/workspace/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/buildscripts/buildvue.js", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/buildscripts/readme.md", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/experience/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/experience/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/experience/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/experiences/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/experiences/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/experiences/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/page/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/page/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/reference/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/reference/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/reference/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/subnav/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/subnav/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/subnav/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/text/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/text/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/text/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/toolingpage/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/toolingpage/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/toolingpage/styles.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/toolingpage/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/toolingpage/toolingpage.html", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/workspace/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/workspace/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/components/workspace/template.vue", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/apps/experiences/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/content/assets/experiences/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/content/extensions/experiences/edit/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/content/objects/experiences/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/content/sites/experiences/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/content/templates/experiences/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/etc/felibs/experiences/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/samples/experiences/ui.apps/src/main/content/jcr_root/etc/felibs/experiences/js/experiences.js", "downloaded_repos/headwirecom_peregrine-cms/sling/peregrine-builder-sling-12/README.md", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/README.md", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core/src/main/java/com/peregrine/slingjunit/AdaptionJTest.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core/src/main/java/com/peregrine/slingjunit/PerAssetJTest.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core/src/main/java/com/peregrine/slingjunit/ReferencesJTest.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core/src/main/java/com/peregrine/slingjunit/ReplicationTestBase.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core/src/main/java/com/peregrine/slingjunit/VersionsJTest.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core/src/main/resources/SLING-INF/contents/apps/per/slingjunit/config/org.apache.sling.jcr.base.internal.LoginAdminWhitelist.fragment-slingjunit.json", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core.local/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core.local/src/main/java/com/peregrine/slingjunit/localFS/LocalFSJTest.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core.local/src/main/resources/SLING-INF/contents/apps/per/slingjunit/config/org.apache.sling.jcr.base.internal.LoginAdminWhitelist.fragment-slingjunit-local.json", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core.remote/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core.remote/src/main/java/com/peregrine/slingjunit/author/PermissionJTest.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core.remote/src/main/java/com/peregrine/slingjunit/author/RemoteReplAuthorJTest.java", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/core.remote/src/main/resources/SLING-INF/contents/apps/per/slingjunit/config/org.apache.sling.jcr.base.internal.LoginAdminWhitelist.fragment-slingjunit-remote.json", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/metadata/icc-profile/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/metadata/per-data/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/metadata/png-iccp/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/metadata/png-ihdr/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/metadata/png-phys/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/Stella.png/_jcr_content/metadata/xmp/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/logo.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/logo.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/peregrine-logo.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/assets/images/peregrine-logo.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/pages/contact/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/pages/sub-template-container-page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/pages/sub-template-page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/templates/base/sub-template/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.common/src/main/content/jcr_root/content/example/templates/base/sub-template-container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.local/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.local/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.local/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.remote/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.remote/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/package.remote/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/slingjunit.parent/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/src/license/licenses.properties", "downloaded_repos/headwirecom_peregrine-cms/src/license/peregrine-cms-license/header.txt", "downloaded_repos/headwirecom_peregrine-cms/src/license/peregrine-cms-license/license.txt", "downloaded_repos/headwirecom_peregrine-cms/src/main/buildjs/mdtopage.js", "downloaded_repos/headwirecom_peregrine-cms/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/src/main/resources/assemblies/source-release-zip-tar.xml", "downloaded_repos/headwirecom_peregrine-cms/src/main/resources/assemblies/source-release.xml", "downloaded_repos/headwirecom_peregrine-cms/src/main/resources/assemblies/source-shared.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/.properties/settings.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/AccordionModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticleheaderModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticlemediafullModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticlepagerModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticlequoteModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticletabsModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticletextblockModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticletextmediaModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ArticletwocolumnModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/BlockModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/BreadcrumbModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/CardsModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/CarouselModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/ContainerModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/FooterModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/HeaderModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/MediaModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/MediablockModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/MediavisibleModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/MenubuttonsModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/PagelistModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/PagelistnestedModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/SocialiconsModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/SpacerModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/TabsModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/TeaserhorizontalModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/TeaserverticalModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/core/src/main/java/com/themeclean/models/TextlinksModel.java", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/sample-accordionmedia.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/sample-toggle.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/sample-togglemedia.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/accordion/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articleheader/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articleheader/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articleheader/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articleheader/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articleheader/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articleheader/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlemediafull/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlemediafull/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlemediafull/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlemediafull/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlemediafull/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlemediafull/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlepager/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlepager/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlepager/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlepager/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlepager/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlepager/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlequote/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlequote/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlequote/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlequote/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlequote/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articlequote/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletabs/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextblock/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextblock/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextblock/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextblock/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextblock/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextblock/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextmedia/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextmedia/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextmedia/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextmedia/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextmedia/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletextmedia/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/articletwocolumn/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/block/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/block/model-article.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/block/model-spacer.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/block/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/block/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/block/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/breadcrumb/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/breadcrumb/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/breadcrumb/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/breadcrumb/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/breadcrumb/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/sample-cardsnobackground.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/cards/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/carousel/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/carousel/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/carousel/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/carousel/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/carousel/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/container/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/container/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/container/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/container/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/container/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/footer/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/header/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/header/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/header/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/header/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/header/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/header/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/media/blockgenerator.txt", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/media/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/media/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/media/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/media/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediablock/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediablock/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediablock/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediablock/sample-image.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediablock/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediablock/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediablock/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediavisible/blockgenerator.txt", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediavisible/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediavisible/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediavisible/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/mediavisible/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/menubuttons/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/menubuttons/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/menubuttons/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/menubuttons/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelist/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelist/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelist/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelist/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelist/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelist/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelistnested/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelistnested/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelistnested/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelistnested/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/pagelistnested/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/placeholder", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/socialicons/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/socialicons/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/socialicons/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/socialicons/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/spacer/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/spacer/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/spacer/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/spacer/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/spacer/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/sample-media.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/tabs/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaserhorizontal/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/sample-empty.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/sample-teaserfull.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/sample.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/teaservertical/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/textlinks/htmltovue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/textlinks/model.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/textlinks/template.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/fragments/textlinks/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/package-lock.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/buildscripts/buildvue.js", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/buildscripts/readme.md", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/thumbnail-sample-accordionmedia.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/thumbnail-sample-toggle.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/thumbnail-sample-togglemedia.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/accordion/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articleheader/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articleheader/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articleheader/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articleheader/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlemediafull/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlemediafull/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlemediafull/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlemediafull/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlemediafull/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlepager/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlepager/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlepager/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlepager/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlepager/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlequote/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlequote/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlequote/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlequote/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articlequote/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletabs/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletabs/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletabs/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletabs/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextblock/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextblock/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextblock/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextblock/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextblock/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextmedia/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextmedia/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextmedia/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextmedia/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletextmedia/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletwocolumn/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletwocolumn/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletwocolumn/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletwocolumn/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/articletwocolumn/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/block/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/block/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/block/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/breadcrumb/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/breadcrumb/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/breadcrumb/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/breadcrumb/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/cards/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/cards/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/cards/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/carousel/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/carousel/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/carousel/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/container/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/container/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/container/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/footer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/footer/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/footer/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/footer/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/footer/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/header/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/header/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/header/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/header/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/header/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/media/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/media/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediablock/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediablock/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediablock/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediablock/thumbnail-sample-empty.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediablock/thumbnail-sample-image.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediablock/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediavisible/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/mediavisible/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/menubuttons/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/menubuttons/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/menubuttons/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/page/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/page/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/pagelist/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/pagelist/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/pagelist/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/pagelist/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/pagelistnested/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/pagelistnested/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/pagelistnested/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/socialicons/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/socialicons/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/socialicons/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/spacer/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/spacer/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/spacer/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/spacer/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/thumbnail-sample-media.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/tabs/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaserhorizontal/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/teaservertical/thumbnail-sample.png", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/textlinks/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/textlinks/dialog.json", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/components/textlinks/template.vue", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/apps/themeclean/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/exif_thumbnail/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/interoperability/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/iptc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/01.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/exif_thumbnail/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/interoperability/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/olympus_makernote/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/013.jpg/_jcr_content/metadata/printim/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/02.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/02.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/02.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/02.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/02.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/02.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/04.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/05.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/05.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/05.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/05.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/05.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/05.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/05.jpg/_jcr_content/metadata/jpegcomment/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/exif_thumbnail/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/gps/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/iptc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/photoshop/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background1.jpg/_jcr_content/metadata/xmp/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background2.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background2.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background2.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background2.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background2.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background2.jpg/_jcr_content/metadata/jpegcomment/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/gps/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/interoperability/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background3.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/exif_thumbnail/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/interoperability/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/iptc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/photoshop/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background4.jpg/_jcr_content/metadata/xmp/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background5.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background5.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background5.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background5.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background5.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background5.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/canon_makernote/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/exif_thumbnail/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/interoperability/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background6.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face1.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face1.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face1.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face1.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face1.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face1.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face1.jpg/_jcr_content/metadata/jpegcomment/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face2.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face2.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face2.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face2.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face2.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face2.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face4.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face5.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face5.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face5.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face5.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face5.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face5.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face5.jpg/_jcr_content/metadata/jpegcomment/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face6.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face6.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face6.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face6.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face6.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/face6.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature1.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature1.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature1.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature1.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature1.jpg/_jcr_content/metadata/iptc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature1.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature1.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature2.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature2.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature2.png/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/feature2.png/_jcr_content/metadata/png-ihdr/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/jumbotron.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/logo.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/logo.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/logo.png/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/logo.png/_jcr_content/metadata/png-gama/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/logo.png/_jcr_content/metadata/png-ihdr/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/logo.png/_jcr_content/metadata/png-srgb/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/meeting_room.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/meeting_room.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/meeting_room.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/meeting_room.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/meeting_room.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/meeting_room.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/meeting_room.jpg/_jcr_content/metadata/jpegcomment/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/adobe_jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/exif_thumbnail/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/icc_profile/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/iptc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/photoshop/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/metadata/xmp/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/peregrine-logo.png/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/peregrine-logo.png/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/peregrine-logo.png/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/peregrine-logo.png/_jcr_content/metadata/png-ihdr/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/screen.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/screen.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/screen.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/screen.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/screen.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/screen.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/workers-looking-tablet.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/workers-looking-tablet.jpg/_jcr_content/_jcr_data.binary", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/workers-looking-tablet.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/workers-looking-tablet.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/workers-looking-tablet.jpg/_jcr_content/metadata/jfif/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/workers-looking-tablet.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/exif_ifd0/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/exif_subifd/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/exif_thumbnail/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/huffman/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/icc_profile/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/iptc/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/jpeg/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/photoshop/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/metadata/xmp/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/object-definitions/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/objects/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/Site 1/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 1/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 1/sub-child 1/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 1/sub-child 2/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 1/sub-child 3/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 2/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 2/other sub-child 1/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 2/other sub-child 2/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/Sample Sites/site 2/child 2/other sub-child 3/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/pages/index/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/templates/base page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/etc/felibs/themeclean/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/META-INF/maven/archetype-metadata.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/.archetype-config/archetype.properties", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/.properties/settings.json", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/Readme.md", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/core/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/fragments/.gitkeep", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/package.json", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/pom.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/META-INF/vault/filter.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/META-INF/vault/settings.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/buildscripts/buildvue.js", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/buildscripts/readme.md", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/apps/__appsFolderName__/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/apps/__appsFolderName__/components/page/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/apps/__appsFolderName__/components/page/renderer.html", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/apps/__appsFolderName__/components/page/template.vue", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/apps/__appsFolderName__/install/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/content/__contentFolderName__/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/content/__contentFolderName__/assets/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/content/__contentFolderName__/objects/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/content/__contentFolderName__/objects/tags/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/content/__contentFolderName__/pages/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/content/__contentFolderName__/templates/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/etc/felibs/__appsFolderName__/.content.xml", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/etc/felibs/__appsFolderName__/css/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/main/resources/archetype-resources/ui.apps/src/main/content/jcr_root/etc/felibs/__appsFolderName__/js/placeholder.txt", "downloaded_repos/headwirecom_peregrine-cms/travis-build.sh"], "skipped": [{"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/test/cli/peregrine.move.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/test/cli/peregrine.move.test.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/test/cli/peregrine.rename.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/test/cli/setenv.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/test/java/com/peregrine/admin/resource/PathPatternTests.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/test/java/com/peregrine/admin/security/PackageValidatorServiceTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/test/java/com/peregrine/admin/servlets/ReplicationServletTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/test/java/com/peregrine/admin/servlets/ReplicationServletTestBase.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/test/java/com/peregrine/admin/servlets/TenantSetupReplicationServletTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/test/resources/tenant-full-package-1.0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/test/resources/tenant-full-package-with-subpackage-1.0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/hammer.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/velocity.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor-bundle.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor-bundle.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor-standalone-preset.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor-standalone-preset.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/swagger-editor.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/validation.worker.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/perapi/swaggereditor/dist/validation.worker.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/docs/peregrine-teaser.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/dist/pagerender-vue-structure-page-template.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/apps/pagerender/react/structure/page/page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/test/js/testUtil.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/dist/pagerenderserver-structure-page-template.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/dist/pagerendervue-structure-page-template.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/content/jcr_root/apps/pagerendervue/structure/page/page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/vue/ui.apps/src/main/js/external/shim.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/adaption/impl/PerPageImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/adaption/impl/PerTestUtil.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/adaption/impl/PeregrineAdapterFactoryTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/reference/impl/ReferenceListerServiceSlingMockTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/reference/impl/ReferenceListerServiceTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/replication/DefaultReplicationConfigTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/replication/ReplicationUtilTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/replication/impl/LocalFileSystemReplicationServiceTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/replication/impl/PerReplicableImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/CacheBuilderBaseTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/ConfigurationFactoryContainerBaseTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/HasNameTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/PageRecognizerBaseTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/PageRecognizersAndChainTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/PrefixAndCutUrlExternalizerBaseTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/PrefixAndCutUrlExternalizerBaseTestBase.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/PropertyProviderBaseTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/SiteMapConstantsTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/SiteMapEntryTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/SiteMapExtractorBaseTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/SiteStructureTestBase.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/UrlExternalizerBaseTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/XMLBuilderTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/ApacheRewriteMapServletTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/ChangeFreqPropertyProviderTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/DefaultSiteMapExtractorTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/DefaultUrlExternalizerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/DirectPropertyProviderTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/EtcMapUrlExternalizerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/LastModPropertyProviderTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/NamedServiceRetrieverImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/NonEmptyPerPageRecognizerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/PageContainsPropertyRecognizerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/PerPageRecognizerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/PrefixAndCutUrlExternalizerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/PriorityPropertyProviderTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/ScheduledSiteMapStructureCacheReBuilderTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapConfigurationImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapConfigurationsContainerImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapExtractorImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapExtractorsContainerImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapFileContentBuilderImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapFilesCacheImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapResourceChangeJobConsumerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapResourceChangeListenerTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapServletTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapStructureCacheImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/SiteMapUrlBuilderImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/VersioningResourceResolverFactoryImplTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/java/com/peregrine/sitemap/impl/XmlNamespaceUtilsTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/resources/mj.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/base/core/src/test/resources/referenceLister-tree.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/SlingResourcesTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/SlingServletTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/TestingTools.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/CharsTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/IOUtilsTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/PageTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/ResourceUtilsTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/StringsTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/TextUtilsTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/json/JSonFormatterTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/servlets/ServletHelperTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/commons/util/PerUtilTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/BindingsMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/MockTools.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/NodeIteratorMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/PageContentMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/PageMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/PropertyIteratorMockBase.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/RangeIteratorMockBase.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/RepoMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/ResourceMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/SiteMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons/src/test/java/com/peregrine/mock/SlingHttpServletRequestMock.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons-test/src/main/java/com/peregrine/commons/test/AbstractTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/commons-test/src/main/java/com/peregrine/commons/test/ExceptionLoggingRule.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/etc/graphiql/graphiql2.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/etc/graphiql/react-dom.production.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/platform/graphql/ui.apps/src/main/content/jcr_root/etc/graphiql/react.production.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/resources/com.peregrine-cms.sling.launchpad-9.1.jar", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/background2.jpg/_jcr_content/_jcr_data.binary", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/mountain.jpg/_jcr_content/_jcr_data.binary", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/content/themeclean/assets/images/yosemite.jpg/_jcr_content/_jcr_data.binary", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/themes/themeclean/ui.apps/src/main/content/jcr_root/etc/felibs/themeclean/vendor/css/static/styles.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/test/resources/projects/basic/archetype.properties", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/headwirecom_peregrine-cms/tooling/maven/archetypes/project/src/test/resources/projects/basic/goal.txt", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.859496831893921, "profiling_times": {"config_time": 6.005485534667969, "core_time": 22.55733036994934, "ignores_time": 0.0019905567169189453, "total_time": 28.56603717803955}, "parsing_time": {"total_time": 18.666196584701538, "per_file_time": {"mean": 0.02167967083008311, "std_dev": 0.004373519262939484}, "very_slow_stats": {"time_ratio": 0.23571474744903684, "count_ratio": 0.009291521486643438}, "very_slow_files": [{"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.time.js", "ftime": 0.3185768127441406}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/etc/felibs/pagerender-react/js/pace.js", "ftime": 0.34323596954345703}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/etc/felibs/pagerenderserver/js/pace.js", "ftime": 0.37088608741760254}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/apiImpl.js", "ftime": 0.37348103523254395}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "ftime": 0.4991941452026367}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.date.js", "ftime": 0.5232079029083252}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/AdminResourceHandlerService.java", "ftime": 0.9026279449462891}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "ftime": 1.068687915802002}]}, "scanning_time": {"total_time": 118.73424983024597, "per_file_time": {"mean": 0.020823263737328304, "std_dev": 0.03985506832487541}, "very_slow_stats": {"time_ratio": 0.3777122739501339, "count_ratio": 0.002279901788846019}, "very_slow_files": [{"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/sideNav.js", "ftime": 2.076931953430176}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/apiImpl.js", "ftime": 2.376890182495117}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/server/ui.apps/src/main/content/jcr_root/etc/felibs/pagerenderserver/js/pace.js", "ftime": 2.6703171730041504}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.date.js", "ftime": 2.679216146469116}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/etc/felibs/pagerender-react/js/pace.js", "ftime": 2.9155728816986084}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/perAdminApp.js", "ftime": 3.140878915786743}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "ftime": 3.6002869606018066}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/bin/materialize.js", "ftime": 5.329824924468994}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/AdminResourceHandlerService.java", "ftime": 6.242959022521973}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "ftime": 8.504199981689453}]}, "matching_time": {"total_time": 47.429187059402466, "per_file_and_rule_time": {"mean": 0.010976437643925582, "std_dev": 0.0030153206823729265}, "very_slow_stats": {"time_ratio": 0.4126509660222366, "count_ratio": 0.01596852580421199}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/headwirecom_peregrine-cms/pagerenderer/react/ui.apps/src/main/content/jcr_root/etc/felibs/pagerender-react/js/pace.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.37872791290283203}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.3981449604034424}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.4417388439178467}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.4537360668182373}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/carousel.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5379929542541504}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/AdminResourceHandlerService.java", "rule_id": "java.lang.security.audit.formatted-sql-string.formatted-sql-string", "time": 0.5727381706237793}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/core/src/main/java/com/peregrine/admin/resource/AdminResourceHandlerService.java", "rule_id": "java.spring.security.audit.spring-sqli.spring-sqli", "time": 0.5975549221038818}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.3878917694091797}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.441220998764038}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/content/jcr_root/etc/felibs/admin/dependencies/font-families.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 2.1673929691314697}]}, "tainting_time": {"total_time": 20.59089970588684, "per_def_and_rule_time": {"mean": 0.0009721860106651018, "std_dev": 5.202519203111946e-05}, "very_slow_stats": {"time_ratio": 0.36130315730563023, "count_ratio": 0.003210576015108593}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/ui.apps/src/main/js/apiImpl.js", "fline": 331, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.16033506393432617}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/date_picker/picker.date.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.1621098518371582}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.177077054977417}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.19254517555236816}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.20159316062927246}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.20848417282104492}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.20851898193359375}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.23700189590454102}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.25924110412597656}, {"fpath": "downloaded_repos/headwirecom_peregrine-cms/admin-base/materialize/materialize-src/js/forms.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.34447693824768066}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1151092736}, "engine_requested": "OSS", "skipped_rules": []}