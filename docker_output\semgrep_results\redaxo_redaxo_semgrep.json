{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/redaxo_redaxo/.github/tests-visual/visual-record.js", "start": {"line": 260, "col": 25, "offset": 10938}, "end": {"line": 260, "col": 61, "offset": 10974}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/.tools/bin/clone-addon", "start": {"line": 33, "col": 1, "offset": 960}, "end": {"line": 33, "col": 45, "offset": 1004}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/.tools/bin/clone-addon", "start": {"line": 36, "col": 5, "offset": 1016}, "end": {"line": 36, "col": 85, "offset": 1096}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/.tools/bin/clone-addon", "start": {"line": 51, "col": 5, "offset": 1537}, "end": {"line": 51, "col": 49, "offset": 1581}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/.tools/bin/clone-addon", "start": {"line": 56, "col": 5, "offset": 1674}, "end": {"line": 56, "col": 68, "offset": 1737}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/redaxo_redaxo/docker-compose.yml", "start": {"line": 4, "col": 5, "offset": 28}, "end": {"line": 4, "col": 11, "offset": 34}, "extra": {"message": "Service 'redaxo' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/redaxo_redaxo/docker-compose.yml", "start": {"line": 4, "col": 5, "offset": 28}, "end": {"line": 4, "col": 11, "offset": 34}, "extra": {"message": "Service 'redaxo' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/redaxo_redaxo/docker-compose.yml", "start": {"line": 11, "col": 5, "offset": 186}, "end": {"line": 11, "col": 7, "offset": 188}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/redaxo_redaxo/docker-compose.yml", "start": {"line": 11, "col": 5, "offset": 186}, "end": {"line": 11, "col": 7, "offset": 188}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lib/backup.php", "start": {"line": 344, "col": 17, "offset": 12792}, "end": {"line": 344, "col": 42, "offset": 12817}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/types/phpcode.php", "start": {"line": 22, "col": 13, "offset": 495}, "end": {"line": 22, "col": 35, "offset": 517}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/api/api_core_update.php", "start": {"line": 55, "col": 17, "offset": 1991}, "end": {"line": 55, "col": 63, "offset": 2037}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/package/package_download.php", "start": {"line": 43, "col": 17, "offset": 1213}, "end": {"line": 43, "col": 66, "offset": 1262}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_convert2img.php", "start": {"line": 61, "col": 13, "offset": 1556}, "end": {"line": 61, "col": 43, "offset": 1586}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_convert2img.php", "start": {"line": 72, "col": 13, "offset": 2080}, "end": {"line": 72, "col": 36, "offset": 2103}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_convert2img.php", "start": {"line": 147, "col": 9, "offset": 4814}, "end": {"line": 147, "col": 32, "offset": 4837}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "start": {"line": 107, "col": 49, "offset": 4174}, "end": {"line": 107, "col": 121, "offset": 4246}, "extra": {"message": "RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "start": {"line": 112, "col": 66, "offset": 4466}, "end": {"line": 112, "col": 103, "offset": 4503}, "extra": {"message": "RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "start": {"line": 1078, "col": 17, "offset": 44552}, "end": {"line": 1078, "col": 73, "offset": 44608}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "start": {"line": 1281, "col": 21, "offset": 55507}, "end": {"line": 1281, "col": 66, "offset": 55552}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 1278, "col": 3, "offset": 34387}, "end": {"line": 1281, "col": 45, "offset": 34603}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 1651, "col": 17, "offset": 45296}, "end": {"line": 1652, "col": 32, "offset": 45378}, "extra": {"message": "RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 1729, "col": 17, "offset": 47340}, "end": {"line": 1729, "col": 39, "offset": 47362}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 2180, "col": 12, "offset": 59368}, "end": {"line": 2180, "col": 34, "offset": 59390}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 2195, "col": 13, "offset": 59802}, "end": {"line": 2195, "col": 35, "offset": 59824}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 2203, "col": 13, "offset": 59993}, "end": {"line": 2203, "col": 35, "offset": 60015}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 2208, "col": 8, "offset": 60192}, "end": {"line": 2208, "col": 34, "offset": 60218}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 2730, "col": 10, "offset": 75091}, "end": {"line": 2730, "col": 32, "offset": 75113}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 3058, "col": 10, "offset": 83112}, "end": {"line": 3058, "col": 30, "offset": 83132}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 4770, "col": 5, "offset": 126795}, "end": {"line": 4770, "col": 74, "offset": 126864}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "start": {"line": 6171, "col": 8, "offset": 166114}, "end": {"line": 6171, "col": 31, "offset": 166137}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/session-timeout.js", "start": {"line": 111, "col": 13, "offset": 4227}, "end": {"line": 126, "col": 19, "offset": 5014}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/session-timeout.js", "start": {"line": 230, "col": 21, "offset": 9759}, "end": {"line": 230, "col": 90, "offset": 9828}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/session-timeout.js", "start": {"line": 233, "col": 25, "offset": 10022}, "end": {"line": 233, "col": 150, "offset": 10147}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/session-timeout.js", "start": {"line": 273, "col": 21, "offset": 11845}, "end": {"line": 273, "col": 97, "offset": 11921}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/session-timeout.js", "start": {"line": 275, "col": 25, "offset": 12006}, "end": {"line": 275, "col": 150, "offset": 12131}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/form_base.php", "start": {"line": 1182, "col": 21, "offset": 34416}, "end": {"line": 1182, "col": 65, "offset": 34460}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/form_base.php", "start": {"line": 1208, "col": 21, "offset": 35826}, "end": {"line": 1208, "col": 67, "offset": 35872}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/form_base.php", "start": {"line": 1221, "col": 17, "offset": 36484}, "end": {"line": 1221, "col": 64, "offset": 36531}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/sql.php", "start": {"line": 364, "col": 27, "offset": 10191}, "end": {"line": 364, "col": 48, "offset": 10212}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/file.php", "start": {"line": 101, "col": 14, "offset": 3037}, "end": {"line": 101, "col": 30, "offset": 3053}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/file.php", "start": {"line": 224, "col": 20, "offset": 7113}, "end": {"line": 224, "col": 36, "offset": 7129}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/file.php", "start": {"line": 239, "col": 27, "offset": 7473}, "end": {"line": 239, "col": 40, "offset": 7486}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/file.php", "start": {"line": 245, "col": 28, "offset": 7717}, "end": {"line": 245, "col": 41, "offset": 7730}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/path.php", "start": {"line": 420, "col": 9, "offset": 10263}, "end": {"line": 420, "col": 32, "offset": 10286}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/version.php", "start": {"line": 144, "col": 13, "offset": 5418}, "end": {"line": 144, "col": 59, "offset": 5464}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/version.php", "start": {"line": 152, "col": 9, "offset": 5724}, "end": {"line": 152, "col": 56, "offset": 5771}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.phpinfo-use.phpinfo-use", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.phpinfo.php", "start": {"line": 7, "col": 1, "offset": 46}, "end": {"line": 7, "col": 11, "offset": 56}, "extra": {"message": "The 'phpinfo' function may reveal sensitive information about your environment.", "metadata": {"cwe": ["CWE-200: Exposure of Sensitive Information to an Unauthorized Actor"], "references": ["https://www.php.net/manual/en/function.phpinfo", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/PhpinfosSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2021 - Broken Access Control"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.phpinfo-use.phpinfo-use", "shortlink": "https://sg.run/W82E"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/package.yml:20:\n (approximate error location; error nearby after) error calling parser: did not find expected ',' or '}' character 0 position 0 returned: 0", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/package.yml"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.github/workflows/unit-tests.yml", "start": {"line": 68, "col": 45, "offset": 2450}, "end": {"line": 68, "col": 64, "offset": 2469}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.github/workflows/unit-tests.yml:68:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.github/workflows/unit-tests.yml", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.github/workflows/unit-tests.yml", "start": {"line": 68, "col": 45, "offset": 2450}, "end": {"line": 68, "col": 64, "offset": 2469}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 6, "col": 23, "offset": 22}}, {"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 2, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php:6:\n `namespace ${NAMESPACE}` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 6, "col": 23, "offset": 22}}, {"file": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 2, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 6, "col": 23, "offset": 22}}, {"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 2, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php:6:\n `namespace ${NAMESPACE}` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 6, "col": 23, "offset": 22}}, {"file": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 2, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 6, "col": 23, "offset": 22}}, {"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 2, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php:6:\n `namespace ${NAMESPACE}` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 6, "col": 23, "offset": 22}}, {"file": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 2, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php:17:\n `readonly` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseExpectedNameResolver.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseExpectedNameResolver.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseExpectedNameResolver.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseExpectedNameResolver.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 8}}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 8}}, {"file": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lib/cronjob.php", "start": {"line": 20, "col": 99, "offset": 0}, "end": {"line": 20, "col": 102, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lib/cronjob.php:20:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lib/cronjob.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lib/cronjob.php", "start": {"line": 20, "col": 99, "offset": 0}, "end": {"line": 20, "col": 102, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media.php", "start": {"line": 116, "col": 36, "offset": 0}, "end": {"line": 116, "col": 39, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media.php:116:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media.php", "start": {"line": 116, "col": 36, "offset": 0}, "end": {"line": 116, "col": 39, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php", "start": {"line": 98, "col": 73, "offset": 0}, "end": {"line": 98, "col": 76, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php", "start": {"line": 249, "col": 80, "offset": 0}, "end": {"line": 249, "col": 83, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php:98:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php", "start": {"line": 98, "col": 73, "offset": 0}, "end": {"line": 98, "col": 76, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php", "start": {"line": 249, "col": 80, "offset": 0}, "end": {"line": 249, "col": 83, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 120, "col": 65, "offset": 0}, "end": {"line": 120, "col": 68, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 121, "col": 66, "offset": 0}, "end": {"line": 121, "col": 69, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 123, "col": 62, "offset": 0}, "end": {"line": 123, "col": 65, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 124, "col": 64, "offset": 0}, "end": {"line": 124, "col": 67, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 126, "col": 77, "offset": 0}, "end": {"line": 126, "col": 80, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php:120:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 120, "col": 65, "offset": 0}, "end": {"line": 120, "col": 68, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 121, "col": 66, "offset": 0}, "end": {"line": 121, "col": 69, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 123, "col": 62, "offset": 0}, "end": {"line": 123, "col": 65, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 124, "col": 64, "offset": 0}, "end": {"line": 124, "col": 67, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "start": {"line": 126, "col": 77, "offset": 0}, "end": {"line": 126, "col": 80, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 95, "col": 69, "offset": 0}, "end": {"line": 95, "col": 72, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 96, "col": 70, "offset": 0}, "end": {"line": 96, "col": 73, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 98, "col": 66, "offset": 0}, "end": {"line": 98, "col": 69, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 99, "col": 68, "offset": 0}, "end": {"line": 99, "col": 71, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 101, "col": 81, "offset": 0}, "end": {"line": 101, "col": 84, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php:95:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 95, "col": 69, "offset": 0}, "end": {"line": 95, "col": 72, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 96, "col": 70, "offset": 0}, "end": {"line": 96, "col": 73, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 98, "col": 66, "offset": 0}, "end": {"line": 98, "col": 69, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 99, "col": 68, "offset": 0}, "end": {"line": 99, "col": 71, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "start": {"line": 101, "col": 81, "offset": 0}, "end": {"line": 101, "col": 84, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 209, "col": 70, "offset": 0}, "end": {"line": 209, "col": 73, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 210, "col": 69, "offset": 0}, "end": {"line": 210, "col": 72, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 212, "col": 66, "offset": 0}, "end": {"line": 212, "col": 69, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 213, "col": 68, "offset": 0}, "end": {"line": 213, "col": 71, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 215, "col": 85, "offset": 0}, "end": {"line": 215, "col": 88, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php:209:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 209, "col": 70, "offset": 0}, "end": {"line": 209, "col": 73, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 210, "col": 69, "offset": 0}, "end": {"line": 210, "col": 72, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 212, "col": 66, "offset": 0}, "end": {"line": 212, "col": 69, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 213, "col": 68, "offset": 0}, "end": {"line": 213, "col": 71, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "start": {"line": 215, "col": 85, "offset": 0}, "end": {"line": 215, "col": 88, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/navigation.php", "start": {"line": 266, "col": 48, "offset": 0}, "end": {"line": 266, "col": 51, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/navigation.php:266:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/navigation.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/navigation.php", "start": {"line": 266, "col": 48, "offset": 0}, "end": {"line": 266, "col": 51, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/pages/linkmap.php", "start": {"line": 75, "col": 13, "offset": 0}, "end": {"line": 75, "col": 23, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/pages/linkmap.php:75:\n `$funcBody,` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/pages/linkmap.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/pages/linkmap.php", "start": {"line": 75, "col": 13, "offset": 0}, "end": {"line": 75, "col": 23, "offset": 10}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/system_setting_default_template_id.php", "start": {"line": 32, "col": 68, "offset": 0}, "end": {"line": 32, "col": 71, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/system_setting_default_template_id.php:32:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/system_setting_default_template_id.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/system_setting_default_template_id.php", "start": {"line": 32, "col": 68, "offset": 0}, "end": {"line": 32, "col": 71, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php", "start": {"line": 60, "col": 51, "offset": 0}, "end": {"line": 60, "col": 54, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php", "start": {"line": 77, "col": 48, "offset": 0}, "end": {"line": 77, "col": 51, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php:60:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php", "start": {"line": 60, "col": 51, "offset": 0}, "end": {"line": 60, "col": 54, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php", "start": {"line": 77, "col": 48, "offset": 0}, "end": {"line": 77, "col": 51, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/page.php", "start": {"line": 477, "col": 49, "offset": 0}, "end": {"line": 477, "col": 52, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/page.php:477:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/page.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/page.php", "start": {"line": 477, "col": 49, "offset": 0}, "end": {"line": 477, "col": 52, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php", "start": {"line": 32, "col": 54, "offset": 0}, "end": {"line": 32, "col": 57, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php", "start": {"line": 50, "col": 54, "offset": 0}, "end": {"line": 50, "col": 57, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php:32:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php", "start": {"line": 32, "col": 54, "offset": 0}, "end": {"line": 32, "col": 57, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php", "start": {"line": 50, "col": 54, "offset": 0}, "end": {"line": 50, "col": 57, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php", "start": {"line": 547, "col": 54, "offset": 0}, "end": {"line": 547, "col": 57, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php", "start": {"line": 565, "col": 54, "offset": 0}, "end": {"line": 565, "col": 57, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php:547:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php", "start": {"line": 547, "col": 54, "offset": 0}, "end": {"line": 547, "col": 57, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php", "start": {"line": 565, "col": 54, "offset": 0}, "end": {"line": 565, "col": 57, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php", "start": {"line": 25, "col": 45, "offset": 0}, "end": {"line": 25, "col": 48, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php", "start": {"line": 26, "col": 53, "offset": 0}, "end": {"line": 26, "col": 56, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php:25:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php", "start": {"line": 25, "col": 45, "offset": 0}, "end": {"line": 25, "col": 48, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php", "start": {"line": 26, "col": 53, "offset": 0}, "end": {"line": 26, "col": 56, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/addon.php", "start": {"line": 131, "col": 51, "offset": 0}, "end": {"line": 131, "col": 54, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/addon.php:131:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/addon.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/addon.php", "start": {"line": 131, "col": 51, "offset": 0}, "end": {"line": 131, "col": 54, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/manager.php", "start": {"line": 628, "col": 51, "offset": 0}, "end": {"line": 628, "col": 54, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/manager.php:628:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/manager.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/manager.php", "start": {"line": 628, "col": 51, "offset": 0}, "end": {"line": 628, "col": 54, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/null.php", "start": {"line": 134, "col": 51, "offset": 0}, "end": {"line": 134, "col": 54, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/null.php:134:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/null.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/null.php", "start": {"line": 134, "col": 51, "offset": 0}, "end": {"line": 134, "col": 54, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/package.php", "start": {"line": 307, "col": 82, "offset": 0}, "end": {"line": 307, "col": 85, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/package.php:307:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/package.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/package.php", "start": {"line": 307, "col": 82, "offset": 0}, "end": {"line": 307, "col": 85, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/plugin.php", "start": {"line": 150, "col": 55, "offset": 0}, "end": {"line": 150, "col": 58, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/plugin.php:150:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/plugin.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/plugin.php", "start": {"line": 150, "col": 55, "offset": 0}, "end": {"line": 150, "col": 58, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php", "start": {"line": 346, "col": 46, "offset": 0}, "end": {"line": 346, "col": 49, "offset": 3}}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php", "start": {"line": 348, "col": 48, "offset": 0}, "end": {"line": 348, "col": 51, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php:346:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php", "start": {"line": 346, "col": 46, "offset": 0}, "end": {"line": 346, "col": 49, "offset": 3}}, {"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php", "start": {"line": 348, "col": 48, "offset": 0}, "end": {"line": 348, "col": 51, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/sql.php", "start": {"line": 2098, "col": 73, "offset": 0}, "end": {"line": 2098, "col": 76, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/sql.php:2098:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/sql.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/sql.php", "start": {"line": 2098, "col": 73, "offset": 0}, "end": {"line": 2098, "col": 76, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/table.php", "start": {"line": 973, "col": 59, "offset": 0}, "end": {"line": 973, "col": 62, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/table.php:973:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/table.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/table.php", "start": {"line": 973, "col": 59, "offset": 0}, "end": {"line": 973, "col": 62, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var_dumper.php", "start": {"line": 25, "col": 50, "offset": 0}, "end": {"line": 25, "col": 53, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var_dumper.php:25:\n `...` was unexpected", "path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var_dumper.php", "spans": [{"file": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var_dumper.php", "start": {"line": 25, "col": 50, "offset": 0}, "end": {"line": 25, "col": 53, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/redaxo_redaxo/.dockerignore", "downloaded_repos/redaxo_redaxo/.editorconfig", "downloaded_repos/redaxo_redaxo/.gitattributes", "downloaded_repos/redaxo_redaxo/.github/.kodiak.toml", "downloaded_repos/redaxo_redaxo/.github/CODE_OF_CONDUCT.md", "downloaded_repos/redaxo_redaxo/.github/FUNDING.yml", "downloaded_repos/redaxo_redaxo/.github/ISSUE_TEMPLATE/---bug-report.md", "downloaded_repos/redaxo_redaxo/.github/ISSUE_TEMPLATE/---feature-request.md", "downloaded_repos/redaxo_redaxo/.github/ISSUE_TEMPLATE/---security-issue.md", "downloaded_repos/redaxo_redaxo/.github/dependabot.yml", "downloaded_repos/redaxo_redaxo/.github/imports/README.md", "downloaded_repos/redaxo_redaxo/.github/imports/inital-content-for-visual-tests.sql", "downloaded_repos/redaxo_redaxo/.github/imports/media/redaxo_2018_berlin_sticker.jpg", "downloaded_repos/redaxo_redaxo/.github/tests-visual/backup_export--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/backup_export.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/backup_import--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/backup_import.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/backup_import_server--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/backup_import_server.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/cronjob_cronjobs--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/cronjob_cronjobs.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/cronjob_cronjobs_add--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/cronjob_cronjobs_add.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/debug_clockwork--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/debug_clockwork.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/index--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/index.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_settings--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_settings.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_types--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_types.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_types_add--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_types_add.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_types_edit--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/media_manager_types_edit.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_media--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_media.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_media_file--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_media_file.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_structure--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_structure.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_sync--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_sync.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_upload--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/mediapool_upload.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_articles--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_articles.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_articles_add--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_articles_add.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_categories--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_categories.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_clangs--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_clangs.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_media--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/metainfo_media.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_actions--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_actions.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_actions_add--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_actions_add.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_modules--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_modules.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_modules_add--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/modules_modules_add.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/packages--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/packages.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/packages_customizer_installed--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/packages_customizer_installed.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/packages_help--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/packages_help.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/phpmailer_config--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/phpmailer_config.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_2--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_2.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_3--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_3.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_4--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_4.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_5--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_5.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_6--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/setup_6.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_article_edit--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_article_edit.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_article_functions--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_article_functions.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_category_edit--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_category_edit.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_slice_edit--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/structure_slice_edit.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_customizer--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_customizer.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_lang--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_lang.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_log--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_log.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_report--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_report.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_settings--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_settings.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_settings_safemode--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/system_settings_safemode.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/templates--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/templates.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/templates_add--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/templates_add.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/templates_edit--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/templates_edit.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_edit--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_edit.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_role_add--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_role_add.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_roles--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_roles.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_users--dark.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/users_users.png", "downloaded_repos/redaxo_redaxo/.github/tests-visual/visual-record.js", "downloaded_repos/redaxo_redaxo/.github/workflows/chatops.yml", "downloaded_repos/redaxo_redaxo/.github/workflows/code-style.yml", "downloaded_repos/redaxo_redaxo/.github/workflows/remove-automerge-label.yml", "downloaded_repos/redaxo_redaxo/.github/workflows/rexlint.yml", "downloaded_repos/redaxo_redaxo/.github/workflows/static-analysis.yml", "downloaded_repos/redaxo_redaxo/.github/workflows/styles-compile.yml", "downloaded_repos/redaxo_redaxo/.github/workflows/unit-tests.yml", "downloaded_repos/redaxo_redaxo/.github/workflows/visual-tests.yml", "downloaded_repos/redaxo_redaxo/.gitignore", "downloaded_repos/redaxo_redaxo/.gitignore.example", "downloaded_repos/redaxo_redaxo/.idea/codeStyles/Project.xml", "downloaded_repos/redaxo_redaxo/.idea/codeStyles/codeStyleConfig.xml", "downloaded_repos/redaxo_redaxo/.idea/encodings.xml", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/code/PHP Constructor.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/code/PHP Fluent Setter Method.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/code/PHP Getter Method.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/code/PHP Setter Method.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/includes/PHP Class Doc Comment.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/includes/PHP Property Doc Comment.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php", "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php", "downloaded_repos/redaxo_redaxo/.idea/icon.svg", "downloaded_repos/redaxo_redaxo/.idea/inspectionProfiles/Project_Default.xml", "downloaded_repos/redaxo_redaxo/.idea/jsonSchemas.xml", "downloaded_repos/redaxo_redaxo/.idea/runConfigurations/phpunit.xml", "downloaded_repos/redaxo_redaxo/.idea/scopes/core___core_addons.xml", "downloaded_repos/redaxo_redaxo/.idea/sqldialects.xml", "downloaded_repos/redaxo_redaxo/.php-cs-fixer.dist.php", "downloaded_repos/redaxo_redaxo/.tools/bin/clone-addon", "downloaded_repos/redaxo_redaxo/.tools/bin/rebase-to-bugfix", "downloaded_repos/redaxo_redaxo/.tools/bin/refresh", "downloaded_repos/redaxo_redaxo/.tools/bin/reinstall-core", "downloaded_repos/redaxo_redaxo/.tools/bin/release", "downloaded_repos/redaxo_redaxo/.tools/bin/update-all", "downloaded_repos/redaxo_redaxo/.tools/bin/update-root-composer", "downloaded_repos/redaxo_redaxo/.tools/bin/update-tools", "downloaded_repos/redaxo_redaxo/.tools/bootstrap.php", "downloaded_repos/redaxo_redaxo/.tools/constants.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/_loader.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/argument.templateType.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/argument.type.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/assign.propertyType.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/booleanAnd.rightAlwaysFalse.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/catch.neverThrown.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/deadCode.unreachable.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/function.alreadyNarrowedType.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/if.alwaysTrue.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/impure.propertyAssign.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/method.childReturnType.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/missingType.generics.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/missingType.iterableValue.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/missingType.parameter.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/possiblyImpure.functionCall.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/possiblyImpure.methodCall.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/return.type.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/staticClassAccess.privateMethod.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/throws.unusedType.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/baseline/varTag.nativeType.php", "downloaded_repos/redaxo_redaxo/.tools/phpstan/console.php", "downloaded_repos/redaxo_redaxo/.tools/psalm/baseline-taint.xml", "downloaded_repos/redaxo_redaxo/.tools/psalm/baseline.xml", "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseExpectedNameResolver.php", "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "downloaded_repos/redaxo_redaxo/LICENSE.md", "downloaded_repos/redaxo_redaxo/README.de.md", "downloaded_repos/redaxo_redaxo/README.md", "downloaded_repos/redaxo_redaxo/SECURITY.md", "downloaded_repos/redaxo_redaxo/assets/.redaxo", "downloaded_repos/redaxo_redaxo/composer.json", "downloaded_repos/redaxo_redaxo/docker-compose.yml", "downloaded_repos/redaxo_redaxo/index.php", "downloaded_repos/redaxo_redaxo/media/.redaxo", "downloaded_repos/redaxo_redaxo/phpstan.dist.neon", "downloaded_repos/redaxo_redaxo/phpunit.dist.xml", "downloaded_repos/redaxo_redaxo/psalm.xml", "downloaded_repos/redaxo_redaxo/rector.php", "downloaded_repos/redaxo_redaxo/redaxo/bin/.htaccess", "downloaded_repos/redaxo_redaxo/redaxo/bin/console", "downloaded_repos/redaxo_redaxo/redaxo/cache/.htaccess", "downloaded_repos/redaxo_redaxo/redaxo/cache/.redaxo", "downloaded_repos/redaxo_redaxo/redaxo/data/.htaccess", "downloaded_repos/redaxo_redaxo/redaxo/data/.redaxo", "downloaded_repos/redaxo_redaxo/redaxo/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/.htaccess", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/composer.json", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/composer.lock", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lib/backup.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lib/compressor.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lib/cronjob.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/lib/tar.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/pages/export.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/pages/import.server.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/pages/import.upload.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/assets/css/bad-styles.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/assets/css/styles.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/assets/images/redaxo-logo.svg", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/assets/javascripts/main.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/composer.json", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/composer.lock", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/lib/be_style.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/lib/command/compile.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/lib/scss_compiler.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/assets/css/styles.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/assets/js/main.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/help.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/pages/system.customizer.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/css/styles.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/android-chrome-192x192.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/android-chrome-512x512.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/apple-touch-icon.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/browserconfig.xml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/favicon-16x16.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/favicon-32x32.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/favicon.ico", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/mstile-144x144.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/mstile-150x150.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/mstile-310x150.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/mstile-310x310.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/mstile-70x70.png", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/safari-pinned-tab.svg", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/icons/site.webmanifest", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/images/lucas-k-wQLAGv4_OYs-unsplash-2400.avif", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/images/lucas-k-wQLAGv4_OYs-unsplash-2400.webp", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/images/lucas-k-wQLAGv4_OYs-unsplash-3500.avif", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/images/lucas-k-wQLAGv4_OYs-unsplash-3500.webp", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/assets/javascripts/redaxo.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/help.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_alerts.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_bootstrap-dark-overrides.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_buttons.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_code.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_collapse.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_customizer.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_docs.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_dropdowns.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_form.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_impersonate.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_list-groups.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_list.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_loader.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_login.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_modal.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_navs.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_panels.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_reset.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_safemode.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_scaffolding.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_sticky.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_tables.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_typo.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_utilities.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_variables-dark.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/_variables.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_alerts.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_badges.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_breadcrumbs.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_buttons.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_close.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_code.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_dropdowns.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_forms.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_input-groups.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_jumbotron.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_labels.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_list-group.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_mixins.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_modals.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_navbar.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_navs.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_pager.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_pagination.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_panels.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_popovers.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_progress-bars.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_scaffolding.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_tables.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_thumbnails.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_type.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/_wells.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/bootstrap-dark-overrides/mixins/_buttons.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/default.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/redaxo/scss/master.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/_base.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/_helper.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/_icon.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/_mime.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/master.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/mixins/_buttons.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/mixins/_form.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/scss/mixins/_helper.scss", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor_files.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/bin/run", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/command/run.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/cronjob.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/form.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/manager.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/manager_sql.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/types/phpcallback.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/types/phpcode.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/lib/types/urlrequest.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/pages/cronjobs.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/pages/log.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/pages/system.log.cronjob.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/help.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/lib/cronjob.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/article_status/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/help.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lib/cronjob.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/composer.json", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/composer.lock", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/frontend/build.sh", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/frontend/frontend.zip", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lib/api_debug.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lib/debug.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lib/debug_clockwork.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lib/extensions/api_function_debug.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lib/extensions/extension_debug.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lib/extensions/logger_debug.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/lib/extensions/sql_debug.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/api/api_core_update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/api/api_package_add.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/api/api_package_delete.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/api/api_package_update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/api/api_package_upload.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/archive.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/command/download.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/command/list.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/command/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/package/package_add.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/package/package_download.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/package/package_update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/packages.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/lib/webservice.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/pages/packages.add.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/pages/packages.update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/pages/packages.upload.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/pages/settings.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/install/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effect_abstract.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_convert2img.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_crop.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_filter_blur.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_filter_brightness.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_filter_colorize.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_filter_contrast.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_filter_greyscale.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_filter_sepia.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_filter_sharpen.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_flip.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_header.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_image_format.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_image_properties.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_insert_image.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_mediapath.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_mirror.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_resize.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_rotate.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_rounded_corners.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/effects/effect_workspace.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/managed_media.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/media_manager.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/lib/not_found_exception.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/pages/effects.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/pages/settings.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/pages/types.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/assets/mediapool.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/functions/function_rex_mediapool.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/cache_media.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/form_element/media.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/form_element/medialist.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category_select.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/mediapool.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/perm.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/service_media.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/service_media_category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/var_media.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/var_medialist.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/pages/media.detail.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/pages/media.list.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/pages/media.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/pages/structure.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/pages/sync.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/pages/upload.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/assets/metainfo.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/assets/metainfo.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/extensions/extension_cleanup.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/functions/function_metainfo.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/default_type.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/api_default_fields.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/article_handler.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/handler.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/date.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/datetime.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/linkbutton.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/linklistbutton.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/mediabutton.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/medialistbutton.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/select.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/text.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/textarea.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input/time.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/input.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/restrictions_element.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/table_expander.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/table_manager.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/pages/content.metainfo.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/pages/field.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/composer.json", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/composer.lock", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lib/cronjob.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lib/mailer.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/lib/system_setting_phpmailer_errormail.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/pages/checkmail.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/pages/config.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/pages/log.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/pages/system.log.phpmailer.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/project/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/project/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/project/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/project/lib/.redaxo", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/project/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/README.de.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/README.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/assets/linkmap.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/functions/function_rex_category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/functions/function_rex_url.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article2category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article2startarticle.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article_add.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article_copy.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article_delete.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article_edit.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article_move.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_article_status.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_category2article.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_category_add.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_category_delete.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_category_edit.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_category_move.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/api_functions/api_category_status.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/article.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/cache_article.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/api_sitemap.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/form_element/linklist.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/form_element/linkmap.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/linkmap.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/renderer.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/sitemap.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/var_link.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/linkmap/var_linklist.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/navigation.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/perm.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/select_category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/service_article.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/service_category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/structure_context.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/structure_element.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/system_setting_article_id.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/var_article.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/var_category.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/pages/linkmap.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/assets/content.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/fragments/module_select.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/fragments/slice_menu_action.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/fragments/slice_menu_ep.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/fragments/slice_menu_move.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/help.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/api_ctype.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/api_functions/api_content.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/api_functions/api_content_copy.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/api_functions/api_content_slice_status.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/api_module.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/api_template.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/article_action.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/article_content.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/article_content_base.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/article_content_editor.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/article_not_found_exception.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/article_slice.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/cache_module.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/cache_template.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/content_service.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/event_select.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/extension_point/art_content_updated.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/extension_point/slice_menu.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/module_perm.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/select_template.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/system_setting_default_template_id.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/var_template.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/var_value.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/pages/content.edit.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/pages/content.functions.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/pages/content.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/pages/modules.actions.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/pages/modules.modules.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/pages/modules.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/pages/templates.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/history.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/history.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.min.css", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/fragments/history/layer.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lib/article_slice_history.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lib/cronjob.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/lib/history_login.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/pages/system.history.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/lib/revision.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/version/uninstall.sql", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/LICENSE", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/help.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lib/role.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/lib/select_element.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/package.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/pages/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/pages/roles.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/pages/users.php", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/uninstall.sql", "downloaded_repos/redaxo_redaxo/redaxo/src/addons/users/update.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/.phpstorm.meta.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/CHANGELOG.md", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/clipboard-copy-element.js", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery-ui.custom.txt", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.min.map", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/redaxo-logo.svg", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/session-timeout.js", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/sha1.js", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/standard.js", "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/webauthn.js", "downloaded_repos/redaxo_redaxo/redaxo/src/core/backend.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/boot.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/composer.json", "downloaded_repos/redaxo_redaxo/redaxo/src/core/composer.lock", "downloaded_repos/redaxo_redaxo/redaxo/src/core/console.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/default.config.yml", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/be_ooops.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/bottom.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/buttons/button.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/buttons/button_group.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/dropdowns/dropdown.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/fe_ooops.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/footer.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/checkbox.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/container.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/form.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/input_group.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/radio.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/search.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/submit.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/widget.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/form/widget_list.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/header.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/login_background.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/login_branding.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/navigation.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/navigations/breadcrumb.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/navigations/content.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/navigations/main.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/navigations/meta.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/navigations/pagination.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/page/docs.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/page/grid.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/page/header.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/page/main_content.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/page/readme.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/page/section.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/toolbar.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/fragments/core/top.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/frontend.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/functions/function_rex_escape.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/functions/function_rex_globals.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/functions/function_rex_other.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/de_de.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/en_gb.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/es_es.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/it_it.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/nl_nl.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/pt_br.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/ru_ru.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lang/sv_se.lang", "downloaded_repos/redaxo_redaxo/redaxo/src/core/layout/bottom.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/layout/top.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/api_function.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/base/factory_trait.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/base/instance_list_pool_trait.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/base/instance_pool_trait.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/base/singleton_trait.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/controller.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/navigation.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/page.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/page_main.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/clang/clang.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/clang/perm.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/clang/service.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/config.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/config_db.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/application.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/assets_sync.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/cache/clear.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/command.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/command_loader.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/command_only_setup_packages.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/command_standalone.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/config/get.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/config/set.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/db/connection_options.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/db/dump_schema.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/db/set_connection.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/extension_point_console_shutdown.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/list_command.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/package/activate.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/package/deactivate.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/package/delete.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/package/install.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/package/list.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/package/run_update_script.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/package/uninstall.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/system/report.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/user/create.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/user/delete.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/user/list.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/user/set_password.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/context.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/csrf_token.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/exception.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/extension.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/extension_point.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/config_form.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/checkbox.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/container.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/control.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/element.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/options.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/prio.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/radio.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/raw.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/elements/select.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/form.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/form/form_base.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/fragment.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/list.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/api_has_user_session.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/api_user_impersonate.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/api_user_remove_auth_method.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/api_user_remove_session.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/api_user_session_status.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/backend_login.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/backend_password_policy.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/complex_perm.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/login.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/login_policy.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/password_policy.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/perm.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/role.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/user.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/user_session.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/login/webauthn.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/addon.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/interface.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/manager.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/null.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/api_package.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/ep_package_cache_deleted.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/interface.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/manager.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/null.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/package.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/interface.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/manager.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/null.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/plugin.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/request.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/rex.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/select.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/setup/import.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/setup/setup.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/column.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/foreign_key.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/index.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/schema_dumper.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/sql.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/table.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/util.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/system_report.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/system_setting.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/dir.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/editor.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/file.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/finder.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/formatter.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/i18n.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/log_file.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/logger.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/markdown.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/pager.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/path.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/path_default_provider.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/socket/socket.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/socket/socket_proxy.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/socket/socket_response.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/sortable_iterator.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/stream.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/string.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/timer.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/type.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/url.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/validation_rule.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/validator.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/util/version.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var/clang.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var/config.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var/property.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var/var.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var_dumper.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/view.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/packages.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/credits.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/login.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/packages.changelog.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/packages.details.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/packages.help.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/packages.license.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/packages.list.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/packages.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/profile.auth_methods.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/profile.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/profile.sessions.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/setup.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/setup.step1.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/setup.step2.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/setup.step3.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/setup.step4.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/setup.step5.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/setup.step6.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.clangs.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.log.external.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.log.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.log.redaxo.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.log.slow-queries.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.phpinfo.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.report.html.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.report.markdown.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/pages/system.settings.php", "downloaded_repos/redaxo_redaxo/redaxo/src/core/schemas/config.json", "downloaded_repos/redaxo_redaxo/redaxo/src/core/schemas/package.json", "downloaded_repos/redaxo_redaxo/redaxo/src/core/update.php"], "skipped": [{"path": "downloaded_repos/redaxo_redaxo/.github/tests-visual/login--dark.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/redaxo_redaxo/.github/tests-visual/login.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/redaxo_redaxo/.github/tests-visual/logout--dark.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/redaxo_redaxo/.github/tests-visual/logout.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/redaxo_redaxo/.github/workflows/unit-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Class.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Interface.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.idea/fileTemplates/internal/PHP Trait.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCasePropertyNameRector.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Rule/UnderscoreToCamelCaseVariableNameRector.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseConflictingNameGuard.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCaseExpectedNameResolver.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/.tools/rector/Util/UnderscoreCamelCasePropertyRenamer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/Archive.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/ArchiveCorruptedException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/ArchiveIOException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/ArchiveIllegalCompressionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/FileInfo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/FileInfoException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/Tar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/backup/vendor/splitbrain/php-archive/src/Zip.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/plugins/customizer/assets/vendor/codemirror.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/fonts/bootstrap/glyphicons-halflings-regular.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/fonts/bootstrap/glyphicons-halflings-regular.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/fonts/bootstrap/glyphicons-halflings-regular.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/fonts/bootstrap/glyphicons-halflings-regular.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/fonts/bootstrap/glyphicons-halflings-regular.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/images/.keep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/affix.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/alert.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/button.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/carousel.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/collapse.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/dropdown.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/modal.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/popover.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/scrollspy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/tab.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/tooltip.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap/transition.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap-sprockets.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/javascripts/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/_bootstrap-compass.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/_bootstrap-mincer.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/_bootstrap-sprockets.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/_bootstrap.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_alerts.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_badges.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_breadcrumbs.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_button-groups.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_buttons.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_carousel.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_close.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_code.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_component-animations.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_dropdowns.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_forms.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_glyphicons.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_grid.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_input-groups.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_jumbotron.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_labels.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_list-group.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_media.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_mixins.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_modals.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_navbar.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_navs.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_normalize.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_pager.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_pagination.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_panels.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_popovers.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_print.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_progress-bars.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_responsive-embed.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_responsive-utilities.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_scaffolding.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_tables.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_theme.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_thumbnails.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_tooltip.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_type.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_utilities.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_variables.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/_wells.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_alerts.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_background-variant.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_border-radius.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_buttons.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_center-block.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_clearfix.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_forms.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_gradients.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_grid-framework.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_grid.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_hide-text.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_image.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_labels.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_list-group.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_nav-divider.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_nav-vertical-align.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_opacity.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_pagination.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_panels.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_progress-bar.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_reset-filter.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_reset-text.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_resize.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_responsive-visibility.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_size.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_tab-focus.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_table-row.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_text-emphasis.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_text-overflow.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/assets/stylesheets/bootstrap/mixins/_vendor-prefixes.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/css/bootstrap-select.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/css/bootstrap-select.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/css/bootstrap-select.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/bootstrap-select.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/bootstrap-select.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/bootstrap-select.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/bootstrap-select.min.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/i18n/defaults-de_DE.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/i18n/defaults-en_US.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/i18n/defaults-es_ES.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/i18n/defaults-it_IT.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/i18n/defaults-nl_NL.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/i18n/defaults-pt_BR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/dist/js/i18n/defaults-sv_SE.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/sass/bootstrap-select.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/bootstrap-select/sass/variables.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_animated.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_bordered-pulled.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_core.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_fixed-width.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_functions.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_icons.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_list.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_mixins.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_rotated-flipped.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_screen-reader.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_shims.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_sizing.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_stacked.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/_variables.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/brands.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/fontawesome.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/regular.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/solid.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/scss/v4-shims.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-brands-400.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-brands-400.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-regular-400.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-regular-400.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-solid-900.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-solid-900.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-v4compatibility.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/font-awesome/webfonts/fa-v4compatibility.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Base/Range.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/AtRootBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/CallableBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/ContentBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/DirectiveBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/EachBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/ElseBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/ElseifBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/ForBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/IfBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/MediaBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/NestedPropertyBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block/WhileBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Block.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Cache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Colors.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/CompilationResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Compiler/CachedResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Compiler/Environment.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Compiler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Exception/CompilerException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Exception/ParserException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Exception/RangeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Exception/SassException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Exception/SassScriptException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Exception/ServerException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter/Compact.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter/Compressed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter/Crunched.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter/Debug.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter/Expanded.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter/Nested.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter/OutputBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Formatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Logger/LoggerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Logger/QuietLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Logger/StreamLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Node/Number.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Node.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/OutputStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Parser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/SourceMap/Base64.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/SourceMap/Base64VLQ.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/SourceMap/SourceMapGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Type.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Util/Path.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Util.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/ValueConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Version.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/be_style/vendor/scssphp/scssphp/src/Warn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/cronjob/plugins/optimize_tables/lib/cronjob.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/.gitattributes", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Authentication/AuthenticatorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Authentication/NullAuthenticator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Authentication/SimpleAuthenticator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Clockwork.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/DataSource/DataSource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/DataSource/DataSourceInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/DataSource/PhpDataSource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/DataSource/XdebugDataSource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Helpers/Concerns/ResolvesViewName.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Helpers/Serializer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Helpers/ServerTiming.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Helpers/StackFilter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Helpers/StackFrame.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Helpers/StackTrace.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/IncomingRequest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/Log.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/LogLevel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/Request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/RequestType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/ShouldCollect.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/ShouldRecord.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/Timeline/Event.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/Timeline/Timeline.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/UserData.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Request/UserDataItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Storage/FileStorage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Storage/RedisStorage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Storage/Search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Storage/Storage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Storage/StorageInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Support/Vanilla/Clockwork.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Support/Vanilla/ClockworkMiddleware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Support/Vanilla/config.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Support/Vanilla/helpers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/Clockwork/Support/Vanilla/iframe.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/debug/vendor/itsgoingd/clockwork/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/tests/managed_media_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/media_manager/tests/media_manager_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/lib/media_category.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/package.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/tests/media_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/mediapool/tests/mediapool_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/category_handler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/clang_handler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/metainfo/lib/handler/media_handler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/COMMITMENT", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/VERSION", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-af.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-as.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-az.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ba.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-be.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-bg.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-bn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ca.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-cs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-da.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-de.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-el.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-eo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-es.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-et.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-fa.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-fi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-fo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-fr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-gl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-he.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-hi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-hr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-hu.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-hy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-id.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-it.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ja.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ka.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ko.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ku.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-lt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-lv.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-mg.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-mn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ms.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-nb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-nl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-pl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-pt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-pt_br.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ro.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ru.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-si.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-sk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-sl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-sr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-sr_latn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-sv.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-tl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-tr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-uk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-ur.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-vi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-zh.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/language/phpmailer.lang-zh_cn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/src/DSNConfigurator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/src/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/src/OAuth.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/src/OAuthTokenProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/src/PHPMailer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/src/POP3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/phpmailer/vendor/phpmailer/phpmailer/src/SMTP.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/lib/navigation.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/pages/linkmap.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/lib/system_setting_default_template_id.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/tests/article_content_base_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/tests/article_content_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/content/tests/article_slice_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/tests/article_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/tests/category_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/tests/function_url_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/tests/navigation_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery-pjax.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery-ui.custom.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/autoload.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/be/page.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/check.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/console/setup/run.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/error_handler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/addons/addon.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/manager.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/null.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/package.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/packages/plugins/plugin.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/response.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/sql.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/sql/table.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/lib/var_dumper.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/base/factory_trait_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/base/instance_list_pool_trait_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/base/instance_pool_trait_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/base/singleton_trait_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/be/navigation_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/clang/clang_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/config_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/console/command_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/console/config/get_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/console/config/set_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/context_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/extension_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/functions_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/list_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/login/backend_login_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/login/password_policy_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/packages/package_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/path_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/request_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/rex_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/sql/sql_select_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/sql/sql_table_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/sql/sql_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/sql/sql_util_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/dir_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/file_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/finder_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/formatter_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/i18n_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/log_entry_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/log_file_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/markdown_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/pager_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/socket_proxy_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/socket_response_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/socket_response_testfiles/response_chunked.testresp", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/socket_response_testfiles/response_chunked_gzip.testresp", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/socket_response_testfiles/response_decoded", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/socket_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/sortable_iterator_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/stream_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/string_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/timer_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/type_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/validator_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/util/version_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/var/var_config_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/var/var_property_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/var/var_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/var/var_test_base.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/tests/view_test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/autoload_files.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/ca-bundle/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/ca-bundle/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/ca-bundle/res/cacert.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/ca-bundle/src/CaBundle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/ElementReference/Resolver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/ElementReference/Subject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/ElementReference/Usage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/Exceptions/NestingException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/Helper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/Sanitizer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/data/AllowedAttributes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/data/AllowedTags.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/data/AttributeInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/data/TagInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/data/XPath.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/enshrined/svg-sanitize/src/svg-scanner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/erusev/parsedown/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/erusev/parsedown/Parsedown.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/erusev/parsedown/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/erusev/parsedown-extra/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/erusev/parsedown-extra/ParsedownExtra.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/erusev/parsedown-extra/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Exception/ErrorException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Exception/Formatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Exception/Frame.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Exception/FrameCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Exception/Inspector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Handler/CallbackHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Handler/Handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Handler/HandlerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Handler/JsonResponseHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Handler/PlainTextHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Handler/PrettyPageHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Handler/XmlResponseHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Inspector/InspectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Inspector/InspectorFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Inspector/InspectorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/css/prism.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/css/whoops.base.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/js/clipboard.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/js/prism.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/js/whoops.base.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/js/zepto.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/env_details.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/frame_code.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/frame_list.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/frames_container.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/frames_description.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/header.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/header_outer.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/layout.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/panel_details.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/panel_details_outer.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/panel_left.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Resources/views/panel_left_outer.html.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Run.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/RunInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Util/HtmlDumperOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Util/Misc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Util/SystemFacade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/filp/whoops/src/Whoops/Util/TemplateHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/AttestationObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/AuthenticatorData.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/AndroidKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/AndroidSafetyNet.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/Apple.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/FormatBase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/None.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/Packed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/Tpm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Attestation/Format/U2f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/Binary/ByteBuffer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/CBOR/CborDecoder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/WebAuthn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/lbuchs/webauthn/src/WebAuthnException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/container/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/container/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/container/src/ContainerExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/container/src/ContainerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/container/src/NotFoundExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/src/MessageInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/src/RequestInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/src/ResponseInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/src/ServerRequestInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/src/StreamInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/src/UploadedFileInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/http-message/src/UriInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/AbstractLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/LogLevel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/LoggerAwareInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/LoggerAwareTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/LoggerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/LoggerTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/psr/log/src/NullLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/AbstractArray.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/AbstractCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/AbstractSet.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/ArrayInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Collection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/CollectionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/DoubleEndedQueue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/DoubleEndedQueueInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Exception/CollectionMismatchException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Exception/InvalidSortOrderException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Exception/NoSuchElementException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Exception/OutOfBoundsException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Exception/UnsupportedOperationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Exception/ValueExtractionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/GenericArray.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Map/AbstractMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Map/AbstractTypedMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Map/AssociativeArrayMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Map/MapInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Map/NamedParameterMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Map/TypedMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Map/TypedMapInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Queue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/QueueInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Set.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Tool/TypeTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Tool/ValueExtractorTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/collection/src/Tool/ValueToStringTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Exception/HttpRangeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Exception/InvalidRangeSetException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Exception/InvalidRangeUnitException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Exception/NoRangeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Exception/NotSatisfiableException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Exception/ParseException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Range.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/AbstractUnit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/AbstractUnitRange.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/BytesRange.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/BytesRangesCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/BytesUnit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/GenericRange.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/GenericUnit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/UnitInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/UnitRangeInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/Unit/UnitRangesCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/UnitFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/ramsey/http-range/src/UnitFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Application.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Attribute/AsCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/CI/GithubActionReporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Color.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/Command.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/CompleteCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/DumpCompletionCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/HelpCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/LazyCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/ListCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/LockableTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/SignalableCommandInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Command/TraceableCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/CommandLoader/CommandLoaderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/CommandLoader/ContainerCommandLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/CommandLoader/FactoryCommandLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Completion/CompletionInput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Completion/CompletionSuggestions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Completion/Output/BashCompletionOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Completion/Output/CompletionOutputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Completion/Output/FishCompletionOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Completion/Output/ZshCompletionOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Completion/Suggestion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/ConsoleEvents.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Cursor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/DataCollector/CommandDataCollector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Debug/CliRequest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/DependencyInjection/AddConsoleCommandPass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/ApplicationDescription.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/Descriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/DescriptorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/JsonDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/MarkdownDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/ReStructuredTextDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/TextDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Descriptor/XmlDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Event/ConsoleCommandEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Event/ConsoleErrorEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Event/ConsoleEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Event/ConsoleSignalEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Event/ConsoleTerminateEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/EventListener/ErrorListener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/CommandNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/InvalidOptionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/LogicException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/MissingInputException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/NamespaceNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/RunCommandFailedException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Exception/RuntimeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/NullOutputFormatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/NullOutputFormatterStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/OutputFormatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/OutputFormatterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/OutputFormatterStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/OutputFormatterStyleInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/OutputFormatterStyleStack.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Formatter/WrappableOutputFormatterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/DebugFormatterHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/DescriptorHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/Dumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/FormatterHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/Helper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/HelperInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/HelperSet.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/InputAwareHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/OutputWrapper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/ProcessHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/ProgressBar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/ProgressIndicator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/QuestionHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/SymfonyQuestionHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/Table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/TableCell.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/TableCellStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/TableRows.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/TableSeparator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Helper/TableStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/ArgvInput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/ArrayInput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/Input.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/InputArgument.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/InputAwareInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/InputDefinition.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/InputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/InputOption.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/StreamableInputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Input/StringInput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Logger/ConsoleLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Messenger/RunCommandContext.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Messenger/RunCommandMessage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Messenger/RunCommandMessageHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/AnsiColorMode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/BufferedOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/ConsoleOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/ConsoleOutputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/ConsoleSectionOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/NullOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/Output.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/OutputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/StreamOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Output/TrimmedBufferOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Question/ChoiceQuestion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Question/ConfirmationQuestion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Question/Question.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Resources/bin/hiddeninput.exe", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Resources/completion.bash", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Resources/completion.fish", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Resources/completion.zsh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/SignalRegistry/SignalMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/SignalRegistry/SignalRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/SingleCommandApplication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Style/OutputStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Style/StyleInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Style/SymfonyStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Terminal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Tester/ApplicationTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Tester/CommandCompletionTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Tester/CommandTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Tester/Constraint/CommandIsSuccessful.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/Tester/TesterTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/console/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/deprecation-contracts/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/deprecation-contracts/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/deprecation-contracts/function.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/AcceptHeader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/AcceptHeaderItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/BinaryFileResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/ChainRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Cookie.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Exception/BadRequestException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Exception/ConflictingHeadersException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Exception/JsonException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Exception/RequestExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Exception/SessionNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Exception/SuspiciousOperationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Exception/UnexpectedValueException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/ExpressionRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/AccessDeniedException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/CannotWriteFileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/ExtensionFileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/FileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/FileNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/FormSizeFileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/IniSizeFileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/NoFileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/NoTmpDirFileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/PartialFileException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/UnexpectedTypeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Exception/UploadException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/File.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/Stream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/File/UploadedFile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/FileBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/HeaderBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/HeaderUtils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/InputBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/IpUtils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/JsonResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/ParameterBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RateLimiter/AbstractRequestRateLimiter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RateLimiter/PeekableRequestRateLimiterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RateLimiter/RequestRateLimiterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RedirectResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/AttributesRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/ExpressionRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/HostRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/IpsRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/IsJsonRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/MethodRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/PathRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/PortRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher/SchemeRequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestMatcherInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/RequestStack.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Response.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/ResponseHeaderBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/ServerBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Attribute/AttributeBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Attribute/AttributeBagInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Flash/AutoExpireFlashBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Flash/FlashBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Flash/FlashBagInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/FlashBagAwareSessionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Session.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/SessionBagInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/SessionBagProxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/SessionFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/SessionFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/SessionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/SessionUtils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/AbstractSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/IdentityMarshaller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/MarshallingSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/MemcachedSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/MigratingSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/MongoDbSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/NativeFileSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/NullSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/PdoSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/RedisSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/SessionHandlerFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Handler/StrictSessionHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/MetadataBag.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/MockArraySessionStorage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/MockFileSessionStorage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/MockFileSessionStorageFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/NativeSessionStorage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/NativeSessionStorageFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/PhpBridgeSessionStorage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/PhpBridgeSessionStorageFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Proxy/AbstractProxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/Proxy/SessionHandlerProxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/SessionStorageFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/Session/Storage/SessionStorageInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/StreamedJsonResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/StreamedResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/UriSigner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/UrlHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/http-foundation/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Php83.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateInvalidOperationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateInvalidTimeZoneException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateMalformedIntervalStringException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateMalformedPeriodStringException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateMalformedStringException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateObjectError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/DateRangeError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/Override.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/Resources/stubs/SQLite3Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/bootstrap81.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/polyfill-php83/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/Attribute/Required.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/Attribute/SubscribedService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/ResetInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/ServiceCollectionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/ServiceLocatorTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/ServiceMethodsSubscriberTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/ServiceProviderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/ServiceSubscriberInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/ServiceSubscriberTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/service-contracts/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/AbstractString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/AbstractUnicodeString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/ByteString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/CodePointString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Exception/RuntimeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Inflector/EnglishInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Inflector/FrenchInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Inflector/InflectorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/LazyString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Resources/data/wcswidth_table_wide.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Resources/data/wcswidth_table_zero.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Resources/functions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Slugger/AsciiSlugger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/Slugger/SluggerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/UnicodeString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/string/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/AmqpCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ArgsStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/Caster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ClassStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ConstStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/CutArrayStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/CutStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/DOMCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/DateCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/DoctrineCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/DsCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/DsPairStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/EnumStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ExceptionCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/FFICaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/FiberCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/FrameStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/GmpCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ImagineCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ImgStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/IntlCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/LinkStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/MemcachedCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/MysqliCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/PdoCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/PgSqlCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ProxyManagerCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/RdKafkaCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/RedisCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ReflectionCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ResourceCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/ScalarStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/SplCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/StubCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/SymfonyCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/TraceStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/UninitializedStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/UuidCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/XmlReaderCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Caster/XmlResourceCaster.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/AbstractCloner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/ClonerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/Cursor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/Data.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/DumperInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/Internal/NoDefault.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/Stub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Cloner/VarCloner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Command/Descriptor/CliDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Command/Descriptor/DumpDescriptorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Command/Descriptor/HtmlDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Command/ServerDumpCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/AbstractDumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/CliDumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/ContextProvider/CliContextProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/ContextProvider/ContextProviderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/ContextProvider/RequestContextProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/ContextProvider/SourceContextProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/ContextualizedDumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/DataDumperInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/HtmlDumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Dumper/ServerDumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Exception/ThrowingCasterException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Resources/css/htmlDescriptor.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Resources/functions/dump.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Resources/js/htmlDescriptor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Server/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/Server/DumpServer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/VarDumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/var-dumper/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Command/LintCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Dumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Escaper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Exception/DumpException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Exception/ParseException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Exception/RuntimeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Inline.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Parser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Tag/TaggedValue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Unescaper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/Yaml.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/symfony/yaml/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/anti-xss/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/anti-xss/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/anti-xss/src/voku/helper/AntiXSS.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/anti-xss/src/voku/helper/data/entities_fallback.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/ASCII.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/ascii_by_languages.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/ascii_extras_by_languages.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/ascii_language_max_key.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/ascii_ord.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x000.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x001.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x002.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x003.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x004.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x005.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x006.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x007.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x009.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x00a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x00b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x00c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x00d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x00e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x00f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x010.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x011.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x012.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x013.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x014.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x015.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x016.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x017.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x018.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x01d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x01e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x01f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x020.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x021.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x022.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x023.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x024.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x025.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x026.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x027.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x028.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x029.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x02a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x02c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x02e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x02f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x030.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x031.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x032.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x033.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x04d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x04e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x04f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x050.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x051.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x052.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x053.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x054.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x055.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x056.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x057.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x058.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x059.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x05a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x05b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x05c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x05d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x05e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x05f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x060.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x061.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x062.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x063.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x064.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x065.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x066.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x067.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x068.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x069.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x06a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x06b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x06c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x06d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x06e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x06f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x070.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x071.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x072.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x073.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x074.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x075.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x076.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x077.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x078.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x079.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x07a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x07b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x07c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x07d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x07e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x07f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x080.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x081.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x082.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x083.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x084.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x085.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x086.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x087.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x088.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x089.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x08a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x08b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x08c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x08d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x08e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x08f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x090.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x091.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x092.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x093.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x094.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x095.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x096.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x097.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x098.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x099.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x09a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x09b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x09c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x09d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x09e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x09f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0a0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0a1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0a2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0a3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0a4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0ac.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0ad.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0ae.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0af.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b8.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0b9.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0ba.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0bb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0bc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0bd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0be.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0bf.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c8.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0c9.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0ca.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0cb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0cc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0cd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0ce.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0cf.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0d7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0f9.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0fa.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0fb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0fc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0fd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0fe.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x0ff.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x1d4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x1d5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x1d6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x1d7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-ascii/src/voku/helper/data/x1f1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/LICENSE-APACHE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/LICENSE-GPL", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/Bootup.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/UTF8.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/caseFolding_full.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/chr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/emoji.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/encodings.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/ord.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/transliterator_list.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/utf8_fix.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/redaxo_redaxo/redaxo/src/core/vendor/voku/portable-utf8/src/voku/helper/data/win1252_to_utf8.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6731212139129639, "profiling_times": {"config_time": 5.8951804637908936, "core_time": 29.653573989868164, "ignores_time": 0.001764535903930664, "total_time": 35.551493883132935}, "parsing_time": {"total_time": 15.382085800170898, "per_file_time": {"mean": 0.025808868792233056, "std_dev": 0.0138001721702898}, "very_slow_stats": {"time_ratio": 0.21462526301536225, "count_ratio": 0.003355704697986577}, "very_slow_files": [{"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "ftime": 0.6415631771087646}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "ftime": 2.659821033477783}]}, "scanning_time": {"total_time": 65.57547450065613, "per_file_time": {"mean": 0.02511508023770821, "std_dev": 0.23310231938908316}, "very_slow_stats": {"time_ratio": 0.5031537832337233, "count_ratio": 0.0015319800842589046}, "very_slow_files": [{"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/session-timeout.js", "ftime": 1.6960580348968506}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/standard.js", "ftime": 2.61360502243042}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "ftime": 4.85706901550293}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "ftime": 23.827816009521484}]}, "matching_time": {"total_time": 22.996986150741577, "per_file_and_rule_time": {"mean": 0.011278561133271985, "std_dev": 0.005741616002733914}, "very_slow_stats": {"time_ratio": 0.631671563026472, "count_ratio": 0.0196174595389897}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.44413208961486816}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.44529008865356445}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.49340391159057617}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.556380033493042}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 0.5603370666503906}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.681520938873291}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.0231900215148926}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.2543458938598633}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.4960110187530518}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/addons/structure/plugins/history/assets/noUiSlider/nouislider.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.9712209701538086}]}, "tainting_time": {"total_time": 14.469865560531616, "per_def_and_rule_time": {"mean": 0.0025696795525717675, "std_dev": 0.000884222453592357}, "very_slow_stats": {"time_ratio": 0.5910926024222861, "count_ratio": 0.006215592257147931}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.180466890335083}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.18125081062316895}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.21548104286193848}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.21993112564086914}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.3558969497680664}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.4726688861846924}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.6404831409454346}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.7154719829559326}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.7812049388885498}, {"fpath": "downloaded_repos/redaxo_redaxo/redaxo/src/core/assets/jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.5540330410003662}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1119595008}, "engine_requested": "OSS", "skipped_rules": []}