{"version": "1.130.0", "results": [{"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/xpertbot_craft-wheelform/src/controllers/MessageController.php", "start": {"line": 363, "col": 16, "offset": 12864}, "end": {"line": 363, "col": 37, "offset": 12885}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js:\n ", "path": "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js:\n ", "path": "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js:\n ", "path": "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js"}], "paths": {"scanned": ["downloaded_repos/xpertbot_craft-wheelform/.github/stale.yml", "downloaded_repos/xpertbot_craft-wheelform/.gitignore", "downloaded_repos/xpertbot_craft-wheelform/.nvmrc", "downloaded_repos/xpertbot_craft-wheelform/CHANGELOG.md", "downloaded_repos/xpertbot_craft-wheelform/LICENSE.md", "downloaded_repos/xpertbot_craft-wheelform/README.md", "downloaded_repos/xpertbot_craft-wheelform/composer.json", "downloaded_repos/xpertbot_craft-wheelform/package-lock.json", "downloaded_repos/xpertbot_craft-wheelform/package.json", "downloaded_repos/xpertbot_craft-wheelform/phpstan.neon", "downloaded_repos/xpertbot_craft-wheelform/postcss.config.js", "downloaded_repos/xpertbot_craft-wheelform/resources/img/form-entries.jpg", "downloaded_repos/xpertbot_craft-wheelform/resources/img/plugin-logo.png", "downloaded_repos/xpertbot_craft-wheelform/resources/js/classes/EntriesService.js", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/FormConfig/Container.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/FormConfig/Lightswitch.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/FormConfig/Settings.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/FormConfig/partials/FieldOptions.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/FormConfig/types/Field.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/FormConfig/types/Html.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/Partials/Collapsable.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/components/Partials/Editor.vue", "downloaded_repos/xpertbot_craft-wheelform/resources/js/entry.js", "downloaded_repos/xpertbot_craft-wheelform/resources/js/filters/filters.js", "downloaded_repos/xpertbot_craft-wheelform/resources/sass/cp-wheelform.scss", "downloaded_repos/xpertbot_craft-wheelform/src/Mailer.php", "downloaded_repos/xpertbot_craft-wheelform/src/Plugin.php", "downloaded_repos/xpertbot_craft-wheelform/src/assets/ListFieldAsset.php", "downloaded_repos/xpertbot_craft-wheelform/src/assets/ToolsAsset.php", "downloaded_repos/xpertbot_craft-wheelform/src/assets/WheelformCpAsset.php", "downloaded_repos/xpertbot_craft-wheelform/src/assets/css/codemirror.css", "downloaded_repos/xpertbot_craft-wheelform/src/assets/css/cp-wheelform.css", "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/list-field.js", "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/tools.js", "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js", "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js.LICENSE.txt", "downloaded_repos/xpertbot_craft-wheelform/src/behaviors/FormFieldBehavior.php", "downloaded_repos/xpertbot_craft-wheelform/src/behaviors/JsonFieldBehavior.php", "downloaded_repos/xpertbot_craft-wheelform/src/console/controllers/MessageController.php", "downloaded_repos/xpertbot_craft-wheelform/src/controllers/BaseController.php", "downloaded_repos/xpertbot_craft-wheelform/src/controllers/EntriesController.php", "downloaded_repos/xpertbot_craft-wheelform/src/controllers/FormController.php", "downloaded_repos/xpertbot_craft-wheelform/src/controllers/MessageController.php", "downloaded_repos/xpertbot_craft-wheelform/src/db/BaseActiveRecord.php", "downloaded_repos/xpertbot_craft-wheelform/src/db/Form.php", "downloaded_repos/xpertbot_craft-wheelform/src/db/FormField.php", "downloaded_repos/xpertbot_craft-wheelform/src/db/Message.php", "downloaded_repos/xpertbot_craft-wheelform/src/db/MessageValue.php", "downloaded_repos/xpertbot_craft-wheelform/src/events/MessageEvent.php", "downloaded_repos/xpertbot_craft-wheelform/src/events/RegisterFieldsEvent.php", "downloaded_repos/xpertbot_craft-wheelform/src/events/ResponseEvent.php", "downloaded_repos/xpertbot_craft-wheelform/src/events/SendEvent.php", "downloaded_repos/xpertbot_craft-wheelform/src/extensions/WheelformVariable.php", "downloaded_repos/xpertbot_craft-wheelform/src/fields/FormField.php", "downloaded_repos/xpertbot_craft-wheelform/src/helpers/ExportHelper.php", "downloaded_repos/xpertbot_craft-wheelform/src/helpers/TagHelper.php", "downloaded_repos/xpertbot_craft-wheelform/src/icon-mask.svg", "downloaded_repos/xpertbot_craft-wheelform/src/icon.svg", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/Install.php", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/m180407_040301_add_index_view_column_to_form_fields_table.php", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/m180407_170219_add_active_column_to_form_fields.php", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/m180430_232743_add_read_column_to_message.php", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/m180602_051517_AddOrderToField.php", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/m180802_015031_save_entry_to_forms_table.php", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/m180804_230709_add_options_to_form_fields.php", "downloaded_repos/xpertbot_craft-wheelform/src/migrations/m180814_230614_add_options_column_forms_table.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/Settings.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/BaseFieldType.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Checkbox.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Consent.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Date.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Email.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/File.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Hidden.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/HtmlField.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/ListField.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Number.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Radio.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Select.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Telephone.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Text.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Textarea.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/fields/Time.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/helpers/JsonField.php", "downloaded_repos/xpertbot_craft-wheelform/src/models/tools/ImportFile.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/BaseService.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/FieldService.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/FormService.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/MessageService.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/MetaTagsService.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/RecaptchaV3Service.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/WheelformService.php", "downloaded_repos/xpertbot_craft-wheelform/src/services/permissions/WheelformPermissions.php", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_edit-form.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_emails/general.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_emails/notification.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_entries.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_entry.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_includes/_form_field.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_index.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/_settings.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/embeds/_edit-form-field.twig", "downloaded_repos/xpertbot_craft-wheelform/src/templates/utilities/tools.twig", "downloaded_repos/xpertbot_craft-wheelform/src/translations/bg/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/translations/de/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/translations/es/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/translations/fr/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/translations/it/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/translations/nb/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/translations/nl/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/translations/pt/wheelform.php", "downloaded_repos/xpertbot_craft-wheelform/src/utilities/Tools.php", "downloaded_repos/xpertbot_craft-wheelform/src/validators/JsonValidator.php", "downloaded_repos/xpertbot_craft-wheelform/src/widgets/LinkPager.php", "downloaded_repos/xpertbot_craft-wheelform/tsconfig.json", "downloaded_repos/xpertbot_craft-wheelform/webpack.config.js"], "skipped": [{"path": "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.941338062286377, "profiling_times": {"config_time": 6.4864420890808105, "core_time": 8.2722806930542, "ignores_time": 0.11797976493835449, "total_time": 14.878191471099854}, "parsing_time": {"total_time": 0.8683416843414307, "per_file_time": {"mean": 0.010461948004113623, "std_dev": 0.0010141624132176715}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 8.073384523391724, "per_file_time": {"mean": 0.025548685200606738, "std_dev": 0.09015883028738599}, "very_slow_stats": {"time_ratio": 0.6586939979645168, "count_ratio": 0.0031645569620253164}, "very_slow_files": [{"fpath": "downloaded_repos/xpertbot_craft-wheelform/src/assets/js/wheelform-bundle.js", "ftime": 5.317889928817749}]}, "matching_time": {"total_time": 0.4932882785797119, "per_file_and_rule_time": {"mean": 0.003245317622234947, "std_dev": 8.327284680364608e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.1221766471862793, "per_def_and_rule_time": {"mean": 0.001110696792602539, "std_dev": 6.826250748252444e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}