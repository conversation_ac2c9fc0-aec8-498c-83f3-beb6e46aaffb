{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 51, "col": 48, "offset": 1515}, "end": {"line": 51, "col": 100, "offset": 1567}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml:51:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ fromJSON(steps.app-version.outputs.result).version` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 51, "col": 48, "offset": 1515}, "end": {"line": 51, "col": 100, "offset": 1567}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 163, "col": 10, "offset": 5725}, "end": {"line": 163, "col": 13, "offset": 5728}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 166, "col": 1, "offset": 5725}, "end": {"line": 166, "col": 43, "offset": 5767}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 167, "col": 82, "offset": 5725}, "end": {"line": 167, "col": 96, "offset": 5739}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 167, "col": 102, "offset": 5725}, "end": {"line": 167, "col": 116, "offset": 5739}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 54, "offset": 5725}, "end": {"line": 169, "col": 57, "offset": 5728}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 95, "offset": 5725}, "end": {"line": 169, "col": 98, "offset": 5728}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 129, "offset": 5725}, "end": {"line": 169, "col": 132, "offset": 5728}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 165, "offset": 5725}, "end": {"line": 169, "col": 168, "offset": 5728}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 171, "col": 4, "offset": 5725}, "end": {"line": 171, "col": 7, "offset": 5728}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 172, "col": 11, "offset": 5725}, "end": {"line": 172, "col": 14, "offset": 5728}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 172, "col": 38, "offset": 5725}, "end": {"line": 172, "col": 41, "offset": 5728}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml:163:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 163, "col": 10, "offset": 5725}, "end": {"line": 163, "col": 13, "offset": 5728}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 166, "col": 1, "offset": 5725}, "end": {"line": 166, "col": 43, "offset": 5767}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 167, "col": 82, "offset": 5725}, "end": {"line": 167, "col": 96, "offset": 5739}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 167, "col": 102, "offset": 5725}, "end": {"line": 167, "col": 116, "offset": 5739}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 54, "offset": 5725}, "end": {"line": 169, "col": 57, "offset": 5728}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 95, "offset": 5725}, "end": {"line": 169, "col": 98, "offset": 5728}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 129, "offset": 5725}, "end": {"line": 169, "col": 132, "offset": 5728}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 169, "col": 165, "offset": 5725}, "end": {"line": 169, "col": 168, "offset": 5728}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 171, "col": 4, "offset": 5725}, "end": {"line": 171, "col": 7, "offset": 5728}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 172, "col": 11, "offset": 5725}, "end": {"line": 172, "col": 14, "offset": 5728}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "start": {"line": 172, "col": 38, "offset": 5725}, "end": {"line": 172, "col": 41, "offset": 5728}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 124, "col": 61, "offset": 3904}, "end": {"line": 124, "col": 64, "offset": 3907}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml:124:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 124, "col": 61, "offset": 3904}, "end": {"line": 124, "col": 64, "offset": 3907}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 135, "col": 11, "offset": 4195}, "end": {"line": 135, "col": 29, "offset": 4213}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 139, "col": 48, "offset": 4195}, "end": {"line": 139, "col": 51, "offset": 4198}}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 140, "col": 26, "offset": 4195}, "end": {"line": 140, "col": 29, "offset": 4198}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml:135:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.databases` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 135, "col": 11, "offset": 4195}, "end": {"line": 135, "col": 29, "offset": 4213}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 139, "col": 48, "offset": 4195}, "end": {"line": 139, "col": 51, "offset": 4198}}, {"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "start": {"line": 140, "col": 26, "offset": 4195}, "end": {"line": 140, "col": 29, "offset": 4198}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-mysql.yml", "start": {"line": 142, "col": 26, "offset": 4649}, "end": {"line": 142, "col": 29, "offset": 4652}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-mysql.yml:142:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-mysql.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-mysql.yml", "start": {"line": 142, "col": 26, "offset": 4649}, "end": {"line": 142, "col": 29, "offset": 4652}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-oci.yml", "start": {"line": 150, "col": 26, "offset": 4631}, "end": {"line": 150, "col": 29, "offset": 4634}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-oci.yml:150:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-oci.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-oci.yml", "start": {"line": 150, "col": 26, "offset": 4631}, "end": {"line": 150, "col": 29, "offset": 4634}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-pgsql.yml", "start": {"line": 140, "col": 26, "offset": 4482}, "end": {"line": 140, "col": 29, "offset": 4485}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-pgsql.yml:140:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-pgsql.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-pgsql.yml", "start": {"line": 140, "col": 26, "offset": 4482}, "end": {"line": 140, "col": 29, "offset": 4485}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-sqlite.yml", "start": {"line": 129, "col": 26, "offset": 4111}, "end": {"line": 129, "col": 29, "offset": 4114}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-sqlite.yml:129:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-sqlite.yml", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-sqlite.yml", "start": {"line": 129, "col": 26, "offset": 4111}, "end": {"line": 129, "col": 29, "offset": 4114}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigCreate.php", "start": {"line": 18, "col": 11, "offset": 0}, "end": {"line": 18, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Command/ConfigCreate.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigCreate.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigCreate.php", "start": {"line": 18, "col": 11, "offset": 0}, "end": {"line": 18, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigDelete.php", "start": {"line": 20, "col": 11, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Command/ConfigDelete.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigDelete.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigDelete.php", "start": {"line": 20, "col": 11, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigGet.php", "start": {"line": 19, "col": 11, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Command/ConfigGet.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigGet.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigGet.php", "start": {"line": 19, "col": 11, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigSet.php", "start": {"line": 21, "col": 11, "offset": 0}, "end": {"line": 21, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Command/ConfigSet.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigSet.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigSet.php", "start": {"line": 21, "col": 11, "offset": 0}, "end": {"line": 21, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "start": {"line": 24, "col": 20, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 7}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "start": {"line": 25, "col": 20, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 5}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "start": {"line": 26, "col": 11, "offset": 0}, "end": {"line": 26, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php:24:\n `IConfig` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "start": {"line": 24, "col": 20, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 7}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "start": {"line": 25, "col": 20, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 5}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "start": {"line": 26, "col": 11, "offset": 0}, "end": {"line": 26, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php", "start": {"line": 22, "col": 20, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 7}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php", "start": {"line": 24, "col": 20, "offset": 0}, "end": {"line": 24, "col": 28, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php:22:\n `IConfig` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php", "start": {"line": 22, "col": 20, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 7}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php", "start": {"line": 24, "col": 20, "offset": 0}, "end": {"line": 24, "col": 28, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 28, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 24, "col": 20, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 7}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 26, "col": 11, "offset": 0}, "end": {"line": 26, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 32, "col": 53, "offset": 0}, "end": {"line": 32, "col": 56, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php:23:\n `ISession` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 28, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 24, "col": 20, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 7}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 26, "col": 11, "offset": 0}, "end": {"line": 26, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "start": {"line": 32, "col": 53, "offset": 0}, "end": {"line": 32, "col": 56, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 33, "col": 11, "offset": 0}, "end": {"line": 33, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 34, "col": 11, "offset": 0}, "end": {"line": 34, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 35, "col": 20, "offset": 0}, "end": {"line": 35, "col": 27, "offset": 7}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 36, "col": 11, "offset": 0}, "end": {"line": 36, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 37, "col": 20, "offset": 0}, "end": {"line": 37, "col": 28, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 38, "col": 11, "offset": 0}, "end": {"line": 38, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/GroupManager.php:33:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 33, "col": 11, "offset": 0}, "end": {"line": 33, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 34, "col": 11, "offset": 0}, "end": {"line": 34, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 35, "col": 20, "offset": 0}, "end": {"line": 35, "col": 27, "offset": 7}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 36, "col": 11, "offset": 0}, "end": {"line": 36, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 37, "col": 20, "offset": 0}, "end": {"line": 37, "col": 28, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "start": {"line": 38, "col": 11, "offset": 0}, "end": {"line": 38, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 28, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "start": {"line": 24, "col": 11, "offset": 0}, "end": {"line": 24, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "start": {"line": 25, "col": 20, "offset": 0}, "end": {"line": 25, "col": 27, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php:23:\n `ISession` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 28, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "start": {"line": 24, "col": 11, "offset": 0}, "end": {"line": 24, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "start": {"line": 25, "col": 20, "offset": 0}, "end": {"line": 25, "col": 27, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "start": {"line": 25, "col": 11, "offset": 0}, "end": {"line": 25, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "start": {"line": 26, "col": 11, "offset": 0}, "end": {"line": 26, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "start": {"line": 27, "col": 11, "offset": 0}, "end": {"line": 27, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "start": {"line": 25, "col": 11, "offset": 0}, "end": {"line": 25, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "start": {"line": 26, "col": 11, "offset": 0}, "end": {"line": 26, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "start": {"line": 27, "col": 11, "offset": 0}, "end": {"line": 27, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php", "start": {"line": 22, "col": 11, "offset": 0}, "end": {"line": 22, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php", "start": {"line": 22, "col": 11, "offset": 0}, "end": {"line": 22, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php", "start": {"line": 19, "col": 11, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php", "start": {"line": 20, "col": 11, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php", "start": {"line": 19, "col": 11, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php", "start": {"line": 20, "col": 11, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 78, "col": 11, "offset": 0}, "end": {"line": 78, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 79, "col": 20, "offset": 0}, "end": {"line": 79, "col": 27, "offset": 7}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 80, "col": 20, "offset": 0}, "end": {"line": 80, "col": 28, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 81, "col": 11, "offset": 0}, "end": {"line": 81, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php:78:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 78, "col": 11, "offset": 0}, "end": {"line": 78, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 79, "col": 20, "offset": 0}, "end": {"line": 79, "col": 27, "offset": 7}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 80, "col": 20, "offset": 0}, "end": {"line": 80, "col": 28, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "start": {"line": 81, "col": 11, "offset": 0}, "end": {"line": 81, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 21, "col": 20, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 5}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 22, "col": 20, "offset": 0}, "end": {"line": 22, "col": 28, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 7}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 24, "col": 11, "offset": 0}, "end": {"line": 24, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php:21:\n `IL10N` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 21, "col": 20, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 5}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 22, "col": 20, "offset": 0}, "end": {"line": 22, "col": 28, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 23, "col": 20, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 7}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "start": {"line": 24, "col": 11, "offset": 0}, "end": {"line": 24, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php", "start": {"line": 17, "col": 20, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 5}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php", "start": {"line": 18, "col": 11, "offset": 0}, "end": {"line": 18, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php:17:\n `IL10N` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php", "start": {"line": 17, "col": 20, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 5}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php", "start": {"line": 18, "col": 11, "offset": 0}, "end": {"line": 18, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 37, "col": 20, "offset": 0}, "end": {"line": 37, "col": 27, "offset": 7}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 38, "col": 11, "offset": 0}, "end": {"line": 38, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 39, "col": 20, "offset": 0}, "end": {"line": 39, "col": 28, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 40, "col": 11, "offset": 0}, "end": {"line": 40, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 41, "col": 11, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 42, "col": 11, "offset": 0}, "end": {"line": 42, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 43, "col": 11, "offset": 0}, "end": {"line": 43, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 44, "col": 11, "offset": 0}, "end": {"line": 44, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 45, "col": 20, "offset": 0}, "end": {"line": 45, "col": 28, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 46, "col": 11, "offset": 0}, "end": {"line": 46, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/UserBackend.php:37:\n `IConfig` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 37, "col": 20, "offset": 0}, "end": {"line": 37, "col": 27, "offset": 7}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 38, "col": 11, "offset": 0}, "end": {"line": 38, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 39, "col": 20, "offset": 0}, "end": {"line": 39, "col": 28, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 40, "col": 11, "offset": 0}, "end": {"line": 40, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 41, "col": 11, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 42, "col": 11, "offset": 0}, "end": {"line": 42, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 43, "col": 11, "offset": 0}, "end": {"line": 43, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 44, "col": 11, "offset": 0}, "end": {"line": 44, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 45, "col": 20, "offset": 0}, "end": {"line": 45, "col": 28, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "start": {"line": 46, "col": 11, "offset": 0}, "end": {"line": 46, "col": 19, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_user_saml/lib/UserData.php", "start": {"line": 19, "col": 11, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 8}}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserData.php", "start": {"line": 20, "col": 11, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/nextcloud_user_saml/lib/UserData.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/nextcloud_user_saml/lib/UserData.php", "spans": [{"file": "downloaded_repos/nextcloud_user_saml/lib/UserData.php", "start": {"line": 19, "col": 11, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 8}}, {"file": "downloaded_repos/nextcloud_user_saml/lib/UserData.php", "start": {"line": 20, "col": 11, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 8}}]}], "paths": {"scanned": ["downloaded_repos/nextcloud_user_saml/.git-blame-ignore-revs", "downloaded_repos/nextcloud_user_saml/.github/CODEOWNERS", "downloaded_repos/nextcloud_user_saml/.github/contributing.md", "downloaded_repos/nextcloud_user_saml/.github/dependabot.yml", "downloaded_repos/nextcloud_user_saml/.github/issue_template.md", "downloaded_repos/nextcloud_user_saml/.github/issue_template.md.license", "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/lint-info-xml.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/lint-php-cs.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/lint-php.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-mysql.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-oci.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-pgsql.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-sqlite.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-summary-when-unrelated.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/pr-feedback.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/psalm-matrix.yml", "downloaded_repos/nextcloud_user_saml/.github/workflows/reuse.yml", "downloaded_repos/nextcloud_user_saml/.gitignore", "downloaded_repos/nextcloud_user_saml/.patches/php-saml-600.patch", "downloaded_repos/nextcloud_user_saml/.php-cs-fixer.dist.php", "downloaded_repos/nextcloud_user_saml/.scrutinizer.yml", "downloaded_repos/nextcloud_user_saml/.tx/config", "downloaded_repos/nextcloud_user_saml/AUTHORS.md", "downloaded_repos/nextcloud_user_saml/CHANGELOG.md", "downloaded_repos/nextcloud_user_saml/LICENSE", "downloaded_repos/nextcloud_user_saml/LICENSES/AGPL-3.0-or-later.txt", "downloaded_repos/nextcloud_user_saml/LICENSES/CC0-1.0.txt", "downloaded_repos/nextcloud_user_saml/LICENSES/MIT.txt", "downloaded_repos/nextcloud_user_saml/Makefile", "downloaded_repos/nextcloud_user_saml/README.md", "downloaded_repos/nextcloud_user_saml/REUSE.toml", "downloaded_repos/nextcloud_user_saml/appinfo/info.xml", "downloaded_repos/nextcloud_user_saml/appinfo/routes.php", "downloaded_repos/nextcloud_user_saml/composer.json", "downloaded_repos/nextcloud_user_saml/composer.lock", "downloaded_repos/nextcloud_user_saml/css/admin.css", "downloaded_repos/nextcloud_user_saml/css/personal.css", "downloaded_repos/nextcloud_user_saml/css/selectUserBackEnd.css", "downloaded_repos/nextcloud_user_saml/img/app-dark.svg", "downloaded_repos/nextcloud_user_saml/img/app.svg", "downloaded_repos/nextcloud_user_saml/js/admin.js", "downloaded_repos/nextcloud_user_saml/js/selectUserBackEnd.js", "downloaded_repos/nextcloud_user_saml/js/timezone.js", "downloaded_repos/nextcloud_user_saml/l10n/.gitkeep", "downloaded_repos/nextcloud_user_saml/l10n/af.js", "downloaded_repos/nextcloud_user_saml/l10n/af.json", "downloaded_repos/nextcloud_user_saml/l10n/ar.js", "downloaded_repos/nextcloud_user_saml/l10n/ar.json", "downloaded_repos/nextcloud_user_saml/l10n/ast.js", "downloaded_repos/nextcloud_user_saml/l10n/ast.json", "downloaded_repos/nextcloud_user_saml/l10n/bg.js", "downloaded_repos/nextcloud_user_saml/l10n/bg.json", "downloaded_repos/nextcloud_user_saml/l10n/ca.js", "downloaded_repos/nextcloud_user_saml/l10n/ca.json", "downloaded_repos/nextcloud_user_saml/l10n/cs.js", "downloaded_repos/nextcloud_user_saml/l10n/cs.json", "downloaded_repos/nextcloud_user_saml/l10n/da.js", "downloaded_repos/nextcloud_user_saml/l10n/da.json", "downloaded_repos/nextcloud_user_saml/l10n/de.js", "downloaded_repos/nextcloud_user_saml/l10n/de.json", "downloaded_repos/nextcloud_user_saml/l10n/de_DE.js", "downloaded_repos/nextcloud_user_saml/l10n/de_DE.json", "downloaded_repos/nextcloud_user_saml/l10n/el.js", "downloaded_repos/nextcloud_user_saml/l10n/el.json", "downloaded_repos/nextcloud_user_saml/l10n/en_GB.js", "downloaded_repos/nextcloud_user_saml/l10n/en_GB.json", "downloaded_repos/nextcloud_user_saml/l10n/es.js", "downloaded_repos/nextcloud_user_saml/l10n/es.json", "downloaded_repos/nextcloud_user_saml/l10n/es_419.js", "downloaded_repos/nextcloud_user_saml/l10n/es_419.json", "downloaded_repos/nextcloud_user_saml/l10n/es_AR.js", "downloaded_repos/nextcloud_user_saml/l10n/es_AR.json", "downloaded_repos/nextcloud_user_saml/l10n/es_CL.js", "downloaded_repos/nextcloud_user_saml/l10n/es_CL.json", "downloaded_repos/nextcloud_user_saml/l10n/es_CO.js", "downloaded_repos/nextcloud_user_saml/l10n/es_CO.json", "downloaded_repos/nextcloud_user_saml/l10n/es_CR.js", "downloaded_repos/nextcloud_user_saml/l10n/es_CR.json", "downloaded_repos/nextcloud_user_saml/l10n/es_DO.js", "downloaded_repos/nextcloud_user_saml/l10n/es_DO.json", "downloaded_repos/nextcloud_user_saml/l10n/es_EC.js", "downloaded_repos/nextcloud_user_saml/l10n/es_EC.json", "downloaded_repos/nextcloud_user_saml/l10n/es_GT.js", "downloaded_repos/nextcloud_user_saml/l10n/es_GT.json", "downloaded_repos/nextcloud_user_saml/l10n/es_HN.js", "downloaded_repos/nextcloud_user_saml/l10n/es_HN.json", "downloaded_repos/nextcloud_user_saml/l10n/es_MX.js", "downloaded_repos/nextcloud_user_saml/l10n/es_MX.json", "downloaded_repos/nextcloud_user_saml/l10n/es_NI.js", "downloaded_repos/nextcloud_user_saml/l10n/es_NI.json", "downloaded_repos/nextcloud_user_saml/l10n/es_PA.js", "downloaded_repos/nextcloud_user_saml/l10n/es_PA.json", "downloaded_repos/nextcloud_user_saml/l10n/es_PE.js", "downloaded_repos/nextcloud_user_saml/l10n/es_PE.json", "downloaded_repos/nextcloud_user_saml/l10n/es_PR.js", "downloaded_repos/nextcloud_user_saml/l10n/es_PR.json", "downloaded_repos/nextcloud_user_saml/l10n/es_PY.js", "downloaded_repos/nextcloud_user_saml/l10n/es_PY.json", "downloaded_repos/nextcloud_user_saml/l10n/es_SV.js", "downloaded_repos/nextcloud_user_saml/l10n/es_SV.json", "downloaded_repos/nextcloud_user_saml/l10n/es_UY.js", "downloaded_repos/nextcloud_user_saml/l10n/es_UY.json", "downloaded_repos/nextcloud_user_saml/l10n/et_EE.js", "downloaded_repos/nextcloud_user_saml/l10n/et_EE.json", "downloaded_repos/nextcloud_user_saml/l10n/eu.js", "downloaded_repos/nextcloud_user_saml/l10n/eu.json", "downloaded_repos/nextcloud_user_saml/l10n/fa.js", "downloaded_repos/nextcloud_user_saml/l10n/fa.json", "downloaded_repos/nextcloud_user_saml/l10n/fi.js", "downloaded_repos/nextcloud_user_saml/l10n/fi.json", "downloaded_repos/nextcloud_user_saml/l10n/fr.js", "downloaded_repos/nextcloud_user_saml/l10n/fr.json", "downloaded_repos/nextcloud_user_saml/l10n/ga.js", "downloaded_repos/nextcloud_user_saml/l10n/ga.json", "downloaded_repos/nextcloud_user_saml/l10n/gl.js", "downloaded_repos/nextcloud_user_saml/l10n/gl.json", "downloaded_repos/nextcloud_user_saml/l10n/he.js", "downloaded_repos/nextcloud_user_saml/l10n/he.json", "downloaded_repos/nextcloud_user_saml/l10n/hr.js", "downloaded_repos/nextcloud_user_saml/l10n/hr.json", "downloaded_repos/nextcloud_user_saml/l10n/hu.js", "downloaded_repos/nextcloud_user_saml/l10n/hu.json", "downloaded_repos/nextcloud_user_saml/l10n/ia.js", "downloaded_repos/nextcloud_user_saml/l10n/ia.json", "downloaded_repos/nextcloud_user_saml/l10n/id.js", "downloaded_repos/nextcloud_user_saml/l10n/id.json", "downloaded_repos/nextcloud_user_saml/l10n/is.js", "downloaded_repos/nextcloud_user_saml/l10n/is.json", "downloaded_repos/nextcloud_user_saml/l10n/it.js", "downloaded_repos/nextcloud_user_saml/l10n/it.json", "downloaded_repos/nextcloud_user_saml/l10n/ja.js", "downloaded_repos/nextcloud_user_saml/l10n/ja.json", "downloaded_repos/nextcloud_user_saml/l10n/ka.js", "downloaded_repos/nextcloud_user_saml/l10n/ka.json", "downloaded_repos/nextcloud_user_saml/l10n/ka_GE.js", "downloaded_repos/nextcloud_user_saml/l10n/ka_GE.json", "downloaded_repos/nextcloud_user_saml/l10n/ko.js", "downloaded_repos/nextcloud_user_saml/l10n/ko.json", "downloaded_repos/nextcloud_user_saml/l10n/lt_LT.js", "downloaded_repos/nextcloud_user_saml/l10n/lt_LT.json", "downloaded_repos/nextcloud_user_saml/l10n/lv.js", "downloaded_repos/nextcloud_user_saml/l10n/lv.json", "downloaded_repos/nextcloud_user_saml/l10n/nb.js", "downloaded_repos/nextcloud_user_saml/l10n/nb.json", "downloaded_repos/nextcloud_user_saml/l10n/nl.js", "downloaded_repos/nextcloud_user_saml/l10n/nl.json", "downloaded_repos/nextcloud_user_saml/l10n/oc.js", "downloaded_repos/nextcloud_user_saml/l10n/oc.json", "downloaded_repos/nextcloud_user_saml/l10n/pl.js", "downloaded_repos/nextcloud_user_saml/l10n/pl.json", "downloaded_repos/nextcloud_user_saml/l10n/pt_BR.js", "downloaded_repos/nextcloud_user_saml/l10n/pt_BR.json", "downloaded_repos/nextcloud_user_saml/l10n/pt_PT.js", "downloaded_repos/nextcloud_user_saml/l10n/pt_PT.json", "downloaded_repos/nextcloud_user_saml/l10n/ro.js", "downloaded_repos/nextcloud_user_saml/l10n/ro.json", "downloaded_repos/nextcloud_user_saml/l10n/ru.js", "downloaded_repos/nextcloud_user_saml/l10n/ru.json", "downloaded_repos/nextcloud_user_saml/l10n/sc.js", "downloaded_repos/nextcloud_user_saml/l10n/sc.json", "downloaded_repos/nextcloud_user_saml/l10n/sk.js", "downloaded_repos/nextcloud_user_saml/l10n/sk.json", "downloaded_repos/nextcloud_user_saml/l10n/sl.js", "downloaded_repos/nextcloud_user_saml/l10n/sl.json", "downloaded_repos/nextcloud_user_saml/l10n/sq.js", "downloaded_repos/nextcloud_user_saml/l10n/sq.json", "downloaded_repos/nextcloud_user_saml/l10n/sr.js", "downloaded_repos/nextcloud_user_saml/l10n/sr.json", "downloaded_repos/nextcloud_user_saml/l10n/sv.js", "downloaded_repos/nextcloud_user_saml/l10n/sv.json", "downloaded_repos/nextcloud_user_saml/l10n/sw.js", "downloaded_repos/nextcloud_user_saml/l10n/sw.json", "downloaded_repos/nextcloud_user_saml/l10n/th.js", "downloaded_repos/nextcloud_user_saml/l10n/th.json", "downloaded_repos/nextcloud_user_saml/l10n/tr.js", "downloaded_repos/nextcloud_user_saml/l10n/tr.json", "downloaded_repos/nextcloud_user_saml/l10n/ug.js", "downloaded_repos/nextcloud_user_saml/l10n/ug.json", "downloaded_repos/nextcloud_user_saml/l10n/uk.js", "downloaded_repos/nextcloud_user_saml/l10n/uk.json", "downloaded_repos/nextcloud_user_saml/l10n/vi.js", "downloaded_repos/nextcloud_user_saml/l10n/vi.json", "downloaded_repos/nextcloud_user_saml/l10n/zh_CN.js", "downloaded_repos/nextcloud_user_saml/l10n/zh_CN.json", "downloaded_repos/nextcloud_user_saml/l10n/zh_HK.js", "downloaded_repos/nextcloud_user_saml/l10n/zh_HK.json", "downloaded_repos/nextcloud_user_saml/l10n/zh_TW.js", "downloaded_repos/nextcloud_user_saml/l10n/zh_TW.json", "downloaded_repos/nextcloud_user_saml/lib/AppInfo/Application.php", "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigCreate.php", "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigDelete.php", "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigGet.php", "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigSet.php", "downloaded_repos/nextcloud_user_saml/lib/Command/GetMetadata.php", "downloaded_repos/nextcloud_user_saml/lib/Command/GroupMigrationCopyIncomplete.php", "downloaded_repos/nextcloud_user_saml/lib/Controller/SAMLController.php", "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php", "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "downloaded_repos/nextcloud_user_saml/lib/Db/ConfigurationsEntity.php", "downloaded_repos/nextcloud_user_saml/lib/Db/ConfigurationsMapper.php", "downloaded_repos/nextcloud_user_saml/lib/Exceptions/GroupNotFoundException.php", "downloaded_repos/nextcloud_user_saml/lib/Exceptions/NoUserFoundException.php", "downloaded_repos/nextcloud_user_saml/lib/Exceptions/NonMigratableGroupException.php", "downloaded_repos/nextcloud_user_saml/lib/Exceptions/UserFilterViolationException.php", "downloaded_repos/nextcloud_user_saml/lib/GroupBackend.php", "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "downloaded_repos/nextcloud_user_saml/lib/Helper/TXmlHelper.php", "downloaded_repos/nextcloud_user_saml/lib/Jobs/MigrateGroups.php", "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "downloaded_repos/nextcloud_user_saml/lib/Listener/SabrePluginEventListener.php", "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "downloaded_repos/nextcloud_user_saml/lib/Migration/CleanupRemovedConfig.php", "downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php", "downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php", "downloaded_repos/nextcloud_user_saml/lib/Migration/Version3001Date20200630193443.php", "downloaded_repos/nextcloud_user_saml/lib/Migration/Version5000Date20211025124248.php", "downloaded_repos/nextcloud_user_saml/lib/Migration/Version6000Date20220912152700.php", "downloaded_repos/nextcloud_user_saml/lib/Migration/Version6001Date20240202183823.php", "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "downloaded_repos/nextcloud_user_saml/lib/Service/GroupMigration.php", "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php", "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "downloaded_repos/nextcloud_user_saml/lib/UserData.php", "downloaded_repos/nextcloud_user_saml/lib/UserResolver.php", "downloaded_repos/nextcloud_user_saml/psalm.xml", "downloaded_repos/nextcloud_user_saml/rector.php", "downloaded_repos/nextcloud_user_saml/screenshots/1.png", "downloaded_repos/nextcloud_user_saml/screenshots/2.png", "downloaded_repos/nextcloud_user_saml/templates/admin.php", "downloaded_repos/nextcloud_user_saml/templates/error.php", "downloaded_repos/nextcloud_user_saml/templates/login_post.php", "downloaded_repos/nextcloud_user_saml/templates/notPermitted.php", "downloaded_repos/nextcloud_user_saml/templates/notProvisioned.php", "downloaded_repos/nextcloud_user_saml/templates/selectUserBackEnd.php", "downloaded_repos/nextcloud_user_saml/vendor-bin/cs-fixer/composer.json", "downloaded_repos/nextcloud_user_saml/vendor-bin/cs-fixer/composer.lock", "downloaded_repos/nextcloud_user_saml/vendor-bin/psalm/composer.json", "downloaded_repos/nextcloud_user_saml/vendor-bin/psalm/composer.lock", "downloaded_repos/nextcloud_user_saml/vendor-bin/rector/composer.json", "downloaded_repos/nextcloud_user_saml/vendor-bin/rector/composer.lock"], "skipped": [{"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/appstore-build-publish.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/integration.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-mysql.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-oci.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-pgsql.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/.github/workflows/phpunit-sqlite.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/js/vendor/jstz.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigCreate.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigDelete.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigGet.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Command/ConfigSet.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Controller/SettingsController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Controller/TimezoneController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/DavPlugin.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/GroupManager.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Listener/LoadAdditionalScriptsListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Middleware/OnlyLoggedInMiddleware.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Migration/RememberLocalGroupsForPotentialMigrations.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Migration/TransferGroupMembers.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/SAMLSettings.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Admin.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/Settings/Section.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserBackend.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/lib/UserData.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/integration/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/integration/composer.lock", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/integration/features/EnvironmentVariable.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/integration/features/Shibboleth.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/integration/features/bootstrap/FeatureContext.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/psalm-baseline.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/stub.phpstub", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/AppInfo/ApplicationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/Command/GetMetadataTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/Controller/SAMLControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/GroupBackendTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/GroupManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/Middleware/OnlyLoggedInMiddlewareTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/Settings/AdminTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/Settings/SectionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/UserBackendTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/UserDataTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_user_saml/tests/unit/phpunit.xml", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7423698902130127, "profiling_times": {"config_time": 9.859072923660278, "core_time": 4.1937477588653564, "ignores_time": 0.0018856525421142578, "total_time": 14.05616307258606}, "parsing_time": {"total_time": 2.4314992427825928, "per_file_time": {"mean": 0.011415489402735176, "std_dev": 0.0005932388274398369}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 11.502321720123291, "per_file_time": {"mean": 0.01640844753227289, "std_dev": 0.004125629362406314}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.564199924468994, "per_file_and_rule_time": {"mean": 0.0028422646925590055, "std_dev": 0.0001797717428112981}, "very_slow_stats": {"time_ratio": 0.1773624357145447, "count_ratio": 0.0023923444976076554}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/nextcloud_user_saml/lib/Controller/SAMLController.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.10935711860656738}, {"fpath": "downloaded_repos/nextcloud_user_saml/js/admin.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.12471413612365723}, {"fpath": "downloaded_repos/nextcloud_user_saml/js/admin.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.3980839252471924}]}, "tainting_time": {"total_time": 0.7247314453125, "per_def_and_rule_time": {"mean": 0.0011503673735119048, "std_dev": 1.3233123941455083e-05}, "very_slow_stats": {"time_ratio": 0.08343630253073943, "count_ratio": 0.0015873015873015873}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/nextcloud_user_saml/js/admin.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.06046891212463379}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}