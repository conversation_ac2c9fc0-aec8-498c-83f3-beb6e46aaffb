{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/BeryJu_hass-auth-header/custom_components/auth_header/headers.py", "start": {"line": 151, "col": 25, "offset": 5329}, "end": {"line": 151, "col": 98, "offset": 5402}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Found username in credentials: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_hass-auth-header/scripts/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 16, "offset": 38}, "extra": {"message": "Service 'homeassistant' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_hass-auth-header/scripts/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 16, "offset": 38}, "extra": {"message": "Service 'homeassistant' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/BeryJu_hass-auth-header/.github/dependabot.yml", "downloaded_repos/BeryJu_hass-auth-header/.gitignore", "downloaded_repos/BeryJu_hass-auth-header/.isort.cfg", "downloaded_repos/BeryJu_hass-auth-header/.vscode/tasks.json", "downloaded_repos/BeryJu_hass-auth-header/LICENSE", "downloaded_repos/BeryJu_hass-auth-header/Makefile", "downloaded_repos/BeryJu_hass-auth-header/README.md", "downloaded_repos/BeryJu_hass-auth-header/custom_components/auth_header/__init__.py", "downloaded_repos/BeryJu_hass-auth-header/custom_components/auth_header/headers.py", "downloaded_repos/BeryJu_hass-auth-header/custom_components/auth_header/manifest.json", "downloaded_repos/BeryJu_hass-auth-header/custom_components/auth_header/store-token.js", "downloaded_repos/BeryJu_hass-auth-header/hacs.json", "downloaded_repos/<PERSON>ry<PERSON><PERSON>_hass-auth-header/poetry.lock", "downloaded_repos/BeryJu_hass-auth-header/pyproject.toml", "downloaded_repos/<PERSON>ry<PERSON>u_hass-auth-header/scripts/authentik-blueprint.yaml", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_hass-auth-header/scripts/docker-compose.yml", "downloaded_repos/BeryJu_hass-auth-header/scripts/fixed_config/configuration.yaml"], "skipped": [{"path": "downloaded_repos/BeryJu_hass-auth-header/.github/demo.gif", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.976970911026001, "profiling_times": {"config_time": 6.635178565979004, "core_time": 3.0745229721069336, "ignores_time": 0.002119779586791992, "total_time": 9.71329140663147}, "parsing_time": {"total_time": 0.16640281677246094, "per_file_time": {"mean": 0.016640281677246092, "std_dev": 7.927413823836103e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.706289529800415, "per_file_time": {"mean": 0.016052034768191246, "std_dev": 0.0010120142227797878}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.1297149658203125, "per_file_and_rule_time": {"mean": 0.0007767363222773205, "std_dev": 2.2843469758205125e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.032068490982055664, "per_def_and_rule_time": {"mean": 0.00025451183319091797, "std_dev": 7.521673826287286e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}