#!/bin/bash
# Fireshare Vulnerability Testing Environment Setup
# Sets up a test environment for vulnerability assessment

set -e

echo "🔧 Setting up Fireshare Vulnerability Testing Environment"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install requests urllib3

# Create test directories
echo "📁 Creating test directories..."
mkdir -p test_environment/{fireshare_data,fireshare_processed,fireshare_videos}

# Create vulnerable Docker Compose configuration
echo "🐳 Creating vulnerable test configuration..."
cat > test_environment/docker-compose-vulnerable.yml << 'EOF'
version: "3"
services:
  fireshare-vulnerable:
    container_name: fireshare-vulnerable-test
    image: shaneisrael/fireshare:latest
    ports:
      - "8080:80"
    volumes:
      - ./fireshare_data:/data
      - ./fireshare_processed:/processed
      - ./fireshare_videos:/videos
    environment:
      # VULNERABLE CONFIGURATION - DO NOT USE IN PRODUCTION
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=admin
      - SECRET_KEY=vulnerable_secret_key_for_testing
      - MINUTES_BETWEEN_VIDEO_SCANS=5
      - THUMBNAIL_VIDEO_LOCATION=0
      - DOMAIN=localhost:8080
      - PUID=1000
      - PGID=1000
      # Enable LDAP for LDAP injection testing (optional)
      - LDAP_ENABLE=false
      - LDAP_URL=ldap://localhost:389
      - LDAP_BASEDN=dc=example,dc=com
      - LDAP_BINDDN=cn=admin
      - LDAP_PASSWORD=admin
      - LDAP_USER_FILTER=(uid={input})
      - LDAP_ADMIN_GROUP=cn=admins
EOF

# Create sample video files for testing
echo "🎬 Creating sample video files..."
# Create a minimal MP4 file for testing
echo -e '\x00\x00\x00\x20ftypmp41\x00\x00\x00\x00mp41isom\x00\x00\x00\x08free' > test_environment/fireshare_videos/sample.mp4
echo -e '\x00\x00\x00\x20ftypmp41\x00\x00\x00\x00mp41isom\x00\x00\x00\x08free' > test_environment/fireshare_videos/test_video.mp4

# Create test script
echo "📝 Creating test execution script..."
cat > test_environment/run_vulnerability_tests.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Fireshare Vulnerability Assessment"

# Check if Fireshare is running
if ! curl -s http://localhost:8080 > /dev/null; then
    echo "❌ Fireshare is not running. Start it with: docker-compose -f docker-compose-vulnerable.yml up -d"
    exit 1
fi

echo "✅ Fireshare is running"

# Wait for application to be fully ready
echo "⏳ Waiting for application to initialize..."
sleep 10

# Run vulnerability tests
echo "🔍 Running vulnerability assessment..."
python3 ../fireshare_vulnerability_tester.py http://localhost:8080 -u admin -p admin

echo "✅ Vulnerability assessment complete"
EOF

chmod +x test_environment/run_vulnerability_tests.sh

# Create cleanup script
echo "🧹 Creating cleanup script..."
cat > test_environment/cleanup.sh << 'EOF'
#!/bin/bash
echo "🧹 Cleaning up test environment..."

# Stop and remove containers
docker-compose -f docker-compose-vulnerable.yml down -v

# Remove test data
rm -rf fireshare_data/* fireshare_processed/* fireshare_videos/*

echo "✅ Cleanup complete"
EOF

chmod +x test_environment/cleanup.sh

# Create quick start guide
cat > test_environment/QUICK_START.md << 'EOF'
# Quick Start Guide

## 1. Start Vulnerable Fireshare Instance
```bash
cd test_environment
docker-compose -f docker-compose-vulnerable.yml up -d
```

## 2. Wait for Initialization (30 seconds)
```bash
sleep 30
```

## 3. Run Vulnerability Tests
```bash
./run_vulnerability_tests.sh
```

## 4. Access Web Interface (Optional)
Open http://localhost:8080 in your browser
- Username: admin
- Password: admin

## 5. Run Individual Tests
```bash
# Test SQL injection only
python3 ../fireshare_vulnerability_tester.py http://localhost:8080 --test sql-public

# Test with authentication
python3 ../fireshare_vulnerability_tester.py http://localhost:8080 -u admin -p admin --test command
```

## 6. Cleanup
```bash
./cleanup.sh
```

## Expected Vulnerabilities
The test environment should detect:
- ✅ SQL Injection (Public) - CRITICAL
- ✅ Directory Traversal - HIGH  
- ✅ Command Injection - CRITICAL
- ✅ Missing Admin Authorization - HIGH
- ✅ Input Validation Issues - MEDIUM

## Troubleshooting
- If tests fail, check: `docker logs fireshare-vulnerable-test`
- Ensure port 8080 is not in use: `netstat -an | grep 8080`
- Restart container: `docker-compose -f docker-compose-vulnerable.yml restart`
EOF

echo "✅ Test environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. cd test_environment"
echo "2. docker-compose -f docker-compose-vulnerable.yml up -d"
echo "3. ./run_vulnerability_tests.sh"
echo ""
echo "📖 See test_environment/QUICK_START.md for detailed instructions"
echo ""
echo "⚠️  WARNING: This creates a VULNERABLE test environment. Do not expose to networks!"
