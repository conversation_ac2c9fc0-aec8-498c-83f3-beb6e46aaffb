{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/Cotonti_<PERSON>tonti/index.php", "start": {"line": 20, "col": 18, "offset": 481}, "end": {"line": 20, "col": 35, "offset": 498}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/install.php", "start": {"line": 157, "col": 5, "offset": 4484}, "end": {"line": 157, "col": 25, "offset": 4504}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/base.js", "start": {"line": 6, "col": 4793, "offset": 4922}, "end": {"line": 6, "col": 4807, "offset": 4936}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Cotonti_<PERSON>tonti/js/source/src/CotontiApplication.js", "start": {"line": 197, "col": 17, "offset": 5332}, "end": {"line": 197, "col": 52, "offset": 5367}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/Modal.js", "start": {"line": 74, "col": 9, "offset": 2105}, "end": {"line": 74, "col": 44, "offset": 2140}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/Modal.js", "start": {"line": 102, "col": 13, "offset": 2895}, "end": {"line": 102, "col": 43, "offset": 2925}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/Modal.js", "start": {"line": 109, "col": 21, "offset": 3107}, "end": {"line": 109, "col": 51, "offset": 3137}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/Modal.js", "start": {"line": 141, "col": 21, "offset": 4019}, "end": {"line": 141, "col": 57, "offset": 4055}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/Toast.js", "start": {"line": 61, "col": 9, "offset": 1916}, "end": {"line": 61, "col": 44, "offset": 1951}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/serverEvents/sharedWorker.js", "start": {"line": 56, "col": 29, "offset": 1371}, "end": {"line": 56, "col": 86, "offset": 1428}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/inc/install.functions.php", "start": {"line": 34, "col": 9, "offset": 795}, "end": {"line": 34, "col": 29, "offset": 815}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.functions.php", "start": {"line": 127, "col": 5, "offset": 3657}, "end": {"line": 127, "col": 36, "offset": 3688}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.functions.php", "start": {"line": 131, "col": 5, "offset": 3743}, "end": {"line": 131, "col": 33, "offset": 3771}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.functions.php", "start": {"line": 225, "col": 5, "offset": 5756}, "end": {"line": 225, "col": 16, "offset": 5767}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.functions.php", "start": {"line": 228, "col": 6, "offset": 5826}, "end": {"line": 228, "col": 40, "offset": 5860}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.functions.php", "start": {"line": 541, "col": 10, "offset": 12947}, "end": {"line": 541, "col": 52, "offset": 12989}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.functions.php", "start": {"line": 556, "col": 9, "offset": 13661}, "end": {"line": 556, "col": 48, "offset": 13700}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/inc/pfs.main.php", "start": {"line": 237, "col": 10, "offset": 7979}, "end": {"line": 237, "col": 47, "offset": 8016}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/inc/pfs.main.php", "start": {"line": 253, "col": 9, "offset": 8683}, "end": {"line": 253, "col": 43, "offset": 8717}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/inc/pfs.main.php", "start": {"line": 281, "col": 5, "offset": 9502}, "end": {"line": 281, "col": 16, "offset": 9513}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/inc/pfs.main.php", "start": {"line": 283, "col": 6, "offset": 9572}, "end": {"line": 283, "col": 42, "offset": 9608}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/inc/pfs.main.php", "start": {"line": 318, "col": 5, "offset": 10740}, "end": {"line": 318, "col": 16, "offset": 10751}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/inc/pfs.main.php", "start": {"line": 320, "col": 6, "offset": 10810}, "end": {"line": 320, "col": 42, "offset": 10846}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/CreateAction.php", "start": {"line": 53, "col": 19, "offset": 1510}, "end": {"line": 53, "col": 50, "offset": 1541}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/DeleteAction.php", "start": {"line": 64, "col": 19, "offset": 1761}, "end": {"line": 64, "col": 50, "offset": 1792}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/EditAction.php", "start": {"line": 131, "col": 26, "offset": 3637}, "end": {"line": 131, "col": 69, "offset": 3680}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/<PERSON>tonti_Cotonti/plugins/comments/inc/CommentsWidget.php", "start": {"line": 82, "col": 23, "offset": 1942}, "end": {"line": 82, "col": 54, "offset": 1973}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/js/comments.js", "start": {"line": 212, "col": 13, "offset": 6900}, "end": {"line": 212, "col": 50, "offset": 6937}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/InterchangeBuilder.php", "start": {"line": 182, "col": 9, "offset": 5932}, "end": {"line": 182, "col": 56, "offset": 5979}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/search/search.php", "start": {"line": 431, "col": 30, "offset": 18159}, "end": {"line": 431, "col": 103, "offset": 18232}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/<PERSON>tonti_Cotonti/plugins/trashcan/inc/trashcan.functions.php", "start": {"line": 64, "col": 11, "offset": 1592}, "end": {"line": 64, "col": 40, "offset": 1621}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/trashcan/trashcan.admin.php", "start": {"line": 201, "col": 11, "offset": 6308}, "end": {"line": 201, "col": 40, "offset": 6337}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/inc/userimages.functions.php", "start": {"line": 261, "col": 7, "offset": 7467}, "end": {"line": 261, "col": 24, "offset": 7484}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/inc/userimages.functions.php", "start": {"line": 277, "col": 8, "offset": 8084}, "end": {"line": 277, "col": 25, "offset": 8101}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/userimages/userimages.php", "start": {"line": 39, "col": 6, "offset": 948}, "end": {"line": 39, "col": 23, "offset": 965}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/userimages.users.edit.update.php", "start": {"line": 31, "col": 5, "offset": 733}, "end": {"line": 31, "col": 19, "offset": 747}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/rc.php", "start": {"line": 34, "col": 18, "offset": 1008}, "end": {"line": 34, "col": 26, "offset": 1016}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/rc.php", "start": {"line": 46, "col": 33, "offset": 1354}, "end": {"line": 46, "col": 41, "offset": 1362}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/rc.php", "start": {"line": 54, "col": 43, "offset": 1602}, "end": {"line": 54, "col": 51, "offset": 1610}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/rc.php", "start": {"line": 54, "col": 65, "offset": 1624}, "end": {"line": 54, "col": 73, "offset": 1632}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/rc.php", "start": {"line": 75, "col": 8, "offset": 2216}, "end": {"line": 75, "col": 61, "offset": 2269}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/rc.php", "start": {"line": 87, "col": 10, "offset": 2557}, "end": {"line": 87, "col": 18, "offset": 2565}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/Cotonti_Cotonti/system/Resources.php", "start": {"line": 225, "col": 31, "offset": 7061}, "end": {"line": 225, "col": 60, "offset": 7090}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/Resources.php", "start": {"line": 380, "col": 20, "offset": 11350}, "end": {"line": 380, "col": 72, "offset": 11402}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.cache.disk.php", "start": {"line": 193, "col": 6, "offset": 4832}, "end": {"line": 193, "col": 16, "offset": 4842}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.extensions.php", "start": {"line": 437, "col": 41, "offset": 15526}, "end": {"line": 437, "col": 65, "offset": 15550}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.phpinfo-use.phpinfo-use", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.phpinfo.php", "start": {"line": 24, "col": 1, "offset": 641}, "end": {"line": 24, "col": 11, "offset": 651}, "extra": {"message": "The 'phpinfo' function may reveal sensitive information about your environment.", "metadata": {"cwe": ["CWE-200: Exposure of Sensitive Information to an Unauthorized Actor"], "references": ["https://www.php.net/manual/en/function.phpinfo", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/PhpinfosSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2021 - Broken Access Control"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.phpinfo-use.phpinfo-use", "shortlink": "https://sg.run/W82E"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 364, "col": 22, "offset": 9456}, "end": {"line": 364, "col": 32, "offset": 9466}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 402, "col": 11, "offset": 10560}, "end": {"line": 402, "col": 72, "offset": 10621}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 419, "col": 4, "offset": 10897}, "end": {"line": 419, "col": 41, "offset": 10934}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 669, "col": 21, "offset": 17526}, "end": {"line": 669, "col": 44, "offset": 17549}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 722, "col": 6, "offset": 19362}, "end": {"line": 722, "col": 20, "offset": 19376}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 837, "col": 33, "offset": 21828}, "end": {"line": 837, "col": 65, "offset": 21860}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 902, "col": 24, "offset": 23480}, "end": {"line": 902, "col": 52, "offset": 23508}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 1147, "col": 11, "offset": 29684}, "end": {"line": 1147, "col": 49, "offset": 29722}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "start": {"line": 1243, "col": 11, "offset": 31881}, "end": {"line": 1243, "col": 50, "offset": 31920}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/common.php", "start": {"line": 424, "col": 47, "offset": 14289}, "end": {"line": 424, "col": 77, "offset": 14319}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cotemplate.php", "start": {"line": 376, "col": 20, "offset": 9322}, "end": {"line": 376, "col": 60, "offset": 9362}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cotemplate.php", "start": {"line": 377, "col": 19, "offset": 9382}, "end": {"line": 377, "col": 58, "offset": 9421}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Cotonti_Cotonti/system/cotemplate.php", "start": {"line": 378, "col": 18, "offset": 9440}, "end": {"line": 378, "col": 58, "offset": 9480}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Cotonti_Cotonti/system/extrafields.php", "start": {"line": 927, "col": 6, "offset": 32079}, "end": {"line": 927, "col": 32, "offset": 32105}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Cotonti_Cotonti/system/extrafields.php", "start": {"line": 955, "col": 5, "offset": 32924}, "end": {"line": 955, "col": 51, "offset": 32970}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "start": {"line": 4082, "col": 21, "offset": 118954}, "end": {"line": 4082, "col": 34, "offset": 118967}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/Cotonti_Cotonti/system/uploads.php", "start": {"line": 52, "col": 21, "offset": 1469}, "end": {"line": 52, "col": 41, "offset": 1489}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "php.lang.security.injection.printed-request.printed-request", "message": "Timeout when running php.lang.security.injection.printed-request.printed-request on downloaded_repos/Cotonti_Cotonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php:\n ", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "message": "Timeout when running php.lang.security.injection.tainted-callable.tainted-callable on downloaded_repos/Cotonti_Cotonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php:\n ", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "message": "Timeout when running php.lang.security.injection.tainted-exec.tainted-exec on downloaded_repos/Cotonti_Cotonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php:\n ", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Cotonti_<PERSON>tonti/modules/rss/tpl/rss.tpl", "start": {"line": 2, "col": 3, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/Cotonti_Cotonti/modules/rss/tpl/rss.tpl:2:\n `xml version='1.0' encoding=` was unexpected", "path": "downloaded_repos/Cotonti_<PERSON>tonti/modules/rss/tpl/rss.tpl", "spans": [{"file": "downloaded_repos/Cotonti_<PERSON>tonti/modules/rss/tpl/rss.tpl", "start": {"line": 2, "col": 3, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.index.tpl", "start": {"line": 2, "col": 3, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/Cotonti_Cotonti/plugins/sitemap/tpl/sitemap.index.tpl:2:\n `xml version=\"1.0\" encoding=` was unexpected", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.index.tpl", "spans": [{"file": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.index.tpl", "start": {"line": 2, "col": 3, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.tpl", "start": {"line": 2, "col": 3, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/Cotonti_Cotonti/plugins/sitemap/tpl/sitemap.tpl:2:\n `xml version=\"1.0\" encoding=` was unexpected", "path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.tpl", "spans": [{"file": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.tpl", "start": {"line": 2, "col": 3, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 27}}]}], "paths": {"scanned": ["downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/.gitattributes", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/.gitignore", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/.htaccess", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/Install.txt", "downloaded_repos/Cotonti_Cotonti/License.txt", "downloaded_repos/Cotonti_<PERSON>tonti/README.md", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/admin.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/apple-touch-icon.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/composer.json", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/composer.lock", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/datas/.htaccess", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/datas/avatars/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/datas/cache/.htaccess", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/datas/cache/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/datas/config-sample.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/datas/exflds/index.html", "downloaded_repos/Cotonti_Cotonti/datas/extensions.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/datas/index.html", "downloaded_repos/Cotonti_Cotonti/datas/mimetype.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/datas/photos/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/datas/thumbs/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/datas/users/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/favicon.ico", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/blank-avatar.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/filetypes/default/avi.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/bmp.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/gif.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/filetypes/default/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/jpeg.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/jpg.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/mov.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/mp3.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/mpeg.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/mpg.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/filetypes/default/ogg.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/pdf.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/png.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/filetypes/default/qt.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/rar.png", "downloaded_repos/<PERSON>tonti_Cotonti/images/filetypes/default/txt.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/wav.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/filetypes/default/zip.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/filetypes/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/00.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ad.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ae.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/af.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ag.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ai.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/al.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/am.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/an.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ao.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/aq.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ar.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/as.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/at.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/au.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/aw.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ax.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/az.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ba.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/bb.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bd.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/be.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bf.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bg.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bh.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/bi.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/bj.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bl.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bn.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/bo.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/br.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bs.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bt.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bv.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bw.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/by.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/bz.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ca.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cc.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cd.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cf.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cg.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ch.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ci.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ck.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cl.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/cm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cn.png", "downloaded_repos/<PERSON>tonti_Cotonti/images/flags/co.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/cr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cs.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cu.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cv.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cx.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cy.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/cz.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/de.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/dj.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/dk.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/dm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/do.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/dz.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ec.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ee.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/eg.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/eh.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/en.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/er.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/es.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/et.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/fi.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/fj.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/fk.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/fm.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/fo.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/fr.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ga.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gb.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gd.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ge.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gf.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gg.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gh.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/gi.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gl.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gn.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gp.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gq.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gs.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gt.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/gu.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gw.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/gy.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/hk.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/hm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/hn.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/hr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ht.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/hu.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/id.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ie.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/il.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/im.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/in.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/io.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/iq.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ir.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/is.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/it.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/je.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/jm.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/jo.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/jp.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ke.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/kg.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/kh.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ki.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/km.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/kn.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/kp.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/kr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/kw.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ky.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/kz.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/la.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/lb.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/lc.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/li.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/lk.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/lr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ls.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/lt.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/lu.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/lv.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ly.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ma.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/mc.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/md.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/me.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mf.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/mg.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mh.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mk.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ml.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/mm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mn.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/mo.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mp.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mq.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/mr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ms.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mt.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/mu.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mv.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mw.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mx.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/my.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/mz.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/na.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/nc.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ne.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/nf.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ng.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ni.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/nl.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/no.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/np.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/nr.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/nu.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/nz.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/om.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/pa.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/pe.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/pf.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/pg.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ph.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/pk.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/pl.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/pm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/pn.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/pr.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ps.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/pt.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/pw.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/py.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/qa.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/re.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ro.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/rs.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ru.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/rw.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/sa.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sb.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sc.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sd.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/se.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sg.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sh.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/si.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/sj.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sk.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sl.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sn.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/so.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/st.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sv.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sy.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/sz.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tc.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/td.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tf.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tg.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/th.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/tj.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tk.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tl.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tn.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/to.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tr.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tt.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tv.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tw.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/tz.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/ua.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ug.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/uk.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/um.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/us.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/uy.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/uz.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/va.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/vc.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/flags/ve.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/vg.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/vi.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/vn.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/vu.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/wf.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ws.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/ye.png", "downloaded_repos/<PERSON>tonti_Cotonti/images/flags/yt.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/flags/za.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/zm.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/flags/zw.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-down-active.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-down.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-follow.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-left.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-right.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-unread.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-up-active.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/arrow-up.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_1.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_3.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_4.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_5.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_a.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_r.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/auth_w.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/bin.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/blank.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/comments.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/comments2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/delete.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/discheck0.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/discheck1.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/folder.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/forums.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/forums2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/icons/default/16/join1.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/images/icons/default/16/join2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/lock.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/page.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/page2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/prefs.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/star.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/subfolder.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/tools.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/undo.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/16/user.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/comments.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/done.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/error.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/folder.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/forums.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/help.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/info.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/lock.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/message.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/news.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/online.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/page.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/pfs.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/plugin.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/polls.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/prefs.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/queue.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/search.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/stats.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/tags.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/users.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/24/warning.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/32/core.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/32/extension.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/32/folder.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/32/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/32/users-off.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/32/users.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/32/wrench.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/info.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/locale.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/main.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/menus.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/performance.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/php.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/security.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/sessions.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/theme.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/cfg/title.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/images/icons/default/default.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/activity0.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/activity1.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/activity2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/activity3.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/activity4.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/activity5.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/forum.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_hot.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_locked.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_moved.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_new.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_new_hot.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_new_locked.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_new_sticky.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_new_sticky_locked.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_sticky.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/posts_sticky_locked.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/forums/subforum.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/images/icons/default/modules/index.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pfs/folder.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pfs/gallery.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pfs/image.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pfs/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pfs/link.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pfs/thumbnail.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pm/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pm/pm-delete.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pm/pm-edit.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pm/pm-new.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/modules/pm/pm.png", "downloaded_repos/<PERSON>tonti_Cotonti/images/icons/default/modules/users.png", "downloaded_repos/<PERSON>ton<PERSON>_Cotonti/images/icons/default/resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/remove.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/star.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars0.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars1.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars10.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars3.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars4.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars5.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars6.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars7.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars8.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/stars9.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote0.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote1.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote10.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote3.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote4.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote5.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote6.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote7.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote8.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/icons/default/stars/vote9.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/icons/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/pixel.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/angel.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/angry.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/confused.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/devil.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/grin.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/happy.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/smilies/inlove.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/kiss.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/smilies/lang/en.lang.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/smilies/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/smilies/lang/ru.lang.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/rose.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/sad.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/smilies/set.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/sleeping.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/smile.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/sunglasses.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/surprised.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/tongue.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/images/smilies/wink.gif", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/images/spinner.gif", "downloaded_repos/Cotonti_<PERSON>tonti/index.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/install.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/MIT-LICENSE.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/ajax_on.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/base.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/base.min.js.map", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/js/sharedWorkerServerEvents.min.js.map", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/source/gulpfile.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/source/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/package-lock.json", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/js/source/package.json", "downloaded_repos/Cotonti_<PERSON>tonti/js/source/src/CotontiApplication.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/Modal.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/Toast.js", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/js/source/src/_header.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/base.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/source/src/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/js/source/src/serverEvents/ServerEvents.js", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/js/source/src/serverEvents/client/BaseServerEventsClient.js", "downloaded_repos/<PERSON>ton<PERSON>_Cotonti/js/source/src/serverEvents/client/ServerEventsClient.js", "downloaded_repos/<PERSON>tonti_Cotonti/js/source/src/serverEvents/client/ServerEventsSharedWorkerClient.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/serverEvents/client/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/js/source/src/serverEvents/driver/BaseServerEventsDriver.js", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/js/source/src/serverEvents/driver/ServerEventsAjaxDriver.js", "downloaded_repos/<PERSON>ton<PERSON>_Cotonti/js/source/src/serverEvents/driver/ServerEventsDriverFactory.js", "downloaded_repos/<PERSON>ton<PERSON>_Cotonti/js/source/src/serverEvents/driver/ServerEventsSSEDriver.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/serverEvents/driver/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/serverEvents/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/source/src/serverEvents/sharedWorker.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lang/en/admin.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lang/en/countries.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lang/en/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lang/en/main.en.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/lang/en/message.en.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/lang/en/users.en.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/lang/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/lang/ru/admin.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lang/ru/countries.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lang/ru/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lang/ru/main.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lang/ru/message.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lang/ru/translit.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lang/ru/users.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/bootstrap/css/bootstrap.min.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/bootstrap/css/bootstrap.min.css.map", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/bootstrap/css/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/bootstrap/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/bootstrap/js/bootstrap.bundle.min.js.map", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/bootstrap/js/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/lib/cssmin.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/lib/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/jsmin.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/login.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/message.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.admin.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.extrafields.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.posts.getItems.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.structure.getUrl.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.structure.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.topics.getItems.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/forums.trashcan.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/ForumsDictionary.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/forums/inc/ForumsHelper.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/forums/inc/ForumsPostsControlService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/ForumsPostsRepository.php", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/modules/forums/inc/ForumsPostsService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/ForumsTopicsControlService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/ForumsTopicsHelper.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/ForumsTopicsRepository.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/ForumsTopicsService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.editpost.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.newtopic.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.posts.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.sections.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.topics.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/forums/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/forums/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/lang/forums.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/lang/forums.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/forums.install.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/forums.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/forums.uninstall.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/forums.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/forums/setup/patch_0.7.0.4.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/patch_0.8.0.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/patch_0.8.3.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/patch_1.1.3.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/patch_1.1.5.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/patch_1.1.6.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/patch_1.1.7.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/setup/patch_1.1.8.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/tpl/forums.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/tpl/forums.editpost.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/tpl/forums.newtopic.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/tpl/forums.posts.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/tpl/forums.sections.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/forums/tpl/forums.topics.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/forums/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/index/index.html", "downloaded_repos/Cotonti_Cotonti/modules/index/index.php", "downloaded_repos/Cotonti_Cotonti/modules/index/index.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/index/index.setup.php", "downloaded_repos/Cotonti_Cotonti/modules/index/lang/index.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/index/lang/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/modules/index/lang/index.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/install/img/body_bg.png", "downloaded_repos/Cotonti_Cotonti/modules/install/img/box_bg.png", "downloaded_repos/Cotonti_Cotonti/modules/install/img/box_footer.png", "downloaded_repos/Cotonti_Cotonti/modules/install/img/box_header.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/img/box_ul_li.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/install/img/icons.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/img/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/img/submit.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/inc/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/inc/install.functions.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/inc/install.install.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/inc/install.resources.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/inc/install.update.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/install/lang/index.html", "downloaded_repos/Cotonti_Cotonti/modules/install/lang/install.en.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/lang/install.ru.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/tpl/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/install/tpl/install.install.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/install/tpl/install.update.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/install/tpl/style.css", "downloaded_repos/Cotonti_<PERSON>tonti/modules/install/tpl/warnings.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/img/adminmenu_page.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/img/index.html", "downloaded_repos/Cotonti_Cotonti/modules/page/inc/PageControlService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/inc/PageDictionary.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/page/inc/PageRepository.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/inc/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/inc/page.add.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/page/inc/page.counter.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/inc/page.edit.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/inc/page.functions.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/inc/page.list.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/inc/page.main.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/inc/page.resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/lang/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/modules/page/lang/page.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/lang/page.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/page.admin.home.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/page/page.admin.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/page/page.extension.getPublicUrl.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/page.extrafields.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/page.header.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/page/page.item.getItems.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/page.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/page.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/page/page.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/page.structure.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/page/page.trashcan.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/page/setup/page.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/page.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_0.9.0.1.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_0.9.11.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_0.9.12.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_0.9.14.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_0.9.2.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_0.9.4.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_0.9.5.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_1.0.10.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_1.0.11.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_1.0.4.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/setup/patch_1.0.9.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/tpl/page.add.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/tpl/page.admin.home.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/tpl/page.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/page/tpl/page.edit.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/tpl/page.enum.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/tpl/page.list.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/tpl/page.list.unvalidated.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/tpl/page.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/inc/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.admin.allpfs.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.edit.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.editfolder.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.functions.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/inc/pfs.main.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.resources.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/inc/pfs.view.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/lang/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/modules/pfs/lang/pfs.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/lang/pfs.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/pfs.admin.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/pfs.admin.users.add.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/pfs.admin.users.add.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/pfs.admin.users.edit.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/pfs.admin.users.update.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/pfs.comments.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/pfs.forums.tags.php", "downloaded_repos/Cotonti_Cotonti/modules/pfs/pfs.header.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/pfs.page.tags.php", "downloaded_repos/Cotonti_<PERSON>tonti/modules/pfs/pfs.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/pfs.pm.tags.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/pfs.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/pfs.setup.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/setup/patch_0.9.11.sql", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/setup/patch_1.0.12.inc", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/setup/pfs.install.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/setup/pfs.install.sql", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/setup/pfs.uninstall.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/setup/pfs.uninstall.sql", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/tpl/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/tpl/pfs.admin.allpfs.tpl", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pfs/tpl/pfs.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/tpl/pfs.edit.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pfs/tpl/pfs.editfolder.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/tpl/pfs.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pfs/tpl/pfs.view.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/inc/new-message.mp3", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/inc/pm.functions.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/inc/pm.list.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/inc/pm.message.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/inc/pm.resources.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pm/inc/pm.send.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/pm/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/js/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/pm/js/pm.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/lang/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/lang/pm.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/lang/pm.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/pm.header.first.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/pm.header.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pm/pm.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/pm/pm.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/pm.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/pm.useredit.first.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/pm.useredit.tags.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/pm.usertags.php", "downloaded_repos/Cotonti_Cotonti/modules/pm/services/LangService.php", "downloaded_repos/Cotonti_Cotonti/modules/pm/services/PrivateMessageNotificationsService.php", "downloaded_repos/Cotonti_Cotonti/modules/pm/services/PrivateMessageService.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/services/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/setup/patch_1.0.12.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/setup/pm.install.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/setup/pm.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/setup/pm.uninstall.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/setup/pm.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/pm/tpl/pm.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/tpl/pm.list.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/tpl/pm.message.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/tpl/pm.popUpNotification.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/pm/tpl/pm.send.tpl", "downloaded_repos/Cotonti_Cotonti/modules/polls/inc/PollsControlService.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/inc/PollsDictionary.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/polls/inc/PollsHelper.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/polls/inc/PollsRepository.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/polls/inc/PollsService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/polls/inc/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/inc/polls.functions.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/inc/polls.resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/polls/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/polls/js/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/js/polls.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/lang/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/modules/polls/lang/polls.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/lang/polls.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.admin.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.editpost.done.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.editpost.first.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.editpost.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.include.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.newtopic.done.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.newtopic.first.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.newtopic.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.posts.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.topics.loop.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.forums.topics.query.php", "downloaded_repos/<PERSON><PERSON>ti_<PERSON>tonti/modules/polls/polls.index.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/polls/polls.item.delete.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/polls.item.getItems.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/polls.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/polls.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/polls.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/setup/patch_1.0.4.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/setup/patch_1.0.6.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/setup/patch_1.1.0.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/setup/polls.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/setup/polls.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/modules/polls/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/tpl/polls.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/polls/tpl/polls.index.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/polls/tpl/polls.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/rss/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/rss/lang/index.html", "downloaded_repos/Cotonti_Cotonti/modules/rss/lang/rss.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/rss/lang/rss.ru.lang.php", "downloaded_repos/Cotonti_Cotonti/modules/rss/rss.php", "downloaded_repos/Cotonti_<PERSON>tonti/modules/rss/rss.png", "downloaded_repos/Cotonti_Cotonti/modules/rss/rss.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/rss/tpl/index.html", "downloaded_repos/Cotonti_<PERSON>tonti/modules/rss/tpl/rss.tpl", "downloaded_repos/Cotonti_Cotonti/modules/users/inc/UsersControlService.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/UsersDictionary.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/UsersRepository.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/users/inc/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/users.details.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/users/inc/users.edit.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/users.functions.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/users.main.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/inc/users.passrecover.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/users.profile.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/users.register.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/inc/users.resources.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/users/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/users/lang/index.html", "downloaded_repos/Cotonti_Cotonti/modules/users/lang/users.en.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/lang/users.ru.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/users/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/users/setup/patch_1.4.10.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/users/setup/patch_1.4.11.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/users/setup/patch_1.4.12.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/users/setup/patch_1.4.5.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/users/setup/patch_1.4.7.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/users/tpl/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/modules/users/tpl/users.admin.home.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/tpl/users.details.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/users/tpl/users.edit.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/tpl/users.passrecover.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/tpl/users.profile.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/tpl/users.register.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/tpl/users.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/users.admin.home.php", "downloaded_repos/Cotonti_Cotonti/modules/users/users.extrafields.php", "downloaded_repos/Cotonti_Cotonti/modules/users/users.item.getItems.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/users.php", "downloaded_repos/<PERSON>tonti_Cotonti/modules/users/users.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/users/users.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autoalias2/autoalias2.admin.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/autoalias2/autoalias2.page.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autoalias2/autoalias2.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autoalias2/autoalias2.setup.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/autoalias2/inc/autoalias2.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autoalias2/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autoalias2/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/autoalias2/lang/autoalias2.en.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/autoalias2/lang/autoalias2.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autoalias2/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autoalias2/tpl/autoalias2.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/autoalias2/tpl/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/autocomplete/autocomplete.ajax.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autocomplete/autocomplete.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autocomplete/autocomplete.rc.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autocomplete/autocomplete.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autocomplete/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autocomplete/lang/autocomplete.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/autocomplete/lang/autocomplete.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autocomplete/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autocomplete/lib/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autocomplete/lib/jquery.autocomplete.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/banlist.admin.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/banlist.global.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/banlist.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/banlist/banlist.register.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/banlist/banlist.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/banlist/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/banlist/lang/banlist.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/lang/banlist.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/setup/banlist.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/setup/banlist.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/banlist/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/setup/patch_1.0.4.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/setup/patch_1.0.5.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/setup/patch_1.0.6.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/banlist/tpl/banlist.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/banlist/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/ckeditor.anchor.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/ckeditor.editor.php", "downloaded_repos/Cotonti_Cotonti/plugins/ckeditor/ckeditor.pfs.php", "downloaded_repos/Cotonti_Cotonti/plugins/ckeditor/ckeditor.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/ckeditor.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/js/editor.js", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/ckeditor/js/functions.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/js/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/js/presets/ckeditor.default.set.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/js/presets/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/ckeditor/lang/ckeditor.en.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/ckeditor/lang/ckeditor.ru.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/ckeditor/lang/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/ckeditor/lib/LICENSE-ckeditor5.md", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/lib/ckeditor5-content.css", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/ckeditor/lib/ckeditor5-editor.css", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/lib/ckeditor5.css", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/lib/ckeditor5.css.map", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/af.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/af.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/af.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ar.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ar.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ar.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ast.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ast.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ast.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/az.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/az.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/az.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/be.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/be.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/be.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bg.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bg.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bg.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bn.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bn.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bn.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bs.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bs.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/bs.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ca.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ca.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ca.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/cs.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/cs.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/cs.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/da.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/da.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/da.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/de-ch.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/de-ch.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/de-ch.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/de.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/de.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/de.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/el.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/el.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/el.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en-au.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en-au.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en-au.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en-gb.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en-gb.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en-gb.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/en.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/eo.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/eo.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/eo.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/es-co.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/es-co.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/es-co.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/es.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/es.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/es.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/et.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/et.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/et.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/eu.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/eu.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/eu.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/fa.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fa.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fa.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fi.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fi.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fi.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/fr.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fr.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fr.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/gl.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/gl.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/gl.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/gu.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/gu.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/gu.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/he.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/he.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/he.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/hi.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/hi.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/hi.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hr.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hr.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hr.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/hu.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hu.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hu.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hy.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hy.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hy.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/id.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/id.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/id.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/it.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/it.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/it.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/ja.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ja.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ja.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/jv.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/jv.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/jv.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/kk.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/kk.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/kk.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/km.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/km.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/km.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/kn.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/kn.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/kn.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ko.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ko.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ko.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ku.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ku.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ku.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/lt.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/lt.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/lt.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/lv.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/lv.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/lv.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ms.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ms.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ms.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/nb.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/nb.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/nb.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ne.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ne.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ne.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/nl.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/nl.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/nl.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/no.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/no.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/no.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/oc.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/oc.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/oc.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pl.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pl.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pl.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pt-br.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pt-br.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pt-br.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pt.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pt.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/pt.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ro.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ro.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ro.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ru.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ru.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ru.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/si.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/si.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/si.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sk.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sk.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sk.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sl.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sl.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sl.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sq.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sq.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sq.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sr-latn.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sr-latn.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sr-latn.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sr.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sr.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sr.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sv.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sv.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/sv.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/th.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/th.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/th.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/ti.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ti.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ti.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tk.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tk.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tk.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tr.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tr.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tr.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/translation.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/translation.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/translation.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tt.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tt.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/tt.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ug.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ug.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ug.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/uk.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/uk.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/uk.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ur.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ur.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ur.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/uz.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/uz.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/uz.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/vi.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/vi.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/vi.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/zh-cn.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/zh-cn.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/zh-cn.umd.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/zh.d.ts", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/zh.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/zh.umd.js", "downloaded_repos/Cotonti_Cotonti/plugins/cleaner/cleaner.php", "downloaded_repos/Cotonti_Cotonti/plugins/cleaner/cleaner.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/cleaner/cleaner.setup.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/cleaner/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/cleaner/lang/cleaner.en.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/cleaner/lang/cleaner.ru.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/cleaner/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.admin.config.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.admin.home.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.extension.getPublicUrl.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/comments.extension.install.done.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.extrafields.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.item.delete.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.item.getItems.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.list.loop.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/comments.list.query.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.list.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/comments.page.tags.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.polls.index.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.polls.view.tags.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.polls.viewall.tags.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.recentitems.recentpages.query.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.recentitems.recentpages.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/comments.rss.create.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.search.first.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.search.list.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/comments.setup.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/comments.statistics.tags.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/comments.statistics.user.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/comments.trashcan.php", "downloaded_repos/<PERSON>ton<PERSON>_Cotonti/plugins/comments/controllers/IndexController.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/CreateAction.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/DeleteAction.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/DisplayAction.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/EditAction.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/actions/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/admin/IndexController.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/admin/actions/IndexAction.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/controllers/admin/actions/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/controllers/admin/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/controllers/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/comments/inc/BaseCommentsWidget.php", "downloaded_repos/Cotonti_Cotonti/plugins/comments/inc/CommentsControlService.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/comments/inc/CommentsDictionary.php", "downloaded_repos/Cotonti_Cotonti/plugins/comments/inc/CommentsDtoRepository.php", "downloaded_repos/Cotonti_Cotonti/plugins/comments/inc/CommentsNotificationService.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/comments/inc/CommentsRepository.php", "downloaded_repos/Cotonti_Cotonti/plugins/comments/inc/CommentsService.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/comments/inc/CommentsWidget.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/comments/inc/RecentCommentsWidget.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/inc/comments.enablement.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/inc/comments.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/inc/comments.resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/js/comments.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/js/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/comments/lang/comments.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/lang/comments.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/lang/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/comments/setup/comments.install.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/setup/comments.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/setup/comments.uninstall.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/setup/comments.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/setup/patch_0.7.0.1.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/setup/patch_0.9.0.2.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/setup/patch_1.1.4.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/setup/patch_1.1.6.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/setup/patch_2.0.0.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/tpl/comments.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/tpl/comments.edit.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/tpl/comments.recent.widget.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/comments/tpl/comments.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/comments/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/contact/contact.extrafields.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/contact/contact.header.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/contact/contact.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/contact/contact.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/contact/contact.setup.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/contact/contact.tools.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/contact/inc/contact.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/contact/lang/contact.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/contact/lang/contact.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/setup/contact.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/setup/contact.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/contact/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/setup/patch_2.7.1.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/setup/patch_2.7.3.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/tpl/contact.tools.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/tpl/contact.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/contact/tpl/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/hiddengroups/hiddengroups.admin.users.add.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hiddengroups/hiddengroups.admin.users.add.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/hiddengroups/hiddengroups.admin.users.delete.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hiddengroups/hiddengroups.admin.users.edit.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/hiddengroups/hiddengroups.admin.users.row.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hiddengroups/hiddengroups.admin.users.update.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/hiddengroups/hiddengroups.groups.php", "downloaded_repos/Cotonti_Cotonti/plugins/hiddengroups/hiddengroups.png", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/hiddengroups/hiddengroups.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/hiddengroups.users.edit.update.done.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/hiddengroups.users.filters.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/hiddengroups/hiddengroups.users.query.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/hiddengroups.users.register.add.done.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/inc/hiddengroups.functions.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/inc/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/hiddengroups/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/hiddengroups/lang/hiddengroups.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/lang/hiddengroups.ru.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/hiddengroups/lang/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/setup/hiddengroups.install.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/setup/hiddengroups.uninstall.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hiddengroups/setup/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/hits.admin.home.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/hits.admin.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/hits.global.php", "downloaded_repos/Cotonti_<PERSON>tonti/plugins/hits/hits.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/hits.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/inc/hits.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/index.html", "downloaded_repos/Cotonti_<PERSON>tonti/plugins/hits/lang/hits.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/lang/hits.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/lang/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/setup/hits.install.sql", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/setup/hits.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/setup/patch_0.7.1.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/setup/patch_1.2.2.inc", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/hits/setup/patch_1.2.3.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/tpl/hits.admin.home.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/hits/tpl/hits.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/hits/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/html/html.forums.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/html/html.parser.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/html/html.pfs.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/html/html.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/html/html.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/html/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/html/lang/html.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/html/lang/html.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/html/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/html/setup/html.install.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/html/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/htmlpurifier.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/htmlpurifier.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/htmlpurifier.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/htmlpurifier/inc/htmlpurifier.html5.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/htmlpurifier/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/htmlpurifier/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lang/htmlpurifier.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lang/htmlpurifier.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/htmlpurifier/lib/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/Builder/ConfigSchema.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/Builder/Xml.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/Exception.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/Interchange/Directive.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/Interchange/Id.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/Interchange.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/InterchangeBuilder.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/Validator.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/ValidatorAtom.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedClasses.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedFrameTargets.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedRel.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedRev.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.ClassUseCDATA.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultImageAlt.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultInvalidImage.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultInvalidImageAlt.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultTextDir.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.EnableID.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.ForbiddenClasses.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.ID.HTML5.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDBlacklist.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDBlacklistRegexp.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDPrefix.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDPrefixLocal.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.AutoParagraph.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.Custom.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.DisplayLinkURI.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.Linkify.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.PurifierLinkify.DocURL.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.PurifierLinkify.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.Predicate.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.RemoveNbsp.Exceptions.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.RemoveNbsp.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveSpansWithoutAttributes.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowDuplicates.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowImportant.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowTricky.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowedFonts.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowedProperties.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.DefinitionRev.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.ForbiddenProperties.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.MaxImgLength.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.Proprietary.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/CSS.Trusted.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Cache.DefinitionImpl.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Cache.SerializerPath.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Cache.SerializerPermissions.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.AggressivelyFixLt.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.AggressivelyRemoveScript.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.AllowHostnameUnderscore.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.AllowParseManyTags.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.CollectErrors.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.ColorKeywords.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.ConvertDocumentToFragment.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.DirectLexLineNumberSyncInterval.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.DisableExcludes.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.EnableIDNA.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.Encoding.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.EscapeInvalidChildren.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.EscapeInvalidTags.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.EscapeNonASCIICharacters.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.HiddenElements.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.Language.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.LegacyEntityDecoder.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.LexerImpl.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.MaintainLineNumbers.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.NormalizeNewlines.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.RemoveInvalidImg.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.RemoveProcessingInstructions.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Core.RemoveScriptContents.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Filter.Custom.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.Escaping.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.Scope.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.TidyImpl.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Filter.YouTube.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Allowed.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedAttributes.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedComments.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedCommentsRegexp.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedElements.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedModules.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Attr.Name.UseCDATA.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.BlockWrapper.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.CoreModules.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.CustomDoctype.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.DefinitionID.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.DefinitionRev.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Doctype.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.FlashAllowFullScreen.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.ForbiddenAttributes.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.ForbiddenElements.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Forms.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.MaxImgLength.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Nofollow.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Parent.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Proprietary.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeEmbed.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeIframe.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeObject.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeScripting.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Strict.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TargetBlank.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TargetNoopener.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TargetNoreferrer.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TidyAdd.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TidyLevel.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TidyRemove.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Trusted.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/HTML.XHTML.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Output.CommentScriptContents.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Output.FixInnerHTML.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Output.FlashCompat.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Output.Newline.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Output.SortAttr.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Output.TidyFormat.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/Test.ForceNoIconv.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.AllowedSchemes.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.Base.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.DefaultScheme.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.DefinitionID.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.DefinitionRev.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.Disable.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.DisableExternal.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.DisableExternalResources.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.DisableResources.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.Host.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.HostBlacklist.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.MakeAbsolute.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.Munge.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.MungeResources.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.MungeSecretKey.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.OverrideAllowedSchemes.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/URI.SafeIframeRegexp.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema/info.ini", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/ConfigSchema/schema.ser", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/EntityLookup/entities.ser", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Filter/ExtractStyleBlocks.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Filter/YouTube.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Language/messages/en.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Lexer/PH5P.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Printer/CSSDefinition.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Printer/ConfigForm.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Printer/ConfigForm.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Printer/ConfigForm.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Printer/HTMLDefinition.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/standalone/HTMLPurifier/Printer.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/htmlpurifier/lib/standalone/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/presets/htmlpurifier.default.preset.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/presets/htmlpurifier.group_1.preset.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/presets/htmlpurifier.group_5.preset.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/htmlpurifier/presets/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/setup/htmlpurifier.install.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/setup/htmlpurifier.uninstall.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/htmlpurifier/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/i18n.admin.structure.delete.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/i18n.admin.structure.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/i18n.extension.install.done.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/i18n.global.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.header.tags.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.input.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/i18n.list.main.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/i18n.list.query.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/i18n.list.rowcat.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/i18n.list.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/i18n.page.delete.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.page.enum.query.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.page.item.getItems.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.page.main.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/i18n.page.tags.php", "downloaded_repos/Cotonti_Cotonti/plugins/i18n/i18n.pagetags.php", "downloaded_repos/Cotonti_Cotonti/plugins/i18n/i18n.php", "downloaded_repos/Cotonti_Cotonti/plugins/i18n/i18n.png", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.recentitems.recentpages.query.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/i18n.recentitems.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/i18n.search.catlist.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/i18n.search.items.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/i18n.search.query.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.selectBox.structure.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.setup.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/i18n.structure.update.done.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/i18n.tags.search.loop.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/i18n.tags.search.query.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/i18n.trashcan.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/inc/i18n.functions.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/inc/i18n.page.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/inc/i18n.resources.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/inc/i18n.structure.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/inc/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/lang/i18n.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/lang/i18n.ru.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/lang/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/i18n/setup/i18n.install.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/setup/i18n.install.sql", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/setup/i18n.uninstall.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/setup/i18n.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/setup/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/setup/patch_0.7.3.inc", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/setup/patch_0.9.0.inc", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/i18n/setup/patch_1.0.13.inc", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/setup/patch_1.0.6.inc", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/tpl/i18n.locales.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/tpl/i18n.page.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/i18n/tpl/i18n.structure.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/i18n/tpl/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/indexnews/inc/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/inc/indexnews.functions.php", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/indexnews.index.main.php", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/indexnews.index.tags.php", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/indexnews.png", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/indexnews.setup.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/indexnews/lang/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/lang/indexnews.en.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/lang/indexnews.ru.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/indexnews/tpl/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/indexnews/tpl/indexnews.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ipsearch/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ipsearch/ipsearch.admin.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ipsearch/ipsearch.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ipsearch/ipsearch.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ipsearch/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ipsearch/lang/ipsearch.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ipsearch/lang/ipsearch.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ipsearch/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ipsearch/tpl/ipsearch.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/mcaptcha/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/mcaptcha/lang/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/mcaptcha/lang/mcaptcha.en.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/mcaptcha/lang/mcaptcha.ru.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/mcaptcha/mcaptcha.global.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/mcaptcha/mcaptcha.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/mcaptcha/mcaptcha.register.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/mcaptcha/mcaptcha.register.validate.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/mcaptcha/mcaptcha.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/inc/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/inc/ratings.enablement.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/inc/ratings.functions.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/inc/ratings.resources.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/ratings/js/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/js/ratings.js", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/lang/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/lang/ratings.en.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/lang/ratings.ru.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.admin.config.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.admin.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.ajax.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.extension.install.done.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.list.loop.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.list.tags.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.page.delete.done.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.page.tags.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.png", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.rc.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/ratings.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/setup/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/ratings/setup/patch_0.9.0.inc", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/setup/patch_0.9.2.sql", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/ratings/setup/patch_1.1.19.inc", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/setup/ratings.install.php", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/setup/ratings.install.sql", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/setup/ratings.uninstall.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/setup/ratings.uninstall.sql", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/tpl/delete.gif", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/tpl/ratings.admin.tpl", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/tpl/ratings.css", "downloaded_repos/Cotonti_Cotonti/plugins/ratings/tpl/ratings.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/tpl/star.gif", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/recentitems/inc/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/inc/recentitems.functions.php", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/recentitems/lang/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/lang/recentitems.en.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/lang/recentitems.ru.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/recentitems.index.php", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/recentitems.php", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/recentitems.png", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/recentitems.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/recentitems/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/recentitems/tpl/recentitems.forums.index.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/recentitems/tpl/recentitems.forums.tpl", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/tpl/recentitems.pages.index.tpl", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/tpl/recentitems.pages.tpl", "downloaded_repos/Cotonti_Cotonti/plugins/recentitems/tpl/recentitems.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/referers/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/referers/lang/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/referers/lang/referers.en.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/referers/lang/referers.ru.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/referers/referers.admin.php", "downloaded_repos/Cotonti_Cotonti/plugins/referers/referers.global.php", "downloaded_repos/Cotonti_Cotonti/plugins/referers/referers.png", "downloaded_repos/Cotonti_Cotonti/plugins/referers/referers.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/referers/setup/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/referers/setup/patch_1.0.3.inc", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/referers/setup/referers.install.sql", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/referers/setup/referers.uninstall.sql", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/referers/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/referers/tpl/referers.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/search/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/inc/search.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/search/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/search/js/highlight.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/search/js/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/search/lang/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/search/lang/search.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/lang/search.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/search.forums.posts.first.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/search.header.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/search.page.first.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/search/search.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/search.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/search.rc.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/search/search.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/search/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/search/tpl/search.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/sitemap/inc/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/sitemap/inc/sitemap.functions.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/sitemap/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/sitemap/lang/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/sitemap/lang/sitemap.en.lang.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/sitemap/lang/sitemap.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/sitemap/setup/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/setup/sitemap.install.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/sitemap/sitemap.ajax.php", "downloaded_repos/Cotonti_Cotonti/plugins/sitemap/sitemap.png", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/sitemap/sitemap.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/sitemap/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.index.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/statistics/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/statistics/lang/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/statistics/lang/statistics.en.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/statistics/lang/statistics.ru.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/statistics/statistics.php", "downloaded_repos/Cotonti_Cotonti/plugins/statistics/statistics.png", "downloaded_repos/Cotonti_Cotonti/plugins/statistics/statistics.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/statistics/tpl/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/statistics/tpl/statistics.tpl", "downloaded_repos/Cotonti_Cotonti/plugins/tags/inc/TagReferencesRepository.php", "downloaded_repos/Cotonti_Cotonti/plugins/tags/inc/TagsService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/inc/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/inc/tags.config.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/inc/tags.functions.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/inc/tags.resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/lang/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/tags/lang/tags.en.lang.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/lang/tags.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/setup/patch_0.9.14.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/setup/patch_1.0.7.inc", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/setup/patch_1.0.9.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/setup/tags.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/setup/tags.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.admin.page.loop.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.ajax.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.editpost.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.editpost.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.newtopic.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.newtopic.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.posts.main.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.posts.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.forums.topics.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.global.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.i18n.page.delete.done.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.index.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.item.delete.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.list.loop.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.list.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.page.add.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.page.add.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.page.edit.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tags.page.edit.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.page.enum.loop.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.page.main.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.page.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/tags/tags.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/tags/tags.png", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/tags/tags.rc.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.setup.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tags.tools.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/tags/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/tags/tpl/tags.css", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tpl/tags.tools.tpl", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/tags/tpl/tags.tpl", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/trashcan/inc/TrashcanService.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/inc/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/trashcan/inc/trashcan.functions.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/trashcan/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/trashcan/lang/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/trashcan/lang/trashcan.en.lang.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/trashcan/lang/trashcan.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/setup/patch_1.7.2.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/setup/patch_1.7.5.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/setup/trashcan.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/setup/trashcan.uninstall.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/tpl/trashcan.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/tpl/trashcan.info.admin.tpl", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/trashcan/trashcan.admin.home.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/trashcan/trashcan.admin.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/trashcan/trashcan.comment.delete.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/trashcan/trashcan.forums.topic.delete.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/trashcan/trashcan.item.delete.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/trashcan/trashcan.page.delete.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/trashcan/trashcan.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/trashcan/trashcan.post.delete.php", "downloaded_repos/<PERSON>tonti_Cotonti/plugins/trashcan/trashcan.setup.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/trashcan/trashcan.user.delete.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/urleditor/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/inc/urleditor.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/urleditor/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/lang/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/lang/urleditor.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/lang/urleditor.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/presets/compat.dat", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/presets/handy.dat", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/urleditor/presets/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/urleditor/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/urleditor/tpl/urleditor.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/urleditor.admin.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/urleditor.input.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/urleditor.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/urleditor/urleditor.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/userimages/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/inc/userimages.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/inc/userimages.resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/userimages/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/lang/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/userimages/lang/userimages.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/lang/userimages.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/userimages/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/setup/userimages.install.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/userimages/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/tpl/userimages.admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/userimages.admin.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/userimages/userimages.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/userimages/userimages.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/userimages.profile.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/userimages.profile.update.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/userimages.setup.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/userimages.users.edit.tags.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/userimages/userimages.users.edit.update.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/userimages/userimages.usertags.main.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/whosonline/inc/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/whosonline/inc/whosonline.functions.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/whosonline/index.html", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/whosonline/lang/index.html", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/lang/whosonline.en.lang.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/lang/whosonline.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/whosonline/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/whosonline/setup/patch_1.3.4.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/whosonline/setup/patch_1.3.5.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/whosonline/setup/patch_1.3.6.inc", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/whosonline/setup/whosonline.install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/whosonline/tpl/index.html", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/whosonline/tpl/whosonline.tpl", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/whosonline/whosonline.forums.sections.loop.subsections.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/whosonline/whosonline.forums.sections.main.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/whosonline/whosonline.forums.sections.tags.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/whosonline/whosonline.forums.topics.tags.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.global.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.header.main.php", "downloaded_repos/Cotonti_<PERSON>tonti/plugins/whosonline/whosonline.input.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.png", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.setup.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.users.auth.check.done.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.users.logout.php", "downloaded_repos/Cotonti_Cotonti/plugins/whosonline/whosonline.usertags.main.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/rc.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/robots.txt", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/sef-urls.htaccess", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/sef-urls.nginx.conf", "downloaded_repos/Cotonti_Cotonti/setup/README.md", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/setup/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/setup/install.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch-genoa.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch-genoa.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1016.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1027.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1035.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1036.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1049.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1056.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1059.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1062.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1063.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1065.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1068.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1093.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1102.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1105.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1112.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1134.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1135.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1147.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1152.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1164.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1168.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1169.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1169.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1194.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1195.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1252.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1266.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1293.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1297.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1306.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1311.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1318.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.0-r1324.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1326.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1329.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1337.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1370.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1374.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1446.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1447.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1458.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1461.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1463.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1473.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1481.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1484.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1486.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1514.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1572.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1592.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1601.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1620.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1681.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1813.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1842.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.0-r1936.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1971.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r1972.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r881.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r899.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r930.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.0-r943.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.1-r2008.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.1-r2033.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.11-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.11-02.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.11-03.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.11-04.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.12-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.13-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.14-01.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.14-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.14-02.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.14-03.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.15-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.18.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.19-01.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.19.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.2-r2099.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.22.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.23.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.24.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.25.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.26.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.3-r2142.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.3-r2145.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.3-r2145.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.3-r2150.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.4-001.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.4-002.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.5-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.6-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.8-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.8-02.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.8-03.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.8-04.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.8-04.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.8-05.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.9-01.inc", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/setup/siena/patch_0.9.9-01.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/patch_0.9.9-02.sql", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/setup/siena/seditio_bbcodes.sql", "downloaded_repos/Cotonti_Cotonti/system/Cot.php", "downloaded_repos/Cotonti_Cotonti/system/ErrorHandler.php", "downloaded_repos/Cotonti_Cotonti/system/Resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.cache.disk.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.cache.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.config.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.extensions.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/system/admin/admin.extrafields.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.functions.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.home.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.infos.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.log.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.main.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/admin/admin.other.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.phpinfo.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/admin/admin.resources.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.rights.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/admin/admin.rightsbyitem.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.users.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/system/admin/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/system/admin/tpl/admin.cache.disk.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.cache.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.config.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.extensions.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.extrafields.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.home.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.infos.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.log.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.other.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.phpinfo.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.rights.tpl", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/admin/tpl/admin.rightsbyitem.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.structure.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/admin.users.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/footer.tpl", "downloaded_repos/Cotonti_<PERSON>tonti/system/admin/tpl/header.tpl", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/admin/tpl/inc/admin.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/inc/bootstrap-reboot.min.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/inc/fb-buttons.css", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/admin/tpl/inc/helpers.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/system/admin/tpl/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/system/admin/tpl/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/tpl/message.tpl", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/admin/tpl/warnings.tpl", "downloaded_repos/Cotonti_Cotonti/system/auth.php", "downloaded_repos/Cotonti_Cotonti/system/cache.php", "downloaded_repos/Cotonti_Cotonti/system/common.php", "downloaded_repos/<PERSON>tonti_Cotonti/system/configuration.php", "downloaded_repos/Cotonti_Cotonti/system/controllers/BaseAction.php", "downloaded_repos/Cotonti_Cotonti/system/controllers/BaseController.php", "downloaded_repos/Cotonti_Cotonti/system/controllers/MainController.php", "downloaded_repos/Cotonti_Cotonti/system/controllers/ServerEventsController.php", "downloaded_repos/Cotonti_Cotonti/system/controllers/actions/ServerEvents/ServerEventsAjaxAction.php", "downloaded_repos/Cotonti_Cotonti/system/controllers/actions/ServerEvents/ServerSentEventsAction.php", "downloaded_repos/Cotonti_Cotonti/system/controllers/actions/ServerEvents/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/system/controllers/actions/index.html", "downloaded_repos/<PERSON>tonti_Cotonti/system/controllers/index.html", "downloaded_repos/Cotonti_Cotonti/system/cotemplate.php", "downloaded_repos/<PERSON><PERSON><PERSON>_Cotonti/system/database/DataBaseDictionary.php", "downloaded_repos/<PERSON>ton<PERSON>_Cotonti/system/database/exceptions/DeadlockException.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>ti/system/database/exceptions/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/system/database/index.html", "downloaded_repos/<PERSON>ton<PERSON>_Cotonti/system/database/traits/DetectLostConnectionTrait.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/database/traits/TransactionTrait.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/system/database/traits/index.html", "downloaded_repos/Cotonti_Cotonti/system/database.php", "downloaded_repos/Cotonti_Cotonti/system/debug.php", "downloaded_repos/Cotonti_Cotonti/system/dto/ItemDto.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/dto/index.html", "downloaded_repos/Cotonti_Cotonti/system/exceptions/BadRequestHttpException.php", "downloaded_repos/<PERSON>tonti_Cotonti/system/exceptions/ForbiddenHttpException.php", "downloaded_repos/Cotonti_Cotonti/system/exceptions/HttpException.php", "downloaded_repos/Cotonti_Cotonti/system/exceptions/InvalidConfigException.php", "downloaded_repos/Cotonti_Cotonti/system/exceptions/NotFoundHttpException.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>ti/system/exceptions/index.html", "downloaded_repos/Cotonti_Cotonti/system/extensions/ExtensionsControlService.php", "downloaded_repos/Cotonti_Cotonti/system/extensions/ExtensionsDictionary.php", "downloaded_repos/Cotonti_Cotonti/system/extensions/ExtensionsHelper.php", "downloaded_repos/Cotonti_Cotonti/system/extensions/ExtensionsRepository.php", "downloaded_repos/Cotonti_Cotonti/system/extensions/ExtensionsService.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>ti/system/extensions/index.html", "downloaded_repos/Cotonti_Cotonti/system/extensions.php", "downloaded_repos/Cotonti_Cotonti/system/extrafields.php", "downloaded_repos/Cotonti_Cotonti/system/footer.php", "downloaded_repos/Cotonti_Cotonti/system/forms.php", "downloaded_repos/Cotonti_Cotonti/system/functions.php", "downloaded_repos/Cotonti_Cotonti/system/header.php", "downloaded_repos/<PERSON>tonti_<PERSON>tonti/system/index.html", "downloaded_repos/Cotonti_Cotonti/system/plugin.php", "downloaded_repos/Cotonti_Cotonti/system/repositories/BaseRepository.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/system/repositories/index.html", "downloaded_repos/Cotonti_Cotonti/system/resources.rc.php", "downloaded_repos/Cotonti_Cotonti/system/router/Route.php", "downloaded_repos/Cotonti_Cotonti/system/router/Router.php", "downloaded_repos/Cotonti_Cotonti/system/router/index.html", "downloaded_repos/Cotonti_Cotonti/system/serverEvents/ServerEventMessageDto.php", "downloaded_repos/Cotonti_Cotonti/system/serverEvents/ServerEventService.php", "downloaded_repos/Cotonti_Cotonti/system/serverEvents/ServerEventsDictionary.php", "downloaded_repos/Cotonti_Cotonti/system/serverEvents/ServerEventsObserverService.php", "downloaded_repos/Cotonti_Cotonti/system/serverEvents/repositories/ServerEventsObserversRepository.php", "downloaded_repos/Cotonti_Cotonti/system/serverEvents/repositories/ServerEventsRepository.php", "downloaded_repos/Cotonti_Cotonti/system/services/ItemService.php", "downloaded_repos/Cotonti_Cotonti/system/services/index.html", "downloaded_repos/Cotonti_Cotonti/system/structure/StructureControlService.php", "downloaded_repos/Cotonti_Cotonti/system/structure/StructureDictionary.php", "downloaded_repos/Cotonti_Cotonti/system/structure/StructureDtoRepository.php", "downloaded_repos/Cotonti_Cotonti/system/structure/StructureHelper.php", "downloaded_repos/Cotonti_Cotonti/system/structure/StructureRepository.php", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>ti/system/structure/index.html", "downloaded_repos/Cotonti_Cotonti/system/structure.php", "downloaded_repos/Cotonti_Cotonti/system/traits/GetInstanceTrait.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/system/traits/index.html", "downloaded_repos/Cotonti_Cotonti/system/uploads.php", "downloaded_repos/<PERSON>tonti_Cotonti/system/users/UsersHelper.php", "downloaded_repos/<PERSON>tonti_Cotonti/system/users/UsersRepository.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/themes/.htaccess", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/admin/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/themes/nemesis/css/default.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/css/extras.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/css/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/themes/nemesis/css/modalbox.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/css/reset.css", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/error.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/footer.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/header.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/img/bullets.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/themes/nemesis/img/code-blueprint.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/img/comments.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/img/download.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/themes/nemesis/img/favicon.svg", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/themes/nemesis/img/front_image.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/themes/nemesis/img/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/img/online0.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/img/online1.png", "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>ti/themes/nemesis/img/powered-by-cotonti.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/img/rss.png", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/inc/contact.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/themes/nemesis/inc/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/index.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/themes/nemesis/js/index.html", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>/themes/nemesis/js/js.js", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/login.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/message.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/nemesis.en.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/nemesis.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/nemesis.rc.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/nemesis.ru.lang.php", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/themes/nemesis/plugin.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/popup.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/themes/nemesis/warnings.tpl"], "skipped": [{"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/base.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/jqModal.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/js/jquery.tablednd.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON>tonti_Cotonti/js/sharedWorkerServerEvents.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/bootstrap/js/bootstrap.bundle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/lib/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/lib/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/lib/vendor/composer/platform_check.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Cotonti_<PERSON>tonti/modules/rss/tpl/rss.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/autocomplete/lib/jquery.autocomplete.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/lib/ckeditor5.umd.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ckeditor/lib/ckeditor5.umd.js.map", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/ratings/js/jquery.rating.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.index.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/plugins/sitemap/tpl/sitemap.tpl", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.0885519981384277, "profiling_times": {"config_time": 6.737226963043213, "core_time": 23.264204502105713, "ignores_time": 0.0019311904907226562, "total_time": 30.00450325012207}, "parsing_time": {"total_time": 44.65614700317383, "per_file_time": {"mean": 0.0481208480637649, "std_dev": 0.011892462122729727}, "very_slow_stats": {"time_ratio": 0.2106912719354408, "count_ratio": 0.023706896551724137}, "very_slow_files": [{"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/de.js", "ftime": 0.34939098358154297}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/modules/forums/inc/forums.posts.php", "ftime": 0.3584311008453369}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/plugins/ckeditor/lib/translations/he.umd.js", "ftime": 0.36030101776123047}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/hr.js", "ftime": 0.3605978488922119}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/uz.js", "ftime": 0.39684104919433594}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/fi.js", "ftime": 0.3970448970794678}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/ckeditor/lib/translations/ko.umd.js", "ftime": 0.4081261157989502}, {"fpath": "downloaded_repos/<PERSON>tonti_Cotonti/system/configuration.php", "ftime": 0.4081909656524658}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/common.php", "ftime": 0.41320204734802246}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "ftime": 2.076657772064209}]}, "scanning_time": {"total_time": 192.74467182159424, "per_file_time": {"mean": 0.03745524131783801, "std_dev": 0.08450499341788635}, "very_slow_stats": {"time_ratio": 0.24133190853338068, "count_ratio": 0.00194325689856199}, "very_slow_files": [{"fpath": "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/inc/install.install.php", "ftime": 2.0571751594543457}, {"fpath": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/search/search.php", "ftime": 2.4885990619659424}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/cache.php", "ftime": 2.6025969982147217}, {"fpath": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/inc/page.functions.php", "ftime": 2.9563469886779785}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php", "ftime": 3.2197580337524414}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "ftime": 3.6833391189575195}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/base.js", "ftime": 4.128629207611084}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/common.php", "ftime": 4.482264995574951}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/plugins/htmlpurifier/lib/HTMLPurifier.standalone.php", "ftime": 5.648507833480835}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "ftime": 15.24822211265564}]}, "matching_time": {"total_time": 82.00486660003662, "per_file_and_rule_time": {"mean": 0.023735127814771835, "std_dev": 0.0056411172085985865}, "very_slow_stats": {"time_ratio": 0.4485348623159176, "count_ratio": 0.05036179450072359}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/<PERSON>tonti_<PERSON>tonti/modules/page/inc/page.functions.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.5647969245910645}, {"fpath": "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/inc/install.install.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.5726010799407959}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.605741024017334}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/common.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.6803381443023682}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>ti/js/base.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.734454870223999}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.749330997467041}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.8217859268188477}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 1.3637759685516357}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 1.560723066329956}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/functions.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 2.491748094558716}]}, "tainting_time": {"total_time": 19.254951000213623, "per_def_and_rule_time": {"mean": 0.0030241795194304423, "std_dev": 6.466987188253993e-05}, "very_slow_stats": {"time_ratio": 0.13798532358108268, "count_ratio": 0.004554735354169939}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.10458803176879883}, {"fpath": "downloaded_repos/<PERSON>tonti_Cotonti/modules/install/inc/install.install.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.10517311096191406}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "fline": 1, "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.10617804527282715}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "time": 0.10765504837036133}, {"fpath": "downloaded_repos/Cotonti_Cotonti/system/common.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.11021113395690918}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.11601495742797852}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.11652588844299316}, {"fpath": "downloaded_repos/<PERSON>ton<PERSON>_<PERSON>tonti/plugins/search/search.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.14366602897644043}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "time": 0.15844511985778809}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_<PERSON>tonti/system/admin/admin.structure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 0.16769719123840332}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1159944128}, "engine_requested": "OSS", "skipped_rules": []}