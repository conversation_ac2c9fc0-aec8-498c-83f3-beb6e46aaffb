{"version": "1.130.0", "results": [{"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 40, "col": 3, "offset": 1289}, "end": {"line": 40, "col": 80, "offset": 1366}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 64, "col": 3, "offset": 2039}, "end": {"line": 67, "col": 6, "offset": 2169}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 75, "col": 3, "offset": 2299}, "end": {"line": 79, "col": 6, "offset": 2444}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 87, "col": 3, "offset": 2635}, "end": {"line": 93, "col": 22, "offset": 2870}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 95, "col": 4, "offset": 2895}, "end": {"line": 101, "col": 23, "offset": 3100}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 103, "col": 3, "offset": 3107}, "end": {"line": 123, "col": 6, "offset": 3535}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 124, "col": 3, "offset": 3538}, "end": {"line": 129, "col": 6, "offset": 3715}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 137, "col": 3, "offset": 3815}, "end": {"line": 143, "col": 22, "offset": 4054}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 145, "col": 4, "offset": 4079}, "end": {"line": 151, "col": 23, "offset": 4283}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 153, "col": 3, "offset": 4290}, "end": {"line": 166, "col": 6, "offset": 4584}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 174, "col": 3, "offset": 4752}, "end": {"line": 177, "col": 22, "offset": 4832}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 180, "col": 4, "offset": 4858}, "end": {"line": 183, "col": 7, "offset": 4934}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 186, "col": 3, "offset": 4942}, "end": {"line": 191, "col": 6, "offset": 5066}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 199, "col": 3, "offset": 5265}, "end": {"line": 202, "col": 22, "offset": 5345}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 205, "col": 4, "offset": 5371}, "end": {"line": 208, "col": 7, "offset": 5447}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 211, "col": 3, "offset": 5455}, "end": {"line": 217, "col": 6, "offset": 5607}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 225, "col": 3, "offset": 5713}, "end": {"line": 228, "col": 22, "offset": 5796}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 230, "col": 4, "offset": 5821}, "end": {"line": 233, "col": 7, "offset": 5897}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 242, "col": 3, "offset": 6020}, "end": {"line": 246, "col": 6, "offset": 6104}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 293, "col": 3, "offset": 7315}, "end": {"line": 296, "col": 24, "offset": 7425}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 312, "col": 3, "offset": 7860}, "end": {"line": 315, "col": 24, "offset": 7970}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 329, "col": 6, "offset": 8331}, "end": {"line": 342, "col": 9, "offset": 8647}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 350, "col": 6, "offset": 8785}, "end": {"line": 353, "col": 9, "offset": 8868}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 367, "col": 3, "offset": 9162}, "end": {"line": 371, "col": 15, "offset": 9324}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 379, "col": 3, "offset": 9490}, "end": {"line": 382, "col": 24, "offset": 9600}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "start": {"line": 396, "col": 3, "offset": 9958}, "end": {"line": 409, "col": 6, "offset": 10259}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ldap-bind-without-password.ldap-bind-without-password", "path": "downloaded_repos/heiglandreas_authLdap/src/Wrapper/Ldap.php", "start": {"line": 35, "col": 11, "offset": 728}, "end": {"line": 35, "col": 39, "offset": 756}, "extra": {"message": "Detected anonymous LDAP bind. This permits anonymous users to execute LDAP statements. Consider enforcing authentication for LDAP.", "metadata": {"references": ["https://www.php.net/manual/en/function.ldap-bind.php"], "cwe": ["CWE-287: Improper Authentication"], "owasp": ["A02:2017 - Broken Authentication", "A07:2021 - Identification and Authentication Failures"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/php.lang.security.ldap-bind-without-password.ldap-bind-without-password", "shortlink": "https://sg.run/18Rv"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml", "start": {"line": 35, "col": 59, "offset": 1022}, "end": {"line": 35, "col": 62, "offset": 1025}}, {"path": "downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml", "start": {"line": 35, "col": 115, "offset": 1022}, "end": {"line": 35, "col": 118, "offset": 1025}}]], "message": "Syntax error at line downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml:35:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml", "spans": [{"file": "downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml", "start": {"line": 35, "col": 59, "offset": 1022}, "end": {"line": 35, "col": 62, "offset": 1025}}, {"file": "downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml", "start": {"line": 35, "col": 115, "offset": 1022}, "end": {"line": 35, "col": 118, "offset": 1025}}]}], "paths": {"scanned": ["downloaded_repos/heiglandreas_authLdap/.ci/50-init.ldif", "downloaded_repos/heiglandreas_authLdap/.distignore", "downloaded_repos/heiglandreas_authLdap/.editorconfig", "downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml", "downloaded_repos/heiglandreas_authLdap/.github/workflows/codacy.yml", "downloaded_repos/heiglandreas_authLdap/.github/workflows/deploy_on_release.yml", "downloaded_repos/heiglandreas_authLdap/.github/workflows/tests.yml", "downloaded_repos/heiglandreas_authLdap/.gitignore", "downloaded_repos/heiglandreas_authLdap/.phive/phars.xml", "downloaded_repos/heiglandreas_authLdap/.svnAccess.dist", "downloaded_repos/heiglandreas_authLdap/LICENSE.md", "downloaded_repos/heiglandreas_authLdap/README.md", "downloaded_repos/heiglandreas_authLdap/SECURITY.md", "downloaded_repos/heiglandreas_authLdap/authLdap.css", "downloaded_repos/heiglandreas_authLdap/authLdap.php", "downloaded_repos/heiglandreas_authLdap/behat.yml.dist", "downloaded_repos/heiglandreas_authLdap/build.xml", "downloaded_repos/heiglandreas_authLdap/compose.yml", "downloaded_repos/heiglandreas_authLdap/composer.json", "downloaded_repos/heiglandreas_authLdap/dockersetup/Dockerfile_wordpress", "downloaded_repos/heiglandreas_authLdap/dockersetup/ldap/environment", "downloaded_repos/heiglandreas_authLdap/dockersetup/nginx/default.conf", "downloaded_repos/heiglandreas_authLdap/features/bootstrap/FeatureContext.php", "downloaded_repos/heiglandreas_authLdap/features/log in as LDAP user without wpaccount take over.feature", "downloaded_repos/heiglandreas_authLdap/features/log in using no groups at all.feature", "downloaded_repos/heiglandreas_authLdap/features/log in with multiple role-assignements in LDAP.feature", "downloaded_repos/heiglandreas_authLdap/features/log in with role-assignement in LDAP based on DN .feature", "downloaded_repos/heiglandreas_authLdap/phpcs-version-check.xml", "downloaded_repos/heiglandreas_authLdap/phpcs.xml", "downloaded_repos/heiglandreas_authLdap/phpunit.xml.dist", "downloaded_repos/heiglandreas_authLdap/readme.txt", "downloaded_repos/heiglandreas_authLdap/security.txt", "downloaded_repos/heiglandreas_authLdap/src/Authenticate.php", "downloaded_repos/heiglandreas_authLdap/src/Authorize.php", "downloaded_repos/heiglandreas_authLdap/src/Exception/Error.php", "downloaded_repos/heiglandreas_authLdap/src/Exception/InvalidLdapUri.php", "downloaded_repos/heiglandreas_authLdap/src/Exception/MissingValidLdapConnection.php", "downloaded_repos/heiglandreas_authLdap/src/Exception/SearchUnsuccessfull.php", "downloaded_repos/heiglandreas_authLdap/src/Exception/UnknownOption.php", "downloaded_repos/heiglandreas_authLdap/src/LdapList.php", "downloaded_repos/heiglandreas_authLdap/src/LdapUri.php", "downloaded_repos/heiglandreas_authLdap/src/LoggedInUserToWpUser.php", "downloaded_repos/heiglandreas_authLdap/src/Logger.php", "downloaded_repos/heiglandreas_authLdap/src/LoggerInterface.php", "downloaded_repos/heiglandreas_authLdap/src/Manager/Ldap.php", "downloaded_repos/heiglandreas_authLdap/src/OptionFactory.php", "downloaded_repos/heiglandreas_authLdap/src/Options.php", "downloaded_repos/heiglandreas_authLdap/src/UserRoleHandler.php", "downloaded_repos/heiglandreas_authLdap/src/Value/CachePassword.php", "downloaded_repos/heiglandreas_authLdap/src/Value/DefaultRole.php", "downloaded_repos/heiglandreas_authLdap/src/Value/DoNotOverwriteNonLdapUsers.php", "downloaded_repos/heiglandreas_authLdap/src/Value/Enabled.php", "downloaded_repos/heiglandreas_authLdap/src/Value/GroupAssignment.php", "downloaded_repos/heiglandreas_authLdap/src/Value/GroupAttribute.php", "downloaded_repos/heiglandreas_authLdap/src/Value/GroupBase.php", "downloaded_repos/heiglandreas_authLdap/src/Value/GroupEnabled.php", "downloaded_repos/heiglandreas_authLdap/src/Value/GroupFilter.php", "downloaded_repos/heiglandreas_authLdap/src/Value/GroupOverUser.php", "downloaded_repos/heiglandreas_authLdap/src/Value/GroupSeparator.php", "downloaded_repos/heiglandreas_authLdap/src/Value/Groups.php", "downloaded_repos/heiglandreas_authLdap/src/Value/LoggedInUser.php", "downloaded_repos/heiglandreas_authLdap/src/Value/MailAttribute.php", "downloaded_repos/heiglandreas_authLdap/src/Value/NameAttribute.php", "downloaded_repos/heiglandreas_authLdap/src/Value/Password.php", "downloaded_repos/heiglandreas_authLdap/src/Value/ReadLdapAsUser.php", "downloaded_repos/heiglandreas_authLdap/src/Value/SecondNameAttribute.php", "downloaded_repos/heiglandreas_authLdap/src/Value/UidAttribute.php", "downloaded_repos/heiglandreas_authLdap/src/Value/UserFilter.php", "downloaded_repos/heiglandreas_authLdap/src/Value/Username.php", "downloaded_repos/heiglandreas_authLdap/src/Value/WebAttribute.php", "downloaded_repos/heiglandreas_authLdap/src/Wrapper/Ldap.php", "downloaded_repos/heiglandreas_authLdap/src/Wrapper/LdapFactory.php", "downloaded_repos/heiglandreas_authLdap/src/Wrapper/LdapInterface.php", "downloaded_repos/heiglandreas_authLdap/view/admin.phtml"], "skipped": [{"path": "downloaded_repos/heiglandreas_authLdap/.github/workflows/behat.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/heiglandreas_authLdap/tests/LDAPListBaseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/heiglandreas_authLdap/tests/LdapTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/heiglandreas_authLdap/tests/LdapUriTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/heiglandreas_authLdap/tests/Manager/LDAPBaseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/heiglandreas_authLdap/tests/UserRoleHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/heiglandreas_authLdap/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6210908889770508, "profiling_times": {"config_time": 5.93578314781189, "core_time": 2.6264054775238037, "ignores_time": 0.001912832260131836, "total_time": 8.56495475769043}, "parsing_time": {"total_time": 0.4381735324859619, "per_file_time": {"mean": 0.008763470649719239, "std_dev": 0.00014911811907634276}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.3921689987182617, "per_file_time": {"mean": 0.0069958241141621184, "std_dev": 0.0006036554226258087}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.3762214183807373, "per_file_and_rule_time": {"mean": 0.0018174947747861703, "std_dev": 3.91088112124182e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.05088496208190918, "per_def_and_rule_time": {"mean": 0.0004240413506825765, "std_dev": 5.70161323590273e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}