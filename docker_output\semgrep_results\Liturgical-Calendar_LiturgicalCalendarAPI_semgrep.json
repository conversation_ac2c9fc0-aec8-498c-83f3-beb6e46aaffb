{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/Dockerfile", "start": {"line": 54, "col": 1, "offset": 1893}, "end": {"line": 54, "col": 57, "offset": 1949}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"php\", \"-S\", \"0.0.0.0:8000\", \"-t\", \"/var/www/html\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.php-permissive-cors.php-permissive-cors", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Core.php", "start": {"line": 72, "col": 13, "offset": 2996}, "end": {"line": 72, "col": 53, "offset": 3036}, "extra": {"message": "Access-Control-Allow-Origin response header is set to \"*\". This will disable CORS Same Origin Policy restrictions.", "metadata": {"references": ["https://developer.mozilla.org/ru/docs/Web/HTTP/Headers/Access-Control-Allow-Origin"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-346: Origin Validation Error"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/php.lang.security.php-permissive-cors.php-permissive-cors", "shortlink": "https://sg.run/y1XR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.php-permissive-cors.php-permissive-cors", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Metadata.php", "start": {"line": 287, "col": 9, "offset": 13127}, "end": {"line": 287, "col": 49, "offset": 13167}, "extra": {"message": "Access-Control-Allow-Origin response header is set to \"*\". This will disable CORS Same Origin Policy restrictions.", "metadata": {"references": ["https://developer.mozilla.org/ru/docs/Web/HTTP/Headers/Access-Control-Allow-Origin"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-346: Origin Validation Error"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/php.lang.security.php-permissive-cors.php-permissive-cors", "shortlink": "https://sg.run/y1XR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "start": {"line": 508, "col": 17, "offset": 23229}, "end": {"line": 508, "col": 34, "offset": 23246}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "start": {"line": 605, "col": 17, "offset": 27620}, "end": {"line": 605, "col": 30, "offset": 27633}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "start": {"line": 704, "col": 17, "offset": 32173}, "end": {"line": 704, "col": 30, "offset": 32186}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "start": {"line": 897, "col": 27, "offset": 40666}, "end": {"line": 897, "col": 52, "offset": 40691}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "start": {"line": 904, "col": 31, "offset": 41263}, "end": {"line": 904, "col": 44, "offset": 41276}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Tests.php", "start": {"line": 96, "col": 21, "offset": 3731}, "end": {"line": 96, "col": 61, "offset": 3771}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/EventsParams.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/EventsParams.php:34:\n `object` was unexpected", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/EventsParams.php", "spans": [{"file": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/EventsParams.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "start": {"line": 23, "col": 22, "offset": 0}, "end": {"line": 23, "col": 28, "offset": 6}}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "start": {"line": 33, "col": 18, "offset": 0}, "end": {"line": 33, "col": 23, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php:23:\n `object` was unexpected", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "spans": [{"file": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "start": {"line": 23, "col": 22, "offset": 0}, "end": {"line": 23, "col": 28, "offset": 6}}, {"file": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "start": {"line": 33, "col": 18, "offset": 0}, "end": {"line": 33, "col": 23, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Metadata.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 24, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Metadata.php:19:\n `array` was unexpected", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Metadata.php", "spans": [{"file": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Metadata.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 24, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "start": {"line": 31, "col": 13, "offset": 0}, "end": {"line": 31, "col": 21, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php:31:\n `readonly` was unexpected", "path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "spans": [{"file": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "start": {"line": 31, "col": 13, "offset": 0}, "end": {"line": 31, "col": 21, "offset": 8}}]}], "paths": {"scanned": ["downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.env.example", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.github/FUNDING.yml", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.github/ISSUE_TEMPLATE/new_bug_report.yml", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.github/PULL_REQUEST_TEMPLATE/pull_request_template.md", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.github/dependabot.yml", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.github/workflows/main.yml", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.gitignore", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/.vscode/tasks.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/CODE_OF_CONDUCT.md", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/CONTRIBUTING.md", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/Dockerfile", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/Dockerfile.dockerignore", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/LICENSE", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/LitCalTestServer.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/README.md", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/composer.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/composer.lock", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/de/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/de/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/es/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/es/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/fr/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/fr/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/hr/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/hr/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/hu/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/hu/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/id/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/id/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/it/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/it/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/la/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/la/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/litcal.pot", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/nl/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/nl/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/pt/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/pt/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/sk/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/sk/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/vi/LC_MESSAGES/litcal.mo", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/i18n/vi/LC_MESSAGES/litcal.po", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/index.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/intelephense-helper.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/CommonDef.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/DiocesanCalendar.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCal.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalDataPath.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalDecreesPath.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalDecreesSource.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalEasterPath.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalEventsPath.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalMetadata.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalMissalsPath.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalSchemasPath.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalTest.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalTestsPath.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LitCalTranslation.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/LiturgicalCalendar.xsd", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/NationalCalendar.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/PropriumDeSanctis.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/PropriumDeTempore.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/WiderRegionCalendar.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/schemas/openapi.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/CA/charlo_ca/Charlottetown.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/CA/charlo_ca/i18n/en_CA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/CA/charlo_ca/i18n/fr_CA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/IT/romamo_it/Diocesi di Roma.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/IT/romamo_it/i18n/it_IT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/IT/socaap_it/Diocesi di Sora - Cassino - Aquino - Pontecorvo.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/IT/socaap_it/i18n/it_IT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/bredad_nl/Breda.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/bredad_nl/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/grolee_nl/Groningen–Leeuwarden.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/grolee_nl/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/haaams_nl/Haarlem–Amsterdam.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/haaams_nl/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/roermo_nl/Roermond.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/roermo_nl/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/rotter_nl/Rotterdam.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/rotter_nl/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/sherto_nl/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/sherto_nl/’s-Hertogenbosch.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/utrech_nl/Utrecht.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/NL/utrech_nl/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/US/boston_us/Archdiocese of Boston (Massachusetts).json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/dioceses/US/boston_us/i18n/en_US.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/CA/CA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/CA/i18n/en_CA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/CA/i18n/fr_CA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/IT/IT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/IT/i18n/it_IT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/NL/NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/NL/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/US/US.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/nations/US/i18n/en_US.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/Americas.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/en_CA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/en_US.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_AR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_BO.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_BR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_BZ.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_CL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_CO.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_CR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_CU.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_DO.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_EC.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_GT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_HN.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_MX.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_NI.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_PA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_PE.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_PR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_PY.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/es_SV.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/fr_CA.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Americas/i18n/pt_BR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Asia/Asia.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Asia/i18n/ja_JP.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Asia/i18n/zh_Hans_CN.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/Europe.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/bg_BG.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/cs_CZ.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/da_DK.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/de_AT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/de_BE.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/de_CH.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/de_DE.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/de_LI.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/de_LU.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/el_CY.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/el_GR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/es_ES.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/et_EE.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/fi_FI.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/fr_BE.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/fr_FR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/ga_IE.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/hr_HR.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/hu_HU.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/it_CH.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/it_IT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/lb_LU.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/lt_LT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/lv_LV.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/mt_MT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/nl_NL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/pl_PL.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/pt_PT.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/ro_RO.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/sk_SK.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/sl_SI.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/sv_SE.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/calendars/wider_regions/Europe/i18n/tr_CY.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/decrees.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/de.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/en.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/es.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/fr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/hr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/id.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/it.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/la.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/nl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/pl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/pt.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/decrees/i18n/sk.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/de.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/en.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/es.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/fr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/hr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/id.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/it.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/la.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/nl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/pl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/pt.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/i18n/sk.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_1970/propriumdesanctis_1970.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/de.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/en.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/es.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/fr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/hr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/id.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/it.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/la.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/nl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/pl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/pt.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/i18n/sk.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2002/propriumdesanctis_2002.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/de.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/en.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/es.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/fr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/hr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/id.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/it.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/la.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/nl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/pl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/pt.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/i18n/sk.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_2008/propriumdesanctis_2008.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_IT_1983/propriumdesanctis_IT_1983.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdesanctis_US_2011/propriumdesanctis_US_2011.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/de.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/en.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/es.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/fr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/hr.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/id.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/it.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/la.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/nl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/pl.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/pt.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/i18n/sk.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/sourcedata/missals/propriumdetempore/propriumdetempore.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/world_dioceses.json", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/0354-0354,_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_Kalendarium_Anno_CCCLIV_Conscriptum,_MLT.pdf", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/1570-07-19,_SS_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>um,_LT.pdf", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/Brief history of the development of the English Roman Missal.pdf", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/GNLY.pdf", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/parseOpenAPI.js", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/phpcs.xml", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/redocly.yaml", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Core.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/DateTime.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/AcceptHeader.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/Ascension.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/CacheDuration.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/CalEventAction.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/CorpusChristi.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/Epiphany.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/ICSErrorLevel.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/JsonData.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/LitColor.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/LitCommon.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/LitFeastType.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/LitGrade.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/LitLocale.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/LitSchema.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/LitSeason.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/RequestContentType.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/RequestMethod.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/ReturnType.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/RomanMissal.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/Route.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/StatusCode.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Enum/YearType.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Festivity.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/FestivityCollection.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Health.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/LatinUtils.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/LitTest.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/CalendarParams.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/DecreesParams.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/EventsParams.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/MissalsParams.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Calendar.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Decrees.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Easter.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Events.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Metadata.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Missals.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Schemas.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Tests.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Router.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Utilities.php", "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/pgettext.php"], "skipped": [{"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/tests/MaryMotherChurchTest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/tests/NativityJohnBaptistTest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/tests/PrayerUnbornTest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/tests/StJaneFrancesDeChantalTest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/tests/rotter_nl_HLaurentiusdiakenenmartelaarpatroonvanhetbisdomTest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/ICEL_Roman_Missal_Volume_1A.pdf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/Liber_sacramentorum_Romanae_Ecclesiae.pdf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/MESSALE ROMANO - terza edizione in lingua italiana.pdf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/MISSAL-CP-ENGLISH.pdf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/Martirologio-Romano.pdf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/Martyrologium Romanum (2004)_compressed.pdf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/Missale Romanum Editio Typica Tertia 2002.pdf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/EventsParams.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Metadata.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.7611639499664307, "profiling_times": {"config_time": 6.0888512134552, "core_time": 7.5087878704071045, "ignores_time": 0.001634836196899414, "total_time": 13.600058794021606}, "parsing_time": {"total_time": 4.427253246307373, "per_file_time": {"mean": 0.01860190439624946, "std_dev": 0.006978966156036617}, "very_slow_stats": {"time_ratio": 0.2581241047697898, "count_ratio": 0.004201680672268907}, "very_slow_files": [{"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Calendar.php", "ftime": 1.1427807807922363}]}, "scanning_time": {"total_time": 22.272427082061768, "per_file_time": {"mean": 0.027980436032740894, "std_dev": 0.05076788851417487}, "very_slow_stats": {"time_ratio": 0.37414705601198406, "count_ratio": 0.002512562814070352}, "very_slow_files": [{"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/literature/1570-07-19,_SS_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>um,_LT.pdf", "ftime": 3.9574060440063477}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Calendar.php", "ftime": 4.3757569789886475}]}, "matching_time": {"total_time": 7.0841076374053955, "per_file_and_rule_time": {"mean": 0.008324450807761926, "std_dev": 0.0013887644076662764}, "very_slow_stats": {"time_ratio": 0.5490202723725967, "count_ratio": 0.023501762632197415}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/jsondata/world_dioceses.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.14068388938903809}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Missals.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.14145183563232422}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Calendar.php", "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.16012787818908691}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.16835808753967285}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Params/RegionalDataParams.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.1957850456237793}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/RegionalData.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.2214040756225586}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Calendar.php", "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.23518705368041992}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Events.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.2777681350708008}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Calendar.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.4920928478240967}, {"fpath": "downloaded_repos/Liturgical-Calendar_LiturgicalCalendarAPI/src/Paths/Calendar.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.6371610164642334}]}, "tainting_time": {"total_time": 2.141622543334961, "per_def_and_rule_time": {"mean": 0.0012694857992501252, "std_dev": 3.4351606518683683e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}