{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/openstax_openstax-cms/Dockerfile", "start": {"line": 21, "col": 1, "offset": 389}, "end": {"line": 21, "col": 52, "offset": 440}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"/usr/local/cms-app/docker/entrypoint\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/openstax_openstax-cms/Dockerfile", "start": {"line": 25, "col": 1, "offset": 495}, "end": {"line": 25, "col": 36, "offset": 530}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD /usr/local/cms-app/docker/start", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/openstax_openstax-cms/api/tests.py", "start": {"line": 35, "col": 25, "offset": 1089}, "end": {"line": 35, "col": 72, "offset": 1136}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/openstax_openstax-cms/api/tests.py", "start": {"line": 43, "col": 25, "offset": 1487}, "end": {"line": 43, "col": 72, "offset": 1534}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/openstax_openstax-cms/api/tests.py", "start": {"line": 56, "col": 25, "offset": 2006}, "end": {"line": 56, "col": 72, "offset": 2053}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/openstax_openstax-cms/books/views.py", "start": {"line": 13, "col": 1, "offset": 387}, "end": {"line": 19, "col": 48, "offset": 616}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/openstax_openstax-cms/books/views.py", "start": {"line": 22, "col": 1, "offset": 619}, "end": {"line": 28, "col": 46, "offset": 861}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/openstax_openstax-cms/docker-compose.override.yml", "start": {"line": 16, "col": 3, "offset": 225}, "end": {"line": 16, "col": 11, "offset": 233}, "extra": {"message": "Service 'postgres' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/openstax_openstax-cms/docker-compose.override.yml", "start": {"line": 16, "col": 3, "offset": 225}, "end": {"line": 16, "col": 11, "offset": 233}, "extra": {"message": "Service 'postgres' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/openstax_openstax-cms/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 27}, "end": {"line": 3, "col": 6, "offset": 30}, "extra": {"message": "Service 'app' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/openstax_openstax-cms/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 27}, "end": {"line": 3, "col": 6, "offset": 30}, "extra": {"message": "Service 'app' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.formathtml-fstring-parameter.formathtml-fstring-parameter", "path": "downloaded_repos/openstax_openstax-cms/errata/models.py", "start": {"line": 214, "col": 20, "offset": 7953}, "end": {"line": 214, "col": 170, "offset": 8103}, "extra": {"message": "Passing a formatted string as first parameter to `format_html` disables the proper encoding of variables. Any HTML in the first parameter is not encoded. Using a formatted string as first parameter obscures which parameters are encoded. Correct use of `format_html` is passing a static format string as first parameter, and the variables to substitute as subsequent parameters.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.2/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.formathtml-fstring-parameter.formathtml-fstring-parameter", "shortlink": "https://sg.run/lxQo"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/openstax_openstax-cms/errata/templates/templates/email.html", "start": {"line": 173, "col": 10, "offset": 6686}, "end": {"line": 173, "col": 436, "offset": 7112}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/openstax_openstax-cms/errata/templates/templates/email.html", "start": {"line": 250, "col": 12, "offset": 11481}, "end": {"line": 250, "col": 342, "offset": 11811}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/openstax_openstax-cms/news/views.py", "start": {"line": 7, "col": 1, "offset": 194}, "end": {"line": 10, "col": 66, "offset": 335}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/openstax_openstax-cms/news/views.py", "start": {"line": 12, "col": 1, "offset": 337}, "end": {"line": 18, "col": 53, "offset": 600}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/openstax_openstax-cms/news/views.py", "start": {"line": 20, "col": 1, "offset": 602}, "end": {"line": 23, "col": 66, "offset": 745}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/openstax_openstax-cms/news/views.py", "start": {"line": 25, "col": 1, "offset": 747}, "end": {"line": 28, "col": 66, "offset": 905}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/openstax_openstax-cms/openstax/middleware.py", "start": {"line": 87, "col": 32, "offset": 3726}, "end": {"line": 87, "col": 54, "offset": 3748}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "path": "downloaded_repos/openstax_openstax-cms/openstax/settings/base.py", "start": {"line": 277, "col": 1, "offset": 8923}, "end": {"line": 277, "col": 15, "offset": 8937}, "extra": {"message": "Django REST framework configuration is missing default rate- limiting options. This could inadvertently allow resource starvation or Denial of Service (DoS) attacks. Add 'DEFAULT_THROTTLE_CLASSES' and 'DEFAULT_THROTTLE_RATES' to add rate-limiting to your application.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-770: Allocation of Resources Without Limits or Throttling"], "references": ["https://www.django-rest-framework.org/api-guide/throttling/#setting-the-throttling-policy"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "shortlink": "https://sg.run/vzBY"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "start": {"line": 11, "col": 5, "offset": 276}, "end": {"line": 11, "col": 91, "offset": 362}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/openstax_openstax-cms/redirects/templates/wagtailredirects/add.html", "start": {"line": 18, "col": 5, "offset": 552}, "end": {"line": 30, "col": 12, "offset": 992}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "path": "downloaded_repos/openstax_openstax-cms/salesforce/tests.py", "start": {"line": 90, "col": 5, "offset": 3171}, "end": {"line": 93, "col": 70, "offset": 3519}, "extra": {"message": "Hardcoded password is used as a default argument to 'create_salesforce_setting'. This could be dangerous if a real password is not supplied.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["python"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "shortlink": "https://sg.run/Lw9r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/openstax_openstax-cms/versions/views.py", "start": {"line": 10, "col": 14, "offset": 186}, "end": {"line": 10, "col": 73, "offset": 245}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/export-page.html", "start": {"line": 3, "col": 21, "offset": 75}, "end": {"line": 3, "col": 37, "offset": 91}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/import-page.html", "start": {"line": 3, "col": 21, "offset": 75}, "end": {"line": 3, "col": 37, "offset": 91}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/index.html", "start": {"line": 3, "col": 21, "offset": 75}, "end": {"line": 3, "col": 37, "offset": 91}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/index.html:1:\n Failure: not a program", "path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/index.html"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openstax_openstax-cms/pages/templates/news/news_article.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 57, "offset": 111}}]], "message": "Syntax error at line downloaded_repos/openstax_openstax-cms/pages/templates/news/news_article.html:1:\n `{% load wagtailimages_tags wagtailcore_tags static %}\n\n{% image page.promote_image original as promote_image %}` was unexpected", "path": "downloaded_repos/openstax_openstax-cms/pages/templates/news/news_article.html", "spans": [{"file": "downloaded_repos/openstax_openstax-cms/pages/templates/news/news_article.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 57, "offset": 111}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openstax_openstax-cms/pages/templates/page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 57, "offset": 111}}]], "message": "Syntax error at line downloaded_repos/openstax_openstax-cms/pages/templates/page.html:1:\n `{% load wagtailimages_tags wagtailcore_tags static %}\n\n{% image page.promote_image original as promote_image %}` was unexpected", "path": "downloaded_repos/openstax_openstax-cms/pages/templates/page.html", "spans": [{"file": "downloaded_repos/openstax_openstax-cms/pages/templates/page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 57, "offset": 111}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 26, "offset": 143}}, {"path": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 10, "col": 29, "offset": 44}}, {"path": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html:1:\n `{% extends \"wagtailadmin/admin_base.html\" %}\n{% load static %}\n\n{% block branding_title %}OpenStax CMS{% endblock %}\n\n{% block branding_logo %}` was unexpected", "path": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "spans": [{"file": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 26, "offset": 143}}, {"file": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 10, "col": 29, "offset": 44}}, {"file": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/openstax_openstax-cms/.github/dependabot.yml", "downloaded_repos/openstax_openstax-cms/.github/workflows/tests.yml", "downloaded_repos/openstax_openstax-cms/.gitignore", "downloaded_repos/openstax_openstax-cms/.python-version", "downloaded_repos/openstax_openstax-cms/CONTRIBUTING.md", "downloaded_repos/openstax_openstax-cms/Dockerfile", "downloaded_repos/openstax_openstax-cms/LICENSE", "downloaded_repos/openstax_openstax-cms/README.md", "downloaded_repos/openstax_openstax-cms/__init__.py", "downloaded_repos/openstax_openstax-cms/accounts/__init__.py", "downloaded_repos/openstax_openstax-cms/accounts/tests.py", "downloaded_repos/openstax_openstax-cms/accounts/urls.py", "downloaded_repos/openstax_openstax-cms/accounts/views.py", "downloaded_repos/openstax_openstax-cms/api/__init__.py", "downloaded_repos/openstax_openstax-cms/api/admin.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0002_customizationrequest.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0003_auto_20200923_1328.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0004_customizationrequest_complete.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0005_customizationrequest_created.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0006_customizationrequest_book.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0007_featureflag.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0008_alter_featureflag_name.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0009_auto_20220203_1256.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0010_alter_featureflag_name.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0011_webviewsettings.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0012_alter_webviewsettings_options.py", "downloaded_repos/openstax_openstax-cms/api/migrations/0013_delete_progresstracker.py", "downloaded_repos/openstax_openstax-cms/api/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/api/models.py", "downloaded_repos/openstax_openstax-cms/api/serializers.py", "downloaded_repos/openstax_openstax-cms/api/signals.py", "downloaded_repos/openstax_openstax-cms/api/tests.py", "downloaded_repos/openstax_openstax-cms/api/urls.py", "downloaded_repos/openstax_openstax-cms/api/views.py", "downloaded_repos/openstax_openstax-cms/books/__init__.py", "downloaded_repos/openstax_openstax-cms/books/constants.py", "downloaded_repos/openstax_openstax-cms/books/management/commands/add_missing_licenses.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0001_initial_squashed_0100_videofacultyresources.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0002_auto_20200720_1527.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0101_auto_20200716_1553.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0102_auto_20200716_1605.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0103_auto_20200722_1544.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0103_merge_20200723_1300.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0104_book_give_today.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0105_merge_20200727_1405.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0106_auto_20200728_1547.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0107_auto_20200813_1613.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0108_auto_20200910_1245.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0109_customizationrequest.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0109_remove_book_give_today.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0110_customizationform.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0111_delete_customizationform.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0112_delete_customizationrequest.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0113_merge_20200930_1001.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0114_book_customization_form_content.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0115_auto_20200930_1205.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0116_auto_20200930_1237.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0117_auto_20201002_1301.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0118_auto_20201014_1249.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0119_auto_20201014_1251.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0120_auto_20201016_1253.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0121_auto_20201020_1707.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0122_auto_20201021_1050.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0123_book_support_statement.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0124_auto_20201021_1100.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0125_remove_book_table_of_contents.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0126_auto_20210127_1042.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0127_remove_book_ally_content.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0128_remove_book_coming_soon.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0129_auto_20210709_1624.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0130_remove_book_errata_content.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0131_book_translations.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0132_bookindex_translations.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0133_bookcategories_bookcategory.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0134_alter_book_license_name.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0135_alter_book_license_name.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0136_auto_20220915_0950.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0137_alter_book_salesforce_book_id.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0138_alter_book_authors_alter_book_bookstore_content_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0138_auto_20230112_1426.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0139_auto_20230112_1426.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0140_merge_20230118_1543.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0141_remove_book_k12_subject_studentresources_k12.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0142_remove_book_digital_isbn_10_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0143_remove_studentresources_k12_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0144_book_assignable_book_book_assignable_book_link.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0145_remove_book_assignable_book_link.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0146_alter_facultyresources_link_external_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0147_alter_facultyresources_link_external_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0148_book_book_uuid_alter_book_cnx_id.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0149_alter_book_book_state.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0150_book_amazon_iframe.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0151_book_promote_snippet_promotesnippets.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0152_alter_book_promote_snippet_delete_promotesnippets.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0153_book_polish_site_link.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0154_alter_book_salesforce_abbreviation_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0155_alter_book_book_cover_text_color_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0156_book_content_warning.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0157_alter_book_print_isbn_13_and_more.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0158_alter_book_cover_color.py", "downloaded_repos/openstax_openstax-cms/books/migrations/0159_book_assignable_isbn_13.py", "downloaded_repos/openstax_openstax-cms/books/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/books/models.py", "downloaded_repos/openstax_openstax-cms/books/serializers.py", "downloaded_repos/openstax_openstax-cms/books/tests.py", "downloaded_repos/openstax_openstax-cms/books/urls.py", "downloaded_repos/openstax_openstax-cms/books/views.py", "downloaded_repos/openstax_openstax-cms/codecov.yml", "downloaded_repos/openstax_openstax-cms/docker/bash", "downloaded_repos/openstax_openstax-cms/docker/entrypoint", "downloaded_repos/openstax_openstax-cms/docker/start", "downloaded_repos/openstax_openstax-cms/docker-compose.override.yml", "downloaded_repos/openstax_openstax-cms/docker-compose.yml", "downloaded_repos/openstax_openstax-cms/docs/app-design.md", "downloaded_repos/openstax_openstax-cms/donations/__init__.py", "downloaded_repos/openstax_openstax-cms/donations/admin.py", "downloaded_repos/openstax_openstax-cms/donations/apps.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0002_donationpopup.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0003_auto_20210607_1320.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0004_auto_20210609_1344.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0005_auto_20211021_1045.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0006_fundraiser.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0007_auto_20220516_2113.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0008_thankyounote_source.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0009_alter_thankyounote_options.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0009_thankyounote_salesforce_id.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/0010_merge_20250110_1518.py", "downloaded_repos/openstax_openstax-cms/donations/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/donations/models.py", "downloaded_repos/openstax_openstax-cms/donations/serializers.py", "downloaded_repos/openstax_openstax-cms/donations/signals.py", "downloaded_repos/openstax_openstax-cms/donations/tests.py", "downloaded_repos/openstax_openstax-cms/donations/urls.py", "downloaded_repos/openstax_openstax-cms/donations/views.py", "downloaded_repos/openstax_openstax-cms/errata/__init__.py", "downloaded_repos/openstax_openstax-cms/errata/admin.py", "downloaded_repos/openstax_openstax-cms/errata/forms.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0002_auto_20161121_1453.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0003_errata_archived.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0004_auto_20161206_1115.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0005_errata_supporting_documentation.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0006_remove_errata_supporting_documentation.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0007_externaldocumentation_internaldocumentation.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0008_auto_20161212_1309.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0009_auto_20170124_1338.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0010_auto_20170126_1218.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0011_auto_20170131_1201.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0012_auto_20170202_1423.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0013_auto_20170203_0955.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0014_auto_20170203_1420.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0015_auto_20170206_1125.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0016_errata_is_assessment_errata.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0017_auto_20170419_1228.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0018_auto_20170919_1140.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0019_auto_20171019_1158.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0020_errata_duplicate_id.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0021_auto_20180125_1128.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0022_auto_20180205_1505.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0023_auto_20180222_1002.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0024_errata_assessment_id.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0025_auto_20180301_1107.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0026_auto_20180308_0943.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0027_errata_number_of_errors.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0028_auto_20180830_1220.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0029_auto_20181002_1052.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0030_auto_20181130_1235.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0031_blockedusers.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0032_auto_20190627_1548.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0033_auto_20190701_1154.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0034_auto_20190730_1153.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0035_emailtext.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0036_errata_junk.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0037_auto_20191210_1419.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0038_auto_20200127_1318.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0039_auto_20200213_1248.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0039_auto_20200213_1418.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0040_merge_20200214_0945.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0041_auto_20200326_1531.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0042_auto_20200401_1500.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0043_auto_20200414_1517.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0044_auto_20200714_1429.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0045_auto_20200721_0959.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0046_auto_20200722_1225.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0047_auto_20200804_1200.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0048_auto_20200807_1056.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0049_alter_errata_status.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0050_auto_20220207_1057.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0051_auto_20220516_2113.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0052_alter_errata_status.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0053_alter_errata_resource.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0054_alter_errata_resource.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0055_remove_errata_accounts_user_email_and_more.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/0056_alter_errata_options.py", "downloaded_repos/openstax_openstax-cms/errata/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/errata/models.py", "downloaded_repos/openstax_openstax-cms/errata/serializers.py", "downloaded_repos/openstax_openstax-cms/errata/static/errata/errata-admin-ui.js", "downloaded_repos/openstax_openstax-cms/errata/templates/templates/email.html", "downloaded_repos/openstax_openstax-cms/errata/templates/templates/email.txt", "downloaded_repos/openstax_openstax-cms/errata/tests.py", "downloaded_repos/openstax_openstax-cms/errata/urls.py", "downloaded_repos/openstax_openstax-cms/errata/views.py", "downloaded_repos/openstax_openstax-cms/errata/wagtail_hooks.py", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/README.md", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/books_license.yaml", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/books_no_cnx_id.yaml", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/books_prealgebra.yaml", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/books_univ_physics.yaml", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/contact.yaml", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/partners.yaml", "downloaded_repos/openstax_openstax-cms/fixtures/vcr_cassettes/retired_book.yaml", "downloaded_repos/openstax_openstax-cms/global_settings/__init__.py", "downloaded_repos/openstax_openstax-cms/global_settings/apps.py", "downloaded_repos/openstax_openstax-cms/global_settings/functions.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0002_auto_20160906_1344.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0003_auto_20160906_1348.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0004_auto_20160906_1403.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0005_remove_stickynote_header.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0006_auto_20161213_1054.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0007_footer.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0008_auto_20170822_0956.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0009_auto_20170912_1337.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0010_auto_20170914_1600.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0011_mail.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0012_auto_20171009_1008.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0013_auto_20180515_1603.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0014_auto_20180607_1351.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0015_auto_20180816_1056.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0016_cloudfrontdistribution.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0017_auto_20200325_1058.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0018_auto_20200727_1047.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0019_stickynote_show_popup.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0020_givetoday.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/0021_auto_20201002_1012.py", "downloaded_repos/openstax_openstax-cms/global_settings/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/global_settings/models.py", "downloaded_repos/openstax_openstax-cms/global_settings/signals.py", "downloaded_repos/openstax_openstax-cms/global_settings/views.py", "downloaded_repos/openstax_openstax-cms/global_settings/wagtail_hooks.py", "downloaded_repos/openstax_openstax-cms/mail/__init__.py", "downloaded_repos/openstax_openstax-cms/mail/admin.py", "downloaded_repos/openstax_openstax-cms/mail/functions.py", "downloaded_repos/openstax_openstax-cms/mail/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/mail/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/mail/models.py", "downloaded_repos/openstax_openstax-cms/mail/tests.py", "downloaded_repos/openstax_openstax-cms/manage.py", "downloaded_repos/openstax_openstax-cms/news/__init__.py", "downloaded_repos/openstax_openstax-cms/news/feeds.py", "downloaded_repos/openstax_openstax-cms/news/fields.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0002_newsarticle_tags.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0003_auto_20160517_1521.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0004_newsarticle_press_kit.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0005_auto_20160523_1023.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0006_auto_20160523_1042.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0007_auto_20160523_1049.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0008_newsarticle_author.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0009_auto_20160614_1024.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0010_auto_20160614_1032.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0011_auto_20160614_1034.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0012_auto_20160614_1037.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0013_auto_20160616_1033.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0014_auto_20160617_1140.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0015_remove_newsarticle_heading.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0016_newsarticle_heading.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0017_auto_20161018_0757.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0018_auto_20161025_1300.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0019_experts_expertspr_pressindex_pressrelease.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0020_auto_20180509_1016.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0021_auto_20180515_1603.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0022_auto_20180515_1612.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0023_missionstatement_missionstatements.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0024_auto_20180517_1102.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0025_auto_20180517_1426.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0026_auto_20180517_1434.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0027_auto_20180517_1443.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0028_remove_pressindex_mentions.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0029_pressindex_mentions.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0030_auto_20180521_1137.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0031_auto_20180523_0934.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0032_auto_20180705_1947.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0033_auto_20181113_1249.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0034_auto_20191008_1546.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0035_auto_20210226_1150.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0036_blogtype_contenttype.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0037_newsarticle_collections.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0038_auto_20220420_1547.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0039_auto_20220427_1107.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0040_alter_newsarticle_content_types.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0041_alter_newsarticletag_tag.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0041_auto_20220517_1353.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0042_auto_20220520_0855.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0042_merge_20220519_1249.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0043_alter_newsarticle_featured_video.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0044_auto_20220520_1158.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0045_merge_20220520_1523.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0046_auto_20220616_1349.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0047_alter_newsarticle_article_subjects_and_more.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0048_pressindex_faqs_pressindex_infographic_image_and_more.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0049_pressindex_about_alter_pressindex_mentions.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0050_newsindex_display_footer_and_more.py", "downloaded_repos/openstax_openstax-cms/news/migrations/0051_alter_pressindex_faqs.py", "downloaded_repos/openstax_openstax-cms/news/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/news/models.py", "downloaded_repos/openstax_openstax-cms/news/search.py", "downloaded_repos/openstax_openstax-cms/news/tests.py", "downloaded_repos/openstax_openstax-cms/news/urls.py", "downloaded_repos/openstax_openstax-cms/news/views.py", "downloaded_repos/openstax_openstax-cms/openstax/__init__.py", "downloaded_repos/openstax_openstax-cms/openstax/api.py", "downloaded_repos/openstax_openstax-cms/openstax/custom_storages.py", "downloaded_repos/openstax_openstax-cms/openstax/functions.py", "downloaded_repos/openstax_openstax-cms/openstax/middleware.py", "downloaded_repos/openstax_openstax-cms/openstax/settings/__init__.py", "downloaded_repos/openstax_openstax-cms/openstax/settings/base.py", "downloaded_repos/openstax_openstax-cms/openstax/settings/docker.py", "downloaded_repos/openstax_openstax-cms/openstax/settings/local.py.example", "downloaded_repos/openstax_openstax-cms/openstax/settings/test.py", "downloaded_repos/openstax_openstax-cms/openstax/tests.py", "downloaded_repos/openstax_openstax-cms/openstax/urls.py", "downloaded_repos/openstax_openstax-cms/openstax/wsgi.py", "downloaded_repos/openstax_openstax-cms/oxauth/__init__.py", "downloaded_repos/openstax_openstax-cms/oxauth/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/oxauth/migrations/0002_create_user_profile.py", "downloaded_repos/openstax_openstax-cms/oxauth/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/oxauth/models.py", "downloaded_repos/openstax_openstax-cms/oxmenus/__init__.py", "downloaded_repos/openstax_openstax-cms/oxmenus/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/oxmenus/migrations/0002_alter_menus_options_alter_menus_menu.py", "downloaded_repos/openstax_openstax-cms/oxmenus/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/oxmenus/models.py", "downloaded_repos/openstax_openstax-cms/oxmenus/serializers.py", "downloaded_repos/openstax_openstax-cms/oxmenus/signals.py", "downloaded_repos/openstax_openstax-cms/oxmenus/tests.py", "downloaded_repos/openstax_openstax-cms/oxmenus/urls.py", "downloaded_repos/openstax_openstax-cms/oxmenus/views.py", "downloaded_repos/openstax_openstax-cms/oxmenus/wagtail_hooks.py", "downloaded_repos/openstax_openstax-cms/pages/__init__.py", "downloaded_repos/openstax_openstax-cms/pages/custom_blocks.py", "downloaded_repos/openstax_openstax-cms/pages/custom_fields.py", "downloaded_repos/openstax_openstax-cms/pages/management/__init__.py", "downloaded_repos/openstax_openstax-cms/pages/management/commands/__init__.py", "downloaded_repos/openstax_openstax-cms/pages/management/commands/create_subject_pages.py", "downloaded_repos/openstax_openstax-cms/pages/management/commands/create_subjects_page.py", "downloaded_repos/openstax_openstax-cms/pages/management/commands/restructure_root_page.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0001_initial_squashed_0232_auto_20200623_0848.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0002_auto_20200813_1540.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0002_auto_20200928_1405.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0003_merge_20200930_1001.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0004_delete_roverpage.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0004_llphpage.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0005_auto_20201105_1414.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0006_auto_20201105_1620.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0007_merge_20201106_1444.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0008_tutormarketing.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0009_auto_20201204_1518.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0010_auto_20201204_1520.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0011_auto_20201204_1524.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0012_auto_20201204_1525.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0013_auto_20201204_1532.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0014_auto_20201207_1355.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0015_delete_card.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0016_auto_20201209_0842.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0017_tutorlanding.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0018_auto_20210126_0947.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0019_auto_20210126_1056.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0020_auto_20210126_1314.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0021_auto_20210126_1318.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0022_auto_20210126_1323.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0023_auto_20210126_1333.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0024_auto_20210126_1336.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0025_auto_20210126_1519.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0026_auto_20210126_1521.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0027_auto_20210126_1607.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0028_auto_20210127_1109.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0029_auto_20210127_1300.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0030_auto_20210127_1302.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0031_auto_20210127_1304.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0032_auto_20210127_1347.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0033_auto_20210127_1356.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0034_auto_20210127_1550.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0035_auto_20210127_1612.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0036_auto_20210127_1731.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0037_auto_20210426_1315.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0038_auto_20210427_1025.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0039_auto_20210427_1415.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0040_auto_20210505_1339.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0041_auto_20210506_1329.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0042_auto_20210506_1347.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0043_auto_20210506_1453.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0044_auto_20210513_1515.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0045_auto_20210517_0957.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0046_auto_20210628_1632.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0047_institutionalpartnership_application_link.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0048_auto_20210830_1133.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0049_auto_20211215_1046.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0050_auto_20211216_1220.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0051_auto_20220207_1342.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0051_subjects.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0052_alter_supporters_funder_groups.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0052_subject_subjectorderable.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0053_alter_supporters_funder_groups.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0053_subject_page_description.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0054_alter_supporters_funder_groups.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0055_supporters_disclaimer.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0056_merge_20220208_1013.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0057_merge_20220223_1247.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0058_alter_subject_tutor_ad.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0059_alter_supporters_funder_groups.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0060_auto_20220412_1344.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0061_alter_impactstory_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0062_alter_impactstory_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0063_auto_20220614_1525.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0064_formheadings.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0065_auto_20220823_1112.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0066_allylogos.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0067_alter_aboutuspage_what_cards_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0067_learningresearchpage.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0068_k12mainpage_k12subject.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0069_merge_20230118_1543.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0069_remove_learningresearchpage_mission_header_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0070_alter_k12mainpage_faqs_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0071_merge_20230202_1030.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0072_k12mainpage_subject_list_default_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0073_assignable.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0074_remove_assignable_admin_button_text_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0075_remove_assignable_available_courses_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0076_remove_assignable_heading_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0077_assignable_add_assignable_cta_button_text_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0078_rename_add_assignable_header_assignable_faq_header_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0079_remove_assignable_section_2_image_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0080_homepage_k12_cta_button_text_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0081_remove_allylogos_openstax_logos_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0082_remove_webinarpage_description_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0083_alter_k12mainpage_testimonials.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0084_customizablepage_squashed_0110_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0111_alter_rootpage_body_squashed_0130_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0131_alter_impact_making_a_difference.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0131_alter_impact_making_a_difference_squashed_0144_alter_rootpage_body_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0132_remove_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0133_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0134_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0135_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0136_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0137_alter_rootpage_body_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0137_alter_rootpage_promote_image.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0138_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0139_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0140_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0141_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0142_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0143_alter_rootpage_body_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0144_alter_rootpage_body_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0145_alter_rootpage_body_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0146_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0147_merge_20240725_1221.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0148_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0149_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0150_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0151_alter_assignable_faqs_alter_faq_questions_and_more.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0152_alter_rootpage_body_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0153_alter_homepage_options.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0154_alter_impact_improving_access.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0155_alter_rootpage_layout.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0156_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0157_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0158_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0159_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/0160_alter_rootpage_body.py", "downloaded_repos/openstax_openstax-cms/pages/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/pages/models.py", "downloaded_repos/openstax_openstax-cms/pages/static/images/openstax.png", "downloaded_repos/openstax_openstax-cms/pages/templates/news/news_article.html", "downloaded_repos/openstax_openstax-cms/pages/templates/page.html", "downloaded_repos/openstax_openstax-cms/pages/templates/pages/general_page.html", "downloaded_repos/openstax_openstax-cms/pages/templates/preview.html", "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/base.html", "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailusers/users/edit.html", "downloaded_repos/openstax_openstax-cms/pages/tests.py", "downloaded_repos/openstax_openstax-cms/public/favicon.ico", "downloaded_repos/openstax_openstax-cms/pyproject.toml", "downloaded_repos/openstax_openstax-cms/redirects/__init__.py", "downloaded_repos/openstax_openstax-cms/redirects/management/commands/check_redirects.py", "downloaded_repos/openstax_openstax-cms/redirects/templates/wagtailredirects/add.html", "downloaded_repos/openstax_openstax-cms/requirements/base.txt", "downloaded_repos/openstax_openstax-cms/requirements/dev.txt", "downloaded_repos/openstax_openstax-cms/requirements/production.txt", "downloaded_repos/openstax_openstax-cms/requirements/test.txt", "downloaded_repos/openstax_openstax-cms/salesforce/__init__.py", "downloaded_repos/openstax_openstax-cms/salesforce/admin.py", "downloaded_repos/openstax_openstax-cms/salesforce/functions.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/delete_resource_downloads.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/sync_thank_you_notes.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_opportunities.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_partners.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_resource_downloads.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_schools.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_schools_and_mapbox.py", "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/upload_mapbox_schools.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0002_auto_20160318_0843.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0003_school.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0004_auto_20180522_1342.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0005_auto_20180522_1530.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0006_auto_20180522_1552.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0007_auto_20180522_1557.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0008_mapboxdataset.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0009_salesforcesettings.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0010_auto_20180910_1320.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0011_auto_20190531_0025.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0012_mapboxdataset_style_url.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0013_mapboxdataset_tileset_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0014_remove_mapboxdataset_dataset_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0015_adoptionopportunityrecord.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0016_auto_20190815_1211.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0017_adoptionopportunityrecord_updated.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0018_remove_adoptionopportunityrecord_updated.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0019_adoptionopportunityrecord_updated.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0020_school_salesforce_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0021_partner.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0022_auto_20191111_1220.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0023_auto_20191111_1229.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0024_auto_20191111_1525.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0025_partner_books.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0026_auto_20191115_1123.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0027_auto_20191122_1705.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0028_partner_partner_logo.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0029_partner_visible_on_website.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0030_auto_20191220_1353.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0031_auto_20200109_1048.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0032_partner_partner_website.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0033_partner_partner_list_label.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0034_auto_20200117_1536.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0035_auto_20200117_1619.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0036_partner_formstack_url.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0037_partnercategorymapping_partnerfieldnamemapping_partnertypemapping.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0038_remove_partnertypemapping_salesforce_name.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0039_auto_20200304_0919.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0039_partner_lead_sharing.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0040_partnerfieldnamemapping_hidden.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0041_merge_20200311_1529.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0042_salesforceforms.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0043_salesforceforms_debug.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0044_auto_20200615_1402.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0045_auto_20200707_1042.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0046_auto_20200720_1527.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0047_auto_20200721_1134.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0048_auto_20200721_1256.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0049_resourcedownload_salesforce_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0050_auto_20200724_1333.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0051_auto_20200727_1324.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0052_auto_20200728_1547.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0053_remove_adoptionopportunityrecord_updated.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0054_remove_adoptionopportunityrecord_book.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0055_adoptionopportunityrecord_verified.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0056_auto_20200730_1547.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0057_partner_instructional_level_k12.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0058_auto_20201012_0946.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0058_partnerreview.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0059_auto_20201005_1411.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0060_partnerreview_partner.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0061_merge_20201012_1151.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0062_savingsnumber.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0063_auto_20201014_1229.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0064_auto_20201015_1524.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0065_auto_20201016_1253.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0066_auto_20201021_1627.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0067_auto_20201023_0945.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0068_auto_20201028_1341.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0069_auto_20201028_1350.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0070_partnerreview_approved_review_text.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0071_auto_20201028_1427.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0072_auto_20201028_1434.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0073_remove_partnerreview_approved_review_text.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0074_partnerreview_partner_response_date.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0075_auto_20201117_1412.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0076_auto_20201117_1419.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0077_partnerreview_to_delete.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0078_auto_20201119_1633.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0079_auto_20201130_1600.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0080_resourcedownload_contact_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0081_auto_20210510_1023.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0082_partner_partnership_level.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0083_alter_resourcedownload_contact_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0084_alter_resourcedownload_contact_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0085_partnerreview_user_faculty_status.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0086_alter_partnerreview_user_faculty_status.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0087_school_location.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0088_school_total_school_enrollment.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0089_auto_20211014_1124.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0090_auto_20211014_1201.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0091_auto_20211018_1150.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0092_auto_20211018_1152.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0093_partnerreview_submitted_by_account_uuid.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0094_alter_partnerreview_submitted_by_account_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0095_adoptionopportunityrecord_account_uuid.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0096_salesforceforms_debug_email.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0097_auto_20220328_1130.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0098_partner_equity_rating.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0098_remove_resourcedownload_salesforce_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0099_remove_resourcedownload_number_of_times_accessed.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0100_merge_20220426_0826.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0100_partner_partner_sf_account_id.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0101_merge_20220506_1353.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0102_auto_20220512_0814.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0103_auto_20220516_1045.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0104_alter_partner_visible_on_website.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0105_auto_20221212_1543.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0106_remove_resourcedownload_salesforce__account_b11d37_idx_and_more.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0107_alter_resourcedownload_account_uuid_and_more.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0108_partner_partner_anniversary_date_and_more.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0109_remove_partner_salesforce_created_date.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0110_partner_account_id_partner_partner_status.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0111_alter_partner_partner_sf_account_id_and_more.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0112_remove_adoptionopportunityrecord_fall_student_number_and_more.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/0113_school_created_school_updated.py", "downloaded_repos/openstax_openstax-cms/salesforce/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/salesforce/models.py", "downloaded_repos/openstax_openstax-cms/salesforce/salesforce.py", "downloaded_repos/openstax_openstax-cms/salesforce/serializers.py", "downloaded_repos/openstax_openstax-cms/salesforce/signals.py", "downloaded_repos/openstax_openstax-cms/salesforce/tests.py", "downloaded_repos/openstax_openstax-cms/salesforce/urls.py", "downloaded_repos/openstax_openstax-cms/salesforce/views.py", "downloaded_repos/openstax_openstax-cms/setup.cfg", "downloaded_repos/openstax_openstax-cms/shared/test_utilities.py", "downloaded_repos/openstax_openstax-cms/shared/tests.py", "downloaded_repos/openstax_openstax-cms/snippets/__init__.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0001_initial_squashed_0022_subject_page_content.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0002_auto_20200722_1544.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0003_delete_givetoday.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0004_customizationformcontent.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0005_delete_customizationformcontent.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0006_erratacontent.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0007_erratacontent_heading.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0008_alter_erratacontent_book_state.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0009_alter_erratacontent_book_state.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0010_auto_20210715_1313.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0011_auto_20210715_1317.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0012_auto_20210715_1327.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0013_subjectcategories.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0014_auto_20220126_1603.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0015_rename_subjectcategories_subjectcategory.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0016_subject_icon.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0017_subjectcategory_description.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0018_subject_subject_color.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0019_givebanner.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0020_blogcontenttype.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0020_contentlicense.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0021_blogcollection.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0022_merge_0020_contentlicense_0021_blogcollection.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0023_delete_contentlicense.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0024_k12subject.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0025_facultyresource_icon_studentresource_icon_and_more.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0026_facultyresource_resource_category_and_more.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0027_nowebinarmessage.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0028_webinarcollection.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0029_assignableavailable.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0030_alter_erratacontent_book_state.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0030_amazonbookblurb.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0031_merge_20231101_1313.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0031_merge_20231101_1438.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0032_merge_20231117_0830.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0033_promotesnippet.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0034_delete_assignableavailable.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0035_alter_k12subject_subject_color_and_more.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0036_contentwarning.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0037_pagelayout.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0038_alter_pagelayout_layout.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0039_alter_pagelayout_layout.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0040_remove_pagelayout_background_image.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0041_delete_pagelayout.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0042_alter_amazonbookblurb_locale_and_more.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/0043_alter_k12subject_subject_color_and_more.py", "downloaded_repos/openstax_openstax-cms/snippets/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/snippets/models.py", "downloaded_repos/openstax_openstax-cms/snippets/serializers.py", "downloaded_repos/openstax_openstax-cms/snippets/signals.py", "downloaded_repos/openstax_openstax-cms/snippets/tests.py", "downloaded_repos/openstax_openstax-cms/snippets/urls.py", "downloaded_repos/openstax_openstax-cms/snippets/views.py", "downloaded_repos/openstax_openstax-cms/versions/__init__.py", "downloaded_repos/openstax_openstax-cms/versions/templates/versions.html", "downloaded_repos/openstax_openstax-cms/versions/views.py", "downloaded_repos/openstax_openstax-cms/versions/wagtail_hooks.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/__init__.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/admin_urls.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/apps.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/config.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/exporting.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/forms.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/functions.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/importing.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/export-page.html", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/import-page.html", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/index.html", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/views.py", "downloaded_repos/openstax_openstax-cms/wagtailimportexport/wagtail_hooks.py", "downloaded_repos/openstax_openstax-cms/webinars/__init__.py", "downloaded_repos/openstax_openstax-cms/webinars/migrations/0001_initial.py", "downloaded_repos/openstax_openstax-cms/webinars/migrations/0002_webinar_display_on_tutor_page.py", "downloaded_repos/openstax_openstax-cms/webinars/migrations/0003_webinar_webinar_subjects.py", "downloaded_repos/openstax_openstax-cms/webinars/migrations/0004_alter_webinar_webinar_subjects.py", "downloaded_repos/openstax_openstax-cms/webinars/migrations/0005_webinar_webinar_collections.py", "downloaded_repos/openstax_openstax-cms/webinars/migrations/0006_alter_webinar_spaces_remaining.py", "downloaded_repos/openstax_openstax-cms/webinars/migrations/__init__.py", "downloaded_repos/openstax_openstax-cms/webinars/models.py", "downloaded_repos/openstax_openstax-cms/webinars/search.py", "downloaded_repos/openstax_openstax-cms/webinars/serializers.py", "downloaded_repos/openstax_openstax-cms/webinars/signals.py", "downloaded_repos/openstax_openstax-cms/webinars/tests.py", "downloaded_repos/openstax_openstax-cms/webinars/urls.py", "downloaded_repos/openstax_openstax-cms/webinars/views.py", "downloaded_repos/openstax_openstax-cms/webinars/wagtail_hooks.py"], "skipped": [{"path": "downloaded_repos/openstax_openstax-cms/docs/openstax-cms-db.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openstax_openstax-cms/pages/templates/news/news_article.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstax_openstax-cms/pages/templates/page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstax_openstax-cms/pages/templates/wagtailadmin/admin_base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/templates/wagtailimportexport/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/tests/test_functions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/tests/tests.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8228869438171387, "profiling_times": {"config_time": 6.974085092544556, "core_time": 10.42629098892212, "ignores_time": 0.0022864341735839844, "total_time": 17.403613090515137}, "parsing_time": {"total_time": 3.0851192474365234, "per_file_time": {"mean": 0.004445416783049741, "std_dev": 0.00017871547284879695}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 53.43548846244812, "per_file_time": {"mean": 0.02491164963284292, "std_dev": 0.03412024502213881}, "very_slow_stats": {"time_ratio": 0.31031468765089115, "count_ratio": 0.002331002331002331}, "very_slow_files": [{"fpath": "downloaded_repos/openstax_openstax-cms/pages/tests.py", "ftime": 1.5023860931396484}, {"fpath": "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_partners.py", "ftime": 2.4545109272003174}, {"fpath": "downloaded_repos/openstax_openstax-cms/books/models.py", "ftime": 3.1322789192199707}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0001_initial_squashed_0232_auto_20200623_0848.py", "ftime": 4.700898885726929}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/models.py", "ftime": 4.7917420864105225}]}, "matching_time": {"total_time": 27.038862943649292, "per_file_and_rule_time": {"mean": 0.007780967753568143, "std_dev": 0.002079098031949832}, "very_slow_stats": {"time_ratio": 0.43562176797085733, "count_ratio": 0.010647482014388488}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/openstax_openstax-cms/books/models.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.2873201370239258}, {"fpath": "downloaded_repos/openstax_openstax-cms/books/migrations/0001_initial_squashed_0100_videofacultyresources.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.3591749668121338}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/models.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.37007999420166016}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/models.py", "rule_id": "python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "time": 0.38436293601989746}, {"fpath": "downloaded_repos/openstax_openstax-cms/books/models.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.4821619987487793}, {"fpath": "downloaded_repos/openstax_openstax-cms/books/models.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.6934881210327148}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/models.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.7738959789276123}, {"fpath": "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_partners.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.933459997177124}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0001_initial_squashed_0232_auto_20200623_0848.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 1.2298221588134766}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0001_initial_squashed_0232_auto_20200623_0848.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 1.3442039489746094}]}, "tainting_time": {"total_time": 8.525484323501587, "per_def_and_rule_time": {"mean": 0.0019292790956102269, "std_dev": 0.00021629204517697838}, "very_slow_stats": {"time_ratio": 0.45203445907771284, "count_ratio": 0.006336275175379045}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0151_alter_assignable_faqs_alter_faq_questions_and_more.py", "fline": 10, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.12903618812561035}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0145_alter_rootpage_body_alter_rootpage_layout.py", "fline": 10, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.13344097137451172}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0144_alter_rootpage_body_alter_rootpage_layout.py", "fline": 10, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.13697600364685059}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0152_alter_rootpage_body_alter_rootpage_layout.py", "fline": 10, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.1441340446472168}, {"fpath": "downloaded_repos/openstax_openstax-cms/books/migrations/0001_initial_squashed_0100_videofacultyresources.py", "fline": 13, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.1445009708404541}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0150_alter_rootpage_body.py", "fline": 10, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.15073609352111816}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0143_alter_rootpage_body_alter_rootpage_layout.py", "fline": 10, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.15375304222106934}, {"fpath": "downloaded_repos/openstax_openstax-cms/wagtailimportexport/importing.py", "fline": 22, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21992993354797363}, {"fpath": "downloaded_repos/openstax_openstax-cms/pages/migrations/0001_initial_squashed_0232_auto_20200623_0848.py", "fline": 14, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.45839715003967285}, {"fpath": "downloaded_repos/openstax_openstax-cms/salesforce/management/commands/update_partners.py", "fline": 21, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.6038320064544678}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1098247616}, "engine_requested": "OSS", "skipped_rules": []}