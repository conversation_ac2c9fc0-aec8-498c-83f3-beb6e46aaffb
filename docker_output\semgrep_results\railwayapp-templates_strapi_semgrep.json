{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/railwayapp-templates_strapi/.editorconfig", "downloaded_repos/railwayapp-templates_strapi/.eslintignore", "downloaded_repos/railwayapp-templates_strapi/.eslintrc", "downloaded_repos/railwayapp-templates_strapi/.github/dependabot.yml", "downloaded_repos/railwayapp-templates_strapi/.gitignore", "downloaded_repos/railwayapp-templates_strapi/README.md", "downloaded_repos/railwayapp-templates_strapi/config/admin.js", "downloaded_repos/railwayapp-templates_strapi/config/api.js", "downloaded_repos/railwayapp-templates_strapi/config/database.js", "downloaded_repos/railwayapp-templates_strapi/config/env/development/database.js", "downloaded_repos/railwayapp-templates_strapi/config/env/development/server.js", "downloaded_repos/railwayapp-templates_strapi/config/middlewares.js", "downloaded_repos/railwayapp-templates_strapi/config/plugins.js", "downloaded_repos/railwayapp-templates_strapi/config/server.js", "downloaded_repos/railwayapp-templates_strapi/database/migrations/.gitkeep", "downloaded_repos/railwayapp-templates_strapi/favicon.png", "downloaded_repos/railwayapp-templates_strapi/package.json", "downloaded_repos/railwayapp-templates_strapi/public/robots.txt", "downloaded_repos/railwayapp-templates_strapi/public/uploads/.gitkeep", "downloaded_repos/railwayapp-templates_strapi/railway.json", "downloaded_repos/railwayapp-templates_strapi/src/admin/app.example.js", "downloaded_repos/railwayapp-templates_strapi/src/admin/vite.config.example.js", "downloaded_repos/railwayapp-templates_strapi/src/api/.gitkeep", "downloaded_repos/railwayapp-templates_strapi/src/extensions/.gitkeep", "downloaded_repos/railwayapp-templates_strapi/src/extensions/users-permissions/config/jwt.js", "downloaded_repos/railwayapp-templates_strapi/src/index.js", "downloaded_repos/railwayapp-templates_strapi/yarn.lock"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6638319492340088, "profiling_times": {"config_time": 5.776111364364624, "core_time": 2.61149001121521, "ignores_time": 0.0016918182373046875, "total_time": 8.390640497207642}, "parsing_time": {"total_time": 0.2488722801208496, "per_file_time": {"mean": 0.01659148534138997, "std_dev": 3.531180849424548e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.1179850101470947, "per_file_time": {"mean": 0.016202681306479644, "std_dev": 0.0018286807483975356}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.10297107696533203, "per_file_and_rule_time": {"mean": 0.001608923077583313, "std_dev": 9.051395243764306e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0042858123779296875, "per_def_and_rule_time": {"mean": 0.0010714530944824219, "std_dev": 2.0548585553115117e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}