{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/laravel_pulse/src/Ingests/RedisIngest.php", "start": {"line": 95, "col": 67, "offset": 2354}, "end": {"line": 95, "col": 96, "offset": 2383}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 70, "col": 78, "offset": 1693}, "end": {"line": 70, "col": 81, "offset": 1696}}]], "message": "Syntax error at line downloaded_repos/laravel_pulse/.github/workflows/tests.yml:70:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 70, "col": 78, "offset": 1693}, "end": {"line": 70, "col": 81, "offset": 1696}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 132, "col": 78, "offset": 3413}, "end": {"line": 132, "col": 81, "offset": 3416}}]], "message": "Syntax error at line downloaded_repos/laravel_pulse/.github/workflows/tests.yml:132:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 132, "col": 78, "offset": 3413}, "end": {"line": 132, "col": 81, "offset": 3416}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 192, "col": 78, "offset": 5043}, "end": {"line": 192, "col": 81, "offset": 5046}}]], "message": "Syntax error at line downloaded_repos/laravel_pulse/.github/workflows/tests.yml:192:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 192, "col": 78, "offset": 5043}, "end": {"line": 192, "col": 81, "offset": 5046}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 243, "col": 78, "offset": 6370}, "end": {"line": 243, "col": 81, "offset": 6373}}]], "message": "Syntax error at line downloaded_repos/laravel_pulse/.github/workflows/tests.yml:243:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "start": {"line": 243, "col": 78, "offset": 6370}, "end": {"line": 243, "col": 81, "offset": 6373}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/laravel_pulse/src/Pulse.php", "start": {"line": 131, "col": 54, "offset": 0}, "end": {"line": 131, "col": 57, "offset": 3}}, {"path": "downloaded_repos/laravel_pulse/src/Pulse.php", "start": {"line": 294, "col": 90, "offset": 0}, "end": {"line": 294, "col": 93, "offset": 3}}, {"path": "downloaded_repos/laravel_pulse/src/Pulse.php", "start": {"line": 313, "col": 63, "offset": 0}, "end": {"line": 313, "col": 66, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/laravel_pulse/src/Pulse.php:131:\n `...` was unexpected", "path": "downloaded_repos/laravel_pulse/src/Pulse.php", "spans": [{"file": "downloaded_repos/laravel_pulse/src/Pulse.php", "start": {"line": 131, "col": 54, "offset": 0}, "end": {"line": 131, "col": 57, "offset": 3}}, {"file": "downloaded_repos/laravel_pulse/src/Pulse.php", "start": {"line": 294, "col": 90, "offset": 0}, "end": {"line": 294, "col": 93, "offset": 3}}, {"file": "downloaded_repos/laravel_pulse/src/Pulse.php", "start": {"line": 313, "col": 63, "offset": 0}, "end": {"line": 313, "col": 66, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/laravel_pulse/.editorconfig", "downloaded_repos/laravel_pulse/.gitattributes", "downloaded_repos/laravel_pulse/.github/CODE_OF_CONDUCT.md", "downloaded_repos/laravel_pulse/.github/CONTRIBUTING.md", "downloaded_repos/laravel_pulse/.github/ISSUE_TEMPLATE/1_Bug_report.yml", "downloaded_repos/laravel_pulse/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/laravel_pulse/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/laravel_pulse/.github/SECURITY.md", "downloaded_repos/laravel_pulse/.github/SUPPORT.md", "downloaded_repos/laravel_pulse/.github/workflows/coding-standards.yml", "downloaded_repos/laravel_pulse/.github/workflows/compile-assets.yml", "downloaded_repos/laravel_pulse/.github/workflows/facade.yml", "downloaded_repos/laravel_pulse/.github/workflows/issues.yml", "downloaded_repos/laravel_pulse/.github/workflows/pull-requests.yml", "downloaded_repos/laravel_pulse/.github/workflows/static-analysis.yml", "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "downloaded_repos/laravel_pulse/.github/workflows/update-changelog.yml", "downloaded_repos/laravel_pulse/.gitignore", "downloaded_repos/laravel_pulse/CHANGELOG.md", "downloaded_repos/laravel_pulse/LICENSE.md", "downloaded_repos/laravel_pulse/README.md", "downloaded_repos/laravel_pulse/UPGRADE.md", "downloaded_repos/laravel_pulse/art/logo.svg", "downloaded_repos/laravel_pulse/composer.json", "downloaded_repos/laravel_pulse/config/pulse.php", "downloaded_repos/laravel_pulse/database/migrations/2023_06_07_000001_create_pulse_tables.php", "downloaded_repos/laravel_pulse/package-lock.json", "downloaded_repos/laravel_pulse/package.json", "downloaded_repos/laravel_pulse/phpstan.neon.dist", "downloaded_repos/laravel_pulse/phpunit.xml.dist", "downloaded_repos/laravel_pulse/postcss.config.js", "downloaded_repos/laravel_pulse/resources/css/pulse.css", "downloaded_repos/laravel_pulse/resources/js/pulse.js", "downloaded_repos/laravel_pulse/resources/views/components/card-header.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/card.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/http-method-badge.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/arrow-trending-up.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/arrows-left-right.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/bug-ant.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/circle-stack.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/clipboard.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/clock.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/cloud-arrow-up.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/command-line.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/computer-desktop.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/cursor-arrow-rays.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/ellipsis-horizontal.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/information-circle.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/moon.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/no-pulse.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/queue-list.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/rocket-launch.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/scale.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/server.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/signal-slash.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/sparkles.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/icons/sun.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/no-results.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/placeholder.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/pulse.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/scroll.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/select.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/servers-placeholder.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/table.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/td.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/th.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/thead.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/theme-switcher.blade.php", "downloaded_repos/laravel_pulse/resources/views/components/user-card.blade.php", "downloaded_repos/laravel_pulse/resources/views/dashboard.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/cache.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/exceptions.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/period-selector.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/queues.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/servers.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/slow-jobs.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/slow-outgoing-requests.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/slow-queries.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/slow-requests.blade.php", "downloaded_repos/laravel_pulse/resources/views/livewire/usage.blade.php", "downloaded_repos/laravel_pulse/src/Commands/CheckCommand.php", "downloaded_repos/laravel_pulse/src/Commands/ClearCommand.php", "downloaded_repos/laravel_pulse/src/Commands/RestartCommand.php", "downloaded_repos/laravel_pulse/src/Commands/WorkCommand.php", "downloaded_repos/laravel_pulse/src/Concerns/ConfiguresAfterResolving.php", "downloaded_repos/laravel_pulse/src/Contracts/Ingest.php", "downloaded_repos/laravel_pulse/src/Contracts/ResolvesUsers.php", "downloaded_repos/laravel_pulse/src/Contracts/Storage.php", "downloaded_repos/laravel_pulse/src/Entry.php", "downloaded_repos/laravel_pulse/src/Events/ExceptionReported.php", "downloaded_repos/laravel_pulse/src/Events/IsolatedBeat.php", "downloaded_repos/laravel_pulse/src/Events/SharedBeat.php", "downloaded_repos/laravel_pulse/src/Facades/Pulse.php", "downloaded_repos/laravel_pulse/src/Http/Middleware/Authorize.php", "downloaded_repos/laravel_pulse/src/Ingests/NullIngest.php", "downloaded_repos/laravel_pulse/src/Ingests/RedisIngest.php", "downloaded_repos/laravel_pulse/src/Ingests/StorageIngest.php", "downloaded_repos/laravel_pulse/src/LegacyUsers.php", "downloaded_repos/laravel_pulse/src/Livewire/Cache.php", "downloaded_repos/laravel_pulse/src/Livewire/Card.php", "downloaded_repos/laravel_pulse/src/Livewire/Concerns/HasPeriod.php", "downloaded_repos/laravel_pulse/src/Livewire/Concerns/RemembersQueries.php", "downloaded_repos/laravel_pulse/src/Livewire/Exceptions.php", "downloaded_repos/laravel_pulse/src/Livewire/PeriodSelector.php", "downloaded_repos/laravel_pulse/src/Livewire/Queues.php", "downloaded_repos/laravel_pulse/src/Livewire/Servers.php", "downloaded_repos/laravel_pulse/src/Livewire/SlowJobs.php", "downloaded_repos/laravel_pulse/src/Livewire/SlowOutgoingRequests.php", "downloaded_repos/laravel_pulse/src/Livewire/SlowQueries.php", "downloaded_repos/laravel_pulse/src/Livewire/SlowRequests.php", "downloaded_repos/laravel_pulse/src/Livewire/Usage.php", "downloaded_repos/laravel_pulse/src/Pulse.php", "downloaded_repos/laravel_pulse/src/PulseServiceProvider.php", "downloaded_repos/laravel_pulse/src/Recorders/CacheInteractions.php", "downloaded_repos/laravel_pulse/src/Recorders/Concerns/Groups.php", "downloaded_repos/laravel_pulse/src/Recorders/Concerns/Ignores.php", "downloaded_repos/laravel_pulse/src/Recorders/Concerns/LivewireRoutes.php", "downloaded_repos/laravel_pulse/src/Recorders/Concerns/Sampling.php", "downloaded_repos/laravel_pulse/src/Recorders/Concerns/Thresholds.php", "downloaded_repos/laravel_pulse/src/Recorders/Concerns/Throttling.php", "downloaded_repos/laravel_pulse/src/Recorders/Exceptions.php", "downloaded_repos/laravel_pulse/src/Recorders/Queues.php", "downloaded_repos/laravel_pulse/src/Recorders/Servers.php", "downloaded_repos/laravel_pulse/src/Recorders/SlowJobs.php", "downloaded_repos/laravel_pulse/src/Recorders/SlowOutgoingRequests.php", "downloaded_repos/laravel_pulse/src/Recorders/SlowQueries.php", "downloaded_repos/laravel_pulse/src/Recorders/SlowRequests.php", "downloaded_repos/laravel_pulse/src/Recorders/UserJobs.php", "downloaded_repos/laravel_pulse/src/Recorders/UserRequests.php", "downloaded_repos/laravel_pulse/src/Storage/DatabaseStorage.php", "downloaded_repos/laravel_pulse/src/Support/CacheStoreResolver.php", "downloaded_repos/laravel_pulse/src/Support/PulseMigration.php", "downloaded_repos/laravel_pulse/src/Support/RedisAdapter.php", "downloaded_repos/laravel_pulse/src/Support/RedisServerException.php", "downloaded_repos/laravel_pulse/src/Users.php", "downloaded_repos/laravel_pulse/src/Value.php", "downloaded_repos/laravel_pulse/tailwind.config.js", "downloaded_repos/laravel_pulse/testbench.yaml", "downloaded_repos/laravel_pulse/vite.config.js", "downloaded_repos/laravel_pulse/workbench/.env.example", "downloaded_repos/laravel_pulse/workbench/.gitignore"], "skipped": [{"path": "downloaded_repos/laravel_pulse/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/laravel_pulse/dist/pulse.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/dist/pulse.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/src/Pulse.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/AuthTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Commands/CheckCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Commands/ClearCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Exceptions/MyException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Ingests/DatabaseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/CacheTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/CustomCardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/ExceptionsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/QueuesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/RemembersQueriesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/ServersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/SlowJobsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/SlowOutgoingRequestsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/SlowQueriesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/SlowRequestsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Livewire/UsageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/PulseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/CacheInteractionsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/ExceptionsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/QueuesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/ServersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/SlowJobsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/SlowOutgoingRequestsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/SlowQueriesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/SlowRequestsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Recorders/UserRequestsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/RedisTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Feature/Storage/DatabaseStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/StorageFake.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/Unit/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_pulse/tests/fixtures/custom.css", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.691542148590088, "profiling_times": {"config_time": 6.944744110107422, "core_time": 2.9971866607666016, "ignores_time": 0.0016891956329345703, "total_time": 9.944831132888794}, "parsing_time": {"total_time": 1.1787910461425781, "per_file_time": {"mean": 0.009583667041809577, "std_dev": 0.0004943866786858712}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.7635135650634766, "per_file_time": {"mean": 0.006823490284107346, "std_dev": 0.0004722089215656659}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.4252588748931885, "per_file_and_rule_time": {"mean": 0.0009028850846989139, "std_dev": 9.059059431113108e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.04009079933166504, "per_def_and_rule_time": {"mean": 0.0004830216786947595, "std_dev": 1.1999218195306462e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}