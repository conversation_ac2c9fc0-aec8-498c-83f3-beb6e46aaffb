{"version": "1.130.0", "results": [{"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mavenium_PyEditorial/templates/content/blogcategory_form.html", "start": {"line": 36, "col": 4, "offset": 922}, "end": {"line": 53, "col": 11, "offset": 1297}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mavenium_PyEditorial/templates/content/podcastcategory_form.html", "start": {"line": 36, "col": 4, "offset": 925}, "end": {"line": 53, "col": 11, "offset": 1303}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mavenium_PyEditorial/templates/content/videocastcategory_form.html", "start": {"line": 36, "col": 4, "offset": 928}, "end": {"line": 53, "col": 11, "offset": 1309}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 36, "offset": 69}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/base.html:1:\n `{% load static %}\n{% load i18n %}\n{% get_media_prefix as MEDIA_URL %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/base.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 36, "offset": 69}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html:1:\n `{% extends 'base.html' %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 37, "offset": 99}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 17, "offset": 12}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "start": {"line": 94, "col": 1, "offset": 0}, "end": {"line": 94, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/index.html:1:\n `{% extends 'base.html' %}\n{% load i18n %}\n\n{% block content %}\n    {% for last_post in last_blog %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 37, "offset": 99}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 17, "offset": 12}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "start": {"line": 94, "col": 1, "offset": 0}, "end": {"line": 94, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html:1:\n `{% extends 'base.html' %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/registration/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/registration/login.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/registration/login.html:1:\n `{% extends 'base.html' %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/registration/login.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/registration/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/registration/login.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html:1:\n `{% extends 'base.html' %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/search.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/search.html:1:\n `{% extends 'base.html' %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/search.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/search.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/single.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/single.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/single.html:1:\n `{% extends 'base.html' %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/single.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/single.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/single.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html:1:\n `{% extends 'base.html' %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html", "spans": [{"file": "downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 62}}, {"file": "downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/mavenium_PyEditorial/.gitattributes", "downloaded_repos/mavenium_PyEditorial/.gitignore", "downloaded_repos/mavenium_PyEditorial/Dockerfile", "downloaded_repos/mavenium_PyEditorial/LICENSE", "downloaded_repos/mavenium_PyEditorial/PyEditorial/__init__.py", "downloaded_repos/mavenium_PyEditorial/PyEditorial/asgi.py", "downloaded_repos/mavenium_PyEditorial/PyEditorial/settings.py", "downloaded_repos/mavenium_PyEditorial/PyEditorial/urls.py", "downloaded_repos/mavenium_PyEditorial/PyEditorial/wsgi.py", "downloaded_repos/mavenium_PyEditorial/README.md", "downloaded_repos/mavenium_PyEditorial/Screenshots/Add-Blog.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Add-Podcast.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Add-Skill.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Add-Videocast.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Admin.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Blog-Admin.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Blog-Single.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Constance.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Podcast-Single.png", "downloaded_repos/mavenium_PyEditorial/Screenshots/Videocast-Single.png", "downloaded_repos/mavenium_PyEditorial/content/__init__.py", "downloaded_repos/mavenium_PyEditorial/content/admin.py", "downloaded_repos/mavenium_PyEditorial/content/apps.py", "downloaded_repos/mavenium_PyEditorial/content/context_processors.py", "downloaded_repos/mavenium_PyEditorial/content/forms.py", "downloaded_repos/mavenium_PyEditorial/content/models.py", "downloaded_repos/mavenium_PyEditorial/content/tests.py", "downloaded_repos/mavenium_PyEditorial/content/urls.py", "downloaded_repos/mavenium_PyEditorial/content/views.py", "downloaded_repos/mavenium_PyEditorial/docker-compose.yml", "downloaded_repos/mavenium_PyEditorial/manage.py", "downloaded_repos/mavenium_PyEditorial/requirements.txt", "downloaded_repos/mavenium_PyEditorial/templates/base.html", "downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html", "downloaded_repos/mavenium_PyEditorial/templates/content/blog_form.html", "downloaded_repos/mavenium_PyEditorial/templates/content/blogcategory_form.html", "downloaded_repos/mavenium_PyEditorial/templates/content/podcast_form.html", "downloaded_repos/mavenium_PyEditorial/templates/content/podcastcategory_form.html", "downloaded_repos/mavenium_PyEditorial/templates/content/skill_form.html", "downloaded_repos/mavenium_PyEditorial/templates/content/videocast_form.html", "downloaded_repos/mavenium_PyEditorial/templates/content/videocastcategory_form.html", "downloaded_repos/mavenium_PyEditorial/templates/index.html", "downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html", "downloaded_repos/mavenium_PyEditorial/templates/registration/login.html", "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html", "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_confirm.html", "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_done.html", "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_form.html", "downloaded_repos/mavenium_PyEditorial/templates/search.html", "downloaded_repos/mavenium_PyEditorial/templates/single.html", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/css/fontawesome-all.min.css", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/css/main.css", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/js/main.js", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/js/util.js", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-brands-400.eot", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-brands-400.svg", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-brands-400.ttf", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-brands-400.woff", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-brands-400.woff2", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-regular-400.eot", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-regular-400.svg", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-regular-400.ttf", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-regular-400.woff", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-regular-400.woff2", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-solid-900.eot", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-solid-900.svg", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-solid-900.ttf", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-solid-900.woff", "downloaded_repos/mavenium_PyEditorial/templates/static/assets/webfonts/fa-solid-900.woff2", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic01.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic02.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic03.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic04.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic05.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic06.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic07.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic08.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic09.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic10.jpg", "downloaded_repos/mavenium_PyEditorial/templates/static/images/pic11.jpg", "downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html"], "skipped": [{"path": "downloaded_repos/mavenium_PyEditorial/Screenshots/Archive.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/mavenium_PyEditorial/Screenshots/Index.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/blog_archive.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/podcast_archive.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/registration/login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/registration/password_reset_complete.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/single.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/static/assets/js/breakpoints.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/static/assets/js/browser.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/static/assets/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mavenium_PyEditorial/templates/videocast_archive.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.7714779376983643, "profiling_times": {"config_time": 6.474405765533447, "core_time": 3.375965118408203, "ignores_time": 0.0019156932830810547, "total_time": 9.853378057479858}, "parsing_time": {"total_time": 0.5346662998199463, "per_file_time": {"mean": 0.01909522499356951, "std_dev": 0.0009286450635307213}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.368090867996216, "per_file_time": {"mean": 0.021840454339981084, "std_dev": 0.0061633216713812555}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7530341148376465, "per_file_and_rule_time": {"mean": 0.004482345921652657, "std_dev": 0.00013111814776277253}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.2795908451080322, "per_def_and_rule_time": {"mean": 0.0011365481508456593, "std_dev": 1.6654598576083783e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}