{"version": "1.130.0", "results": [{"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/auth0_webauthn.me/api/index.js", "start": {"line": 4, "col": 7, "offset": 86}, "end": {"line": 4, "col": 22, "offset": 101}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/browser-support/index.js", "start": {"line": 27, "col": 5, "offset": 897}, "end": {"line": 27, "col": 53, "offset": 945}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/browser-support/index.js", "start": {"line": 28, "col": 5, "offset": 950}, "end": {"line": 28, "col": 59, "offset": 1004}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 270, "col": 5, "offset": 8380}, "end": {"line": 270, "col": 58, "offset": 8433}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 271, "col": 5, "offset": 8438}, "end": {"line": 271, "col": 75, "offset": 8508}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 275, "col": 9, "offset": 8690}, "end": {"line": 281, "col": 38, "offset": 8980}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 298, "col": 5, "offset": 9377}, "end": {"line": 298, "col": 50, "offset": 9422}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 299, "col": 5, "offset": 9427}, "end": {"line": 299, "col": 67, "offset": 9489}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 302, "col": 9, "offset": 9559}, "end": {"line": 308, "col": 38, "offset": 9841}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 325, "col": 5, "offset": 10248}, "end": {"line": 325, "col": 60, "offset": 10303}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 328, "col": 9, "offset": 10365}, "end": {"line": 330, "col": 11, "offset": 10486}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 334, "col": 9, "offset": 10569}, "end": {"line": 336, "col": 11, "offset": 10705}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 691, "col": 5, "offset": 22033}, "end": {"line": 691, "col": 27, "offset": 22055}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 747, "col": 5, "offset": 24739}, "end": {"line": 747, "col": 27, "offset": 24761}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "start": {"line": 829, "col": 5, "offset": 28313}, "end": {"line": 829, "col": 27, "offset": 28335}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/auth0_webauthn.me/src/debugger/utils.js", "start": {"line": 105, "col": 5, "offset": 2016}, "end": {"line": 105, "col": 20, "offset": 2031}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/tutorial/animation.js", "start": {"line": 203, "col": 5, "offset": 4965}, "end": {"line": 203, "col": 59, "offset": 5019}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/auth0_webauthn.me/src/util/modal.js", "start": {"line": 11, "col": 5, "offset": 335}, "end": {"line": 11, "col": 45, "offset": 375}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.pug.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/auth0_webauthn.me/views/footer.pug", "start": {"line": 7, "col": 3, "offset": 147}, "end": {"line": 7, "col": 31, "offset": 175}, "extra": {"message": "Detected an explicit unescape in a Pug template, using either '!=' or '!{...}'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://pugjs.org/language/code.html#unescaped-buffered-code", "https://pugjs.org/language/attributes.html#unescaped-attributes"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.pug.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/3xbe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/auth0_webauthn.me/.github/workflows/preview.yml", "start": {"line": 24, "col": 37, "offset": 596}, "end": {"line": 24, "col": 40, "offset": 599}}]], "message": "Syntax error at line downloaded_repos/auth0_webauthn.me/.github/workflows/preview.yml:24:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/auth0_webauthn.me/.github/workflows/preview.yml", "spans": [{"file": "downloaded_repos/auth0_webauthn.me/.github/workflows/preview.yml", "start": {"line": 24, "col": 37, "offset": 596}, "end": {"line": 24, "col": 40, "offset": 599}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/auth0_webauthn.me/.github/workflows/production.yml", "start": {"line": 21, "col": 44, "offset": 494}, "end": {"line": 21, "col": 47, "offset": 497}}]], "message": "Syntax error at line downloaded_repos/auth0_webauthn.me/.github/workflows/production.yml:21:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/auth0_webauthn.me/.github/workflows/production.yml", "spans": [{"file": "downloaded_repos/auth0_webauthn.me/.github/workflows/production.yml", "start": {"line": 21, "col": 44, "offset": 494}, "end": {"line": 21, "col": 47, "offset": 497}}]}], "paths": {"scanned": ["downloaded_repos/auth0_webauthn.me/.github/workflows/preview.yml", "downloaded_repos/auth0_webauthn.me/.github/workflows/production.yml", "downloaded_repos/auth0_webauthn.me/.gitignore", "downloaded_repos/auth0_webauthn.me/CONTRIBUTING.md", "downloaded_repos/auth0_webauthn.me/Gruntfile.js", "downloaded_repos/auth0_webauthn.me/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/auth0_webauthn.me/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/auth0_webauthn.me/LICENSE", "downloaded_repos/auth0_webauthn.me/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/auth0_webauthn.me/README.md", "downloaded_repos/auth0_webauthn.me/api/index.js", "downloaded_repos/auth0_webauthn.me/favicon.png", "downloaded_repos/auth0_webauthn.me/fonts/FaktPro-Blond.woff", "downloaded_repos/auth0_webauthn.me/fonts/FaktPro-Normal.woff", "downloaded_repos/auth0_webauthn.me/fonts/FaktPro-SemiBold.woff", "downloaded_repos/auth0_webauthn.me/fonts/FaktPro-Thin.woff", "downloaded_repos/auth0_webauthn.me/img/1-Web-Authentication-Entities.png", "downloaded_repos/auth0_webauthn.me/img/1-Web-Authentication-Entities.svg", "downloaded_repos/auth0_webauthn.me/img/2-Registration.png", "downloaded_repos/auth0_webauthn.me/img/2-Registration.svg", "downloaded_repos/auth0_webauthn.me/img/3-Login.png", "downloaded_repos/auth0_webauthn.me/img/3-Login.svg", "downloaded_repos/auth0_webauthn.me/img/animation.svg", "downloaded_repos/auth0_webauthn.me/img/auth0-logo.svg", "downloaded_repos/auth0_webauthn.me/img/auth0.svg", "downloaded_repos/auth0_webauthn.me/img/check-circle.svg", "downloaded_repos/auth0_webauthn.me/img/checkbox.svg", "downloaded_repos/auth0_webauthn.me/img/chrome-logo.png", "downloaded_repos/auth0_webauthn.me/img/crafted-by-auth0-logo-color.svg", "downloaded_repos/auth0_webauthn.me/img/crafted-by-auth0-logo.svg", "downloaded_repos/auth0_webauthn.me/img/edge-logo.png", "downloaded_repos/auth0_webauthn.me/img/facebook-logo.svg", "downloaded_repos/auth0_webauthn.me/img/firefox-logo.png", "downloaded_repos/auth0_webauthn.me/img/platform.svg", "downloaded_repos/auth0_webauthn.me/img/question-circle.svg", "downloaded_repos/auth0_webauthn.me/img/roaming.svg", "downloaded_repos/auth0_webauthn.me/img/select.svg", "downloaded_repos/auth0_webauthn.me/img/twitter-logo.png", "downloaded_repos/auth0_webauthn.me/img/twitter-logo.svg", "downloaded_repos/auth0_webauthn.me/img/webauthn-logo-no-text.svg", "downloaded_repos/auth0_webauthn.me/img/webauthn-logo.svg", "downloaded_repos/auth0_webauthn.me/img/x-circle.svg", "downloaded_repos/auth0_webauthn.me/img/yubikey.svg", "downloaded_repos/auth0_webauthn.me/less/browser-support/index.less", "downloaded_repos/auth0_webauthn.me/less/buttons.less", "downloaded_repos/auth0_webauthn.me/less/code.less", "downloaded_repos/auth0_webauthn.me/less/common.less", "downloaded_repos/auth0_webauthn.me/less/debugger/editor.less", "downloaded_repos/auth0_webauthn.me/less/debugger/form.less", "downloaded_repos/auth0_webauthn.me/less/debugger/index.less", "downloaded_repos/auth0_webauthn.me/less/debugger/output.less", "downloaded_repos/auth0_webauthn.me/less/debugger/tabs.less", "downloaded_repos/auth0_webauthn.me/less/error.less", "downloaded_repos/auth0_webauthn.me/less/fonts.less", "downloaded_repos/auth0_webauthn.me/less/footer/branding.less", "downloaded_repos/auth0_webauthn.me/less/footer/browser-status.less", "downloaded_repos/auth0_webauthn.me/less/footer/index.less", "downloaded_repos/auth0_webauthn.me/less/header.less", "downloaded_repos/auth0_webauthn.me/less/hero.less", "downloaded_repos/auth0_webauthn.me/less/introduction/index.less", "downloaded_repos/auth0_webauthn.me/less/modal.less", "downloaded_repos/auth0_webauthn.me/less/passkeys/index.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/index.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/step-1.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/step-2.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/step-3.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/step-4-allow-credentials.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/step-4.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/step-common.less", "downloaded_repos/auth0_webauthn.me/less/tutorial/try-the-debugger.less", "downloaded_repos/auth0_webauthn.me/less/typography.less", "downloaded_repos/auth0_webauthn.me/less/vars.less", "downloaded_repos/auth0_webauthn.me/package-lock.json", "downloaded_repos/auth0_webauthn.me/package.json", "downloaded_repos/auth0_webauthn.me/robots.txt", "downloaded_repos/auth0_webauthn.me/src/browser-support/index.js", "downloaded_repos/auth0_webauthn.me/src/ccpa-modal.js", "downloaded_repos/auth0_webauthn.me/src/cookie-consent.js", "downloaded_repos/auth0_webauthn.me/src/debugger/debugger.js", "downloaded_repos/auth0_webauthn.me/src/debugger/dom-elements.js", "downloaded_repos/auth0_webauthn.me/src/debugger/event-manager.js", "downloaded_repos/auth0_webauthn.me/src/debugger/index.js", "downloaded_repos/auth0_webauthn.me/src/debugger/oids.js", "downloaded_repos/auth0_webauthn.me/src/debugger/output-parser.js", "downloaded_repos/auth0_webauthn.me/src/debugger/strings.js", "downloaded_repos/auth0_webauthn.me/src/debugger/transformations.js", "downloaded_repos/auth0_webauthn.me/src/debugger/utils.js", "downloaded_repos/auth0_webauthn.me/src/index.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/animation.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/error.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/index.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-1-animations.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-1.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-2-animations.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-2.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-3.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-4-animations.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-4.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-5-animations.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-5.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-6-animations.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/step-6.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/utils.js", "downloaded_repos/auth0_webauthn.me/src/tutorial/webauthn.js", "downloaded_repos/auth0_webauthn.me/src/util/modal.js", "downloaded_repos/auth0_webauthn.me/src/util/tabs.js", "downloaded_repos/auth0_webauthn.me/twitter-card.png", "downloaded_repos/auth0_webauthn.me/vercel.json", "downloaded_repos/auth0_webauthn.me/views/browser-support/index.pug", "downloaded_repos/auth0_webauthn.me/views/debugger/create.pug", "downloaded_repos/auth0_webauthn.me/views/debugger/get.pug", "downloaded_repos/auth0_webauthn.me/views/debugger/howto.pug", "downloaded_repos/auth0_webauthn.me/views/debugger/index.pug", "downloaded_repos/auth0_webauthn.me/views/debugger/output-authenticate.pug", "downloaded_repos/auth0_webauthn.me/views/debugger/output-cbor.pug", "downloaded_repos/auth0_webauthn.me/views/debugger/output.pug", "downloaded_repos/auth0_webauthn.me/views/footer.pug", "downloaded_repos/auth0_webauthn.me/views/header.pug", "downloaded_repos/auth0_webauthn.me/views/hero.pug", "downloaded_repos/auth0_webauthn.me/views/index.pug", "downloaded_repos/auth0_webauthn.me/views/introduction/index.pug", "downloaded_repos/auth0_webauthn.me/views/introduction/introduction.markdown", "downloaded_repos/auth0_webauthn.me/views/introduction.pug", "downloaded_repos/auth0_webauthn.me/views/layout.pug", "downloaded_repos/auth0_webauthn.me/views/navbar.pug", "downloaded_repos/auth0_webauthn.me/views/passkeys/index.pug", "downloaded_repos/auth0_webauthn.me/views/passkeys/passkeys.markdown", "downloaded_repos/auth0_webauthn.me/views/prefetch.pug", "downloaded_repos/auth0_webauthn.me/views/top.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/index.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-1.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-2.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-3.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-4-allow-credentials.html", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-4.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-5.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-6.pug", "downloaded_repos/auth0_webauthn.me/views/tutorial/step-left.pug", "downloaded_repos/auth0_webauthn.me/webpack.common.js", "downloaded_repos/auth0_webauthn.me/webpack.dev.js", "downloaded_repos/auth0_webauthn.me/webpack.prod.js"], "skipped": [{"path": "downloaded_repos/auth0_webauthn.me/.github/workflows/preview.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/auth0_webauthn.me/.github/workflows/production.yml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.7193920612335205, "profiling_times": {"config_time": 5.987417697906494, "core_time": 3.253084182739258, "ignores_time": 0.0015969276428222656, "total_time": 9.24325942993164}, "parsing_time": {"total_time": 0.9690604209899902, "per_file_time": {"mean": 0.023635620024146098, "std_dev": 0.0016914160278316413}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.6205382347106934, "per_file_time": {"mean": 0.011174500724415715, "std_dev": 0.0016445236276905059}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.9336738586425781, "per_file_and_rule_time": {"mean": 0.0038903077443440746, "std_dev": 6.583341813423102e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.24417352676391602, "per_def_and_rule_time": {"mean": 0.00047783469034034447, "std_dev": 8.416893713915679e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}