{"version": "1.130.0", "results": [{"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Classes/PrivacyideaAuth.php", "start": {"line": 63, "col": 4, "offset": 2266}, "end": {"line": 63, "col": 58, "offset": 2320}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= 0)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Classes/PrivacyideaService.php", "start": {"line": 84, "col": 20, "offset": 2542}, "end": {"line": 84, "col": 94, "offset": 2616}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.nginx.security.insecure-ssl-version.insecure-ssl-version", "path": "downloaded_repos/privacyidea_privacyidea/deploy/nginx/sites-available/privacyidea", "start": {"line": 15, "col": 2, "offset": 434}, "end": {"line": 15, "col": 38, "offset": 470}, "extra": {"message": "Detected use of an insecure SSL version. Secure SSL versions are TLSv1.2 and TLS1.3; older versions are known to be broken and are susceptible to attacks. Prefer use of TLSv1.2 or later.", "metadata": {"cwe": ["CWE-326: Inadequate Encryption Strength"], "references": ["https://www.acunetix.com/blog/web-security-zone/hardening-nginx/", "https://www.acunetix.com/blog/articles/tls-ssl-cipher-hardening/"], "category": "security", "technology": ["nginx"], "confidence": "HIGH", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.nginx.security.insecure-ssl-version.insecure-ssl-version", "shortlink": "https://sg.run/gLKy"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-private-key.detected-private-key", "path": "downloaded_repos/privacyidea_privacyidea/deploy/privacyidea/private.pem", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 52, "offset": 83}, "extra": {"message": "Private Key detected. This is a sensitive credential and should not be hardcoded here. Instead, store this in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-private-key.detected-private-key", "shortlink": "https://sg.run/b7dr"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/prepolicy.py", "start": {"line": 1643, "col": 22, "offset": 72887}, "end": {"line": 1643, "col": 58, "offset": 72923}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/utils.py", "start": {"line": 341, "col": 19, "offset": 11154}, "end": {"line": 342, "col": 95, "offset": 11323}, "extra": {"message": "Detected user input used to manually construct a SQL string. This is usually bad practice because manual construction could accidentally result in a SQL injection. An attacker could use a SQL injection to steal or modify contents of the database. Instead, use a parameterized query which is available by default in most database engines. Alternatively, consider using the Django object-relational mappers (ORM) instead of raw SQL queries.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/security/#sql-injection-protection"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "impact": "LOW", "likelihood": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/python.django.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/PbZp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/utils.py", "start": {"line": 347, "col": 19, "offset": 11482}, "end": {"line": 348, "col": 98, "offset": 11657}, "extra": {"message": "Detected user input used to manually construct a SQL string. This is usually bad practice because manual construction could accidentally result in a SQL injection. An attacker could use a SQL injection to steal or modify contents of the database. Instead, use a parameterized query which is available by default in most database engines. Alternatively, consider using the Django object-relational mappers (ORM) instead of raw SQL queries.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/security/#sql-injection-protection"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "impact": "LOW", "likelihood": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/python.django.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/PbZp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/utils.py", "start": {"line": 361, "col": 19, "offset": 12118}, "end": {"line": 362, "col": 98, "offset": 12293}, "extra": {"message": "Detected user input used to manually construct a SQL string. This is usually bad practice because manual construction could accidentally result in a SQL injection. An attacker could use a SQL injection to steal or modify contents of the database. Instead, use a parameterized query which is available by default in most database engines. Alternatively, consider using the Django object-relational mappers (ORM) instead of raw SQL queries.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/security/#sql-injection-protection"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "impact": "LOW", "likelihood": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/python.django.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/PbZp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/system.py", "start": {"line": 110, "col": 47, "offset": 4268}, "end": {"line": 111, "col": 79, "offset": 4383}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/token.py", "start": {"line": 214, "col": 52, "offset": 12059}, "end": {"line": 214, "col": 91, "offset": 12098}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/base.py", "start": {"line": 65, "col": 15, "offset": 2257}, "end": {"line": 65, "col": 41, "offset": 2283}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/base.py", "start": {"line": 140, "col": 11, "offset": 4764}, "end": {"line": 140, "col": 44, "offset": 4797}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/base.py", "start": {"line": 168, "col": 23, "offset": 5663}, "end": {"line": 168, "col": 84, "offset": 5724}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/config.py", "start": {"line": 813, "col": 26, "offset": 32138}, "end": {"line": 813, "col": 59, "offset": 32171}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/config.py", "start": {"line": 842, "col": 22, "offset": 33122}, "end": {"line": 842, "col": 55, "offset": 33155}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/config.py", "start": {"line": 870, "col": 22, "offset": 33942}, "end": {"line": 870, "col": 55, "offset": 33975}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/config.py", "start": {"line": 897, "col": 22, "offset": 34762}, "end": {"line": 897, "col": 55, "offset": 34795}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/config.py", "start": {"line": 927, "col": 22, "offset": 35655}, "end": {"line": 927, "col": 58, "offset": 35691}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/container.py", "start": {"line": 449, "col": 17, "offset": 21309}, "end": {"line": 449, "col": 45, "offset": 21337}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/container.py", "start": {"line": 1377, "col": 17, "offset": 60234}, "end": {"line": 1377, "col": 45, "offset": 60262}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/crypto.py", "start": {"line": 955, "col": 13, "offset": 30781}, "end": {"line": 955, "col": 67, "offset": 30835}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/usernotification.py", "start": {"line": 118, "col": 15, "offset": 4151}, "end": {"line": 118, "col": 31, "offset": 4167}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/importotp.py", "start": {"line": 366, "col": 21, "offset": 13482}, "end": {"line": 367, "col": 72, "offset": 13603}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"The Token with the serial %s has the \"\n                              \"productname %s\" % (SERIAL, DESCRIPTION) being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.weak-ssl-version.weak-ssl-version", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machines/ldap.py", "start": {"line": 266, "col": 44, "offset": 10622}, "end": {"line": 266, "col": 62, "offset": 10640}, "extra": {"message": "An insecure SSL version was detected. TLS versions 1.0, 1.1, and all SSL versions are considered weak encryption and are deprecated. Use 'ssl.PROTOCOL_TLSv1_2' or higher.", "metadata": {"cwe": ["CWE-326: Inadequate Encryption Strength"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/plugins/insecure_ssl_tls.py#L30", "asvs": {"control_id": "9.1.3 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v91-client-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "references": ["https://tools.ietf.org/html/rfc7568", "https://tools.ietf.org/id/draft-ietf-tls-oldversions-deprecate-02.html", "https://docs.python.org/3/library/ssl.html#ssl.PROTOCOL_TLSv1_2"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.audit.weak-ssl-version.weak-ssl-version", "shortlink": "https://sg.run/RoZO"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.weak-ssl-version.weak-ssl-version", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machines/ldap.py", "start": {"line": 308, "col": 39, "offset": 12470}, "end": {"line": 308, "col": 57, "offset": 12488}, "extra": {"message": "An insecure SSL version was detected. TLS versions 1.0, 1.1, and all SSL versions are considered weak encryption and are deprecated. Use 'ssl.PROTOCOL_TLSv1_2' or higher.", "metadata": {"cwe": ["CWE-326: Inadequate Encryption Strength"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/plugins/insecure_ssl_tls.py#L30", "asvs": {"control_id": "9.1.3 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v91-client-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "references": ["https://tools.ietf.org/html/rfc7568", "https://tools.ietf.org/id/draft-ietf-tls-oldversions-deprecate-02.html", "https://docs.python.org/3/library/ssl.html#ssl.PROTOCOL_TLSv1_2"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.audit.weak-ssl-version.weak-ssl-version", "shortlink": "https://sg.run/RoZO"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/LDAPIdResolver.py", "start": {"line": 813, "col": 35, "offset": 34209}, "end": {"line": 813, "col": 74, "offset": 34248}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(id_string.encode(\"utf-8\"))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/SQLIdResolver.py", "start": {"line": 440, "col": 40, "offset": 16263}, "end": {"line": 440, "col": 75, "offset": 16298}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(id_str.encode('utf8'))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/ocratoken.py", "start": {"line": 226, "col": 49, "offset": 8292}, "end": {"line": 226, "col": 82, "offset": 8325}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(to_bytes(challenge))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.cryptography.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/webauthn.py", "start": {"line": 2047, "col": 56, "offset": 84273}, "end": {"line": 2047, "col": 60, "offset": 84277}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "SHA256", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "references": ["https://cryptography.io/en/latest/hazmat/primitives/cryptographic-hashes/#sha-1", "https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["cryptography"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "functional-categories": ["crypto::search::symmetric-algorithm::cryptography"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.cryptography.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/J9Qy"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/__init__.py", "start": {"line": 381, "col": 20, "offset": 11122}, "end": {"line": 381, "col": 41, "offset": 11143}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(payload)", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/__init__.py", "start": {"line": 1140, "col": 11, "offset": 39830}, "end": {"line": 1140, "col": 38, "offset": 39857}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/configControllers.js", "start": {"line": 540, "col": 22, "offset": 22053}, "end": {"line": 540, "col": 38, "offset": 22069}, "extra": {"message": "RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/eventController.js", "start": {"line": 289, "col": 22, "offset": 12715}, "end": {"line": 289, "col": 38, "offset": 12731}, "extra": {"message": "RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.tiqr.html", "start": {"line": 83, "col": 9, "offset": 2995}, "end": {"line": 84, "col": 21, "offset": 3085}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dialogs/views/dialog.about.html", "start": {"line": 26, "col": 25, "offset": 1105}, "end": {"line": 28, "col": 39, "offset": 1243}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dialogs/views/dialog.about.html", "start": {"line": 42, "col": 21, "offset": 2005}, "end": {"line": 44, "col": 43, "offset": 2146}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/filters/filters.js", "start": {"line": 33, "col": 58, "offset": 1017}, "end": {"line": 33, "col": 95, "offset": 1054}, "extra": {"message": "RegExp() called with a `query` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.angular.security.detect-angular-open-redirect.detect-angular-open-redirect", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/controllers/loginControllers.js", "start": {"line": 621, "col": 21, "offset": 32631}, "end": {"line": 621, "col": 71, "offset": 32681}, "extra": {"message": "Use of $window.location.href can lead to open-redirect if user input is used for redirection.", "metadata": {"asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "references": ["https://docs.angularjs.org/api/ng/service/$sce#trustAsJs", "https://owasp.org/www-chapter-london/assets/slides/OWASPLondon20170727_AngularJS.pdf"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["angular"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.angular.security.detect-angular-open-redirect.detect-angular-open-redirect", "shortlink": "https://sg.run/rdn1"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/bootstrap.js", "start": {"line": 1401, "col": 5, "offset": 40547}, "end": {"line": 1401, "col": 48, "offset": 40590}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.angular.security.detect-angular-element-methods.detect-angular-element-methods", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/hotkeys.js", "start": {"line": 279, "col": 41, "offset": 9301}, "end": {"line": 279, "col": 66, "offset": 9326}, "extra": {"message": "Use of angular.element can lead to XSS if user-input is treated as part of the HTML element within `append`. It is recommended to contextually output encode user-input, before inserting into `append`. If the HTML needs to be preserved it is recommended to sanitize the input using $sce.getTrustedHTML or $sanitize.", "metadata": {"confidence": "LOW", "references": ["https://docs.angularjs.org/api/ng/function/angular.element", "https://owasp.org/www-chapter-london/assets/slides/OWASPLondon20170727_AngularJS.pdf"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["<PERSON><PERSON>s"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.angular.security.detect-angular-element-methods.detect-angular-element-methods", "shortlink": "https://sg.run/ydnO"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-sanitize.js", "start": {"line": 467, "col": 7, "offset": 19140}, "end": {"line": 467, "col": 41, "offset": 19174}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "start": {"line": 1250, "col": 20, "offset": 41123}, "end": {"line": 1250, "col": 51, "offset": 41154}, "extra": {"message": "RegExp() called with a `val` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "start": {"line": 1256, "col": 21, "offset": 41359}, "end": {"line": 1256, "col": 59, "offset": 41397}, "extra": {"message": "RegExp() called with a `val` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "start": {"line": 11, "col": 1, "offset": 231}, "end": {"line": 26, "col": 8, "offset": 636}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-certificate", "start": {"line": 42, "col": 33, "offset": 1059}, "end": {"line": 42, "col": 37, "offset": 1063}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-file-permissions.insecure-file-permissions", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-certificate", "start": {"line": 45, "col": 13, "offset": 1141}, "end": {"line": 45, "col": 33, "offset": 1161}, "extra": {"message": "These permissions `0x400` are widely permissive and grant access to more people than may be necessary. A good default is `0o644` which gives read and write access to yourself and read access to everyone else.", "metadata": {"category": "security", "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-276: Incorrect Default Permissions"], "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-file-permissions.insecure-file-permissions", "shortlink": "https://sg.run/AXY4"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.exec-detected.exec-detected", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 23, "col": 5, "offset": 362}, "end": {"line": 23, "col": 57, "offset": 414}, "extra": {"message": "Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.exec-detected.exec-detected", "shortlink": "https://sg.run/ndRX"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 27, "col": 74, "offset": 520}, "end": {"line": 27, "col": 78, "offset": 524}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 34, "col": 56, "offset": 748}, "end": {"line": 34, "col": 60, "offset": 752}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 35, "col": 87, "offset": 840}, "end": {"line": 35, "col": 91, "offset": 844}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 42, "col": 74, "offset": 1097}, "end": {"line": 42, "col": 78, "offset": 1101}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 43, "col": 94, "offset": 1196}, "end": {"line": 43, "col": 98, "offset": 1200}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 50, "col": 84, "offset": 1493}, "end": {"line": 50, "col": 88, "offset": 1497}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "start": {"line": 51, "col": 104, "offset": 1602}, "end": {"line": 51, "col": 108, "offset": 1606}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-pip-update", "start": {"line": 37, "col": 25, "offset": 1213}, "end": {"line": 37, "col": 29, "offset": 1217}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-user-action", "start": {"line": 115, "col": 25, "offset": 4454}, "end": {"line": 121, "col": 73, "offset": 4843}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.post(\"{0!s}/token/init\".format(url),\n                                  verify=True,\n                                  data={\"type\": tokentype,\n                                        \"genkey\": 1,\n                                        \"user\": user,\n                                        \"realm\": realm},\n                                  headers={\"Authorization\": auth_token})", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/privacyidea_privacyidea/tools/ssha.py", "start": {"line": 25, "col": 9, "offset": 432}, "end": {"line": 25, "col": 51, "offset": 474}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(passwd.encode('utf8') + salt)", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "message": "Timeout when running python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http on downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policy.py:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policy.py"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenDetailController.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenDetailController.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.angular.security.detect-angular-element-taint.detect-angular-element-taint", "message": "Timeout when running javascript.angular.security.detect-angular-element-taint.detect-angular-element-taint on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js:\n ", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/privacyidea_privacyidea/.circleci/config.yml", "start": {"line": 83, "col": 49, "offset": 2658}, "end": {"line": 83, "col": 52, "offset": 2661}}]], "message": "Syntax error at line downloaded_repos/privacyidea_privacyidea/.circleci/config.yml:83:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `.db` was unexpected", "path": "downloaded_repos/privacyidea_privacyidea/.circleci/config.yml", "spans": [{"file": "downloaded_repos/privacyidea_privacyidea/.circleci/config.yml", "start": {"line": 83, "col": 49, "offset": 2658}, "end": {"line": 83, "col": 52, "offset": 2661}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 28, "offset": 27}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 28, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html:1:\n `{% include 'header.html' %}` was unexpected", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "spans": [{"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 28, "offset": 27}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 28, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 9, "col": 70, "offset": 0}, "end": {"line": 9, "col": 99, "offset": 29}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 10, "col": 42, "offset": 0}, "end": {"line": 10, "col": 89, "offset": 47}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 11, "col": 42, "offset": 0}, "end": {"line": 11, "col": 100, "offset": 58}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 12, "col": 42, "offset": 0}, "end": {"line": 12, "col": 94, "offset": 52}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 13, "col": 42, "offset": 0}, "end": {"line": 13, "col": 87, "offset": 45}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 15, "col": 42, "offset": 0}, "end": {"line": 15, "col": 78, "offset": 36}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 16, "col": 42, "offset": 0}, "end": {"line": 16, "col": 76, "offset": 34}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 17, "col": 42, "offset": 0}, "end": {"line": 17, "col": 79, "offset": 37}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 18, "col": 42, "offset": 0}, "end": {"line": 18, "col": 80, "offset": 38}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 19, "col": 42, "offset": 0}, "end": {"line": 19, "col": 80, "offset": 38}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 20, "col": 42, "offset": 0}, "end": {"line": 20, "col": 81, "offset": 39}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 21, "col": 42, "offset": 0}, "end": {"line": 21, "col": 79, "offset": 37}}]], "message": "Syntax error at line downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html:9:\n `/favicon.png\" | versioned }}\"` was unexpected", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "spans": [{"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 9, "col": 70, "offset": 0}, "end": {"line": 9, "col": 99, "offset": 29}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 10, "col": 42, "offset": 0}, "end": {"line": 10, "col": 89, "offset": 47}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 11, "col": 42, "offset": 0}, "end": {"line": 11, "col": 100, "offset": 58}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 12, "col": 42, "offset": 0}, "end": {"line": 12, "col": 94, "offset": 52}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 13, "col": 42, "offset": 0}, "end": {"line": 13, "col": 87, "offset": 45}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 15, "col": 42, "offset": 0}, "end": {"line": 15, "col": 78, "offset": 36}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 16, "col": 42, "offset": 0}, "end": {"line": 16, "col": 76, "offset": 34}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 17, "col": 42, "offset": 0}, "end": {"line": 17, "col": 79, "offset": 37}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 18, "col": 42, "offset": 0}, "end": {"line": 18, "col": 80, "offset": 38}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 19, "col": 42, "offset": 0}, "end": {"line": 19, "col": 80, "offset": 38}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 20, "col": 42, "offset": 0}, "end": {"line": 20, "col": 81, "offset": 39}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "start": {"line": 21, "col": 42, "offset": 0}, "end": {"line": 21, "col": 79, "offset": 37}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 28, "offset": 27}}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html", "start": {"line": 64, "col": 1, "offset": 0}, "end": {"line": 64, "col": 28, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html:1:\n `{% include 'header.html' %}` was unexpected", "path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html", "spans": [{"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 28, "offset": 27}}, {"file": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html", "start": {"line": 64, "col": 1, "offset": 0}, "end": {"line": 64, "col": 28, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 27, "col": 43, "offset": 0}, "end": {"line": 27, "col": 44, "offset": 1}}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 28, "col": 44, "offset": 0}, "end": {"line": 28, "col": 45, "offset": 1}}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 29, "col": 45, "offset": 0}, "end": {"line": 29, "col": 46, "offset": 1}}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 30, "col": 43, "offset": 0}, "end": {"line": 30, "col": 44, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys:27:\n `2` was unexpected", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "spans": [{"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 27, "col": 43, "offset": 0}, "end": {"line": 27, "col": 44, "offset": 1}}, {"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 28, "col": 44, "offset": 0}, "end": {"line": 28, "col": 45, "offset": 1}}, {"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 29, "col": 45, "offset": 0}, "end": {"line": 29, "col": 46, "offset": 1}}, {"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "start": {"line": 30, "col": 43, "offset": 0}, "end": {"line": 30, "col": 44, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 30, "col": 43, "offset": 0}, "end": {"line": 30, "col": 44, "offset": 1}}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 31, "col": 44, "offset": 0}, "end": {"line": 31, "col": 45, "offset": 1}}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 32, "col": 44, "offset": 0}, "end": {"line": 32, "col": 45, "offset": 1}}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 33, "col": 44, "offset": 0}, "end": {"line": 33, "col": 45, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh:30:\n `2` was unexpected", "path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "spans": [{"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 30, "col": 43, "offset": 0}, "end": {"line": 30, "col": 44, "offset": 1}}, {"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 31, "col": 44, "offset": 0}, "end": {"line": 31, "col": 45, "offset": 1}}, {"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 32, "col": 44, "offset": 0}, "end": {"line": 32, "col": 45, "offset": 1}}, {"file": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "start": {"line": 33, "col": 44, "offset": 0}, "end": {"line": 33, "col": 45, "offset": 1}}]}], "paths": {"scanned": ["downloaded_repos/privacyidea_privacyidea/.circleci/config.yml", "downloaded_repos/privacyidea_privacyidea/.codespell_ignore", "downloaded_repos/privacyidea_privacyidea/.codespell_skip", "downloaded_repos/privacyidea_privacyidea/.dockerignore", "downloaded_repos/privacyidea_privacyidea/.github/ISSUE_TEMPLATE/1-bug_report.md", "downloaded_repos/privacyidea_privacyidea/.github/ISSUE_TEMPLATE/2-feature_request.md", "downloaded_repos/privacyidea_privacyidea/.github/ISSUE_TEMPLATE/3-are_you_lost.md", "downloaded_repos/privacyidea_privacyidea/.github/ISSUE_TEMPLATE/4-idea.md", "downloaded_repos/privacyidea_privacyidea/.github/codecov.yml", "downloaded_repos/privacyidea_privacyidea/.github/dependabot.yml", "downloaded_repos/privacyidea_privacyidea/.github/filters.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/bandit.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/codeql.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/codespell.yaml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/dependency-review.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/gitlab-trigger.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/greetings.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/python-dist.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/python.yml", "downloaded_repos/privacyidea_privacyidea/.github/workflows/zizmor.yml", "downloaded_repos/privacyidea_privacyidea/.gitignore", "downloaded_repos/privacyidea_privacyidea/.gitmodules", "downloaded_repos/privacyidea_privacyidea/.pep8speaks.yml", "downloaded_repos/privacyidea_privacyidea/.readthedocs.yaml", "downloaded_repos/privacyidea_privacyidea/AUTHORS.md", "downloaded_repos/privacyidea_privacyidea/CONTRIBUTING.md", "downloaded_repos/privacyidea_privacyidea/Changelog", "downloaded_repos/privacyidea_privacyidea/Gruntfile.js", "downloaded_repos/privacyidea_privacyidea/LICENSE", "downloaded_repos/privacyidea_privacyidea/MANIFEST.in", "downloaded_repos/privacyidea_privacyidea/Makefile", "downloaded_repos/privacyidea_privacyidea/README.rst", "downloaded_repos/privacyidea_privacyidea/READ_BEFORE_UPDATE.md", "downloaded_repos/privacyidea_privacyidea/SECURITY.md", "downloaded_repos/privacyidea_privacyidea/authmodules/OTRS/README.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/README.md", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/Makefile", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Classes/PrivacyideaAuth.php", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Classes/PrivacyideaService.php", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Classes/Utility/ExtensionManagerConfigurationUtility.php", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/ChangeLog/Index.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/Configuration/Index.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/Images/configuration.png", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/Includes.txt", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/Index.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/Introduction/Index.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/KnownProblems/Index.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/Settings.yml", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Documentation/ToDoList/Index.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/ExtensionBuilder.json", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Readme.rst", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Resources/Private/.htaccess", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Resources/Private/Language/locallang.xlf", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Resources/Private/Language/locallang_csh_tx_privacyidea_domain_model_privacyideaauth.xlf", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Resources/Private/Language/locallang_db.xlf", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Resources/Private/Language/locallang_em.xlf", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Resources/Public/Icons/relation.gif", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/Resources/Public/Icons/tx_privacyidea_domain_model_privacyideaauth.gif", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/ext_conf_template.txt", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/ext_emconf.php", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/ext_icon.gif", "downloaded_repos/privacyidea_privacyidea/authmodules/TYPO3/privacyidea/ext_localconf.php", "downloaded_repos/privacyidea_privacyidea/authmodules/__init__.py", "downloaded_repos/privacyidea_privacyidea/authmodules/apache2/README.md", "downloaded_repos/privacyidea_privacyidea/authmodules/apache2/__init__.py", "downloaded_repos/privacyidea_privacyidea/authmodules/apache2/apache.conf", "downloaded_repos/privacyidea_privacyidea/authmodules/apache2/privacyidea_apache.py", "downloaded_repos/privacyidea_privacyidea/deploy/README.md", "downloaded_repos/privacyidea_privacyidea/deploy/apache/privacyideaapp.wsgi", "downloaded_repos/privacyidea_privacyidea/deploy/apache/sites-available/privacyidea.conf", "downloaded_repos/privacyidea_privacyidea/deploy/crontab/privacyidea", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/changelog", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/clean", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/compat", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/control", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/python-privacyidea.triggers", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/rules", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/source/format", "downloaded_repos/privacyidea_privacyidea/deploy/debian-virtualenv/source/options", "downloaded_repos/privacyidea_privacyidea/deploy/docker/Dockerfile", "downloaded_repos/privacyidea_privacyidea/deploy/docker/README.Docker.md", "downloaded_repos/privacyidea_privacyidea/deploy/docker/entrypoint.sh", "downloaded_repos/privacyidea_privacyidea/deploy/logging.cfg", "downloaded_repos/privacyidea_privacyidea/deploy/nginx/privacyideaapp.py", "downloaded_repos/privacyidea_privacyidea/deploy/nginx/sites-available/privacyidea", "downloaded_repos/privacyidea_privacyidea/deploy/pi.cfg", "downloaded_repos/privacyidea_privacyidea/deploy/privacyidea/NetKnights.pem", "downloaded_repos/privacyidea_privacyidea/deploy/privacyidea/dictionary", "downloaded_repos/privacyidea_privacyidea/deploy/privacyidea/enckey", "downloaded_repos/privacyidea_privacyidea/deploy/privacyidea/private.pem", "downloaded_repos/privacyidea_privacyidea/deploy/privacyidea/public.pem", "downloaded_repos/privacyidea_privacyidea/deploy/uwsgi/apps-available/privacyidea.xml", "downloaded_repos/privacyidea_privacyidea/doc/Makefile", "downloaded_repos/privacyidea_privacyidea/doc/_static/css/custom.css", "downloaded_repos/privacyidea_privacyidea/doc/application_plugins/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/application_plugins/openvpn.rst", "downloaded_repos/privacyidea_privacyidea/doc/application_plugins/owncloud.png", "downloaded_repos/privacyidea_privacyidea/doc/application_plugins/plugin-guide.rst", "downloaded_repos/privacyidea_privacyidea/doc/application_plugins/rlm_perl.rst", "downloaded_repos/privacyidea_privacyidea/doc/application_plugins/rlm_rest.rst", "downloaded_repos/privacyidea_privacyidea/doc/audit/auditlog.png", "downloaded_repos/privacyidea_privacyidea/doc/audit/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/conf.py", "downloaded_repos/privacyidea_privacyidea/doc/configuration/caconnectors.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/CA-connectors.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/Entra_app_permissions.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/Entra_client_credential.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/backup1.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/backup2.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/backup3.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/configure-privacyidea.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/database.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/manage-admins.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/manage-radius-clients.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/redundancy-successful.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/appliance/start-screen.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/ask-create-realm.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/enroll-cert.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/entraid_resolver.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/entraid_resolver_authorization.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/http_resolver_advanced.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/http_resolver_basic.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/http_resolver_endpoint_config.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/keycloak_resolver.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/ldap-resolver.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/radius-server-chain.odg", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/radius-server-chain.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/radius-server-config.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/realm_create_list.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/realm_create_page.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/realm_edit_list_one_node.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/realm_edit_page.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/realm_list_all.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/realm_list_node.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/scim-resolver.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/sms_gateway_new.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/smtp-server-edit.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/smtp_server_edit.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/smtp_server_list.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/sql-resolver.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/system-config.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/images/token-config.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/privacyidea-appliance.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/privacyideaserver_config.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/radiusserver_config.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/realms.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/serviceids.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/sms_gateway_config.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/smtpserver_config.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/system_config.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/token_config.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/email.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/hotp.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/images/email.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/images/hotp.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/images/tiqr.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/images/totp.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/images/yubico.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/images/yubikey.png", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/sms.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/tiqr.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/totp.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/u2f.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/webauthn.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/yubico.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/tokenconfig/yubikey.rst", "downloaded_repos/privacyidea_privacyidea/doc/configuration/useridresolvers.rst", "downloaded_repos/privacyidea_privacyidea/doc/container/container_types.rst", "downloaded_repos/privacyidea_privacyidea/doc/container/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/container/synchronization.rst", "downloaded_repos/privacyidea_privacyidea/doc/container/templates.rst", "downloaded_repos/privacyidea_privacyidea/doc/enroll_yubikey.png", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/containerhandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/counterhandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/customuserattributehandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/event-details.png", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/event-list.png", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/federationhandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/logginghandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/requestmangler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/responsemangler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/scripthandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/tokenhandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/usernotification.rst", "downloaded_repos/privacyidea_privacyidea/doc/eventhandler/webhookhandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/admins.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/brute-force.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/creating-users.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/crypto-considerations.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/customization.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/high-availability.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/images/ha-master-master.odg", "downloaded_repos/privacyidea_privacyidea/doc/faq/images/ha-master-master.png", "downloaded_repos/privacyidea_privacyidea/doc/faq/images/ha-one-dbms.odg", "downloaded_repos/privacyidea_privacyidea/doc/faq/images/ha-one-dbms.png", "downloaded_repos/privacyidea_privacyidea/doc/faq/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/migration-strategies.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/mysqldb.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/performance.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/policies.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/re-encryption.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/resolver.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/rollout-strategies.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/shortcuts.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/time.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/tokenview.rst", "downloaded_repos/privacyidea_privacyidea/doc/faq/translation.rst", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/add_admin.rst", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/images/enroll1.png", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/images/enroll2.png", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/images/realm1.png", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/images/resolver1.png", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/images/resolver2.png", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/images/testtoken.png", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/images/users.png", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/login.rst", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/realm.rst", "downloaded_repos/privacyidea_privacyidea/doc/firststeps/token.rst", "downloaded_repos/privacyidea_privacyidea/doc/glossary/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/images/privacyidea-color.png", "downloaded_repos/privacyidea_privacyidea/doc/images/privacyidea-favicon_book.png", "downloaded_repos/privacyidea_privacyidea/doc/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/centos.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/pip.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/system/inifile.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/system/logging.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/system/pi-manage.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/system/securitymodule.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/system/wsgiscript.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/ubuntu.rst", "downloaded_repos/privacyidea_privacyidea/doc/installation/upgrade.rst", "downloaded_repos/privacyidea_privacyidea/doc/jobqueue/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/machines/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/application.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/audit.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/auth.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/caconnector.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/client.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/container.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/defaultrealm.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/event.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/healthcheck.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/info.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/machineresolver.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/machines.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/monitoring.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/periodictask.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/policy.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/privacyideaserver.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/radiusserver.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/realm.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/recover.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/register.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/resolver.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/serviceid.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/smsgateway.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/smtpserver.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/subscriptions.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/system.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/token.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/tokengroup.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/ttype.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/user.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api/validate.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/api.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/db.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/apipolicy.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/applicationclass.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/audit.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/caconnector.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/containerclass.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/containerfunction.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/containertypes/smartphone.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/containertypes/yubikey.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/eventhandler/base.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/eventhandler/usernotification.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/eventhandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/jobqueue.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/machineresolvers.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/monitoring.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/pinhandler.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/policy.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/policydecorator.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/queueclasses/huey_queue.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/smsprovider/httpprovider.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/smsprovider/sipgateprovider.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/smsprovider/smtpprovider.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/smsprovider.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokenclass.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokenfunction.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/4eyes.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/certificate.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/daplug.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/email.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/hotp.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/motp.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/ocra.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/paper.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/passkey.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/password.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/push.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/questionnaire.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/radius.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/registration.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/remote.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/sms.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/spass.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/ssh.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/tiqr.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/totp.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/u2f.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/vasco.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/webauthn.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/yubico.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/tokentypes/yubikey.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/useridresolvers.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib/users.rst", "downloaded_repos/privacyidea_privacyidea/doc/modules/lib.rst", "downloaded_repos/privacyidea_privacyidea/doc/overview/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/periodictask/eventcounter.rst", "downloaded_repos/privacyidea_privacyidea/doc/periodictask/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/periodictask/periodictasks.png", "downloaded_repos/privacyidea_privacyidea/doc/periodictask/simplestats.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/admin.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/authentication.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/authorization.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/conditions.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/container.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/enrollment.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/images/admin_policies.png", "downloaded_repos/privacyidea_privacyidea/doc/policies/images/default_templates.png", "downloaded_repos/privacyidea_privacyidea/doc/policies/images/policies.png", "downloaded_repos/privacyidea_privacyidea/doc/policies/images/register-dialog.png", "downloaded_repos/privacyidea_privacyidea/doc/policies/images/register-policy.png", "downloaded_repos/privacyidea_privacyidea/doc/policies/images/register.png", "downloaded_repos/privacyidea_privacyidea/doc/policies/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/register.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/templates.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/token.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/user.rst", "downloaded_repos/privacyidea_privacyidea/doc/policies/webui.rst", "downloaded_repos/privacyidea_privacyidea/doc/requirements.txt", "downloaded_repos/privacyidea_privacyidea/doc/spelling_wordlist.txt", "downloaded_repos/privacyidea_privacyidea/doc/todo.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/authentication_modes.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/images/authentication_mode_challenge.svg", "downloaded_repos/privacyidea_privacyidea/doc/tokens/images/authentication_mode_challenge.txt", "downloaded_repos/privacyidea_privacyidea/doc/tokens/images/authentication_mode_outofband.svg", "downloaded_repos/privacyidea_privacyidea/doc/tokens/images/authentication_mode_outofband.txt", "downloaded_repos/privacyidea_privacyidea/doc/tokens/images/authentication_mode_simple.svg", "downloaded_repos/privacyidea_privacyidea/doc/tokens/images/authentication_mode_simple.txt", "downloaded_repos/privacyidea_privacyidea/doc/tokens/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/supported_tokens.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/4eyes.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/application_specific.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/certificate.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/daypassword.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/email.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/hotp.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_4eyes.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_certificate_pkcs12.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_email.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_hotp1.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_hotp2.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_questionnaire.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_radius.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_remote.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_sms.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_tan1.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_tan2.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_tiqr_1.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_tiqr_2.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_totp.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_vasco.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/enroll_yubico.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/generate_csr1.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/generate_csr2.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/generate_csr3.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/push_token_deployment.svg", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/push_token_deployment.txt", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/sshkey.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/images/upload_csr.png", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/indexedsecret.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/motp.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/ocra.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/paper.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/passkey.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/push.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/pw.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/questionnaire.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/radius.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/registration.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/remote.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/sms.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/spass.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/sshkey.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/tan.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/tiqr.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/totp.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/u2f.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/vasco.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/webauthn.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/yubico.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes/yubikey.rst", "downloaded_repos/privacyidea_privacyidea/doc/tokens/tokentypes.rst", "downloaded_repos/privacyidea_privacyidea/doc/webui/container_view.rst", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/componentsview.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_create.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_created_register.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_created_with_template.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_details.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_details_add_tokens.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_details_synchronization.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_list.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_template_create.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_template_details.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_template_details_containers.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_template_details_tokens.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/container_template_list.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/dashboard.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/edit_user_store.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/lost_token_button.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/machinesview.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/token_details.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/token_enroll.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/token_view.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/user_add.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/user_details.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/user_edit.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/images/usersview.png", "downloaded_repos/privacyidea_privacyidea/doc/webui/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/webui/manage_users.rst", "downloaded_repos/privacyidea_privacyidea/doc/webui/token_details.rst", "downloaded_repos/privacyidea_privacyidea/doc/webui/user_attributes.rst", "downloaded_repos/privacyidea_privacyidea/doc/webui/user_details.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/2step/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/container_wizard.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_tools/images/enroll_yubikey.png", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_tools/images/ykpers-log-settings.png", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_tools/images/ykpers-mass-initialize.png", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_tools/images/ykpers-quick-initialize-aes.png", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_tools/images/ykpers-quick-initialize-oath-hotp.png", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_tools/yubikey_enrollment_tools.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_tools.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/enrollment_wizard/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/import/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/import/yubikey.png", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/index.rst", "downloaded_repos/privacyidea_privacyidea/doc/workflows_and_tools/tools/index.rst", "downloaded_repos/privacyidea_privacyidea/pi-manage", "downloaded_repos/privacyidea_privacyidea/po/cs.po", "downloaded_repos/privacyidea_privacyidea/po/de.po", "downloaded_repos/privacyidea_privacyidea/po/es.po", "downloaded_repos/privacyidea_privacyidea/po/fr.po", "downloaded_repos/privacyidea_privacyidea/po/hu.po", "downloaded_repos/privacyidea_privacyidea/po/it.po", "downloaded_repos/privacyidea_privacyidea/po/mn.po", "downloaded_repos/privacyidea_privacyidea/po/nb_NO.po", "downloaded_repos/privacyidea_privacyidea/po/nl.po", "downloaded_repos/privacyidea_privacyidea/po/pl.po", "downloaded_repos/privacyidea_privacyidea/po/pt.po", "downloaded_repos/privacyidea_privacyidea/po/pt_BR.po", "downloaded_repos/privacyidea_privacyidea/po/ro.po", "downloaded_repos/privacyidea_privacyidea/po/ru.po", "downloaded_repos/privacyidea_privacyidea/po/si.po", "downloaded_repos/privacyidea_privacyidea/po/ta.po", "downloaded_repos/privacyidea_privacyidea/po/template.pot", "downloaded_repos/privacyidea_privacyidea/po/tr.po", "downloaded_repos/privacyidea_privacyidea/po/uk.po", "downloaded_repos/privacyidea_privacyidea/po/zh_Hans.po", "downloaded_repos/privacyidea_privacyidea/po/zh_Hant.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/application.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/audit.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/auth.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/before_after.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/caconnector.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/clienttype.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/container.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/event.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/healthcheck.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/info.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/decorators.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/policyhelper.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/postpolicy.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/prepolicy.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/utils.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/machine.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/machineresolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/monitoring.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/periodictask.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/policy.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/privacyideaserver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/radiusserver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/realm.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/recover.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/register.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/resolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/serviceid.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/smsgateway.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/smtpserver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/subscriptions.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/system.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/token.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/tokengroup.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/ttype.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/user.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/api/validate.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/app.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/babel.cfg", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/admin.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/api.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/audit.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/backup.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/challenge.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/main.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/pi_config.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/pi_setup.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pimanage/token.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pitokenjanitor/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pitokenjanitor/main.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pitokenjanitor/utils/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pitokenjanitor/utils/findtokens.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pitokenjanitor/utils/importtokens.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/pitokenjanitor/utils/updatetokens.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/privacyideatokenjanitor/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/privacyideatokenjanitor/findtokens.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/privacyideatokenjanitor/loadtokens.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/privacyideatokenjanitor/main.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/privacyideatokenjanitor/updatetokens.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/tools/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/tools/cron.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/tools/expired_users.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/tools/get_serial.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/tools/get_unused_tokens.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/tools/standalone.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/cli/tools/usercache_cleanup.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/config.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/luks.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/offline.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/applications/ssh.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/apps.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/audit.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/auditmodules/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/auditmodules/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/auditmodules/containeraudit.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/auditmodules/loggeraudit.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/auditmodules/sqlaudit.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/auth.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/authcache.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/cache/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/caconnector.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/caconnectors/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/caconnectors/baseca.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/caconnectors/caservice_pb2.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/caconnectors/caservice_pb2_grpc.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/caconnectors/localca.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/caconnectors/msca.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/challenge.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/challengeresponsedecorators.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/clientapplication.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/config.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/container.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containerclass.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containers/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containers/container_info.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containers/container_states.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containers/smartphone.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containers/smartphone_options.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containers/yubikey.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containertemplate/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containertemplate/containertemplatebase.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containertemplate/smartphonetemplate.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/containertemplate/yubikeytemplate.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/counter.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/crypto.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/decorators.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/error.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/event.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/containerhandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/counterhandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/customuserattributeshandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/federationhandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/logginghandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/requestmangler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/responsemangler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/scripthandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/tokenhandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/usernotification.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/eventhandler/webhookeventhandler.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/fido2/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/fido2/challenge.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/fido2/config.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/fido2/policy_action.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/fido2/token_info.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/fido2/util.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/framework.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/importotp.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/info/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/info/rss.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/lifecycle.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/log.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machine.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machineresolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machines/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machines/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machines/hosts.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/machines/ldap.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/monitoringmodules/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/monitoringmodules/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/monitoringmodules/sqlstats.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/monitoringstats.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/passwordreset.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/periodictask.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/pinhandling/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/pinhandling/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policies/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policies/policy_conditions.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policies/policy_helper.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policy.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policydecorators.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/pooling.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/privacyideaserver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/queue.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/queues/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/queues/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/queues/huey_queue.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/radiusserver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/realm.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/EntraIDResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/HTTPResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/KeycloakResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/LDAPIdResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/PasswdIdResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/SCIMIdResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/SQLIdResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/UserIdResolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/util.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/security/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/security/aeshsm.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/security/default.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/security/encryptkey.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/security/password/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/serviceid.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/FirebaseProvider.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/HttpSMSProvider.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/SMSProvider.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/ScriptSMSProvider.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/SipgateSMSProvider.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/SmppSMSProvider.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/SmtpSMSProvider.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smsprovider/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/smtpserver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/sqlutils.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/subscriptions.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/task/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/task/base.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/task/eventcounter.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/task/simplestats.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/token.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokenclass.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokengroup.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/HMAC.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/applicationspecificpasswordtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/certificatetoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/daplugtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/daypasswordtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/emailtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/foureyestoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/hotptoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/indexedsecrettoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/mOTP.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/motptoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/ocra.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/ocratoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/papertoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/passkeytoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/passwordtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/pushtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/questionnairetoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/radiustoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/registrationtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/remotetoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/smstoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/spasstoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/sshkeytoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/tantoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/tiqrtoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/totptoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/u2f.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/u2ftoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/vasco.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/vascotoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/webauthn.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/webauthntoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/yubicotoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/yubikeytoken.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/user.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/usercache.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/users/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/users/custom_user_attributes.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/compare.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/emailvalidation.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/export.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/messages.pot", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/README", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/alembic.ini", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/env.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/script.py.mako", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/006d4747f858_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/00762b3f7a60_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/04c078e29924_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/0c7123345224_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/140ba0ca4f07_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/145ce80decd_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/14a1bcb10018_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/19f727d285e2_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/1a0710df148b_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/1a69e5e5e2ac_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/1edda52b619f_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/204d8d4f351e_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/205bda834127_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/20969b4cbf06_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/2100d1fad908_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/2118e566df16_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/2181294eed0b_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/22558d9f3178_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/239995464c48_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/250931d82e51_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/2551ee982544_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/2ac117d0a6f5_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/2c9430cfc66b_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/307a4fbe8a05_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/3236a1abf1c6_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/3429d523e51f_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/36428afb2457_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/37e6b49fc686_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/3ae3c668f444_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/3ba618f6b820_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/3c6e9dd7fbac_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/3d7f8b29cbb1_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/3f7e8583ea2_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/4023571658f8_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/41ad6c9ada8b_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/4238eac8ccab_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/449903fb6e35_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/48ee74b8a7c8_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/49a04e560d96_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/4a0aec37e7cf_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/4d9178fa8336_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/4f32a4e1bf33_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/50adc980d625_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/5402fd96fbca_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/5741e5dac477_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/58e4f7ebb705_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/59ef3e03bc62_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/5cb310101a1f_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/5f40baab76ca_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/631ec59e1ca6_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/69e7817b9863_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/7301d5130c3a_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/849170064430_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/86f40f535d7c_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/888b56ed5dcb_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/89e57ed16379_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/8d40dbcfda25_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/903a6ed6f6c4_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/9155f0d3d028_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/a28f2733897b_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/a63df077051a_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/a7e91b18a460_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/b9131d0686eb_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/c128c01a5520_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/cb6d7b7bae63_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/d0e7144947d0_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/d2ae8e54b628_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/d3c0f0403a84_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/d415d490eb05_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/d5870fd2f2a4_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/d6b40a745e5_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/d756b34061ff_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/db6b2ef8100f_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/dceb6cd3c41e_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/e360c56bcf8c_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/e3a64b4ca634_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/e5cbeb7c177_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/eac770c0bbed_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/ef29ba43e290_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/fa07bd604a75.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/fabcf24d9304_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/migrations/versions/ff26585932ec_.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/audit.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/cache.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/caconnector.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/challenge.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/config.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/customuserattribute.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/db.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/event.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/eventcounter.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/machine.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/monitoringstats.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/periodictask.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/policy.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/realm.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/resolver.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/server.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/serviceid.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/smsgateway.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/subscription.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/token.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/tokencontainer.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/tokengroup.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/models/utils.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/README.md", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/app.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/audit/controllers/auditControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/audit/factories/audit.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/audit/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/audit/views/audit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/audit/views/audit.log.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/components/controllers/componentControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/components/factories/component.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/components/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/components/views/component.clienttype.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/components/views/component.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/components/views/component.subscriptions.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/configControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/eventController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/ldapMachineResolverController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/periodicTaskController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/privacyideaServerController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/radiusServerController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/realmController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/serviceidController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/smsgatewayController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/smtpServerController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/tokengroupController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/factories/config.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.caconnectors.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.caconnectors.local.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.caconnectors.microsoft.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.events.details.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.events.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.events.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.machineresolvers.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.mresolvers.hosts.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.mresolvers.ldap.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.mresolvers.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.periodictasks.details.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.periodictasks.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.periodictasks.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.policies.details.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.policies.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.policies.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.privacyideaserver.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.privacyideaserver.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.radius.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.radius.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.realms.create.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.realms.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.realms.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.http.advanced.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.http.basic.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.http.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.ldap.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.passwd.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.scim.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.resolvers.sql.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.serviceid.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.serviceid.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.smsgateway.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.smsgateway.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.smtp.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.smtp.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.system.doc.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.system.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.system.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.daypassword.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.email.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.hotp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.question.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.radius.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.remote.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.sms.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.tiqr.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.totp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.u2f.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.webauthn.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.yubico.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.token.yubikey.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.tokengroup.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.tokengroup.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/config.tokens.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/views/dialog.confirm_delete.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dashboard/controllers/dashboardControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dashboard/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dashboard/views/dashboard.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dialogs/views/dialog.about.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dialogs/views/dialog.autocreate_realm.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dialogs/views/dialog.lock.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dialogs/views/dialog.no.token.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/dialogs/views/dialog.welcome.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/controllers/directives.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.assigntoken.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.assignuser.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.attachmachine.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.attachtoken.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.containerTemplateDiff.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.containertemplate.details.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.csvdownload.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.filter.table.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.httpResolverEndpointConfig.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.otpList.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.policyconditions.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.registerContainer.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.selectorcreatecontainer.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.selectresolver.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.tokendata.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/views/directive.verifyEnrolledToken.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/filters/filters.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/info/controllers/infoController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/info/factories/info.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/info/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/info/views/info.rss.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/controllers/loginControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/factories/auth.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/factories/u2f.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/factories/webauthn.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/views/enter-response.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/views/login.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/views/offline.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/views/pinchange.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/machine/controllers/machineController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/machine/controllers/machineDetailsController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/machine/factories/machine.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/machine/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/machine/views/machine.details.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/machine/views/machine.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/machine/views/machine.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/recovery/controllers/recoveryControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/recovery/factories/recoveryFactory.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/recovery/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/recovery/views/recovery.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/recovery/views/recovery.reset.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/register/controllers/registerControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/register/factories/registerFactory.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/register/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/register/views/register.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/containerControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/containerTemplateControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenApplicationsController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenChallengesController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenDetailController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenGetSerialController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenLostController.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/factories/container.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/factories/token.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/factories/validate.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/dialog.ask_token_delete.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.applications.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.applications.offline.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.applications.ssh.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.assign.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.challenges.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.containercreate.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.containerdetails.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.containerlist.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.containertemplates.create.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.containertemplates.edit.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.containertemplates.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.details.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.display.apps.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.4eyes.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.applspec.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.certificate.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.daypassword.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.email.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.hotp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.indexedsecret.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.motp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.paper.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.push.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.question.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.radius.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.registration.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.remote.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.sms.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.spass.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.sshkey.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.tan.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.tiqr.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.totp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.u2f.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.vasco.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.webauthn.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.yubico.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enroll.yubikey.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.applspec.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.certificate.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.daypassword.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.hotp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.motp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.paper.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.push.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.registration.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.tan.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.tiqr.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.totp.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.u2f.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.enrolled.webauthn.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.getserial.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.import.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/views/token.lost.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/controllers/userControllers.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/factories/user.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/states/states.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/views/dialog.ask_user_delete.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/views/user.add.dynamic.form.fields.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/views/user.add.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/views/user.details.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/views/user.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/views/user.list.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/user/views/user.password.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/angular-inform.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/angular-inform.css.map", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/bootstrap-theme.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/bootstrap-theme.css.map", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/bootstrap.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/bootstrap.css.map", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/hotkeys.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/css/isteven-multi-select.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/fonts/glyphicons-halflings-regular.eot", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/fonts/glyphicons-halflings-regular.svg", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/fonts/glyphicons-halflings-regular.ttf", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/fonts/glyphicons-halflings-regular.woff", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/fonts/glyphicons-halflings-regular.woff2", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/angular-inform.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/angular-inform.js.map", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/bootstrap.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/hotkeys.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-gettext.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-idle.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-sanitize.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js.map", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/isteven-multi-select.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/u2f-api.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/webauthn-client/pi-webauthn.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/baseline.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/content.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/generic.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/highlight.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/menu.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/papertoken.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/privacyIDEA1.png", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/signin.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/css/table-ui.css", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/customize/README.rst", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/favicon.png", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/img/FIDO-U2F-Security-Key-444x444.png", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/img/plugup.jpg", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/img/solokeys.png", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/img/u2fzero.png", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/package-lock.json", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/package.json", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/providers/errorMessageProvider.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/providers/versioningProvider.js", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/baseline.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/deactivated.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/documentation.rst", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/footer.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/index.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/menu.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html", "downloaded_repos/privacyidea_privacyidea/privacyidea/static/update_contrib.sh", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/cs/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/cs/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/de/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/de/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/es/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/es/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/fr/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/fr/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/it/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/it/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/nl/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/nl/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/pl/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/pl/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/pt/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/pt/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/pt_BR/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/pt_BR/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/ro/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/ro/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/ru/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/ru/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/si/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/si/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/ta/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/ta/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/tr/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/tr/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/uk/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/uk/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/zh_Hans/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/zh_Hans/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/zh_Hant/LC_MESSAGES/messages.mo", "downloaded_repos/privacyidea_privacyidea/privacyidea/translations/zh_Hant/LC_MESSAGES/messages.po", "downloaded_repos/privacyidea_privacyidea/privacyidea/webui/__init__.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/webui/certificate.py", "downloaded_repos/privacyidea_privacyidea/privacyidea/webui/login.py", "downloaded_repos/privacyidea_privacyidea/pyproject.toml", "downloaded_repos/privacyidea_privacyidea/requirements-hsm.txt", "downloaded_repos/privacyidea_privacyidea/requirements-kerberos.txt", "downloaded_repos/privacyidea_privacyidea/requirements.txt", "downloaded_repos/privacyidea_privacyidea/tools/creategoogleauthenticator-file", "downloaded_repos/privacyidea_privacyidea/tools/getgooglecodes", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-convert-base32.py", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-convert-token", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-convert-token.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-convert-xml-to-csv", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-ad-users", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-ad-users.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-certificate", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-certificate.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-pwidresolver-user", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-pwidresolver-user.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-userdb", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-create-userdb.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-diag", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-export-linotp-counter.py", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-export-privacyidea-counter.py", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fix-access-rights.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-migrate-linotp.py", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-pip-update", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-pip-update.1", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-queue-huey", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-schema-upgrade", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-sync-owncloud.py", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-update-counter.py", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-update-linotp-counter.py", "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-user-action", "downloaded_repos/privacyidea_privacyidea/tools/reset-privacyidea", "downloaded_repos/privacyidea_privacyidea/tools/ssha.py"], "skipped": [{"path": "downloaded_repos/privacyidea_privacyidea/.circleci/config.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policy.py", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenDetailController.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/translation/translations.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/angular.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/jquery.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-ui-router.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ui-bootstrap-tpls.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/cert_request_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/header.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/templates/token_enrolled.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_cron.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_expired_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_pi_get_serial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_pi_get_unused_tokens.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_pi_standalone.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_pimanage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_pitokenjanitor.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_privacyideatokenjanitor.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/cli/test_cli_usercache_cleanup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/ldap3mock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/mscamock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/passkey_base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/pkcs11mock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/queuemock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/radiusmock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/redismock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/requirements.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/smppmock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/smtpmock.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_2stepinit.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_applications.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_audit.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_caconnector.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_clienttype.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_container.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_events.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_healthcheck.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_info.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_lib_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_lib_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_machineresolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_machines.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_machines_serviceid.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_monitoring.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_offline_no_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_passkey.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_periodictask.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_privacyideaserver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_push_validate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_radiusserver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_register.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_serviceids.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_smsgateway.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_smtpserver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_subscriptions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_system.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_tokengroup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_ttype.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_u2f.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_validate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_api_validate_webauthn.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_app.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_db_model.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_applications.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_audit.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_authcache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_caconnector.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_challenges.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_clientapplication.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_counter.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_crypto.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_eventhandler_logging.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_eventhandler_usernotification.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_events.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_framework.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_importotp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_info.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_lifecycle.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_machine_resolver_ldap.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_machines.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_machinetokens.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_monitoringstats.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_periodictask.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_policydecorator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_pooling.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_privacyideaserver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_queue.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_radiusserver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_realm.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_recovery.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_resolver_httpresolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_serviceid.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_smsprovider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_smtpserver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_sqlutils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_subscriptions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_task_eventcounter.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_task_simplestats.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tasks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokenclass.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokencontainer.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokengroup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_certificate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_daplug.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_daypassword.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_email.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_foureyes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_hotp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_indexedsecret.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_motp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_paper.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_passkey.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_passkeytoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_passwordtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_push.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_questionnaire.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_radius.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_registration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_remote.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_sms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_spass.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_ssh.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_tan.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_tiqr.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_totp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_u2f.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_vasco.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_webauthn.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_yubico.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_tokens_yubikey.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_usercache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_lib_utils_compare.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_mock_ldap3.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_mod_apache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_resolver_realm.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_scripts.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_ui_certificate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/test_ui_login.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/FIDO-U2F-Security-Key-444x444.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/NetKnights.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/altstatic/templates/testui.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/attestation/yubico.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/audit.sqlite", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ca/cacert.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ca/cakey.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ca/index.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ca/index.txt.attr", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ca/openssl.cnf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ca/serial", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ca/templates.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/dictionary", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/does_not_exist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/emailtemplate.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/empty.oath", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/enckey", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/enckey.enc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/fancytoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/firebase-test.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/gmailvalidator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/google-services.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/gpg/public.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/gpg/pubring.gpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/gpg/random_seed", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/gpg/secring.gpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/gpg/trustdb.gpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/hosts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/import.oath", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/import.oath.asc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/jwt_sign.key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/logging.cfg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/logging.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/logging_broken.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/msca-connector/ca.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/msca-connector/privacyidea-encrypted.key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/msca-connector/privacyidea.key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/msca-connector/privacyidea.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/ocra.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/passwd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/passwd-duplicate-name", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/passwords", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/policy.cfg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/policy_empty_file.cfg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/private.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/pskc-aes.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/pskc-password.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/public.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/pw-2nd-resolver", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/scripts/fail.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/scripts/ls.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/scripts/success.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/test.sub", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/test2.sub", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/testuser-api.sqlite", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/testuser.sqlite", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/testusercache.sqlite", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/trusted_attestation_roots/solokeys_device_attestation_ca.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/trusted_attestation_roots/yubico_u2f_device_attestation_ca.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/yubico-oath-long.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/yubico-oath.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tests/testdata/yubico.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-authorizedkeys", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/privacyidea_privacyidea/tools/privacyidea-fetchssh", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.9986770153045654, "profiling_times": {"config_time": 7.119089841842651, "core_time": 35.51545548439026, "ignores_time": 0.002915620803833008, "total_time": 42.638834714889526}, "parsing_time": {"total_time": 23.91193723678589, "per_file_time": {"mean": 0.04616204099765617, "std_dev": 0.018397788600387797}, "very_slow_stats": {"time_ratio": 0.4504356066040843, "count_ratio": 0.02895752895752896}, "very_slow_files": [{"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/angular-idle.js", "ftime": 0.5095150470733643}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/directives/controllers/directives.js", "ftime": 0.5307669639587402}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/prepolicy.py", "ftime": 0.5811841487884521}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenControllers.js", "ftime": 0.6816270351409912}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/factories/config.js", "ftime": 0.8589367866516113}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policy.py", "ftime": 0.91969895362854}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/isteven-multi-select.js", "ftime": 1.055039882659912}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/bootstrap.js", "ftime": 1.1725730895996094}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/controllers/configControllers.js", "ftime": 1.211064100265503}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "ftime": 1.217432975769043}]}, "scanning_time": {"total_time": 454.41227746009827, "per_file_time": {"mean": 0.15021893469755332, "std_dev": 0.9599876983291548}, "very_slow_stats": {"time_ratio": 0.7014545873413219, "count_ratio": 0.02115702479338843}, "very_slow_files": [{"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/pushtoken.py", "ftime": 8.246397018432617}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/postpolicy.py", "ftime": 8.296299934387207}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/bootstrap.js", "ftime": 8.328366041183472}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/token/controllers/tokenControllers.js", "ftime": 8.563292980194092}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/__init__.py", "ftime": 8.890186071395874}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/isteven-multi-select.js", "ftime": 9.365363121032715}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/controllers/loginControllers.js", "ftime": 9.398068904876709}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "ftime": 20.78276515007019}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policy.py", "ftime": 21.755423069000244}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/prepolicy.py", "ftime": 24.135812997817993}]}, "matching_time": {"total_time": 202.36788749694824, "per_file_and_rule_time": {"mean": 0.03148224758819979, "std_dev": 0.013103060427732979}, "very_slow_stats": {"time_ratio": 0.6731558612845326, "count_ratio": 0.06627255756067206}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/factories/config.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.258329153060913}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/policy.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 1.4040391445159912}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.5715060234069824}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/prepolicy.py", "rule_id": "python.django.security.injection.sql.sql-injection-using-db-cursor-execute.sql-injection-db-cursor-execute", "time": 1.6074860095977783}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/utils/__init__.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.6463909149169922}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/resolvers/HTTPResolver.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.7206339836120605}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/prepolicy.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 2.0642638206481934}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/config/states/states.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 2.1358301639556885}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/api/lib/prepolicy.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 2.607602834701538}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/lib/tokens/pushtoken.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 2.8600728511810303}]}, "tainting_time": {"total_time": 97.82706904411316, "per_def_and_rule_time": {"mean": 0.004000289063345456, "std_dev": 0.0013092091799696143}, "very_slow_stats": {"time_ratio": 0.47479744214116565, "count_ratio": 0.010263749744428542}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/isteven-multi-select.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.5188920497894287}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/bootstrap.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.5296239852905273}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.6027348041534424}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.6375668048858643}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/isteven-multi-select.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.6698219776153564}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.7547521591186523}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/ng-file-upload.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.9822380542755127}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/webauthn-client/pi-webauthn.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 1.0576128959655762}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/contrib/js/ngmodules/isteven-multi-select.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 1.697732925415039}, {"fpath": "downloaded_repos/privacyidea_privacyidea/privacyidea/static/components/login/controllers/loginControllers.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 4.119668006896973}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1121681088}, "engine_requested": "OSS", "skipped_rules": []}