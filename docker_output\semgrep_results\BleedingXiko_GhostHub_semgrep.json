{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/BleedingXiko_GhostHub/Dockerfile", "start": {"line": 72, "col": 1, "offset": 1907}, "end": {"line": 72, "col": 77, "offset": 1983}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD python /app/scripts/docker-init.py && python /app/docker-media-server.py", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-file-permissions.insecure-file-permissions", "path": "downloaded_repos/BleedingXiko_GhostHub/app/utils/server_utils.py", "start": {"line": 332, "col": 21, "offset": 13891}, "end": {"line": 332, "col": 45, "offset": 13915}, "extra": {"message": "These permissions `0o700` are widely permissive and grant access to more people than may be necessary. A good default is `0o644` which gives read and write access to yourself and read access to everyone else.", "metadata": {"category": "security", "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-276: Incorrect Default Permissions"], "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-file-permissions.insecure-file-permissions", "shortlink": "https://sg.run/AXY4"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/main.js", "start": {"line": 102, "col": 25, "offset": 4325}, "end": {"line": 102, "col": 331, "offset": 4631}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/adminController.js", "start": {"line": 57, "col": 9, "offset": 3160}, "end": {"line": 57, "col": 53, "offset": 3204}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/adminController.js", "start": {"line": 61, "col": 9, "offset": 3378}, "end": {"line": 61, "col": 47, "offset": 3416}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/adminController.js", "start": {"line": 65, "col": 9, "offset": 3543}, "end": {"line": 65, "col": 50, "offset": 3584}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/categoryManager.js", "start": {"line": 84, "col": 13, "offset": 4086}, "end": {"line": 86, "col": 15, "offset": 4240}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/categoryManager.js", "start": {"line": 278, "col": 13, "offset": 11490}, "end": {"line": 278, "col": 87, "offset": 11564}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/chatManager.js", "start": {"line": 619, "col": 25, "offset": 20174}, "end": {"line": 619, "col": 75, "offset": 20224}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/chatManager.js", "start": {"line": 705, "col": 5, "offset": 23334}, "end": {"line": 718, "col": 7, "offset": 23836}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/chatManager.js", "start": {"line": 995, "col": 9, "offset": 33694}, "end": {"line": 1008, "col": 11, "offset": 34248}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/chatManager.js", "start": {"line": 1057, "col": 9, "offset": 36765}, "end": {"line": 1061, "col": 11, "offset": 37022}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/commandHandler.js", "start": {"line": 97, "col": 19, "offset": 3079}, "end": {"line": 97, "col": 60, "offset": 3120}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/commandPopup.js", "start": {"line": 560, "col": 23, "offset": 17555}, "end": {"line": 560, "col": 63, "offset": 17595}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/configModal.js", "start": {"line": 228, "col": 13, "offset": 9621}, "end": {"line": 228, "col": 79, "offset": 9687}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaLoader.js", "start": {"line": 754, "col": 9, "offset": 33887}, "end": {"line": 754, "col": 135, "offset": 34013}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaNavigation.js", "start": {"line": 89, "col": 43, "offset": 3940}, "end": {"line": 89, "col": 106, "offset": 4003}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaNavigation.js", "start": {"line": 92, "col": 75, "offset": 4251}, "end": {"line": 92, "col": 133, "offset": 4309}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaNavigation.js", "start": {"line": 100, "col": 32, "offset": 4731}, "end": {"line": 100, "col": 88, "offset": 4787}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaNavigation.js", "start": {"line": 541, "col": 23, "offset": 22460}, "end": {"line": 541, "col": 55, "offset": 22492}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "start": {"line": 327, "col": 49, "offset": 14766}, "end": {"line": 327, "col": 88, "offset": 14805}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "start": {"line": 700, "col": 23, "offset": 30024}, "end": {"line": 700, "col": 63, "offset": 30064}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "start": {"line": 752, "col": 41, "offset": 33166}, "end": {"line": 752, "col": 84, "offset": 33209}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "start": {"line": 781, "col": 31, "offset": 35073}, "end": {"line": 781, "col": 84, "offset": 35126}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/tunnelModal.js", "start": {"line": 141, "col": 17, "offset": 6202}, "end": {"line": 141, "col": 61, "offset": 6246}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "start": {"line": 6, "col": 21172, "offset": 21267}, "end": {"line": 6, "col": 21199, "offset": 21294}, "extra": {"message": "`a.authority.replace` method will only replace the first occurrence when used with a string argument (\"[\"). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "start": {"line": 6, "col": 21172, "offset": 21267}, "end": {"line": 6, "col": 21215, "offset": 21310}, "extra": {"message": "`a.authority.replace(\"[\",\"\").replace` method will only replace the first occurrence when used with a string argument (\"]\"). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/configManager.js", "start": {"line": 50, "col": 13, "offset": 2205}, "end": {"line": 50, "col": 31, "offset": 2223}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/BleedingXiko_GhostHub/.github/FUNDING.yml", "downloaded_repos/BleedingXiko_GhostHub/.gitignore", "downloaded_repos/BleedingXiko_GhostHub/Dockerfile", "downloaded_repos/BleedingXiko_GhostHub/LICENSE", "downloaded_repos/BleedingXiko_GhostHub/README.md", "downloaded_repos/BleedingXiko_GhostHub/THIRD_PARTY_LICENSES.md", "downloaded_repos/BleedingXiko_GhostHub/app/__init__.py", "downloaded_repos/BleedingXiko_GhostHub/app/config.py", "downloaded_repos/BleedingXiko_GhostHub/app/constants.py", "downloaded_repos/BleedingXiko_GhostHub/app/models/__init__.py", "downloaded_repos/BleedingXiko_GhostHub/app/routes/__init__.py", "downloaded_repos/BleedingXiko_GhostHub/app/routes/api_routes.py", "downloaded_repos/BleedingXiko_GhostHub/app/routes/main_routes.py", "downloaded_repos/BleedingXiko_GhostHub/app/routes/media_routes.py", "downloaded_repos/BleedingXiko_GhostHub/app/routes/sync_routes.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/__init__.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/category_service.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/config_service.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/indexing_service.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/media_service.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/progress_service.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/streaming_service.py", "downloaded_repos/BleedingXiko_GhostHub/app/services/sync_service.py", "downloaded_repos/BleedingXiko_GhostHub/app/socket_events.py", "downloaded_repos/BleedingXiko_GhostHub/app/utils/__init__.py", "downloaded_repos/BleedingXiko_GhostHub/app/utils/cache_utils.py", "downloaded_repos/BleedingXiko_GhostHub/app/utils/file_utils.py", "downloaded_repos/BleedingXiko_GhostHub/app/utils/log_utils.py", "downloaded_repos/BleedingXiko_GhostHub/app/utils/media_utils.py", "downloaded_repos/BleedingXiko_GhostHub/app/utils/server_utils.py", "downloaded_repos/BleedingXiko_GhostHub/app/utils/system_utils.py", "downloaded_repos/BleedingXiko_GhostHub/bin/build_exe.bat", "downloaded_repos/BleedingXiko_GhostHub/bin/start_server.bat", "downloaded_repos/BleedingXiko_GhostHub/bin/test_exe.bat", "downloaded_repos/BleedingXiko_GhostHub/docker/.dockerignore", "downloaded_repos/BleedingXiko_GhostHub/docker/docker-compose.yml", "downloaded_repos/BleedingXiko_GhostHub/docker-media-server.py", "downloaded_repos/BleedingXiko_GhostHub/ghosthub.py", "downloaded_repos/BleedingXiko_GhostHub/ghosthub.spec", "downloaded_repos/BleedingXiko_GhostHub/hooks/hook-dns.py", "downloaded_repos/BleedingXiko_GhostHub/media/README.md", "downloaded_repos/BleedingXiko_GhostHub/nginx.conf.example", "downloaded_repos/BleedingXiko_GhostHub/preview.png", "downloaded_repos/BleedingXiko_GhostHub/requirements.txt", "downloaded_repos/BleedingXiko_GhostHub/scripts/docker-init.py", "downloaded_repos/BleedingXiko_GhostHub/scripts/generate_icons.py", "downloaded_repos/BleedingXiko_GhostHub/static/css/base.css", "downloaded_repos/BleedingXiko_GhostHub/static/css/chat.css", "downloaded_repos/BleedingXiko_GhostHub/static/css/components.css", "downloaded_repos/BleedingXiko_GhostHub/static/css/layout.css", "downloaded_repos/BleedingXiko_GhostHub/static/css/media.css", "downloaded_repos/BleedingXiko_GhostHub/static/css/responsive.css", "downloaded_repos/BleedingXiko_GhostHub/static/css/styles.css", "downloaded_repos/BleedingXiko_GhostHub/static/icons/Ghosthub.ico", "downloaded_repos/BleedingXiko_GhostHub/static/icons/Ghosthub180.png", "downloaded_repos/BleedingXiko_GhostHub/static/icons/Ghosthub192.png", "downloaded_repos/BleedingXiko_GhostHub/static/icons/Ghosthub512.png", "downloaded_repos/BleedingXiko_GhostHub/static/js/commands/help.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/commands/index.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/commands/kick.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/commands/myview.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/commands/random.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/commands/view.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/core/app.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/core/configDescriptions.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/main.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/adminController.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/categoryManager.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/chatManager.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/commandHandler.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/commandPopup.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/configModal.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/eventHandlers.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/fullscreenManager.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaLoader.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaNavigation.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/tunnelModal.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/uiController.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/pwa-installer.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/authManager.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/cacheManager.js", "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/configManager.js", "downloaded_repos/BleedingXiko_GhostHub/static/manifest.json", "downloaded_repos/BleedingXiko_GhostHub/static/sw.js", "downloaded_repos/BleedingXiko_GhostHub/templates/add_category.html", "downloaded_repos/BleedingXiko_GhostHub/templates/index.html", "downloaded_repos/BleedingXiko_GhostHub/wsgi.py"], "skipped": [{"path": "downloaded_repos/BleedingXiko_GhostHub/preview-mobile-2.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/BleedingXiko_GhostHub/preview-mobile.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/BleedingXiko_GhostHub/static/icons/Ghosthub1024.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/BleedingXiko_GhostHub/tests/test_ghosthub.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 3.8189289569854736, "profiling_times": {"config_time": 7.5103747844696045, "core_time": 14.564888954162598, "ignores_time": 0.001626729965209961, "total_time": 22.078198194503784}, "parsing_time": {"total_time": 4.62591552734375, "per_file_time": {"mean": 0.07116793118990383, "std_dev": 0.0336667248425064}, "very_slow_stats": {"time_ratio": 0.4607072718891425, "count_ratio": 0.046153846153846156}, "very_slow_files": [{"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "ftime": 0.348344087600708}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaLoader.js", "ftime": 0.38711094856262207}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "ftime": 1.395737886428833}]}, "scanning_time": {"total_time": 41.37627291679382, "per_file_time": {"mean": 0.1702727280526493, "std_dev": 0.5412691750880164}, "very_slow_stats": {"time_ratio": 0.667800687877087, "count_ratio": 0.037037037037037035}, "very_slow_files": [{"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaNavigation.js", "ftime": 1.9832839965820312}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/routes/api_routes.py", "ftime": 1.9925990104675293}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "ftime": 2.1036691665649414}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/services/streaming_service.py", "ftime": 2.18910813331604}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/services/media_service.py", "ftime": 2.245800018310547}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/socket_events.py", "ftime": 2.295248031616211}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaLoader.js", "ftime": 2.495180130004883}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/utils/server_utils.py", "ftime": 2.989556074142456}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "ftime": 9.336658954620361}]}, "matching_time": {"total_time": 20.421875476837158, "per_file_and_rule_time": {"mean": 0.025752680298659715, "std_dev": 0.00680923457069931}, "very_slow_stats": {"time_ratio": 0.6140376840012033, "count_ratio": 0.058007566204287514}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.3706400394439697}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/utils/server_utils.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.37667298316955566}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/socket_events.py", "rule_id": "python.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.4080691337585449}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/services/media_service.py", "rule_id": "python.flask.security.injection.raw-html-concat.raw-html-format", "time": 0.41602301597595215}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.4166219234466553}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/syncManager.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.43639302253723145}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/socket_events.py", "rule_id": "python.flask.security.injection.raw-html-concat.raw-html-format", "time": 0.47265195846557617}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/modules/mediaLoader.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6518411636352539}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/services/streaming_service.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.8363111019134521}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.3736319541931152}]}, "tainting_time": {"total_time": 10.583317518234253, "per_def_and_rule_time": {"mean": 0.0032554037275405274, "std_dev": 0.00023543963894246672}, "very_slow_stats": {"time_ratio": 0.3111567603899386, "count_ratio": 0.005536757920639803}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "time": 0.14108586311340332}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.16759300231933594}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.17909598350524902}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/utils/media_utils.py", "fline": 301, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.18463802337646484}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.22272992134094238}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/app/services/indexing_service.py", "fline": 102, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.2269279956817627}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.23383593559265137}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.29350900650024414}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.32872700691223145}, {"fpath": "downloaded_repos/BleedingXiko_GhostHub/static/js/utils/SocketIoMin.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.3806488513946533}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}