{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/JibayMcs_filament-tour/bin/build.js", "start": {"line": 37, "col": 33, "offset": 1003}, "end": {"line": 37, "col": 126, "offset": 1096}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/JibayMcs_filament-tour/resources/js/index.js", "start": {"line": 69, "col": 21, "offset": 1838}, "end": {"line": 69, "col": 58, "offset": 1875}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/JibayMcs_filament-tour/resources/js/index.js", "start": {"line": 117, "col": 21, "offset": 3562}, "end": {"line": 117, "col": 78, "offset": 3619}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/JibayMcs_filament-tour/resources/js/index.js", "start": {"line": 234, "col": 21, "offset": 8200}, "end": {"line": 234, "col": 78, "offset": 8257}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 38, "offset": 934}, "end": {"line": 38, "col": 57, "offset": 953}}, {"path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 24, "offset": 934}, "end": {"line": 39, "col": 43, "offset": 953}}]], "message": "Syntax error at line downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml:38:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 38, "offset": 934}, "end": {"line": 38, "col": 57, "offset": 953}}, {"file": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 24, "offset": 934}, "end": {"line": 39, "col": 43, "offset": 953}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 53, "offset": 1121}, "end": {"line": 43, "col": 69, "offset": 1137}}, {"path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 97, "offset": 1121}, "end": {"line": 43, "col": 115, "offset": 1139}}, {"path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 137, "offset": 1121}, "end": {"line": 43, "col": 152, "offset": 1136}}, {"path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 19, "offset": 1121}, "end": {"line": 44, "col": 22, "offset": 1124}}]], "message": "Syntax error at line downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml:43:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 53, "offset": 1121}, "end": {"line": 43, "col": 69, "offset": 1137}}, {"file": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 97, "offset": 1121}, "end": {"line": 43, "col": 115, "offset": 1139}}, {"file": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 137, "offset": 1121}, "end": {"line": 43, "col": 152, "offset": 1136}}, {"file": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 19, "offset": 1121}, "end": {"line": 44, "col": 22, "offset": 1124}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 35, "col": 59, "offset": 0}, "end": {"line": 36, "col": 24, "offset": 25}}, {"path": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 36, "col": 26, "offset": 0}, "end": {"line": 36, "col": 58, "offset": 32}}, {"path": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 38, "col": 82, "offset": 0}, "end": {"line": 38, "col": 83, "offset": 1}}, {"path": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 41, "col": 13, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php:35:\n `;\n                foreach` was unexpected", "path": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "spans": [{"file": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 35, "col": 59, "offset": 0}, "end": {"line": 36, "col": 24, "offset": 25}}, {"file": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 36, "col": 26, "offset": 0}, "end": {"line": 36, "col": 58, "offset": 32}}, {"file": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 38, "col": 82, "offset": 0}, "end": {"line": 38, "col": 83, "offset": 1}}, {"file": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "start": {"line": 41, "col": 13, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 6}}]}], "paths": {"scanned": ["downloaded_repos/JibayMcs_filament-tour/.editorconfig", "downloaded_repos/JibayMcs_filament-tour/.gitattributes", "downloaded_repos/JibayMcs_filament-tour/.github/CONTRIBUTING.md", "downloaded_repos/JibayMcs_filament-tour/.github/FUNDING.yml", "downloaded_repos/JibayMcs_filament-tour/.github/ISSUE_TEMPLATE/bug.yml", "downloaded_repos/JibayMcs_filament-tour/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/JibayMcs_filament-tour/.github/SECURITY.md", "downloaded_repos/JibayMcs_filament-tour/.github/workflows/fix-php-code-styling.yml", "downloaded_repos/JibayMcs_filament-tour/.github/workflows/phpstan.yml", "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "downloaded_repos/JibayMcs_filament-tour/.github/workflows/update-changelog.yml", "downloaded_repos/JibayMcs_filament-tour/.gitignore", "downloaded_repos/JibayMcs_filament-tour/.prettierrc", "downloaded_repos/JibayMcs_filament-tour/CHANGELOG.md", "downloaded_repos/JibayMcs_filament-tour/LICENSE.md", "downloaded_repos/JibayMcs_filament-tour/README.md", "downloaded_repos/JibayMcs_filament-tour/bin/build.js", "downloaded_repos/JibayMcs_filament-tour/composer.json", "downloaded_repos/JibayMcs_filament-tour/config/filament-tour.php", "downloaded_repos/JibayMcs_filament-tour/database/factories/ModelFactory.php", "downloaded_repos/JibayMcs_filament-tour/database/migrations/create_tour_table.php.stub", "downloaded_repos/JibayMcs_filament-tour/docs/INDEX.md", "downloaded_repos/JibayMcs_filament-tour/package.json", "downloaded_repos/JibayMcs_filament-tour/phpstan-baseline.neon", "downloaded_repos/JibayMcs_filament-tour/phpstan.neon.dist", "downloaded_repos/JibayMcs_filament-tour/phpunit.xml.dist", "downloaded_repos/JibayMcs_filament-tour/postcss.config.cjs", "downloaded_repos/JibayMcs_filament-tour/resources/css/index.css", "downloaded_repos/JibayMcs_filament-tour/resources/js/css-selector.js", "downloaded_repos/JibayMcs_filament-tour/resources/js/index.js", "downloaded_repos/JibayMcs_filament-tour/resources/lang/ar/filament-tour.php", "downloaded_repos/JibayMcs_filament-tour/resources/lang/de/filament-tour.php", "downloaded_repos/JibayMcs_filament-tour/resources/lang/en/filament-tour.php", "downloaded_repos/JibayMcs_filament-tour/resources/lang/fr/filament-tour.php", "downloaded_repos/JibayMcs_filament-tour/resources/lang/pt_BR/filament-tour.php", "downloaded_repos/JibayMcs_filament-tour/resources/lang/pt_PT/filament-tour.php", "downloaded_repos/JibayMcs_filament-tour/resources/views/.gitkeep", "downloaded_repos/JibayMcs_filament-tour/resources/views/highlight/button.blade.php", "downloaded_repos/JibayMcs_filament-tour/resources/views/livewire/filament-tour-widget.blade.php", "downloaded_repos/JibayMcs_filament-tour/resources/views/tour/step/popover/title.blade.php", "downloaded_repos/JibayMcs_filament-tour/src/FilamentTourPlugin.php", "downloaded_repos/JibayMcs_filament-tour/src/FilamentTourServiceProvider.php", "downloaded_repos/JibayMcs_filament-tour/src/Highlight/HasHighlight.php", "downloaded_repos/JibayMcs_filament-tour/src/Highlight/Highlight.php", "downloaded_repos/JibayMcs_filament-tour/src/Livewire/FilamentTourWidget.php", "downloaded_repos/JibayMcs_filament-tour/src/Tour/HasTour.php", "downloaded_repos/JibayMcs_filament-tour/src/Tour/Step/StepEvent.php", "downloaded_repos/JibayMcs_filament-tour/src/Tour/Step.php", "downloaded_repos/JibayMcs_filament-tour/src/Tour/Tour.php", "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanReadJson.php", "downloaded_repos/JibayMcs_filament-tour/stubs/.gitkeep", "downloaded_repos/JibayMcs_filament-tour/tailwind.config.js"], "skipped": [{"path": "downloaded_repos/JibayMcs_filament-tour/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/JibayMcs_filament-tour/resources/dist/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/JibayMcs_filament-tour/resources/dist/filament-tour.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/JibayMcs_filament-tour/resources/dist/filament-tour.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/JibayMcs_filament-tour/src/Tour/Traits/CanConstructRoute.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/JibayMcs_filament-tour/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/JibayMcs_filament-tour/tests/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/JibayMcs_filament-tour/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/JibayMcs_filament-tour/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.539870023727417, "profiling_times": {"config_time": 5.42560338973999, "core_time": 2.5579864978790283, "ignores_time": 0.0020465850830078125, "total_time": 7.98655366897583}, "parsing_time": {"total_time": 0.41037559509277344, "per_file_time": {"mean": 0.011399322085910374, "std_dev": 0.00019256474345155894}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.2688632011413574, "per_file_time": {"mean": 0.008935656346065898, "std_dev": 0.0010342713269466562}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.41434788703918457, "per_file_and_rule_time": {"mean": 0.001506719589233399, "std_dev": 2.5336988365564724e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.07966303825378418, "per_def_and_rule_time": {"mean": 0.0005989702124344675, "std_dev": 6.715531499881032e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}