{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/.DS_Store", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/.gitignore", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/MIGRATION.md", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/README.md", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/RELEASE_NOTES.md", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/VERSION-README.md", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/debug_logs.js", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/deploy.sh", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/hacs.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/license", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/package-lock.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/package.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/package.json (snippet)", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/prepare-build.js", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/.DS_Store", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/cs.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/da.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/de.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/en-GB.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/en.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/es.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/fr.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/it.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/nb.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/nl.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/nn.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/no.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/pl.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/src/translations/sv.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/tsconfig.json", "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/webpack.config.js"], "skipped": [{"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/.DS_Store", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/assets/default-car.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/assets/uvc-logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/cards/ultra-vehicle-card.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/color-picker.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/components/action-image-row.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/components/color-picker.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/components/entity-picker.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/components/gradient-editor.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/components/navigation-picker.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/components/navigation-selector.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/debug-info.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/.DS_Store", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/editor-styles.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/about-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/action-images-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/bars-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/customize-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/icons-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/image-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/images-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/info-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/tabs/settings-tab.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/ultra-card-editor.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/ultra-vehicle-card-editor-old.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/editor/ultra-vehicle-card-editor.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/entity-picker.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/index.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/localize/localize.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/services/dynamic-color-service.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/services/dynamic-icon-service.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/services/section-highlight-service.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/services/template-service.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/types/index.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/types.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/ultra-card.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/ultra-vehicle-card.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/ultra-vehicle-card.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/ultra-vehicle-card.js.LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/utils/constants.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/utils/image-upload.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/utils/migrateConfig.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/utils/translation-helper.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/utils/version-logger.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/version.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WJDDesigns_Ultra-Vehicle-Card/dist/version.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.861130952835083, "profiling_times": {"config_time": 5.865853786468506, "core_time": 2.7926578521728516, "ignores_time": 0.0016646385192871094, "total_time": 8.660979509353638}, "parsing_time": {"total_time": 1.0470077991485596, "per_file_time": {"mean": 0.0475912635976618, "std_dev": 0.002122084040499739}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.787402868270874, "per_file_time": {"mean": 0.033183367479415166, "std_dev": 0.0028631522272575868}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7157652378082275, "per_file_and_rule_time": {"mean": 0.010844927845579205, "std_dev": 8.514078099274404e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0047070980072021484, "per_def_and_rule_time": {"mean": 0.0009414196014404297, "std_dev": 2.1438803742057642e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}