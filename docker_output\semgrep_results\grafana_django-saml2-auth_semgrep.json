{"version": "1.130.0", "results": [{"check_id": "python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/saml.py", "start": {"line": 51, "col": 5, "offset": 1721}, "end": {"line": 51, "col": 34, "offset": 1750}, "extra": {"message": "Detected Flask route directly returning a formatted string. This is subject to cross-site scripting if user input can reach the string. Consider using the template engine instead and rendering pages with 'render_template()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "shortlink": "https://sg.run/Zv6o"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/utils.py", "start": {"line": 78, "col": 15, "offset": 2676}, "end": {"line": 78, "col": 41, "offset": 2702}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/views.py", "start": {"line": 86, "col": 1, "offset": 2291}, "end": {"line": 214, "col": 34, "offset": 7599}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "start": {"line": 37, "col": 16, "offset": 1029}, "end": {"line": 37, "col": 19, "offset": 1032}}]], "message": "Syntax error at line downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml:37:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "start": {"line": 37, "col": 16, "offset": 1029}, "end": {"line": 37, "col": 19, "offset": 1032}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "start": {"line": 85, "col": 107, "offset": 2755}, "end": {"line": 85, "col": 110, "offset": 2758}}]], "message": "Syntax error at line downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml:85:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "start": {"line": 85, "col": 107, "offset": 2755}, "end": {"line": 85, "col": 110, "offset": 2758}}]}], "paths": {"scanned": ["downloaded_repos/grafana_django-saml2-auth/.git-blame-ignore-revs", "downloaded_repos/grafana_django-saml2-auth/.github/dependabot.yml", "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "downloaded_repos/grafana_django-saml2-auth/.github/workflows/stale.yml", "downloaded_repos/grafana_django-saml2-auth/.gitignore", "downloaded_repos/grafana_django-saml2-auth/AUTHORS.md", "downloaded_repos/grafana_django-saml2-auth/CONTRIBUTING.md", "downloaded_repos/grafana_django-saml2-auth/LICENSE", "downloaded_repos/grafana_django-saml2-auth/README.md", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/__init__.py", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/errors.py", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/exceptions.py", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/saml.py", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/templates/django_saml2_auth/denied.html", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/templates/django_saml2_auth/error.html", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/templates/django_saml2_auth/signout.html", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/urls.py", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/user.py", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/utils.py", "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/views.py", "downloaded_repos/grafana_django-saml2-auth/pyproject.toml", "downloaded_repos/grafana_django-saml2-auth/uv.lock"], "skipped": [{"path": "downloaded_repos/grafana_django-saml2-auth/.github/workflows/deploy.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/dummy_cert.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/dummy_key.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/metadata.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/metadata2.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/test_saml.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/test_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9515650272369385, "profiling_times": {"config_time": 6.586008548736572, "core_time": 3.228045701980591, "ignores_time": 0.0019006729125976562, "total_time": 9.817259550094604}, "parsing_time": {"total_time": 0.24015355110168457, "per_file_time": {"mean": 0.021832141009244053, "std_dev": 6.72578117233149e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.1958491802215576, "per_file_time": {"mean": 0.037859468624509604, "std_dev": 0.009450718409423652}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.9172685146331787, "per_file_and_rule_time": {"mean": 0.0044744805591862375, "std_dev": 0.00017945919129772227}, "very_slow_stats": {"time_ratio": 0.14224094125157286, "count_ratio": 0.004878048780487805}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/grafana_django-saml2-auth/django_saml2_auth/saml.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.13047313690185547}]}, "tainting_time": {"total_time": 0.16240906715393066, "per_def_and_rule_time": {"mean": 0.0006852703255440113, "std_dev": 1.236576551974091e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}