{"version": "1.130.0", "results": [{"check_id": "python.django.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/benavlabs_crudadmin/crudadmin/event/decorators.py", "start": {"line": 152, "col": 41, "offset": 5547}, "end": {"line": 152, "col": 82, "offset": 5588}, "extra": {"message": "Detected user input used to manually construct a SQL string. This is usually bad practice because manual construction could accidentally result in a SQL injection. An attacker could use a SQL injection to steal or modify contents of the database. Instead, use a parameterized query which is available by default in most database engines. Alternatively, consider using the Django object-relational mappers (ORM) instead of raw SQL queries.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/security/#sql-injection-protection"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "impact": "LOW", "likelihood": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/python.django.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/PbZp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "go.lang.security.audit.xss.no-interpolation-js-template-string.no-interpolation-js-template-string", "path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/management/events.html", "start": {"line": 415, "col": 17, "offset": 11186}, "end": {"line": 415, "col": 87, "offset": 11256}, "extra": {"message": "Detected template variable interpolation in a JavaScript template string. This is potentially vulnerable to cross-site scripting (XSS) attacks because a malicious actor has control over JavaScript but without the need to use escaped characters. Instead, obtain this variable outside of the template string and ensure your template is properly escaped.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/golang/go/issues/9200#issuecomment-66100328", "https://blogtitle.github.io/robn-go-security-pearls-cross-site-scripting-xss/"], "category": "security", "technology": ["generic"], "confidence": "LOW", "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/go.lang.security.audit.xss.no-interpolation-js-template-string.no-interpolation-js-template-string", "shortlink": "https://sg.run/8yl7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 65}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html", "start": {"line": 141, "col": 1, "offset": 0}, "end": {"line": 141, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html:1:\n `{% set include_sidebar_and_header = False %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html", "spans": [{"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 65}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html", "start": {"line": 141, "col": 1, "offset": 0}, "end": {"line": 141, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 28, "offset": 181}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 294, "col": 85, "offset": 0}, "end": {"line": 294, "col": 86, "offset": 1}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 358, "col": 1, "offset": 0}, "end": {"line": 358, "col": 7, "offset": 6}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 384, "col": 1, "offset": 0}, "end": {"line": 384, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html:1:\n `{% extends \"base/base.html\" %}\n\n{% block title %}Create {{ model_name }}{% endblock %}\n\n{% set include_sidebar_and_header = False %}\n\n{% block content %}\n<div class=\"admin-content\">` was unexpected", "path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "spans": [{"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 28, "offset": 181}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 294, "col": 85, "offset": 0}, "end": {"line": 294, "col": 86, "offset": 1}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 358, "col": 1, "offset": 0}, "end": {"line": 358, "col": 7, "offset": 6}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "start": {"line": 384, "col": 1, "offset": 0}, "end": {"line": 384, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 23, "offset": 163}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 346, "col": 1, "offset": 0}, "end": {"line": 348, "col": 20, "offset": 35}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 392, "col": 72, "offset": 0}, "end": {"line": 392, "col": 73, "offset": 1}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 609, "col": 1, "offset": 0}, "end": {"line": 609, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html:1:\n `{% extends \"base/base.html\" %}\n\n{% block title %}{{ model_name }} Administration{% endblock %}\n\n{% set include_sidebar_and_header = True %}\n\n{% block extra_head %}` was unexpected", "path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "spans": [{"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 23, "offset": 163}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 346, "col": 1, "offset": 0}, "end": {"line": 348, "col": 20, "offset": 35}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 392, "col": 72, "offset": 0}, "end": {"line": 392, "col": 73, "offset": 1}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "start": {"line": 609, "col": 1, "offset": 0}, "end": {"line": 609, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 28, "offset": 181}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 306, "col": 81, "offset": 0}, "end": {"line": 306, "col": 82, "offset": 1}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 341, "col": 47, "offset": 0}, "end": {"line": 341, "col": 48, "offset": 1}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 342, "col": 47, "offset": 0}, "end": {"line": 342, "col": 48, "offset": 1}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 371, "col": 1, "offset": 0}, "end": {"line": 371, "col": 7, "offset": 6}}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 396, "col": 1, "offset": 0}, "end": {"line": 396, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html:1:\n `{% extends \"base/base.html\" %}\n\n{% block title %}Update {{ model_name }}{% endblock %}\n\n{% set include_sidebar_and_header = False %}\n\n{% block content %}\n<div class=\"admin-content\">` was unexpected", "path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "spans": [{"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 28, "offset": 181}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 306, "col": 81, "offset": 0}, "end": {"line": 306, "col": 82, "offset": 1}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 341, "col": 47, "offset": 0}, "end": {"line": 341, "col": 48, "offset": 1}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 342, "col": 47, "offset": 0}, "end": {"line": 342, "col": 48, "offset": 1}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 371, "col": 1, "offset": 0}, "end": {"line": 371, "col": 7, "offset": 6}}, {"file": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "start": {"line": 396, "col": 1, "offset": 0}, "end": {"line": 396, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/benavlabs_crudadmin/.github/workflows/linting.yml", "downloaded_repos/benavlabs_crudadmin/.github/workflows/tests.yml", "downloaded_repos/benavlabs_crudadmin/.github/workflows/type-check.yml", "downloaded_repos/benavlabs_crudadmin/.gitignore", "downloaded_repos/benavlabs_crudadmin/.pre-commit-config.yaml", "downloaded_repos/benavlabs_crudadmin/.python-version", "downloaded_repos/benavlabs_crudadmin/CODE_OF_CONDUCT.md", "downloaded_repos/benavlabs_crudadmin/CONTRIBUTING.md", "downloaded_repos/benavlabs_crudadmin/LICENSE", "downloaded_repos/benavlabs_crudadmin/README.md", "downloaded_repos/benavlabs_crudadmin/SECURITY.md", "downloaded_repos/benavlabs_crudadmin/crudadmin/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/admin_site.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/auth.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/helper.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/middleware/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/middleware/auth.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/middleware/https.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/middleware/ip_restriction.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/model_view.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/typing.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_user/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_user/models.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_user/schemas.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_user/service.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/core/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/core/auth.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/core/db.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/core/exceptions.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/core/rate_limiter.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/core/schemas/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/core/schemas/timestamp.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/event/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/event/decorators.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/event/integration.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/event/models.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/event/schemas.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/event/service.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/py.typed", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/backends/__init__.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/backends/database.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/backends/hybrid.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/backends/memcached.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/backends/memory.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/backends/redis.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/configs.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/manager.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/models.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/schemas.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/storage.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/session/user_agents_types.py", "downloaded_repos/benavlabs_crudadmin/crudadmin/static/favicon.png", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/management/events.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/management/events_content.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/management/health.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/management/health_content.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/components/list_content.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/components/pagination.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/components/table_content.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/auth/login.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/base/base.html", "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/shared/utils/refresh.html", "downloaded_repos/benavlabs_crudadmin/docs/advanced/overview.md", "downloaded_repos/benavlabs_crudadmin/docs/api/admin_site.md", "downloaded_repos/benavlabs_crudadmin/docs/api/crud_admin.md", "downloaded_repos/benavlabs_crudadmin/docs/api/events.md", "downloaded_repos/benavlabs_crudadmin/docs/api/model_view.md", "downloaded_repos/benavlabs_crudadmin/docs/api/overview.md", "downloaded_repos/benavlabs_crudadmin/docs/api/session.md", "downloaded_repos/benavlabs_crudadmin/docs/assets/CRUDAdmin.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/benav_labs_banner.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/logo.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/action-buttons-states.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/create-form-example.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/dashboard-layout.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/delete-confirmation.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/event-log-details.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/event-log-filters.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/form-validation-errors.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/health-monitoring.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/model-list-page.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/pagination-controls.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/search-interface.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/sortable-headers.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/screenshots/update-form-example.png", "downloaded_repos/benavlabs_crudadmin/docs/assets/youtube-preview.png", "downloaded_repos/benavlabs_crudadmin/docs/community/CODE_OF_CONDUCT.md", "downloaded_repos/benavlabs_crudadmin/docs/community/CONTRIBUTING.md", "downloaded_repos/benavlabs_crudadmin/docs/community/LICENSE.md", "downloaded_repos/benavlabs_crudadmin/docs/community/overview.md", "downloaded_repos/benavlabs_crudadmin/docs/index.md", "downloaded_repos/benavlabs_crudadmin/docs/quick-start.md", "downloaded_repos/benavlabs_crudadmin/docs/stylesheets/extra.css", "downloaded_repos/benavlabs_crudadmin/docs/usage/adding-models.md", "downloaded_repos/benavlabs_crudadmin/docs/usage/admin-users.md", "downloaded_repos/benavlabs_crudadmin/docs/usage/common-patterns.md", "downloaded_repos/benavlabs_crudadmin/docs/usage/configuration.md", "downloaded_repos/benavlabs_crudadmin/docs/usage/interface.md", "downloaded_repos/benavlabs_crudadmin/docs/usage/overview.md", "downloaded_repos/benavlabs_crudadmin/docs/usage/session-backends.md", "downloaded_repos/benavlabs_crudadmin/mkdocs.yml", "downloaded_repos/benavlabs_crudadmin/mypy.ini", "downloaded_repos/benavlabs_crudadmin/pyproject.toml", "downloaded_repos/benavlabs_crudadmin/tox.ini"], "skipped": [{"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/static/htmx.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/dashboard/dashboard_content.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/create.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/benavlabs_crudadmin/crudadmin/templates/admin/model/update.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/admin_user/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/admin_user/test_service.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/auth/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/auth/test_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/core/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/core/test_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/core/test_db.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/core/test_rate_limiter.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/crud/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/crud/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/crud/test_operations.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/crud/test_root_mount_path.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/crud/test_select_schema.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/crud/test_session_backend_parameters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/crud/test_uuid_support.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/event/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/event/test_decorators.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/event/test_integration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/event/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/event/test_service.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/session/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/session/test_backends.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/session/test_integration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/session/test_manager.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/session/test_rate_limiter_integration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/benavlabs_crudadmin/tests/test_cache_headers.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9407219886779785, "profiling_times": {"config_time": 7.700710773468018, "core_time": 4.841704845428467, "ignores_time": 0.0020313262939453125, "total_time": 12.54570484161377}, "parsing_time": {"total_time": 1.0511126518249512, "per_file_time": {"mean": 0.020213704842787526, "std_dev": 0.0008503283374972308}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 9.750029563903809, "per_file_time": {"mean": 0.03409101246120216, "std_dev": 0.02502271584331317}, "very_slow_stats": {"time_ratio": 0.19030104912659837, "count_ratio": 0.0034965034965034965}, "very_slow_files": [{"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "ftime": 1.8554408550262451}]}, "matching_time": {"total_time": 3.1653940677642822, "per_file_and_rule_time": {"mean": 0.00538332324449708, "std_dev": 0.0001699086549928465}, "very_slow_stats": {"time_ratio": 0.0798449907261823, "count_ratio": 0.003401360544217687}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/session/manager.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.1091148853302002}, {"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/model_view.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.14362597465515137}]}, "tainting_time": {"total_time": 2.316437005996704, "per_def_and_rule_time": {"mean": 0.0013467657011608744, "std_dev": 5.1393214520856225e-05}, "very_slow_stats": {"time_ratio": 0.29254925023989176, "count_ratio": 0.004069767441860465}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "fline": 672, "rule_id": "python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "time": 0.06290507316589355}, {"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "fline": 672, "rule_id": "python.django.security.injection.raw-html-format.raw-html-format", "time": 0.0661778450012207}, {"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "fline": 672, "rule_id": "python.django.security.injection.tainted-sql-string.tainted-sql-string", "time": 0.07140111923217773}, {"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "fline": 672, "rule_id": "python.django.security.injection.tainted-url-host.tainted-url-host", "time": 0.07955288887023926}, {"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/admin_site.py", "fline": 527, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.08524680137634277}, {"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "fline": 672, "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.10117411613464355}, {"fpath": "downloaded_repos/benavlabs_crudadmin/crudadmin/admin_interface/crud_admin.py", "fline": 672, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.2112140655517578}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}