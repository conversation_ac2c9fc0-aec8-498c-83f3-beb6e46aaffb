{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/batiste_django-page-cms/docker-compose.yaml", "start": {"line": 4, "col": 3, "offset": 26}, "end": {"line": 4, "col": 5, "offset": 28}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/batiste_django-page-cms/docker-compose.yaml", "start": {"line": 4, "col": 3, "offset": 26}, "end": {"line": 4, "col": 5, "offset": 28}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/batiste_django-page-cms/docker-compose.yaml", "start": {"line": 29, "col": 3, "offset": 489}, "end": {"line": 29, "col": 7, "offset": 493}, "extra": {"message": "Service 'fast' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/batiste_django-page-cms/docker-compose.yaml", "start": {"line": 29, "col": 3, "offset": 489}, "end": {"line": 29, "col": 7, "offset": 493}, "extra": {"message": "Service 'fast' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 102, "col": 1, "offset": 4081}, "end": {"line": 102, "col": 89, "offset": 4169}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 19, "col": 1, "offset": 628}, "end": {"line": 32, "col": 18, "offset": 1049}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 31, "col": 16, "offset": 1001}, "end": {"line": 31, "col": 46, "offset": 1031}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 51, "col": 1, "offset": 1534}, "end": {"line": 75, "col": 18, "offset": 2501}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 78, "col": 1, "offset": 2504}, "end": {"line": 118, "col": 18, "offset": 4117}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 96, "col": 28, "offset": 3380}, "end": {"line": 96, "col": 53, "offset": 3405}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 137, "col": 24, "offset": 4864}, "end": {"line": 137, "col": 45, "offset": 4885}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 177, "col": 12, "offset": 6027}, "end": {"line": 177, "col": 38, "offset": 6053}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 180, "col": 1, "offset": 6056}, "end": {"line": 206, "col": 42, "offset": 7051}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "start": {"line": 228, "col": 12, "offset": 7686}, "end": {"line": 228, "col": 40, "offset": 7714}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/models.py", "start": {"line": 463, "col": 16, "offset": 16586}, "end": {"line": 463, "col": 54, "offset": 16624}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/models.py", "start": {"line": 583, "col": 20, "offset": 20564}, "end": {"line": 584, "col": 58, "offset": 20677}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/placeholders.py", "start": {"line": 263, "col": 20, "offset": 9398}, "end": {"line": 263, "col": 37, "offset": 9415}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/placeholders.py", "start": {"line": 264, "col": 16, "offset": 9431}, "end": {"line": 264, "col": 65, "offset": 9480}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/placeholders.py", "start": {"line": 269, "col": 27, "offset": 9642}, "end": {"line": 269, "col": 70, "offset": 9685}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/placeholders.py", "start": {"line": 447, "col": 16, "offset": 15174}, "end": {"line": 447, "col": 35, "offset": 15193}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 30, "col": 8, "offset": 875}, "end": {"line": 30, "col": 24, "offset": 891}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 37, "col": 8, "offset": 1055}, "end": {"line": 37, "col": 24, "offset": 1071}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 2027, "col": 16, "offset": 53713}, "end": {"line": 2027, "col": 93, "offset": 53790}, "extra": {"message": "RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 2082, "col": 17, "offset": 55509}, "end": {"line": 2082, "col": 37, "offset": 55529}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 2483, "col": 12, "offset": 67089}, "end": {"line": 2483, "col": 32, "offset": 67109}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 2497, "col": 13, "offset": 67501}, "end": {"line": 2497, "col": 33, "offset": 67521}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 2505, "col": 13, "offset": 67690}, "end": {"line": 2505, "col": 33, "offset": 67710}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 5937, "col": 4, "offset": 164907}, "end": {"line": 5937, "col": 18, "offset": 164921}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 6163, "col": 8, "offset": 171377}, "end": {"line": 6163, "col": 31, "offset": 171400}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 6390, "col": 4, "offset": 177504}, "end": {"line": 6390, "col": 35, "offset": 177535}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 6398, "col": 4, "offset": 177890}, "end": {"line": 6398, "col": 35, "offset": 177921}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 6491, "col": 4, "offset": 180769}, "end": {"line": 6491, "col": 43, "offset": 180808}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "start": {"line": 6569, "col": 6, "offset": 182970}, "end": {"line": 6569, "col": 81, "offset": 183045}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.query-2.1.7.js", "start": {"line": 116, "col": 11, "offset": 4039}, "end": {"line": 116, "col": 42, "offset": 4070}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.ui.js", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 3696, "offset": 3695}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.ui.js", "start": {"line": 1, "col": 280, "offset": 279}, "end": {"line": 1, "col": 312, "offset": 311}, "extra": {"message": "RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/pages_form.js", "start": {"line": 93, "col": 21, "offset": 3138}, "end": {"line": 93, "col": 117, "offset": 3234}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 61, "col": 36, "offset": 3337}, "end": {"line": 61, "col": 78, "offset": 3379}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 64, "col": 36, "offset": 3624}, "end": {"line": 64, "col": 78, "offset": 3666}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/contact.html", "start": {"line": 2, "col": 1, "offset": 16}, "end": {"line": 5, "col": 8, "offset": 119}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "start": {"line": 28, "col": 1, "offset": 1005}, "end": {"line": 28, "col": 69, "offset": 1073}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/batiste_django-page-cms/pages/templatetags/pages_tags.py", "start": {"line": 462, "col": 23, "offset": 13996}, "end": {"line": 462, "col": 57, "offset": 14030}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/utils.py", "start": {"line": 151, "col": 16, "offset": 4621}, "end": {"line": 151, "col": 67, "offset": 4672}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/utils.py", "start": {"line": 154, "col": 12, "offset": 4833}, "end": {"line": 154, "col": 51, "offset": 4872}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/widgets.py", "start": {"line": 54, "col": 27, "offset": 1819}, "end": {"line": 55, "col": 57, "offset": 1903}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/widgets.py", "start": {"line": 131, "col": 16, "offset": 4401}, "end": {"line": 131, "col": 40, "offset": 4425}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/batiste_django-page-cms/pages/widgets.py", "start": {"line": 159, "col": 16, "offset": 5141}, "end": {"line": 160, "col": 54, "offset": 5222}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-card.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 53, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/example/templates/blog-card.html:1:\n `{% load pages_tags static i18n humanize thumbnail %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-card.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/example/templates/blog-card.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 53, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 99}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 35}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html:1:\n `{% extends 'index.html' %}\n{% load pages_tags static i18n humanize thumbnail %}\n\n{% block header %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 99}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 35}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 89}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 20, "col": 20, "offset": 35}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 34, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html:1:\n `{% extends 'index.html' %}\n{% load pages_tags static i18n humanize %}\n\n{% block header %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 89}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 20, "col": 20, "offset": 35}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 34, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 11, "col": 71, "offset": 0}, "end": {"line": 11, "col": 98, "offset": 27}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 13, "col": 68, "offset": 0}, "end": {"line": 13, "col": 95, "offset": 27}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 21, "col": 31, "offset": 0}, "end": {"line": 21, "col": 49, "offset": 18}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 22, "col": 31, "offset": 0}, "end": {"line": 22, "col": 49, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/example/templates/index.html:1:\n `{% load pages_tags static i18n %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 11, "col": 71, "offset": 0}, "end": {"line": 11, "col": 98, "offset": 27}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 13, "col": 68, "offset": 0}, "end": {"line": 13, "col": 95, "offset": 27}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 21, "col": 31, "offset": 0}, "end": {"line": 21, "col": 49, "offset": 18}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "start": {"line": 22, "col": 31, "offset": 0}, "end": {"line": 22, "col": 49, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 46}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 35}}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/example/templates/search/search.html:1:\n `{% extends 'index.html' %}\n\n{% block header %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 46}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 35}}, {"file": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 24, "offset": 77}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 20, "offset": 35}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 24, "col": 58, "offset": 0}, "end": {"line": 24, "col": 63, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 20, "offset": 39}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 36, "col": 5, "offset": 0}, "end": {"line": 36, "col": 15, "offset": 10}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 55, "col": 5, "offset": 0}, "end": {"line": 56, "col": 12, "offset": 23}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 59, "col": 1, "offset": 0}, "end": {"line": 59, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html:1:\n `{% extends \"admin/base_site.html\" %}\n{% load i18n %}\n\n{% block breadcrumbs %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 24, "offset": 77}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 20, "offset": 35}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 24, "col": 58, "offset": 0}, "end": {"line": 24, "col": 63, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 20, "offset": 39}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 36, "col": 5, "offset": 0}, "end": {"line": 36, "col": 15, "offset": 10}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 55, "col": 5, "offset": 0}, "end": {"line": 56, "col": 12, "offset": 23}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "start": {"line": 59, "col": 1, "offset": 0}, "end": {"line": 59, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 39, "offset": 113}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html:1:\n `{% extends \"admin/change_list.html\" %}\n\n{% load admin_list i18n static %}\n\n{% block extrahead %}{{ block.super }}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 39, "offset": 113}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 39, "offset": 187}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 7, "col": 53, "offset": 0}, "end": {"line": 7, "col": 71, "offset": 18}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 35}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 56, "col": 1, "offset": 0}, "end": {"line": 56, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html:1:\n `{% extends \"admin/change_form.html\" %}\n{% load i18n admin_modify pages_tags static admin_urls %}\n\n{% block title %}{% trans \"Edit\" %}{% endblock %}\n\n{% block extrahead %}{{ block.super }}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 39, "offset": 187}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 7, "col": 53, "offset": 0}, "end": {"line": 7, "col": 71, "offset": 18}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 35}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "start": {"line": 56, "col": 1, "offset": 0}, "end": {"line": 56, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 33, "offset": 32}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 11, "col": 91, "offset": 0}, "end": {"line": 11, "col": 122, "offset": 31}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 33, "col": 120, "offset": 0}, "end": {"line": 33, "col": 125, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 34, "col": 106, "offset": 0}, "end": {"line": 34, "col": 111, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 35, "col": 124, "offset": 0}, "end": {"line": 35, "col": 129, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 37, "col": 101, "offset": 0}, "end": {"line": 37, "col": 106, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 41, "col": 90, "offset": 0}, "end": {"line": 41, "col": 95, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 45, "col": 98, "offset": 0}, "end": {"line": 45, "col": 103, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 46, "col": 90, "offset": 0}, "end": {"line": 46, "col": 95, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 47, "col": 91, "offset": 0}, "end": {"line": 47, "col": 96, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 48, "col": 100, "offset": 0}, "end": {"line": 48, "col": 105, "offset": 5}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 106, "col": 1, "offset": 0}, "end": {"line": 110, "col": 12, "offset": 124}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html:1:\n `{% load pages_tags i18n cache %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 33, "offset": 32}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 11, "col": 91, "offset": 0}, "end": {"line": 11, "col": 122, "offset": 31}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 33, "col": 120, "offset": 0}, "end": {"line": 33, "col": 125, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 34, "col": 106, "offset": 0}, "end": {"line": 34, "col": 111, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 35, "col": 124, "offset": 0}, "end": {"line": 35, "col": 129, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 37, "col": 101, "offset": 0}, "end": {"line": 37, "col": 106, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 41, "col": 90, "offset": 0}, "end": {"line": 41, "col": 95, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 45, "col": 98, "offset": 0}, "end": {"line": 45, "col": 103, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 46, "col": 90, "offset": 0}, "end": {"line": 46, "col": 95, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 47, "col": 91, "offset": 0}, "end": {"line": 47, "col": 96, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 48, "col": 100, "offset": 0}, "end": {"line": 48, "col": 105, "offset": 5}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "start": {"line": 106, "col": 1, "offset": 0}, "end": {"line": 110, "col": 12, "offset": 124}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 35, "offset": 62}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 4, "col": 13, "offset": 20}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html:1:\n `{% load pages_tags cache %}\n{% for page in pages_navigation %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 35, "offset": 62}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 4, "col": 13, "offset": 20}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 32, "offset": 59}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html:1:\n `{% load pages_tags cache %}\n{% if page.calculated_status %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 32, "offset": 59}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 21, "offset": 20}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html:1:\n `{% if sql_queries %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 21, "offset": 20}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 33, "offset": 32}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html", "start": {"line": 9, "col": 81, "offset": 0}, "end": {"line": 9, "col": 91, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html:1:\n `{% load cache pages_tags i18n %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 33, "offset": 32}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html", "start": {"line": 9, "col": 81, "offset": 0}, "end": {"line": 9, "col": 91, "offset": 10}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 29, "offset": 28}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "start": {"line": 4, "col": 43, "offset": 0}, "end": {"line": 4, "col": 70, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html:1:\n `{% load static admin_urls %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 29, "offset": 28}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "start": {"line": 4, "col": 43, "offset": 0}, "end": {"line": 4, "col": 70, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 21, "offset": 48}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html:1:\n `{% load pages_tags cache %}\n{% if page.status %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 21, "offset": 48}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/languages.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/languages.html:1:\n `{% load i18n pages_tags %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/languages.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/languages.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 62}}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html:1:\n `{% extends 'pages/examples/index.html' %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html", "spans": [{"file": "downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 62}}, {"file": "downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/batiste_django-page-cms/.codeclimate.yml", "downloaded_repos/batiste_django-page-cms/.coveragerc", "downloaded_repos/batiste_django-page-cms/.github/workflows/codeql-analysis.yml", "downloaded_repos/batiste_django-page-cms/.github/workflows/django.yml", "downloaded_repos/batiste_django-page-cms/.gitignore", "downloaded_repos/batiste_django-page-cms/.pep8", "downloaded_repos/batiste_django-page-cms/.tx/config", "downloaded_repos/batiste_django-page-cms/AUTHORS", "downloaded_repos/batiste_django-page-cms/CHANGELOG", "downloaded_repos/batiste_django-page-cms/CONTRIBUTING", "downloaded_repos/batiste_django-page-cms/Dockerfile", "downloaded_repos/batiste_django-page-cms/FAQ", "downloaded_repos/batiste_django-page-cms/INSTALL", "downloaded_repos/batiste_django-page-cms/LICENSE", "downloaded_repos/batiste_django-page-cms/MANIFEST.in", "downloaded_repos/batiste_django-page-cms/README", "downloaded_repos/batiste_django-page-cms/README.rst", "downloaded_repos/batiste_django-page-cms/THANKS", "downloaded_repos/batiste_django-page-cms/TODO", "downloaded_repos/batiste_django-page-cms/doc/3rd-party-apps.rst", "downloaded_repos/batiste_django-page-cms/doc/Makefile", "downloaded_repos/batiste_django-page-cms/doc/changelog.rst", "downloaded_repos/batiste_django-page-cms/doc/commands.rst", "downloaded_repos/batiste_django-page-cms/doc/conf.py", "downloaded_repos/batiste_django-page-cms/doc/contributions.rst", "downloaded_repos/batiste_django-page-cms/doc/create-blog-application.rst", "downloaded_repos/batiste_django-page-cms/doc/display-content.rst", "downloaded_repos/batiste_django-page-cms/doc/edit-content.rst", "downloaded_repos/batiste_django-page-cms/doc/gerbi.png", "downloaded_repos/batiste_django-page-cms/doc/images/admin-screenshot-1.png", "downloaded_repos/batiste_django-page-cms/doc/images/admin-screenshot-2.png", "downloaded_repos/batiste_django-page-cms/doc/images/ckeditor_placeholder.png", "downloaded_repos/batiste_django-page-cms/doc/images/inline-edit.png", "downloaded_repos/batiste_django-page-cms/doc/images/rte-light.png", "downloaded_repos/batiste_django-page-cms/doc/images/section.png", "downloaded_repos/batiste_django-page-cms/doc/index.rst", "downloaded_repos/batiste_django-page-cms/doc/installation.rst", "downloaded_repos/batiste_django-page-cms/doc/introduction.rst", "downloaded_repos/batiste_django-page-cms/doc/navigation-template-tags.rst", "downloaded_repos/batiste_django-page-cms/doc/page-api.rst", "downloaded_repos/batiste_django-page-cms/doc/placeholders.rst", "downloaded_repos/batiste_django-page-cms/doc/settings-list.rst", "downloaded_repos/batiste_django-page-cms/doc/static/main.css", "downloaded_repos/batiste_django-page-cms/docker-compose.yaml", "downloaded_repos/batiste_django-page-cms/example/__init__.py", "downloaded_repos/batiste_django-page-cms/example/blog/__init__.py", "downloaded_repos/batiste_django-page-cms/example/blog/urls.py", "downloaded_repos/batiste_django-page-cms/example/blog/views.py", "downloaded_repos/batiste_django-page-cms/example/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/example/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/example/locale/uk/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/example/locale/uk/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/example/manage.py", "downloaded_repos/batiste_django-page-cms/example/settings.py", "downloaded_repos/batiste_django-page-cms/example/static/cms/blog.css", "downloaded_repos/batiste_django-page-cms/example/static/cms/main.css", "downloaded_repos/batiste_django-page-cms/example/templates/blog-card.html", "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "downloaded_repos/batiste_django-page-cms/example/templates/language-selector.html", "downloaded_repos/batiste_django-page-cms/example/templates/search/indexes/pages/page_text.txt", "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "downloaded_repos/batiste_django-page-cms/example/urls.py", "downloaded_repos/batiste_django-page-cms/pages/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/admin/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/admin/forms.py", "downloaded_repos/batiste_django-page-cms/pages/admin/utils.py", "downloaded_repos/batiste_django-page-cms/pages/admin/views.py", "downloaded_repos/batiste_django-page-cms/pages/api.py", "downloaded_repos/batiste_django-page-cms/pages/app_config.py", "downloaded_repos/batiste_django-page-cms/pages/cache.py", "downloaded_repos/batiste_django-page-cms/pages/checks.py", "downloaded_repos/batiste_django-page-cms/pages/command_line.py", "downloaded_repos/batiste_django-page-cms/pages/context_processors.py", "downloaded_repos/batiste_django-page-cms/pages/fixtures/pages_tests.json", "downloaded_repos/batiste_django-page-cms/pages/locale/da/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/da/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/de/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/de/LC_MESSAGES/djangojs.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/de/LC_MESSAGES/djangojs.po", "downloaded_repos/batiste_django-page-cms/pages/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/en/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/es/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/es/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/he/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/he/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/it/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/ru/LC_MESSAGES/djangojs.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/ru/LC_MESSAGES/djangojs.po", "downloaded_repos/batiste_django-page-cms/pages/locale/sk/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/sk/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/tr/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/tr/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/uk/LC_MESSAGES/django.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/uk/LC_MESSAGES/django.po", "downloaded_repos/batiste_django-page-cms/pages/locale/uk/LC_MESSAGES/djangojs.mo", "downloaded_repos/batiste_django-page-cms/pages/locale/uk/LC_MESSAGES/djangojs.po", "downloaded_repos/batiste_django-page-cms/pages/management/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/management/commands/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/management/commands/pages_demo.py", "downloaded_repos/batiste_django-page-cms/pages/management/commands/pages_pull.py", "downloaded_repos/batiste_django-page-cms/pages/management/commands/pages_push.py", "downloaded_repos/batiste_django-page-cms/pages/management/utils.py", "downloaded_repos/batiste_django-page-cms/pages/managers.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0001_initial.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0002_page_sites.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0003_page_uuid.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0004_auto_20161209_0648.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0005_media.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0006_auto_20170119_0628.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0007_language_code.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/0008_auto_20181102_0818.py", "downloaded_repos/batiste_django-page-cms/pages/migrations/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/models.py", "downloaded_repos/batiste_django-page-cms/pages/phttp.py", "downloaded_repos/batiste_django-page-cms/pages/placeholders.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/actions.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/admin_urls.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/management/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/management/commands/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/management/commands/pages_export_json.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/management/commands/pages_import_json.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/models.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/tests.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/utils.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/pofiles/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/pofiles/management/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/pofiles/management/commands/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/pofiles/management/commands/pages_export_po.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/pofiles/management/commands/pages_import_po.py", "downloaded_repos/batiste_django-page-cms/pages/plugins/pofiles/utils.py", "downloaded_repos/batiste_django-page-cms/pages/search_indexes.py", "downloaded_repos/batiste_django-page-cms/pages/serializers.py", "downloaded_repos/batiste_django-page-cms/pages/settings.py", "downloaded_repos/batiste_django-page-cms/pages/static/pages/css/font-awesome.css", "downloaded_repos/batiste_django-page-cms/pages/static/pages/css/font-awesome.min.css", "downloaded_repos/batiste_django-page-cms/pages/static/pages/css/inline-edit.css", "downloaded_repos/batiste_django-page-cms/pages/static/pages/css/pages.css", "downloaded_repos/batiste_django-page-cms/pages/static/pages/css/rte.css", "downloaded_repos/batiste_django-page-cms/pages/static/pages/fonts/FontAwesome.otf", "downloaded_repos/batiste_django-page-cms/pages/static/pages/fonts/fontawesome-webfont.eot", "downloaded_repos/batiste_django-page-cms/pages/static/pages/fonts/fontawesome-webfont.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/fonts/fontawesome-webfont.ttf", "downloaded_repos/batiste_django-page-cms/pages/static/pages/fonts/fontawesome-webfont.woff", "downloaded_repos/batiste_django-page-cms/pages/static/pages/fonts/fontawesome-webfont.woff2", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/add.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/add.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/delete.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/draft.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/edit.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/edit.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/hidden.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/hidden.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/icons.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/insert.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/move.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/published.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/redirect.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/sort.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/view.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/icons/view.svg", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/loading.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/rte/bold.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/rte/close.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/rte/image.png", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/rte/italic.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/rte/link.png", "downloaded_repos/batiste_django-page-cms/pages/static/pages/images/rte/unordered.gif", "downloaded_repos/batiste_django-page-cms/pages/static/pages/img/icon_searchbox.png", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/iframe.rte.js", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.query-2.1.7.js", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.rte.js", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.ui.js", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/pages.js", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/pages_form.js", "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/pages_list.js", "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html", "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_list.html", "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_list_table.html", "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/includes/fieldset.html", "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/sub_menu.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/contact.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/content.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/embed.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/ckeditor.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/cool.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/files.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/nice.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/revisions.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/sub_menu.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/traduction_helper.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/file_input.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/languages.html", "downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/richtextarea.html", "downloaded_repos/batiste_django-page-cms/pages/templates/search/indexes/pages/page_text.txt", "downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html", "downloaded_repos/batiste_django-page-cms/pages/templatetags/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/templatetags/ckeditor_placeholder.py", "downloaded_repos/batiste_django-page-cms/pages/templatetags/pages_tags.py", "downloaded_repos/batiste_django-page-cms/pages/test_runner.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/documents/__init__.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/documents/admin.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/documents/models.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/documents/urls.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/documents/views.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/manage.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/search_sites.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/templates/404.html", "downloaded_repos/batiste_django-page-cms/pages/testproj/templates/block_placeholder.html", "downloaded_repos/batiste_django-page-cms/pages/testproj/templates/slow_template_parsing.html", "downloaded_repos/batiste_django-page-cms/pages/testproj/templates/syntax_error.html", "downloaded_repos/batiste_django-page-cms/pages/testproj/test_settings.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/urls.py", "downloaded_repos/batiste_django-page-cms/pages/testproj/views.py", "downloaded_repos/batiste_django-page-cms/pages/urlconf_registry.py", "downloaded_repos/batiste_django-page-cms/pages/urls.py", "downloaded_repos/batiste_django-page-cms/pages/utils.py", "downloaded_repos/batiste_django-page-cms/pages/views.py", "downloaded_repos/batiste_django-page-cms/pages/widgets.py", "downloaded_repos/batiste_django-page-cms/pages/widgets_registry.py", "downloaded_repos/batiste_django-page-cms/requirements-doc.txt", "downloaded_repos/batiste_django-page-cms/requirements-frozen.txt", "downloaded_repos/batiste_django-page-cms/setup.cfg", "downloaded_repos/batiste_django-page-cms/setup.py"], "skipped": [{"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-card.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-home.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/blog-post.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/example/templates/search/search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/plugins/jsonexport/templates/admin/pages/page/import_pages.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/iframeResizer.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/media/change_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/change_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/admin/pages/page/menu.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/breadcrumb.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/dynamic_tree_menu.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/debug.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/examples/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/inline-edit.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/menu.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/auto_render.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/auto_render2.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/base.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/block.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/block2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/block3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/extends.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/fileinput.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/test1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/test2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/test3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/test4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/test5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/test6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/test7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/tests/untranslated.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/pages/widgets/languages.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/templates/search/search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_checks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_commands.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_functional.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_regression.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_selenium.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_templates.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/test_unit.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/batiste_django-page-cms/pages/tests/testcase.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7283811569213867, "profiling_times": {"config_time": 5.97075080871582, "core_time": 25.969505548477173, "ignores_time": 0.002036571502685547, "total_time": 31.94328212738037}, "parsing_time": {"total_time": 2.817460775375366, "per_file_time": {"mean": 0.02515589978013717, "std_dev": 0.02476589794596049}, "very_slow_stats": {"time_ratio": 0.5922563275310145, "count_ratio": 0.008928571428571428}, "very_slow_files": [{"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "ftime": 1.668658971786499}]}, "scanning_time": {"total_time": 37.76828217506409, "per_file_time": {"mean": 0.06052609322926942, "std_dev": 0.8258102624820491}, "very_slow_stats": {"time_ratio": 0.5977008371296817, "count_ratio": 0.0016025641025641025}, "very_slow_files": [{"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "ftime": 22.57413387298584}]}, "matching_time": {"total_time": 16.514981508255005, "per_file_and_rule_time": {"mean": 0.020018159403945458, "std_dev": 0.008375636704973788}, "very_slow_stats": {"time_ratio": 0.6577958205833146, "count_ratio": 0.03636363636363636}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.3579699993133545}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.45669984817504883}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.5002601146697998}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.5118749141693115}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.848142147064209}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.8660588264465332}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 0.9191348552703857}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.0082080364227295}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.052354097366333}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.0635368824005127}]}, "tainting_time": {"total_time": 12.270952939987183, "per_def_and_rule_time": {"mean": 0.003861218672116797, "std_dev": 0.0018596595933537648}, "very_slow_stats": {"time_ratio": 0.7191419921467094, "count_ratio": 0.008495909376966647}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.2675161361694336}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.express.security.express-wkhtml-injection.express-wkhtmltoimage-injection", "time": 0.27962684631347656}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.express.security.express-sandbox-injection.express-sandbox-code-injection", "time": 0.2808809280395508}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.2821071147918701}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.3109569549560547}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.312654972076416}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.8355779647827148}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.8454020023345947}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.879918098449707}, {"fpath": "downloaded_repos/batiste_django-page-cms/pages/static/pages/javascript/jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.632472038269043}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090525056}, "engine_requested": "OSS", "skipped_rules": []}