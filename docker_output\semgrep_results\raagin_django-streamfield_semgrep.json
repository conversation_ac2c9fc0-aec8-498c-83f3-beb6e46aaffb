{"version": "1.130.0", "results": [{"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/raagin_django-streamfield/frontend/src/components/AbstractBlock.vue", "start": {"line": 38, "col": 13, "offset": 1340}, "end": {"line": 38, "col": 26, "offset": 1353}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/raagin_django-streamfield/frontend/src/components/App.vue", "start": {"line": 202, "col": 13, "offset": 8330}, "end": {"line": 202, "col": 78, "offset": 8395}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/raagin_django-streamfield/frontend/src/components/App.vue", "start": {"line": 206, "col": 13, "offset": 8515}, "end": {"line": 206, "col": 75, "offset": 8577}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/raagin_django-streamfield/frontend/src/components/App.vue", "start": {"line": 207, "col": 13, "offset": 8666}, "end": {"line": 207, "col": 74, "offset": 8727}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/raagin_django-streamfield/frontend/src/components/StreamBlock.vue", "start": {"line": 95, "col": 33, "offset": 4346}, "end": {"line": 97, "col": 44, "offset": 4509}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/raagin_django-streamfield/frontend/src/components/StreamBlock.vue", "start": {"line": 129, "col": 25, "offset": 6313}, "end": {"line": 131, "col": 36, "offset": 6460}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/raagin_django-streamfield/streamfield/base.py", "start": {"line": 113, "col": 16, "offset": 3649}, "end": {"line": 113, "col": 40, "offset": 3673}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/raagin_django-streamfield/streamfield/base.py", "start": {"line": 125, "col": 16, "offset": 3968}, "end": {"line": 125, "col": 40, "offset": 3992}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_admin_help.html", "start": {"line": 2, "col": 1, "offset": 16}, "end": {"line": 2, "col": 17, "offset": 32}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/raagin_django-streamfield/streamfield/templatetags/streamfield_tags.py", "start": {"line": 37, "col": 12, "offset": 1003}, "end": {"line": 39, "col": 12, "offset": 1059}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/fields/file_browse_widget.html:1:\n Failure: not a program", "path": "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/fields/file_browse_widget.html"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js:\n ", "path": "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.vm-runincontext-injection.vm-runincontext-injection", "message": "Timeout when running javascript.aws-lambda.security.vm-runincontext-injection.vm-runincontext-injection on downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js:\n ", "path": "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js:\n ", "path": "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_texts.js", "start": {"line": 1, "col": 2, "offset": 0}, "end": {"line": 1, "col": 15, "offset": 13}}]], "message": "Syntax error at line downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_texts.js:1:\n `% load i18n %` was unexpected", "path": "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_texts.js", "spans": [{"file": "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_texts.js", "start": {"line": 1, "col": 2, "offset": 0}, "end": {"line": 1, "col": 15, "offset": 13}}]}], "paths": {"scanned": ["downloaded_repos/raagin_django-streamfield/.gitignore", "downloaded_repos/raagin_django-streamfield/LICENSE", "downloaded_repos/raagin_django-streamfield/MANIFEST.in", "downloaded_repos/raagin_django-streamfield/README.md", "downloaded_repos/raagin_django-streamfield/changes2.0.md", "downloaded_repos/raagin_django-streamfield/frontend/babel.config.json", "downloaded_repos/raagin_django-streamfield/frontend/images/icons.svg", "downloaded_repos/raagin_django-streamfield/frontend/package.json", "downloaded_repos/raagin_django-streamfield/frontend/src/admin_popup_response.js", "downloaded_repos/raagin_django-streamfield/frontend/src/components/AbstractBlock.vue", "downloaded_repos/raagin_django-streamfield/frontend/src/components/AddBlockHere.vue", "downloaded_repos/raagin_django-streamfield/frontend/src/components/App.vue", "downloaded_repos/raagin_django-streamfield/frontend/src/components/BlockHeader.vue", "downloaded_repos/raagin_django-streamfield/frontend/src/components/BlockList.vue", "downloaded_repos/raagin_django-streamfield/frontend/src/components/BlockOptions.vue", "downloaded_repos/raagin_django-streamfield/frontend/src/components/StreamBlock.vue", "downloaded_repos/raagin_django-streamfield/frontend/src/streamfield_widget.js", "downloaded_repos/raagin_django-streamfield/frontend/src/style.sass", "downloaded_repos/raagin_django-streamfield/frontend/src/utils.js", "downloaded_repos/raagin_django-streamfield/frontend/webpack.config.js", "downloaded_repos/raagin_django-streamfield/setup.py", "downloaded_repos/raagin_django-streamfield/streamfield/__init__.py", "downloaded_repos/raagin_django-streamfield/streamfield/admin.py", "downloaded_repos/raagin_django-streamfield/streamfield/apps.py", "downloaded_repos/raagin_django-streamfield/streamfield/base.py", "downloaded_repos/raagin_django-streamfield/streamfield/fields.py", "downloaded_repos/raagin_django-streamfield/streamfield/forms.py", "downloaded_repos/raagin_django-streamfield/streamfield/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/raagin_django-streamfield/streamfield/locale/en/LC_MESSAGES/django.po", "downloaded_repos/raagin_django-streamfield/streamfield/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/raagin_django-streamfield/streamfield/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/raagin_django-streamfield/streamfield/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/raagin_django-streamfield/streamfield/locale/it/LC_MESSAGES/django.po", "downloaded_repos/raagin_django-streamfield/streamfield/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/raagin_django-streamfield/streamfield/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/raagin_django-streamfield/streamfield/models.py", "downloaded_repos/raagin_django-streamfield/streamfield/settings.py", "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/admin_popup_response.js", "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.css", "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/abstract_block_template.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/change_form.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/change_form_render_template.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/fields/default.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/fields/file_browse_widget.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/fields/select.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/fields/stream_field_widget.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/streamfield_popup_response.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/default_block_tmpl.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_admin_help.html", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_texts.js", "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_widget.html", "downloaded_repos/raagin_django-streamfield/streamfield/templatetags/__init__.py", "downloaded_repos/raagin_django-streamfield/streamfield/templatetags/streamfield_tags.py", "downloaded_repos/raagin_django-streamfield/streamfield/tests.py", "downloaded_repos/raagin_django-streamfield/streamfield/urls.py", "downloaded_repos/raagin_django-streamfield/streamfield/views.py", "downloaded_repos/raagin_django-streamfield/test_project/manage.py", "downloaded_repos/raagin_django-streamfield/test_project/pages/__init__.py", "downloaded_repos/raagin_django-streamfield/test_project/pages/admin.py", "downloaded_repos/raagin_django-streamfield/test_project/pages/migrations/0001_initial.py", "downloaded_repos/raagin_django-streamfield/test_project/pages/migrations/__init__.py", "downloaded_repos/raagin_django-streamfield/test_project/pages/models.py", "downloaded_repos/raagin_django-streamfield/test_project/pages/templates/pages/index.html", "downloaded_repos/raagin_django-streamfield/test_project/pages/templates/pages/page_detail.html", "downloaded_repos/raagin_django-streamfield/test_project/pages/tests.py", "downloaded_repos/raagin_django-streamfield/test_project/pages/views.py", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/__init__.py", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/admin.py", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/migrations/0001_initial.py", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/migrations/__init__.py", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/models.py", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/templates/streamblocks/admin/fields/textarea.html", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/templates/streamblocks/admin/richtext.html", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/templates/streamblocks/column.html", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/templates/streamblocks/richtext.html", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/tests.py", "downloaded_repos/raagin_django-streamfield/test_project/streamblocks/views.py", "downloaded_repos/raagin_django-streamfield/test_project/test_project/__init__.py", "downloaded_repos/raagin_django-streamfield/test_project/test_project/settings.py", "downloaded_repos/raagin_django-streamfield/test_project/test_project/urls.py", "downloaded_repos/raagin_django-streamfield/test_project/test_project/wsgi.py"], "skipped": [{"path": "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/admin/fields/file_browse_widget.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/raagin_django-streamfield/streamfield/templates/streamfield/streamfield_texts.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.9360599517822266, "profiling_times": {"config_time": 6.602626085281372, "core_time": 23.057801246643066, "ignores_time": 0.0019664764404296875, "total_time": 29.663264513015747}, "parsing_time": {"total_time": 0.43094396591186523, "per_file_time": {"mean": 0.01026057061694917, "std_dev": 0.0001632906374824469}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 22.915172576904297, "per_file_time": {"mean": 0.10275862142109554, "std_dev": 1.8170699834154203}, "very_slow_stats": {"time_ratio": 0.8806900196851213, "count_ratio": 0.004484304932735426}, "very_slow_files": [{"fpath": "downloaded_repos/raagin_django-streamfield/streamfield/static/streamfield/streamfield_widget.js", "ftime": 20.181163787841797}]}, "matching_time": {"total_time": 0.36087727546691895, "per_file_and_rule_time": {"mean": 0.0017690062522888184, "std_dev": 1.1428391148668612e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.10118365287780762, "per_def_and_rule_time": {"mean": 0.0005325455414621455, "std_dev": 8.281746912713221e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}