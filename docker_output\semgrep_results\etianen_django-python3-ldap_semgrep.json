{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/etianen_django-python3-ldap/.coveragerc", "downloaded_repos/etianen_django-python3-ldap/.github/workflows/python-package.yml", "downloaded_repos/etianen_django-python3-ldap/.github/workflows/python-publish.yml", "downloaded_repos/etianen_django-python3-ldap/.gitignore", "downloaded_repos/etianen_django-python3-ldap/CHANGELOG.rst", "downloaded_repos/etianen_django-python3-ldap/LICENSE", "downloaded_repos/etianen_django-python3-ldap/MANIFEST.in", "downloaded_repos/etianen_django-python3-ldap/README.rst", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/__init__.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/auth.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/conf.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/ldap.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/management/__init__.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/management/commands/__init__.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/management/commands/ldap_clean_users.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/management/commands/ldap_promote.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/management/commands/ldap_sync_users.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/tests.py", "downloaded_repos/etianen_django-python3-ldap/django_python3_ldap/utils.py", "downloaded_repos/etianen_django-python3-ldap/setup.cfg", "downloaded_repos/etianen_django-python3-ldap/setup.py"], "skipped": [{"path": "downloaded_repos/etianen_django-python3-ldap/tests/django_python3_ldap_test/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/etianen_django-python3-ldap/tests/django_python3_ldap_test/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/etianen_django-python3-ldap/tests/manage.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.1940441131591797, "profiling_times": {"config_time": 12.168495178222656, "core_time": 3.103909969329834, "ignores_time": 0.13991260528564453, "total_time": 15.413137674331665}, "parsing_time": {"total_time": 0.3065826892852783, "per_file_time": {"mean": 0.021898763520377024, "std_dev": 6.745102830226486e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.6353328227996826, "per_file_time": {"mean": 0.029202371835708615, "std_dev": 0.002617709579168626}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.2275371551513672, "per_file_and_rule_time": {"mean": 0.0018058504377092635, "std_dev": 1.3689873376438972e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.05673360824584961, "per_def_and_rule_time": {"mean": 0.00042980006246855765, "std_dev": 3.7810657804645024e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}