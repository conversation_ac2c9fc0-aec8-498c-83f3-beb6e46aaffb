{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 38, "offset": 1166}, "end": {"line": 48, "col": 57, "offset": 1185}}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 49, "col": 24, "offset": 1166}, "end": {"line": 49, "col": 43, "offset": 1185}}]], "message": "Syntax error at line downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml:48:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 38, "offset": 1166}, "end": {"line": 48, "col": 57, "offset": 1185}}, {"file": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 49, "col": 24, "offset": 1166}, "end": {"line": 49, "col": 43, "offset": 1185}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 53, "offset": 1353}, "end": {"line": 53, "col": 69, "offset": 1369}}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 97, "offset": 1353}, "end": {"line": 53, "col": 115, "offset": 1371}}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 54, "col": 19, "offset": 1353}, "end": {"line": 54, "col": 22, "offset": 1356}}]], "message": "Syntax error at line downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml:53:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 53, "offset": 1353}, "end": {"line": 53, "col": 69, "offset": 1369}}, {"file": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 97, "offset": 1353}, "end": {"line": 53, "col": 115, "offset": 1371}}, {"file": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "start": {"line": 54, "col": 19, "offset": 1353}, "end": {"line": 54, "col": 22, "offset": 1356}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/maize-tech_laravel-magic-login/src/Support/AuthData.php", "start": {"line": 11, "col": 25, "offset": 0}, "end": {"line": 11, "col": 30, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/maize-tech_laravel-magic-login/src/Support/AuthData.php:11:\n `array` was unexpected", "path": "downloaded_repos/maize-tech_laravel-magic-login/src/Support/AuthData.php", "spans": [{"file": "downloaded_repos/maize-tech_laravel-magic-login/src/Support/AuthData.php", "start": {"line": 11, "col": 25, "offset": 0}, "end": {"line": 11, "col": 30, "offset": 5}}]}], "paths": {"scanned": ["downloaded_repos/maize-tech_laravel-magic-login/.editorconfig", "downloaded_repos/maize-tech_laravel-magic-login/.gitattributes", "downloaded_repos/maize-tech_laravel-magic-login/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/maize-tech_laravel-magic-login/.github/dependabot.yml", "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/phpstan.yml", "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/update-changelog.yml", "downloaded_repos/maize-tech_laravel-magic-login/.gitignore", "downloaded_repos/maize-tech_laravel-magic-login/CHANGELOG.md", "downloaded_repos/maize-tech_laravel-magic-login/LICENSE.md", "downloaded_repos/maize-tech_laravel-magic-login/README.md", "downloaded_repos/maize-tech_laravel-magic-login/art/socialcard-dark.png", "downloaded_repos/maize-tech_laravel-magic-login/art/socialcard-light.png", "downloaded_repos/maize-tech_laravel-magic-login/composer.json", "downloaded_repos/maize-tech_laravel-magic-login/config/magic-login.php", "downloaded_repos/maize-tech_laravel-magic-login/database/migrations/create_magic_logins_table.php.stub", "downloaded_repos/maize-tech_laravel-magic-login/phpstan-baseline.neon", "downloaded_repos/maize-tech_laravel-magic-login/phpstan.neon.dist", "downloaded_repos/maize-tech_laravel-magic-login/phpunit.xml.dist", "downloaded_repos/maize-tech_laravel-magic-login/src/Actions/SendMagicLinkAction.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Facades/MagicLink.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Http/Controllers/MagicLoginController.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Http/Middleware/ValidateSignature.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Http/Requests/MagicLoginRequest.php", "downloaded_repos/maize-tech_laravel-magic-login/src/MagicLink.php", "downloaded_repos/maize-tech_laravel-magic-login/src/MagicLoginServiceProvider.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Models/MagicLogin.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Notifications/MagicLinkNotification.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Support/AuthData.php", "downloaded_repos/maize-tech_laravel-magic-login/src/Support/Config.php"], "skipped": [{"path": "downloaded_repos/maize-tech_laravel-magic-login/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/src/Support/AuthData.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/MagicLoginTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/Support/Exceptions/InvalidSignatureTestException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/Support/Factories/AdminFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/Support/Factories/UserFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/Support/Models/Admin.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/Support/Models/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maize-tech_laravel-magic-login/tests/Support/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7032639980316162, "profiling_times": {"config_time": 6.23292088508606, "core_time": 2.295358896255493, "ignores_time": 0.001680135726928711, "total_time": 8.530803680419922}, "parsing_time": {"total_time": 0.27013087272644043, "per_file_time": {"mean": 0.01350654363632202, "std_dev": 5.037098900672276e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.6213781833648682, "per_file_time": {"mean": 0.007397359325772241, "std_dev": 0.00015347141259606557}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.14045429229736328, "per_file_and_rule_time": {"mean": 0.0006270280906132287, "std_dev": 2.4593632713472504e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}