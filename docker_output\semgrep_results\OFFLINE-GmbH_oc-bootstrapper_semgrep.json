{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/OFFLINE-********************/Dockerfile", "start": {"line": 29, "col": 1, "offset": 591}, "end": {"line": 29, "col": 14, "offset": 604}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT []", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/OFFLINE-********************/Dockerfile", "start": {"line": 30, "col": 1, "offset": 605}, "end": {"line": 30, "col": 37, "offset": 641}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/composer/vendor/bin/october\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/OFFLINE-********************/src/Config/Setup.php", "start": {"line": 133, "col": 17, "offset": 4149}, "end": {"line": 133, "col": 32, "offset": 4164}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/OFFLINE-********************/src/Config/Writer.php", "start": {"line": 82, "col": 13, "offset": 1528}, "end": {"line": 82, "col": 31, "offset": 1546}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/OFFLINE-********************/src/Console/InstallCommand.php", "start": {"line": 474, "col": 25, "offset": 13732}, "end": {"line": 474, "col": 53, "offset": 13760}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/OFFLINE-********************/src/Downloader/OctoberCms.php", "start": {"line": 129, "col": 10, "offset": 3008}, "end": {"line": 129, "col": 32, "offset": 3030}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/OFFLINE-********************/src/Util/ManageDirectory.php", "start": {"line": 116, "col": 13, "offset": 2993}, "end": {"line": 116, "col": 26, "offset": 3006}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/OFFLINE-********************/templates/lando.yml:1:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/OFFLINE-********************/templates/lando.yml"}], "paths": {"scanned": ["downloaded_repos/OFFLINE-********************/.gitignore", "downloaded_repos/OFFLINE-********************/Dockerfile", "downloaded_repos/OFFLINE-********************/LICENSE", "downloaded_repos/OFFLINE-********************/README.md", "downloaded_repos/OFFLINE-********************/composer.json", "downloaded_repos/OFFLINE-********************/october", "downloaded_repos/OFFLINE-********************/src/Config/Config.php", "downloaded_repos/OFFLINE-********************/src/Config/Setup.php", "downloaded_repos/OFFLINE-********************/src/Config/Writer.php", "downloaded_repos/OFFLINE-********************/src/Config/Yaml.php", "downloaded_repos/OFFLINE-********************/src/Console/InitCommand.php", "downloaded_repos/OFFLINE-********************/src/Console/InstallCommand.php", "downloaded_repos/OFFLINE-********************/src/Console/PushCommand.php", "downloaded_repos/OFFLINE-********************/src/Console/UpdateCommand.php", "downloaded_repos/OFFLINE-********************/src/Deployment/DeploymentBase.php", "downloaded_repos/OFFLINE-********************/src/Deployment/DeploymentFactory.php", "downloaded_repos/OFFLINE-********************/src/Deployment/DeploymentInterface.php", "downloaded_repos/OFFLINE-********************/src/Deployment/Gitlab.php", "downloaded_repos/OFFLINE-********************/src/Downloader/OctoberCms.php", "downloaded_repos/OFFLINE-********************/src/Exceptions/DeploymentExistsException.php", "downloaded_repos/OFFLINE-********************/src/Exceptions/DevEnvExistsException.php", "downloaded_repos/OFFLINE-********************/src/Exceptions/PluginExistsException.php", "downloaded_repos/OFFLINE-********************/src/Exceptions/ThemeExistsException.php", "downloaded_repos/OFFLINE-********************/src/Manager/BaseManager.php", "downloaded_repos/OFFLINE-********************/src/Manager/PluginManager.php", "downloaded_repos/OFFLINE-********************/src/Manager/ThemeManager.php", "downloaded_repos/OFFLINE-********************/src/Util/Artisan.php", "downloaded_repos/OFFLINE-********************/src/Util/CliIO.php", "downloaded_repos/OFFLINE-********************/src/Util/Composer.php", "downloaded_repos/OFFLINE-********************/src/Util/ConfigMaker.php", "downloaded_repos/OFFLINE-********************/src/Util/Git.php", "downloaded_repos/OFFLINE-********************/src/Util/Gitignore.php", "downloaded_repos/OFFLINE-********************/src/Util/KeyGenerator.php", "downloaded_repos/OFFLINE-********************/src/Util/ManageDirectory.php", "downloaded_repos/OFFLINE-********************/src/Util/RunsProcess.php", "downloaded_repos/OFFLINE-********************/src/Util/UsesTemplate.php", "downloaded_repos/OFFLINE-********************/templates/Envoy.blade.php", "downloaded_repos/OFFLINE-********************/templates/README.md", "downloaded_repos/OFFLINE-********************/templates/gitignore", "downloaded_repos/OFFLINE-********************/templates/gitignore.bare", "downloaded_repos/OFFLINE-********************/templates/gitlab-ci.yml", "downloaded_repos/OFFLINE-********************/templates/lando.yml", "downloaded_repos/OFFLINE-********************/templates/october.yaml"], "skipped": [{"path": "downloaded_repos/OFFLINE-********************/templates/lando.yml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.8905928134918213, "profiling_times": {"config_time": 7.655827283859253, "core_time": 2.7681851387023926, "ignores_time": 0.0019290447235107422, "total_time": 10.426944017410278}, "parsing_time": {"total_time": 0.45454835891723633, "per_file_time": {"mean": 0.012626343303256566, "std_dev": 0.00018251822172541703}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.0440990924835205, "per_file_time": {"mean": 0.0084886105079961, "std_dev": 0.00034274804942949585}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.23108243942260742, "per_file_and_rule_time": {"mean": 0.0019750635848086095, "std_dev": 1.9744173742849713e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.03224539756774902, "per_def_and_rule_time": {"mean": 0.00031613134870342175, "std_dev": 2.3355699596823223e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}