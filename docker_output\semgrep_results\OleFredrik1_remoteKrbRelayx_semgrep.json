{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.httpsconnection-detected.httpsconnection-detected", "path": "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/clients/httprelayclient.py", "start": {"line": 117, "col": 28, "offset": 4731}, "end": {"line": 117, "col": 96, "offset": 4799}, "extra": {"message": "The HTTPSConnection API has changed frequently with minor releases of Python. Ensure you are using the API for your version of Python securely. For example, Python 3 versions prior to 3.4.3 will not verify SSL certificates by default. See https://docs.python.org/3/library/http.client.html#http.client.HTTPSConnection for more information.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-295: Improper Certificate Validation"], "references": ["https://docs.python.org/3/library/http.client.html#http.client.HTTPSConnection"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.lang.security.audit.httpsconnection-detected.httpsconnection-detected", "shortlink": "https://sg.run/8yby"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.httpsconnection-detected.httpsconnection-detected", "path": "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/clients/httprelayclient.py", "start": {"line": 119, "col": 28, "offset": 4900}, "end": {"line": 119, "col": 76, "offset": 4948}, "extra": {"message": "The HTTPSConnection API has changed frequently with minor releases of Python. Ensure you are using the API for your version of Python securely. For example, Python 3 versions prior to 3.4.3 will not verify SSL certificates by default. See https://docs.python.org/3/library/http.client.html#http.client.HTTPSConnection for more information.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-295: Improper Certificate Validation"], "references": ["https://docs.python.org/3/library/http.client.html#http.client.HTTPSConnection"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.lang.security.audit.httpsconnection-detected.httpsconnection-detected", "shortlink": "https://sg.run/8yby"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/OleFredrik1_remoteKrbRelayx/.gitignore", "downloaded_repos/OleFredrik1_remoteKrbRelayx/LICENSE", "downloaded_repos/OleFredrik1_remoteKrbRelayx/README.md", "downloaded_repos/OleFredrik1_remoteKrbRelayx/images/coerce_to_illustration.png", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remoteKrbRelayx.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/config.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/clients/__init__.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/clients/httprelayclient.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/clients/smbrelayclient.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/utils/config.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/utils/kerberos.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/krbrelayx/lib/utils/spnego.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/potato.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/remotekrbrelayx/rpcrelayserver.py", "downloaded_repos/OleFredrik1_remoteKrbRelayx/requirements.txt", "downloaded_repos/OleFredrik1_remoteKrbRelayx/setup.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.097288131713867, "profiling_times": {"config_time": 7.085795164108276, "core_time": 3.4421815872192383, "ignores_time": 0.0026280879974365234, "total_time": 10.531583547592163}, "parsing_time": {"total_time": 0.2911255359649658, "per_file_time": {"mean": 0.02646595781499689, "std_dev": 0.00015523993492614645}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.711902141571045, "per_file_time": {"mean": 0.06306749166444292, "std_dev": 0.01590828327781256}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.018172025680542, "per_file_and_rule_time": {"mean": 0.0068795407140577166, "std_dev": 0.00024052937689014975}, "very_slow_stats": {"time_ratio": 0.14408961150660002, "count_ratio": 0.006756756756756757}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/OleFredrik1_remoteKrbRelayx/remoteKrbRelayx.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.14670801162719727}]}, "tainting_time": {"total_time": 0.29001545906066895, "per_def_and_rule_time": {"mean": 0.0006591260433197022, "std_dev": 2.6891698116025186e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}