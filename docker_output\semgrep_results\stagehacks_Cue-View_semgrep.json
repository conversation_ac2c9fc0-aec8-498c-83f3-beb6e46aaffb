{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/networkInterfaces.js", "start": {"line": 29, "col": 5, "offset": 805}, "end": {"line": 29, "col": 68, "offset": 868}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/stagehacks_Cue-View/plugins/digico/main.js", "start": {"line": 166, "col": 5, "offset": 5737}, "end": {"line": 166, "col": 30, "offset": 5762}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/digico/main.js", "start": {"line": 183, "col": 5, "offset": 6341}, "end": {"line": 187, "col": 8, "offset": 6483}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/eos/main.js", "start": {"line": 119, "col": 7, "offset": 4398}, "end": {"line": 123, "col": 10, "offset": 4529}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/qlab/main.js", "start": {"line": 216, "col": 9, "offset": 7577}, "end": {"line": 220, "col": 12, "offset": 7732}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/qlab/main.js", "start": {"line": 222, "col": 9, "offset": 7756}, "end": {"line": 226, "col": 12, "offset": 7910}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/qlab/main.js", "start": {"line": 260, "col": 11, "offset": 9220}, "end": {"line": 260, "col": 79, "offset": 9288}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/qlab/main.js", "start": {"line": 262, "col": 11, "offset": 9316}, "end": {"line": 262, "col": 53, "offset": 9358}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/qlab/main.js", "start": {"line": 265, "col": 11, "offset": 9404}, "end": {"line": 265, "col": 48, "offset": 9441}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/stagehacks_Cue-View/plugins/shure/main.js", "start": {"line": 136, "col": 9, "offset": 4241}, "end": {"line": 136, "col": 99, "offset": 4331}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/stagehacks_Cue-View/plugins/x32/template.ejs", "start": {"line": 16, "col": 78, "offset": 389}, "end": {"line": 16, "col": 105, "offset": 416}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/stagehacks_Cue-View/plugins/x32/template.ejs", "start": {"line": 41, "col": 9, "offset": 1281}, "end": {"line": 41, "col": 51, "offset": 1323}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/stagehacks_Cue-View/plugins/xair/template.ejs", "start": {"line": 33, "col": 9, "offset": 845}, "end": {"line": 33, "col": 43, "offset": 879}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/stagehacks_Cue-View/src/plugins.js", "start": {"line": 21, "col": 72, "offset": 690}, "end": {"line": 21, "col": 94, "offset": 712}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/stagehacks_Cue-View/src/plugins.js", "start": {"line": 33, "col": 58, "offset": 1083}, "end": {"line": 33, "col": 85, "offset": 1110}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/stagehacks_Cue-View/src/plugins.js", "start": {"line": 36, "col": 81, "offset": 1214}, "end": {"line": 36, "col": 105, "offset": 1238}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/src/view.js", "start": {"line": 150, "col": 5, "offset": 4274}, "end": {"line": 150, "col": 27, "offset": 4296}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/stagehacks_Cue-View/src/view.js", "start": {"line": 262, "col": 91, "offset": 8854}, "end": {"line": 262, "col": 98, "offset": 8861}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/stagehacks_Cue-View/src/view.js", "start": {"line": 270, "col": 91, "offset": 9268}, "end": {"line": 270, "col": 98, "offset": 9275}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/src/view.js", "start": {"line": 375, "col": 3, "offset": 12383}, "end": {"line": 375, "col": 85, "offset": 12465}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/stagehacks_Cue-View/src/view.js", "start": {"line": 376, "col": 3, "offset": 12468}, "end": {"line": 376, "col": 70, "offset": 12535}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 10, "offset": 9}}, {"path": "downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html", "start": {"line": 11, "col": 41, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html:11:\n `(Look for` was unexpected", "path": "downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html", "spans": [{"file": "downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 10, "offset": 9}}, {"file": "downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html", "start": {"line": 11, "col": 41, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 1}}]}], "paths": {"scanned": ["downloaded_repos/stagehacks_Cue-View/.eslintrc.js", "downloaded_repos/stagehacks_Cue-View/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/stagehacks_Cue-View/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/stagehacks_Cue-View/.github/dependabot.yml", "downloaded_repos/stagehacks_Cue-View/.github/release.yaml", "downloaded_repos/stagehacks_Cue-View/.github/workflows/release.yaml", "downloaded_repos/stagehacks_Cue-View/.gitignore", "downloaded_repos/stagehacks_Cue-View/.nvmrc", "downloaded_repos/stagehacks_Cue-View/.prettierignore", "downloaded_repos/stagehacks_Cue-View/.prettierrc.js", "downloaded_repos/stagehacks_Cue-View/.vscode/extensions.json", "downloaded_repos/stagehacks_Cue-View/.vscode/launch.json", "downloaded_repos/stagehacks_Cue-View/.vscode/settings.json", "downloaded_repos/stagehacks_Cue-View/LICENSE", "downloaded_repos/stagehacks_Cue-View/README.md", "downloaded_repos/stagehacks_Cue-View/index.html", "downloaded_repos/stagehacks_Cue-View/main.js", "downloaded_repos/stagehacks_Cue-View/networkInterfaces.html", "downloaded_repos/stagehacks_Cue-View/networkInterfaces.js", "downloaded_repos/stagehacks_Cue-View/package-lock.json", "downloaded_repos/stagehacks_Cue-View/package.json", "downloaded_repos/stagehacks_Cue-View/plugins/artnet/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/artnet/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/artnet/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/artnet/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/artnet/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/atem/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/img/button_green.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/img/button_off.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/img/button_red.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/img/button_white.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/img/button_yellow.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/img/tbar_bg.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/img/tbar_handle.png", "downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/atem/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/atem/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/atem/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/digico/fader.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/digico/icon.afphoto", "downloaded_repos/stagehacks_Cue-View/plugins/digico/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/digico/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/digico/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/digico/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/digico/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/eos/cue.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/eos/cue.js", "downloaded_repos/stagehacks_Cue-View/plugins/eos/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/eos/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/eos/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/eos/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/eos/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/lightfactory/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/lightfactory/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/lightfactory/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/lightfactory/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/lightfactory/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/livestream-studio/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/livestream-studio/img/tbar_bg.png", "downloaded_repos/stagehacks_Cue-View/plugins/livestream-studio/img/tbar_handle.png", "downloaded_repos/stagehacks_Cue-View/plugins/livestream-studio/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/livestream-studio/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/livestream-studio/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/livestream-studio/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/pjlink/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/pjlink/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/pjlink/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/pjlink/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/pjlink/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/posistagenet/icon.afphoto", "downloaded_repos/stagehacks_Cue-View/plugins/posistagenet/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/posistagenet/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/posistagenet/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/posistagenet/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/posistagenet/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/cart.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/cue.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/cuelist.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/arm.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/arrow-down.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/arrow-right.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/audio.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/auto_continue.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/auto_continue_stubby.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/auto_follow.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/camera.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/devamp.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/disarm.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/disarmed-pattern-light.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/disarmed-pattern-light.tiff", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/empty.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/fade.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/goto.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/group-arrow.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/group.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/light.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/load.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/memo.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/mic.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/midi-file.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/midi.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/network.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/new_group_arrow.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/pause.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/pause_circled.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/play_circled.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/playhead.afdesign", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/playhead.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/reset.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/script.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/start.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_broken.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_broken_white.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_flagged.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_loaded.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_paused.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_running.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_spinner.gif", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/status_spinner.psd", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/stop.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/target.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/text.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/timecode.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/v5/group-1.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/v5/group-2.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/v5/group-3.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/v5/group-4.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/v5/group-6.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/video.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/img/wait.png", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/qlab/tile.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/sacn/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/sacn/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/sacn/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/sacn/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/sacn/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/shure/channel.js", "downloaded_repos/stagehacks_Cue-View/plugins/shure/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/shure/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/shure/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/shure/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/shure/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/watchout/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/watchout/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/watchout/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/watchout/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/watchout/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/x32/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/x32/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/x32/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/x32/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/x32/template.ejs", "downloaded_repos/stagehacks_Cue-View/plugins/xair/icon.png", "downloaded_repos/stagehacks_Cue-View/plugins/xair/info.html", "downloaded_repos/stagehacks_Cue-View/plugins/xair/main.js", "downloaded_repos/stagehacks_Cue-View/plugins/xair/styles.css", "downloaded_repos/stagehacks_Cue-View/plugins/xair/template.ejs", "downloaded_repos/stagehacks_Cue-View/preload.js", "downloaded_repos/stagehacks_Cue-View/src/assets/css/index.css", "downloaded_repos/stagehacks_Cue-View/src/assets/css/plugin_default.css", "downloaded_repos/stagehacks_Cue-View/src/assets/font/MaterialIcons-Regular.ttf", "downloaded_repos/stagehacks_Cue-View/src/assets/img/icon.icns", "downloaded_repos/stagehacks_Cue-View/src/assets/img/icon.ico", "downloaded_repos/stagehacks_Cue-View/src/assets/img/icon.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_add_box_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_add_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_broken_image_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_clear_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_done_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_info_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_link_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_push_pin_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_refresh_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/assets/img/outline_search_white_18dp.png", "downloaded_repos/stagehacks_Cue-View/src/device.js", "downloaded_repos/stagehacks_Cue-View/src/index.js", "downloaded_repos/stagehacks_Cue-View/src/plugins.js", "downloaded_repos/stagehacks_Cue-View/src/saveSlots.js", "downloaded_repos/stagehacks_Cue-View/src/search.js", "downloaded_repos/stagehacks_Cue-View/src/view.js"], "skipped": [{"path": "downloaded_repos/stagehacks_Cue-View/plugins/atem/info.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.7709569931030273, "profiling_times": {"config_time": 6.3585357666015625, "core_time": 3.650282859802246, "ignores_time": 0.0018229484558105469, "total_time": 10.011648178100586}, "parsing_time": {"total_time": 1.7789816856384277, "per_file_time": {"mean": 0.048080586098335876, "std_dev": 0.005829926138822895}, "very_slow_stats": {"time_ratio": 0.2491127226234623, "count_ratio": 0.02702702702702703}, "very_slow_files": [{"fpath": "downloaded_repos/stagehacks_Cue-View/package-lock.json", "ftime": 0.44316697120666504}]}, "scanning_time": {"total_time": 7.148173809051514, "per_file_time": {"mean": 0.016979035175894327, "std_dev": 0.0051264840454940215}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.59273362159729, "per_file_and_rule_time": {"mean": 0.010669685685585557, "std_dev": 0.00038350320907174304}, "very_slow_stats": {"time_ratio": 0.05882821919070416, "count_ratio": 0.00411522633744856}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/stagehacks_Cue-View/src/view.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1525259017944336}]}, "tainting_time": {"total_time": 0.7620208263397217, "per_def_and_rule_time": {"mean": 0.0030480833053588867, "std_dev": 2.2529487620559847e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1088952192}, "engine_requested": "OSS", "skipped_rules": []}