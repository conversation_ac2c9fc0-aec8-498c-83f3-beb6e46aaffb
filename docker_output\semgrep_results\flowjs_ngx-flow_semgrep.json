{"version": "1.130.0", "results": [{"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/flowjs_ngx-flow/server/app.js", "start": {"line": 7, "col": 5, "offset": 280}, "end": {"line": 7, "col": 20, "offset": 295}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/flowjs_ngx-flow/server/flow-node.js", "start": {"line": 8, "col": 47, "offset": 228}, "end": {"line": 8, "col": 62, "offset": 243}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/flowjs_ngx-flow/server/flow-node.js", "start": {"line": 24, "col": 44, "offset": 677}, "end": {"line": 24, "col": 86, "offset": 719}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 30, "col": 71, "offset": 70}}]], "message": "Syntax error at line downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.html:30:\n `Total progress: {{(flow.transfers$ | async)?.totalProgress | percent}}` was unexpected", "path": "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.html", "spans": [{"file": "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 30, "col": 71, "offset": 70}}]}], "paths": {"scanned": ["downloaded_repos/flowjs_ngx-flow/.codeclimate.yml", "downloaded_repos/flowjs_ngx-flow/.editorconfig", "downloaded_repos/flowjs_ngx-flow/.github/workflows/npm-publish.yml", "downloaded_repos/flowjs_ngx-flow/.gitignore", "downloaded_repos/flowjs_ngx-flow/.travis.yml", "downloaded_repos/flowjs_ngx-flow/LICENSE", "downloaded_repos/flowjs_ngx-flow/README.md", "downloaded_repos/flowjs_ngx-flow/angular.json", "downloaded_repos/flowjs_ngx-flow/browserslist", "downloaded_repos/flowjs_ngx-flow/e2e/protractor.conf.js", "downloaded_repos/flowjs_ngx-flow/e2e/src/app.e2e-spec.ts", "downloaded_repos/flowjs_ngx-flow/e2e/src/app.po.ts", "downloaded_repos/flowjs_ngx-flow/e2e/tsconfig.e2e.json", "downloaded_repos/flowjs_ngx-flow/eslint.config.js", "downloaded_repos/flowjs_ngx-flow/karma.base.js", "downloaded_repos/flowjs_ngx-flow/package-lock.json", "downloaded_repos/flowjs_ngx-flow/package.json", "downloaded_repos/flowjs_ngx-flow/prettier.config.js", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/README.md", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/eslint.config.js", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/karma.conf.js", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/ng-package.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/ng-package.prod.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/package-lock.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/package.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/button.directive.spec.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/button.directive.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/drop.directive.spec.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/drop.directive.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/flow-constructor.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/flow-injection-token.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/flow.directive.spec.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/flow.directive.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/helpers/flow-file-to-transfer.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/ngx-flow.module.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/src.directive.spec.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/src.directive.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/transfer.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/upload-state.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/public-api.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/typings.d.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/tsconfig.lib.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/tsconfig.lib.prod.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/tsconfig.spec.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/eslint.config.js", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/karma.conf.js", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.css", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.html", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/assets/.gitkeep", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/favicon.ico", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/index.html", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/main.ts", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/styles.css", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/tsconfig.app.json", "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/tsconfig.spec.json", "downloaded_repos/flowjs_ngx-flow/server/README.md", "downloaded_repos/flowjs_ngx-flow/server/app.js", "downloaded_repos/flowjs_ngx-flow/server/flow-node.js", "downloaded_repos/flowjs_ngx-flow/server/public/flow.js", "downloaded_repos/flowjs_ngx-flow/server/tmp/.gitkeep", "downloaded_repos/flowjs_ngx-flow/tsconfig.json"], "skipped": [{"path": "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/helpers/tests/flow-file-mock-factory.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/helpers/tests/flow-mock.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow/src/lib/helpers/tests/transfer-mock-factory.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/flowjs_ngx-flow/projects/ngx-flow-demo/src/app/app.component.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6890149116516113, "profiling_times": {"config_time": 6.291837692260742, "core_time": 3.670203447341919, "ignores_time": 0.0016641616821289062, "total_time": 9.964821100234985}, "parsing_time": {"total_time": 1.0434253215789795, "per_file_time": {"mean": 0.020868506431579593, "std_dev": 0.0024030350425573725}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.304249048233032, "per_file_time": {"mean": 0.024737063495592133, "std_dev": 0.01221708392844146}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.412886619567871, "per_file_and_rule_time": {"mean": 0.004888881036567028, "std_dev": 0.00020668431076018582}, "very_slow_stats": {"time_ratio": 0.16458074449264573, "count_ratio": 0.006920415224913495}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/flowjs_ngx-flow/server/public/flow.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.10484194755554199}, {"fpath": "downloaded_repos/flowjs_ngx-flow/server/public/flow.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.12769198417663574}]}, "tainting_time": {"total_time": 0.5345034599304199, "per_def_and_rule_time": {"mean": 0.0017467433331059474, "std_dev": 2.3599101437286114e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}