#!/usr/bin/env python3

"""
Unified Vulnerability Scanner with Docker & Semgrep Integration
==============================================================

Combines all CVE trend scanners, downloads repositories, runs Semgrep analysis,
and produces comprehensive vulnerability reports with scoring.
"""

import argparse
import requests
import datetime
import csv
import os
import json
import subprocess
import shutil
import tempfile
import threading
import time
from pathlib import Path
from langdetect import detect
from concurrent.futures import ThreadPoolExecutor, as_completed

# -------------------- UNIFIED CONFIGURATION --------------------
UNIFIED_CONFIG = {
    "github_token": os.getenv("GITHUB_TOKEN", "your_github_token_here"),
    "search_pages": 5,  # Increased to find more candidates
    "min_stars": 50,
    "max_stars": 2000,
    "updated_days_ago": 120,
    "max_repos_to_analyze": 100,  # Analyze more repos to find best ones
    "top_repos_to_keep": 10,  # Number of top repos to keep in CSV (configurable)
    "semgrep_timeout": 300,  # 5 minutes per repo
    "clone_timeout": 120,    # 2 minutes per clone
    "max_concurrent_scans": 2,  # Reduced for stability
    "output_csv": "unified_vulnerability_results.csv",
    "semgrep_results_dir": "semgrep_results",
    "repos_dir": "downloaded_repos",
    "detailed_log": "unified_scan_log.json",
    "use_docker": False,  # Always use direct Semgrep when running in container
    "docker_image": "returntocorp/semgrep:latest",
    "in_container": os.getenv("SEMGREP_IN_DOCKER", "0") == "1"
}

HEADERS = {"Authorization": f"token {UNIFIED_CONFIG['github_token']}"}

# Combined vulnerability patterns from all scanners
COMBINED_PATTERNS = {
    "high_cve_applications": [
        "wordpress", "drupal", "joomla", "magento", "prestashop", "opencart",
        "phpmyadmin", "adminer", "roundcube", "cms", "admin panel", "dashboard",
        "file manager", "upload", "gallery", "media server", "chat", "forum"
    ],
    
    "critical_features": [
        "file upload", "image upload", "admin panel", "user management",
        "password reset", "authentication", "api endpoint", "search",
        "export", "import", "backup", "plugin", "template", "ldap", "oauth"
    ],
    
    "vulnerability_keywords": [
        "auth", "login", "password", "token", "upload", "admin", "jwt",
        "reset", "api", "sql", "query", "exec", "eval", "include", "require"
    ],
    
    "exclude_keywords": [
        "plugin", "DEPRECATED", "A list of", "module", "command-line", "template",
        "package", "sdk", "starter", "portfolio", "extension", "demo", "wrapper",
        "boilerplate", "library", "framework", "example", "test", "sample", "tutorial"
    ]
}

class UnifiedVulnerabilityScanner:
    def __init__(self):
        self.repos_analyzed = []
        self.semgrep_results = {}
        self.scan_stats = {
            "total_repos_found": 0,
            "repos_cloned": 0,
            "semgrep_scans_completed": 0,
            "high_risk_repos": 0,
            "vulnerabilities_found": 0
        }
        
        # Create output directories
        os.makedirs(UNIFIED_CONFIG["semgrep_results_dir"], exist_ok=True)
        os.makedirs(UNIFIED_CONFIG["repos_dir"], exist_ok=True)

    def calculate_unified_risk_score(self, repo):
        """Calculate comprehensive risk score combining all scanner methodologies"""
        score = 0
        risk_factors = []
        
        desc = (repo.get("description", "") or "").lower()
        name = repo.get("full_name", "").lower()
        topics = [t.lower() for t in (repo.get("topics", []) or [])]
        language = (repo.get("language", "") or "").lower()
        
        # 1. Application type analysis (25 points)
        app_matches = sum(1 for app in COMBINED_PATTERNS["high_cve_applications"] 
                         if app in desc or app in name or any(app in topic for topic in topics))
        if app_matches > 0:
            app_score = min(app_matches * 8, 25)
            score += app_score
            risk_factors.append(f"High-risk application type (+{app_score})")
        
        # 2. Critical features (20 points)
        feature_matches = sum(1 for feature in COMBINED_PATTERNS["critical_features"] 
                             if feature in desc)
        if feature_matches > 0:
            feature_score = min(feature_matches * 4, 20)
            score += feature_score
            risk_factors.append(f"Critical features (+{feature_score})")
        
        # 3. Vulnerability keywords (15 points)
        keyword_matches = sum(1 for keyword in COMBINED_PATTERNS["vulnerability_keywords"] 
                             if keyword in desc)
        if keyword_matches > 0:
            keyword_score = min(keyword_matches * 3, 15)
            score += keyword_score
            risk_factors.append(f"Vulnerability keywords (+{keyword_score})")
        
        # 4. Repository metrics (20 points)
        stars = repo.get("stargazers_count", 0)
        issues = repo.get("open_issues_count", 0)
        
        # Star range analysis
        if 100 <= stars <= 1500:  # Sweet spot for vulnerabilities
            score += 10
            risk_factors.append("Optimal star range (+10)")
        
        # Issue count
        if issues >= 50:
            issue_score = min(issues // 10, 10)
            score += issue_score
            risk_factors.append(f"High issue count (+{issue_score})")
        
        # 5. Language risk (10 points)
        lang_risks = {"php": 10, "javascript": 8, "python": 6, "ruby": 6, "java": 4}
        if language in lang_risks:
            lang_score = lang_risks[language]
            score += lang_score
            risk_factors.append(f"High-risk language: {language} (+{lang_score})")
        
        # 6. Age and maintenance (10 points)
        try:
            created_at = repo.get("created_at", "")
            if created_at:
                created_date = datetime.datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                age_years = (datetime.datetime.now(created_date.tzinfo) - created_date).days / 365
                if 2 <= age_years <= 7:  # Vulnerable age range
                    score += 5
                    risk_factors.append(f"Vulnerable age: {age_years:.1f}y (+5)")
        except:
            pass
        
        return min(score, 100), risk_factors

    def analyze_repository_health(self, repo):
        """Analyze repository health and maintenance activity"""
        health_score = 0
        health_factors = []

        # Get repository details
        repo_name = repo.get("full_name", "")

        try:
            # Get detailed repository information
            response = requests.get(f"https://api.github.com/repos/{repo_name}", headers=HEADERS)
            if response.status_code == 200:
                repo_details = response.json()
            else:
                return 0, ["API access failed"]

            # 1. Maintenance Activity Analysis (30 points)
            last_push = repo_details.get("pushed_at", "")
            if last_push:
                from datetime import datetime
                try:
                    last_push_date = datetime.fromisoformat(last_push.replace('Z', '+00:00'))
                    days_since_push = (datetime.now(last_push_date.tzinfo) - last_push_date).days

                    if days_since_push <= 30:
                        health_score += 30
                        health_factors.append("Recently active (last 30 days)")
                    elif days_since_push <= 90:
                        health_score += 20
                        health_factors.append("Moderately active (last 90 days)")
                    elif days_since_push <= 365:
                        health_score += 10
                        health_factors.append("Low activity (last year)")
                    else:
                        health_factors.append(f"Stale repository ({days_since_push} days)")
                except:
                    pass

            # 2. Issue Management (20 points)
            open_issues = repo_details.get("open_issues_count", 0)
            if open_issues == 0:
                health_score += 20
                health_factors.append("No open issues")
            elif open_issues <= 10:
                health_score += 15
                health_factors.append("Few open issues")
            elif open_issues <= 50:
                health_score += 10
                health_factors.append("Moderate open issues")
            else:
                health_factors.append(f"Many open issues ({open_issues})")

            # 3. Release Management (15 points)
            try:
                releases_response = requests.get(f"https://api.github.com/repos/{repo_name}/releases", headers=HEADERS)
                if releases_response.status_code == 200:
                    releases = releases_response.json()
                    if releases:
                        latest_release = releases[0]
                        release_date = latest_release.get("published_at", "")
                        if release_date:
                            release_date_obj = datetime.fromisoformat(release_date.replace('Z', '+00:00'))
                            days_since_release = (datetime.now(release_date_obj.tzinfo) - release_date_obj).days

                            if days_since_release <= 90:
                                health_score += 15
                                health_factors.append("Recent release")
                            elif days_since_release <= 365:
                                health_score += 10
                                health_factors.append("Release within year")
                            else:
                                health_factors.append("Old releases")
                    else:
                        health_factors.append("No releases")
            except:
                pass

            # 4. Contributor Activity (15 points)
            try:
                contributors_response = requests.get(f"https://api.github.com/repos/{repo_name}/contributors", headers=HEADERS)
                if contributors_response.status_code == 200:
                    contributors = contributors_response.json()
                    contributor_count = len(contributors) if isinstance(contributors, list) else 0

                    if contributor_count >= 10:
                        health_score += 15
                        health_factors.append(f"Active community ({contributor_count} contributors)")
                    elif contributor_count >= 3:
                        health_score += 10
                        health_factors.append(f"Small team ({contributor_count} contributors)")
                    elif contributor_count >= 1:
                        health_score += 5
                        health_factors.append("Single maintainer")
                    else:
                        health_factors.append("No contributors")
            except:
                pass

            # 5. Documentation Quality (10 points)
            has_readme = repo_details.get("has_readme", False)
            has_wiki = repo_details.get("has_wiki", False)
            description = repo_details.get("description", "")

            doc_score = 0
            if has_readme:
                doc_score += 5
            if has_wiki:
                doc_score += 3
            if description and len(description) > 20:
                doc_score += 2

            health_score += doc_score
            if doc_score >= 8:
                health_factors.append("Good documentation")
            elif doc_score >= 5:
                health_factors.append("Basic documentation")
            else:
                health_factors.append("Poor documentation")

            # 6. Repository Size and Complexity (10 points)
            size_kb = repo_details.get("size", 0)
            if 100 <= size_kb <= 10000:  # 100KB to 10MB - reasonable size
                health_score += 10
                health_factors.append("Reasonable codebase size")
            elif size_kb > 50000:  # Very large
                health_factors.append("Very large codebase")
            elif size_kb < 10:  # Very small
                health_factors.append("Minimal codebase")

        except Exception as e:
            health_factors.append(f"Analysis error: {str(e)[:50]}")

        return min(health_score, 100), health_factors

    def analyze_security_hygiene(self, repo):
        """Analyze security hygiene controls and practices"""
        security_score = 0
        security_factors = []

        repo_name = repo.get("full_name", "")

        try:
            # 1. Security Policy and Documentation (25 points)
            try:
                # Check for SECURITY.md file
                security_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents/SECURITY.md", headers=HEADERS)
                if security_response.status_code == 200:
                    security_score += 15
                    security_factors.append("Has SECURITY.md policy")
                else:
                    # Check for .github/SECURITY.md
                    security_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents/.github/SECURITY.md", headers=HEADERS)
                    if security_response.status_code == 200:
                        security_score += 15
                        security_factors.append("Has .github/SECURITY.md policy")
                    else:
                        security_factors.append("No security policy found")
            except:
                pass

            # Check for vulnerability disclosure in README or docs
            try:
                readme_response = requests.get(f"https://api.github.com/repos/{repo_name}/readme", headers=HEADERS)
                if readme_response.status_code == 200:
                    readme_content = readme_response.json().get("content", "")
                    if readme_content:
                        import base64
                        readme_text = base64.b64decode(readme_content).decode('utf-8', errors='ignore').lower()
                        if any(term in readme_text for term in ["security", "vulnerability", "disclosure", "responsible"]):
                            security_score += 10
                            security_factors.append("Security info in README")
            except:
                pass

            # 2. Branch Protection (20 points)
            try:
                default_branch = repo.get("default_branch", "main")
                protection_response = requests.get(f"https://api.github.com/repos/{repo_name}/branches/{default_branch}/protection", headers=HEADERS)
                if protection_response.status_code == 200:
                    protection_data = protection_response.json()
                    security_score += 20
                    security_factors.append("Branch protection enabled")

                    # Check for required reviews
                    if protection_data.get("required_pull_request_reviews"):
                        security_score += 5
                        security_factors.append("Required PR reviews")
                else:
                    security_factors.append("No branch protection")
            except:
                pass

            # 3. CI/CD and Automated Checks (20 points)
            try:
                # Check for GitHub Actions workflows
                workflows_response = requests.get(f"https://api.github.com/repos/{repo_name}/actions/workflows", headers=HEADERS)
                if workflows_response.status_code == 200:
                    workflows = workflows_response.json().get("workflows", [])
                    if workflows:
                        security_score += 10
                        security_factors.append("Has CI/CD workflows")

                        # Check for security-related workflows
                        workflow_names = " ".join([w.get("name", "").lower() for w in workflows])
                        if any(term in workflow_names for term in ["security", "scan", "test", "lint", "audit"]):
                            security_score += 10
                            security_factors.append("Security-focused workflows")
                    else:
                        security_factors.append("No CI/CD workflows")
            except:
                pass

            # 4. Dependency Management (15 points)
            dependency_files = [
                "package-lock.json", "yarn.lock", "Pipfile.lock", "poetry.lock",
                "Gemfile.lock", "composer.lock", "go.sum", "Cargo.lock"
            ]

            lock_files_found = 0
            for dep_file in dependency_files:
                try:
                    dep_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents/{dep_file}", headers=HEADERS)
                    if dep_response.status_code == 200:
                        lock_files_found += 1
                except:
                    continue

            if lock_files_found > 0:
                security_score += 15
                security_factors.append(f"Dependency lock files ({lock_files_found})")
            else:
                security_factors.append("No dependency lock files")

            # 5. Code Quality Indicators (10 points)
            try:
                # Check for common quality files
                quality_files = [".gitignore", ".editorconfig", "eslintrc", "pylintrc", "rubocop"]
                quality_found = 0

                for quality_file in quality_files:
                    try:
                        quality_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents/{quality_file}", headers=HEADERS)
                        if quality_response.status_code == 200:
                            quality_found += 1
                    except:
                        continue

                if quality_found >= 2:
                    security_score += 10
                    security_factors.append("Code quality tools configured")
                elif quality_found >= 1:
                    security_score += 5
                    security_factors.append("Some quality tools")
                else:
                    security_factors.append("No quality tools detected")
            except:
                pass

            # 6. Issue and PR Templates (10 points)
            try:
                templates_found = 0
                template_files = [
                    ".github/ISSUE_TEMPLATE.md", ".github/PULL_REQUEST_TEMPLATE.md",
                    ".github/issue_template.md", ".github/pull_request_template.md"
                ]

                for template_file in template_files:
                    try:
                        template_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents/{template_file}", headers=HEADERS)
                        if template_response.status_code == 200:
                            templates_found += 1
                    except:
                        continue

                if templates_found >= 2:
                    security_score += 10
                    security_factors.append("Issue/PR templates configured")
                elif templates_found >= 1:
                    security_score += 5
                    security_factors.append("Some templates configured")
                else:
                    security_factors.append("No issue/PR templates")
            except:
                pass

        except Exception as e:
            security_factors.append(f"Security analysis error: {str(e)[:50]}")

        return min(security_score, 100), security_factors

    def analyze_development_practices(self, repo):
        """Analyze development practices and code maturity"""
        dev_score = 0
        dev_factors = []

        repo_name = repo.get("full_name", "")

        try:
            # 1. Semantic Versioning and Release Management (25 points)
            try:
                releases_response = requests.get(f"https://api.github.com/repos/{repo_name}/releases", headers=HEADERS)
                if releases_response.status_code == 200:
                    releases = releases_response.json()
                    if releases:
                        # Check for semantic versioning pattern
                        semver_count = 0
                        for release in releases[:5]:  # Check last 5 releases
                            tag_name = release.get("tag_name", "")
                            if tag_name:
                                # Check for semantic versioning pattern (x.y.z)
                                import re
                                if re.match(r'^v?\d+\.\d+\.\d+', tag_name):
                                    semver_count += 1

                        if semver_count >= 3:
                            dev_score += 25
                            dev_factors.append("Follows semantic versioning")
                        elif semver_count >= 1:
                            dev_score += 15
                            dev_factors.append("Some semantic versioning")
                        else:
                            dev_factors.append("No semantic versioning")

                        # Check for release notes
                        has_release_notes = any(release.get("body", "").strip() for release in releases[:3])
                        if has_release_notes:
                            dev_score += 10
                            dev_factors.append("Has release notes")
                    else:
                        dev_factors.append("No releases found")
            except:
                pass

            # 2. Testing Infrastructure (20 points)
            test_indicators = [
                "test/", "tests/", "spec/", "__tests__/", "test_", "_test.py",
                "pytest.ini", "jest.config", "phpunit.xml", "Rakefile"
            ]

            test_files_found = 0
            try:
                # Check repository contents for test indicators
                contents_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents", headers=HEADERS)
                if contents_response.status_code == 200:
                    contents = contents_response.json()
                    if isinstance(contents, list):
                        file_names = [item.get("name", "").lower() for item in contents]
                        for indicator in test_indicators:
                            if any(indicator in name for name in file_names):
                                test_files_found += 1
                                break

                if test_files_found > 0:
                    dev_score += 20
                    dev_factors.append("Has testing infrastructure")
                else:
                    dev_factors.append("No testing infrastructure detected")
            except:
                pass

            # 3. Documentation Quality (20 points)
            doc_score = 0
            try:
                # Check for comprehensive README
                readme_response = requests.get(f"https://api.github.com/repos/{repo_name}/readme", headers=HEADERS)
                if readme_response.status_code == 200:
                    readme_data = readme_response.json()
                    readme_size = readme_data.get("size", 0)

                    if readme_size > 2000:  # Substantial README
                        doc_score += 10
                        dev_factors.append("Comprehensive README")
                    elif readme_size > 500:
                        doc_score += 5
                        dev_factors.append("Basic README")
                    else:
                        dev_factors.append("Minimal README")

                # Check for additional documentation
                doc_files = ["CONTRIBUTING.md", "CHANGELOG.md", "docs/", "documentation/"]
                docs_found = 0
                for doc_file in doc_files:
                    try:
                        doc_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents/{doc_file}", headers=HEADERS)
                        if doc_response.status_code == 200:
                            docs_found += 1
                    except:
                        continue

                if docs_found >= 2:
                    doc_score += 10
                    dev_factors.append("Extensive documentation")
                elif docs_found >= 1:
                    doc_score += 5
                    dev_factors.append("Some documentation")

                dev_score += doc_score
            except:
                pass

            # 4. Code Organization and Structure (15 points)
            try:
                # Check for organized project structure
                structure_indicators = [
                    "src/", "lib/", "app/", "components/", "modules/",
                    "package.json", "setup.py", "Cargo.toml", "composer.json"
                ]

                structure_score = 0
                contents_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents", headers=HEADERS)
                if contents_response.status_code == 200:
                    contents = contents_response.json()
                    if isinstance(contents, list):
                        file_names = [item.get("name", "").lower() for item in contents]

                        # Check for organized structure
                        has_src_structure = any(indicator in file_names for indicator in structure_indicators[:5])
                        has_config_files = any(indicator in file_names for indicator in structure_indicators[5:])

                        if has_src_structure and has_config_files:
                            structure_score = 15
                            dev_factors.append("Well-organized project structure")
                        elif has_src_structure or has_config_files:
                            structure_score = 10
                            dev_factors.append("Basic project structure")
                        else:
                            dev_factors.append("Poor project organization")

                dev_score += structure_score
            except:
                pass

            # 5. Contribution Guidelines (10 points)
            try:
                contrib_files = ["CONTRIBUTING.md", ".github/CONTRIBUTING.md", "CONTRIBUTORS.md"]
                has_contrib_guide = False

                for contrib_file in contrib_files:
                    try:
                        contrib_response = requests.get(f"https://api.github.com/repos/{repo_name}/contents/{contrib_file}", headers=HEADERS)
                        if contrib_response.status_code == 200:
                            has_contrib_guide = True
                            break
                    except:
                        continue

                if has_contrib_guide:
                    dev_score += 10
                    dev_factors.append("Has contribution guidelines")
                else:
                    dev_factors.append("No contribution guidelines")
            except:
                pass

            # 6. License and Legal (10 points)
            license_info = repo.get("license")
            if license_info and license_info.get("name"):
                dev_score += 10
                dev_factors.append(f"Licensed: {license_info.get('name')}")
            else:
                dev_factors.append("No license specified")

        except Exception as e:
            dev_factors.append(f"Development analysis error: {str(e)[:50]}")

        return min(dev_score, 100), dev_factors

    def is_relevant_repository(self, repo):
        """Enhanced filtering to focus on most promising repositories"""
        desc = (repo.get("description", "") or "").lower()
        topics = repo.get("topics", []) or []
        
        # Skip if contains exclude keywords
        if any(exclude in desc for exclude in COMBINED_PATTERNS["exclude_keywords"]):
            return False
        
        # Skip if not English
        try:
            if desc and detect(desc) != "en":
                return False
        except:
            pass
        
        # Must have some vulnerability indicators
        has_vuln_indicators = (
            any(app in desc for app in COMBINED_PATTERNS["high_cve_applications"]) or
            any(feature in desc for feature in COMBINED_PATTERNS["critical_features"]) or
            any(keyword in desc for keyword in COMBINED_PATTERNS["vulnerability_keywords"])
        )
        
        return has_vuln_indicators

    def search_github_repositories(self):
        """Search GitHub for potentially vulnerable repositories"""
        print("🔍 Searching GitHub for vulnerable repositories...")
        
        all_repos = []
        languages = ["PHP", "JavaScript", "Python", "Ruby", "Java"]
        
        # CVE-focused search queries
        search_queries = [
            "admin panel", "file upload", "cms", "user management", "authentication",
            "api endpoint", "dashboard", "control panel", "media server", "gallery"
        ]
        
        for lang in languages:
            print(f"  📋 Scanning {lang} repositories...")
            
            for query in search_queries:
                for page in range(1, UNIFIED_CONFIG["search_pages"] + 1):
                    try:
                        since_date = (datetime.datetime.now() - 
                                    datetime.timedelta(days=UNIFIED_CONFIG["updated_days_ago"])).strftime("%Y-%m-%dT%H:%M:%SZ")
                        
                        search_params = {
                            "q": f"language:{lang} stars:{UNIFIED_CONFIG['min_stars']}..{UNIFIED_CONFIG['max_stars']} "
                                 f"pushed:>={since_date} {query} in:description",
                            "sort": "stars",
                            "order": "desc",
                            "per_page": 30,
                            "page": page
                        }
                        
                        response = requests.get(
                            "https://api.github.com/search/repositories",
                            headers=HEADERS,
                            params=search_params
                        )
                        
                        if response.status_code == 200:
                            repos = response.json().get("items", [])
                            for repo in repos:
                                if self.is_relevant_repository(repo):
                                    # Avoid duplicates
                                    if not any(r["full_name"] == repo["full_name"] for r in all_repos):
                                        all_repos.append(repo)
                        
                        elif response.status_code == 403:
                            print(f"    ⚠️ Rate limited, waiting...")
                            time.sleep(60)
                            break
                        
                        # Rate limiting
                        time.sleep(1)
                        
                    except Exception as e:
                        print(f"    ❌ Error searching: {e}")
                        continue
        
        print(f"  ✅ Found {len(all_repos)} potentially vulnerable repositories")
        self.scan_stats["total_repos_found"] = len(all_repos)
        return all_repos

    def clone_repository(self, repo_url, repo_name, clone_dir):
        """Clone a repository with timeout"""
        try:
            print(f"    📥 Cloning {repo_name}...")

            # Clean repo name for directory
            safe_name = repo_name.replace("/", "_").replace("\\", "_")
            repo_path = os.path.join(clone_dir, safe_name)

            # Remove if exists
            if os.path.exists(repo_path):
                shutil.rmtree(repo_path)

            # Fix clone URL - ensure it ends with .git
            if not repo_url.endswith('.git'):
                if repo_url.endswith('/'):
                    repo_url = repo_url + 'git'
                else:
                    repo_url = repo_url + '.git'

            # Clone with timeout
            cmd = ["git", "clone", "--depth", "1", repo_url, repo_path]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=UNIFIED_CONFIG["clone_timeout"]
            )

            if result.returncode == 0 and os.path.exists(repo_path):
                self.scan_stats["repos_cloned"] += 1
                print(f"    ✅ Successfully cloned {repo_name}")
                return repo_path
            else:
                print(f"    ❌ Clone failed for {repo_name}: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            print(f"    ⏰ Clone timeout for {repo_name}")
            return None
        except Exception as e:
            print(f"    ❌ Clone error for {repo_name}: {e}")
            return None

    def run_semgrep_analysis(self, repo_path, repo_name):
        """Run Semgrep analysis on a repository"""
        try:
            print(f"    🔍 Running Semgrep on {repo_name}...")

            # Prepare output file
            safe_name = repo_name.replace("/", "_").replace("\\", "_")
            output_file = os.path.join(UNIFIED_CONFIG["semgrep_results_dir"], f"{safe_name}_semgrep.json")

            # Ensure output directory exists
            os.makedirs(UNIFIED_CONFIG["semgrep_results_dir"], exist_ok=True)

            if UNIFIED_CONFIG["use_docker"]:
                # Run Semgrep in Docker with better volume mounting
                abs_repo_path = os.path.abspath(repo_path)
                abs_output_dir = os.path.abspath(UNIFIED_CONFIG["semgrep_results_dir"])

                cmd = [
                    "docker", "run", "--rm",
                    "-v", f"{abs_repo_path}:/src:ro",  # Read-only source
                    "-v", f"{abs_output_dir}:/output",
                    UNIFIED_CONFIG["docker_image"],
                    "--config=auto",
                    "--json",
                    "--output", f"/output/{safe_name}_semgrep.json",
                    "--verbose",
                    "/src"
                ]
            else:
                # Run Semgrep directly
                cmd = [
                    "semgrep",
                    "--config=auto",
                    "--json",
                    "--output", output_file,
                    "--verbose",
                    repo_path
                ]

            print(f"    📋 Running command: {' '.join(cmd[:5])}...")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=UNIFIED_CONFIG["semgrep_timeout"]
            )

            print(f"    📊 Semgrep exit code: {result.returncode}")
            if result.stderr:
                print(f"    📝 Semgrep stderr: {result.stderr[:200]}...")

            # Check if output file was created
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if not content.strip():
                            print(f"    ⚠️ Empty Semgrep output file")
                            return {"vulnerability_count": 0, "severity_score": 0, "categories": [], "raw_findings": []}

                        semgrep_data = json.loads(content)

                    findings = semgrep_data.get("results", [])
                    self.scan_stats["semgrep_scans_completed"] += 1

                    if findings:
                        self.scan_stats["vulnerabilities_found"] += len(findings)
                        print(f"    🚨 Found {len(findings)} potential vulnerabilities")
                    else:
                        print(f"    ✅ No vulnerabilities found")

                    return self.analyze_semgrep_results(findings, semgrep_data)

                except json.JSONDecodeError as e:
                    print(f"    ❌ Invalid Semgrep JSON output: {e}")
                    return {"vulnerability_count": 0, "severity_score": 0, "categories": [], "raw_findings": []}
            else:
                print(f"    ❌ No Semgrep output file generated at {output_file}")
                # Try to create a minimal result if Semgrep ran but didn't create output
                if result.returncode == 0:
                    print(f"    ℹ️ Semgrep completed successfully but found no issues")
                    return {"vulnerability_count": 0, "severity_score": 0, "categories": [], "raw_findings": []}
                else:
                    print(f"    ❌ Semgrep failed with exit code {result.returncode}")
                    return {"vulnerability_count": 0, "severity_score": 0, "categories": [], "raw_findings": []}

        except subprocess.TimeoutExpired:
            print(f"    ⏰ Semgrep timeout for {repo_name}")
            return {"vulnerability_count": 0, "severity_score": 0, "categories": [], "raw_findings": []}
        except Exception as e:
            print(f"    ❌ Semgrep error for {repo_name}: {e}")
            return {"vulnerability_count": 0, "severity_score": 0, "categories": [], "raw_findings": []}

    def analyze_semgrep_results(self, findings, full_semgrep_data=None):
        """Analyze Semgrep findings and calculate scores with full details"""
        if not findings:
            return {
                "vulnerability_count": 0,
                "severity_score": 0,
                "categories": [],
                "raw_findings": [],
                "detailed_findings": []
            }

        severity_weights = {
            "ERROR": 10,    # Critical vulnerabilities
            "WARNING": 5,   # Medium severity
            "INFO": 1       # Low severity
        }

        categories = set()
        total_severity = 0
        detailed_findings = []

        for finding in findings:
            severity = finding.get("extra", {}).get("severity", "INFO")
            total_severity += severity_weights.get(severity, 1)

            # Extract vulnerability categories
            rule_id = finding.get("check_id", "")
            if "sql" in rule_id.lower():
                categories.add("SQL Injection")
            elif "xss" in rule_id.lower() or "cross-site" in rule_id.lower():
                categories.add("XSS")
            elif "command" in rule_id.lower() or "injection" in rule_id.lower():
                categories.add("Command Injection")
            elif "path" in rule_id.lower() or "traversal" in rule_id.lower():
                categories.add("Path Traversal")
            elif "auth" in rule_id.lower():
                categories.add("Authentication")
            elif "crypto" in rule_id.lower():
                categories.add("Cryptography")
            elif "hardcoded" in rule_id.lower() or "secret" in rule_id.lower():
                categories.add("Hardcoded Secrets")
            elif "deserialization" in rule_id.lower() or "pickle" in rule_id.lower():
                categories.add("Deserialization")
            else:
                categories.add("Other")

            # Create detailed finding summary
            detailed_finding = {
                "rule_id": rule_id,
                "severity": severity,
                "message": finding.get("extra", {}).get("message", ""),
                "file": finding.get("path", ""),
                "line": finding.get("start", {}).get("line", 0),
                "code": finding.get("extra", {}).get("lines", ""),
                "category": list(categories)[-1] if categories else "Other"
            }
            detailed_findings.append(detailed_finding)

        return {
            "vulnerability_count": len(findings),
            "severity_score": min(total_severity, 100),  # Cap at 100
            "categories": list(categories),
            "raw_findings": findings,  # Store complete raw findings
            "detailed_findings": detailed_findings
        }

    def process_repository(self, repo):
        """Process a single repository: clone, scan, analyze"""
        repo_name = repo["full_name"]

        # Fix clone URL - use clone_url if available, otherwise construct from html_url
        if "clone_url" in repo and repo["clone_url"]:
            repo_url = repo["clone_url"]
        else:
            # Construct clone URL from html_url
            html_url = repo.get("html_url", "")
            if html_url:
                repo_url = html_url + ".git"
            else:
                print(f"    ❌ No valid URL found for {repo_name}")
                return None

        print(f"\n🎯 Processing: {repo_name}")
        print(f"    🔗 Clone URL: {repo_url}")

        # Calculate initial risk score
        risk_score, risk_factors = self.calculate_unified_risk_score(repo)

        # Perform comprehensive repository analysis
        print(f"    📊 Analyzing repository health...")
        health_score, health_factors = self.analyze_repository_health(repo)

        print(f"    🔒 Analyzing security hygiene...")
        security_score, security_factors = self.analyze_security_hygiene(repo)

        print(f"    🛠️ Analyzing development practices...")
        dev_score, dev_factors = self.analyze_development_practices(repo)

        # Clone repository
        repo_path = self.clone_repository(repo_url, repo_name, UNIFIED_CONFIG["repos_dir"])

        if repo_path:
            # Run Semgrep analysis
            semgrep_results = self.run_semgrep_analysis(repo_path, repo_name)

            # Clean up cloned repo to save space
            try:
                shutil.rmtree(repo_path)
                print(f"    🧹 Cleaned up {repo_name}")
            except Exception as e:
                print(f"    ⚠️ Failed to cleanup {repo_name}: {e}")
        else:
            semgrep_results = {
                "vulnerability_count": 0,
                "severity_score": 0,
                "categories": [],
                "raw_findings": [],
                "detailed_findings": []
            }

        # Calculate comprehensive final score
        # Weight: Risk (40%) + Semgrep (30%) + Health (10%) + Security (10%) + Development (10%)
        comprehensive_score = (
            (risk_score * 0.4) +
            (semgrep_results["severity_score"] * 0.3) +
            (health_score * 0.1) +
            (security_score * 0.1) +
            (dev_score * 0.1)
        )
        final_score = min(comprehensive_score, 100)

        if final_score >= 70:
            self.scan_stats["high_risk_repos"] += 1

        # Store comprehensive results
        result = {
            "repo_name": repo_name,
            "repo_url": repo["html_url"],
            "clone_url": repo_url,
            "stars": repo.get("stargazers_count", 0),
            "issues": repo.get("open_issues_count", 0),
            "language": repo.get("language", ""),
            "description": repo.get("description", ""),
            "risk_score": risk_score,
            "health_score": health_score,
            "security_score": security_score,
            "development_score": dev_score,
            "semgrep_vulnerabilities": semgrep_results["vulnerability_count"],
            "semgrep_severity_score": semgrep_results["severity_score"],
            "vulnerability_categories": "; ".join(semgrep_results["categories"]),
            "final_score": final_score,
            "risk_factors": "; ".join(risk_factors[:3]),
            "health_factors": "; ".join(health_factors[:3]),
            "security_factors": "; ".join(security_factors[:3]),
            "development_factors": "; ".join(dev_factors[:3]),
            "last_updated": repo.get("pushed_at", ""),
            "created_at": repo.get("created_at", ""),
            "semgrep_detailed_findings": semgrep_results.get("detailed_findings", []),
            "semgrep_raw_findings": semgrep_results.get("raw_findings", [])
        }

        self.repos_analyzed.append(result)
        print(f"    ✅ {repo_name} - Final Score: {final_score:.1f} (Risk: {risk_score}, Semgrep: {semgrep_results['severity_score']})")
        return result

    def save_results(self):
        """Save top N results with comprehensive analysis details to CSV and JSON"""
        print("\n📊 Saving results...")

        # Sort by final score (highest first) and keep only top N
        sorted_repos = sorted(self.repos_analyzed, key=lambda x: x["final_score"], reverse=True)
        top_n_repos = sorted_repos[:UNIFIED_CONFIG["top_repos_to_keep"]]

        print(f"  🎯 Keeping top {UNIFIED_CONFIG['top_repos_to_keep']} repositories out of {len(sorted_repos)} analyzed")

        # Enhanced CSV headers with comprehensive analysis information
        csv_headers = [
            "rank", "repo_name", "repo_url", "clone_url", "stars", "issues", "language", "description",
            "risk_score", "health_score", "security_score", "development_score",
            "semgrep_vulnerabilities", "semgrep_severity_score", "vulnerability_categories",
            "final_score", "risk_factors", "health_factors", "security_factors", "development_factors",
            "last_updated", "created_at", "semgrep_findings_summary", "detailed_vulnerabilities"
        ]

        # Prepare top N data with enhanced details
        top_n_enhanced = []
        for rank, repo in enumerate(top_n_repos, 1):
            # Create detailed vulnerability summary
            detailed_vulns = []
            semgrep_findings = repo.get("semgrep_detailed_findings", [])

            for finding in semgrep_findings:
                vuln_detail = f"[{finding.get('severity', 'INFO')}] {finding.get('rule_id', 'Unknown')}: {finding.get('message', 'No message')} (File: {finding.get('file', 'Unknown')}, Line: {finding.get('line', 0)})"
                detailed_vulns.append(vuln_detail)

            # Create findings summary
            if semgrep_findings:
                severity_counts = {}
                for finding in semgrep_findings:
                    sev = finding.get('severity', 'INFO')
                    severity_counts[sev] = severity_counts.get(sev, 0) + 1

                summary_parts = []
                for sev in ['ERROR', 'WARNING', 'INFO']:
                    if sev in severity_counts:
                        summary_parts.append(f"{sev}: {severity_counts[sev]}")
                findings_summary = "; ".join(summary_parts)
            else:
                findings_summary = "No vulnerabilities found"

            enhanced_repo = {
                "rank": rank,
                "repo_name": repo["repo_name"],
                "repo_url": repo["repo_url"],
                "clone_url": repo.get("clone_url", ""),
                "stars": repo["stars"],
                "issues": repo["issues"],
                "language": repo["language"],
                "description": repo["description"],
                "risk_score": repo["risk_score"],
                "health_score": repo.get("health_score", 0),
                "security_score": repo.get("security_score", 0),
                "development_score": repo.get("development_score", 0),
                "semgrep_vulnerabilities": repo["semgrep_vulnerabilities"],
                "semgrep_severity_score": repo["semgrep_severity_score"],
                "vulnerability_categories": repo["vulnerability_categories"],
                "final_score": repo["final_score"],
                "risk_factors": repo["risk_factors"],
                "health_factors": repo.get("health_factors", ""),
                "security_factors": repo.get("security_factors", ""),
                "development_factors": repo.get("development_factors", ""),
                "last_updated": repo["last_updated"],
                "created_at": repo["created_at"],
                "semgrep_findings_summary": findings_summary,
                "detailed_vulnerabilities": " | ".join(detailed_vulns) if detailed_vulns else "None"
            }
            top_n_enhanced.append(enhanced_repo)

        # Save main CSV with top N only
        try:
            # Clean data to ensure proper encoding
            cleaned_data = []
            for repo in top_n_enhanced:
                cleaned_repo = {}
                for key, value in repo.items():
                    if isinstance(value, str):
                        # Replace problematic characters and ensure ASCII-safe strings
                        cleaned_value = value.encode('ascii', 'replace').decode('ascii')
                        cleaned_repo[key] = cleaned_value
                    else:
                        cleaned_repo[key] = value
                cleaned_data.append(cleaned_repo)

            with open(UNIFIED_CONFIG["output_csv"], "w", newline="", encoding="utf-8") as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=csv_headers, extrasaction='ignore')
                writer.writeheader()
                writer.writerows(cleaned_data)
            print(f"  ✅ CSV saved successfully: {UNIFIED_CONFIG['output_csv']}")
        except Exception as e:
            print(f"  ❌ Error saving CSV: {e}")
            # Try to save a simplified version with basic ASCII only
            simplified_headers = ["rank", "repo_name", "repo_url", "final_score", "semgrep_vulnerabilities"]
            simplified_data = []
            for repo in top_n_enhanced:
                simplified_repo = {}
                for key in simplified_headers:
                    value = repo.get(key, "")
                    if isinstance(value, str):
                        # Ensure ASCII-safe
                        simplified_repo[key] = value.encode('ascii', 'replace').decode('ascii')
                    else:
                        simplified_repo[key] = value
                simplified_data.append(simplified_repo)

            with open("simplified_results.csv", "w", newline="", encoding="utf-8") as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=simplified_headers)
                writer.writeheader()
                writer.writerows(simplified_data)
            print(f"  ✅ Simplified CSV saved: simplified_results.csv")

        # Save detailed JSON log with full Semgrep data for top 10
        detailed_data = {
            "scan_timestamp": datetime.datetime.now().isoformat(),
            "scan_statistics": self.scan_stats,
            "configuration": UNIFIED_CONFIG,
            "total_repositories_analyzed": len(sorted_repos),
            "top_repositories": top_n_repos,  # Includes full semgrep_raw_findings
            "all_repositories_summary": [
                {
                    "repo_name": repo["repo_name"],
                    "final_score": repo["final_score"],
                    "semgrep_vulnerabilities": repo["semgrep_vulnerabilities"]
                } for repo in sorted_repos
            ]
        }

        with open(UNIFIED_CONFIG["detailed_log"], "w", encoding="utf-8") as jsonfile:
            json.dump(detailed_data, jsonfile, indent=2, default=str)

        print(f"  ✅ Top 10 results: {UNIFIED_CONFIG['output_csv']}")
        print(f"  📋 Detailed log: {UNIFIED_CONFIG['detailed_log']}")

        # Print top N summary
        print(f"\n🏆 TOP {UNIFIED_CONFIG['top_repos_to_keep']} REPOSITORIES:")
        for i, repo in enumerate(top_n_enhanced, 1):
            print(f"  {i:2d}. {repo['repo_name']} - Score: {repo['final_score']:.1f} ({repo['semgrep_vulnerabilities']} vulns)")

        return len(sorted_repos), len(top_n_repos)

    def run_scan(self, max_repos=None):
        """Run the complete unified vulnerability scan"""
        if UNIFIED_CONFIG["in_container"]:
            print("🐳 UNIFIED VULNERABILITY SCANNER - CONTAINER MODE")
            print("=" * 60)
            print("Running inside Docker container with integrated Semgrep")
        else:
            print("🎯 UNIFIED VULNERABILITY SCANNER")
            print("=" * 50)
            print("Combining CVE trends + Semgrep analysis")
        print()

        # Force direct Semgrep when running in container
        if UNIFIED_CONFIG["in_container"]:
            UNIFIED_CONFIG["use_docker"] = False
            print("✅ Using direct Semgrep execution (container mode)")
        elif UNIFIED_CONFIG["use_docker"]:
            try:
                result = subprocess.run(["docker", "--version"], capture_output=True)
                if result.returncode != 0:
                    print("❌ Docker not available, switching to direct Semgrep")
                    UNIFIED_CONFIG["use_docker"] = False
                else:
                    print("✅ Docker available for Semgrep analysis")
            except:
                print("❌ Docker not available, switching to direct Semgrep")
                UNIFIED_CONFIG["use_docker"] = False
        else:
            print("✅ Using direct Semgrep execution")

        # Search for repositories
        repos = self.search_github_repositories()

        if not repos:
            print("❌ No repositories found")
            return

        # Limit repositories for analysis (we'll analyze more but keep only top 10)
        analysis_limit = max_repos if max_repos else UNIFIED_CONFIG["max_repos_to_analyze"]
        if len(repos) > analysis_limit:
            repos = repos[:analysis_limit]
            print(f"🎯 Limited to {len(repos)} repositories for analysis (will keep top 10)")

        # Process repositories with threading for efficiency
        print(f"\n🚀 Processing {len(repos)} repositories...")
        print(f"   Max concurrent scans: {UNIFIED_CONFIG['max_concurrent_scans']}")

        with ThreadPoolExecutor(max_workers=UNIFIED_CONFIG['max_concurrent_scans']) as executor:
            # Submit all tasks
            future_to_repo = {executor.submit(self.process_repository, repo): repo for repo in repos}

            # Process completed tasks
            for future in as_completed(future_to_repo):
                repo = future_to_repo[future]
                try:
                    result = future.result()
                    print(f"  ✅ Completed: {result['repo_name']} (Score: {result['final_score']:.1f})")
                except Exception as e:
                    print(f"  ❌ Failed: {repo['full_name']} - {e}")

        # Save results
        total_repos, high_priority_count = self.save_results()

        # Print summary
        self.print_summary(total_repos, high_priority_count)

    def print_summary(self, total_repos, high_priority_count):
        """Print scan summary statistics"""
        print(f"\n📊 SCAN SUMMARY")
        print("=" * 40)
        print(f"Repositories found: {self.scan_stats['total_repos_found']}")
        print(f"Repositories cloned: {self.scan_stats['repos_cloned']}")
        print(f"Semgrep scans completed: {self.scan_stats['semgrep_scans_completed']}")
        print(f"Total vulnerabilities found: {self.scan_stats['vulnerabilities_found']}")
        print(f"High-risk repositories: {high_priority_count}")

        if self.repos_analyzed:
            avg_score = sum(repo["final_score"] for repo in self.repos_analyzed) / len(self.repos_analyzed)
            print(f"Average final score: {avg_score:.1f}")

            # Top vulnerability categories
            all_categories = []
            for repo in self.repos_analyzed:
                if repo["vulnerability_categories"]:
                    all_categories.extend(repo["vulnerability_categories"].split("; "))

            if all_categories:
                from collections import Counter
                top_categories = Counter(all_categories).most_common(5)
                print(f"\nTop vulnerability categories:")
                for category, count in top_categories:
                    print(f"  {category}: {count}")

        print(f"\n🎯 NEXT STEPS:")
        print("1. Review high_priority_unified_results.csv for immediate targets")
        print("2. Check Semgrep results in semgrep_results/ directory")
        print("3. Focus on repositories with both high risk scores and Semgrep findings")
        print("4. Follow responsible disclosure practices")

def main():
    parser = argparse.ArgumentParser(description="Unified Vulnerability Scanner with Comprehensive Analysis")
    parser.add_argument("--max-repos", type=int, default=100, help="Maximum repositories to analyze")
    parser.add_argument("--top-repos", type=int, default=10, help="Number of top repositories to keep in CSV")
    parser.add_argument("--pages", type=int, default=5, help="Search pages per query")
    parser.add_argument("--min-stars", type=int, default=50, help="Minimum star count")
    parser.add_argument("--max-stars", type=int, default=2000, help="Maximum star count")
    parser.add_argument("--no-docker", action="store_true", help="Don't use Docker for Semgrep")
    parser.add_argument("--concurrent", type=int, default=2, help="Max concurrent scans")
    parser.add_argument("--timeout", type=int, default=300, help="Semgrep timeout in seconds")

    args = parser.parse_args()

    # Update configuration
    UNIFIED_CONFIG["search_pages"] = args.pages
    UNIFIED_CONFIG["min_stars"] = args.min_stars
    UNIFIED_CONFIG["max_stars"] = args.max_stars
    UNIFIED_CONFIG["max_concurrent_scans"] = args.concurrent
    UNIFIED_CONFIG["semgrep_timeout"] = args.timeout
    UNIFIED_CONFIG["use_docker"] = not args.no_docker
    UNIFIED_CONFIG["top_repos_to_keep"] = args.top_repos

    # Check GitHub token
    if UNIFIED_CONFIG["github_token"] == "your_github_token_here":
        print("❌ Please set GITHUB_TOKEN environment variable")
        return

    # Run scanner
    scanner = UnifiedVulnerabilityScanner()
    scanner.run_scan(max_repos=args.max_repos)

if __name__ == "__main__":
    main()
