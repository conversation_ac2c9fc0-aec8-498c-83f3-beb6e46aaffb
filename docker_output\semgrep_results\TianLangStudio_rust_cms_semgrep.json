{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "start": {"line": 627, "col": 5, "offset": 17158}, "end": {"line": 627, "col": 27, "offset": 17180}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "start": {"line": 1351, "col": 9, "offset": 37531}, "end": {"line": 1351, "col": 155, "offset": 37677}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "start": {"line": 1355, "col": 9, "offset": 37829}, "end": {"line": 1355, "col": 42, "offset": 37862}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "start": {"line": 1367, "col": 20, "offset": 38249}, "end": {"line": 1367, "col": 69, "offset": 38298}, "extra": {"message": "RegExp() called with a `_ref` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "start": {"line": 1388, "col": 7, "offset": 38997}, "end": {"line": 1388, "col": 37, "offset": 39027}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "start": {"line": 2051, "col": 13, "offset": 59716}, "end": {"line": 2053, "col": 27, "offset": 59822}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "start": {"line": 2185, "col": 9, "offset": 69387}, "end": {"line": 2185, "col": 40, "offset": 69418}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base.html", "start": {"line": 17, "col": 9, "offset": 813}, "end": {"line": 18, "col": 50, "offset": 976}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base.html", "start": {"line": 201, "col": 25, "offset": 11807}, "end": {"line": 201, "col": 393, "offset": 12175}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base.html", "start": {"line": 203, "col": 22, "offset": 12237}, "end": {"line": 203, "col": 102, "offset": 12317}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base.html", "start": {"line": 278, "col": 9, "offset": 16668}, "end": {"line": 278, "col": 110, "offset": 16769}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base.html", "start": {"line": 280, "col": 9, "offset": 16816}, "end": {"line": 280, "col": 112, "offset": 16919}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base_cn.html", "start": {"line": 17, "col": 9, "offset": 816}, "end": {"line": 18, "col": 50, "offset": 979}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base_cn.html", "start": {"line": 204, "col": 25, "offset": 11984}, "end": {"line": 204, "col": 393, "offset": 12352}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base_cn.html", "start": {"line": 206, "col": 22, "offset": 12414}, "end": {"line": 206, "col": 102, "offset": 12494}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base_cn.html", "start": {"line": 281, "col": 9, "offset": 16876}, "end": {"line": 281, "col": 110, "offset": 16977}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base_cn.html", "start": {"line": 283, "col": 9, "offset": 17024}, "end": {"line": 283, "col": 112, "offset": 17127}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "start": {"line": 163, "col": 17, "offset": 7761}, "end": {"line": 165, "col": 21, "offset": 7917}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "message": "Timeout when running javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp on downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "message": "Timeout when running javascript.aws-lambda.security.tainted-eval.tainted-eval on downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "message": "Timeout when running javascript.aws-lambda.security.tainted-eval.tainted-eval on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "message": "Timeout when running javascript.aws-lambda.security.tainted-eval.tainted-eval on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "message": "Timeout when running javascript.aws-lambda.security.tainted-eval.tainted-eval on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js:\n ", "path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 12, "offset": 72}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 21, "col": 20, "offset": 74}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 73, "col": 1, "offset": 0}, "end": {"line": 76, "col": 12, "offset": 54}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 80, "col": 1, "offset": 0}, "end": {"line": 80, "col": 22, "offset": 21}}]], "message": "Syntax error at line downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html:1:\n `{% extends \"freelancer/common/base.html\" %}\n{% block head %}\n{{super()}}` was unexpected", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "spans": [{"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 12, "offset": 72}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 21, "col": 20, "offset": 74}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 73, "col": 1, "offset": 0}, "end": {"line": 76, "col": 12, "offset": 54}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "start": {"line": 80, "col": 1, "offset": 0}, "end": {"line": 80, "col": 22, "offset": 21}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 74}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 19, "offset": 39}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 22, "offset": 21}}]], "message": "Syntax error at line downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html:1:\n `{% extends \"freelancer/common/base.html\" %}\n{% block head %}\n  {{super()}}` was unexpected", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "spans": [{"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 74}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 19, "offset": 39}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 22, "offset": 21}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 12, "offset": 72}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 12, "col": 30, "offset": 114}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 53, "col": 32, "offset": 0}, "end": {"line": 53, "col": 47, "offset": 15}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 63, "col": 1, "offset": 0}, "end": {"line": 63, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html:1:\n `{% extends \"freelancer/common/base.html\" %}\n{% block head %}\n{{super()}}` was unexpected", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "spans": [{"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 12, "offset": 72}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 12, "col": 30, "offset": 114}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 53, "col": 32, "offset": 0}, "end": {"line": 53, "col": 47, "offset": 15}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "start": {"line": 63, "col": 1, "offset": 0}, "end": {"line": 63, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 76}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 39}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 22, "offset": 21}}]], "message": "Syntax error at line downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html:1:\n `{% extends \"freelancer/common/base.html\" %}\r\n{% block head %}\r\n  {{super()}}` was unexpected", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "spans": [{"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 76}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 39}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 22, "offset": 21}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 74}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 13, "col": 19, "offset": 38}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "start": {"line": 229, "col": 1, "offset": 0}, "end": {"line": 229, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html:1:\n `{% extends \"freelancer/common/base.html\" %}\n{% block head %}\n  {{super()}}` was unexpected", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "spans": [{"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 74}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 13, "col": 19, "offset": 38}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "start": {"line": 229, "col": 1, "offset": 0}, "end": {"line": 229, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 77}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 38}}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "start": {"line": 220, "col": 1, "offset": 0}, "end": {"line": 220, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html:1:\n `{% extends \"freelancer/common/base_cn.html\" %}\n{% block head %}\n  {{super()}}` was unexpected", "path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "spans": [{"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 14, "offset": 77}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 38}}, {"file": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "start": {"line": 220, "col": 1, "offset": 0}, "end": {"line": 220, "col": 23, "offset": 22}}]}], "paths": {"scanned": ["downloaded_repos/TianLangStudio_rust_cms/.gitignore", "downloaded_repos/TianLangStudio_rust_cms/Cargo.lock", "downloaded_repos/TianLangStudio_rust_cms/Cargo.toml", "downloaded_repos/TianLangStudio_rust_cms/LICENSE", "downloaded_repos/TianLangStudio_rust_cms/README.md", "downloaded_repos/TianLangStudio_rust_cms/README_CN.md", "downloaded_repos/TianLangStudio_rust_cms/bin/init_db.sh", "downloaded_repos/TianLangStudio_rust_cms/bin/release.sh", "downloaded_repos/TianLangStudio_rust_cms/bin/start.sh", "downloaded_repos/TianLangStudio_rust_cms/common/Cargo.toml", "downloaded_repos/TianLangStudio_rust_cms/common/src/config_util.rs", "downloaded_repos/TianLangStudio_rust_cms/common/src/db_util.rs", "downloaded_repos/TianLangStudio_rust_cms/common/src/lib.rs", "downloaded_repos/TianLangStudio_rust_cms/common/src/log_util.rs", "downloaded_repos/TianLangStudio_rust_cms/common/src/result.rs", "downloaded_repos/TianLangStudio_rust_cms/common/src/sign_util.rs", "downloaded_repos/TianLangStudio_rust_cms/conf/application.yaml", "downloaded_repos/TianLangStudio_rust_cms/conf/application_dev.yaml", "downloaded_repos/TianLangStudio_rust_cms/conf/application_prod.yaml", "downloaded_repos/TianLangStudio_rust_cms/conf/log4rs.yaml", "downloaded_repos/TianLangStudio_rust_cms/dao/.env", "downloaded_repos/TianLangStudio_rust_cms/dao/Cargo.toml", "downloaded_repos/TianLangStudio_rust_cms/dao/diesel.toml", "downloaded_repos/TianLangStudio_rust_cms/dao/migrations/.gitkeep", "downloaded_repos/TianLangStudio_rust_cms/dao/src/lib.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/models/articlemod.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/models/filemod.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/models/mod.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/models/usermod.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/repos/articlerepo.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/repos/filerepo.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/repos/mod.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/repos/userrepo.rs", "downloaded_repos/TianLangStudio_rust_cms/dao/src/schema.rs", "downloaded_repos/TianLangStudio_rust_cms/diesel.toml", "downloaded_repos/TianLangStudio_rust_cms/doc/ChangeLog.md", "downloaded_repos/TianLangStudio_rust_cms/doc/db/db.sql", "downloaded_repos/TianLangStudio_rust_cms/doc/db/rust_cms.sql", "downloaded_repos/TianLangStudio_rust_cms/doc/imgs/rust_cms_screenshot_en.png", "downloaded_repos/TianLangStudio_rust_cms/doc/imgs/screenshot.png", "downloaded_repos/TianLangStudio_rust_cms/static/ads.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/codemirror/5.48.4/codemirror.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/all.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/all.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/brands.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/brands.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/fontawesome.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/fontawesome.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/regular.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/regular.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/solid.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/solid.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/svg-with-js.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/svg-with-js.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/v4-shims.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/css/v4-shims.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/brands.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/regular.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/v4-shims.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-brands-400.eot", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-brands-400.svg", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-brands-400.ttf", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-brands-400.woff", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-brands-400.woff2", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-regular-400.eot", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-regular-400.svg", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-regular-400.ttf", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-regular-400.woff", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-regular-400.woff2", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-solid-900.eot", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-solid-900.svg", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-solid-900.ttf", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-solid-900.woff", "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/webfonts/fa-solid-900.woff2", "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.bubble.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.snow.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/share/css/share.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/share/images/share_core_square.jpg", "downloaded_repos/TianLangStudio_rust_cms/static/com/share/images/social.png", "downloaded_repos/TianLangStudio_rust_cms/static/com/share/js/share.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/font/summernote.eot", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/font/summernote.ttf", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/font/summernote.woff", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/font/summernote.woff2", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ar-AR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ar-AR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-az-AZ.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-az-AZ.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-bg-BG.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-bg-BG.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ca-ES.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ca-ES.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-cs-CZ.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-cs-CZ.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-da-DK.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-da-DK.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-de-DE.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-de-DE.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-el-GR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-el-GR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-es-ES.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-es-ES.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-es-EU.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-es-EU.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fa-IR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fa-IR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fi-FI.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fi-FI.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fr-FR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fr-FR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-gl-ES.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-gl-ES.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-he-IL.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-he-IL.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-hr-HR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-hr-HR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-hu-HU.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-hu-HU.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-id-ID.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-id-ID.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-it-IT.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-it-IT.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ja-JP.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ja-JP.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ko-KR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ko-KR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-lt-LT.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-lt-LT.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-lt-LV.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-lt-LV.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-mn-MN.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-mn-MN.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-nb-NO.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-nb-NO.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-nl-NL.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-nl-NL.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pl-PL.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pl-PL.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pt-BR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pt-BR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pt-PT.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pt-PT.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ro-RO.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ro-RO.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ru-RU.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ru-RU.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sk-SK.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sk-SK.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sl-SI.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sl-SI.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sr-RS-Latin.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sr-RS-Latin.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sr-RS.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sr-RS.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sv-SE.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sv-SE.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ta-IN.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ta-IN.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-th-TH.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-th-TH.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-tr-TR.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-tr-TR.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-uk-UA.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-uk-UA.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-uz-UZ.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-uz-UZ.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-vi-VN.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-vi-VN.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-zh-CN.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-zh-CN.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-zh-TW.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-zh-TW.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/databasic/summernote-ext-databasic.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/databasic/summernote-ext-databasic.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/hello/summernote-ext-hello.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/specialchars/summernote-ext-specialchars.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js.map", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.min.js.map", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js.map", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.min.js.map", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js.map", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.min.js.LICENSE.txt", "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.min.js.map", "downloaded_repos/TianLangStudio_rust_cms/static/com/tuieditor/2.3.0/toastui-editor-only.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/tuieditor/2.3.0/toastui-editor-viewer.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/com/tuieditor/2.3.0/toastui-editor.min.css", "downloaded_repos/TianLangStudio_rust_cms/static/img/5-star-feedback-upwork.png", "downloaded_repos/TianLangStudio_rust_cms/static/img/beian.png", "downloaded_repos/TianLangStudio_rust_cms/static/img/code_thinking.svg", "downloaded_repos/TianLangStudio_rust_cms/static/img/farm_girl.png", "downloaded_repos/TianLangStudio_rust_cms/static/img/farm_girl.svg", "downloaded_repos/TianLangStudio_rust_cms/static/img/favicon.ico", "downloaded_repos/TianLangStudio_rust_cms/static/img/logo.png", "downloaded_repos/TianLangStudio_rust_cms/static/img/qqgroup-keeplearning.png", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/assets/img/avataaars.svg", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/assets/img/portfolio/cabin.png", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/assets/img/portfolio/cake.png", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/assets/img/portfolio/circus.png", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/assets/img/portfolio/game.png", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/assets/img/portfolio/safe.png", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/assets/img/portfolio/submarine.png", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/css/article.css", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/css/body.css", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/css/heading.css", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/css/styles.css", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/js/article.js", "downloaded_repos/TianLangStudio_rust_cms/static/tmpl/freelancer/js/scripts.js", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base.html", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/common/base_cn.html", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "downloaded_repos/TianLangStudio_rust_cms/template/sitemap.xml", "downloaded_repos/TianLangStudio_rust_cms/web/Cargo.toml", "downloaded_repos/TianLangStudio_rust_cms/web/src/articlectrl.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/filectrl.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/funs/article.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/funs/mod.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/funs/user.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/indexctrl.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/main.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/middleware.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/userctrl.rs", "downloaded_repos/TianLangStudio_rust_cms/web/src/web_util.rs"], "skipped": [{"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/all.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/all.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/brands.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/regular.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/v4-shims.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.min.js.map", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ar-AR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-az-AZ.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-bg-BG.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ca-ES.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-cs-CZ.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-da-DK.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-de-DE.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-el-GR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-es-ES.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-es-EU.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fa-IR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fi-FI.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-fr-FR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-gl-ES.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-he-IL.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-hr-HR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-hu-HU.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-id-ID.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-it-IT.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ja-JP.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ko-KR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-lt-LT.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-lt-LV.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-mn-MN.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-nb-NO.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-nl-NL.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pl-PL.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pt-BR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-pt-PT.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ro-RO.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ru-RU.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sk-SK.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sl-SI.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sr-RS-Latin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sr-RS.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-sv-SE.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-ta-IN.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-th-TH.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-tr-TR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-uk-UA.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-uz-UZ.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-vi-VN.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-zh-CN.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/lang/summernote-zh-TW.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/tuieditor/2.3.0/toastui-editor-all.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/com/tuieditor/2.3.0/toastui-editor-viewer.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/static/js/jquery/3.5.1/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/admin/article/edit.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/article.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/articles.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/error.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TianLangStudio_rust_cms/template/freelancer/index_cn.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.926624059677124, "profiling_times": {"config_time": 6.561364650726318, "core_time": 28.381687879562378, "ignores_time": 0.002160310745239258, "total_time": 34.94662690162659}, "parsing_time": {"total_time": 12.592331409454346, "per_file_time": {"mean": 0.17735678041484998, "std_dev": 0.09097084513124144}, "very_slow_stats": {"time_ratio": 0.4579314559323672, "count_ratio": 0.07042253521126761}, "very_slow_files": [{"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/regular.js", "ftime": 0.42566990852355957}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/brands.js", "ftime": 0.8169589042663574}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "ftime": 1.2856988906860352}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.js", "ftime": 1.5593979358673096}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.js", "ftime": 1.678699016571045}]}, "scanning_time": {"total_time": 130.26029992103577, "per_file_time": {"mean": 0.22078016935768782, "std_dev": 2.781584296927571}, "very_slow_stats": {"time_ratio": 0.8029238063655589, "count_ratio": 0.01864406779661017}, "very_slow_files": [{"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js", "ftime": 1.7851378917694092}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/brands.js", "ftime": 2.1755950450897217}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.js", "ftime": 4.200829982757568}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.js", "ftime": 5.391600131988525}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.js", "ftime": 5.45647120475769}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "ftime": 6.627824783325195}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote.js", "ftime": 17.551491022109985}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-bs4.js", "ftime": 17.58933687210083}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/summernote-lite.js", "ftime": 17.66167187690735}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/quill/v1.3.6/quill.core.js", "ftime": 24.484076023101807}]}, "matching_time": {"total_time": 15.118923664093018, "per_file_and_rule_time": {"mean": 0.034597079322867305, "std_dev": 0.010754578363374326}, "very_slow_stats": {"time_ratio": 0.5795644299698225, "count_ratio": 0.06636155606407322}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/brands.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2618420124053955}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.32129693031311035}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.3338620662689209}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.3736281394958496}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/solid.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.39731907844543457}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.4245719909667969}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.43887901306152344}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.49892210960388184}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.6420331001281738}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.6539208889007568}]}, "tainting_time": {"total_time": 3.855959415435791, "per_def_and_rule_time": {"mean": 0.0021174955603711094, "std_dev": 9.2720111650088e-05}, "very_slow_stats": {"time_ratio": 0.3733003995655493, "count_ratio": 0.009884678747940691}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/databasic/summernote-ext-databasic.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.06097817420959473}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.07486510276794434}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/specialchars/summernote-ext-specialchars.js", "fline": 1, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.07519912719726562}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/specialchars/summernote-ext-specialchars.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.07789993286132812}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.08274602890014648}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.09630990028381348}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/summernote/0.8.18/plugin/specialchars/summernote-ext-specialchars.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.09742999076843262}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.10305595397949219}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/fontawesome.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.12946295738220215}, {"fpath": "downloaded_repos/TianLangStudio_rust_cms/static/com/fontawesome/5.13.1/js/conflict-detection.js", "fline": 293, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.21134209632873535}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}