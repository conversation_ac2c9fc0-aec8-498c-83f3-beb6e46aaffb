{"version": "1.130.0", "results": [{"check_id": "python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/auth.py", "start": {"line": 95, "col": 9, "offset": 2866}, "end": {"line": 95, "col": 43, "offset": 2900}, "extra": {"message": "Detected Flask route directly returning a formatted string. This is subject to cross-site scripting if user input can reach the string. Consider using the template engine instead and rendering pages with 'render_template()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "shortlink": "https://sg.run/Zv6o"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/sync.py", "start": {"line": 232, "col": 36, "offset": 6961}, "end": {"line": 232, "col": 50, "offset": 6975}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/sync.py", "start": {"line": 244, "col": 17, "offset": 7324}, "end": {"line": 244, "col": 45, "offset": 7352}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "start": {"line": 97, "col": 46, "offset": 2729}, "end": {"line": 97, "col": 49, "offset": 2732}}]], "message": "Syntax error at line downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml:97:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "spans": [{"file": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "start": {"line": 97, "col": 46, "offset": 2729}, "end": {"line": 97, "col": 49, "offset": 2732}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "start": {"line": 138, "col": 46, "offset": 3922}, "end": {"line": 138, "col": 49, "offset": 3925}}]], "message": "Syntax error at line downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml:138:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "spans": [{"file": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "start": {"line": 138, "col": 46, "offset": 3922}, "end": {"line": 138, "col": 49, "offset": 3925}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "start": {"line": 171, "col": 46, "offset": 4983}, "end": {"line": 171, "col": 49, "offset": 4986}}]], "message": "Syntax error at line downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml:171:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "spans": [{"file": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "start": {"line": 171, "col": 46, "offset": 4983}, "end": {"line": 171, "col": 49, "offset": 4986}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 15, "offset": 14}}, {"path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 12, "offset": 11}}, {"path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "start": {"line": 44, "col": 24, "offset": 0}, "end": {"line": 44, "col": 37, "offset": 13}}]], "message": "Syntax error at line downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html:1:\n `{% if value %}` was unexpected", "path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "spans": [{"file": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 15, "offset": 14}}, {"file": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 12, "offset": 11}}, {"file": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "start": {"line": 44, "col": 24, "offset": 0}, "end": {"line": 44, "col": 37, "offset": 13}}]}], "paths": {"scanned": ["downloaded_repos/uploadcare_pyuploadcare/.editorconfig", "downloaded_repos/uploadcare_pyuploadcare/.flake8", "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/codeql.yml", "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/publish.yml", "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "downloaded_repos/uploadcare_pyuploadcare/.gitignore", "downloaded_repos/uploadcare_pyuploadcare/.readthedocs.yaml", "downloaded_repos/uploadcare_pyuploadcare/HISTORY.md", "downloaded_repos/uploadcare_pyuploadcare/LICENSE", "downloaded_repos/uploadcare_pyuploadcare/MANIFEST.in", "downloaded_repos/uploadcare_pyuploadcare/Makefile", "downloaded_repos/uploadcare_pyuploadcare/README.md", "downloaded_repos/uploadcare_pyuploadcare/docs/Makefile", "downloaded_repos/uploadcare_pyuploadcare/docs/cli.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/conf.py", "downloaded_repos/uploadcare_pyuploadcare/docs/core_api.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/django-widget.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/feedback.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/index.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/install.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/quickstart.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/security_issues.rst", "downloaded_repos/uploadcare_pyuploadcare/docs/testing.rst", "downloaded_repos/uploadcare_pyuploadcare/lgtm.yml", "downloaded_repos/uploadcare_pyuploadcare/mypy.ini", "downloaded_repos/uploadcare_pyuploadcare/pyproject.toml", "downloaded_repos/uploadcare_pyuploadcare/pytest.ini", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/__init__.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/__init__.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/addon_entities.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/api.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/auth.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/base.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/client.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/entities.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/metadata.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/responses.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/api/utils.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/client.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/conf.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/__init__.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/client.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/conf.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/forms.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/models.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/static/uploadcare/uc-file-uploader-inline.min.css", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/static/uploadcare/uc-file-uploader-minimal.min.css", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/static/uploadcare/uc-file-uploader-regular.min.css", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/subclassing.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/exceptions.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/helpers.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/resources/__init__.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/resources/base.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/resources/file.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/resources/file_group.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/resources/file_list.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/resources/group_list.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/resources/utils.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/secure_url.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/transformations/__init__.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/transformations/base.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/transformations/document.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/transformations/image.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/transformations/video.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/__init__.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/__init__.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/convert_document.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/convert_video.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/create_group.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/create_webhook.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/delete_files.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/delete_webhook.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/get_file.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/get_project.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/helpers.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/list_files.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/list_groups.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/list_webhooks.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/store_files.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/sync.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/update_webhook.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/upload.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/commands/upload_from_url.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/main.py", "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/ucare_cli/settings.py", "downloaded_repos/uploadcare_pyuploadcare/uploadcare.ini.template"], "skipped": [{"path": "downloaded_repos/uploadcare_pyuploadcare/.github/workflows/test.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/static/uploadcare/file-uploader.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/static/uploadcare/uploadcare.full.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/static/uploadcare/uploadcare.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/pyuploadcare/dj/templates/uploadcare/forms/widgets/file.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/dj/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/dj/test_conf.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/dj/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/dj/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_aws_rekognition_detect_labels_execute.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_aws_rekognition_detect_labels_status.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_aws_rekognition_detect_moderation_labels_execute.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_aws_rekognition_detect_moderation_labels_status.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_clam_av_execute.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_clam_av_status.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_convert_document.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_convert_document_get_group_info.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_convert_document_with_save_in_group.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_convert_video.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_create_key_and_delete.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_create_webhook.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_delete_webhook.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_get_all_metadata.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_get_empty_metadata.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_get_metadata_value_for_specific_key.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_get_project_info.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_list_webhooks.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_multipart_upload.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_remove_bg_execute.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_remove_bg_status.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_update_webhook.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_upload_file.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_upload_file_secure.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/cassettes/test_upload_file_secure_with_metadata.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_addons.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_document_convert_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_metadata_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_project_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_throttling_header.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_upload_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_upload_api_with_metadata.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_video_convert_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/api/test_webhooks_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_acl_token_bare_uuid.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_acl_token_basic_url.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_acl_token_group_url.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_acl_token_uuid_with_transformations.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_acl_token_wildcard_uuid.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_acl_token_wildcard_uuid_with_transformations.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_url_token_basic_url.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_url_token_group_url.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/cassettes/test_url_token_uuid_with_transformations.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_client_create_webhook.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_client_get_project_info.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_create_file_group.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_create_local_copy_with_transformation.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_create_webhook_signed_secret.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_delete_file_group_with_files.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_delete_webhook.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_convert_document.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_convert_document_page.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_convert_document_with_save_in_group.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_convert_video.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_create_local_copy.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_delete.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_detect_faces.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_get_converted_document_group.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_info_has_new_structure.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_list_iterate.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload_big_file.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload_big_file_callback.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload_big_file_text_mode.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload_by_url.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload_by_url_with_metadata.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload_multiple.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_file_upload_with_metadata.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_get_file_group.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_get_group_with_deleted_files.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_list_file_groups.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_list_files.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_list_webhooks.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_retrieve_fileinfo_with_appdata.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_retrieve_fileinfo_with_metadata.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/cassettes/test_update_webhook.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/test_project.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/test_resources.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/resources/test_webhooks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/settings/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/settings/test_subdomains.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/test_client_arguments.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/test_secure_url.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/test_webhook_exceptions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/transformations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/transformations/test_document.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/transformations/test_image.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/transformations/test_video.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_convert_document.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_convert_video.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_create_webhooks.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_delete_one_file.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_delete_several_files.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_delete_webhooks.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_get_file_by_cdn_url.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_get_file_by_uuid.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_get_project.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_group_create_by_uuid_and_cdn_url.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_groups_list_empty_result.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_groups_list_with_params.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_groups_list_without_params.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_list_webhooks.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_store_one_file.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_store_several_files.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/cassettes/test_update_webhooks.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/helpers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_args.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_config_file.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_convert_document.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_convert_video.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_create_webhook.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_delete_files.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_delete_webhook.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_file_group.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_get_file.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_get_project.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_list_webhooks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_store_files.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/functional/ucare_cli/test_update_webhook.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/integration/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/integration/assets/img.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/integration/test_api_resources.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/integration/test_helpers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/integration/ucare_cli/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/integration/ucare_cli/test_sync_files.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/integration/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/gallery/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/gallery/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/gallery/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/gallery/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/gallery/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uploadcare_pyuploadcare/tests/test_project/urls.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8964040279388428, "profiling_times": {"config_time": 5.9809699058532715, "core_time": 3.5834317207336426, "ignores_time": 0.001981496810913086, "total_time": 9.567659854888916}, "parsing_time": {"total_time": 0.8063814640045166, "per_file_time": {"mean": 0.013006152645234139, "std_dev": 0.00044270081244819474}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 6.3842527866363525, "per_file_time": {"mean": 0.027051918587442162, "std_dev": 0.007005680794064682}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.1561086177825928, "per_file_and_rule_time": {"mean": 0.0037760220976928065, "std_dev": 6.0187529241651566e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.7981810569763184, "per_def_and_rule_time": {"mean": 0.0003941634849265771, "std_dev": 7.365550047784202e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}