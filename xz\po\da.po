# Danish translation xz.
# This file is put in the public domain.
# <PERSON> <<EMAIL>>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: xz 5.2.4\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2019-03-04 23:08+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Danish <<EMAIL>>\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Ugyldigt parameter til --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: For mange argumenter til --block-list"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr ""

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 kan kun bruges som det sidste element i --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Ukendt filformattype"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Typen for integritetkontrol er ikke understøttet"

#: src/xz/args.c
#, fuzzy
#| msgid "Only one file can be specified with `--files' or `--files0'."
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Kun en fil kan angives med »--files« eller »--files0«."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, fuzzy, c-format
#| msgid "%s: "
msgid "%s: %s"
msgstr "%s: "

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "Miljøvariablen %s indeholder for mange argumenter"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "Komprimeringsunderstøttelse blev deaktiveret på byggetidspunktet"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "Dekomprimeringsunderstøttelse blev deaktiveret på byggetidspunktet"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr ""

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr ""

#: src/xz/args.c
#, fuzzy
#| msgid "%s: With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "%s: med --format=raw, --suffix=.SUF er krævet med mindre der skrives til standardud"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "Maksimalt antal filtre er fire"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr ""

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "Begræsningen for brug af hukommelse er for lav for den givne filteropsætning."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr ""

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Det frarådes at bruge en forhåndskonfiguration i rå tilstand (raw mode)."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "De præcise indstillinger for forhåndskonfigurationerne kan variere mellem programversioner."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "Formatet .lzma understøtter kun LZMA1-filteret"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA1 kan ikke bruges med .xz-formatet"

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "The filter chain is incompatible with --flush-timeout"
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "Filterkæden er ikke kompatibel med --flush-timeout"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Skifter til enkelt trådet tilstand på grund af --flush-timeout"

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Unsupported options"
msgid "Unsupported options in filter chain %u"
msgstr "Tilvalg er ikke understøttede"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Bruger op til %<PRIu32> tråde."

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Filterkæde eller filterindstillinger er ikke understøttet"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "Dekomprimering vil kræve %s MiB hukommelse."

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Adjusted the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Justerede antallet af tråde fra %s til %s for ikke at overskride begræsningen på brug af hukommelse på %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr ""

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Adjusted the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Justerede antallet af tråde fra %s til %s for ikke at overskride begræsningen på brug af hukommelse på %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Justerede LZMA%c-ordbogsstørrelsen fra %s MiB til %s MiB for ikke at overskride begrænsningen på brug af hukommelse på %s MiB"

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Justerede LZMA%c-ordbogsstørrelsen fra %s MiB til %s MiB for ikke at overskride begrænsningen på brug af hukommelse på %s MiB"

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Error creating a pipe: %s"
msgid "Error changing to filter chain %u: %s"
msgstr "Det opstod en fejl under oprettelse af en datakanal: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Det opstod en fejl under oprettelse af en datakanal: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: poll() mislykkedes: %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Filen er vist blevet flyttet, sletter ikke"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Kan ikke fjerne: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: Kan ikke angive filejeren: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: Kan ikke angive filgruppen: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: Kan ikke angive filtilladelser: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Lukning af filen fejlede: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: Lukning af filen fejlede: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Der opstod en fejl under indhentelse af filstatusflag fra standardind: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Er en symbolsk henvisning, udelader"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Er en mappe, udelader"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Er ikke en normal fil, udelader"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: Filen har setuid- eller setgid-bitsæt, udelader"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: Fil har klæbende bitsæt, udelader"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: Inddatafil har mere end en hård henvisning, udelader"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Tomt filnavn, udelader"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Der opstod en fejl under gendannelse af statusflagene til standardind: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Der opstod en fejl under indhentelse af filstatusflag fra standardud: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Lukning af filen fejlede: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Not a regular file, skipping"
msgid "%s: Destination is not a regular file"
msgstr "%s: Er ikke en normal fil, udelader"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Der opstod en fejl under gendannelse af flaget O_APPEND til standardud: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Lukning af filen fejlede: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Søgning fejlede under forsøg på at oprette en tynd fil: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Læsefejl: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Der opstod en fejl under søgning efter filen: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Uventet filafslutning"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Skrivefejl: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Deaktiveret"

#: src/xz/hardware.c
#, fuzzy
#| msgid "Total amount of physical memory (RAM): "
msgid "Amount of physical memory (RAM):"
msgstr "Samlet mængde fysisk hukommelse (RAM):           "

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr ""

#: src/xz/hardware.c
msgid "Compression:"
msgstr ""

#: src/xz/hardware.c
msgid "Decompression:"
msgstr ""

#: src/xz/hardware.c
#, fuzzy
#| msgid "Memory usage limit for decompression:  "
msgid "Multi-threaded decompression:"
msgstr "Grænse for hukommelsesforbug til dekomprimering: "

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr ""

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr ""

#: src/xz/hardware.c
#, fuzzy
#| msgid "Memory usage limit reached"
msgid "Memory usage limits:"
msgstr "Begrænsning på brug af hukommelse er nået"

#: src/xz/list.c
msgid "Streams:"
msgstr ""

#: src/xz/list.c
msgid "Blocks:"
msgstr ""

#: src/xz/list.c
#, fuzzy
#| msgid "  Compressed size:    %s\n"
msgid "Compressed size:"
msgstr "  Komprimeret str.:   %s\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Uncompressed size:  %s\n"
msgid "Uncompressed size:"
msgstr "  Ukomprimeret str.:  %s\n"

#: src/xz/list.c
msgid "Ratio:"
msgstr ""

#: src/xz/list.c
msgid "Check:"
msgstr ""

#: src/xz/list.c
#, fuzzy
#| msgid "  Stream padding:     %s\n"
msgid "Stream Padding:"
msgstr "  Strømfyld:          %s\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Memory needed:      %s MiB\n"
msgid "Memory needed:"
msgstr "  Hukommelse krævet:  %s MiB\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Sizes in headers:   %s\n"
msgid "Sizes in headers:"
msgstr "  Størrelser i teksthoveder: %s\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Number of files:    %s\n"
msgid "Number of files:"
msgstr "  Antal filer:        %s\n"

#: src/xz/list.c
msgid "Stream"
msgstr ""

#: src/xz/list.c
msgid "Block"
msgstr ""

#: src/xz/list.c
msgid "Blocks"
msgstr ""

#: src/xz/list.c
msgid "CompOffset"
msgstr ""

#: src/xz/list.c
msgid "UncompOffset"
msgstr ""

#: src/xz/list.c
msgid "CompSize"
msgstr ""

#: src/xz/list.c
msgid "UncompSize"
msgstr ""

#: src/xz/list.c
#, fuzzy
#| msgid "Totals:"
msgid "TotalSize"
msgstr "I alt:"

#: src/xz/list.c
msgid "Ratio"
msgstr ""

#: src/xz/list.c
msgid "Check"
msgstr ""

#: src/xz/list.c
msgid "CheckVal"
msgstr ""

#: src/xz/list.c
msgid "Padding"
msgstr ""

#: src/xz/list.c
msgid "Header"
msgstr ""

#: src/xz/list.c
msgid "Flags"
msgstr ""

#: src/xz/list.c
msgid "MemUsage"
msgstr ""

#: src/xz/list.c
msgid "Filters"
msgstr ""

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Ingen"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "Ukendt-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "Ukendt-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "Ukendt-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "Ukendt-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "Ukendt-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "Ukendt-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "Ukendt-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "Ukendt-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "Ukendt-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "Ukendt-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "Ukendt-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "Ukendt-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: Filen er tom"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: For lille til at være en gyldig .xz-fil"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr ""

#: src/xz/list.c
msgid "Yes"
msgstr "Ja"

#: src/xz/list.c
msgid "No"
msgstr "Nej"

#: src/xz/list.c
#, fuzzy
#| msgid "  Minimum XZ Utils version: %s\n"
msgid "Minimum XZ Utils version:"
msgstr "  Minimum for XZ Utils-version: %s\n"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s fil\n"
msgstr[1] "%s filer\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "I alt:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr ""

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr ""

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list understøtter ikke læsning fra standardind"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Der opstod en fejl under forsøg på læsning af filnavne: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Uventet afslutning på inddata under forsøg på læsning af filnavne"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr ""

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Komprimering og dekomprimering med --robot er endnu ikke understøttet."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr ""

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Intern fejl (fejl)"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "Kan ikke etbalere signalhåndteringer"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Ingen integritetkontrol; verificerer ikke filintegritet"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr ""

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Begrænsning på brug af hukommelse er nået"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "Filformatet blev ikke genkendt"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Tilvalg er ikke understøttede"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Komprimerede data er ødelagte"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Uventet afslutning på inddata"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "%s MiB hukommelse er krævet. Begrænseren er deaktiveret."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "%s MiB hukommelse er krævet. Begrænsningen er %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Filterkæde: %s\n"

#: src/xz/message.c
#, fuzzy, c-format
#| msgid "Try `%s --help' for more information."
msgid "Try '%s --help' for more information."
msgstr "Prøv »%s --help« for yderligere information."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr ""

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Compress or decompress FILEs in the .xz format."
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Mandatory arguments to long options are mandatory for short options too.\n"
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr ""
"Obligatoriske argumenter til lange tilvalg er også obligatoriske for korte\n"
"tilvalg.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "Operation mode:"
msgstr " Operationstilstand:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Memory usage limit for decompression:  "
msgid "force compression"
msgstr "Grænse for hukommelsesforbug til dekomprimering: "

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Memory usage limit for decompression:  "
msgid "force decompression"
msgstr "Grænse for hukommelsesforbug til dekomprimering: "

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Operation modifiers:\n"
msgid "Operation modifiers:"
msgstr ""
"\n"
"Operationsændrere:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Writing to standard output failed"
msgid "write to standard output and don't delete input files"
msgstr "Skrivning til standardud mislykkedes"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr ""

#: src/xz/message.c
msgid ".SUF"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr ""

#: src/xz/message.c
msgid "FILE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Basic file format and compression options:"
msgstr ""

#: src/xz/message.c
msgid "FORMAT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr ""

#: src/xz/message.c
msgid "NAME"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't verify the integrity check when decompressing"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr ""

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr ""

#: src/xz/message.c
msgid "SIZE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr ""

#: src/xz/message.c
msgid "BLOCKS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr ""

#: src/xz/message.c
msgid "LIMIT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr ""

#: src/xz/message.c
msgid "FILTERS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display more information about the liblzma filter string syntax and exit"
msgstr ""

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr ""

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr ""

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr ""

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of position bits"
msgstr ""

#: src/xz/message.c
msgid "MODE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "compression mode"
msgstr " Operationstilstand:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Other options:\n"
msgid "Other options:"
msgstr ""
"\n"
"Andre tilvalg:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "make warnings not affect the exit status"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --robot         use machine-parsable messages (useful for scripts)"
msgid "use machine-parsable messages (useful for scripts)"
msgstr ""
"      --robot         brug beskeder der kan fortolkes maskinelt (nyttigt\n"
"                      for skripter)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -V, --version       display the version number and exit"
msgid "display the version number and exit"
msgstr "  -V, --version       vis versionsnummer og afslut"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "\n"
#| "With no FILE, or when FILE is -, read standard input.\n"
msgid "With no FILE, or when FILE is -, read standard input."
msgstr ""
"\n"
"Med ingen FIL, eller når FIL er -, læs standardind.\n"

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "Report bugs to <%s> (in English or Finnish).\n"
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr ""
"Rapporter fejl til <%s> (på engelsk eller finsk).\n"
"Rapporter oversættelsesfejl til <<EMAIL>>.\n"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "%s home page: <%s>\n"
msgid "%s home page: <%s>"
msgstr "%s hjemmeside: <%s>\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "DETTE ER EN UDVIKLINGSVERSION - BRUG IKKE I PRODUKTION."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, c-format
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "The supported filters and their options are:"
msgstr "Filterkæde eller filterindstillinger er ikke understøttet"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Options must be `name=value' pairs separated with commas"
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "%s: Tilvalg skal være »navne=værdi«-par adskilt med kommaer"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Ugyldigt tilvalgsnavn"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option value"
msgid "Invalid option value"
msgstr "%s: Ugyldigt tilvalgsværdi"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "LZMA1/LZMA2-forhåndskonfiguration er ikke understøttet: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "Summen af lc og lp må ikke være højere end 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: Filnavn har ukendt endelse, udelader"

#: src/xz/suffix.c
#, fuzzy, c-format
#| msgid "%s: File already has `%s' suffix, skipping"
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: Filen har allrede endelsen »%s«, udelader."

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Ugyldig filnavnendelse"

#: src/xz/util.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Value is not a non-negative decimal integer"
msgid "Value is not a non-negative decimal integer"
msgstr "%s: Værdi er ikke et positivt decimalheltal"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Ugyldig multiplikatorendelse"

#: src/xz/util.c
#, fuzzy
#| msgid "Valid suffixes are `KiB' (2^10), `MiB' (2^20), and `GiB' (2^30)."
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Gyldige endelser er »KiB« (2^10), »MiB« (2^20) og »GiB« (2^30)."

#: src/xz/util.c
#, fuzzy, c-format
#| msgid "Value of the option `%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "Værdien for tilvalget »%s« skal være i intervallet [%<PRIu64>, %<PRIu64>]"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "Komprimerede data kan ikke læses fra en terminal"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "Komprimerede data kan ikke skrives til en terminal"

#: src/lzmainfo/lzmainfo.c
#, c-format
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
msgid "Show information stored in the .lzma file header."
msgstr ""

#: src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid "%s: Too small to be a valid .xz file"
msgid "File is too small to be a .lzma file"
msgstr "%s: For lille til at være en gyldig .xz-fil"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr ""

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Skrivning til standardud mislykkedes"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Ukendt fejl"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported options"
msgid "Unsupported preset"
msgstr "Tilvalg er ikke understøttede"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "Unsupported flag in the preset"
msgstr "Filterkæde eller filterindstillinger er ikke understøttet"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option name"
msgid "Unknown option name"
msgstr "%s: Ugyldigt tilvalgsnavn"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr ""

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid multiplier suffix"
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "%s: Ugyldig multiplikatorendelse"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Unknown file format type"
msgid "Unknown filter name"
msgstr "%s: Ukendt filformattype"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "LZMA1 cannot be used with the .xz format"
msgid "This filter cannot be used in the .xz format"
msgstr "LZMA1 kan ikke bruges med .xz-formatet"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr ""

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Maximum number of filters is four"
msgid "The maximum number of filters is four"
msgstr "Maksimalt antal filtre er fire"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr ""

#~ msgid ""
#~ "  -h, --help          display the short help (lists only the basic options)\n"
#~ "  -H, --long-help     display this long help and exit"
#~ msgstr ""
#~ "  -h, --help          vis den korte hjælpetekst (viser kun grundlæggende\n"
#~ "                      tilvalg)\n"
#~ "  -H, --long-help     vis den lange hjælpetekst og afslut"

#~ msgid ""
#~ "  -h, --help          display this short help and exit\n"
#~ "  -H, --long-help     display the long help (lists also the advanced options)"
#~ msgstr ""
#~ "  -h, --help          vis den korte hjælpetekst og afslut\n"
#~ "  -H, --long-help     vis den lange hjælpetekst (viser også de avancerede\n"
#~ "                      tilvalg)"

#~ msgid "Sandbox is disabled due to incompatible command line arguments"
#~ msgstr "Sandkassen er deaktiveret på grund af inkompatible kommandolinjeargumenter"

#~ msgid "Sandbox was successfully enabled"
#~ msgstr "Sandkassen blev aktiveret"

#~ msgid "Failed to enable the sandbox"
#~ msgstr "Kunne ikke aktivere sandkassen"

#~ msgid "Memory usage limit for compression:    "
#~ msgstr "Grænse for hukommelsesforbrug til komprimering:  "

#, c-format
#~ msgid "  Streams:            %s\n"
#~ msgstr "  Strømme:            %s\n"

#, c-format
#~ msgid "  Blocks:             %s\n"
#~ msgstr "  Blokke:             %s\n"

#, c-format
#~ msgid "  Ratio:              %s\n"
#~ msgstr "  Pakkeforhold:       %s\n"

#, c-format
#~ msgid "  Check:              %s\n"
#~ msgstr "  Kontrol:            %s\n"

#~ msgid ""
#~ "  Streams:\n"
#~ "    Stream    Blocks      CompOffset    UncompOffset        CompSize      UncompSize  Ratio  Check      Padding"
#~ msgstr ""
#~ "  Strømme:\n"
#~ "     Strøm    Blokke      KompForsk.    DekompForsk.        KompStr.      DekompStr.  Forh.  Kontrol       Fyld"

#, c-format
#~ msgid ""
#~ "  Blocks:\n"
#~ "    Stream     Block      CompOffset    UncompOffset       TotalSize      UncompSize  Ratio  Check"
#~ msgstr ""
#~ "  Blokke:\n"
#~ "     Strøm     Blok       KompForsk.    DekompForsk.        Ialtstr.      DekompStr.  Forh.  Kontrol"

#, c-format
#~ msgid "      CheckVal %*s Header  Flags        CompSize    MemUsage  Filters"
#~ msgstr "    KontrolVær %*sTeksth Flag         Kompstr.    HukForb.  Filtre"

#, c-format
#~ msgid "The selected match finder requires at least nice=%<PRIu32>"
#~ msgstr "Den valgte matchfinder kræver mindst nice=%<PRIu32>"
