{"version": "1.130.0", "results": [{"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/src/Admin/JQAdm/Cms/Standard.php", "start": {"line": 182, "col": 11, "offset": 4389}, "end": {"line": 182, "col": 61, "offset": 4439}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/src/Admin/JQAdm/Cms/Standard.php", "start": {"line": 248, "col": 11, "offset": 5794}, "end": {"line": 248, "col": 89, "offset": 5872}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js:\n ", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js:\n ", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js:\n ", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js:\n ", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js:\n ", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js:\n ", "path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js"}], "paths": {"scanned": ["downloaded_repos/aimeos_ai-cms-grapesjs/.circleci/config.yml", "downloaded_repos/aimeos_ai-cms-grapesjs/.gitignore", "downloaded_repos/aimeos_ai-cms-grapesjs/.tx/config", "downloaded_repos/aimeos_ai-cms-grapesjs/LICENSE", "downloaded_repos/aimeos_ai-cms-grapesjs/README.md", "downloaded_repos/aimeos_ai-cms-grapesjs/composer.json", "downloaded_repos/aimeos_ai-cms-grapesjs/config/admin.php", "downloaded_repos/aimeos_ai-cms-grapesjs/config/client.php", "downloaded_repos/aimeos_ai-cms-grapesjs/config/controller.php", "downloaded_repos/aimeos_ai-cms-grapesjs/config/mshop.php", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ar", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ar.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/bg", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/bg.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/cs", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/cs.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/da", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/da.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/de", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/de.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/en", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/en.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/es", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/es.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/et", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/et.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/fa", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/fa.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/fr", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/fr.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/hr", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/hr.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/hu", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/hu.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/id", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/id.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/it", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/it.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ja", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ja.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ko", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ko.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/lv", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/lv.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/nl", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/nl.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/pl", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/pl.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/pt", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/pt.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/pt_BR", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/pt_BR.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ro", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ro.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ru", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/ru.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/sl", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/sl.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/source.pot", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/tr", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/tr.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/uk", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/uk.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/zh", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/zh.po", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/zh_CN", "downloaded_repos/aimeos_ai-cms-grapesjs/i18n/zh_CN.po", "downloaded_repos/aimeos_ai-cms-grapesjs/manifest.jsb2", "downloaded_repos/aimeos_ai-cms-grapesjs/manifest.php", "downloaded_repos/aimeos_ai-cms-grapesjs/phing.xml", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/Cms.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/CmsRemoveIndexes.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/default/DemoAddCmsData.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/default/MShopAddTypeDataCms.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/default/data/demo-cms.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/default/data/type.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/default/schema/cms.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/unittest/CmsAddTestData.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/unittest/CmsAddTypeTestData.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/unittest/data/cms.php", "downloaded_repos/aimeos_ai-cms-grapesjs/setup/unittest/data/type.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Admin/JQAdm/Cms/Content/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Admin/JQAdm/Cms/Media/Property/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Admin/JQAdm/Cms/Media/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Admin/JQAdm/Cms/Seo/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Admin/JQAdm/Cms/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Client/Html/Cms/Page/Cataloglist/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Client/Html/Cms/Page/Contact/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Client/Html/Cms/Page/Decorator/Recaptcha.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Client/Html/Cms/Page/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Client/JsonApi/Cms/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Controller/Frontend/Cms/Decorator/Base.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Controller/Frontend/Cms/Exception.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Controller/Frontend/Cms/Iface.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/Controller/Frontend/Cms/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Exception.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Item/Iface.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Item/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Manager/Iface.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Manager/Lists/Iface.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Manager/Lists/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Manager/Lists/Type/Iface.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Manager/Lists/Type/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/src/MShop/Cms/Manager/Standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/admin/jqadm/cms/item-content.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/admin/jqadm/cms/item-media-property.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/admin/jqadm/cms/item-media.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/admin/jqadm/cms/item-seo.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/admin/jqadm/cms/item.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/admin/jqadm/cms/list.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/client/html/cms/page/body.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/client/html/cms/page/cataloglist/list.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/client/html/cms/page/header.php", "downloaded_repos/aimeos_ai-cms-grapesjs/templates/client/jsonapi/cms/standard.php", "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/custom.css", "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/custom.js", "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js", "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.css", "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js", "downloaded_repos/aimeos_ai-cms-grapesjs/themes/client/html/default/cms-page.css", "downloaded_repos/aimeos_ai-cms-grapesjs/themes/client/html/default/cms-page.js"], "skipped": [{"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Admin/JQAdm/Cms/Content/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Admin/JQAdm/Cms/Media/Property/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Admin/JQAdm/Cms/Media/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Admin/JQAdm/Cms/Seo/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Admin/JQAdm/Cms/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Client/Html/Cms/Page/Cataloglist/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Client/Html/Cms/Page/Contact/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Client/Html/Cms/Page/Decorator/RecaptchaTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Client/Html/Cms/Page/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Client/JsonApi/Cms/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Controller/Frontend/Cms/Decorator/BaseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/Controller/Frontend/Cms/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/MShop/Cms/Item/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/MShop/Cms/Manager/Lists/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/MShop/Cms/Manager/Lists/Type/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/MShop/Cms/Manager/StandardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/TestHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/phpunit-coverage.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/tests/phpunit.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6832799911499023, "profiling_times": {"config_time": 7.106277942657471, "core_time": 24.49536633491516, "ignores_time": 0.0025069713592529297, "total_time": 31.60507822036743}, "parsing_time": {"total_time": 0.5894482135772705, "per_file_time": {"mean": 0.011121664407118311, "std_dev": 0.00033753950238014517}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 29.873166799545288, "per_file_time": {"mean": 0.10058305319712225, "std_dev": 1.716489329863434}, "very_slow_stats": {"time_ratio": 0.9183500696420508, "count_ratio": 0.006734006734006734}, "very_slow_files": [{"fpath": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs.js", "ftime": 5.467376947402954}, {"fpath": "downloaded_repos/aimeos_ai-cms-grapesjs/themes/admin/jqadm/grapesjs-ext.js", "ftime": 21.96664786338806}]}, "matching_time": {"total_time": 0.3803558349609375, "per_file_and_rule_time": {"mean": 0.003143436652569732, "std_dev": 5.325368271385679e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.1374669075012207, "per_def_and_rule_time": {"mean": 0.0007013617729654118, "std_dev": 7.940914122034629e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}