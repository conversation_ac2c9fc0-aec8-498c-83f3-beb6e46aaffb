#!/bin/bash

# Docker Container Vulnerability Scanner - Quick Start Script
# ===========================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
MAX_REPOS=50
PAGES=3
CONCURRENT=1
BUILD_ONLY=false
TEST_MODE=false
USE_COMPOSE=false
SKIP_SEMGREP=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Docker Container Vulnerability Scanner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -r, --max-repos NUM     Maximum repositories to analyze (default: 50)"
    echo "  -p, --pages NUM         Search pages per query (default: 3)"
    echo "  -c, --concurrent NUM    Max concurrent scans (default: 1)"
    echo "  -t, --test              Test mode (20 repos, 2 pages)"
    echo "  -s, --skip-semgrep      Skip Semgrep scanning, only pull data"
    echo "  -b, --build-only        Only build Docker image"
    echo "  -d, --docker-compose    Use docker-compose instead"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  GITHUB_TOKEN            Required: GitHub API token"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Full scan (50 repos)"
    echo "  $0 -t                                # Test scan (20 repos)"
    echo "  $0 -r 100 -p 5                      # Comprehensive scan"
    echo "  $0 -d                                # Use docker-compose"
    echo "  $0 -b                                # Build image only"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--max-repos)
            MAX_REPOS="$2"
            shift 2
            ;;
        -p|--pages)
            PAGES="$2"
            shift 2
            ;;
        -c|--concurrent)
            CONCURRENT="$2"
            shift 2
            ;;
        -t|--test)
            TEST_MODE=true
            MAX_REPOS=20
            PAGES=2
            shift
            ;;
        -s|--skip-semgrep)
            SKIP_SEMGREP=true
            shift
            ;;
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -d|--docker-compose)
            USE_COMPOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Banner
echo "🐳 DOCKER CONTAINER VULNERABILITY SCANNER"
echo "=========================================="
echo "All-in-One Container with Semgrep Integration"
echo ""

# Check prerequisites
print_status "Checking prerequisites..."

# Check if GitHub token is set
if [ -z "$GITHUB_TOKEN" ]; then
    print_error "GITHUB_TOKEN environment variable is not set"
    echo "Please set it with: export GITHUB_TOKEN='your_token_here'"
    exit 1
fi
print_success "GitHub token is set"

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed"
    echo "Please install Docker from https://docs.docker.com/get-docker/"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    echo "Please start Docker daemon"
    exit 1
fi
print_success "Docker is available"

# Create output directory
mkdir -p docker_output
print_success "Output directory created"

# Run with docker-compose if requested
if [ "$USE_COMPOSE" = true ]; then
    print_status "Using docker-compose..."
    
    # Create .env file
    echo "GITHUB_TOKEN=$GITHUB_TOKEN" > .env
    
    if [ "$TEST_MODE" = true ]; then
        print_status "Running test mode with docker-compose..."
        docker-compose --profile test up --build vulnerability-scanner-test
    else
        print_status "Running full scan with docker-compose..."
        docker-compose up --build vulnerability-scanner
    fi
    
else
    # Use Python script for more control
    print_status "Using Python Docker runner..."
    
    if [ "$BUILD_ONLY" = true ]; then
        python3 run_docker_container_scanner.py --build-only
    elif [ "$TEST_MODE" = true ]; then
        python3 run_docker_container_scanner.py \
            --max-repos 20 \
            --pages 2 \
            --concurrent 1
    else
        python3 run_docker_container_scanner.py \
            --max-repos "$MAX_REPOS" \
            --pages "$PAGES" \
            --concurrent "$CONCURRENT" \
            $([ "$SKIP_SEMGREP" = true ] && echo "--skip-semgrep")
    fi
fi

# Show results if not build-only
if [ "$BUILD_ONLY" = false ]; then
    echo ""
    print_status "Scan completed! Results:"
    
    if [ -f "docker_output/unified_vulnerability_results.csv" ]; then
        RESULT_COUNT=$(tail -n +2 "docker_output/unified_vulnerability_results.csv" | wc -l)
        print_success "Top repositories found: $RESULT_COUNT"
    fi
    
    if [ -d "docker_output/semgrep_results" ]; then
        SEMGREP_COUNT=$(find docker_output/semgrep_results -name "*.json" 2>/dev/null | wc -l)
        print_success "Semgrep analysis files: $SEMGREP_COUNT"
    fi
    
    echo ""
    echo "📁 Output files:"
    ls -la docker_output/ 2>/dev/null || true
    
    echo ""
    print_success "Scan complete! Check the docker_output/ directory for results."
    echo ""
    echo "🎯 Key files:"
    echo "  📄 docker_output/unified_vulnerability_results.csv - Top 10 repositories"
    echo "  🔍 docker_output/semgrep_results/ - Detailed Semgrep analysis"
    echo "  📝 docker_output/logs/ - Scan logs"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Review the CSV file for top vulnerable repositories"
    echo "2. Check Semgrep results for detailed vulnerability analysis"
    echo "3. Follow responsible disclosure practices"
fi
