{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-wp-cli.php", "start": {"line": 711, "col": 11, "offset": 23696}, "end": {"line": 711, "col": 31, "offset": 23716}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/functions.php", "start": {"line": 369, "col": 30, "offset": 14244}, "end": {"line": 369, "col": 76, "offset": 14290}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/class-letsencrypt-handler.php", "start": {"line": 1561, "col": 6, "offset": 50223}, "end": {"line": 1561, "col": 21, "offset": 50238}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/cpanel/cpanel.php", "start": {"line": 227, "col": 9, "offset": 7638}, "end": {"line": 227, "col": 56, "offset": 7685}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/cpanel/cpanel.php", "start": {"line": 277, "col": 3, "offset": 9037}, "end": {"line": 277, "col": 48, "offset": 9082}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= 0)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/directadmin/httpsocket.php", "start": {"line": 205, "col": 4, "offset": 4435}, "end": {"line": 205, "col": 51, "offset": 4482}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lib/admin/class-encryption.php", "start": {"line": 117, "col": 4, "offset": 2884}, "end": {"line": 117, "col": 86, "offset": 2966}, "extra": {"message": "The function `openssl_decrypt` returns either a string of the decrypted data on success or `false` on failure. If the failure case is not handled, this could lead to undefined behavior in your application. Please handle the case where `openssl_decrypt` returns `false`.", "metadata": {"references": ["https://www.php.net/manual/en/function.openssl-decrypt.php"], "cwe": ["CWE-252: Unchecked Return Value"], "owasp": ["A02:2021 - Cryptographic Failures"], "technology": ["php", "openssl"], "category": "security", "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "shortlink": "https://sg.run/kzn7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lib/admin/class-encryption.php", "start": {"line": 125, "col": 26, "offset": 3239}, "end": {"line": 125, "col": 56, "offset": 3269}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/components/Modal/RssslModal.js", "start": {"line": 44, "col": 53, "offset": 1963}, "end": {"line": 44, "col": 61, "offset": 1971}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/firewall-manager.php", "start": {"line": 242, "col": 4, "offset": 6652}, "end": {"line": 242, "col": 25, "offset": 6673}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/tests.php", "start": {"line": 104, "col": 5, "offset": 2948}, "end": {"line": 104, "col": 54, "offset": 2997}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/tests.php", "start": {"line": 352, "col": 4, "offset": 9481}, "end": {"line": 352, "col": 22, "offset": 9499}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/rename-admin-user.php", "start": {"line": 143, "col": 22, "offset": 6679}, "end": {"line": 143, "col": 49, "offset": 6706}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/BaseAuth.js", "start": {"line": 66, "col": 13, "offset": 2198}, "end": {"line": 66, "col": 51, "offset": 2236}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/onboarding.js", "start": {"line": 176, "col": 13, "offset": 7504}, "end": {"line": 176, "col": 52, "offset": 7543}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/profile.js", "start": {"line": 87, "col": 41, "offset": 4748}, "end": {"line": 87, "col": 100, "offset": 4807}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/profile.js", "start": {"line": 135, "col": 29, "offset": 7175}, "end": {"line": 135, "col": 88, "offset": 7234}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities/FileStorage.php", "start": {"line": 112, "col": 17, "offset": 2966}, "end": {"line": 112, "col": 30, "offset": 2979}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities/class-rsssl-file-storage.php", "start": {"line": 120, "col": 5, "offset": 3023}, "end": {"line": 120, "col": 20, "offset": 3038}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities/class-rsssl-file-storage.php", "start": {"line": 150, "col": 12, "offset": 3811}, "end": {"line": 150, "col": 29, "offset": 3828}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities/class-rsssl-file-storage.php", "start": {"line": 178, "col": 5, "offset": 4462}, "end": {"line": 178, "col": 20, "offset": 4477}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Field.js", "start": {"line": 111, "col": 27, "offset": 4483}, "end": {"line": 111, "col": 57, "offset": 4513}, "extra": {"message": "RegExp() called with a `props` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/hoverTooltip.js", "start": {"line": 30, "col": 13, "offset": 911}, "end": {"line": 30, "col": 45, "offset": 943}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/hoverTooltip_bu.js", "start": {"line": 54, "col": 9, "offset": 1503}, "end": {"line": 54, "col": 41, "offset": 1535}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/webpack.feature.config.js", "start": {"line": 11, "col": 48, "offset": 460}, "end": {"line": 11, "col": 99, "offset": 511}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/webpack.feature.config.js", "start": {"line": 12, "col": 50, "offset": 564}, "end": {"line": 12, "col": 108, "offset": 622}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade.php", "start": {"line": 151, "col": 4, "offset": 6632}, "end": {"line": 151, "col": 81, "offset": 6709}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.js", "start": {"line": 14, "col": 1, "offset": 423}, "end": {"line": 14, "col": 78, "offset": 500}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.js", "start": {"line": 93, "col": 4, "offset": 2951}, "end": {"line": 93, "col": 58, "offset": 3005}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.js", "start": {"line": 97, "col": 5, "offset": 3152}, "end": {"line": 97, "col": 75, "offset": 3222}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.js", "start": {"line": 112, "col": 4, "offset": 3802}, "end": {"line": 112, "col": 56, "offset": 3854}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.php", "start": {"line": 417, "col": 21, "offset": 20232}, "end": {"line": 417, "col": 76, "offset": 20287}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/package-lock.json:5774:\n `<<` was unexpected", "path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/package-lock.json"}], "paths": {"scanned": ["downloaded_repos/Really-Simple-Plugins_really-simple-ssl/.DS_Store", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/.editorconfig", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/.gitattributes", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/.gitignore", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/.wp-env.json", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/.DS_Store", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/.DS_Store", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/base.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/layout.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/animations.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/animations.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/animations.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/animations.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/badges.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/badges.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/badges.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/badges.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/bullets.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/buttons.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/dashboard.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/datatables.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/header.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/icons.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/introcontainer.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/introcontainer.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/introcontainer.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/introcontainer.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/modal.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/new-features.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/notices.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/onboarding.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/other-plugins.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/placeholder.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/placeholder.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/placeholder.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/placeholder.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/premium_overlay.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/progress.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/ssltest.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/tips-tricks.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/_closeButton.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/_icons.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/_progressBar.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/_theme.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/_toast.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/_toastContainer.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/_variables.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/animations/_bounce.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/animations/_flip.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/animations/_slide.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/animations/_spin.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/animations/_zoom.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/main.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/toast/minimal.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/tooltip.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/two-fa.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/fields.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/learning-mode.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/letsencrypt.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/menu.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/mixed-content-scan.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/notice.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/permissions-policy.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/snackbar.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard/vulnerabilities.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/wizard.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/modules/xml-rpc.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/states.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/states.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/states.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/states.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/theme.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/theme.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/theme.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin/theme.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/admin.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/rsssl-plugin.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/rsssl-plugin.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/rsssl-plugin.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/rsssl-plugin.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/rtl/admin.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/rtl/plugin.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/rtl/rsssl-plugin.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/variables.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/variables.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/variables.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/css/variables.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/assets.min.asset.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/assets.min.js.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/styles-rtl.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/styles.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/styles.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/styles.min.asset.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/styles.min.js.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/img/.DS_Store", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/img/icon.png", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/img/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/img/really-simple-plugins.svg", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/img/really-simple-security-logo.svg", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/js/.DS_Store", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/templates/passkey/login.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/templates/two_fa/expired.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/templates/two_fa/login.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/templates/two_fa/onboarding.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/templates/two_fa/profile-settings.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/templates/two_fa/selectable-option.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/templates/two_fa/totp-config.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/two-fa/rtl/two-fa.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-admin.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-cache.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-certificate.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-front-end.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-installer.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-mixed-content-fixer.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-multisite.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-server.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-site-health.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-wp-cli.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/compatibility.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/force-deactivate.txt", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/functions.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/languages/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/languages/really-simple-ssl-nl_NL.l10n.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/languages/really-simple-ssl.pot", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/class-le-restapi.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/class-letsencrypt-handler.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/composer.json", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/composer.lock", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/config/class-hosts.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/config/fields.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/config/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/config/notices.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/cron.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/download.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/functions.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/cloudways/cloudways.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/cloudways/functions.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/cpanel/cpanel.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/cpanel/functions.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/directadmin/directadmin.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/directadmin/functions.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/directadmin/httpsocket.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/hostgator/hostgator.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/integrations.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/plesk/functions.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/integrations/plesk/plesk.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/letsencrypt.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/package-lock.json", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lib/admin/class-encryption.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lib/admin/class-helper.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/.DS_Store", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/class-mail-admin.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/class-mail.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/templates/block-unbranded.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/templates/block.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/templates/email-unbranded.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/templates/email.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/mailer/templates/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/.DS_Store", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/modal.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/package-lock.json", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/package.json", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/components/DeactivationModal/DeactivationModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/components/Modal/RssslModal.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/components/Modal/RssslModal.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/components/Modal/RssslModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/components/Modal/RssslModal.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/components/Modal/RssslModal.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/src/index.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/webpack.config.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/onboarding/class-onboarding.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/onboarding/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/placeholders/class-placeholder.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/placeholders/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/progress/class-progress.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/progress/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/readme.txt", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/rector.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/rlrsssl-really-simple-ssl.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/rsssl-auto-loader.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/cron.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/deactivate-integration.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/firewall-manager.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/functions.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/hardening.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/includes/check404/class-rsssl-simple-404-interceptor.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/includes/check404/class-rsssl-test-404.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/integrations.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/notices.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/security.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/server/disable-indexing.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/server/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/sync-settings.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/tests.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/block-code-execution-uploads.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/disable-xmlrpc.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/display-name-is-login-name.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/file-editing.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/hide-wp-version.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/prevent-login-info-leakage.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/rename-admin-user.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/rest-api.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/css/profile-settings.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/css/two-fa-onboarding.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/css/two-fa.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/BaseAuth.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/initialize_two_fa.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/onboarding.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/assets/js/profile.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-parameter-validation.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-passkey-list-table.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-fa-authentication.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-fa-data-parameters.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-fa-status.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor-admin.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor-compat.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor-on-board-api.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor-reset-factory.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor-settings.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/contracts/interface-rsssl-has-processing-interface.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/contracts/interface-rsssl-two-fa-user-query-builder-interface.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/contracts/interface-rsssl-two-fa-user-repository-interface.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/controllers/class-rsssl-abstract-controller.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/controllers/class-rsssl-base-controller.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/controllers/class-rsssl-email-controller.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/controllers/class-rsssl-two-fa-user-controller.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/function-login-footer.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/function-login-header.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/models/class-rsssl-request-parameters.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/models/class-rsssl-two-fa-data-parameters.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/models/class-rsssl-two-fa-user-collection.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/models/class-rsssl-two-fa-user.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/models/class-rsssl-two-factor-user-factory.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/providers/class-rsssl-provider-loader-free.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/providers/class-rsssl-provider-loader.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/providers/class-rsssl-two-factor-provider.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/providers/interface-rsssl-two-factor-provider-interface.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/repositories/class-rsssl-two-fa-user-query-builder.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/repositories/class-rsssl-two-fa-user-repository.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/services/class-rsssl-callback-queue.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/services/class-rsssl-two-fa-forced-role-service.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/services/class-rsssl-two-fa-reminder-service.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/services/class-rsssl-two-fa-status-service.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/services/class-rsssl-two-factor-reset-service.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/traits/trait-rsssl-args-builder.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/traits/trait-rsssl-email-trait.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/traits/trait-rsssl-two-fa-helper.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/user-enumeration.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/user-registration.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities/FileStorage.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities/class-rsssl-file-storage.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities/class-rsssl-folder-name.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/vulnerabilities.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security.md", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/config.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/disable-fields-filter.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/access-control.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/encryption.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/firewall.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/general.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/hardening-basic.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/hardening-extended.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/hardening-xml.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/hibp-integration.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/letsencrypt.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/limit-login-attempts.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/security-headers.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/two-fa.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/fields/vulnerability-detection.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/mails.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/menu.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/package-lock.json", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/package.json", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/settings.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/DashboardPage.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/GridBlock.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/OtherPlugins/OtherPlugins.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/OtherPlugins/OtherPluginsData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/OtherPlugins/OtherPluginsHeader.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/Progress/ProgressBlock.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/Progress/ProgressBlockHeader.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/Progress/ProgressData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/Progress/ProgressFooter.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/SslLabs/SslLabs.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/SslLabs/SslLabsData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/SslLabs/SslLabsFooter.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/SslLabs/SslLabsHeader.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/TaskElement.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/TipsTricks/TipsTricks.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/TipsTricks/TipsTricksFooter.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/Vulnerabilities/Vulnerabilities.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/Vulnerabilities/VulnerabilitiesFooter.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/Vulnerabilities/VulnerabilitiesHeader.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Header.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/LetsEncrypt/Activate.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/LetsEncrypt/Directories.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/LetsEncrypt/DnsVerification.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/LetsEncrypt/Generation.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/LetsEncrypt/Installation.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/LetsEncrypt/LetsEncrypt.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/LetsEncrypt/letsEncryptData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Menu/Menu.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Menu/MenuData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Menu/MenuItem.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Modal/Modal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Modal/ModalControl.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Modal/ModalData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Items/CheckboxItem.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Items/ListItem.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Items/PremiumItem.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Onboarding.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/OnboardingControls.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/OnboardingData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/OnboardingModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/PremiumItem.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Steps/StepConfig.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Steps/StepEmail.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Steps/StepFeatures.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Steps/StepLicense.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Steps/StepPlugins.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/Steps/StepPro.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/checkbox.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Onboarding/onboarding.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Page.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Placeholder/DashboardPlaceholder.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Placeholder/DatatablePlaceholder.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Placeholder/MenuPlaceholder.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Placeholder/PagePlaceholder.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Placeholder/Placeholder.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Placeholder/SettingsPlaceholder.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/AutoComplete/AutoComplete.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/AutoComplete/AutoCompleteControl.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Button.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Captcha/Captcha.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Captcha/CaptchaData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Captcha/CaptchaKey.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Captcha/HCaptcha.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Captcha/ReCaptcha.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/CheckboxControl.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/Buttons/Buttons.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/Buttons/ControlButton.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/Buttons/MultiSelectButton.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/Buttons/RowButton.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/Checkboxes.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/DataTable.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/DataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/DataTableWrapper.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/SearchBar/SearchBar.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/SearchBar/SearchBar.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/SelectedRowsControl/SelectedRowsControl.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DataTable/SelectedRowsControl/SelectedRowsControl.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DynamicDataTable/AddButton.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DynamicDataTable/DynamicDataTable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DynamicDataTable/DynamicDataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/DynamicDataTable/SearchBar.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/EventLog/EventLogDataTable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/EventLog/EventLogDataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Field.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/FieldsData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/FilterData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/AddButton.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/BlockListDatatable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/GeoDataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/GeoDatatable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/TrustIpAddressModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/WhiteListDatatable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/WhiteListTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GroupFilter.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Help.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Host/Host.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Host/HostData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LearningMode/ChangeStatus.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LearningMode/Delete.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LearningMode/LearningMode.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LearningMode/LearningModeData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LearningMode/ManualCspAddition.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LearningMode/ManualCspAdditionModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/License/License.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/License/LicenseData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/AddIpAddressModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/AddUserModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/Cidr.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/CountryDataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/CountryDatatable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/IpAddressDataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/IpAddressDatatable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/IpAddressInput.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/LimitLoginAttemptsData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/UserDataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/UserDatatable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/MixedContentScan/MixedContentData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/MixedContentScan/MixedContentScan.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Notices.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Password.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/PermissionsPolicy.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/PostDropDown.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/PremiumOverlay.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RiskConfiguration/NotificationTester.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RiskConfiguration/RiskComponent.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RiskConfiguration/RiskData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RiskConfiguration/RunnerData.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RiskConfiguration/VulnerabilitiesOverview.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RiskConfiguration/datatable.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RiskConfiguration/modal.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/RolesDropDown.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/SelectControl.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Settings.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/SettingsGroup.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Support.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/TwoFA/RolesStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/TwoFA/TwoFaDataTable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/TwoFA/TwoFaDataTableStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/TwoFA/TwoFaEnabledDropDown.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/TwoFA/TwoFaRolesDropDown.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/TwoFA/select.scss", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/firewall/UserAgentModal.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/firewall/UserAgentStore.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/firewall/UserAgentTable.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/index.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/AddUrlRef.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/Error.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/ErrorBoundary.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/Flag/Flag.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/Hyperlink.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/Icon.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/ReactConditions.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/api.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/autoCompleteTheme.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/formatting.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/getAnchor.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/hoverTooltip.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/hoverTooltip_bu.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/lib.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/utils/sleeper.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/webpack.config.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/webpack.feature.config.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/ssl-test-page.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/system-status.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/cloudflare/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/cloudflare/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/cloudfront/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/cloudfront/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/envhttps/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/envhttps/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/loadbalancer/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/loadbalancer/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttps1/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttps1/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpson/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpson/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpxforwardedssl1/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpxforwardedssl1/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpxforwardedsslon/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpxforwardedsslon/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpxproto/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverhttpxproto/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverport443/.htaccess", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/testssl/serverport443/ssl-test-page.html", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/uninstall.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/ajax.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/img/burst.png", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/img/complianz-gdpr.png", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/img/really-simple-ssl.png", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/index.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.css.map", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.js", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.less", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.min.css", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.php", "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade.php"], "skipped": [{"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/assets.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/assets/features/two-fa/styles.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/composer.phar", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/lib/generator.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/lib/parse.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/lib/task.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/lib/tasks.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/lib/timer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/lib/utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/composer/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/pretty-time/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/pretty-time/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/pretty-time/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/pretty-time/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/pretty-time/utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/use/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/use/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/use/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/node_modules/use/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/composer/platform_check.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/AbstractKeyValuable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Account.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Authorizer/AbstractAuthorizer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Authorizer/AbstractDNSWriter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Authorizer/DNS.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Authorizer/HTTP.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Cache/AbstractKeyValuableCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Cache/AccountResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Cache/DirectoryResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Cache/NewNonceResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Cache/OrderResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Connector/Connector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Connector/RawResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/AbstractException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/AuthorizationInvalid.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/DNSAuthorizationInvalid.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/ExpiredAuthorization.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/HTTPAuthorizationInvalid.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/InvalidResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/OpenSSLException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/RateLimitReached.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Exception/StatusInvalid.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Order.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/AbstractRequest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Account/AbstractLocation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Account/ChangeKeys.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Account/Create.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Account/Deactivate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Account/Get.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Account/GetData.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Account/Update.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Authorization/Get.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Authorization/Start.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/GetDirectory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/GetNewNonce.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Order/Create.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Order/Finalize.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Order/Get.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Order/GetCertificate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Request/Order/RevokeCertificate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/AbstractResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/AbstractAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/AbstractLocation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/ChangeKeys.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/Create.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/Deactivate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/Get.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/GetData.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Account/Update.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Authorization/AbstractAuthorization.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Authorization/Get.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Authorization/Start.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Authorization/Struct/Challenge.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Authorization/Struct/Identifier.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/GetDirectory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/GetNewNonce.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Order/AbstractOrder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Order/Create.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Order/Finalize.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Order/Get.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Order/GetCertificate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Response/Order/RevokeCertificate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/SingletonTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Struct/CertificateBundle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Struct/ChallengeAuthorizationKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Utilities/Base64.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Utilities/Certificate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Utilities/KeyGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Utilities/Logger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2/Utilities/RequestSigner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2Tests/AbstractTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2Tests/AccountTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2Tests/Authorizer/HTTPTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/fbett/le_acme2/src/LE_ACME2Tests/TestHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/.styleci.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/.travis.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/Dockerfile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/composer.lock", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/docker-compose.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/phpunit-watcher.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Client/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Client.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/InternalClient.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/Certificate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/Dns.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/EventLog.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/Ip.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/Locale.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/PhpHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/ProtectedDirectory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/SecretKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/Server.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/Session.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator/SiteAlias.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Operator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/Certificate/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/Dns/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/EventLog/DetailedEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/EventLog/Event.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/Locale/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/PhpHandler/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/SecretKey/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/Session/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/Site/GeneralInfo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/Site/HostingInfo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/Site/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/SiteAlias/GeneralInfo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct/SiteAlias/Info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/Struct.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/src/Api/XmlResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/api-php-lib/wait-for-plesk.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/vendor/plesk/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/build/291.61c1eeb0cf491fffa001.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/build/433.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/build/433.ef6236b038b45328e4c7.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/build/926.f073f8b70413c27b4a27.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/build/index.d4bca8705bbc6e3e5777.asset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/build/index.d4bca8705bbc6e3e5777.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/tests/code-execution.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/tests/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/139.12de12bd599af6bb648c.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/366.79830113ad25eba9fb57.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/470.5091a8eb35ca6d3dbc61.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/485-rtl.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/485.47f7474dc2a61c04262b.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/485.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/573.287bf1828cad7f912345.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/8.a58b0fbbca98fed315b4.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/829-rtl.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/829.0d69f68a1345874307b1.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/829.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/838.c841004b517cdf3abd86.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/91.b0f863c7b47144cb4cdf.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/995.7a0675fe0519b06656b3.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/index.a4cc556db77e3384994b.asset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/build/index.a4cc556db77e3384994b.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/dist/bundle.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/dist/main.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/package-lock.json", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/ajax.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/upgrade/upgrade-to-pro.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8665390014648438, "profiling_times": {"config_time": 6.253237962722778, "core_time": 8.93115782737732, "ignores_time": 0.002320528030395508, "total_time": 15.187870025634766}, "parsing_time": {"total_time": 9.966937065124512, "per_file_time": {"mean": 0.02984112893749855, "std_dev": 0.003560285541587044}, "very_slow_stats": {"time_ratio": 0.10993177650065433, "count_ratio": 0.005988023952095809}, "very_slow_files": [{"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/lets-encrypt/class-letsencrypt-handler.php", "ftime": 0.34732913970947266}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/package-lock.json", "ftime": 0.7483539581298828}]}, "scanning_time": {"total_time": 66.09231114387512, "per_file_time": {"mean": 0.04991866400594803, "std_dev": 0.051068932076804775}, "very_slow_stats": {"time_ratio": 0.24560752576974285, "count_ratio": 0.005287009063444109}, "very_slow_files": [{"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/package-lock.json", "ftime": 1.5179848670959473}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Dashboard/SslLabs/SslLabs.js", "ftime": 1.6405081748962402}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/settings.php", "ftime": 2.036991834640503}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/package-lock.json", "ftime": 2.0437941551208496}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/modal/package-lock.json", "ftime": 2.192005157470703}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/security/wordpress/two-fa/class-rsssl-two-factor.php", "ftime": 2.238212823867798}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-admin.php", "ftime": 4.563271999359131}]}, "matching_time": {"total_time": 31.851768493652344, "per_file_and_rule_time": {"mean": 0.026856465846249872, "std_dev": 0.006106544367020879}, "very_slow_stats": {"time_ratio": 0.5602119973651906, "count_ratio": 0.05733558178752108}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/BlockListDatatable.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.49742984771728516}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/config/menu.php", "rule_id": "php.lang.security.exec-use.exec-use", "time": 0.5062859058380127}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-admin.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.5389649868011475}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-admin.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.5620861053466797}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/settings.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.5756151676177979}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/CountryDatatable.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5996761322021484}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/WhiteListDatatable.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.6029720306396484}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LearningMode/LearningMode.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.818856954574585}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/GeoBlockList/GeoDatatable.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.9531800746917725}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/class-admin.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 1.1051928997039795}]}, "tainting_time": {"total_time": 7.084749698638916, "per_def_and_rule_time": {"mean": 0.0016852401756990763, "std_dev": 1.6507717692577466e-05}, "very_slow_stats": {"time_ratio": 0.022621388621745203, "count_ratio": 0.0007136060894386299}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Field.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.050405025482177734}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/Field.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "time": 0.05202007293701172}, {"fpath": "downloaded_repos/Really-Simple-Plugins_really-simple-ssl/settings/src/Settings/LimitLoginAttempts/IpAddressDataTableStore.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.05784177780151367}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1092855424}, "engine_requested": "OSS", "skipped_rules": []}