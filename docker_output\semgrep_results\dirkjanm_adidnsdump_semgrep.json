{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/dirkjanm_adidnsdump/.editorconfig", "downloaded_repos/dirkjanm_adidnsdump/.gitignore", "downloaded_repos/dirkjanm_adidnsdump/LICENSE", "downloaded_repos/dirkjanm_adidnsdump/README.md", "downloaded_repos/dirkjanm_adidnsdump/adidnsdump/__init__.py", "downloaded_repos/dirkjanm_adidnsdump/adidnsdump/dnsdump.py", "downloaded_repos/dirkjanm_adidnsdump/setup.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.8589890003204346, "profiling_times": {"config_time": 7.7850987911224365, "core_time": 2.8479561805725098, "ignores_time": 0.002001047134399414, "total_time": 10.636879444122314}, "parsing_time": {"total_time": 0.03613686561584473, "per_file_time": {"mean": 0.012045621871948242, "std_dev": 3.972674869601178e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.45188045501708984, "per_file_time": {"mean": 0.0265812032362994, "std_dev": 0.006551939017345864}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.19713497161865234, "per_file_and_rule_time": {"mean": 0.008213957150777182, "std_dev": 0.00013649695439078692}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.08482837677001953, "per_def_and_rule_time": {"mean": 0.0005973829350001376, "std_dev": 2.1209866265698364e-05}, "very_slow_stats": {"time_ratio": 0.6467413911342454, "count_ratio": 0.007042253521126761}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/dirkjanm_adidnsdump/adidnsdump/dnsdump.py", "fline": 337, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.054862022399902344}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}