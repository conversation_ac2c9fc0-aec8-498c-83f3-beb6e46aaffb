{"version": "1.130.0", "results": [{"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource/Pages/ListModules.php", "start": {"line": 23, "col": 21, "offset": 667}, "end": {"line": 23, "col": 66, "offset": 712}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource/Pages/ViewModule.php", "start": {"line": 31, "col": 21, "offset": 1112}, "end": {"line": 31, "col": 76, "offset": 1167}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2332, "offset": 2331}, "end": {"line": 1, "col": 2403, "offset": 2402}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "start": {"line": 42, "col": 29759, "offset": 40271}, "end": {"line": 42, "col": 29794, "offset": 40306}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "start": {"line": 43, "col": 1039, "offset": 46975}, "end": {"line": 43, "col": 1052, "offset": 46988}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "start": {"line": 43, "col": 2738, "offset": 48674}, "end": {"line": 43, "col": 2862, "offset": 48798}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "start": {"line": 93, "col": 58, "offset": 189773}, "end": {"line": 93, "col": 115, "offset": 189830}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/tags-input.js", "start": {"line": 1, "col": 751, "offset": 750}, "end": {"line": 1, "col": 768, "offset": 767}, "extra": {"message": "RegExp() called with a `{state:a,splitKeys:n}` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/date-time-picker.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/date-time-picker.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "message": "Timeout when running javascript.express.security.audit.express-res-sendfile.express-res-sendfile on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js:\n ", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php", "start": {"line": 80, "col": 25, "offset": 0}, "end": {"line": 80, "col": 30, "offset": 5}}, {"path": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php", "start": {"line": 81, "col": 25, "offset": 0}, "end": {"line": 81, "col": 31, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php:80:\n `true:` was unexpected", "path": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php", "spans": [{"file": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php", "start": {"line": 80, "col": 25, "offset": 0}, "end": {"line": 80, "col": 30, "offset": 5}}, {"file": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php", "start": {"line": 81, "col": 25, "offset": 0}, "end": {"line": 81, "col": 31, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2843, "offset": 0}, "end": {"line": 1, "col": 2846, "offset": 3}}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2866, "offset": 0}, "end": {"line": 1, "col": 2869, "offset": 3}}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2912, "offset": 0}, "end": {"line": 1, "col": 2915, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js:1:\n `01:` was unexpected", "path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "spans": [{"file": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2843, "offset": 0}, "end": {"line": 1, "col": 2846, "offset": 3}}, {"file": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2866, "offset": 0}, "end": {"line": 1, "col": 2869, "offset": 3}}, {"file": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2912, "offset": 0}, "end": {"line": 1, "col": 2915, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/liberu-cms_cms-laravel/.circleci/config.yml", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/conf.d/default.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/config/conf.d/default.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/config/fpm-pool.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/config/nginx.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/config/php.ini", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/config/supervisord.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/fpm-pool.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/nginx.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/php.ini", "downloaded_repos/liberu-cms_cms-laravel/.docker/config/supervisord.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/.rr.prod.yaml", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/FrankenPHP/supervisord.frankenphp.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/RoadRunner/.rr.prod.yaml", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/RoadRunner/supervisord.roadrunner.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/Swoole/supervisord.swoole.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/entrypoint.sh", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/opcache.ini", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/php.ini", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/supervisord.app.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/supervisord.app.roadrunner.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/supervisord.horizon.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/octane/utilities.sh", "downloaded_repos/liberu-cms_cms-laravel/.docker/php.ini", "downloaded_repos/liberu-cms_cms-laravel/.docker/start-container", "downloaded_repos/liberu-cms_cms-laravel/.docker/supervisord.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/supervisord.horizon.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/supervisord.scheduler.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/supervisord.worker.conf", "downloaded_repos/liberu-cms_cms-laravel/.docker/utilities.sh", "downloaded_repos/liberu-cms_cms-laravel/.env.example", "downloaded_repos/liberu-cms_cms-laravel/.env.testing", "downloaded_repos/liberu-cms_cms-laravel/.github/FUNDING.yml", "downloaded_repos/liberu-cms_cms-laravel/.github/ISSUE_TEMPLATE/sweep-template.yml", "downloaded_repos/liberu-cms_cms-laravel/.github/dependabot.yml", "downloaded_repos/liberu-cms_cms-laravel/.github/issue_template.md", "downloaded_repos/liberu-cms_cms-laravel/.github/workflows/install.yml", "downloaded_repos/liberu-cms_cms-laravel/.github/workflows/main.yml", "downloaded_repos/liberu-cms_cms-laravel/.github/workflows/security.yml", "downloaded_repos/liberu-cms_cms-laravel/.github/workflows/tests.yml", "downloaded_repos/liberu-cms_cms-laravel/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/Dockerfile", "downloaded_repos/liberu-cms_cms-laravel/README.md", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Fortify/CreateNewUser.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Fortify/CreateNewUserWithTeams.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Fortify/PasswordValidationRules.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Fortify/ResetUserPassword.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Fortify/UpdateUserPassword.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Fortify/UpdateUserProfileInformation.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/AddTeamMember.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/CreateTeam.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/DeleteTeam.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/DeleteUser.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/DeleteUserWithTeams.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/InviteTeamMember.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/RemoveTeamMember.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Jetstream/UpdateTeamName.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/CreateConnectedAccount.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/CreateUserFromProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/CreateUserWithTeamsFromProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/GenerateRedirectForProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/HandleInvalidState.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/ResolveSocialiteUser.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/SetUserPassword.php", "downloaded_repos/liberu-cms_cms-laravel/app/Actions/Socialstream/UpdateConnectedAccount.php", "downloaded_repos/liberu-cms_cms-laravel/app/Console/Commands/ContentPublishLoadTest.php", "downloaded_repos/liberu-cms_cms-laravel/app/Console/Commands/ModuleCommand.php", "downloaded_repos/liberu-cms_cms-laravel/app/Console/Kernel.php", "downloaded_repos/liberu-cms_cms-laravel/app/Exceptions/ExceptionHandler.php", "downloaded_repos/liberu-cms_cms-laravel/app/Exceptions/Handler.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GlobalSettingsResource/Pages/CreateGlobalSettings.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GlobalSettingsResource/Pages/EditGlobalSettings.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GlobalSettingsResource/Pages/ListGlobalSettings.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GlobalSettingsResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestLayoutManagmentResource/Pages/CreateGuestLayoutManagment.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestLayoutManagmentResource/Pages/EditGuestLayoutManagment.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestLayoutManagmentResource/Pages/ListGuestLayoutManagments.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestLayoutManagmentResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestMenuResource/Pages/CreateGuestMenu.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestMenuResource/Pages/EditGuestMenu.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestMenuResource/Pages/ListGuestMenus.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/GuestMenuResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/MenuResource/Pages/CreateMenu.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/MenuResource/Pages/EditMenu.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/MenuResource/Pages/ListMenus.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource/Pages/ListModules.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource/Pages/ViewModule.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/SiteSettingsResource/Pages/CreateSiteSettings.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/SiteSettingsResource/Pages/EditSiteSettings.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/SiteSettingsResource/Pages/ListSiteSettings.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/TeamsResource/Pages/CreateTeams.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/TeamsResource/Pages/EditTeams.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/TeamsResource/Pages/ListTeams.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/TeamsResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Pages/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Pages/CreateTeam.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Pages/EditProfile.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Pages/EditTeam.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Pages/PersonalAccessTokensPage.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Pages/UpdateProfileInformationPage.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ActivationResource/Pages/CreateActivation.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ActivationResource/Pages/EditActivation.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ActivationResource/Pages/ListActivations.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ActivationResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/AuthorResource/Pages/CreateAuthor.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/AuthorResource/Pages/EditAuthor.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/AuthorResource/Pages/ListAuthors.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/AuthorResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CommentResource/Pages/CreateComment.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CommentResource/Pages/EditComment.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CommentResource/Pages/ListComments.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CommentResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CompanyResource/Pages/CreateCompany.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CompanyResource/Pages/EditCompany.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CompanyResource/Pages/ListCompanies.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/CompanyResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentCategoryResource/Pages/CreateContentCategory.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentCategoryResource/Pages/EditContentCategory.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentCategoryResource/Pages/ListContentCategories.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentCategoryResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentResource/Pages/ContentVersionHistory.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentResource/Pages/CreateContent.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentResource/Pages/EditContent.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentResource/Pages/ListContents.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/ContentResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/PageResource/Pages/CreatePage.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/PageResource/Pages/EditPage.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/PageResource/Pages/ListPages.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/PageResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/TagResource/Pages/CreateTag.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/TagResource/Pages/EditTag.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/TagResource/Pages/ListTags.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Resources/TagResource.php", "downloaded_repos/liberu-cms_cms-laravel/app/Filament/App/Widgets/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/AboutController.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/ContactController.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/Controller.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/ForgotPasswordController.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/LoginController.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/ResetPasswordController.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/TeamInvitationController.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Controllers/WebRenderController.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Kernel.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Livewire/ContentComponent.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Livewire/ContentPreview.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Livewire/CreateTeam.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Livewire/Homepage.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Livewire/PageComponent.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Livewire/PostComponent.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Livewire/TagComponent.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/Authenticate.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/EncryptCookies.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/PreventRequestsDuringMaintenance.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/RedirectIfAuthenticated.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/ScreeningDataEncryptor.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/TeamsPermission.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/TrimStrings.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/TrustHosts.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/TrustProxies.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/ValidateSignature.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Middleware/VerifyCsrfToken.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Requests/StoreaboutRequest.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Requests/UpdateaboutRequest.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Responses/LoginResponse.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Responses/LogoutResponse.php", "downloaded_repos/liberu-cms_cms-laravel/app/Http/Responses/RegisterResponse.php", "downloaded_repos/liberu-cms_cms-laravel/app/Listeners/CreatePersonalTeam.php", "downloaded_repos/liberu-cms_cms-laravel/app/Listeners/EmailTracker.php", "downloaded_repos/liberu-cms_cms-laravel/app/Listeners/SwitchTeam.php", "downloaded_repos/liberu-cms_cms-laravel/app/Livewire/Counter.php", "downloaded_repos/liberu-cms_cms-laravel/app/Livewire/Footer.php", "downloaded_repos/liberu-cms_cms-laravel/app/Livewire/Header.php", "downloaded_repos/liberu-cms_cms-laravel/app/Livewire/Navigation.php", "downloaded_repos/liberu-cms_cms-laravel/app/Livewire/Webrender.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Activation.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Author.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Comment.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Company.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Contact.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Content.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/ContentCategory.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/ContentVersion.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/GuestLayoutManagment.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Membership.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Menu.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Page.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Person.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Post.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Tag.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Team.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/TeamInvitation.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/Tenant.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/User.php", "downloaded_repos/liberu-cms_cms-laravel/app/Models/about.php", "downloaded_repos/liberu-cms_cms-laravel/app/Modules/BaseModule.php", "downloaded_repos/liberu-cms_cms-laravel/app/Modules/Contracts/ModuleInterface.php", "downloaded_repos/liberu-cms_cms-laravel/app/Modules/ModuleManager.php", "downloaded_repos/liberu-cms_cms-laravel/app/Modules/ModuleServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/ActivationPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/AuthorPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/CommentPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/CompanyPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/ContentCategoryPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/ContentPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/GuestLayoutManagmentPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/MenuPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/PagePolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/RolePolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/SiteSettingsPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/TagPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Policies/TeamPolicy.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/AppServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/AuthServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/BroadcastServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/EventServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/Filament/AdminPanelProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/Filament/AppPanelProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/FortifyServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/JetstreamServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/RouteServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/SocialstreamServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Providers/TeamServiceProvider.php", "downloaded_repos/liberu-cms_cms-laravel/app/Services/EntityManagementService.php", "downloaded_repos/liberu-cms_cms-laravel/app/Services/FileService.php", "downloaded_repos/liberu-cms_cms-laravel/app/Services/MenuService.php", "downloaded_repos/liberu-cms_cms-laravel/app/Services/SiteSettingsService.php", "downloaded_repos/liberu-cms_cms-laravel/app/Traits/IsTenantModel.php", "downloaded_repos/liberu-cms_cms-laravel/app/Traits/SEOable.php", "downloaded_repos/liberu-cms_cms-laravel/app/View/Components/AppLayout.php", "downloaded_repos/liberu-cms_cms-laravel/app/View/Components/GuestLayout.php", "downloaded_repos/liberu-cms_cms-laravel/artisan", "downloaded_repos/liberu-cms_cms-laravel/bootstrap/app.php", "downloaded_repos/liberu-cms_cms-laravel/bootstrap/cache/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/composer.json", "downloaded_repos/liberu-cms_cms-laravel/composer.lock", "downloaded_repos/liberu-cms_cms-laravel/config/app.php", "downloaded_repos/liberu-cms_cms-laravel/config/auth.php", "downloaded_repos/liberu-cms_cms-laravel/config/broadcasting.php", "downloaded_repos/liberu-cms_cms-laravel/config/cache.php", "downloaded_repos/liberu-cms_cms-laravel/config/cors.php", "downloaded_repos/liberu-cms_cms-laravel/config/database.php", "downloaded_repos/liberu-cms_cms-laravel/config/filament-shield.php", "downloaded_repos/liberu-cms_cms-laravel/config/filament.php", "downloaded_repos/liberu-cms_cms-laravel/config/filesystems.php", "downloaded_repos/liberu-cms_cms-laravel/config/fortify.php", "downloaded_repos/liberu-cms_cms-laravel/config/hashing.php", "downloaded_repos/liberu-cms_cms-laravel/config/jetstream.php", "downloaded_repos/liberu-cms_cms-laravel/config/livewire.php", "downloaded_repos/liberu-cms_cms-laravel/config/logging.php", "downloaded_repos/liberu-cms_cms-laravel/config/mail.php", "downloaded_repos/liberu-cms_cms-laravel/config/modules.php", "downloaded_repos/liberu-cms_cms-laravel/config/octane.php", "downloaded_repos/liberu-cms_cms-laravel/config/permission.php", "downloaded_repos/liberu-cms_cms-laravel/config/queue.php", "downloaded_repos/liberu-cms_cms-laravel/config/sanctum.php", "downloaded_repos/liberu-cms_cms-laravel/config/services.php", "downloaded_repos/liberu-cms_cms-laravel/config/session.php", "downloaded_repos/liberu-cms_cms-laravel/config/view.php", "downloaded_repos/liberu-cms_cms-laravel/database/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/database/factories/MenuFactory.php", "downloaded_repos/liberu-cms_cms-laravel/database/factories/TeamFactory.php", "downloaded_repos/liberu-cms_cms-laravel/database/factories/UserFactory.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/0001_01_01_000000_create_users_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/0001_01_01_000001_make_password_nullable_on_users_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/0001_01_01_000002_create_connected_accounts_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2014_10_12_200000_add_two_factor_columns_to_users_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2017_01_01_112100_create_file_types_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2019_08_19_000000_create_failed_jobs_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2019_09_15_000010_create_tenants_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2019_09_15_000020_create_domains_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2019_12_14_000001_create_personal_access_tokens_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2020_05_21_100000_create_teams_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2020_05_21_200000_create_team_user_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2020_05_21_300000_create_team_invitations_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2020_06_30_135250_create_activations_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_24_172932_create_tags_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_25_165425_create_content_categories_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_26_000000_create_categories_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_26_164350_create_authors_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_26_164941_create_contents_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_26_165831_create_comments_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_27_000000_create_pages_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2023_10_28_000000_create_posts_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_02_21_190705_create_permission_tables.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_07_01_000000_add_indexes_to_contents_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_07_24_080000_create_menus_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_08_11_080605_create_abouts_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_08_11_174853_create_contacts_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_08_16_144419_create_home_contents_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_08_17_095101_create_guest_layout_managments_table.php", "downloaded_repos/liberu-cms_cms-laravel/database/migrations/2024_09_29_093707_add_team_to_resources.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/DatabaseSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/GuestLayoutManagmentSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/MenuSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/PermissionsSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/RolesSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/SiteSettingsSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/Tables/PermissionsSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/TeamSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/database/seeders/UserSeeder.php", "downloaded_repos/liberu-cms_cms-laravel/docs/MODULAR_ARCHITECTURE.md", "downloaded_repos/liberu-cms_cms-laravel/error_log", "downloaded_repos/liberu-cms_cms-laravel/package-lock.json", "downloaded_repos/liberu-cms_cms-laravel/package.json", "downloaded_repos/liberu-cms_cms-laravel/phpunit.xml", "downloaded_repos/liberu-cms_cms-laravel/postcss.config.cjs", "downloaded_repos/liberu-cms_cms-laravel/postcss.config.js", "downloaded_repos/liberu-cms_cms-laravel/public/.htaccess", "downloaded_repos/liberu-cms_cms-laravel/public/css/filament/filament/app.css", "downloaded_repos/liberu-cms_cms-laravel/public/css/filament/forms/forms.css", "downloaded_repos/liberu-cms_cms-laravel/public/css/filament/support/support.css", "downloaded_repos/liberu-cms_cms-laravel/public/favicon.ico", "downloaded_repos/liberu-cms_cms-laravel/public/index.php", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/app.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/date-time-picker.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/key-value.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/tags-input.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/textarea.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/notifications/notifications.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/async-alpine.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/tables/components/table.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js", "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js", "downloaded_repos/liberu-cms_cms-laravel/public/robots.txt", "downloaded_repos/liberu-cms_cms-laravel/rector.php", "downloaded_repos/liberu-cms_cms-laravel/resources/css/app.css", "downloaded_repos/liberu-cms_cms-laravel/resources/css/filament/admin/tailwind.config.js", "downloaded_repos/liberu-cms_cms-laravel/resources/css/filament/admin/theme.css", "downloaded_repos/liberu-cms_cms-laravel/resources/images/logo.png", "downloaded_repos/liberu-cms_cms-laravel/resources/js/app.js", "downloaded_repos/liberu-cms_cms-laravel/resources/markdown/policy.md", "downloaded_repos/liberu-cms_cms-laravel/resources/markdown/terms.md", "downloaded_repos/liberu-cms_cms-laravel/resources/views/api/api-token-manager.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/api/index.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/auth/confirm-password.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/auth/forgot-password.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/auth/login.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/auth/register.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/auth/reset-password.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/auth/two-factor-challenge.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/auth/verify-email.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/action-message.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/action-section.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/application-logo.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/application-mark.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/authentication-card-logo.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/authentication-card.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/banner.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/button.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/buttons.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/checkbox.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/confirmation-modal.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/confirms-password.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/contact-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/danger-button.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/dialog-modal.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/dropdown-link.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/dropdown.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/footer.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/form-section.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/header.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/home-header.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/home-navbar.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/image-preview.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/input-error.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/input.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/label.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/layouts/app.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/manage_section.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/modal.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/nav-link.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/nav.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/products_section.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/responsive-nav-link.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/secondary-button.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/section-border.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/section-title.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/switchable-team.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/validation-errors.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/welcome.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/components/why_us_section.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/dashboard.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/filament/pages/api-tokens.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/filament/pages/create-team.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/filament/pages/edit-profile.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/filament/pages/edit-team.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/pages/api-tokens.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/pages/create-team.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/pages/edit-profile.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/pages/edit-team.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/resources/gedcom-resource/pages/gedcom.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/widgets/daboville-report.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/widgets/descendant-chart.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/filament/widgets/report-widget.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/layouts/app.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/layouts/guest.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/livewire/content-preview.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/livewire/counter.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/livewire/footer.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/livewire/homepage.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/livewire/navigation.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/livewire/webrender.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/navigation-menu.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/partials/content-element.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/partials/elements/default.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/policy.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/profile/delete-user-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/profile/logout-other-browser-sessions-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/profile/show.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/profile/two-factor-authentication-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/profile/update-password-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/profile/update-profile-information-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/teams/create-team-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/teams/create.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/teams/delete-team-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/teams/show.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/teams/team-member-manager.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/teams/update-team-name-form.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/terms.blade.php", "downloaded_repos/liberu-cms_cms-laravel/resources/views/welcome.blade.php", "downloaded_repos/liberu-cms_cms-laravel/routes/api.php", "downloaded_repos/liberu-cms_cms-laravel/routes/channels.php", "downloaded_repos/liberu-cms_cms-laravel/routes/console.php", "downloaded_repos/liberu-cms_cms-laravel/routes/web.php", "downloaded_repos/liberu-cms_cms-laravel/setup.sh", "downloaded_repos/liberu-cms_cms-laravel/storage/app/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/app/public/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/framework/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/framework/cache/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/framework/cache/data/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/framework/sessions/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/framework/testing/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/framework/views/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/storage/logs/.gitignore", "downloaded_repos/liberu-cms_cms-laravel/sweep.yaml", "downloaded_repos/liberu-cms_cms-laravel/tailwind.config.js", "downloaded_repos/liberu-cms_cms-laravel/vite.config.js"], "skipped": [{"path": "downloaded_repos/liberu-cms_cms-laravel/app/Filament/Admin/Resources/ModuleResource.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/ar/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/ckb/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/cs/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/de/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/en/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/es/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/fa/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/fr/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/hu/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/hy/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/id/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/it/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/ja/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/km/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/ko/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/ku/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/lv/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/nl/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/pl/filament_shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/pt_BR/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/pt_PT/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/ro/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/ru/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/sk/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/sq/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/tr/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/uk/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/vi/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/zh_CN/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/lang/vendor/filament-shield/zh_TW/filament-shield.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/build/assets/app-9gw5chkH.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/build/assets/app-BoFAPcN5.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/build/assets/theme-B1I_7hHH.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/build/images/logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/build/manifest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/color-picker.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/date-time-picker.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/CreatesApplication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/ApiTokenPermissionsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/BrowserSessionsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/CreateApiTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/CreateTeamTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/DeleteAccountTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/DeleteApiTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/DeleteTeamTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/InviteTeamMemberTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/LeaveTeamTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/ModuleSystemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/ProfileInformationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/RemoveTeamMemberTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/TwoFactorAuthenticationSettingsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/UpdatePasswordTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/UpdateTeamMemberRoleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Feature/UpdateTeamNameTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Unit/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Unit/FilamentResources/PageResourceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Unit/Models/ContentTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Unit/Models/PageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/liberu-cms_cms-laravel/tests/Unit/Providers/AppServiceProviderTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.5068790912628174, "profiling_times": {"config_time": 6.2818872928619385, "core_time": 47.917216300964355, "ignores_time": 0.0019083023071289062, "total_time": 54.20225167274475}, "parsing_time": {"total_time": 2.527083396911621, "per_file_time": {"mean": 0.00668540581193551, "std_dev": 0.0002811196280737964}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 156.4203429222107, "per_file_time": {"mean": 0.12060165221450322, "std_dev": 3.0485332002250516}, "very_slow_stats": {"time_ratio": 0.9427294389309189, "count_ratio": 0.009252120277563608}, "very_slow_files": [{"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/notifications/notifications.js", "ftime": 3.111677885055542}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/support/support.js", "ftime": 5.137054920196533}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/stats-overview/stat/chart.js", "ftime": 5.19734001159668}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/file-upload.js", "ftime": 5.244640111923218}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/widgets/components/chart.js", "ftime": 5.2523181438446045}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/markdown-editor.js", "ftime": 5.565541982650757}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/date-time-picker.js", "ftime": 9.199157953262329}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/select.js", "ftime": 27.92935299873352}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "ftime": 33.1301691532135}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "ftime": 43.01023006439209}]}, "matching_time": {"total_time": 32.835450649261475, "per_file_and_rule_time": {"mean": 0.03791622476820032, "std_dev": 0.03925641380759814}, "very_slow_stats": {"time_ratio": 0.9241522380811261, "count_ratio": 0.06004618937644342}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 1.1081788539886475}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 1.1740810871124268}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.2569670677185059}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/date-time-picker.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.3666632175445557}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.603806972503662}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.7633099555969238}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.854952096939087}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 2.0151450634002686}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 2.088071823120117}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 2.1033718585968018}]}, "tainting_time": {"total_time": 38.30560111999512, "per_def_and_rule_time": {"mean": 0.0018111395328602893, "std_dev": 0.002476230176542411}, "very_slow_stats": {"time_ratio": 0.8041699361958201, "count_ratio": 0.0023167848699763593}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.21806001663208}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "time": 1.471879005432129}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 1.532196044921875}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 1.5329349040985107}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 1.5975289344787598}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "time": 1.7610361576080322}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 2.0609729290008545}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 2.8538029193878174}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 2.8644511699676514}, {"fpath": "downloaded_repos/liberu-cms_cms-laravel/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 3.320460081100464}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1093544320}, "engine_requested": "OSS", "skipped_rules": []}