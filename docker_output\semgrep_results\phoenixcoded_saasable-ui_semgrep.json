{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "start": {"line": 442, "col": 5, "offset": 15744}, "end": {"line": 442, "col": 41, "offset": 15780}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 17, "col": 3, "offset": 870}, "end": {"line": 17, "col": 121, "offset": 988}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 398, "col": 76, "offset": 0}, "end": {"line": 398, "col": 88, "offset": 12}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 749, "col": 22, "offset": 0}, "end": {"line": 749, "col": 35, "offset": 13}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 752, "col": 27, "offset": 0}, "end": {"line": 752, "col": 39, "offset": 12}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 761, "col": 30, "offset": 0}, "end": {"line": 761, "col": 44, "offset": 14}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 767, "col": 22, "offset": 0}, "end": {"line": 767, "col": 36, "offset": 14}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 770, "col": 29, "offset": 0}, "end": {"line": 770, "col": 54, "offset": 25}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 773, "col": 36, "offset": 0}, "end": {"line": 773, "col": 47, "offset": 11}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 785, "col": 34, "offset": 0}, "end": {"line": 785, "col": 57, "offset": 23}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1399, "col": 76, "offset": 0}, "end": {"line": 1399, "col": 95, "offset": 19}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1524, "col": 76, "offset": 0}, "end": {"line": 1524, "col": 95, "offset": 19}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1718, "col": 49, "offset": 0}, "end": {"line": 1718, "col": 59, "offset": 10}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1721, "col": 49, "offset": 0}, "end": {"line": 1721, "col": 58, "offset": 9}}]], "message": "Syntax error at line downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html:398:\n `& Conditions` was unexpected", "path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "spans": [{"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 398, "col": 76, "offset": 0}, "end": {"line": 398, "col": 88, "offset": 12}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 749, "col": 22, "offset": 0}, "end": {"line": 749, "col": 35, "offset": 13}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 752, "col": 27, "offset": 0}, "end": {"line": 752, "col": 39, "offset": 12}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 761, "col": 30, "offset": 0}, "end": {"line": 761, "col": 44, "offset": 14}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 767, "col": 22, "offset": 0}, "end": {"line": 767, "col": 36, "offset": 14}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 770, "col": 29, "offset": 0}, "end": {"line": 770, "col": 54, "offset": 25}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 773, "col": 36, "offset": 0}, "end": {"line": 773, "col": 47, "offset": 11}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 785, "col": 34, "offset": 0}, "end": {"line": 785, "col": 57, "offset": 23}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1399, "col": 76, "offset": 0}, "end": {"line": 1399, "col": 95, "offset": 19}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1524, "col": 76, "offset": 0}, "end": {"line": 1524, "col": 95, "offset": 19}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1718, "col": 49, "offset": 0}, "end": {"line": 1718, "col": 59, "offset": 10}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "start": {"line": 1721, "col": 49, "offset": 0}, "end": {"line": 1721, "col": 58, "offset": 9}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-header.html", "start": {"line": 327, "col": 76, "offset": 0}, "end": {"line": 327, "col": 88, "offset": 12}}]], "message": "Syntax error at line downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-header.html:327:\n `& Conditions` was unexpected", "path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-header.html", "spans": [{"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-header.html", "start": {"line": 327, "col": 76, "offset": 0}, "end": {"line": 327, "col": 88, "offset": 12}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html", "start": {"line": 22, "col": 231, "offset": 0}, "end": {"line": 22, "col": 241, "offset": 10}}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html", "start": {"line": 23, "col": 231, "offset": 0}, "end": {"line": 23, "col": 240, "offset": 9}}]], "message": "Syntax error at line downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html:22:\n `& Licenses` was unexpected", "path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html", "spans": [{"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html", "start": {"line": 22, "col": 231, "offset": 0}, "end": {"line": 22, "col": 241, "offset": 10}}, {"file": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html", "start": {"line": 23, "col": 231, "offset": 0}, "end": {"line": 23, "col": 240, "offset": 9}}]}], "paths": {"scanned": ["downloaded_repos/phoenixcoded_saasable-ui/.github/workflows/prod.yml", "downloaded_repos/phoenixcoded_saasable-ui/LICENSE", "downloaded_repos/phoenixcoded_saasable-ui/README.md", "downloaded_repos/phoenixcoded_saasable-ui/admin/.env", "downloaded_repos/phoenixcoded_saasable-ui/admin/.gitignore", "downloaded_repos/phoenixcoded_saasable-ui/admin/.prettierignore", "downloaded_repos/phoenixcoded_saasable-ui/admin/.prettierrc", "downloaded_repos/phoenixcoded_saasable-ui/admin/README.md", "downloaded_repos/phoenixcoded_saasable-ui/admin/eslint.config.mjs", "downloaded_repos/phoenixcoded_saasable-ui/admin/jsconfig.json", "downloaded_repos/phoenixcoded_saasable-ui/admin/next-env.d.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/next.config.mjs", "downloaded_repos/phoenixcoded_saasable-ui/admin/package-lock.json", "downloaded_repos/phoenixcoded_saasable-ui/admin/package.json", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/.gitkeep", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/cards/poster.png", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/components/fullscreen.webp", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/social/apple-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/social/apple-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/social/facebook.svg", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/social/google.svg", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/users/avatar-1.png", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/users/avatar-2.png", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/users/avatar-3.png", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/users/avatar-4.png", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/users/avatar-5.png", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/styles/index.css", "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/styles/third-party/README.md", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/(components)/utils/color/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/(components)/utils/loading.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/(components)/utils/shadow/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/(components)/utils/typography/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/dashboard/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/loading.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/(admin)/sample-page/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/ProviderWrapper.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/auth/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/auth/login/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/auth/register/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/error.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/favicon.ico", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/globals.css", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/loading.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/not-found.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/app/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/branding.json", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/Breadcrumbs.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/ComponentsWrapper.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/Contact.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/DynamicIcon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/Error404.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/Error500.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/Loader.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/MainCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/NotificationItem.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/Profile.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/cards/OverviewCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/cards/PresentationCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/cards/ProgressCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/header/empty-state/EmptyNotification.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/header/empty-state/EmptySearch.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/logo/LogoIcon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/logo/LogoMain.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/logo/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/third-party/Notistack.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/third-party/SimpleBar.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/components/third-party/chart/Legend.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/config.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/contexts/ConfigContext.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/data/countries.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/enum.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/hooks/useConfig.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/hooks/useLocalStorage.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/hooks/useMenuCollapse.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/illustration/DumpingDoodle.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/illustration/ReadingSideDoodle.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/illustration/index.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/maintenance/Error404.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/maintenance/Error500.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/maintenance/Error500Server.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/DrawerContent/NavCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/DrawerContent/ResponsiveDrawer/NavCollapse.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/DrawerContent/ResponsiveDrawer/NavGroup.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/DrawerContent/ResponsiveDrawer/NavItem.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/DrawerContent/ResponsiveDrawer/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/DrawerContent/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/DrawerHeader/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/MiniDrawerStyled.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Drawer/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Header/AppBarStyled.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Header/HeaderContent/Notification.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Header/HeaderContent/Profile.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Header/HeaderContent/SearchBar.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Header/HeaderContent/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Header/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AuthLayout/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/menu/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/menu/manage.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/menu/other.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/menu/pages.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/menu/ui-elements.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/auth/AuthLogin.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/auth/AuthRegister.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/auth/AuthSocial.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/auth/Copyright.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/components/color/ColorBox.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/components/color/index.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/dashboard/AnalyticsOverviewCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/dashboard/AnalyticsOverviewChart.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/sections/dashboard/AnalyticsTopRef.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/states/menu.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/states/snackbar.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Alert.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Avatar.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/AvatarGroup.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Backdrop.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/BarLabel.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Breadcrumbs.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Button.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/CardActions.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/CardContent.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/CardHeader.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/ChartsAxis.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/ChartsAxisHighlight.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/ChartsTooltip.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Chip.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/FormControlLabel.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/FormHelperText.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/IconButton.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/InputAdornment.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/InputLabel.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/LinearProgress.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/ListItemButton.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/ListItemIcon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/ListItemText.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/OutlinedInput.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Popper.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Switch.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Tab.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Tabs.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/Tooltip.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/overrides/index.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/palette.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/shadow.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/theme/README.md", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/themes/typography.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/utils/GetImagePath.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/utils/generateFocusStyle.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/utils/getRadiusStyles.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/utils/validationSchema.js", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/views/admin/dashboard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/views/admin/sample-page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/views/auth/login.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/views/auth/register.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/views/components/utils/colors.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/views/components/utils/shadow.jsx", "downloaded_repos/phoenixcoded_saasable-ui/admin/src/views/components/utils/typography.jsx", "downloaded_repos/phoenixcoded_saasable-ui/support.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/.env", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/.gitignore", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/.prettierignore", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/.prettierrc", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/README.md", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/eslint.config.mjs", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/jsconfig.json", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/next-env.d.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/next.config.mjs", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/package-lock.json", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/package.json", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/.gitkeep", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/background1.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/background2.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/desktop1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/desktop2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics12-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics15-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics20-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics21-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics3-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics4-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics6-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics7-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics8-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics9-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/rocket.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/default/admin-dashboard-2.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/default/admin-dashboard-3.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/default/admin-dashboard.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/default/feature23-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/ai-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/crm-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/crypto-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/hosting-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/hrm-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/lms-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/plugin-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/mega-menu/pms-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/metadata/og.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/404-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/500-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/benefits-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/blog-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/clientele-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/color-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/coming-soon-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/contact-us-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/content-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/cookies-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/cta-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/early-access-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/faqs-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/feature-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/footer-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/forgot-pass-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/gallery-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/hero-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/icon-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/integration-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/maintenance-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/mega-menu-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/navbar-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/new-pass-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/onboard-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/other-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/otp-gen-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/pricing-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/privacy-policy-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/process-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/sign-in-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/sign-up-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/small-hero-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/team-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/testimonial-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/top-offer-bar-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/presentation/typography-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/pro-page/Lock.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/celebration.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/figma.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/javascript.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/jwt.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/m3.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/material-ui.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/next-js.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/pattern1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/pattern1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/react.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/shared/typescript.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/team/team-member-1.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/testimonial/avatar-bg1.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/testimonial/avatar-bg2.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar1.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar10.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar11.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar12.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar13.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar14.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar2.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar3.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar4.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar5.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar6.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar7.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar8.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/user/avatar9.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/svg/sprite-custom.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/svg/tabler-sprite-fill.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/videos/thumbnails/intro-thumbnail.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/robots.txt", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/(default)/contact/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/(default)/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/(default)/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/(default)/privacy-policy/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/(default)/terms-condition/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/ProviderWrapper.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/api/subscribe/route.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/benefit/benefit5/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/clientele/clientele3/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/contact-us/contact-us4/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/cta/cta4/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/cta/cta5/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/error404/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/error500/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/faq/faq6/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/feature/feature18/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/feature/feature20/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/feature/feature21/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/footer/footer7/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/hero/hero17/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/integration/integration2/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/megamenu/megamenu4/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/megamenu/megamenu5/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/navbar/navbar10/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/other/other1/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/other/other2/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/pricing/pricing9/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/pro-page/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/small-hero/small-hero3/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/blocks/testimonial/testimonial10/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/error.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/favicon.ico", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/globals.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/loading.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/not-found.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/about/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/auth/forgot-password/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/auth/login/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/auth/new-password/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/auth/otp-verification/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/auth/register/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/benefit/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/blog/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/clientele/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/color/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/coming-soon/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/contact-us/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/cookie/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/cta/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/early-access/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/error404/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/error500/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/faq/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/feature/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/footer/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/gallery/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/hero/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/icon/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/integration/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/loading.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/mega-menu/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/navbar/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/onboard/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/other/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/pricing/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/privacy-policy/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/pro-page/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/process/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/small-hero/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/team/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/terms-condition/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/testimonial/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/top-offer/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/typography/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/app/sections/under-maintenance/page.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/PrivacyPolicy.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/TermsCondition.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/Typography.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/benefit/Benefit5.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/benefit/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/clientele/Clientele3.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/clientele/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/contact-us/ContactUs4.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/contact-us/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/cta/Cta4.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/cta/Cta5.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/cta/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/faq/Faq6.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/faq/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/feature/Feature18.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/feature/Feature20.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/feature/Feature21.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/feature/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/footer/Footer7.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/footer/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/hero/Hero17.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/hero/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/integration/Integration2.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/integration/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/maintenance/Error404.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/maintenance/Error500.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/maintenance/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/mega-menu/MegaMenu4.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/mega-menu/MegaMenu5.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/mega-menu/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/navbar/Navbar10.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/navbar/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/navbar/navbar-content/NavbarContent10.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/navbar/navbar-content/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/other/Other1.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/other/Other2.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/other/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/pricing/Pricing9.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/pricing/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/pro-page/ProPage.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/pro-page/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/small-hero/SmallHero3.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/small-hero/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/testimonial/Testimonial10.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/testimonial/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/branding.json", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/ButtonAnimationWrapper.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/ContainerWrapper.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/DynamicComponent.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/GraphicsImage.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/LazySection.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/Loader.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/Rating.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/ScrollFab.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/SectionHero.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/Simulator.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/SimulatorTypeset.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/SvgIcon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/ThemeProvider.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/Typeset.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/cards/GraphicsCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/cards/IconCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/cards/PreviewCard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/cards/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/cards/profile-card/ProfileGroup.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/cards/profile-card/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/contact-us/ContactUsForm1.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/contact-us/ContactUsForm2.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/contact-us/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/faq/FaqDetails.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/footer/Copyright.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/footer/FollowUS.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/footer/JoinUS.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/footer/Sitemap.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/footer/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/logo/LogoFab.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/logo/LogoIcon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/logo/LogoMain.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/logo/LogoWatermark.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/logo/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/navbar/MenuPopper.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/navbar/NavItems.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/navbar/NavPrimaryButton.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/navbar/NavSecondaryButton.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/components/navbar/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/config.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/contexts/ConfigContext.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/data/countries.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/data/landings.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/enum.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/hooks/useConfig.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/hooks/useDataThemeMode.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/hooks/useFocusWithin.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/hooks/useLocalStorage.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/icons/Circle.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/icons/CloseEye.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/icons/Curve.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/icons/Curve2.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/icons/Line.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/icons/OpenEye.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/icons/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/Circles.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/ComingSoon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Devto.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Dribbble.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Envato.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Financely.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Marketly.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Mui.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Realtor.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Reddit.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/clientele/Techlify.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Arrow.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Background.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/DrawnArrow.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Graphic1.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/PageLoader.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern1.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern2.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern3.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern4.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern5.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern6.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern7.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern8.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Pattern9.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/RoundFab.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Star.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/StarFab.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/graphics/Wave.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/maintenance/BackShortly.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/maintenance/Error404.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/maintenance/Error500.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/maintenance/Error500Server.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/maintenance/Maintenance.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/testimonial/GoogleImg.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/images/testimonial/Twitch.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/metadata.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/path.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/styles/scrollbar.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/styles/yet-another-react-lightbox.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/utils/CommonFocusStyle.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/utils/GetImagePath.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/utils/Loader.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/utils/constant.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/utils/getBackgroundDots.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/utils/validationSchema.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/about.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/blog.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/cliental.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/cta.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/faq.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/feature.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/hero.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/integration.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/nav-items.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/navbar.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/data/testimonial.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/Accordion.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/AccordionSummary.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/Button.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/Chip.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/Container.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/IconButton.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/Link.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/OutlinedInput.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/Switch.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/Tab.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/overrides/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/palette.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/ai/theme/typography.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/common-data.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/contact.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/benefit.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/clientele.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/contact-us.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/cta.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/faq.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/feature.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/hero.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/index.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/integration.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/navbar.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/other.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/pricing.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/small-hero.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/team.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/data/testimonial.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/privacy-policy.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/landings/default/terms-condition.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/About.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Benefit.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Blog.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Clientele.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Color.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/ComingSoon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/ContactUs.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Cookie.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Cta.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/EarlyAccess.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Error404.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Error500.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Faq.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Feature.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Footer.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/ForgotPassword.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Gallery.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Hero.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Icon.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Integration.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Login.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/MegaMenu.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Navbar.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/NewPassword.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/OnBoard.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Other.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/OtpVerification.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Pricing.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/PrivacyPolicy.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/ProPage.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Process.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Register.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/SmallHero.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Team.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/TermsCondition.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Testimonial.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/TopOffer.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/Typography.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/UnderMaintenance.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/index.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/views/sections/layout.jsx", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/.gitignore", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/.prettierignore", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/.prettierrc", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/README.md", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/gulpfile.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/package-lock.json", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/package.json", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/postcss.config.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/clash-display/ClashDisplay-Variable.ttf", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/clash-display/ClashDisplay.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/general-sana/GeneralSans-Variable.ttf", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/general-sana/GeneralSans.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/inter/Inter-italic.var.woff2", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/inter/Inter-roman.var.woff2", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/inter/inter.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/satoshi/Sato<PERSON>-Variable.ttf", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/satoshi/Satoshi.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/tabler/tabler-icons.woff2", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/tabler-icons.min.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/uncut-sans/Uncut-Sans-VF.ttf", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/uncut-sans/Uncut-Sans.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog10.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog11.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog12.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog13.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog14.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog15.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog16.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog17.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog18.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog19.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog2.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog3.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog4.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog5.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog6.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog7.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog8.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog9.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/favicon.ico", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/free-lock.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/background1.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/background2.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/background3.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/desktop1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/desktop1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/desktop2-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/desktop2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics10-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics10-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics11-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics11-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics12-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics12-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics13-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics13-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics14-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics14-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics15-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics15-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics16-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics16-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics17-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics17-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics18-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics18-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics2-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics20-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics20-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics21-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics21-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics22-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics22-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics23-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics23-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics24-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics24-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics25-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics25-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics3-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics3-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics4-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics4-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics5-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics5-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics6-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics6-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics7-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics7-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics8-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics8-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics9-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics9-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics1-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics1-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics10-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics10-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics11-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics11-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics2-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics2-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics3-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics3-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics4-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics4-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics5-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics5-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics6-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics6-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics7-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics7-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics8-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics8-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics9-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/hero/graphics9-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/rocket.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/top-offer-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/top-offer-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/video-thumbnail-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/video-thumbnail-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/background1.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/background2.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/desktop1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/desktop1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics10-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics10-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics11-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics11-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics12-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics12-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics13-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics13-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics14-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics14-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics15-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics15-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics16-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics16-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics17-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics17-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics18-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics18-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics2-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics3-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics3-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics4-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics4-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics5-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics5-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics6-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics6-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics7-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics7-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics8-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics8-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics9-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/graphics9-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics1-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics1-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics10-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics10-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics11-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics11-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics2-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics2-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics3-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics3-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics4-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics4-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics5-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics5-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics6-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics6-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics7-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics7-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics8-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics8-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics9-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/hero/graphics9-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/rocket.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/top-offer-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crm/top-offer-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/banner.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/crypto-dashboard.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/crypto_exchange_dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/crypto_exchange_light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/cryptoimg.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/cta_banner.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/dashboard1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/dashboard1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/admin-dashboard-2-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/admin-dashboard-2.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/admin-dashboard-3.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/admin-dashboard-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/admin-dashboard.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/feature23-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/feature23-light.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/saasable-figma-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/default/saasable-figma.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/admin-dashboard-dark.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/desktop1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/desktop1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics2-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics3-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics3-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics4-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics4-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics5-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics5-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics6-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics6-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics7-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hosting/graphics7-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/desktop1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/desktop1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/graphics1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/graphics1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/graphics2-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/graphics2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/graphics3-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/hrm/graphics3-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/plugin/desktop1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/plugin/desktop1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/plugin/graphics1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/plugin/graphics1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/plugin/graphics2-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/plugin/graphics2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/background.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/desktop1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/desktop1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/graphics1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/graphics1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/graphics2-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/graphics2-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/graphics3-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/pms/graphics3-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/hero/background.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/asana.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/dropbox.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/google-drive.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/intercom.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/jira.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/loom.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/mail-chimp.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/master-card.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/messenger-facebook.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/notion.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/paypal.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/skype.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/slack.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/trip-advicer.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/integration/zoom.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/logo-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/logo-white.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/ai-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/ai-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/crm-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/crm-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/crypto-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/crypto-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/hosting-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/hosting-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/hrm-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/hrm-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/lms-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/lms-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/plugin-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/plugin-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/pms-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/mega-menu/pms-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/404-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/404-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/500-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/500-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/benefits-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/benefits-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/blog-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/blog-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/clientele-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/clientele-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/color-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/color-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/coming-soon-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/coming-soon-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/contact-us-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/contact-us-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/content-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/content-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/cookies-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/cookies-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/cta-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/cta-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/early-access-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/early-access-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/faqs-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/faqs-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/feature-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/feature-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/footer-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/footer-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/forgot-pass-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/forgot-pass-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/gallery-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/gallery-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/hero-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/hero-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/icon-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/icon-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/integration-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/integration-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/maintenance-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/maintenance-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/mega-menu-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/mega-menu-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/navbar-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/navbar-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/new-pass-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/new-pass-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/onboard-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/onboard-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/other-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/other-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/otp-gen-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/otp-gen-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/pricing-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/pricing-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/privacy-policy-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/privacy-policy-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/process-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/process-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/sign-in-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/sign-in-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/sign-up-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/sign-up-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/small-hero-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/small-hero-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/team-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/team-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/testimonial-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/testimonial-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/top-offer-bar-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/top-offer-bar-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/typography-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/presentation/typography-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/celebration.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/css.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/figma.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/gulp.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/html.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/javascript.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/jwt.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/m3.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/material-ui.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/next-js.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/pattern1-dark.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/pattern1-light.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/react.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/scss.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/tailwindcss.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/shared/typescript.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-1.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-2.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-3.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-4.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-5.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-6.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-7.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/profile-8.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/team-member-1.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/team/team-member-2.jpg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/testimonial/avatar-bg1.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/testimonial/avatar-bg2.svg", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar1.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar10.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar11.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar12.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar13.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar14.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar2.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar3.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar4.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar5.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar6.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar7.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar8.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/user/avatar9.png", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/icon/custom-font.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/script.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/theme.js", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/accordion.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/breadcrumb.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/buttons.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/dropdown.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/forms.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/general.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/keyframes.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/navbar.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/presets.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/partial/typography.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/style.scss", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/scss/themes/tailwind.css", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/about/about-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/about/about-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/benefit/benefit-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/benefit/benefit-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/benefit/benefit-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/benefit/benefit-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/about.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/benefit.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/block.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/blog.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/clientele.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/color.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/coming-soon.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/contact-us.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/cta.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/error404.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/error500.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/faq.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/feature.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/footer.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/hero.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/icon.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/integration.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/navbar.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/other.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/pricing.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/privacy-policy.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/process.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/small-hero.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/team.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/testimonial.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/typography.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/under-maintenance.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/blog/blog-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/blog/blog-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/blog/blog-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/blog/blog-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/clientele/clientele-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/clientele/clientele-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/clientele/clientele-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/clientele/clientele-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/clientele/clientele-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/coming-soon/coming-soon-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/contact-us/contact-us-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-12.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/cta/cta-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/error404/error404-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/error500/error500-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/faq/faq-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/faq/faq-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/faq/faq-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/faq/faq-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/faq/faq-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-11.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-12.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-13.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-16.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-18.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-20.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-21.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-22.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-23.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/feature/feature-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/footer/footer-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/footer/footer-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/footer/footer-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/footer/footer-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/footer/footer-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/footer/footer-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-11.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-15.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-17.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/hero/hero-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/Circles.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/LogoFab.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/arrow.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/block-bg.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Devto.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Dribbble.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Envato.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Financely.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Marketly.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Mui.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Realtor.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Reddit.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/clientele/Techlify.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/drawnarrow.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/icon/Curve.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/icon/Curve2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/icon/Line.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/img-starfab.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/logo-icon.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/logo-main.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/logo-watermark.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/maintenance/BackShortly.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/maintenance/ComingSoon.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/maintenance/Error404.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/maintenance/Error500.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/maintenance/Error500Server.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/maintenance/Maintenance.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/star.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/testimonial/GoogleImg.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/testimonial/Twitch.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/images/wave.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/integration/integration-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/integration/integration-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/integration/integration-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/integration/integration-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/integration/integration-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/integration/integration-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-breadcrumb.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-header.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-show.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-title.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/footer-js.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/head-css.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/head-landing-page-meta.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/head-page-meta.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/loader.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/scroll-top.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/navbar/navbar-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/navbar/navbar-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/navbar/navbar-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/navbar/navbar-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/navbar/navbar-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/navbar/navbar-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/other/other-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/other/other-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/other/other-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/about.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/clientele-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/contact-us-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/feature-18.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/feature-23.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/feature-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/footer-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/privacy-policy.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/small-hero-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/partials/team-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pages/privacy-policy.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/about/about-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/about/about-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/benefit/benefit-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/benefit/benefit-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/benefit/benefit-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/benefit/benefit-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/blog/blog-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/blog/blog-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/blog/blog-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/blog/blog-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/clientele/clientele-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/clientele/clientele-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/clientele/clientele-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/clientele/clientele-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/clientele/clientele-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/coming-soon/coming-soon-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/contact-us/contact-us-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-12.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/cta/cta-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/error404/error404-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/error500/error500-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-11.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-12.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-13.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-16.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-18.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-20.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-21.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-22.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-23.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/feature/feature-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/footer/footer-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/footer/footer-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/footer/footer-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/footer/footer-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/footer/footer-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/footer/footer-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-11.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-15.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-17.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/hero/hero-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/integration/integration-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/integration/integration-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/integration/integration-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/integration/integration-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/integration/integration-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/integration/integration-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/navbar/navbar-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/navbar/navbar-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/navbar/navbar-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/navbar/navbar-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/navbar/navbar-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/navbar/navbar-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/other/other-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/other/other-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/other/other-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/pricing/pricing-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/pricing/pricing-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/pricing/pricing-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/pricing/pricing-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/pricing/pricing-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/pricing/pricing-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/pro-page/pro-page.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/process/process-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/process/process-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/process/process-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/process/process-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/small-hero/small-hero-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/small-hero/small-hero-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/small-hero/small-hero-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/team/team-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/testimonial/testimonial-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/testimonial/testimonial-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/testimonial/testimonial-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/testimonial/testimonial-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/testimonial/testimonial-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/testimonial/testimonial-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/testimonial/testimonial-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/under-maintenance/under-maintenance-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pricing/pricing-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pricing/pricing-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pricing/pricing-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pricing/pricing-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pricing/pricing-8.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/pricing/pricing-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/process/process-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/process/process-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/process/process-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/process/process-7.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/small-hero/small-hero-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/small-hero/small-hero-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/small-hero/small-hero-4.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/team/team-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/testimonial/testimonial-1.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/testimonial/testimonial-10.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/testimonial/testimonial-2.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/testimonial/testimonial-3.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/testimonial/testimonial-5.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/testimonial/testimonial-6.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/testimonial/testimonial-9.html", "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/under-maintenance/under-maintenance-1.html"], "skipped": [{"path": "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/graphics/hosting/dashboard-dark.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/images/graphics/hosting/dashboard-light.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/admin/public/assets/videos/test.mp4", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/images/graphics/ai/graphics19-light.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/svg/tabler-sprite-outline.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/public/assets/videos/test.mp4", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/about/about-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/about/about-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/css/plugins/tiny-slider.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/css/style.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/clash-display/ClashDisplay-Variable.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/clash-display/ClashDisplay.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/general-sana/GeneralSans-Variable.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/general-sana/GeneralSans.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/inter/Inter-italic.var.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/inter/Inter-roman.var.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/inter/inter.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/satoshi/Sato<PERSON>-Variable.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/satoshi/Satoshi.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/tabler/tabler-icons.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/tabler/tabler-icons.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/tabler/tabler-icons.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/tabler-icons.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/uncut-sans/Uncut-Sans-VF.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/fonts/uncut-sans/Uncut-Sans.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog10.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog11.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog12.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog13.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog14.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog15.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog16.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog17.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog18.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog19.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog2.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog3.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog4.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog5.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog6.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog7.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog8.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/blog/blog9.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/favicon.ico", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/free-lock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/background1.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/background2.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/background3.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/desktop1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/desktop1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/desktop2-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/desktop2-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics10-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics10-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics11-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics11-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics12-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics12-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics13-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics13-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics14-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics14-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics15-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics15-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics16-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics16-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics17-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics17-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics18-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics18-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics19-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics19-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics2-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics2-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics20-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics20-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics21-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics21-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics22-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics22-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics23-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics23-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics24-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics24-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics25-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics25-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics3-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics3-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics4-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics4-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics5-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics5-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics6-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics6-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics7-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics7-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics8-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics8-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics9-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/graphics9-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics1-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics1-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics10-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics10-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics11-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics11-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics2-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics2-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics3-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics3-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics4-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics4-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics5-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics5-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics6-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics6-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics7-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics7-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics8-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics8-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics9-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/hero/graphics9-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/rocket.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/top-offer-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/top-offer-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/video-thumbnail-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/ai/video-thumbnail-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/background1.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/background2.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/desktop1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/desktop1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics10-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics10-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics11-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics11-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics12-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics12-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics13-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics13-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics14-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics14-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics15-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics15-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics16-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics16-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics17-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics17-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics18-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics18-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics2-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics2-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics3-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics3-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics4-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics4-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics5-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics5-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics6-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics6-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics7-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics7-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics8-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics8-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics9-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/graphics9-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics1-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics1-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics10-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics10-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics11-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics11-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics2-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics2-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics3-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics3-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics4-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics4-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics5-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics5-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics6-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics6-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics7-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics7-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics8-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics8-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics9-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/hero/graphics9-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/rocket.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/top-offer-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crm/top-offer-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/banner.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/crypto-dashboard.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/crypto_exchange_dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/crypto_exchange_light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/cryptoimg.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/cta_banner.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/dashboard1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/dashboard1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/graphic1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/crypto/graphic1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/admin-dashboard-2-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/admin-dashboard-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/admin-dashboard-3.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/admin-dashboard-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/admin-dashboard.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/feature23-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/feature23-light.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/saasable-figma-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/default/saasable-figma.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/admin-dashboard-dark.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/desktop1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/desktop1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics2-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics2-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics3-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics3-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics4-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics4-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics5-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics5-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics6-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics6-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics7-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hosting/graphics7-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/desktop1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/desktop1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/graphics1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/graphics1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/graphics2-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/graphics2-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/graphics3-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/hrm/graphics3-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/plugin/desktop1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/plugin/desktop1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/plugin/graphics1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/plugin/graphics1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/plugin/graphics2-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/plugin/graphics2-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/background.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/desktop1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/desktop1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/graphics1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/graphics1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/graphics2-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/graphics2-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/graphics3-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/graphics/pms/graphics3-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/hero/background.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/asana.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/dropbox.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/google-drive.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/intercom.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/jira.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/loom.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/mail-chimp.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/master-card.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/messenger-facebook.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/notion.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/paypal.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/skype.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/slack.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/trip-advicer.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/integration/zoom.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/logo-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/logo-white.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/ai-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/ai-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/crm-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/crm-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/crypto-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/crypto-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/hosting-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/hosting-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/hrm-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/hrm-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/lms-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/lms-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/plugin-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/plugin-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/pms-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/mega-menu/pms-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/404-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/404-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/500-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/500-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/benefits-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/benefits-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/blog-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/blog-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/clientele-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/clientele-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/color-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/color-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/coming-soon-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/coming-soon-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/contact-us-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/contact-us-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/content-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/content-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/cookies-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/cookies-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/cta-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/cta-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/early-access-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/early-access-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/faqs-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/faqs-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/feature-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/feature-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/footer-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/footer-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/forgot-pass-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/forgot-pass-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/gallery-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/gallery-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/hero-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/hero-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/icon-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/icon-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/integration-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/integration-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/maintenance-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/maintenance-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/mega-menu-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/mega-menu-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/navbar-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/navbar-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/new-pass-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/new-pass-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/onboard-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/onboard-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/other-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/other-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/otp-gen-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/otp-gen-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/pricing-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/pricing-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/privacy-policy-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/privacy-policy-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/process-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/process-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/sign-in-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/sign-in-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/sign-up-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/sign-up-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/small-hero-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/small-hero-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/team-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/team-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/testimonial-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/testimonial-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/top-offer-bar-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/top-offer-bar-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/typography-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/presentation/typography-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/celebration.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/css.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/figma.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/gulp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/html.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/javascript.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/jwt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/m3.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/material-ui.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/next-js.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/pattern1-dark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/pattern1-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/react.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/scss.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/tailwindcss.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/shared/typescript.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-3.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-4.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-5.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-6.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-7.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/profile-8.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/team-member-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/team/team-member-2.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/testimonial/avatar-bg1.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/testimonial/avatar-bg2.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar10.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar11.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar12.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar13.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar14.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar3.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar4.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar5.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar6.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar7.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar8.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/images/user/avatar9.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/component.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/icon/custom-font.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/plugins/motion.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/plugins/popper.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/plugins/tiny-slider.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/plugins/vanilla-marquee.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/script.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/assets/js/theme.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/benefit/benefit-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/benefit/benefit-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/benefit/benefit-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/benefit/benefit-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/about.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/benefit.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/block.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/blog.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/clientele.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/color.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/coming-soon.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/contact-us.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/cta.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/error404.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/error500.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/faq.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/feature.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/footer.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/hero.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/icon.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/integration.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/navbar.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/other.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/pricing.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/privacy-policy.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/process.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/small-hero.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/team.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/testimonial.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/typography.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/block/under-maintenance.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/blog/blog-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/blog/blog-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/blog/blog-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/blog/blog-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/clientele/clientele-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/clientele/clientele-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/clientele/clientele-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/clientele/clientele-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/clientele/clientele-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/coming-soon/coming-soon-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/contact-us/contact-us-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-12.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/cta/cta-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/error404/error404-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/error500/error500-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/faq/faq-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/faq/faq-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/faq/faq-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/faq/faq-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/faq/faq-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-11.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-12.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-13.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-16.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-18.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-20.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-21.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-22.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-23.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/feature/feature-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/footer/footer-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/footer/footer-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/footer/footer-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/footer/footer-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/footer/footer-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/footer/footer-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-11.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-15.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-17.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/hero/hero-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/Circles.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/LogoFab.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/arrow.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/block-bg.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Devto.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Dribbble.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Envato.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Financely.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Marketly.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Mui.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Realtor.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Reddit.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/clientele/Techlify.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/drawnarrow.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/icon/Curve.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/icon/Curve2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/icon/Line.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/img-starfab.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/logo-icon.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/logo-main.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/logo-watermark.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/maintenance/BackShortly.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/maintenance/ComingSoon.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/maintenance/Error404.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/maintenance/Error500.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/maintenance/Error500Server.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/maintenance/Maintenance.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/star.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/testimonial/GoogleImg.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/testimonial/Twitch.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/images/wave.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/integration/integration-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/integration/integration-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/integration/integration-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/integration/integration-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/integration/integration-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/integration/integration-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/component/component-breadcrumb.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/component/component-header.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/component/component-show.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/component/component-title.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/footer-js.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/head-css.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/head-landing-page-meta.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/head-page-meta.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/loader.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/layouts/scroll-top.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/navbar/navbar-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/navbar/navbar-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/navbar/navbar-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/navbar/navbar-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/navbar/navbar-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/navbar/navbar-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/other/other-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/other/other-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/other/other-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/about.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/clientele-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/contact-us-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/feature-18.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/feature-23.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/feature-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/footer-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/privacy-policy.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/small-hero-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/partials/team-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pages/privacy-policy.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/about/about-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/about/about-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/benefit/benefit-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/benefit/benefit-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/benefit/benefit-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/benefit/benefit-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/blog/blog-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/blog/blog-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/blog/blog-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/blog/blog-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/clientele/clientele-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/clientele/clientele-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/clientele/clientele-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/clientele/clientele-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/clientele/clientele-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/coming-soon/coming-soon-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/contact-us/contact-us-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-12.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/cta/cta-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/error404/error404-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/error500/error500-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/faq/faq-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/faq/faq-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/faq/faq-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/faq/faq-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/faq/faq-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-11.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-12.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-13.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-16.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-18.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-20.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-21.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-22.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-23.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/feature/feature-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/footer/footer-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/footer/footer-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/footer/footer-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/footer/footer-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/footer/footer-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/footer/footer-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-11.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-15.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-17.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/hero/hero-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/integration/integration-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/integration/integration-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/integration/integration-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/integration/integration-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/integration/integration-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/integration/integration-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/navbar/navbar-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/navbar/navbar-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/navbar/navbar-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/navbar/navbar-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/navbar/navbar-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/navbar/navbar-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/other/other-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/other/other-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/other/other-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/pricing/pricing-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/pricing/pricing-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/pricing/pricing-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/pricing/pricing-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/pricing/pricing-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/pricing/pricing-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/pro-page/pro-page.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/process/process-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/process/process-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/process/process-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/process/process-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/small-hero/small-hero-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/small-hero/small-hero-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/small-hero/small-hero-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/team/team-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/testimonial/testimonial-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/testimonial/testimonial-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/testimonial/testimonial-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/testimonial/testimonial-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/testimonial/testimonial-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/testimonial/testimonial-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/testimonial/testimonial-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/partials/under-maintenance/under-maintenance-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pricing/pricing-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pricing/pricing-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pricing/pricing-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pricing/pricing-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pricing/pricing-8.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/pricing/pricing-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/process/process-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/process/process-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/process/process-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/process/process-7.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/small-hero/small-hero-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/small-hero/small-hero-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/small-hero/small-hero-4.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/team/team-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/testimonial/testimonial-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/testimonial/testimonial-10.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/testimonial/testimonial-2.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/testimonial/testimonial-3.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/testimonial/testimonial-5.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/testimonial/testimonial-6.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/testimonial/testimonial-9.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/dist/under-maintenance/under-maintenance-1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/tabler/tabler-icons.ttf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/fonts/tabler/tabler-icons.woff", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/blog/blog1.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics19-dark.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/ai/graphics19-light.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/graphic1-dark.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/images/graphics/crypto/graphic1-light.svg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/layouts/component/component-header.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/partials/faq/faq-6.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.4454269409179688, "profiling_times": {"config_time": 6.959717273712158, "core_time": 17.81118893623352, "ignores_time": 0.002007722854614258, "total_time": 24.773841619491577}, "parsing_time": {"total_time": 12.71164870262146, "per_file_time": {"mean": 0.025942140209431565, "std_dev": 0.004887062096797711}, "very_slow_stats": {"time_ratio": 0.27628240320790637, "count_ratio": 0.014285714285714285}, "very_slow_files": [{"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/src/data/countries.js", "ftime": 0.3385171890258789}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/block/block.html", "ftime": 0.3641188144683838}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/package-lock.json", "ftime": 0.4109020233154297}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "ftime": 0.5282719135284424}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/illustration/ReadingSideDoodle.jsx", "ftime": 0.5535659790039062}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/package-lock.json", "ftime": 0.6357100009918213}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/html/index.html", "ftime": 0.6809189319610596}]}, "scanning_time": {"total_time": 72.0548050403595, "per_file_time": {"mean": 0.021670618057250974, "std_dev": 0.005951851227472058}, "very_slow_stats": {"time_ratio": 0.025024436109251456, "count_ratio": 0.0003007518796992481}, "very_slow_files": [{"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "ftime": 1.803130865097046}]}, "matching_time": {"total_time": 10.491914510726929, "per_file_and_rule_time": {"mean": 0.007156831180577717, "std_dev": 0.00040574868766630134}, "very_slow_stats": {"time_ratio": 0.21213629536821324, "count_ratio": 0.009549795361527967}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/src/layouts/AdminLayout/Header/HeaderContent/Notification.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.11057305335998535}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/src/data/countries.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.11126089096069336}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/PrivacyPolicy.jsx", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.11725997924804688}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/gulpfile.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.12034487724304199}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/src/images/illustration/ReadingSideDoodle.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.1284160614013672}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/package-lock.json", "rule_id": "json.aws.security.public-s3-policy-statement.public-s3-policy-statement", "time": 0.15249204635620117}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/pricing/Pricing9.jsx", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.20853900909423828}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/admin/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.2623918056488037}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/gulpfile.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.2811570167541504}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.3046450614929199}]}, "tainting_time": {"total_time": 3.131476402282715, "per_def_and_rule_time": {"mean": 0.004531803765966302, "std_dev": 0.00011463336838219678}, "very_slow_stats": {"time_ratio": 0.14470864367699876, "count_ratio": 0.00723589001447178}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.054759979248046875}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.05966901779174805}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/react/src/blocks/pricing/Pricing9.jsx", "fline": 34, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.06439781188964844}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.12748193740844727}, {"fpath": "downloaded_repos/phoenixcoded_saasable-ui/uikit/tailwind/src/assets/js/component.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.14684295654296875}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1125280704}, "engine_requested": "OSS", "skipped_rules": []}