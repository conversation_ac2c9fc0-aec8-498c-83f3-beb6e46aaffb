{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/Dockerfile", "start": {"line": 20, "col": 1, "offset": 382}, "end": {"line": 20, "col": 20, "offset": 401}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/entrypoint\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "bash.curl.security.curl-pipe-bash.curl-pipe-bash", "path": "downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/entrypoint", "start": {"line": 8, "col": 5, "offset": 93}, "end": {"line": 8, "col": 85, "offset": 173}, "extra": {"message": "Data is being piped into `bash` from a `curl` command. An attacker with control of the server in the `curl` command could inject malicious code into the pipe, resulting in a system compromise. Avoid piping untrusted data into `bash` or any other shell if you can. If you must do this, consider checking the SHA sum of the content returned by the server to verify its integrity.", "metadata": {"owasp": ["A03:2021 - Injection"], "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "category": "security", "technology": ["bash", "curl"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/bash.curl.security.curl-pipe-bash.curl-pipe-bash", "shortlink": "https://sg.run/KXz6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/et-nik_gameap/.dev/docker/php/entrypoint", "start": {"line": 15, "col": 9, "offset": 390}, "end": {"line": 15, "col": 42, "offset": 423}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/et-nik_gameap/.dev/docker/php/entrypoint", "start": {"line": 79, "col": 1, "offset": 2406}, "end": {"line": 79, "col": 67, "offset": 2472}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/et-nik_gameap/app/Repositories/ClientCertificateRepository.php", "start": {"line": 121, "col": 13, "offset": 3681}, "end": {"line": 121, "col": 26, "offset": 3694}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/et-nik_gameap/app/Repositories/ClientCertificateRepository.php", "start": {"line": 130, "col": 13, "offset": 3971}, "end": {"line": 130, "col": 26, "offset": 3984}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/et-nik_gameap/app/Repositories/NodeRepository.php", "start": {"line": 131, "col": 17, "offset": 3889}, "end": {"line": 131, "col": 41, "offset": 3913}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/et-nik_gameap/database/seeds/UsersTableSeeder.php", "start": {"line": 20, "col": 33, "offset": 386}, "end": {"line": 20, "col": 93, "offset": 446}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/et-nik_gameap/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 8, "offset": 30}, "extra": {"message": "Service 'mysql' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/et-nik_gameap/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 8, "offset": 30}, "extra": {"message": "Service 'mysql' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/et-nik_gameap/docker-compose.yml", "start": {"line": 15, "col": 3, "offset": 318}, "end": {"line": 15, "col": 8, "offset": 323}, "extra": {"message": "Service 'nginx' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/et-nik_gameap/docker-compose.yml", "start": {"line": 15, "col": 3, "offset": 318}, "end": {"line": 15, "col": 8, "offset": 323}, "extra": {"message": "Service 'nginx' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/components/blocks/CreateNodeModal.vue", "start": {"line": 23, "col": 7, "offset": 693}, "end": {"line": 24, "col": 19, "offset": 748}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/components/blocks/CreateNodeModal.vue", "start": {"line": 40, "col": 7, "offset": 1246}, "end": {"line": 41, "col": 19, "offset": 1301}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/parts/form.js", "start": {"line": 4, "col": 13, "offset": 189}, "end": {"line": 4, "col": 81, "offset": 257}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersCreate.vue", "start": {"line": 63, "col": 19, "offset": 1901}, "end": {"line": 63, "col": 33, "offset": 1915}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersEdit.vue", "start": {"line": 84, "col": 17, "offset": 2739}, "end": {"line": 84, "col": 31, "offset": 2753}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue", "start": {"line": 150, "col": 23, "offset": 5259}, "end": {"line": 150, "col": 37, "offset": 5273}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue", "start": {"line": 160, "col": 23, "offset": 5658}, "end": {"line": 160, "col": 37, "offset": 5672}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue", "start": {"line": 170, "col": 23, "offset": 6065}, "end": {"line": 170, "col": 37, "offset": 6079}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue", "start": {"line": 187, "col": 23, "offset": 6740}, "end": {"line": 187, "col": 37, "offset": 6754}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue", "start": {"line": 197, "col": 23, "offset": 7152}, "end": {"line": 197, "col": 37, "offset": 7166}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue", "start": {"line": 207, "col": 23, "offset": 7563}, "end": {"line": 207, "col": 37, "offset": 7577}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/et-nik_gameap/server.php", "start": {"line": 17, "col": 33, "offset": 473}, "end": {"line": 17, "col": 55, "offset": 495}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/et-nik_gameap/.dev/docker/php/Dockerfile", "start": {"line": 17, "col": 1, "offset": 0}, "end": {"line": 23, "col": 20, "offset": 97}}]], "message": "Syntax error at line downloaded_repos/et-nik_gameap/.dev/docker/php/Dockerfile:17:\n `USER \"${USER_ID}:${GROUP_ID}\"\n\nWORKDIR /var/www\n\nCOPY entrypoint /entrypoint\n\nCMD [\"/entrypoint\"]` was unexpected", "path": "downloaded_repos/et-nik_gameap/.dev/docker/php/Dockerfile", "spans": [{"file": "downloaded_repos/et-nik_gameap/.dev/docker/php/Dockerfile", "start": {"line": 17, "col": 1, "offset": 0}, "end": {"line": 23, "col": 20, "offset": 97}}]}], "paths": {"scanned": ["downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/Dockerfile", "downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/entrypoint", "downloaded_repos/et-nik_gameap/.dev/docker/php/Dockerfile", "downloaded_repos/et-nik_gameap/.dev/docker/php/entrypoint", "downloaded_repos/et-nik_gameap/.dev/docker/php/zzz-config.ini", "downloaded_repos/et-nik_gameap/.dev/nginx-configs/default.conf", "downloaded_repos/et-nik_gameap/.drone.yml", "downloaded_repos/et-nik_gameap/.env.example", "downloaded_repos/et-nik_gameap/.env.travis", "downloaded_repos/et-nik_gameap/.gitattributes", "downloaded_repos/et-nik_gameap/.github/FUNDING.yml", "downloaded_repos/et-nik_gameap/.gitignore", "downloaded_repos/et-nik_gameap/.htaccess", "downloaded_repos/et-nik_gameap/.scrutinizer.yml", "downloaded_repos/et-nik_gameap/.styleci.yml", "downloaded_repos/et-nik_gameap/.travis.yml", "downloaded_repos/et-nik_gameap/README.md", "downloaded_repos/et-nik_gameap/app/Adapters/Archiver.php", "downloaded_repos/et-nik_gameap/app/Console/Commands/ChangePassword.php", "downloaded_repos/et-nik_gameap/app/Console/Commands/UpgradeGames.php", "downloaded_repos/et-nik_gameap/app/Console/Kernel.php", "downloaded_repos/et-nik_gameap/app/Exceptions/GameapException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/GdaemonAPI/InvalidApiKeyException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/GdaemonAPI/InvalidSetupTokenExeption.php", "downloaded_repos/et-nik_gameap/app/Exceptions/GdaemonAPI/InvalidTokenExeption.php", "downloaded_repos/et-nik_gameap/app/Exceptions/GdaemonAPI/ValidationException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Handler.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Http/HttpException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/InvalidArgumentValueException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/NotFoundException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Repositories/GdaemonTaskRepository/EmptyServerStartCommandException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Repositories/GdaemonTaskRepository/GdaemonTaskRepositoryException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Repositories/GdaemonTaskRepository/InvalidServerStartCommandException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Repositories/InvalidCertificateException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Repositories/RecordExistExceptions.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Repositories/RepositoryException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Repositories/RepositoryValidationException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Services/EmptyCommandException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Services/InvalidCommandException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Services/ResponseException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/Services/ServerInactiveException.php", "downloaded_repos/et-nik_gameap/app/Exceptions/ValidationException.php", "downloaded_repos/et-nik_gameap/app/Helpers/DateHelper.php", "downloaded_repos/et-nik_gameap/app/Helpers/OsHelper.php", "downloaded_repos/et-nik_gameap/app/Helpers/PermissionHelper.php", "downloaded_repos/et-nik_gameap/app/Helpers/ServerPermissionHelper.php", "downloaded_repos/et-nik_gameap/app/Helpers/UserHelper.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/AuthController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/ClientCertificatesController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/DedicatedServersController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/GameModsController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/GamesController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/GdaemonTasksController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/HealthzController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/ProfileController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/ServersController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/ServersRconController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/ServersSettingsController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/ServersTasksController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/TokensController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/API/UsersController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/Auth/ForgotPasswordController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/Auth/LoginController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/Auth/RegisterController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/Auth/ResetPasswordController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/AuthController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/Controller.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/EmptyController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/AuthController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/Controller.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/DedicatedServersController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/DsStatsController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/ServersController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/ServersTasksController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/SetupController.php", "downloaded_repos/et-nik_gameap/app/Http/Controllers/GdaemonAPI/TasksController.php", "downloaded_repos/et-nik_gameap/app/Http/Kernel.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/AdminMiddleware.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/EncryptCookies.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/GdaemonApiAuth.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/PreferLanguageMiddleware.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/RedirectIfAuthenticated.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/TrimStrings.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/TrustProxies.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/VerifyCsrfToken.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/VerifyGdaemonApiToken.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/VerifyGdaemonCreateToken.php", "downloaded_repos/et-nik_gameap/app/Http/Middleware/VerifyGdaemonSetupToken.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/Admin/SaveUserServerPermissionsRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/Admin/StoreNodeRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/Admin/UpdateNodeRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/GeneratePersonalAccessTokenRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/Rcon/BanRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/Rcon/CommandRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/Rcon/KickRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/SaveServerRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/ServerConsoleCommandRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/ServerSettingSaveRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/ServerTaskCreateRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/ServerTaskUpdateRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/API/UpdateProfileRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/CreateServerRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/DedicatedServerRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/GameModRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/GameRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/ServerDestroyRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/ServerUpdateRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/UserCreateRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Admin/UserUpdateRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/ClientCertificatesRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/CreateGdaemonTaskRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/GdaemonAPI/DedicatedServerRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/GdaemonAPI/DsStatsRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/GdaemonAPI/JsonServerBulkRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/GdaemonAPI/ServerRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/GdaemonAPI/ServerTaskFailRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/GdaemonAPI/ServerTaskUpdateRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/GeneratePersonalAccessTokenRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/JsonRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Modules/InstallModuleRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Modules/ModuleActionRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/ProfileChangePasswordRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/Request.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/SendBugRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/ServerVarsRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/ServersSettingsRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Requests/UpdateGdaemonTaskRequest.php", "downloaded_repos/et-nik_gameap/app/Http/Responses/GdaemonAPI/GameModResponse.php", "downloaded_repos/et-nik_gameap/app/Http/Responses/GdaemonAPI/GameResponse.php", "downloaded_repos/et-nik_gameap/app/Http/Responses/GdaemonAPI/ServerResponse.php", "downloaded_repos/et-nik_gameap/app/Models/ClientCertificate.php", "downloaded_repos/et-nik_gameap/app/Models/DedicatedServer.php", "downloaded_repos/et-nik_gameap/app/Models/DsStats.php", "downloaded_repos/et-nik_gameap/app/Models/DsUser.php", "downloaded_repos/et-nik_gameap/app/Models/Game.php", "downloaded_repos/et-nik_gameap/app/Models/GameMod.php", "downloaded_repos/et-nik_gameap/app/Models/GdaemonTask.php", "downloaded_repos/et-nik_gameap/app/Models/Modules/LaravelModule.php", "downloaded_repos/et-nik_gameap/app/Models/Modules/MarketplaceModule.php", "downloaded_repos/et-nik_gameap/app/Models/PersonalAccessToken.php", "downloaded_repos/et-nik_gameap/app/Models/Server.php", "downloaded_repos/et-nik_gameap/app/Models/ServerSetting.php", "downloaded_repos/et-nik_gameap/app/Models/ServerStats.php", "downloaded_repos/et-nik_gameap/app/Models/ServerTask.php", "downloaded_repos/et-nik_gameap/app/Models/ServerTaskFail.php", "downloaded_repos/et-nik_gameap/app/Models/User.php", "downloaded_repos/et-nik_gameap/app/Policies/GdaemonApiPolicy.php", "downloaded_repos/et-nik_gameap/app/Policies/ServerPolicy.php", "downloaded_repos/et-nik_gameap/app/Providers/AppServiceProvider.php", "downloaded_repos/et-nik_gameap/app/Providers/AuthServiceProvider.php", "downloaded_repos/et-nik_gameap/app/Providers/BroadcastServiceProvider.php", "downloaded_repos/et-nik_gameap/app/Providers/EventServiceProvider.php", "downloaded_repos/et-nik_gameap/app/Providers/FormServiceProvider.php", "downloaded_repos/et-nik_gameap/app/Providers/GdaemonFilesServiceProvider.php", "downloaded_repos/et-nik_gameap/app/Providers/RouteServiceProvider.php", "downloaded_repos/et-nik_gameap/app/Providers/SerializerProvider.php", "downloaded_repos/et-nik_gameap/app/Repositories/ClientCertificateRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/GameModRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/GameRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/GdaemonTaskRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/Modules/LaravelModulesRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/Modules/MarketplaceModulesRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/NodeRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/Repository.php", "downloaded_repos/et-nik_gameap/app/Repositories/ServerRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/ServerSettingsRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/ServersTasksRepository.php", "downloaded_repos/et-nik_gameap/app/Repositories/UserRepository.php", "downloaded_repos/et-nik_gameap/app/Services/Daemon/CertificateService.php", "downloaded_repos/et-nik_gameap/app/Services/Daemon/DebugService.php", "downloaded_repos/et-nik_gameap/app/Services/GdaemonCommandsService.php", "downloaded_repos/et-nik_gameap/app/Services/GlobalApi.php", "downloaded_repos/et-nik_gameap/app/Services/InfoService.php", "downloaded_repos/et-nik_gameap/app/Services/Modules/Installer.php", "downloaded_repos/et-nik_gameap/app/Services/Modules/MarketplaceService.php", "downloaded_repos/et-nik_gameap/app/Services/PersonalAccessTokenService.php", "downloaded_repos/et-nik_gameap/app/Services/ProblemFinderService.php", "downloaded_repos/et-nik_gameap/app/Services/RconService.php", "downloaded_repos/et-nik_gameap/app/Services/ServerControlService.php", "downloaded_repos/et-nik_gameap/app/Services/ServerService.php", "downloaded_repos/et-nik_gameap/app/Traits/Encryptable.php", "downloaded_repos/et-nik_gameap/app/UseCases/Commands/CreateGameServerCommand.php", "downloaded_repos/et-nik_gameap/app/UseCases/Commands/EditGameServerCommand.php", "downloaded_repos/et-nik_gameap/app/UseCases/CreateGameServer.php", "downloaded_repos/et-nik_gameap/app/UseCases/EditGameServer.php", "downloaded_repos/et-nik_gameap/app/Validators/ReCaptcha.php", "downloaded_repos/et-nik_gameap/artisan", "downloaded_repos/et-nik_gameap/bootstrap/app.php", "downloaded_repos/et-nik_gameap/bootstrap/cache/.gitignore", "downloaded_repos/et-nik_gameap/composer.json", "downloaded_repos/et-nik_gameap/config/app.php", "downloaded_repos/et-nik_gameap/config/auth.php", "downloaded_repos/et-nik_gameap/config/broadcasting.php", "downloaded_repos/et-nik_gameap/config/cache.php", "downloaded_repos/et-nik_gameap/config/constants.php", "downloaded_repos/et-nik_gameap/config/database.php", "downloaded_repos/et-nik_gameap/config/file-manager.php", "downloaded_repos/et-nik_gameap/config/filesystems.php", "downloaded_repos/et-nik_gameap/config/infyom/generator_builder.php", "downloaded_repos/et-nik_gameap/config/infyom/laravel_generator.php", "downloaded_repos/et-nik_gameap/config/json-api-paginate.php", "downloaded_repos/et-nik_gameap/config/logging.php", "downloaded_repos/et-nik_gameap/config/mail.php", "downloaded_repos/et-nik_gameap/config/modules.php", "downloaded_repos/et-nik_gameap/config/queue.php", "downloaded_repos/et-nik_gameap/config/roadrunner.php", "downloaded_repos/et-nik_gameap/config/sanctum.php", "downloaded_repos/et-nik_gameap/config/services.php", "downloaded_repos/et-nik_gameap/config/session.php", "downloaded_repos/et-nik_gameap/config/view.php", "downloaded_repos/et-nik_gameap/database/.gitignore", "downloaded_repos/et-nik_gameap/database/factories/DedicatedServerFactory.php", "downloaded_repos/et-nik_gameap/database/factories/GameFactory.php", "downloaded_repos/et-nik_gameap/database/factories/GameModFactory.php", "downloaded_repos/et-nik_gameap/database/factories/GdaemonTaskFactory.php", "downloaded_repos/et-nik_gameap/database/factories/ServerFactory.php", "downloaded_repos/et-nik_gameap/database/factories/ServerSettingFactory.php", "downloaded_repos/et-nik_gameap/database/factories/ServerTaskFactory.php", "downloaded_repos/et-nik_gameap/database/factories/UserFactory.php", "downloaded_repos/et-nik_gameap/database/migrations/2014_10_12_000000_create_users_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2014_10_12_100000_create_password_resets_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_000000_create_dedicated_servers_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_100000_create_ds_stats_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_200000_create_ds_users_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_300000_create_games_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_400000_create_game_mods_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_500000_create_gdaemon_tasks_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_600000_create_servers_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_700000_create_servers_settings_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_09_12_800000_create_servers_stats_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2017_10_15_163730_create_permission_tables.php", "downloaded_repos/et-nik_gameap/database/migrations/2018_12_04_143042_create_client_certificates.php", "downloaded_repos/et-nik_gameap/database/migrations/2018_12_07_143515_create_server_user.php", "downloaded_repos/et-nik_gameap/database/migrations/2019_03_18_154345_create_jobs_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2019_07_01_144304_add_gdaemon_task_canceled_status.php", "downloaded_repos/et-nik_gameap/database/migrations/2019_07_10_183954_add_foreign_keys.php", "downloaded_repos/et-nik_gameap/database/migrations/2019_07_10_205811_add_servers_deleted_at.php", "downloaded_repos/et-nik_gameap/database/migrations/2019_10_06_192224_add_gdaemon_tasks_server_id_nullable.php", "downloaded_repos/et-nik_gameap/database/migrations/2019_12_14_000001_create_personal_access_tokens_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2020_01_22_212705_create_bouncer_tables.php", "downloaded_repos/et-nik_gameap/database/migrations/2020_03_04_182805_create_servers_tasks_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2020_03_04_195038_create_servers_tasks_fails_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2022_06_13_191501_change_games_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2022_06_13_191643_change_game_mods_table.php", "downloaded_repos/et-nik_gameap/database/migrations/2023_02_27_125000_add_games_enabled.php", "downloaded_repos/et-nik_gameap/database/migrations/2023_02_27_152300_remove_games_start_code.php", "downloaded_repos/et-nik_gameap/database/seeds/ClientCertificatesTableSeeder.php", "downloaded_repos/et-nik_gameap/database/seeds/DatabaseSeeder.php", "downloaded_repos/et-nik_gameap/database/seeds/DedicatedServersTableSeeder.php", "downloaded_repos/et-nik_gameap/database/seeds/GameModsTableSeeder.php", "downloaded_repos/et-nik_gameap/database/seeds/GamesTableSeeder.php", "downloaded_repos/et-nik_gameap/database/seeds/PermissionsSeeder.php", "downloaded_repos/et-nik_gameap/database/seeds/ServersTableSeeder.php", "downloaded_repos/et-nik_gameap/database/seeds/UsersTableSeeder.php", "downloaded_repos/et-nik_gameap/docker-compose.yml", "downloaded_repos/et-nik_gameap/docs/gdaemon-api.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/dedicated_servers.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/dedicated_servers_id.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/dedicated_servers_id_busy_ports.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/dedicated_servers_id_ip_list.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/file_manager_content.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/file_manager_upload.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/game_mods.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/games.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/games_id_mods.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/gdaemon_tasks_id.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/gdaemon_tasks_id_output.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_abilities.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_console.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_query.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_rcon.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_rcon_features.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_rcon_players.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_rcon_players_ban.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_rcon_players_kick.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_reinstall.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_restart.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_settings.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_start.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_status.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_stop.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_id_update.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/servers_search.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/users.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/users_id.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/users_id_servers.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/paths/users_id_servers_id_permissions.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/responses/common-error.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/responses/common-success.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/responses/server-control.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/schemas/server.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/schemas/user-server-permission.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/schemas/user-server.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/schemas/user-with-password-and-roles.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/schemas/user-with-roles.yaml", "downloaded_repos/et-nik_gameap/docs/web-api/schemas/user.yaml", "downloaded_repos/et-nik_gameap/docs/web-api.yaml", "downloaded_repos/et-nik_gameap/ecs.php", "downloaded_repos/et-nik_gameap/modules/.gitkeep", "downloaded_repos/et-nik_gameap/package-lock.json", "downloaded_repos/et-nik_gameap/package.json", "downloaded_repos/et-nik_gameap/phpunit.xml", "downloaded_repos/et-nik_gameap/postcss.config.js", "downloaded_repos/et-nik_gameap/public/.htaccess", "downloaded_repos/et-nik_gameap/public/css/login.css", "downloaded_repos/et-nik_gameap/public/css/vue-styles.css", "downloaded_repos/et-nik_gameap/public/favicon-32x32.png", "downloaded_repos/et-nik_gameap/public/favicon-96x96.png", "downloaded_repos/et-nik_gameap/public/favicon.ico", "downloaded_repos/et-nik_gameap/public/file-manager.css", "downloaded_repos/et-nik_gameap/public/images/gap_logo_white.png", "downloaded_repos/et-nik_gameap/public/images/gap_logo_white_mini.png", "downloaded_repos/et-nik_gameap/public/images/gicon.svg", "downloaded_repos/et-nik_gameap/public/images/icons/save-white.svg", "downloaded_repos/et-nik_gameap/public/images/icons/save.svg", "downloaded_repos/et-nik_gameap/public/index.php", "downloaded_repos/et-nik_gameap/public/robots.txt", "downloaded_repos/et-nik_gameap/public/web.config", "downloaded_repos/et-nik_gameap/resources/assets/js/App.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/app.js", "downloaded_repos/et-nik_gameap/resources/assets/js/bootstrap.js", "downloaded_repos/et-nik_gameap/resources/assets/js/components/ContentView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/GBreadcrumbs.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/GButton.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/GDeletableList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/GStatusBadge.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/GameIcon.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/GuestNavbar.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/KeyValueTable.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/Loading.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/MainNavbar.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/MainNavbarDropdown.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/MainSidebar.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/Progressbar.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/SettingsParameters.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/TaskOutput.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/bars.js", "downloaded_repos/et-nik_gameap/resources/assets/js/components/blocks/CreateNodeModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/input/GameapSelect.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/input/InputManyList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/input/InputTextList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/rcon/RconConsole.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/rcon/RconPlayers.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/servers/DsIpSelector.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/servers/GameModSelector.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/servers/ServerSelector.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/servers/SmartPortSelector.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/components/servers/UserServerPrivileges.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/FileManager.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/assets/laravel-file-manager.gif", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/blocks/ContextMenu.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/blocks/InfoBlock.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/blocks/NavbarBlock.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/blocks/NotificationBlock.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/blocks/mixins/contextMenu.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/blocks/mixins/contextMenuActions.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/blocks/mixins/contextMenuRules.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/manager/BreadCrumb.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/manager/DiskList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/manager/GridView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/manager/Manager.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/manager/TableView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/manager/Thumbnail.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/manager/mixins/manager.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/ModalBlock.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/additions/CropperModule.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/additions/SelectedFileList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/mixins/modal.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/AboutModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/AudioPlayerModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/ClipboardModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/DeleteModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/NewFileModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/NewFolderModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/PreviewModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/PropertiesModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/RenameModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/StatusModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/TextEditModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/UnzipModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/UploadModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/VideoPlayerModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/modals/views/ZipModal.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/tree/FolderTree.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/components/tree/TreeBranch.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/emitter.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/http/axios.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/http/get.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/http/post.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/index.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/ar.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/cs.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/de.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/en.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/es.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/fa.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/fr.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/hu.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/it.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/nl.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/pl.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/pt_BR.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/ru.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/sr.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/tr.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/zh_CN.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/lang/zh_TW.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/main.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/mixins/helper.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/mixins/translate.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/actions.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/getters.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/manager/actions.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/manager/getters.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/manager/mutations.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/manager/store.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/messages/mutations.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/messages/store.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/modal/mutations.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/modal/store.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/mutations.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/settings/getters.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/settings/mutations.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/settings/store.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/state.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/tree/actions.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/tree/getters.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/tree/mutations.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/tree/store.js", "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store.js", "downloaded_repos/et-nik_gameap/resources/assets/js/i18n/i18n.js", "downloaded_repos/et-nik_gameap/resources/assets/js/i18n/plurals.js", "downloaded_repos/et-nik_gameap/resources/assets/js/legacy/store/activeTab.js", "downloaded_repos/et-nik_gameap/resources/assets/js/legacy/store/dedicatedServers.js", "downloaded_repos/et-nik_gameap/resources/assets/js/legacy/store/gameMods.js", "downloaded_repos/et-nik_gameap/resources/assets/js/legacy/store/games.js", "downloaded_repos/et-nik_gameap/resources/assets/js/legacy/store/index.js", "downloaded_repos/et-nik_gameap/resources/assets/js/legacy/store/rcon/players.js", "downloaded_repos/et-nik_gameap/resources/assets/js/legacy/store/servers.js", "downloaded_repos/et-nik_gameap/resources/assets/js/parts/dialogs.js", "downloaded_repos/et-nik_gameap/resources/assets/js/parts/form.js", "downloaded_repos/et-nik_gameap/resources/assets/js/parts/validators.js", "downloaded_repos/et-nik_gameap/resources/assets/js/routes.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/auth.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/authSettings.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/clientCertificates.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/daemonTask.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/daemonTaskList.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/game.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/gameList.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/gameMod.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/node.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/nodeList.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/server.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/serverList.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/serverRcon.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/tokens.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/user.js", "downloaded_repos/et-nik_gameap/resources/assets/js/store/userList.js", "downloaded_repos/et-nik_gameap/resources/assets/js/style.css", "downloaded_repos/et-nik_gameap/resources/assets/js/views/EmptyView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/HomeView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/InactiveServer.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/LoginView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/ProfileView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/ServerIdView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/ServersView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/TokensView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminClientCertificatesView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminDaemonTaskListView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminDaemonTaskOutputView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminGamesEdit.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminGamesList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminModEdit.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminNodeShowView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminNodesCreateView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminNodesEditView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminNodesView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersCreate.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersEdit.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminUsersEditView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminUsersView.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/CreateCertificateForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/CreateGameForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/CreateModForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/CreateUserForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateGameForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateNodeForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateUserForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/errors/Error403View.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/errors/Error404View.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/forms/GenerateTokenForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/forms/UpdateProfileForm.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/servertabs/ServerConsole.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/servertabs/ServerControlButton.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/servertabs/ServerMainList.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/servertabs/ServerSettings.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/servertabs/ServerStatus.vue", "downloaded_repos/et-nik_gameap/resources/assets/js/views/servertabs/ServerTasks.vue", "downloaded_repos/et-nik_gameap/resources/assets/less/app.less", "downloaded_repos/et-nik_gameap/resources/assets/sass/_variables.scss", "downloaded_repos/et-nik_gameap/resources/assets/sass/app.scss", "downloaded_repos/et-nik_gameap/resources/assets/sass/bootstrap/switches.scss", "downloaded_repos/et-nik_gameap/resources/assets/sass/bootstrap-custom.scss", "downloaded_repos/et-nik_gameap/resources/assets/sass/gameap.scss", "downloaded_repos/et-nik_gameap/resources/assets/sass/gicon/fonts/gicon.eot", "downloaded_repos/et-nik_gameap/resources/assets/sass/gicon/fonts/gicon.svg", "downloaded_repos/et-nik_gameap/resources/assets/sass/gicon/fonts/gicon.ttf", "downloaded_repos/et-nik_gameap/resources/assets/sass/gicon/fonts/gicon.woff", "downloaded_repos/et-nik_gameap/resources/assets/sass/gicon/gicon.css", "downloaded_repos/et-nik_gameap/resources/lang/en/auth.php", "downloaded_repos/et-nik_gameap/resources/lang/en/client_certificates.php", "downloaded_repos/et-nik_gameap/resources/lang/en/dedicated_servers.php", "downloaded_repos/et-nik_gameap/resources/lang/en/games.php", "downloaded_repos/et-nik_gameap/resources/lang/en/gdaemon_tasks.php", "downloaded_repos/et-nik_gameap/resources/lang/en/home.php", "downloaded_repos/et-nik_gameap/resources/lang/en/labels.php", "downloaded_repos/et-nik_gameap/resources/lang/en/main.php", "downloaded_repos/et-nik_gameap/resources/lang/en/modules.php", "downloaded_repos/et-nik_gameap/resources/lang/en/navbar.php", "downloaded_repos/et-nik_gameap/resources/lang/en/pagination.php", "downloaded_repos/et-nik_gameap/resources/lang/en/passwords.php", "downloaded_repos/et-nik_gameap/resources/lang/en/profile.php", "downloaded_repos/et-nik_gameap/resources/lang/en/rcon.php", "downloaded_repos/et-nik_gameap/resources/lang/en/servers.php", "downloaded_repos/et-nik_gameap/resources/lang/en/servers_tasks.php", "downloaded_repos/et-nik_gameap/resources/lang/en/sidebar.php", "downloaded_repos/et-nik_gameap/resources/lang/en/tokens.php", "downloaded_repos/et-nik_gameap/resources/lang/en/users.php", "downloaded_repos/et-nik_gameap/resources/lang/en/validation.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/auth.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/client_certificates.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/dedicated_servers.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/games.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/gdaemon_tasks.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/home.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/labels.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/main.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/modules.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/navbar.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/pagination.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/passwords.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/profile.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/rcon.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/servers.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/servers_tasks.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/sidebar.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/tokens.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/users.php", "downloaded_repos/et-nik_gameap/resources/lang/ru/validation.php", "downloaded_repos/et-nik_gameap/resources/views/auth/login.blade.php", "downloaded_repos/et-nik_gameap/resources/views/auth/passwords/email.blade.php", "downloaded_repos/et-nik_gameap/resources/views/auth/passwords/reset.blade.php", "downloaded_repos/et-nik_gameap/resources/views/auth/register.blade.php", "downloaded_repos/et-nik_gameap/resources/views/empty.blade.php", "downloaded_repos/et-nik_gameap/resources/views/layouts/guest.blade.php", "downloaded_repos/et-nik_gameap/resources/views/layouts/main.blade.php", "downloaded_repos/et-nik_gameap/routes/api.php", "downloaded_repos/et-nik_gameap/routes/channels.php", "downloaded_repos/et-nik_gameap/routes/console.php", "downloaded_repos/et-nik_gameap/routes/gdaemon_api.php", "downloaded_repos/et-nik_gameap/routes/web.php", "downloaded_repos/et-nik_gameap/server.php", "downloaded_repos/et-nik_gameap/storage/app/.gitignore", "downloaded_repos/et-nik_gameap/storage/app/certs/.gitignore", "downloaded_repos/et-nik_gameap/storage/app/certs/client/.gitignore", "downloaded_repos/et-nik_gameap/storage/app/public/.gitignore", "downloaded_repos/et-nik_gameap/storage/debugbar/.gitignore", "downloaded_repos/et-nik_gameap/storage/framework/.gitignore", "downloaded_repos/et-nik_gameap/storage/framework/cache/.gitignore", "downloaded_repos/et-nik_gameap/storage/framework/sessions/.gitignore", "downloaded_repos/et-nik_gameap/storage/framework/testing/.gitignore", "downloaded_repos/et-nik_gameap/storage/framework/views/.gitignore", "downloaded_repos/et-nik_gameap/storage/logs/.gitignore", "downloaded_repos/et-nik_gameap/tailwind.config.js", "downloaded_repos/et-nik_gameap/vite.config.js", "downloaded_repos/et-nik_gameap/vue.config.js", "downloaded_repos/et-nik_gameap/webpack.mix.js"], "skipped": [{"path": "downloaded_repos/et-nik_gameap/.dev/docker/php/Dockerfile", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/font-awesome/fontawesome-webfont.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/font-awesome/fontawesome-webfont.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/font-awesome/fontawesome-webfont.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/font-awesome/fontawesome-webfont.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/public/fonts/vendor/font-awesome/fontawesome-webfont.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/APITestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/FileManager/InitializeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/GDaemonTasksTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/GamesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/Servers/CreateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/Servers/SaveTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/Servers/ServersTests.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/API/UsersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Browser/BrowserTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Browser/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Browser/console/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Browser/screenshots/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Context/Browser/DaemonContextTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Context/Browser/Models/GameContextTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Context/Browser/Models/GameModContextTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Context/Browser/Models/ServerContextTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Context/Browser/Models/UserContextTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/CreatesApplication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/DaemonAPI/GetInitDataTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/DaemonAPI/GetTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/DaemonAPI/ServersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/DaemonAPI/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/DuskTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/BasicTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/ClientCertificatesControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/DedicatedServersControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/GDaemonTaskControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/GameModsControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/GamesControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/ServerRconControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/ServersControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/ServersTasksControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/Controllers/API/UsersControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Feature/Permissions/PermissionsTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Manual/GET_api_gdaemon.http", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Manual/GET_api_servers.http", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Manual/GET_api_servers_search.http", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Manual/GET_api_user.http", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Manual/PUT_api_servers.http", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Manual/http-client.env.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Manual/http-client.private.env.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Controllers/API/HealthzControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Controllers/API/ServersControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Controllers/API/ServersTasksControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Controllers/GdaemonAPI/ServersControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Controllers/GdaemonAPI/ServersTasksControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/ClientCertificateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/DedicatedServerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/GDaemonTaskTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/GameModTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/GameTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/ServerSettingTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/ServerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Models/UserTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Repositories/MarketplaceModulesRepositoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Repositories/ServerRepositoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Repositories/ServerSettingsRepositoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Repositories/ServersTasksRepositoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Services/Modules/MarketplaceServiceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/et-nik_gameap/tests/Unit/Services/ServerServiceTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6558449268341064, "profiling_times": {"config_time": 5.459974527359009, "core_time": 4.63414192199707, "ignores_time": 0.0018992424011230469, "total_time": 10.096781015396118}, "parsing_time": {"total_time": 2.6620914936065674, "per_file_time": {"mean": 0.00614801730625073, "std_dev": 0.0005108749563371746}, "very_slow_stats": {"time_ratio": 0.1596387697558425, "count_ratio": 0.0023094688221709007}, "very_slow_files": [{"fpath": "downloaded_repos/et-nik_gameap/package-lock.json", "ftime": 0.4249730110168457}]}, "scanning_time": {"total_time": 11.752965688705444, "per_file_time": {"mean": 0.007354797051755597, "std_dev": 0.0015757882066923916}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.38710618019104, "per_file_and_rule_time": {"mean": 0.0015565745313377932, "std_dev": 4.222951500449742e-05}, "very_slow_stats": {"time_ratio": 0.1360082209949169, "count_ratio": 0.001838235294117647}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/et-nik_gameap/package-lock.json", "rule_id": "json.aws.security.public-s3-policy-statement.public-s3-policy-statement", "time": 0.10821199417114258}, {"fpath": "downloaded_repos/et-nik_gameap/resources/assets/js/app.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.11412811279296875}, {"fpath": "downloaded_repos/et-nik_gameap/resources/assets/js/filemanager/store/actions.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.11534810066223145}, {"fpath": "downloaded_repos/et-nik_gameap/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.1229860782623291}]}, "tainting_time": {"total_time": 0.5705106258392334, "per_def_and_rule_time": {"mean": 0.0006468374442621693, "std_dev": 2.5914480592787554e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1095212736}, "engine_requested": "OSS", "skipped_rules": []}