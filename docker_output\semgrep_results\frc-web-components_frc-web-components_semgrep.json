{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/src/cli.ts", "start": {"line": 39, "col": 31, "offset": 1131}, "end": {"line": 39, "col": 41, "offset": 1141}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/src/cli.ts", "start": {"line": 40, "col": 34, "offset": 1234}, "end": {"line": 40, "col": 44, "offset": 1244}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/src/cli.ts", "start": {"line": 46, "col": 28, "offset": 1617}, "end": {"line": 46, "col": 35, "offset": 1624}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/src/cli.ts", "start": {"line": 50, "col": 31, "offset": 1806}, "end": {"line": 50, "col": 38, "offset": 1813}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/NT4_.ts", "start": {"line": 724, "col": 8, "offset": 21449}, "end": {"line": 724, "col": 13, "offset": 21454}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/ReconnectingNt4Socket.ts", "start": {"line": 72, "col": 22, "offset": 1639}, "end": {"line": 72, "col": 27, "offset": 1644}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/provider.ts", "start": {"line": 110, "col": 15, "offset": 3148}, "end": {"line": 110, "col": 74, "offset": 3207}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/provider.ts", "start": {"line": 188, "col": 9, "offset": 5432}, "end": {"line": 188, "col": 65, "offset": 5488}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/differential.ts", "start": {"line": 365, "col": 5, "offset": 10506}, "end": {"line": 366, "col": 29, "offset": 10592}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/differential.ts", "start": {"line": 367, "col": 5, "offset": 10597}, "end": {"line": 367, "col": 72, "offset": 10664}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/differential.ts", "start": {"line": 375, "col": 5, "offset": 10807}, "end": {"line": 375, "col": 72, "offset": 10874}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/differential.ts", "start": {"line": 383, "col": 5, "offset": 11159}, "end": {"line": 384, "col": 29, "offset": 11245}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/differential.ts", "start": {"line": 392, "col": 5, "offset": 11388}, "end": {"line": 392, "col": 72, "offset": 11455}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/mecanum.ts", "start": {"line": 339, "col": 5, "offset": 8839}, "end": {"line": 340, "col": 29, "offset": 8925}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/mecanum.ts", "start": {"line": 341, "col": 5, "offset": 8930}, "end": {"line": 341, "col": 72, "offset": 8997}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/mecanum.ts", "start": {"line": 351, "col": 5, "offset": 9227}, "end": {"line": 351, "col": 72, "offset": 9294}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/mecanum.ts", "start": {"line": 359, "col": 5, "offset": 9579}, "end": {"line": 360, "col": 29, "offset": 9665}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/mecanum.ts", "start": {"line": 371, "col": 5, "offset": 9962}, "end": {"line": 371, "col": 72, "offset": 10029}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/KeyStore.ts", "start": {"line": 23, "col": 7, "offset": 565}, "end": {"line": 23, "col": 61, "offset": 619}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/dashboard.ts", "start": {"line": 219, "col": 5, "offset": 5620}, "end": {"line": 219, "col": 32, "offset": 5647}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/frc-dashboard.ts", "start": {"line": 164, "col": 7, "offset": 4945}, "end": {"line": 164, "col": 28, "offset": 4966}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/nt4/NT4.ts", "start": {"line": 616, "col": 19, "offset": 18007}, "end": {"line": 616, "col": 24, "offset": 18012}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/frc-web-components_frc-web-components/.changeset/README.md", "downloaded_repos/frc-web-components_frc-web-components/.changeset/config.json", "downloaded_repos/frc-web-components_frc-web-components/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/.husky/commit-msg", "downloaded_repos/frc-web-components_frc-web-components/.husky/pre-commit", "downloaded_repos/frc-web-components_frc-web-components/.prettierignore", "downloaded_repos/frc-web-components_frc-web-components/.prettierrc.json", "downloaded_repos/frc-web-components_frc-web-components/.vscode/extensions.json", "downloaded_repos/frc-web-components_frc-web-components/README.md", "downloaded_repos/frc-web-components_frc-web-components/TODO.md", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/.storybook/main.js", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/.storybook/preview.js", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/package.json", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Robot_KitBot.glb", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/3AxisAccelerometer.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Accelerometer.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Axis.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Bar.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/BasicFmsInfo.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/BooleanBox.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/DifferentialDrivebase.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Encoder.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Field.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Gauge.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Gyro.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Icon.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Introduction.mdx", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Logger.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/MecanumDrivebase.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Mechanism2d.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/NetworkAlerts.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/NumberBar.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/NumberSlider.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Pdp.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Preferences.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/Relay.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/SendableChooser.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/SwerveDrivebase.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/ToggleButton.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/ToggleGroup.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/ToggleSwitch.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/VoltageView.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/code-brackets.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/colors.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/comments.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/direction.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/flow.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/plugin.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/repo.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/assets/stackalt.svg", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/canvas/CanvasLine.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/canvas/CanvasMjpgStream.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/charged-up/ScoringGrid.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/command-based/Command.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/command-based/Subsystem.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/field3d/Field3d.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/field3d/Urdf.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/line-chart/LineChart.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/pid/PIDCommand.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/pid/PIDController.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/src/stories/pid/ProfiledPIDController.stories.ts", "downloaded_repos/frc-web-components_frc-web-components/atomic-build-plugin.ts", "downloaded_repos/frc-web-components_frc-web-components/docs/dashboard.md", "downloaded_repos/frc-web-components_frc-web-components/docs/images/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/docs/images/dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/docs/images/plugin-dnd-test.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/images/plugin-launched.png", "downloaded_repos/frc-web-components_frc-web-components/docs/images/plugin-source-code.png", "downloaded_repos/frc-web-components_frc-web-components/docs/images/properties.png", "downloaded_repos/frc-web-components_frc-web-components/docs/images/start-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/addComponent.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/img/address-setting.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/gyro-source.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/img/gyro-source2.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/img/layout01.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/layout02.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/nt-slider.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/img/opening-dashboard.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/img/plugins/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/plugins/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/plugins/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/plugins/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/plugins/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/properties.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/img/saving-dashboard.gif", "downloaded_repos/frc-web-components_frc-web-components/docs/img/selection.png", "downloaded_repos/frc-web-components_frc-web-components/docs/img/themes.png", "downloaded_repos/frc-web-components_frc-web-components/docs/plugins.md", "downloaded_repos/frc-web-components_frc-web-components/eslint.config.js", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/README.md", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/plugin-dnd-test.gif", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/index.html", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/package.json", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/public/assets/party.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/public/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/public/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/src/my-element.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/src/test-plugin/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/src/test-plugin/index.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/examples/lit-plugin/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/.eslintrc.cjs", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/LICENSE", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/README.md", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/-usage-example.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/custom-dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/import-react-component.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/nav-to-my-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/index.html", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/package.json", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/public/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/App.css", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/App.tsx", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/components/Dashboard.tsx", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/components/MyElement.tsx", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/components/ThemeChooser.tsx", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/main.tsx", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/themes.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/examples/react-custom-dashboard/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/.eslintrc.cjs", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/LICENSE", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/README.md", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/plugin-dnd-test.gif", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/index.html", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/package.json", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/public/assets/party.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/public/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/public/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/src/MyElement.tsx", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/src/test-plugin/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/src/test-plugin/index.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/examples/react-plugin/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/.vscode/extensions.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/LICENSE", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/README.md", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/custom-dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/import-svelte-component.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/nav-to-my-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/docs/svelte-usage-example.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/index.html", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/package.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/public/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/App.svelte", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/app.css", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/components/Dashboard.svelte", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/components/MyElement.svelte", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/components/ThemeChooser.svelte", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/main.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/themes.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/svelte.config.js", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-custom-dashboard/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/.vscode/extensions.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/README.md", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/plugin-dnd-test.gif", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/index.html", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/package.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/public/assets/party.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/public/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/public/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/src/MyElement.svelte", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/src/test-plugin/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/src/test-plugin/index.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/svelte.config.js", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/examples/svelte-plugin/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/nx.json", "downloaded_repos/frc-web-components_frc-web-components/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/LICENSE", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/src/cli.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/plugin-dnd-test.gif", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/index.html", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/public/assets/party.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/public/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/public/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/src/my-element.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/src/test-plugin/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/src/test-plugin/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/lit-plugin/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/.eslintrc.cjs", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/LICENSE", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/-usage-example.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/custom-dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/import-react-component.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/nav-to-my-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/index.html", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/public/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/App.css", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/App.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/components/Dashboard.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/components/MyElement.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/components/ThemeChooser.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/main.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/themes.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-custom-dashboard/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/.eslintrc.cjs", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/LICENSE", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/plugin-dnd-test.gif", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/index.html", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/public/assets/party.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/public/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/public/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/src/MyElement.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/src/test-plugin/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/src/test-plugin/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/react-plugin/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/.vscode/extensions.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/LICENSE", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/custom-dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/import-svelte-component.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/nav-to-my-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/plugin-successfully-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/docs/svelte-usage-example.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/index.html", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/public/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/App.svelte", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/app.css", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/components/Dashboard.svelte", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/components/MyElement.svelte", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/components/ThemeChooser.svelte", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/main.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/themes.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/svelte.config.js", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-custom-dashboard/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/.vscode/extensions.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/build-files.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/creating-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/dashboard.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/plugin-dialog.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/plugin-dnd-test.gif", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/plugin-file-menu.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/plugin-loaded.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/refresh-plugin.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/running-in-dev.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/docs/select-plugin-folder.png", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/index.html", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/public/assets/party.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/public/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/public/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/src/MyElement.svelte", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/src/test-plugin/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/src/test-plugin/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/svelte.config.js", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/templates/svelte-plugin/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/create-plugin/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/index.html", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Robot_KitBot.glb", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/add-circle.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/add.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/check.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/collapse.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/connected.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/disconnected.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/expand.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/locked.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/remove.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/unlocked.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/vite.svg", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/App.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/DashboardLayout.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/app-layout.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/context-providers/ComponentContext.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/context-providers/DropZoneContext.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/BooleanBox.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Camera.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/CheckboxGroup.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/CommandBased.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Drivebases.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Dropdowns.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Encoder.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Field.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Gauge.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Icon.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Labels.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/LineChart.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/MarkdownViewer.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/MarkdownViewer.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Mechanism2d.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/NetworkAlerts.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/NumberBar.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/NumberSlider.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Pid.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Preferences.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/RadioGroup.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/Relay.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/TextField.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/ToggleButton.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/fromProps.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/get-poses.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/number-field/numberField.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/sim/AddressableLEDs.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/sim/RobotState.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/sim/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/dashboard-components/toggleSwitch.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/shared/number-input/NumberInput.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/shared/number-input/utils.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/Tab.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/Tab.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/TabComponent.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/constants.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/context-menu/ContextMenu.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/context-menu/ElementMenu.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/context-menu/useContextMenu.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/titlebar/FileMenu.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/titlebar/PluginsDialog.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/titlebar/Titlebar.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/titlebar/Titlebar.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/ComponentPicker.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/Settings.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/Editor.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/editor-layout.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/ColorCellRenderer.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/ColorCellRenderer.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/MarkdownEditor.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/NameCellRenderer.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/NumberArrayEditor.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/NumberArrayEditor.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/ParentActionCellRenderer.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/ParentActionsCellRenderer.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/Properties.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/Properties.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/SimpleDialog.css", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/SimpleDialog.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/SourceCellRenderer.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/SourceCellRenderer.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/StringArrayEditor.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/properties/StringArrayEditor.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/sources/Sources.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/sources/Sources.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tools/editor/sources/data.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/example-layouts/example.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/index.module.scss", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/main.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/plugins/shuffleboard-layout.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/app/createAppSlice.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/app/hooks.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/app/store.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/selectors/componentSelectors.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/selectors/layoutSelectors.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/selectors/sourceSelectors.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/slices/appSlice.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/slices/layout.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/slices/layoutSlice.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/slices/sourceSlice.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/ProtoDecoder.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/StructDecoder.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/NT4.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/NT4_.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/Nt4Socket.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/ReconnectingNt4Socket.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/msgpack.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/msgpack.js", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/utils.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/provider.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/sim/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/source-provider.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/vite.config.dev.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/3-axis-accelerometer/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/accelerometer/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/axis/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/bar/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/basic-fms-info/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/boolean-box/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-circle.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-group.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-line.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-mjpg-stream-instance.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-mjpg-stream.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-ngon.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-rect.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas-text.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/canvas.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/interfaces.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/canvas/wrappers/camera.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/command-based/command.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/command-based/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/command-based/subsystem.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/dashboard-configs.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/curved-arrow.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/differential.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/mecanum.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/swerve.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/encoder/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/field-configs.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/field-images.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/field-interfaces.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/field-path.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/field-robot.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/field.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/get-poses.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/units.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/wrapper/field-object-manager.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/wrapper/wrapper.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/dashboard-configs.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/field-configs.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/field-interfaces.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/field3d-object.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/field3d-pose-visualizer.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/field3d-urdf-wrapper.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/field3d-urdf.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/field3d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/file-utils.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/object-configs.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/urdf-configs.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/urdf.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field3d/utils.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/form-elements/checkbox-group.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/form-elements/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/form-elements/radio-group.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/gauge/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/gyro/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/icon/icon.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/icon/iconset.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/icon/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/label/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/label/label.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/label/number-label.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/dashbard-configs.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/line-chart-axis.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/line-chart-data.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/line-chart-legend.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/line-chart.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/real-time-x-axis.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/logger/html-logger.js", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/logger/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/mechanism2d/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/mechanism2d/mechanism2d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/mechanism2d/wrapper.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/network-alerts/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/number-bar/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/number-slider/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/pdp/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/pid/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/pid/pid-command.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/pid/pid-controller.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/pid/profiled-pid-controller.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/preferences/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/preferences/preferences.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/preferences/wrapper.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/relay/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/scoring-grid/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/scoring-grid/node.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/sendable-chooser/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/sendable-chooser/sendable-chooser.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/sendable-chooser/wrapper.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/toggle-button/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/toggle-group/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/toggle-switch/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/voltage-view/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/KeyStore.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/frc-dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/get-allowed-children.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/layer.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/dashboard/themes.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/gamepad/gamepad-provider.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/gamepad/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/nt4/NT4.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/nt4/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/nt4/msgpack.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/nt4/msgpack.js", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/source-providers/nt4/nt4-provider.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/themes/dark-theme.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/themes/dashboard-themes.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/themes/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/vite.config.dev.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/src/NetworkTables.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/src/create-dashboard.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/src/directives.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/vite.config.dev.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/lit/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/packages/react/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/3AxisAccelerometer.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Accelerometer.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Axis.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Bar.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/BasicFmsInfo.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/BooleanBox.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Canvas.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/CommandBased.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Drivebases.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Encoder.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Field.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Field3d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Form.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Gauge.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Gyro.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Icon.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/KeyValueTable.css", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/KeyValueTable.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Labels.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/LineChart.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Logger.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Mechanism2d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/NetworkAlerts.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/NumberBar.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/NumberSlider.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Pdp.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Pid.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Preferences.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/Relay.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/ScoringGrid.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/SendableChooser.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/ToggleButton.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/ToggleGroup.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/ToggleSwitch.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/VoltageView.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/components/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/networktables/NT4Provider.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/networktables/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/networktables/useEntry.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/networktables/useGlobalListener.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/networktables/useJson.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/react/src/networktables/useKeyListener.tsx", "downloaded_repos/frc-web-components_frc-web-components/packages/react/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/react/vite.config.dev.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/react/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/.vscode/extensions.json", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/CHANGELOG.md", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/README.md", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/package.json", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/src/index.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/src/networktables.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/src/vite-env.d.ts", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/svelte.config.js", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/tsconfig.json", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/tsconfig.node.json", "downloaded_repos/frc-web-components_frc-web-components/packages/svelte/vite.config.ts", "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "downloaded_repos/frc-web-components_frc-web-components/pnpm-workspace.yaml", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/.gitignore", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/.vscode/launch.json", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/.vscode/settings.json", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/.wpilib/wpilib_preferences.json", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/WPILib-License.md", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/build.gradle", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/gradle/wrapper/gradle-wrapper.jar", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/gradle/wrapper/gradle-wrapper.properties", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/gradlew", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/gradlew.bat", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/settings.gradle", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/simgui-ds.json", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/edit.svg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/index.css", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/index.html", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2018-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2019-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2020-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2021-barrel.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2021-bounce.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2021-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2021-galacticsearcha.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2021-galacticsearchb.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2021-slalom.png", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2022-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2023-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2024-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/field-images/2025-field.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/public/no-camera-stream.jpg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/settings.svg", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/example.txt", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/java/frc/robot/Constants.java", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/java/frc/robot/Main.java", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/java/frc/robot/Robot.java", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/java/frc/robot/RobotContainer.java", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/java/frc/robot/commands/Autos.java", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/java/frc/robot/commands/ExampleCommand.java", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/java/frc/robot/subsystems/ExampleSubsystem.java", "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/vendordeps/WPILibNewCommands.json", "downloaded_repos/frc-web-components_frc-web-components/tsconfig.base.json", "downloaded_repos/frc-web-components_frc-web-components/vite.config.ts"], "skipped": [{"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Field3d_2021.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Field3d_2022.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Field3d_2023.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Field3d_2024.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Field3d_2025.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Field3d_Evergreen.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/<PERSON>_<PERSON><PERSON>-<PERSON><PERSON> Awakens (6328).glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Robot_<PERSON><PERSON>-<PERSON><PERSON> Strikes Back (6328).glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Robot_Crab Bot.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Robot_Duck Bot.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Robot_REV 2023 Starter Bot.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Robot_REV 2023 Starter Bot_0.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/apps/storybook/public/3d-models/Robot_REV 2023 Starter Bot_1.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/docs/img/gyro-source3.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/docs/img/move.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/docs/img/remove.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/docs/img/resize.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/docs/img/tabs.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Field3d_2021.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Field3d_2022.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Field3d_2023.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Field3d_2024.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Field3d_2025.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Field3d_Evergreen.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/<PERSON>_<PERSON><PERSON>-<PERSON><PERSON> Awakens (6328).glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Robot_<PERSON><PERSON>-<PERSON><PERSON> Strikes Back (6328).glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Robot_Crab Bot.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Robot_Duck Bot.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Robot_REV 2023 Starter Bot.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Robot_REV 2023 Starter Bot_0.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/public/3d-models/Robot_REV 2023 Starter Bot_1.glb", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/frc-web-components_frc-web-components/robot-project-examples/WebServer/src/main/deploy/dashboard/index.js", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.779615879058838, "profiling_times": {"config_time": 5.888920068740845, "core_time": 8.394423723220825, "ignores_time": 0.002012968063354492, "total_time": 14.286272048950195}, "parsing_time": {"total_time": 7.184746026992798, "per_file_time": {"mean": 0.016145496689871464, "std_dev": 0.0009455226322739659}, "very_slow_stats": {"time_ratio": 0.049421787131372685, "count_ratio": 0.0022471910112359553}, "very_slow_files": [{"fpath": "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "ftime": 0.35508298873901367}]}, "scanning_time": {"total_time": 34.66575312614441, "per_file_time": {"mean": 0.01620652320062846, "std_dev": 0.010117253946234236}, "very_slow_stats": {"time_ratio": 0.0962064470078128, "count_ratio": 0.0004675081813931744}, "very_slow_files": [{"fpath": "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "ftime": 3.335068941116333}]}, "matching_time": {"total_time": 14.41883134841919, "per_file_and_rule_time": {"mean": 0.007883450709906604, "std_dev": 0.0005748173590282526}, "very_slow_stats": {"time_ratio": 0.28955533513737014, "count_ratio": 0.013121924548933843}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/field/field.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.16663193702697754}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/drivebases/swerve.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.16988492012023926}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "rule_id": "yaml.kubernetes.security.privileged-container.privileged-container", "time": 0.20012903213500977}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "rule_id": "yaml.kubernetes.security.allow-privilege-escalation.allow-privilege-escalation", "time": 0.21322393417358398}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/packages/fwc/src/components/line-chart/line-chart.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.22542977333068848}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/client/NT4_.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.22821402549743652}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "rule_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "time": 0.2422640323638916}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/components/tab/Tab.tsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.2654380798339844}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "rule_id": "yaml.docker-compose.security.exposing-docker-socket-volume.exposing-docker-socket-volume", "time": 0.3082418441772461}, {"fpath": "downloaded_repos/frc-web-components_frc-web-components/pnpm-lock.yaml", "rule_id": "yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "time": 0.3759119510650635}]}, "tainting_time": {"total_time": 3.0895233154296875, "per_def_and_rule_time": {"mean": 0.0007291770864832871, "std_dev": 4.293845269505563e-06}, "very_slow_stats": {"time_ratio": 0.016188263983207805, "count_ratio": 0.0002360160490913382}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/frc-web-components_frc-web-components/packages/dashboard/src/store/sources/nt4/StructDecoder.ts", "fline": 38, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.05001401901245117}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1117506880}, "engine_requested": "OSS", "skipped_rules": []}