{"version": "1.130.0", "results": [{"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/bot_comment.py", "start": {"line": 17, "col": 16, "offset": 249}, "end": {"line": 20, "col": 2, "offset": 386}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/bot_comment.py", "start": {"line": 31, "col": 12, "offset": 621}, "end": {"line": 31, "col": 56, "offset": 665}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/tutorial.md", "start": {"line": 643, "col": 27, "offset": 14682}, "end": {"line": 643, "col": 248, "offset": 14903}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "path": "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/utils.py", "start": {"line": 115, "col": 13, "offset": 3366}, "end": {"line": 115, "col": 79, "offset": 3432}, "extra": {"message": "Annotations passed to `typing.get_type_hints` are evaluated in `globals` and `locals` namespaces. Make sure that no arbitrary value can be written as the annotation and passed to `typing.get_type_hints` function.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "category": "security", "references": ["https://docs.python.org/3/library/typing.html#typing.get_type_hints"], "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "shortlink": "https://sg.run/8R6J"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "start": {"line": 31, "col": 49, "offset": 775}, "end": {"line": 31, "col": 52, "offset": 778}}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "start": {"line": 31, "col": 85, "offset": 775}, "end": {"line": 31, "col": 88, "offset": 778}}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "start": {"line": 35, "col": 20, "offset": 775}, "end": {"line": 35, "col": 23, "offset": 778}}]], "message": "Syntax error at line downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml:31:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "start": {"line": 31, "col": 49, "offset": 775}, "end": {"line": 31, "col": 52, "offset": 778}}, {"file": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "start": {"line": 31, "col": 85, "offset": 775}, "end": {"line": 31, "col": 88, "offset": 778}}, {"file": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "start": {"line": 35, "col": 20, "offset": 775}, "end": {"line": 35, "col": 23, "offset": 778}}]}], "paths": {"scanned": ["downloaded_repos/nrbnlulu_strawberry-django-auth/.coveragerc", "downloaded_repos/nrbnlulu_strawberry-django-auth/.flake8", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/ISSUE_TEMPLATE.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/dependabot.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/stale.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/bot_comment.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/gh-pages.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/__main__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/bot_comment.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/checkrelease.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/githubref.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/release.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/releasefile.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/templates/bot_comment.jinja.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/management/utils.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/mypy.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/tests.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.gitignore", "downloaded_repos/nrbnlulu_strawberry-django-auth/.pre-commit-config.yaml", "downloaded_repos/nrbnlulu_strawberry-django-auth/.vscode/settings.json", "downloaded_repos/nrbnlulu_strawberry-django-auth/CHANGELOG.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/CONTRIBUTING.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/CONTRIBUTORS.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/LICENSE", "downloaded_repos/nrbnlulu_strawberry-django-auth/README.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/changeslog_legacy.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/api.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/captcha.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/changelog.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/channels.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/contributing.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/contributors.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/data/api.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/images/demo.jpg", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/images/headers_graphiql.png", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/images/logo.png", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/images/token-auth.png", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/images/update_acount.png", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/images/verify-acount-screen-shot.png", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/index.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/overriding-email-templates.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/pre_build.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/relay.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/settings.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/docs/tutorial.md", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/admin.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/apps.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/captcha_factorty.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/create.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/fonts/Nehama.ttf", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/fonts/OpenSans-Semibold.ttf", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/fonts/pltwide.ttf", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/fonts/stam.ttf", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/models.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/captcha/types_.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/constants.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/directives.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/exceptions.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/interfaces.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/middlewares.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/mixins.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/scalars.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/types_.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/core/utils.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/jwt/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/jwt/tools.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/jwt/types_.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/migrations/0001_initial.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/migrations/0002_alter_userstatus_options.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/migrations/0003_delete_captcha.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/migrations/0004_captcha.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/migrations/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/models.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/py.typed", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/settings.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/settings_type.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/activation_email.html", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/activation_subject.txt", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/images/gd4f267f778b22183ca1ded4dfae871eac04faaac23286dc27d9c228034ff735042cebd167fb684c47ea3bef8803094f5683f08a3728911b4b191f82a7c12de99_1280.jpg", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/images/windrader-wind-power-fichtelberg-wind-park.jpg", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/password_reset_email.html", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/password_reset_subject.txt", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/password_set_email.html", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/templates/email/password_set_subject.txt", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/arg_mutations.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/forms.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/helpers.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/queries.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/relay.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/resolvers.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/signals.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/types_.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/views.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/hello.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/mkdocs.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/poetry.lock", "downloaded_repos/nrbnlulu_strawberry-django-auth/pyproject.toml", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/manage.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/quickstart/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/quickstart/asgi.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/quickstart/schema.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/quickstart/settings.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/quickstart/urls.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/quickstart/wsgi.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/run_daphne.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/templates/email/activation_email.html", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/templates/email/activation_subject.txt", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/templates/email/password_reset_email.html", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/templates/email/password_reset_subject.txt", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/users/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/users/apps.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/users/migrations/0001_initial.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/users/migrations/__init__.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/users/models.py", "downloaded_repos/nrbnlulu_strawberry-django-auth/quickstart/users.json", "downloaded_repos/nrbnlulu_strawberry-django-auth/taskfile.yml", "downloaded_repos/nrbnlulu_strawberry-django-auth/uv.lock"], "skipped": [{"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/.github/workflows/release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/demo.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_app_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_archive_account.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_auth_directives.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_captcha.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_delete_account.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_jwt.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_login.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_middlewares.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_password_change.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_password_reset.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_password_set.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_query.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_refresh_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_register.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_revoke_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_send_password_reset_email.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_update_account.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/test_verify_account.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/asgi.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/customuser/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/customuser/apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/customuser/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/customuser/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/customuser/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/migrate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/relay_schema.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/run_daphne.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/sample/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/sample/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/sample/apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/sample/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/sample/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/sample/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/schema.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/settings_b.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/templates/email/activation_email.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/templates/email/activation_subject.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/templates/email/password_reset_email.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/templates/email/password_reset_subject.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nrbnlulu_strawberry-django-auth/tests/testproject/urls.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7793450355529785, "profiling_times": {"config_time": 6.789770126342773, "core_time": 3.3754143714904785, "ignores_time": 0.0016739368438720703, "total_time": 10.168429613113403}, "parsing_time": {"total_time": 0.7053127288818359, "per_file_time": {"mean": 0.009280430643182052, "std_dev": 0.0002524337170503981}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 5.4331419467926025, "per_file_time": {"mean": 0.016666079591388352, "std_dev": 0.0029912804353709444}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.5757861137390137, "per_file_and_rule_time": {"mean": 0.0022131827440154688, "std_dev": 5.3183617350064965e-05}, "very_slow_stats": {"time_ratio": 0.06674447591176468, "count_ratio": 0.0014044943820224719}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/nrbnlulu_strawberry-django-auth/gqlauth/user/resolvers.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.10517501831054688}]}, "tainting_time": {"total_time": 0.4450953006744385, "per_def_and_rule_time": {"mean": 0.0003870393918908162, "std_dev": 1.5325192728457615e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089102016}, "engine_requested": "OSS", "skipped_rules": []}