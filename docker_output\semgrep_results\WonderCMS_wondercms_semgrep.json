{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 1043, "col": 16, "offset": 30395}, "end": {"line": 1043, "col": 71, "offset": 30450}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 1710, "col": 16, "offset": 49402}, "end": {"line": 1710, "col": 29, "offset": 49415}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 1713, "col": 15, "offset": 49490}, "end": {"line": 1713, "col": 28, "offset": 49503}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 1854, "col": 4, "offset": 54162}, "end": {"line": 1854, "col": 50, "offset": 54208}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 1929, "col": 4, "offset": 56676}, "end": {"line": 1929, "col": 24, "offset": 56696}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 2245, "col": 76, "offset": 65087}, "end": {"line": 2245, "col": 107, "offset": 65118}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 2250, "col": 4, "offset": 65181}, "end": {"line": 2250, "col": 17, "offset": 65194}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 2319, "col": 4, "offset": 66981}, "end": {"line": 2319, "col": 28, "offset": 67005}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 2352, "col": 42, "offset": 68380}, "end": {"line": 2352, "col": 80, "offset": 68418}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/WonderCMS_wondercms/index.php", "start": {"line": 3134, "col": 3, "offset": 101885}, "end": {"line": 3134, "col": 44, "offset": 101926}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/WonderCMS_wondercms/.github/CONTRIBUTING.md", "downloaded_repos/WonderCMS_wondercms/.github/FUNDING.yml", "downloaded_repos/WonderCMS_wondercms/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/WonderCMS_wondercms/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/WonderCMS_wondercms/.github/README.md", "downloaded_repos/WonderCMS_wondercms/.github/SECURITY.md", "downloaded_repos/WonderCMS_wondercms/.gitignore", "downloaded_repos/WonderCMS_wondercms/.htaccess", "downloaded_repos/WonderCMS_wondercms/index.php", "downloaded_repos/WonderCMS_wondercms/license", "downloaded_repos/WonderCMS_wondercms/themes/sky/css/fonts/catamaran-v7-latin-ext_latin-700.woff2", "downloaded_repos/WonderCMS_wondercms/themes/sky/css/fonts/catamaran-v7-latin-ext_latin-regular.woff2", "downloaded_repos/WonderCMS_wondercms/themes/sky/css/style.css", "downloaded_repos/WonderCMS_wondercms/themes/sky/theme.php", "downloaded_repos/WonderCMS_wondercms/themes/sky/wcms-modules.json", "downloaded_repos/WonderCMS_wondercms/version"], "skipped": [{"path": "downloaded_repos/WonderCMS_wondercms/tests/WcmsTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6435739994049072, "profiling_times": {"config_time": 5.762879371643066, "core_time": 5.290080785751343, "ignores_time": 0.002111196517944336, "total_time": 11.055861711502075}, "parsing_time": {"total_time": 0.23321986198425293, "per_file_time": {"mean": 0.058304965496063225, "std_dev": 0.007469373248721212}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.0504372119903564, "per_file_time": {"mean": 0.08473436699973216, "std_dev": 0.23158071192587323}, "very_slow_stats": {"time_ratio": 0.9610334503344846, "count_ratio": 0.027777777777777776}, "very_slow_files": [{"fpath": "downloaded_repos/WonderCMS_wondercms/index.php", "ftime": 2.931572198867798}]}, "matching_time": {"total_time": 1.534740686416626, "per_file_and_rule_time": {"mean": 0.03009295463562011, "std_dev": 0.006962116821575685}, "very_slow_stats": {"time_ratio": 0.6583627057173735, "count_ratio": 0.058823529411764705}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/WonderCMS_wondercms/index.php", "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 0.19302797317504883}, {"fpath": "downloaded_repos/WonderCMS_wondercms/index.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.3748319149017334}, {"fpath": "downloaded_repos/WonderCMS_wondercms/index.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.44255614280700684}]}, "tainting_time": {"total_time": 1.0229413509368896, "per_def_and_rule_time": {"mean": 0.0006608148261866211, "std_dev": 2.328589334345138e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}