{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/elementor_activity-log/classes/class-aal-activity-log-list-table.php", "start": {"line": 771, "col": 4, "offset": 23301}, "end": {"line": 773, "col": 37, "offset": 23416}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-callable.tainted-callable", "path": "downloaded_repos/elementor_activity-log/classes/class-aal-activity-log-list-table.php", "start": {"line": 790, "col": 4, "offset": 23907}, "end": {"line": 794, "col": 20, "offset": 24089}, "extra": {"message": "Callable based on user input risks remote code execution.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "owasp": ["A03:2021 - Injection"], "references": ["https://www.php.net/manual/en/language.types.callable.php"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-callable.tainted-callable", "shortlink": "https://sg.run/YGb33"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/elementor_activity-log/classes/class-aal-activity-log-list-table.php", "start": {"line": 790, "col": 4, "offset": 23907}, "end": {"line": 794, "col": 20, "offset": 24089}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/elementor_activity-log/.gitignore", "downloaded_repos/elementor_activity-log/.jshintrc", "downloaded_repos/elementor_activity-log/.travis.yml", "downloaded_repos/elementor_activity-log/Gruntfile.js", "downloaded_repos/elementor_activity-log/README.md", "downloaded_repos/elementor_activity-log/aryo-activity-log.php", "downloaded_repos/elementor_activity-log/assets/css/settings.css", "downloaded_repos/elementor_activity-log/assets/js/settings.js", "downloaded_repos/elementor_activity-log/bin/install-wp-tests.sh", "downloaded_repos/elementor_activity-log/classes/abstract-class-aal-exporter.php", "downloaded_repos/elementor_activity-log/classes/class-aal-activity-log-list-table.php", "downloaded_repos/elementor_activity-log/classes/class-aal-admin-ui.php", "downloaded_repos/elementor_activity-log/classes/class-aal-api.php", "downloaded_repos/elementor_activity-log/classes/class-aal-export.php", "downloaded_repos/elementor_activity-log/classes/class-aal-hooks.php", "downloaded_repos/elementor_activity-log/classes/class-aal-integration-woocommerce.php", "downloaded_repos/elementor_activity-log/classes/class-aal-maintenance.php", "downloaded_repos/elementor_activity-log/classes/class-aal-notifications.php", "downloaded_repos/elementor_activity-log/classes/class-aal-privacy.php", "downloaded_repos/elementor_activity-log/classes/class-aal-settings.php", "downloaded_repos/elementor_activity-log/exporters/class-aal-exporter-csv.php", "downloaded_repos/elementor_activity-log/hooks/abstract-class-aal-hook-base.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-attachments.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-comments.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-core.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-emails.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-export.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-menus.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-options.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-plugins.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-posts.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-taxonomies.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-themes.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-users.php", "downloaded_repos/elementor_activity-log/hooks/class-aal-hook-widgets.php", "downloaded_repos/elementor_activity-log/i18n/strings.php", "downloaded_repos/elementor_activity-log/notifications/abstract-class-aal-notification-base.php", "downloaded_repos/elementor_activity-log/notifications/class-aal-notification-email.php", "downloaded_repos/elementor_activity-log/package-lock.json", "downloaded_repos/elementor_activity-log/package.json", "downloaded_repos/elementor_activity-log/phpunit.xml", "downloaded_repos/elementor_activity-log/readme.txt", "downloaded_repos/elementor_activity-log/screenshot-1.png", "downloaded_repos/elementor_activity-log/screenshot-2.png", "downloaded_repos/elementor_activity-log/screenshot-3.png"], "skipped": [{"path": "downloaded_repos/elementor_activity-log/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/elementor_activity-log/tests/test-base.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6594839096069336, "profiling_times": {"config_time": 5.747770547866821, "core_time": 2.8309569358825684, "ignores_time": 0.002225160598754883, "total_time": 8.581854820251465}, "parsing_time": {"total_time": 0.559312105178833, "per_file_time": {"mean": 0.015536447366078692, "std_dev": 0.0003727714467515063}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.0785584449768066, "per_file_time": {"mean": 0.016496495595054023, "std_dev": 0.0032545015677865013}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.8104522228240967, "per_file_and_rule_time": {"mean": 0.006868239176475395, "std_dev": 0.00026878100863122665}, "very_slow_stats": {"time_ratio": 0.15788094136322278, "count_ratio": 0.00847457627118644}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/elementor_activity-log/classes/class-aal-activity-log-list-table.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.12795495986938477}]}, "tainting_time": {"total_time": 0.2326064109802246, "per_def_and_rule_time": {"mean": 0.0003915932844784926, "std_dev": 4.3412314677912245e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}