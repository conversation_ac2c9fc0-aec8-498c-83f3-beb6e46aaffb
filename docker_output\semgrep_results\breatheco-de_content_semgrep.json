{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/breatheco-de_content/create-images.js", "start": {"line": 57, "col": 29, "offset": 1767}, "end": {"line": 57, "col": 32, "offset": 1770}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/breatheco-de_content/create-images.js", "start": {"line": 57, "col": 34, "offset": 1772}, "end": {"line": 57, "col": 38, "offset": 1776}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/breatheco-de_content/generate-json.js", "start": {"line": 12, "col": 27, "offset": 356}, "end": {"line": 12, "col": 30, "offset": 359}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/breatheco-de_content/generate-json.js", "start": {"line": 12, "col": 32, "offset": 361}, "end": {"line": 12, "col": 36, "offset": 365}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/breatheco-de_content/src/content/lesson/token-based-api-authentication.es.md", "start": {"line": 164, "col": 19, "offset": 8134}, "end": {"line": 164, "col": 68, "offset": 8183}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/breatheco-de_content/src/content/lesson/token-based-api-authentication.md", "start": {"line": 162, "col": 19, "offset": 7556}, "end": {"line": 162, "col": 68, "offset": 7605}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/breatheco-de_content/src/utils/commands/download_images.js", "start": {"line": 44, "col": 38, "offset": 1702}, "end": {"line": 44, "col": 73, "offset": 1737}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/breatheco-de_content/src/utils/commands/localize_images.js", "start": {"line": 51, "col": 38, "offset": 1921}, "end": {"line": 51, "col": 73, "offset": 1956}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/breatheco-de_content/src/utils/commands/localize_images.js", "start": {"line": 88, "col": 43, "offset": 3364}, "end": {"line": 88, "col": 59, "offset": 3380}, "extra": {"message": "RegExp() called with a `lesson` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/breatheco-de_content/src/utils/commands/localize_images.js", "start": {"line": 88, "col": 43, "offset": 3364}, "end": {"line": 88, "col": 59, "offset": 3380}, "extra": {"message": "RegExp() called with a `type` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/breatheco-de_content/src/utils/commands/sanitize_lesson.js", "start": {"line": 33, "col": 38, "offset": 1179}, "end": {"line": 33, "col": 73, "offset": 1214}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/breatheco-de_content/src/utils/commands/update_lesson.js", "start": {"line": 49, "col": 38, "offset": 2148}, "end": {"line": 49, "col": 73, "offset": 2183}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/breatheco-de_content/src/utils/files.js", "start": {"line": 23, "col": 29, "offset": 709}, "end": {"line": 23, "col": 32, "offset": 712}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/breatheco-de_content/src/utils/files.js", "start": {"line": 23, "col": 34, "offset": 714}, "end": {"line": 23, "col": 38, "offset": 718}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/breatheco-de_content/src/utils/files.js", "start": {"line": 218, "col": 20, "offset": 6493}, "end": {"line": 218, "col": 59, "offset": 6532}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/breatheco-de_content/.github/ISSUE_TEMPLATE/blog_article_4Geeks.md", "downloaded_repos/breatheco-de_content/.github/settings.yml", "downloaded_repos/breatheco-de_content/.gitignore", "downloaded_repos/breatheco-de_content/.gitpod.yml", "downloaded_repos/breatheco-de_content/.vscode/settings.json", "downloaded_repos/breatheco-de_content/CONTRIBUTING.md", "downloaded_repos/breatheco-de_content/README.md", "downloaded_repos/breatheco-de_content/code-review-guidelines.md", "downloaded_repos/breatheco-de_content/create-images.js", "downloaded_repos/breatheco-de_content/environment-varilables-explained.md", "downloaded_repos/breatheco-de_content/generate-json.js", "downloaded_repos/breatheco-de_content/package-lock.json", "downloaded_repos/breatheco-de_content/package.json", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/Bootstrap.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/Cheat-Sheet-HTML.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/HTML5-Cheat-Sheet.png", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/JAVA.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/JAVASCRIPT-DOM.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/JQuert-Ajax.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/JQuert-Core.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/JQuery-CSS-Cheat-Sheet.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/SQL-1.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/disabled-error-message.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/key-codes.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/.gitkeep", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/Bootstrap.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/Cheat-Sheet-HTML.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/GIT-Cheat-Sheet.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/JAVA.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/JAVASCRIPT-DOM.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/JQuert-Ajax.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/JQuert-Core.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/JQuert-Events.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/JQuery-CSS-Cheat-Sheet.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/SQL-1.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/assets/thumb/key-codes.jpg", "downloaded_repos/breatheco-de_content/src/assets/assets/preview/google.png", "downloaded_repos/breatheco-de_content/src/assets/breathecode.32.png", "downloaded_repos/breatheco-de_content/src/assets/breathecode.45.png", "downloaded_repos/breatheco-de_content/src/assets/breathecode.png", "downloaded_repos/breatheco-de_content/src/assets/images/01868f7d-4949-4e15-85da-8042ea24a11a.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/032a818d-e4d7-4276-8195-ce5d8a3edcf6.png", "downloaded_repos/breatheco-de_content/src/assets/images/0352d1b1-0914-4d0f-9379-c47e7895ccb9.png", "downloaded_repos/breatheco-de_content/src/assets/images/03d2c2c4-f510-4088-9bb0-1665dbfe8a68modalwindowhooks.gif", "downloaded_repos/breatheco-de_content/src/assets/images/03ed6b76-0ee0-4b04-bd45-0fb58ae6f800.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/05a36362-c09f-4961-a7b9-8e3132a902b1.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/08e78606-102f-4bc2-a066-7c26ae9400d5.png", "downloaded_repos/breatheco-de_content/src/assets/images/09294580-51b9-4591-9c2c-57c729ad02d1.gif", "downloaded_repos/breatheco-de_content/src/assets/images/092d375f-89c9-4cc9-8aa7-8cc4d1230179.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/0c4fa020-02f6-4ec0-bfeb-a6292145a153.gif", "downloaded_repos/breatheco-de_content/src/assets/images/0c89a48e-d488-4e5c-807a-fd6b9a9179f6.png", "downloaded_repos/breatheco-de_content/src/assets/images/0e52ff67-aa2b-4234-b620-6ce094508f67.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/10ce9a67-1060-4550-a824-5087688d0630.png", "downloaded_repos/breatheco-de_content/src/assets/images/11fcd6d8-6177-4f42-b4e0-7b6475f24b0a.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/1251c891-ac88-464f-ae58-5c9d7abe081cScreenShot20190524at121150PM.png", "downloaded_repos/breatheco-de_content/src/assets/images/12ff6e40-706f-47ff-9ada-53dada968eaf.png", "downloaded_repos/breatheco-de_content/src/assets/images/153d2a7c-b648-4d75-920b-940102f18eaa.gif", "downloaded_repos/breatheco-de_content/src/assets/images/16dbf0c1-afa2-418c-a1b6-3bc8cb1d5c81.png", "downloaded_repos/breatheco-de_content/src/assets/images/182ea93c-9d7d-4c8d-8153-9c1756d8cd1f.png", "downloaded_repos/breatheco-de_content/src/assets/images/18da88b0-403c-4330-a7d6-661307f062e5.png", "downloaded_repos/breatheco-de_content/src/assets/images/1b7f5dc4-029a-475d-8bfd-fac1b739966c.png", "downloaded_repos/breatheco-de_content/src/assets/images/1c00bd95-1359-4fd5-8399-b1b80a769009.png", "downloaded_repos/breatheco-de_content/src/assets/images/205cd2de-dfae-4712-a5e4-1c922994e60d.png", "downloaded_repos/breatheco-de_content/src/assets/images/2387325b-338c-4c18-bb0f-2f95ed28901f.png", "downloaded_repos/breatheco-de_content/src/assets/images/245ba798-e840-42d8-8391-7388159ccfeb.png", "downloaded_repos/breatheco-de_content/src/assets/images/251dd226-0590-4e79-bde1-9973159a2c7a.png", "downloaded_repos/breatheco-de_content/src/assets/images/2b3ed62a-070f-4e7f-9572-34628ffb40d9.png", "downloaded_repos/breatheco-de_content/src/assets/images/2c0000ef-2907-43cb-80ed-2ba4f194b83e.gif", "downloaded_repos/breatheco-de_content/src/assets/images/2cdb146a-d6f7-4591-96fc-e50aef07aca5.png", "downloaded_repos/breatheco-de_content/src/assets/images/2de93dfc-263c-43e3-afa5-6557a5e7cf4c.png", "downloaded_repos/breatheco-de_content/src/assets/images/2e1dfd98-a969-4ad1-8ed3-23626f07be1d.png", "downloaded_repos/breatheco-de_content/src/assets/images/2fef4d85-686b-4bf0-a505-45d3de178fd5.gif", "downloaded_repos/breatheco-de_content/src/assets/images/335ed387-cbf9-4ffa-9529-1ccf2084e393.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/374d53ac-092f-4f34-a6f1-76bfaa5bd676.png", "downloaded_repos/breatheco-de_content/src/assets/images/3884f515-dd7a-48f2-b238-9e2ec26de02d.png", "downloaded_repos/breatheco-de_content/src/assets/images/38fed925-cf75-4f94-bdd7-abcce659fdac.gif", "downloaded_repos/breatheco-de_content/src/assets/images/39d36b52-330f-4ce9-beab-2004e325749c.png", "downloaded_repos/breatheco-de_content/src/assets/images/39f93d0d-248e-4e94-b402-b744c4b06a4d.png", "downloaded_repos/breatheco-de_content/src/assets/images/3e50e217-514d-41dc-a7a4-4725e08f8afb.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/40818d0d-60c6-4ef3-a488-834f21ddebf1.png", "downloaded_repos/breatheco-de_content/src/assets/images/41afcd74-81dd-4e6e-98ee-fc2642a07e7f.png", "downloaded_repos/breatheco-de_content/src/assets/images/41f4a2be-380f-47af-acab-d479acf80921todolisthooks.gif", "downloaded_repos/breatheco-de_content/src/assets/images/47f7c628-5efe-4888-b67c-ca90611e788e.gif", "downloaded_repos/breatheco-de_content/src/assets/images/4889ebd9-201f-46c7-a1fb-d3d8c2f4493e.png", "downloaded_repos/breatheco-de_content/src/assets/images/4a25cfd5-e8ab-4abb-b4f8-148d376b3f3d.gif", "downloaded_repos/breatheco-de_content/src/assets/images/4f549fb5-d4c8-4e31-b63c-34426b675b92.gif", "downloaded_repos/breatheco-de_content/src/assets/images/51a4c486-2a08-4471-b2b5-80e32ce41abc.png", "downloaded_repos/breatheco-de_content/src/assets/images/54a062a9-1b37-4d49-ae22-a23d91ad600f.png", "downloaded_repos/breatheco-de_content/src/assets/images/576d4b0e-8b35-493d-879c-4d8f914c585f.png", "downloaded_repos/breatheco-de_content/src/assets/images/57821cbc-5ba0-4e1c-9b90-7f1df7c637fa.gif", "downloaded_repos/breatheco-de_content/src/assets/images/596b5833-09a1-4ff0-8718-bc7ba4dd995dScreenShot20190524at42229PM.png", "downloaded_repos/breatheco-de_content/src/assets/images/59bf0e4e-f5cf-410c-b2bc-fdc7472e7cdc.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/5a432982-f8b2-42bb-89c5-3c82a8e53d10.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/5e975e91-1447-4117-b50b-b00df99a88a5.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/5f5f59bc-9efa-4ee9-bce6-6af9eedb4738.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/61212ca7-cde0-43c1-8267-a1101a95da2c.png", "downloaded_repos/breatheco-de_content/src/assets/images/6128e9f7-b460-4b10-80e4-34057b7d4df9.png", "downloaded_repos/breatheco-de_content/src/assets/images/61908aa1-63a5-40b1-8a53-8c7b202d4ef3.png", "downloaded_repos/breatheco-de_content/src/assets/images/62897396-651c-4bdb-8604-dfe1898e57bd.png", "downloaded_repos/breatheco-de_content/src/assets/images/6351de1c-6d90-4502-8823-4b751981db9f.png", "downloaded_repos/breatheco-de_content/src/assets/images/655a85b3-660f-45bf-8563-2bcbe13bf0e5.gif", "downloaded_repos/breatheco-de_content/src/assets/images/6891485c-2a5a-4722-a7dc-f108993c18ba.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/6b4upqv6at321.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/6bcba673-a543-4bf1-a80b-083914b91bef.png", "downloaded_repos/breatheco-de_content/src/assets/images/6c7d3747-482a-480d-b5be-fdbf095292f3.png", "downloaded_repos/breatheco-de_content/src/assets/images/6cf4655f-665f-4f68-b021-f34238cedd69.png", "downloaded_repos/breatheco-de_content/src/assets/images/6d44c6a9-7f32-4b0e-86d7-1a210c3a5f4a.gif", "downloaded_repos/breatheco-de_content/src/assets/images/6f07bc8b-6d21-46e6-8710-34992df2508b.png", "downloaded_repos/breatheco-de_content/src/assets/images/6f51ce02-3a75-4027-ada5-cf63c50d1701.png", "downloaded_repos/breatheco-de_content/src/assets/images/6fd2b44b-598b-4ddb-85ba-9c32b086127f.png", "downloaded_repos/breatheco-de_content/src/assets/images/709ff7ce-f7f6-4b16-a172-521fe1787733bitcoing_prices_table.png", "downloaded_repos/breatheco-de_content/src/assets/images/72fe5361-5b2a-460f-8c2a-2d376616abf6.png", "downloaded_repos/breatheco-de_content/src/assets/images/73edbb82-467c-4522-af7d-79c33bb270e2.png", "downloaded_repos/breatheco-de_content/src/assets/images/75ca521c-2ea0-4a7d-ad05-6717200d8553.png", "downloaded_repos/breatheco-de_content/src/assets/images/763d40a2-d4ea-4cf5-a2cd-dc777f71efcbtimerreacthooks.gif", "downloaded_repos/breatheco-de_content/src/assets/images/77c93bfa-92cb-44e3-a7c5-c959e27c5ccc.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/78aedd23-b5dd-4d1e-b693-b3268f4734fa.png", "downloaded_repos/breatheco-de_content/src/assets/images/79da625d-d7bc-409a-a044-9e60ae6a1a2b.png", "downloaded_repos/breatheco-de_content/src/assets/images/7e098348-df50-442b-801e-ac9d098fbc09.png", "downloaded_repos/breatheco-de_content/src/assets/images/7ed2c414-0d00-4e68-b659-b65c26d1983a.png", "downloaded_repos/breatheco-de_content/src/assets/images/7f627fe6-aa37-4450-bbff-dc4ea0ce8309.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/80444105-0d6a-4a93-beb1-090b84b03376.png", "downloaded_repos/breatheco-de_content/src/assets/images/84c4d84c-51b9-4906-a572-71cc07ecfc8c.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/85cedee2-3a9f-49b5-8bc8-d72e3fd739fc.png", "downloaded_repos/breatheco-de_content/src/assets/images/87f2be86-32c3-4bfc-8db4-dbd0d979e4d3.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/88f6b44f-01dc-4a13-ba1f-fbab5280d510.png", "downloaded_repos/breatheco-de_content/src/assets/images/8c053abc-2f09-47e9-b586-751538d180c4.png", "downloaded_repos/breatheco-de_content/src/assets/images/8c9fea86-c56c-486f-8b64-4322338076f7.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/8e339149-97ae-4b15-ba59-a3ddf3777525.png", "downloaded_repos/breatheco-de_content/src/assets/images/8e9ff37a-28f7-4179-8f5d-9278ff7efd55.png", "downloaded_repos/breatheco-de_content/src/assets/images/9021be43-57ae-4667-8c1a-435b8521ce59.png", "downloaded_repos/breatheco-de_content/src/assets/images/91aa197c-b2cb-4717-b41f-0f7b579aab25.gif", "downloaded_repos/breatheco-de_content/src/assets/images/91bcc549-6758-49c3-a790-4245afbd8ece.png", "downloaded_repos/breatheco-de_content/src/assets/images/924b8807-6a06-44e7-87a1-8c93d983d873.png", "downloaded_repos/breatheco-de_content/src/assets/images/945ae0a2-2495-4955-9e9a-46fdd3efc682componentlifecyclehooks.png", "downloaded_repos/breatheco-de_content/src/assets/images/94f4a28c-a93c-4e05-9f86-ce64abc2ff7b.png", "downloaded_repos/breatheco-de_content/src/assets/images/97f74cd8-acdd-4ce9-aa26-bfd494e9b550bitcoin_price_csv.png", "downloaded_repos/breatheco-de_content/src/assets/images/980ce2e0-b73e-4019-8e97-3510e3028e10.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/98208ebb-dcb3-4e40-9ae4-4ec886213f97.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/9edb713a-3a80-442a-9fc5-dd5caa9da62fScreenShot20190524at114329AM.png", "downloaded_repos/breatheco-de_content/src/assets/images/9fa13314-24cc-4a4b-9676-e60616f73602.gif", "downloaded_repos/breatheco-de_content/src/assets/images/Captura de pantalla 2020-12-15 a las 19.02.01.png", "downloaded_repos/breatheco-de_content/src/assets/images/JzNqq1W.png", "downloaded_repos/breatheco-de_content/src/assets/images/LearnPack Instructions — exercise-postcard [Codespaces_ supreme space robot] — Visual Studio Code.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/Release-v1-1-12-·-coreybutler-nvm-windows.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/a2a08a9f-5a5c-415e-93d6-c8a45aecb23d.png", "downloaded_repos/breatheco-de_content/src/assets/images/a4fc1953-3c91-4bf4-a70a-ab4778b3277b.png", "downloaded_repos/breatheco-de_content/src/assets/images/aa1a5994-8de9-4d24-99ce-3a0d686c30bd.png", "downloaded_repos/breatheco-de_content/src/assets/images/abrir-en-learnpack.png", "downloaded_repos/breatheco-de_content/src/assets/images/af7344fc-0ee0-499e-8926-8f70dc9b2b0d.png", "downloaded_repos/breatheco-de_content/src/assets/images/af747595-cf02-42ca-a6bf-00c0c512ef40reactcounterhooks.gif", "downloaded_repos/breatheco-de_content/src/assets/images/angry-baby.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/authentication-diagram.png", "downloaded_repos/breatheco-de_content/src/assets/images/authentication.png", "downloaded_repos/breatheco-de_content/src/assets/images/avatar.png", "downloaded_repos/breatheco-de_content/src/assets/images/b04c5254-086a-4b9f-8b86-0cf95fcc3fcddanabramov.png", "downloaded_repos/breatheco-de_content/src/assets/images/b5e8d1ef-5385-4923-aaf9-b24130405a9a.gif", "downloaded_repos/breatheco-de_content/src/assets/images/b88d7b26-2786-4a3a-b6ee-4b6b84ad6ecf.png", "downloaded_repos/breatheco-de_content/src/assets/images/b929f233-00b2-406f-87a5-ee74146cfd85.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/b96ddb9a-e076-4e27-b18e-f3fd7a256ad2.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/bc337938-55c4-40e2-a370-5d69bf084a3b.png", "downloaded_repos/breatheco-de_content/src/assets/images/bd90ba64-ded5-4fb2-b23f-7d297125e3a5.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/c315bebc-2e8d-4e71-a2df-25ad3618ac3dgithubbackground.jpg.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/c558ac72-194b-40f2-be2f-6e65e8c219c9.png", "downloaded_repos/breatheco-de_content/src/assets/images/c60c4713-aa60-4fbc-9d97-f893b5947e7f.gif", "downloaded_repos/breatheco-de_content/src/assets/images/c7e96be7-a7b6-4b6d-83a2-535b22fdc3b0.png", "downloaded_repos/breatheco-de_content/src/assets/images/c7eb4466-eaa5-4d33-8181-5b4c5df4e7f8.png", "downloaded_repos/breatheco-de_content/src/assets/images/c98f9241-0c8f-46d9-91e6-a093276ac2a3.png", "downloaded_repos/breatheco-de_content/src/assets/images/ca5d4285-c151-4b30-a4a2-c6b35510188b.png", "downloaded_repos/breatheco-de_content/src/assets/images/cdbe1bf9-2b6b-4c21-b127-eacc681d9c8d.png", "downloaded_repos/breatheco-de_content/src/assets/images/ce06fb9d-bc8c-4191-ae12-a2ec4ac6fa1f.png", "downloaded_repos/breatheco-de_content/src/assets/images/commit-object2.png", "downloaded_repos/breatheco-de_content/src/assets/images/controlled-input-example.gif", "downloaded_repos/breatheco-de_content/src/assets/images/create-codespace.gif", "downloaded_repos/breatheco-de_content/src/assets/images/current-time-gif.gif", "downloaded_repos/breatheco-de_content/src/assets/images/d1347307-d440-464f-a793-7a457e9903adcontrolledinputreact.gif", "downloaded_repos/breatheco-de_content/src/assets/images/d29be460-cc2e-42e6-bf92-f9516fd7b21a.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/d2ca9eb7-a3f9-432d-b791-8b1266f8923a.gif", "downloaded_repos/breatheco-de_content/src/assets/images/danabramov.png", "downloaded_repos/breatheco-de_content/src/assets/images/data-structures-python.png", "downloaded_repos/breatheco-de_content/src/assets/images/database-model.png", "downloaded_repos/breatheco-de_content/src/assets/images/db660bb9-1ac6-4730-a9c8-4544d6b801b0.png", "downloaded_repos/breatheco-de_content/src/assets/images/dccad91a-93b3-49c9-a437-6612087b9ee4.png", "downloaded_repos/breatheco-de_content/src/assets/images/ddb1ff5a-621c-4945-9164-20c1a7f5d388.gif", "downloaded_repos/breatheco-de_content/src/assets/images/disciplines_differences.png", "downloaded_repos/breatheco-de_content/src/assets/images/download-nvm-border-compressed.png", "downloaded_repos/breatheco-de_content/src/assets/images/e15c594c-9b46-4c27-bf5a-a5bbb5ef952a.png", "downloaded_repos/breatheco-de_content/src/assets/images/e16d59ad-4c11-4ca0-8bfc-5a9d147c6c2e.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/e590a615-2c9d-4671-8483-99dbdd90cd41.png", "downloaded_repos/breatheco-de_content/src/assets/images/e59de5e1-2751-4286-adfb-69c047e93058.png", "downloaded_repos/breatheco-de_content/src/assets/images/e6ca8daf-9f02-487b-8296-1f440a4e6e59storage_background.jpg.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/e73b673e-d744-45a7-a1ed-61a1dae49560.png", "downloaded_repos/breatheco-de_content/src/assets/images/e8781e98-0f8c-4035-8017-33ca217eb39c.gif", "downloaded_repos/breatheco-de_content/src/assets/images/e8d3d49d-8c7e-4bc2-88e0-15b95a8ccc54.png", "downloaded_repos/breatheco-de_content/src/assets/images/ecb49b67-f513-49b3-bd4a-dd7cc44e9bce.gif", "downloaded_repos/breatheco-de_content/src/assets/images/ed1c57e1-5c67-4bf1-96ed-1fb2353fb2ca.gif", "downloaded_repos/breatheco-de_content/src/assets/images/ed2a2bfb-95eb-473f-af7c-aa9f1d4c055e.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/env-files.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/f2021d8d-193d-4482-b189-ae0005b1bd88.png", "downloaded_repos/breatheco-de_content/src/assets/images/f7b8c75d-e7d4-481e-8346-b95d54a235f6.png", "downloaded_repos/breatheco-de_content/src/assets/images/f9bc68cd-e407-4d55-afd6-ba95b0c8bc02.png", "downloaded_repos/breatheco-de_content/src/assets/images/faaa70b0-5343-43f0-8565-994c9b40ab8b.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/fdb86b48-fb0b-4841-8d4d-60d4dbf4d70c.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/ffe440de-8746-4ab5-81cc-37ef107155e9.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/flux-simple-f8-diagram-1300w.png", "downloaded_repos/breatheco-de_content/src/assets/images/github-gitpod.png", "downloaded_repos/breatheco-de_content/src/assets/images/github-profile.png", "downloaded_repos/breatheco-de_content/src/assets/images/githubbackground.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/gitpod.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-1-global-variables.png", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-2-variable-names-python.png", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-2-variable-names.png", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-3,5-avoid-else-statements.png", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-3-smaller-functions.png", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-4-avoid-else-statements-python.png", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-4-too-many-comments.png", "downloaded_repos/breatheco-de_content/src/assets/images/good-bad-example-code-5-avoid-long-lines.png", "downloaded_repos/breatheco-de_content/src/assets/images/how-gitpod-works.jpeg", "downloaded_repos/breatheco-de_content/src/assets/images/how_to_run_javascript_1.png", "downloaded_repos/breatheco-de_content/src/assets/images/how_to_run_javascript_2.png", "downloaded_repos/breatheco-de_content/src/assets/images/how_to_run_javascript_3.png", "downloaded_repos/breatheco-de_content/src/assets/images/how_to_run_javascript_4.png", "downloaded_repos/breatheco-de_content/src/assets/images/http-0.png", "downloaded_repos/breatheco-de_content/src/assets/images/http-1.png", "downloaded_repos/breatheco-de_content/src/assets/images/http-2.png", "downloaded_repos/breatheco-de_content/src/assets/images/http-3.png", "downloaded_repos/breatheco-de_content/src/assets/images/js.png", "downloaded_repos/breatheco-de_content/src/assets/images/jwt-toke-structure.png", "downloaded_repos/breatheco-de_content/src/assets/images/jwt-token-structure.png", "downloaded_repos/breatheco-de_content/src/assets/images/jwt-vs-bearer-token.png", "downloaded_repos/breatheco-de_content/src/assets/images/learnpack-alert.e.png", "downloaded_repos/breatheco-de_content/src/assets/images/learnpack-alert.png", "downloaded_repos/breatheco-de_content/src/assets/images/learnpack-explanation.e.png", "downloaded_repos/breatheco-de_content/src/assets/images/learnpack-explanation.png", "downloaded_repos/breatheco-de_content/src/assets/images/logo-Forbes.png", "downloaded_repos/breatheco-de_content/src/assets/images/nano_editor.png", "downloaded_repos/breatheco-de_content/src/assets/images/node-expressjs.png", "downloaded_repos/breatheco-de_content/src/assets/images/node-typeorm.png", "downloaded_repos/breatheco-de_content/src/assets/images/nvidia.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/nvm-installation-success.png", "downloaded_repos/breatheco-de_content/src/assets/images/oJEe61z.png", "downloaded_repos/breatheco-de_content/src/assets/images/open-codespace.png", "downloaded_repos/breatheco-de_content/src/assets/images/open-in-learnpack.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/php.png", "downloaded_repos/breatheco-de_content/src/assets/images/python-floor-division-video.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/python-floor-division.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/python.png", "downloaded_repos/breatheco-de_content/src/assets/images/quality-assurance-cycle.png", "downloaded_repos/breatheco-de_content/src/assets/images/react-counter-usestate-example.gif", "downloaded_repos/breatheco-de_content/src/assets/images/rest-api.png", "downloaded_repos/breatheco-de_content/src/assets/images/resume.png", "downloaded_repos/breatheco-de_content/src/assets/images/resume2.png", "downloaded_repos/breatheco-de_content/src/assets/images/sql-1.png", "downloaded_repos/breatheco-de_content/src/assets/images/sql-2.png", "downloaded_repos/breatheco-de_content/src/assets/images/sql-3.png", "downloaded_repos/breatheco-de_content/src/assets/images/sql-4.png", "downloaded_repos/breatheco-de_content/src/assets/images/storage_background.jpg", "downloaded_repos/breatheco-de_content/src/assets/images/terminal-command.png", "downloaded_repos/breatheco-de_content/src/assets/images/terminal.png", "downloaded_repos/breatheco-de_content/src/assets/images/tran-1.png", "downloaded_repos/breatheco-de_content/src/assets/images/types-of-testing.png", "downloaded_repos/breatheco-de_content/src/assets/images/typescript.png", "downloaded_repos/breatheco-de_content/src/assets/images/unit-test-scenarios.png", "downloaded_repos/breatheco-de_content/src/assets/images/unit-test1-example.png", "downloaded_repos/breatheco-de_content/src/assets/images/unit-test1.png", "downloaded_repos/breatheco-de_content/src/assets/images/wrong-stylesheet-404.png", "downloaded_repos/breatheco-de_content/src/assets/svg/databaseModel.svg", "downloaded_repos/breatheco-de_content/src/assets/svg/en.svg", "downloaded_repos/breatheco-de_content/src/assets/svg/es.svg", "downloaded_repos/breatheco-de_content/src/assets/thumbs/.gitkeep", "downloaded_repos/breatheco-de_content/src/content/error/hola-error.md", "downloaded_repos/breatheco-de_content/src/content/how-to/[draft]how-to-search-in-google.md", "downloaded_repos/breatheco-de_content/src/content/how-to/anadir-columna-dataframe-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/array-de-objetos-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/array-vacio-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/ask.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/ask.md", "downloaded_repos/breatheco-de_content/src/content/how-to/clonar-array-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/clone-array-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/como-activar-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/como-crear-diagramas-de-clases.md", "downloaded_repos/breatheco-de_content/src/content/how-to/como-instalar-nvm-en-linux.md", "downloaded_repos/breatheco-de_content/src/content/how-to/como-instalar-pyenv-windows.md", "downloaded_repos/breatheco-de_content/src/content/how-to/como-instalar-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/como-usar-el-metodo-merge-pandas.md", "downloaded_repos/breatheco-de_content/src/content/how-to/convert-list-to-string-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/crear-dataframe.md", "downloaded_repos/breatheco-de_content/src/content/how-to/create-a-snapshot-gitpod-workspace.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/create-a-snapshot-gitpod-workspace.md", "downloaded_repos/breatheco-de_content/src/content/how-to/empty-an-array-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/every-method-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/expresiones-regulares-ejemplos.md", "downloaded_repos/breatheco-de_content/src/content/how-to/expresiones-regulares-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/expresiones-regulares-linux.md", "downloaded_repos/breatheco-de_content/src/content/how-to/expresiones-regulares-php.md", "downloaded_repos/breatheco-de_content/src/content/how-to/expresiones-regulares-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/github-clone-repository.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/github-clone-repository.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-call-a-function-in-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-check-python-version.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-code-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-code-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-comment-multiple-lines-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-concatenate-strings-on-python.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-concatenate-strings-on-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-create-an-object-in-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-create-uml-class-diagrams.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-disable-javascript-in-tor.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-exit-python-in-terminal.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-get-python-list-length.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-get-python-list-length.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-install-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-install-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-install-postgresql-on-windows.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-install-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-multiply-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-print-in-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-read-a-file-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-read-a-file-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-reverse-a-list-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-reverse-a-list-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-reverse-string-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-reverse-string-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-review-code.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-review-code.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-round-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-run-javascript-in-visual-studio-code.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-run-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-split-a-string-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-split-a-string-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-square-a-number-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-update-python-in-terminal.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-update-python-in-terminal.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-update-python-on-mac.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-update-python-on-windows.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-update-python-version.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-use-font-awesome.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-use-font-awesome.md", "downloaded_repos/breatheco-de_content/src/content/how-to/how-to-use-react-global-context.md", "downloaded_repos/breatheco-de_content/src/content/how-to/include-bootstrap-on-your-website-project-template.md", "downloaded_repos/breatheco-de_content/src/content/how-to/instalar-pandas-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/instalar-pandas-python.us.md", "downloaded_repos/breatheco-de_content/src/content/how-to/instalar-pyenv-linux.md", "downloaded_repos/breatheco-de_content/src/content/how-to/instalar-pyenv-mac.md", "downloaded_repos/breatheco-de_content/src/content/how-to/install-git-on-windows-macos-and-linux.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/install-git-on-windows-macos-and-linux.md", "downloaded_repos/breatheco-de_content/src/content/how-to/install-node-npm-mac-osx.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/install-node-npm-mac-osx.md", "downloaded_repos/breatheco-de_content/src/content/how-to/install-nvm-linux.md", "downloaded_repos/breatheco-de_content/src/content/how-to/install-nvm-on-every-operating-system.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/install-nvm-on-every-operating-system.md", "downloaded_repos/breatheco-de_content/src/content/how-to/inteligencia-artificial-generativa.md", "downloaded_repos/breatheco-de_content/src/content/how-to/introduccion-a-los-diagramas-de-clases.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-array-clear.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-array-flatten.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-array-intersection.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-array-last.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-array-slice.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-array-some-method.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-fill-method.md", "downloaded_repos/breatheco-de_content/src/content/how-to/javascript-string-replace.md", "downloaded_repos/breatheco-de_content/src/content/how-to/metodo-apply-pandas.md", "downloaded_repos/breatheco-de_content/src/content/how-to/metodo-drop-pandas.md", "downloaded_repos/breatheco-de_content/src/content/how-to/metodo-pivot-pandas.md", "downloaded_repos/breatheco-de_content/src/content/how-to/metodo-reduce-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/metodo-substring-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/nvm-install-windows.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/nvm-install-windows.md", "downloaded_repos/breatheco-de_content/src/content/how-to/pandas-concat.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/pandas-data-cleaning.md", "downloaded_repos/breatheco-de_content/src/content/how-to/pandas-dataframe-tutorial.md", "downloaded_repos/breatheco-de_content/src/content/how-to/pandas-drop-column.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/pandas-python-ejemplos.md", "downloaded_repos/breatheco-de_content/src/content/how-to/portafolio-desarrollador-web.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/portafolio-github.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/python-list-extend-method.md", "downloaded_repos/breatheco-de_content/src/content/how-to/que-es-pyenv-y-como-instalar-pyenv.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/que-se-puede-hacer-con-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/que-se-puede-hacer-con-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/react-createroot-vs-render.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/react-createroot-vs-render.md", "downloaded_repos/breatheco-de_content/src/content/how-to/recorrer-array-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex-examples.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex-for-end-of-line.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex-for-whitespaces.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex-letter.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex-negative-lookahead.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex-para-no-match.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex-wildcard.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regex.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/regular-expression-examples.md", "downloaded_repos/breatheco-de_content/src/content/how-to/rounded-image-using-div-instead-of-img-tag.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/rounded-image-using-div-instead-of-img-tag.md", "downloaded_repos/breatheco-de_content/src/content/how-to/settimeout-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/split-string-en-javascript.md", "downloaded_repos/breatheco-de_content/src/content/how-to/sqlalchemy-join.md", "downloaded_repos/breatheco-de_content/src/content/how-to/welcome-to-github.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/welcome-to-github.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-does-double-equal-sign-mean-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-does-double-slash-mean-in-python-floor.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-does-mean-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-is-Javascript-used-for.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-is-an-expression-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-is-float-in-python.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-is-pyenv-how-to-install.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-is-python-used-for.es.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-is-python-used-for.md", "downloaded_repos/breatheco-de_content/src/content/how-to/what-is-the-difference-between-java-and-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/4geeks-method-the-assignments.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/4geeks-method-the-assignments.md", "downloaded_repos/breatheco-de_content/src/content/lesson/[draft]build-your-first-resume.md", "downloaded_repos/breatheco-de_content/src/content/lesson/[unassigned]prepararing-for-software-developer-interview.md", "downloaded_repos/breatheco-de_content/src/content/lesson/[unassigned]python-http-requests.md", "downloaded_repos/breatheco-de_content/src/content/lesson/agile-development.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/agile-development.md", "downloaded_repos/breatheco-de_content/src/content/lesson/anadir-columna-dataframe-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/apis-libraries-integrate-fullstack-project.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/apis-libraries-integrate-fullstack-project.md", "downloaded_repos/breatheco-de_content/src/content/lesson/asynchronous-algorithms-async-await.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/asynchronous-algorithms-async-await.md", "downloaded_repos/breatheco-de_content/src/content/lesson/backend-developer.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/backend-developer.md", "downloaded_repos/breatheco-de_content/src/content/lesson/before-we-start-the-fullstack.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/before-we-start-the-fullstack.md", "downloaded_repos/breatheco-de_content/src/content/lesson/bootstrap-tutorial-of-bootstrap-4.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/bootstrap-tutorial-of-bootstrap-4.md", "downloaded_repos/breatheco-de_content/src/content/lesson/bootstrap-tutorial-of-bootstrap-5.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/bootstrap-tutorial-of-bootstrap-5.md", "downloaded_repos/breatheco-de_content/src/content/lesson/bug-issue-tracking-best-practices.md", "downloaded_repos/breatheco-de_content/src/content/lesson/build-and-operate-databases-sqlalchemy.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/build-and-operate-databases-sqlalchemy.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-apis-with-nodejs-expressjs.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-apis-with-nodejs-expressjs.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-apis-with-nodejs-typeorm.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-apis-with-nodejs-typeorm.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-apis-with-python-flask.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-apis-with-python-flask.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-your-github-profile-and-reputation.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-your-github-profile-and-reputation.md", "downloaded_repos/breatheco-de_content/src/content/lesson/building-your-linkedin-reputation.md", "downloaded_repos/breatheco-de_content/src/content/lesson/career-support-perks.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/career-support-perks.md", "downloaded_repos/breatheco-de_content/src/content/lesson/code-reviews-and-importance.md", "downloaded_repos/breatheco-de_content/src/content/lesson/code-reviews-and-its-importance.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/coding-introduction-saas-welcome.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/coding-introduction-saas-welcome.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/coding-standards-guidelines.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/coding-standards-guidelines.md", "downloaded_repos/breatheco-de_content/src/content/lesson/conditionals-in-programing-coding.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/conditionals-in-programing-coding.md", "downloaded_repos/breatheco-de_content/src/content/lesson/conditionals-in-programing-java.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/conditionals-in-programing-java.md", "downloaded_repos/breatheco-de_content/src/content/lesson/conditionals-in-programing-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/conditionals-in-programing-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/context-api.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/context-api.md", "downloaded_repos/breatheco-de_content/src/content/lesson/controlled-vs-uncontrolled-inputs-react-js.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/controlled-vs-uncontrolled-inputs-react-js.md", "downloaded_repos/breatheco-de_content/src/content/lesson/css-layouts.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/css-layouts.md", "downloaded_repos/breatheco-de_content/src/content/lesson/data-science-saas-welcome.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/data-science-saas-welcome.md", "downloaded_repos/breatheco-de_content/src/content/lesson/database-modeling.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/database-modeling.md", "downloaded_repos/breatheco-de_content/src/content/lesson/debugging-css-code.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/debugging-css-code.md", "downloaded_repos/breatheco-de_content/src/content/lesson/debugging-html-code.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/debugging-html-code.md", "downloaded_repos/breatheco-de_content/src/content/lesson/deep-dive-into-python-welcome.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/deep-dive-into-python-welcome.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/dispatcher-pattern.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/django-rest-framework.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/django-rest-framework.md", "downloaded_repos/breatheco-de_content/src/content/lesson/do-not-delete.md", "downloaded_repos/breatheco-de_content/src/content/lesson/event-driven-programming.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/event-driven-programming.md", "downloaded_repos/breatheco-de_content/src/content/lesson/everything-you-need-to-start-using-sqlalchemy.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/everything-you-need-to-start-using-sqlalchemy.md", "downloaded_repos/breatheco-de_content/src/content/lesson/expresiones-regulares-java.md", "downloaded_repos/breatheco-de_content/src/content/lesson/foreach-method-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/front-end-development-saas-welcome.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/front-end-development-saas-welcome.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/full-stack-saas-welcome.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/full-stack-saas-welcome.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/get-ready-for-your-job-hunt.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/get-ready-for-your-job-hunt.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-consume-an-api-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-consume-an-api-in-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-create-unit-testing-with-Javascript-and-Jest.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-create-unit-testing-with-Javascript-and-Jest.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-learn-with-ai.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-networkt-yourself-into-a-software-development-job.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-networkt-yourself-into-a-software-development-job.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-report-a-bug-in-learnpack.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-use-git-version-control-system.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-use-git-version-control-system.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-use-github-codespaces.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-use-github-codespaces.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-use-gitpod.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/how-to-use-gitpod.md", "downloaded_repos/breatheco-de_content/src/content/lesson/html-input-html-textarea.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/html-input-html-textarea.md", "downloaded_repos/breatheco-de_content/src/content/lesson/instalar-pandas-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/instalar-pyenv-windows.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/inteligencia-artificial-generativa.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/intro-a-algoritmos-y-programacion.md", "downloaded_repos/breatheco-de_content/src/content/lesson/intro-to-4geeks.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/intro-to-4geeks.md", "downloaded_repos/breatheco-de_content/src/content/lesson/intro-to-prework.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/intro-to-prework.md", "downloaded_repos/breatheco-de_content/src/content/lesson/intro-to-start-coding-using-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/intro-to-start-coding-using-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javascript-array-every.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javascript-array-fill.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javascript-array-flatten.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javascript-empty-array.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javascript-import.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javascript-import.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javascript-string-replace.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/javscript-substring.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/learn-in-public.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/learn-in-public.md", "downloaded_repos/breatheco-de_content/src/content/lesson/learn-react-js-tutorial.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/learn-react-js-tutorial.md", "downloaded_repos/breatheco-de_content/src/content/lesson/learners-starting-guide.md", "downloaded_repos/breatheco-de_content/src/content/lesson/learning-to-code-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/learning-to-code-with-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-an-amazing-resume.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-an-amazing-resume.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-code-amazingly-readable-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-code-amazingly-readable-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-code-amazingly-readable.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-code-amazingly-readable.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-react-components.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/making-react-components.md", "downloaded_repos/breatheco-de_content/src/content/lesson/managing-react-app-data.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/managing-react-app-data.md", "downloaded_repos/breatheco-de_content/src/content/lesson/mastering-css-selectors.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/mastering-css-selectors.md", "downloaded_repos/breatheco-de_content/src/content/lesson/metodo-foreach-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/modeling-data-using-data-structures.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/modeling-data-using-data-structures.md", "downloaded_repos/breatheco-de_content/src/content/lesson/oop-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/oop-in-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/optimize-react-components-usereducer.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/optimize-react-components-usereducer.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-apply.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-concat.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-dataframe-tutorial.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-dataframe.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-dataframe.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-drop-column.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-drop.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-en-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-pivot.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-python-ejemplos.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pandas-tutorial-exercises.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/php-syntax.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/php-syntax.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pick-your-fullstack-capstone-project.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pick-your-fullstack-capstone-project.md", "downloaded_repos/breatheco-de_content/src/content/lesson/pyenv-virtualenv.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-for-datascience.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-for-datascience.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-modules.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-modules.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-syntax.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-syntax.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-vs-javascript-syntax.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/python-vs-javascript-syntax.md", "downloaded_repos/breatheco-de_content/src/content/lesson/que-son-las-librerias-de-terceros.md", "downloaded_repos/breatheco-de_content/src/content/lesson/que-son-los-diccionarios-en-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/react-design-patterns.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/react-design-patterns.md", "downloaded_repos/breatheco-de_content/src/content/lesson/react-global-context.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/react-hooks-explained.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/react-hooks-explained.md", "downloaded_repos/breatheco-de_content/src/content/lesson/regex-letter.us.md", "downloaded_repos/breatheco-de_content/src/content/lesson/regex-tutorial-regular-expression-examples.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/regex-tutorial-regular-expression-examples.md", "downloaded_repos/breatheco-de_content/src/content/lesson/routing-our-views-with-react-router.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/routing-our-views-with-react-router.md", "downloaded_repos/breatheco-de_content/src/content/lesson/snake-case-variable-naming-convention.md", "downloaded_repos/breatheco-de_content/src/content/lesson/sorting-algorithms-explanied-in-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/sorting-algorithms-explanied-in-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/sorting-and-search-algorithms-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/sorting-and-search-algorithms-in-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/split-string-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/strings-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/strings-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/the-command-line-the-terminal.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/the-command-line-the-terminal.md", "downloaded_repos/breatheco-de_content/src/content/lesson/the-fetch-javascript-api.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/the-fetch-javascript-api.md", "downloaded_repos/breatheco-de_content/src/content/lesson/tips-tools-data-science-machine-learning-project.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/tips-tools-data-science-machine-learning-project.md", "downloaded_repos/breatheco-de_content/src/content/lesson/token-based-api-authentication.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/token-based-api-authentication.md", "downloaded_repos/breatheco-de_content/src/content/lesson/understanding-php-sessions.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/understanding-php-sessions.md", "downloaded_repos/breatheco-de_content/src/content/lesson/understanding-rest-apis.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/understanding-rest-apis.md", "downloaded_repos/breatheco-de_content/src/content/lesson/user-stories-examples.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/user-stories-examples.md", "downloaded_repos/breatheco-de_content/src/content/lesson/usereducer-react.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/usereducer-react.md", "downloaded_repos/breatheco-de_content/src/content/lesson/variable-naming-conventions.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/variable-naming-conventions.md", "downloaded_repos/breatheco-de_content/src/content/lesson/vite.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/vite.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-are-dictionaries-in-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-are-third-party-libraries.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-JWT-and-how-to-implement-with-Express.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-JWT-and-how-to-implement-with-Express.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-JWT-and-how-to-implement-with-Flask.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-JWT-and-how-to-implement-with-Flask.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-a-python-list.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-a-python-list.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-an-array-define-array-java.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-an-array-define-array-java.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-an-array-define-array.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-an-array-define-array.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-an-environment-in-programming.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-an-environment-in-programming.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-coding-learn-to-code.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-coding-learn-to-code.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-css-learn-css.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-css-learn-css.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-debugging-code.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-debugging-code.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-dom-define-dom.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-dom-define-dom.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-front-end-development.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-front-end-development.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-html-learn-html.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-html-learn-html.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-http.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-http.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-java-learn-to-code-in-java.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-java-learn-to-code-in-java.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-javascript-learn-to-code-in-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-javascript-learn-to-code-in-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-jquery-vs-javascript.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-jquery-vs-javascript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-learnpack.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-learnpack.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-object-oriented-programming-concepts.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-object-oriented-programming-concepts.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-react-flux.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-react-flux.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-recursion-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-recursion-in-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-sql-database.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-sql-database.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-the-internet.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-the-internet.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-typescript.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-typescript.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-webpack.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/what-is-webpack.md", "downloaded_repos/breatheco-de_content/src/content/lesson/wireframes-fullstack-project.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/wireframes-fullstack-project.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-functions-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-functions-python.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-functions.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-functions.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-plain-text-files-backend.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-plain-text-files-backend.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-strings-in-java.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-strings-in-java.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-strings-in-python.es.md", "downloaded_repos/breatheco-de_content/src/content/lesson/working-with-strings-in-python.md", "downloaded_repos/breatheco-de_content/src/content/misc/code-of-conduct-and-ethics.es.md", "downloaded_repos/breatheco-de_content/src/content/misc/code-of-conduct-and-ethics.md", "downloaded_repos/breatheco-de_content/src/content/misc/privacy-policy.es.md", "downloaded_repos/breatheco-de_content/src/content/misc/privacy-policy.md", "downloaded_repos/breatheco-de_content/src/content/misc/terms-and-conditions.es.md", "downloaded_repos/breatheco-de_content/src/content/misc/terms-and-conditions.md", "downloaded_repos/breatheco-de_content/src/content/prismic/success-stories-es.json", "downloaded_repos/breatheco-de_content/src/content/prismic/success-stories.json", "downloaded_repos/breatheco-de_content/src/content/prismic/trust-cards-es.json", "downloaded_repos/breatheco-de_content/src/content/prismic/trust-cards.json", "downloaded_repos/breatheco-de_content/src/content/reviews/public-reviews-es.json", "downloaded_repos/breatheco-de_content/src/content/reviews/public-reviews-us.json", "downloaded_repos/breatheco-de_content/src/utils/cli.js", "downloaded_repos/breatheco-de_content/src/utils/commands/download_images.js", "downloaded_repos/breatheco-de_content/src/utils/commands/localize_images.js", "downloaded_repos/breatheco-de_content/src/utils/commands/sanitize_lesson.js", "downloaded_repos/breatheco-de_content/src/utils/commands/update_lesson.js", "downloaded_repos/breatheco-de_content/src/utils/files.js", "downloaded_repos/breatheco-de_content/src/utils/i18n.js", "downloaded_repos/breatheco-de_content/src/utils/variables.js", "downloaded_repos/breatheco-de_content/static/download-images.js", "downloaded_repos/breatheco-de_content/static/report.json"], "skipped": [{"path": "downloaded_repos/breatheco-de_content/src/assets/assets/assets/GIT-Cheat-Sheet.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/assets/assets/JQuert-Events.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/2fd53b0a-5243-4440-8fc6-7fd74ac5a46e.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/4cc6fa0b-2530-4052-aa7e-8dac03788ac3.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/78296f64-547f-4928-a8da-14cb24e836b9.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/Fca0Hkm.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/b5b7f158-1aca-43f0-821a-fc39e6d583e3.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/b84e07e5-5761-469b-85bb-f7afc87d4dc9.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/bdd432f7-adef-4023-976e-1ebd6abe70f7.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/assets/images/bf7bc7e6-c89c-4f97-9377-e30e369ba796.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/breatheco-de_content/src/test/test-lessons.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.926853895187378, "profiling_times": {"config_time": 5.9851157665252686, "core_time": 5.4453654289245605, "ignores_time": 0.0017981529235839844, "total_time": 11.432992458343506}, "parsing_time": {"total_time": 0.6316211223602295, "per_file_time": {"mean": 0.027461787928705628, "std_dev": 0.0005853393030683998}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 8.273519039154053, "per_file_time": {"mean": 0.005863585428174382, "std_dev": 0.0006783858743694948}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.1376957893371582, "per_file_and_rule_time": {"mean": 0.007110598683357238, "std_dev": 0.00037802908712193596}, "very_slow_stats": {"time_ratio": 0.1754362361536698, "count_ratio": 0.00625}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/breatheco-de_content/src/utils/files.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.19959306716918945}]}, "tainting_time": {"total_time": 0.25433993339538574, "per_def_and_rule_time": {"mean": 0.007266855239868164, "std_dev": 2.4892313224752015e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1093471040}, "engine_requested": "OSS", "skipped_rules": []}