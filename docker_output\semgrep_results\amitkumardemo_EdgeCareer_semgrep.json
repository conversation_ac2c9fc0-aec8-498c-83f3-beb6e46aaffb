{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-sonarqube-docs-api-key.detected-sonarqube-docs-api-key", "path": "downloaded_repos/amitkumardemo_EdgeCareer/.github/workflows/sonarcloud.yml", "start": {"line": 50, "col": 15, "offset": 1973}, "end": {"line": 50, "col": 92, "offset": 2050}, "extra": {"message": "SonarQube Docs API Key detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "sonarqube"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-sonarqube-docs-api-key.detected-sonarqube-docs-api-key", "shortlink": "https://sg.run/x10P"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/amitkumardemo_EdgeCareer/dockerfile", "start": {"line": 34, "col": 1, "offset": 887}, "end": {"line": 34, "col": 21, "offset": 907}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"npm\", \"start\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/amitkumardemo_EdgeCareer/.dockerignore", "downloaded_repos/amitkumardemo_EdgeCareer/.eslintrc.json", "downloaded_repos/amitkumardemo_EdgeCareer/.github/ISSUE_TEMPLATE/create-issue-guidelines.md", "downloaded_repos/amitkumardemo_EdgeCareer/.github/workflows/auto-ssoc-label.yml", "downloaded_repos/amitkumardemo_EdgeCareer/.github/workflows/greetings.yml", "downloaded_repos/amitkumardemo_EdgeCareer/.github/workflows/label.yml", "downloaded_repos/amitkumardemo_EdgeCareer/.github/workflows/labeler.yml", "downloaded_repos/amitkumardemo_EdgeCareer/.github/workflows/sonarcloud.yml", "downloaded_repos/amitkumardemo_EdgeCareer/.github/workflows/summary.yml", "downloaded_repos/amitkumardemo_EdgeCareer/.gitignore", "downloaded_repos/amitkumardemo_EdgeCareer/CODE_OF_CONDUCT.md", "downloaded_repos/amitkumardemo_EdgeCareer/CONTRIBUTING.md", "downloaded_repos/amitkumardemo_EdgeCareer/EdgeCareers.png", "downloaded_repos/amitkumardemo_EdgeCareer/LICENSE.md", "downloaded_repos/amitkumardemo_EdgeCareer/README.md", "downloaded_repos/amitkumardemo_EdgeCareer/SECURITY.md", "downloaded_repos/amitkumardemo_EdgeCareer/actions/cover-letter.js", "downloaded_repos/amitkumardemo_EdgeCareer/actions/dashboard.js", "downloaded_repos/amitkumardemo_EdgeCareer/actions/interview.js", "downloaded_repos/amitkumardemo_EdgeCareer/actions/resume.js", "downloaded_repos/amitkumardemo_EdgeCareer/actions/user.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/(auth)/layout.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/(auth)/sign-in/[[...sign-in]]/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(auth)/sign-up/[[...sign-up]]/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/ai-cover-letter/[id]/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/ai-cover-letter/_components/cover-letter-generator.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/ai-cover-letter/_components/cover-letter-list.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/ai-cover-letter/_components/cover-letter-preview.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/ai-cover-letter/new/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/ai-cover-letter/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/dashboard/_component/dashboard-view.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/dashboard/layout.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/dashboard/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/_components/performace-chart.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/_components/quiz-list.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/_components/quiz-result.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/_components/quiz.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/_components/stats-cards.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/layout.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/mock/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/interview/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/layout.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/onboarding/_components/onboarding-form.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/onboarding/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/resume/_components/entry-form.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/resume/_components/resume-builder.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/(main)/resume/page.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/api/inngest/route.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/api/quote/route.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/globals.css", "downloaded_repos/amitkumardemo_EdgeCareer/app/layout.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/lib/helper.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/lib/schema.js", "downloaded_repos/amitkumardemo_EdgeCareer/app/not-found.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/app/page.js", "downloaded_repos/amitkumardemo_EdgeCareer/components/Footer.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ScrollToTop.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/SequenceAnimation.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/header.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/hero.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/theme-provider.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/theme-switch.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/TestimonialCarousel.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/accordion.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/alert-dialog.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/badge.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/blocks/ShinyText/CountUp.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/blocks/ShinyText/ShinyText.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/blocks/ShinyText/SplitText.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/button.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/card.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/dialog.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/dropdown-menu.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/input.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/label.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/progress.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/radio-group.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/select.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/sonner.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/tabs.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components/ui/textarea.jsx", "downloaded_repos/amitkumardemo_EdgeCareer/components.json", "downloaded_repos/amitkumardemo_EdgeCareer/data/faqs.js", "downloaded_repos/amitkumardemo_EdgeCareer/data/features.js", "downloaded_repos/amitkumardemo_EdgeCareer/data/howItWorks.js", "downloaded_repos/amitkumardemo_EdgeCareer/data/industries.js", "downloaded_repos/amitkumardemo_EdgeCareer/data/testimonial.js", "downloaded_repos/amitkumardemo_EdgeCareer/dockerfile", "downloaded_repos/amitkumardemo_EdgeCareer/eslint.config.mjs", "downloaded_repos/amitkumardemo_EdgeCareer/hooks/use-fetch.js", "downloaded_repos/amitkumardemo_EdgeCareer/jsconfig.json", "downloaded_repos/amitkumardemo_EdgeCareer/lib/checkUser.js", "downloaded_repos/amitkumardemo_EdgeCareer/lib/constants.js", "downloaded_repos/amitkumardemo_EdgeCareer/lib/inngest/client.js", "downloaded_repos/amitkumardemo_EdgeCareer/lib/inngest/function.js", "downloaded_repos/amitkumardemo_EdgeCareer/lib/prisma.js", "downloaded_repos/amitkumardemo_EdgeCareer/lib/utils.js", "downloaded_repos/amitkumardemo_EdgeCareer/middleware.js", "downloaded_repos/amitkumardemo_EdgeCareer/next.config.mjs", "downloaded_repos/amitkumardemo_EdgeCareer/package-lock.json", "downloaded_repos/amitkumardemo_EdgeCareer/package.json", "downloaded_repos/amitkumardemo_EdgeCareer/postcss.config.mjs", "downloaded_repos/amitkumardemo_EdgeCareer/prisma/migrations/20250114060115_create_models/migration.sql", "downloaded_repos/amitkumardemo_EdgeCareer/prisma/migrations/20250114064152_update_user/migration.sql", "downloaded_repos/amitkumardemo_EdgeCareer/prisma/migrations/20250117091806_update/migration.sql", "downloaded_repos/amitkumardemo_EdgeCareer/prisma/migrations/20250120090020_hh/migration.sql", "downloaded_repos/amitkumardemo_EdgeCareer/prisma/migrations/20250120124732_update/migration.sql", "downloaded_repos/amitkumardemo_EdgeCareer/prisma/migrations/migration_lock.toml", "downloaded_repos/amitkumardemo_EdgeCareer/prisma/schema.prisma", "downloaded_repos/amitkumardemo_EdgeCareer/public/AmitPhoto.jpg", "downloaded_repos/amitkumardemo_EdgeCareer/public/about.webp", "downloaded_repos/amitkumardemo_EdgeCareer/public/banner.jpeg", "downloaded_repos/amitkumardemo_EdgeCareer/public/banner2.jpeg", "downloaded_repos/amitkumardemo_EdgeCareer/public/banner3.jpeg", "downloaded_repos/amitkumardemo_EdgeCareer/public/head.webp", "downloaded_repos/amitkumardemo_EdgeCareer/public/icons/apple-touch-icon.png", "downloaded_repos/amitkumardemo_EdgeCareer/public/icons/favicon-96x96.png", "downloaded_repos/amitkumardemo_EdgeCareer/public/icons/favicon.ico", "downloaded_repos/amitkumardemo_EdgeCareer/public/icons/favicon.svg", "downloaded_repos/amitkumardemo_EdgeCareer/public/icons/site.webmanifest", "downloaded_repos/amitkumardemo_EdgeCareer/public/icons/web-app-manifest-192x192.png", "downloaded_repos/amitkumardemo_EdgeCareer/public/icons/web-app-manifest-512x512.png", "downloaded_repos/amitkumardemo_EdgeCareer/public/logo.png", "downloaded_repos/amitkumardemo_EdgeCareer/public/skill.png", "downloaded_repos/amitkumardemo_EdgeCareer/public/yashasvi photos.jpg", "downloaded_repos/amitkumardemo_EdgeCareer/tailwind.config.mjs"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.635552167892456, "profiling_times": {"config_time": 5.779149293899536, "core_time": 3.0545272827148438, "ignores_time": 0.11977958679199219, "total_time": 8.954261064529419}, "parsing_time": {"total_time": 1.5571308135986328, "per_file_time": {"mean": 0.016743342081705723, "std_dev": 0.00146095742286108}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.929458141326904, "per_file_time": {"mean": 0.014288284467614213, "std_dev": 0.002773425965088964}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.446831226348877, "per_file_and_rule_time": {"mean": 0.003318420243919444, "std_dev": 8.724697199185643e-05}, "very_slow_stats": {"time_ratio": 0.08011304369320008, "count_ratio": 0.0022935779816513763}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/amitkumardemo_EdgeCareer/components/hero.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.11591005325317383}]}, "tainting_time": {"total_time": 0.23955178260803223, "per_def_and_rule_time": {"mean": 0.003151997139579371, "std_dev": 2.9682794926584645e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}