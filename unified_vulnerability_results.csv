rank,repo_name,repo_url,clone_url,stars,issues,language,description,risk_score,health_score,security_score,development_score,semgrep_vulnerabilities,semgrep_severity_score,vulnerability_categories,final_score,risk_factors,health_factors,security_factors,development_factors,last_updated,created_at,semgrep_findings_summary,detailed_vulnerabilities
1,lara-zeus/sky,https://github.com/lara-zeus/sky,https://github.com/lara-zeus/sky.git,191,1,PHP,"CMS for your website. it include posts, pages, tags, and categories. with a frontend scaffolding ready to use",44,87,70,100,0,0,,64.9,High-risk application type (+16); Vulnerability keywords (+3); Optimal star range (+10),Recently active (last 30 days); Few open issues; Recent release,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-08T10:22:33Z,2022-03-28T21:38:35Z,No vulnerabilities found,None
2,uasoft-indonesia/badaso,https://github.com/uasoft-indonesia/badaso,https://github.com/uasoft-indonesia/badaso.git,1236,1,PHP,"Laravel Vue headless CMS / admin panel / dashboard / builder / API CRUD generator, anything !",59,80,40,80,0,0,,63.5,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+6),Moderately active (last 90 days); Few open issues; Recent release,Has SECURITY.md policy; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-06-13T09:54:58Z,2021-03-15T04:40:50Z,No vulnerabilities found,None
3,moonshine-software/moonshine,https://github.com/moonshine-software/moonshine,https://github.com/moonshine-software/moonshine.git,1089,7,PHP,"Laravel Admin panel and more. Simple for beginners and powerful for experts. Using Blade, Alpine.js and Tailwind CSS.",56,80,50,65,0,0,,61.25,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Few open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-27T18:55:38Z,2022-05-12T15:31:35Z,No vulnerabilities found,None
4,stephenjude/filament-blog,https://github.com/stephenjude/filament-blog,https://github.com/stephenjude/filament-blog.git,189,1,PHP,A faceless blog content manager with configurable richtext and markdown support for filament admin panel,40,90,55,100,0,0,,61.25,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Few open issues; Recent release,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-28T19:10:43Z,2022-02-12T19:22:31Z,No vulnerabilities found,None
5,WordPress/two-factor,https://github.com/WordPress/two-factor,https://github.com/WordPress/two-factor.git,779,73,PHP,Two-Factor Authentication for WordPress.,42,75,75,85,0,0,,60.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Many open issues (73); Recent release,Has SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-21T23:08:47Z,2014-10-23T20:03:04Z,No vulnerabilities found,None
6,saade/filament-laravel-log,https://github.com/saade/filament-laravel-log,https://github.com/saade/filament-laravel-log.git,111,9,PHP,Read Laravel logs from the Filament admin panel,40,85,70,80,0,0,,59.5,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Few open issues; Release within year,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-21T18:47:54Z,2022-03-18T03:09:11Z,No vulnerabilities found,None
7,JibayMcs/filament-tour,https://github.com/JibayMcs/filament-tour,https://github.com/JibayMcs/filament-tour.git,118,6,PHP,"Let's embed the power of DriverJS to your filament admin panel, and guide peoples through your app",35,80,55,100,0,0,,56.75,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Moderately active (last 90 days); Few open issues; Recent release,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-19T13:47:52Z,2023-08-17T07:25:01Z,No vulnerabilities found,None
8,alperenersoy/filament-export,https://github.com/alperenersoy/filament-export,https://github.com/alperenersoy/filament-export.git,257,17,PHP,Customizable export and print functionality for Filament Admin Panel,44,85,40,75,0,0,,56.25,High-risk application type (+8); Critical features (+8); Vulnerability keywords (+3),Recently active (last 30 days); Moderate open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-30T18:46:38Z,2022-05-06T19:54:09Z,No vulnerabilities found,None
9,codeigniter4/shield,https://github.com/codeigniter4/shield,https://github.com/codeigniter4/shield.git,394,15,PHP,Authentication and Authorization for CodeIgniter 4,32,85,55,100,0,0,,56.25,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Recently active (last 30 days); Moderate open issues; Recent release,Has SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-16T18:19:56Z,2020-12-30T06:55:29Z,No vulnerabilities found,None
10,ClassicPress/ClassicPress,https://github.com/ClassicPress/ClassicPress,https://github.com/ClassicPress/ClassicPress.git,808,43,PHP,The CMS for Creators. Stable. Lightweight. Instantly Familiar. Forked from WordPress.,41,67,50,95,0,0,,55.65,High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Release within year,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-28T17:37:02Z,2018-08-29T10:02:51Z,No vulnerabilities found,None
11,maize-tech/laravel-magic-login,https://github.com/maize-tech/laravel-magic-login,https://github.com/maize-tech/laravel-magic-login.git,160,0,PHP,Easily add passwordless authentication into your application,35,87,40,90,0,0,,54.400000000000006,Critical features (+4); Vulnerability keywords (+6); Optimal star range (+10),Recently active (last 30 days); No open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-29T01:50:13Z,2022-12-07T14:52:33Z,No vulnerabilities found,None
12,qcod/laravel-imageup,https://github.com/qcod/laravel-imageup,https://github.com/qcod/laravel-imageup.git,769,7,PHP,"Auto Image & file upload, resize and crop for Laravel eloquent model using Intervention image",40,77,25,100,0,0,,54.15,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Moderately active (last 90 days); Few open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-05-15T03:56:48Z,2018-09-21T11:49:28Z,No vulnerabilities found,None
13,simplesamlphp/simplesamlphp,https://github.com/simplesamlphp/simplesamlphp,https://github.com/simplesamlphp/simplesamlphp.git,1115,114,PHP,SimpleSAMLphp is an application written in native PHP that deals with authentication.,37,65,50,100,0,0,,54.0,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Recently active (last 30 days); Many open issues (114); Recent release,Has SECURITY.md policy; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-25T12:21:47Z,2014-02-25T09:01:45Z,No vulnerabilities found,None
14,scheb/2fa,https://github.com/scheb/2fa,https://github.com/scheb/2fa.git,553,6,PHP,Two-factor authentication for Symfony applications ?,32,77,50,100,0,0,,53.9,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); Few open issues; Recent release,Has SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-27T12:43:42Z,2020-02-09T15:59:35Z,No vulnerabilities found,None
15,stephenjude/filament-debugger,https://github.com/stephenjude/filament-debugger,https://github.com/stephenjude/filament-debugger.git,93,0,PHP,"Easily add Telescope, Horizon and Laravel Pulse to Filament admin panel.",30,95,40,90,0,0,,53.5,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); No open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-28T06:46:38Z,2022-08-11T08:54:27Z,No vulnerabilities found,None
16,renoki-co/php-k8s,https://github.com/renoki-co/php-k8s,https://github.com/renoki-co/php-k8s.git,314,4,PHP,"Unofficial PHP client for Kubernetes. It supports any form of authentication, the exec API, and it has an easy implementation for CRDs.",38,75,30,100,0,0,,53.5,Critical features (+4); Vulnerability keywords (+9); Optimal star range (+10),Recently active (last 30 days); Few open issues; Old releases,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-21T09:24:33Z,2020-04-29T17:19:13Z,No vulnerabilities found,None
17,spatie/livewire-filepond,https://github.com/spatie/livewire-filepond,https://github.com/spatie/livewire-filepond.git,289,9,PHP,Upload files using Filepond in Livewire components,31,77,55,95,0,0,,53.4,High-risk application type (+8); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); Few open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-05-26T07:36:17Z,2024-07-13T14:21:32Z,No vulnerabilities found,None
18,e107inc/e107,https://github.com/e107inc/e107,https://github.com/e107inc/e107.git,329,691,PHP,"e107 Bootstrap CMS (Content Management System) v2 with PHP, MySQL, HTML5, jQuery and Twitter Bootstrap. Issue Discussion Room: https://gitter.im/e107inc/e107 ",44,50,65,75,0,0,,53.0,High-risk application type (+8); Vulnerability keywords (+6); Optimal star range (+10),Recently active (last 30 days); Many open issues (691); Old releases,Has SECURITY.md policy; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-20T18:33:58Z,2012-11-16T19:49:01Z,No vulnerabilities found,None
19,silverstripe/silverstripe-elemental,https://github.com/silverstripe/silverstripe-elemental,https://github.com/silverstripe/silverstripe-elemental.git,112,119,PHP,Create pages in Silverstripe CMS using content blocks,38,75,40,85,0,0,,52.75,High-risk application type (+8); Optimal star range (+10); High issue count (+10),Recently active (last 30 days); Many open issues (119); Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-23T01:37:03Z,2014-08-26T05:27:34Z,No vulnerabilities found,None
20,rappasoft/laravel-authentication-log,https://github.com/rappasoft/laravel-authentication-log,https://github.com/rappasoft/laravel-authentication-log.git,901,20,PHP,Log user authentication details and send new device notifications.,32,67,55,100,0,0,,52.65,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); Moderate open issues; Release within year,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-05-24T03:13:59Z,2021-09-29T20:40:50Z,No vulnerabilities found,None
21,Laragear/WebAuthn,https://github.com/Laragear/WebAuthn,https://github.com/Laragear/WebAuthn.git,364,0,PHP,"Authenticate users with Passkeys: fingerprints, patterns and biometric data.",28,92,45,85,0,0,,51.900000000000006,Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); No open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-16T15:11:56Z,2022-06-14T09:13:53Z,No vulnerabilities found,None
22,usefulteam/jwt-auth,https://github.com/usefulteam/jwt-auth,https://github.com/usefulteam/jwt-auth.git,133,52,PHP,WordPress JSON Web Token Authentication,48,60,40,65,0,0,,51.75,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+6),Recently active (last 30 days); Many open issues (52); Old releases,No security policy found; Security info in README; No branch protection,Some semantic versioning; Has release notes; Has testing infrastructure,2025-07-24T15:16:38Z,2020-05-05T09:19:46Z,No vulnerabilities found,None
23,laravel/fortify,https://github.com/laravel/fortify,https://github.com/laravel/fortify.git,1673,1,PHP,Backend controllers and scaffolding for Laravel authentication.,22,87,60,95,0,0,,51.650000000000006,Critical features (+4); Vulnerability keywords (+3); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-22T15:26:32Z,2020-08-31T19:23:52Z,No vulnerabilities found,None
24,concretecms/concretecms,https://github.com/concretecms/concretecms,https://github.com/concretecms/concretecms.git,801,548,PHP,Official repository for Concrete CMS development,38,62,35,100,0,0,,51.65,High-risk application type (+8); Optimal star range (+10); High issue count (+10),Recently active (last 30 days); Many open issues (548); Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-17T21:19:33Z,2014-04-22T18:12:28Z,No vulnerabilities found,None
25,stephenjude/filament-two-factor-authentication,https://github.com/stephenjude/filament-two-factor-authentication,https://github.com/stephenjude/filament-two-factor-authentication.git,70,3,PHP,Add two factor authentication (2FA) to Filament panels.,17,87,70,100,0,0,,51.400000000000006,Critical features (+4); Vulnerability keywords (+3); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-24T11:51:54Z,2024-08-13T13:48:28Z,No vulnerabilities found,None
26,aymanalhattami/filament-context-menu,https://github.com/aymanalhattami/filament-context-menu,https://github.com/aymanalhattami/filament-context-menu.git,97,5,PHP,"Add a context menu (right click menu) for resource pages, custom pages and table cells of Filament Admin Panel.",25,67,70,100,0,0,,51.4,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Moderately active (last 90 days); Few open issues; Release within year,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-16T14:34:57Z,2024-04-17T18:05:01Z,No vulnerabilities found,None
27,DirectoryTree/LdapRecord-Laravel,https://github.com/DirectoryTree/LdapRecord-Laravel,https://github.com/DirectoryTree/LdapRecord-Laravel.git,548,5,PHP,Multi-domain LDAP Authentication & Management for Laravel.,36,77,35,85,0,0,,51.4,Critical features (+8); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); Few open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-23T18:32:29Z,2019-04-18T03:18:50Z,No vulnerabilities found,None
28,pantheon-systems/wp-saml-auth,https://github.com/pantheon-systems/wp-saml-auth,https://github.com/pantheon-systems/wp-saml-auth.git,93,33,PHP,Rock-solid SAML authentication for WordPress built on a modern foundation.,25,85,55,90,0,0,,51.25,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Moderate open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-22T15:16:04Z,2016-04-08T11:39:24Z,No vulnerabilities found,None
29,spatie/laravel-one-time-passwords,https://github.com/spatie/laravel-one-time-passwords,https://github.com/spatie/laravel-one-time-passwords.git,122,1,PHP,Use one time passwords (OTP) to authenticate in your Laravel app,26,87,40,95,0,0,,50.650000000000006,Vulnerability keywords (+6); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-28T18:03:44Z,2025-04-10T13:40:25Z,No vulnerabilities found,None
30,coderello/laravel-passport-social-grant,https://github.com/coderello/laravel-passport-social-grant,https://github.com/coderello/laravel-passport-social-grant.git,179,0,PHP,? API authentication via social networks for your Laravel application,30,77,30,100,0,0,,49.9,Critical features (+4); Vulnerability keywords (+6); Optimal star range (+10),Recently active (last 30 days); No open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-13T11:38:24Z,2018-07-06T11:12:57Z,No vulnerabilities found,None
31,TypiCMS/Base,https://github.com/TypiCMS/Base,https://github.com/TypiCMS/Base.git,1450,13,PHP,Multilingual CMS built with Laravel.,28,72,40,95,0,0,,48.65,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,Has SECURITY.md policy; No branch protection; No CI/CD workflows,Follows semantic versioning; Has testing infrastructure; Comprehensive README,2025-07-25T12:05:29Z,2014-12-26T14:15:30Z,No vulnerabilities found,None
32,cesargb/laravel-magiclink,https://github.com/cesargb/laravel-magiclink,https://github.com/cesargb/laravel-magiclink.git,428,4,PHP,Create link for authenticate in Laravel without password or get private content,26,77,35,100,0,0,,48.65,Vulnerability keywords (+6); Optimal star range (+10); High-risk language: php (+10),Moderately active (last 90 days); Few open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-20T11:50:34Z,2017-07-25T18:31:29Z,No vulnerabilities found,None
33,redaxo/redaxo,https://github.com/redaxo/redaxo,https://github.com/redaxo/redaxo.git,339,130,PHP,"REDAXO, a PHP-based CMS since 2004. Both simple and flexible.",38,65,45,65,0,0,,48.5,High-risk application type (+8); Optimal star range (+10); High issue count (+10),Recently active (last 30 days); Many open issues (130); Recent release,Has SECURITY.md policy; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-27T16:02:31Z,2010-07-04T16:46:03Z,No vulnerabilities found,None
34,10up/eight-day-week,https://github.com/10up/eight-day-week,https://github.com/10up/eight-day-week.git,88,9,PHP,Optimize print publication workflows by using WordPress as your print CMS.,26,75,50,85,0,0,,48.25,High-risk application type (+16); High-risk language: php (+10),Moderately active (last 90 days); Few open issues; Release within year,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-06-23T15:14:05Z,2015-10-08T15:48:20Z,No vulnerabilities found,None
35,andrewdwallo/filament-companies,https://github.com/andrewdwallo/filament-companies,https://github.com/andrewdwallo/filament-companies.git,331,3,PHP,"A comprehensive Laravel authentication and authorization system designed for Filament, focusing on multi-tenant company management.",32,80,20,85,0,0,,47.75,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); Few open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-16T05:24:25Z,2022-12-18T02:26:37Z,No vulnerabilities found,None
36,haxtheweb/haxcms-php,https://github.com/haxtheweb/haxcms-php,https://github.com/haxtheweb/haxcms-php.git,125,4,PHP,HAX + CMS to manage your microsite universe with PHP backend,28,77,35,85,0,0,,47.4,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,Has SECURITY.md policy; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-25T17:09:51Z,2018-07-19T13:41:35Z,No vulnerabilities found,None
37,concretecms-community-store/community_store,https://github.com/concretecms-community-store/community_store,https://github.com/concretecms-community-store/community_store.git,109,56,PHP,"An open, free and community developed eCommerce system for Concrete CMS",33,70,40,70,0,0,,47.0,High-risk application type (+8); Optimal star range (+10); High issue count (+5),Recently active (last 30 days); Many open issues (56); Release within year,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-22T11:17:06Z,2016-02-20T11:13:54Z,No vulnerabilities found,None
38,dustin10/VichUploaderBundle,https://github.com/dustin10/VichUploaderBundle,https://github.com/dustin10/VichUploaderBundle.git,1888,55,PHP,A simple Symfony bundle to ease file uploads with ORM entities and ODM documents.,30,72,15,100,0,0,,46.65,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Many open issues (55); Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-23T06:29:54Z,2011-12-01T22:18:22Z,No vulnerabilities found,None
39,oc-shopaholic/oc-shopaholic-plugin,https://github.com/oc-shopaholic/oc-shopaholic-plugin,https://github.com/oc-shopaholic/oc-shopaholic-plugin.git,426,91,PHP,?? No. 1 e-commerce platform for October CMS ,37,57,20,90,0,0,,46.4,High-risk application type (+8); Optimal star range (+10); High issue count (+9),Recently active (last 30 days); Many open issues (91); Old releases,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has testing infrastructure; Comprehensive README,2025-07-02T20:18:20Z,2016-10-26T09:09:04Z,No vulnerabilities found,None
40,s-cart/s-cart,https://github.com/s-cart/s-cart,https://github.com/s-cart/s-cart.git,721,4,PHP,"Free Laravel e-commerce for business: shopping cart, cms content, and more...",33,70,30,75,0,0,,46.25,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-28T23:11:02Z,2020-04-01T16:14:53Z,No vulnerabilities found,None
41,solutionforest/filament-scaffold,https://github.com/solutionforest/filament-scaffold,https://github.com/solutionforest/filament-scaffold.git,56,0,PHP,Filament scaffold is a toolkit for Filament Admin that simplifies the generation of resources in the panel,13,87,45,100,0,0,,45.650000000000006,Vulnerability keywords (+3); High-risk language: php (+10),Recently active (last 30 days); No open issues; Recent release,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-28T16:04:18Z,2024-07-22T04:22:46Z,No vulnerabilities found,None
42,danpros/htmly,https://github.com/danpros/htmly,https://github.com/danpros/htmly.git,1212,24,PHP,"Simple and fast databaseless PHP blogging platform, and Flat-File CMS",28,82,35,65,0,0,,45.400000000000006,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,Has SECURITY.md policy; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-19T00:42:40Z,2013-12-25T01:35:51Z,No vulnerabilities found,None
43,WonderCMS/wondercms,https://github.com/WonderCMS/wondercms,https://github.com/WonderCMS/wondercms.git,702,3,PHP,"Fast and small flat file CMS (5 files). Built with PHP, JSON database.",28,85,30,65,0,0,,45.25,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Release within year,Has .github/SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-12T17:42:45Z,2013-12-15T13:27:38Z,No vulnerabilities found,None
44,nextcloud/cms_pico,https://github.com/nextcloud/cms_pico,https://github.com/nextcloud/cms_pico.git,137,23,PHP,? Integrate Pico CMS and let your users manage their own websites,28,60,50,75,0,0,,44.75,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Moderately active (last 90 days); Moderate open issues; Old releases,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-06-06T16:21:29Z,2017-09-06T10:21:09Z,No vulnerabilities found,None
45,DirectoryTree/Bartender,https://github.com/DirectoryTree/Bartender,https://github.com/DirectoryTree/Bartender.git,274,0,PHP,An opinionated way to authenticate users using Laravel Socialite.,23,75,35,85,0,0,,44.5,Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); No open issues; Release within year,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-18T14:16:57Z,2024-04-01T13:22:45Z,No vulnerabilities found,None
46,Really-Simple-Plugins/really-simple-ssl,https://github.com/Really-Simple-Plugins/really-simple-ssl,https://github.com/Really-Simple-Plugins/really-simple-ssl.git,156,5,PHP,"Easily improve site security with WordPress Hardening, Two-Factor Authentication (2FA), Login Protection, Vulnerability Detection and SSL certificate generation.",38,75,15,55,0,0,,44.5,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+6),Recently active (last 30 days); Few open issues; Release within year,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-29T11:40:18Z,2017-01-19T12:28:45Z,No vulnerabilities found,None
47,zenphoto/zenphoto,https://github.com/zenphoto/zenphoto,https://github.com/zenphoto/zenphoto.git,311,24,PHP,The Zenphoto open-source gallery and CMS project,36,72,30,50,0,0,,44.4,High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,Has SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-11T21:03:47Z,2012-10-19T14:13:03Z,No vulnerabilities found,None
48,enhavo/enhavo,https://github.com/enhavo/enhavo,https://github.com/enhavo/enhavo.git,88,39,PHP,Modern CMS based on fullstack symfony and vue,18,72,50,90,0,0,,44.4,High-risk application type (+8); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,Has SECURITY.md policy; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-25T08:51:59Z,2015-07-02T15:17:17Z,No vulnerabilities found,None
49,codepress/admin-columns,https://github.com/codepress/admin-columns,https://github.com/codepress/admin-columns.git,67,6,PHP,"Admin Columns allows you to manage and organize columns in the posts, users, comments, and media lists tables in the WordPress admin panel. Transform the WordPress admin screens into beautiful, clear overviews.",33,75,20,65,0,0,,44.25,High-risk application type (+16); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Few open issues; Recent release,No security policy found; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-29T13:08:42Z,2013-03-13T11:40:07Z,No vulnerabilities found,None
50,webreinvent/vaahcms,https://github.com/webreinvent/vaahcms,https://github.com/webreinvent/vaahcms.git,572,11,PHP,VaahCMS is a laravel based open-source web application development platform shipped with a headless content management system (CMS).,33,75,15,70,0,0,,44.25,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-22T12:59:20Z,2019-05-11T13:41:08Z,No vulnerabilities found,None
51,FriendsOfTYPO3/content-blocks,https://github.com/FriendsOfTYPO3/content-blocks,https://github.com/FriendsOfTYPO3/content-blocks.git,83,31,PHP,TYPO3 CMS Content Blocks - Content Types API,26,85,30,65,0,0,,44.25,High-risk application type (+8); Vulnerability keywords (+3); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-26T22:37:19Z,2023-06-25T09:57:44Z,No vulnerabilities found,None
52,spicywebau/craft-neo,https://github.com/spicywebau/craft-neo,https://github.com/spicywebau/craft-neo.git,396,34,PHP,A Matrix-like field type for Craft CMS that uses existing fields,28,72,30,75,0,0,,44.15,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-29T03:47:19Z,2015-12-07T10:00:40Z,No vulnerabilities found,None
53,craftcms/commerce,https://github.com/craftcms/commerce,https://github.com/craftcms/commerce.git,234,36,PHP,Fully integrated ecommerce for Craft CMS.,28,72,35,70,0,0,,44.15,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-29T08:01:53Z,2015-04-23T21:02:38Z,No vulnerabilities found,None
54,Flute-CMS/cms,https://github.com/Flute-CMS/cms,https://github.com/Flute-CMS/cms.git,106,0,PHP,Web-based CMS for server games written on PHP,28,77,30,65,0,0,,43.65,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); No open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-24T23:34:15Z,2024-03-03T07:11:11Z,No vulnerabilities found,None
55,getformwork/formwork,https://github.com/getformwork/formwork,https://github.com/getformwork/formwork.git,61,1,PHP,? A flat file-based Content Management System (CMS) to build and manage sites with simplicity,18,72,50,85,0,0,,43.65,High-risk application type (+8); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,Has SECURITY.md policy; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-26T13:52:31Z,2018-06-16T14:39:18Z,No vulnerabilities found,None
56,Ecodev/graphql-upload,https://github.com/Ecodev/graphql-upload,https://github.com/Ecodev/graphql-upload.git,90,1,PHP,A PSR-15 middleware to support file uploads in GraphQL,25,67,30,85,0,0,,43.15,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Recently active (last 30 days); Few open issues; Old releases,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-08T10:19:54Z,2018-01-05T09:28:25Z,No vulnerabilities found,None
57,nystudio107/craft-seomatic,https://github.com/nystudio107/craft-seomatic,https://github.com/nystudio107/craft-seomatic.git,171,53,PHP,"SEOmatic facilitates modern SEO best practices & implementation for Craft CMS 3. It is a turnkey SEO system that is comprehensive, powerful, and flexible.",33,65,15,75,0,0,,43.0,High-risk application type (+8); Optimal star range (+10); High issue count (+5),Recently active (last 30 days); Many open issues (53); Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-22T12:57:17Z,2017-02-18T19:18:55Z,No vulnerabilities found,None
58,nette/security,https://github.com/nette/security,https://github.com/nette/security.git,366,7,PHP,"? Provides authentication, authorization and a role-based access control management via ACL (Access Control List)",27,72,35,65,0,0,,42.9,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); Few open issues; Release within year,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-06-19T17:48:02Z,2014-03-13T03:47:30Z,No vulnerabilities found,None
59,arnoson/kirby-vite,https://github.com/arnoson/kirby-vite,https://github.com/arnoson/kirby-vite.git,91,1,PHP,Use Kirby CMS together with Vite,23,85,30,65,0,0,,42.75,High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 4.6y (+5),Recently active (last 30 days); Few open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-02T08:34:49Z,2020-12-11T20:40:40Z,No vulnerabilities found,None
60,mattiverse/Laravel-Userstamps,https://github.com/mattiverse/Laravel-Userstamps,https://github.com/mattiverse/Laravel-Userstamps.git,621,2,PHP,"Laravel Userstamps provides an Eloquent trait which automatically maintains created_by and updated_by columns on your model, populated by the currently authenticated user in your application.",23,70,30,85,0,0,,42.75,Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10),Moderately active (last 90 days); Few open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-09T07:06:36Z,2016-03-10T18:14:16Z,No vulnerabilities found,None
61,oveleon/contao-cookiebar,https://github.com/oveleon/contao-cookiebar,https://github.com/oveleon/contao-cookiebar.git,59,2,PHP,Cookie bar for the Contao Open Source CMS,23,87,20,70,0,0,,42.400000000000006,High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 5.1y (+5),Recently active (last 30 days); Few open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-09T13:36:22Z,2020-07-07T08:40:07Z,No vulnerabilities found,None
62,et-nik/gameap,https://github.com/et-nik/gameap,https://github.com/et-nik/gameap.git,115,40,PHP,Game Admin Panel (GameAP) is the opensource game servers control panel.,40,52,20,60,0,0,,42.4,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Moderately active (last 90 days); Moderate open issues; Old releases,No security policy found; No branch protection; No CI/CD workflows,Some semantic versioning; Has testing infrastructure; Comprehensive README,2025-05-15T07:09:45Z,2018-11-21T12:01:13Z,No vulnerabilities found,None
63,spicywebau/craft-embedded-assets,https://github.com/spicywebau/craft-embedded-assets,https://github.com/spicywebau/craft-embedded-assets.git,171,19,PHP,"Manage YouTube videos, Instagram photos and more as first class assets in Craft CMS",28,67,30,70,0,0,,42.4,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Moderately active (last 90 days); Moderate open issues; Release within year,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-05-26T11:44:04Z,2015-12-05T06:33:46Z,No vulnerabilities found,None
64,nextcloud/user_saml,https://github.com/nextcloud/user_saml,https://github.com/nextcloud/user_saml.git,96,146,PHP,:lock: App for authenticating Nextcloud users using SAML https://apps.nextcloud.com/apps/user_saml,23,72,45,65,0,0,,42.4,Vulnerability keywords (+3); High issue count (+10); High-risk language: php (+10),Recently active (last 30 days); Many open issues (144); Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-28T01:50:53Z,2016-06-28T22:02:50Z,No vulnerabilities found,None
65,mostafaznv/larupload,https://github.com/mostafaznv/larupload,https://github.com/mostafaznv/larupload.git,67,1,PHP,"Larupload is an ORM based file uploader for laravel to upload image, video, audio and other known files.",25,60,25,90,0,0,,41.75,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Low activity (last year); Few open issues; Release within year,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-04-27T08:20:07Z,2018-07-20T11:21:59Z,No vulnerabilities found,None
66,rainlab/builder-plugin,https://github.com/rainlab/builder-plugin,https://github.com/rainlab/builder-plugin.git,171,2,PHP,Visual development tool for October CMS,28,75,0,85,0,0,,41.75,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Old releases,No security policy found; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-07-26T04:48:03Z,2016-02-10T05:23:43Z,No vulnerabilities found,None
67,putyourlightson/craft-blitz,https://github.com/putyourlightson/craft-blitz,https://github.com/putyourlightson/craft-blitz.git,151,6,PHP,Intelligent static page caching for creating lightning-fast sites with Craft CMS.,28,77,15,65,0,0,,41.4,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-28T12:27:41Z,2018-06-26T17:31:46Z,No vulnerabilities found,None
68,Cotonti/Cotonti,https://github.com/Cotonti/Cotonti,https://github.com/Cotonti/Cotonti.git,99,137,PHP,"Fast, reliable and flexible PHP CMF/CMS",28,57,40,65,0,0,,41.15,High-risk application type (+8); High issue count (+10); High-risk language: php (+10),Recently active (last 30 days); Many open issues (137); Release within year,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-11T16:01:36Z,2010-10-24T12:17:40Z,No vulnerabilities found,None
69,nukeviet/nukeviet,https://github.com/nukeviet/nukeviet,https://github.com/nukeviet/nukeviet.git,158,85,PHP,"NukeViet CMS is multi Content Management System. NukeViet CMS is the 1st open source content management system in Vietnam. NukeViet was awarded the Vietnam Talent 2011, the Ministry of Education and Training Vietnam officially encouraged to use.",36,50,10,75,0,0,,40.75,High-risk application type (+8); Optimal star range (+10); High issue count (+8),Recently active (last 30 days); Many open issues (85); Old releases,No security policy found; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-26T08:35:33Z,2012-11-24T15:39:39Z,No vulnerabilities found,None
70,liberu-cms/cms-laravel,https://github.com/liberu-cms/cms-laravel,https://github.com/liberu-cms/cms-laravel.git,81,5,PHP,CMS application written in Laravel 11 / PHP 8.3 using Filament 3. WordPress alternative.,26,70,45,45,0,0,,40.5,High-risk application type (+16); High-risk language: php (+10),Recently active (last 30 days); Few open issues; No releases,No security policy found; No branch protection; Has CI/CD workflows,No releases found; Has testing infrastructure; Comprehensive README,2025-07-25T00:35:02Z,2024-02-04T14:43:16Z,No vulnerabilities found,None
71,ollieread/multiauth,https://github.com/ollieread/multiauth,https://github.com/ollieread/multiauth.git,444,0,PHP,Multiauth is a replacement for Laravels authentication system,27,52,25,85,0,0,,40.4,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); No open issues; Old releases,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-06-21T09:06:55Z,2013-11-28T14:34:15Z,No vulnerabilities found,None
72,KABBOUCHI/nova-impersonate,https://github.com/KABBOUCHI/nova-impersonate,https://github.com/KABBOUCHI/nova-impersonate.git,234,18,PHP,A Laravel Nova field allows you to authenticate as your users.,28,60,25,70,0,0,,40.25,Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10),Low activity (last year); Moderate open issues; Release within year,No security policy found; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-04-14T08:24:55Z,2018-09-09T16:31:05Z,No vulnerabilities found,None
73,FusionGen/FusionGEN,https://github.com/FusionGen/FusionGEN,https://github.com/FusionGen/FusionGEN.git,110,10,PHP,This is a Continuation of FusionCMS but now under the brand name FusionGEN.  This CMS are an Open-Source Project and anyone may use & contribute this.  The main goal is to get something long-term wise going. This CMS just needs love...!,33,62,45,30,0,0,,40.15,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; No releases,Has .github/SECURITY.md policy; No branch protection; Has CI/CD workflows,No releases found; No testing infrastructure detected; Comprehensive README,2025-07-11T21:41:16Z,2020-05-01T15:31:30Z,No vulnerabilities found,None
74,PHPAuth/PHPAuth,https://github.com/PHPAuth/PHPAuth,https://github.com/PHPAuth/PHPAuth.git,886,17,PHP,  PHPAuth is a secure PHP Authentication class that easily integrates into any site. ,27,50,20,90,0,0,,40.0,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Low activity (last year); Moderate open issues; Old releases,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; Has testing infrastructure,2025-04-08T15:31:54Z,2014-01-25T21:34:44Z,No vulnerabilities found,None
75,abdessamadbettal/lara4,https://github.com/abdessamadbettal/lara4,https://github.com/abdessamadbettal/lara4.git,54,2,PHP,"Simplify your Laravel development with Lara4! Build faster and smarter with tools like Inertia.js, Vue, multi-language support, SEO optimization, and Filament CMS - all ready to use.",18,55,60,70,0,0,,39.5,High-risk application type (+8); High-risk language: php (+10),Moderately active (last 90 days); Few open issues; No releases,Has SECURITY.md policy; Security info in README; No branch protection,No releases found; Has testing infrastructure; Comprehensive README,2025-05-21T18:18:00Z,2024-11-07T12:51:41Z,No vulnerabilities found,None
76,fsi-open/admin-bundle,https://github.com/fsi-open/admin-bundle,https://github.com/fsi-open/admin-bundle.git,58,11,PHP,FSi Admin Bundle is complete solution that provides mechanisms to generate admin panel for any Symfony2 project.,25,67,35,55,0,0,,39.4,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Moderately active (last 90 days); Moderate open issues; Release within year,No security policy found; Security info in README; No branch protection,Follows semantic versioning; No testing infrastructure detected; Comprehensive README,2025-06-10T13:18:54Z,2013-05-22T12:17:16Z,No vulnerabilities found,None
77,tomatophp/filament-cms,https://github.com/tomatophp/filament-cms,https://github.com/tomatophp/filament-cms.git,102,1,PHP,Full CMS System with easy to use page builder & theme manager for FilamentPHP,28,60,15,70,0,0,,38.75,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Low activity (last year); Few open issues; Release within year,Has SECURITY.md policy; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-04-16T04:48:30Z,2024-05-09T11:54:48Z,No vulnerabilities found,None
78,pluxml/PluXml,https://github.com/pluxml/PluXml,https://github.com/pluxml/PluXml.git,229,20,PHP,A CMS to create lightweight websites with ease and without database.,28,67,20,55,0,0,,38.65,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; Release within year,Has .github/SECURITY.md policy; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-27T17:44:28Z,2012-07-26T07:55:26Z,No vulnerabilities found,None
79,putyourlightson/craft-campaign,https://github.com/putyourlightson/craft-campaign,https://github.com/putyourlightson/craft-campaign.git,63,6,PHP,"Send and manage email campaigns, contacts and mailing lists in Craft CMS.",18,87,15,65,0,0,,38.400000000000006,High-risk application type (+8); High-risk language: php (+10),Recently active (last 30 days); Few open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-07-22T09:08:32Z,2018-03-07T11:23:13Z,No vulnerabilities found,None
80,craftcms/aws-s3,https://github.com/craftcms/aws-s3,https://github.com/craftcms/aws-s3.git,62,18,PHP,Amazon S3 volume type for Craft CMS.,18,72,30,70,0,0,,38.4,High-risk application type (+8); High-risk language: php (+10),Moderately active (last 90 days); Moderate open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-06-17T02:45:17Z,2017-01-30T20:26:48Z,No vulnerabilities found,None
81,kleeja-official/kleeja,https://github.com/kleeja-official/kleeja,https://github.com/kleeja-official/kleeja.git,185,13,PHP,"?? File Upload/sharing application, used by thousands of webmasters since 2007. ",35,55,10,55,0,0,,38.25,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),Moderately active (last 90 days); Moderate open issues; Old releases,No security policy found; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-05-07T19:19:54Z,2018-01-08T22:57:26Z,No vulnerabilities found,None
82,processwire/processwire,https://github.com/processwire/processwire,https://github.com/processwire/processwire.git,1004,71,PHP,ProcessWire 3.x is a friendly and powerful open source CMS with a strong API. ,38,50,15,45,0,0,,38.0,High-risk application type (+8); Vulnerability keywords (+3); Optimal star range (+10),Recently active (last 30 days); Many open issues (71); No releases,No security policy found; Security info in README; No branch protection,No releases found; No testing infrastructure detected; Comprehensive README,2025-07-25T20:46:12Z,2016-08-29T09:59:35Z,No vulnerabilities found,None
83,aimeos/ai-cms-grapesjs,https://github.com/aimeos/ai-cms-grapesjs,https://github.com/aimeos/ai-cms-grapesjs.git,608,2,PHP,GrapesJS CMS integration into Aimeos,33,67,15,30,0,0,,36.65,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; No releases,No security policy found; Security info in README; No branch protection,No releases found; No testing infrastructure detected; Comprehensive README,2025-07-09T07:05:34Z,2021-01-24T12:59:28Z,No vulnerabilities found,None
84,KeyAuth/KeyAuth-Source-Code,https://github.com/KeyAuth/KeyAuth-Source-Code,https://github.com/KeyAuth/KeyAuth-Source-Code.git,266,0,PHP,KeyAuth is an open source authentication system with cloud-hosted solutions available as well.,32,67,15,20,0,0,,34.65,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Recently active (last 30 days); No open issues; No releases,No security policy found; No branch protection; Has CI/CD workflows,No releases found; No testing infrastructure detected; Comprehensive README,2025-07-04T16:28:09Z,2021-06-23T21:51:36Z,No vulnerabilities found,None
85,PHPVibe/PHPVibe-Video-Sharing-CMS,https://github.com/PHPVibe/PHPVibe-Video-Sharing-CMS,https://github.com/PHPVibe/PHPVibe-Video-Sharing-CMS.git,82,17,PHP,PHPVibe - Video Sharing CMS,23,55,25,55,0,0,,34.5,High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 2.6y (+5),Moderately active (last 90 days); Moderate open issues; Recent release,No security policy found; Security info in README; No branch protection,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-05-20T11:47:48Z,2023-01-05T13:02:23Z,No vulnerabilities found,None
86,ashraf-kabir/personal-blog,https://github.com/ashraf-kabir/personal-blog,https://github.com/ashraf-kabir/personal-blog.git,63,1,PHP,"Dynamic blogsite using PHP, JS & mysql where user can register, log in, post blog with images, comment, edit profile, change password, etc. There is a separate admin panel also where admin can approve user & theirs posts & comments and manipulate the whole site.",36,65,5,15,0,0,,34.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+9),Recently active (last 30 days); Few open issues; No releases,No security policy found; No branch protection; No CI/CD workflows,No releases found; No testing infrastructure detected; Basic README,2025-07-05T06:22:15Z,2019-08-26T17:23:48Z,No vulnerabilities found,None
87,delight-im/PHP-Auth,https://github.com/delight-im/PHP-Auth,https://github.com/delight-im/PHP-Auth.git,1177,34,PHP,"Authentication for PHP. Simple, lightweight and secure.",27,52,35,30,0,0,,33.65,Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10),Moderately active (last 90 days); Moderate open issues; No releases,No security policy found; Security info in README; No branch protection,No releases found; No testing infrastructure detected; Comprehensive README,2025-06-04T17:48:29Z,2015-10-20T12:26:04Z,No vulnerabilities found,None
88,FriendsOfREDAXO/cke5,https://github.com/FriendsOfREDAXO/cke5,https://github.com/FriendsOfREDAXO/cke5.git,54,6,PHP,Integrates the CKEditor5 into REDAXO CMS,18,70,10,60,0,0,,33.5,High-risk application type (+8); High-risk language: php (+10),Moderately active (last 90 days); Few open issues; Recent release,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-05-16T14:50:57Z,2018-05-04T18:10:51Z,No vulnerabilities found,None
89,modxcms/fred,https://github.com/modxcms/fred,https://github.com/modxcms/fred.git,62,42,PHP,"The friendly front-end editor for visual, drag-and-drop content building in MODX CMS",18,47,35,65,0,0,,33.4,High-risk application type (+8); High-risk language: php (+10),Moderately active (last 90 days); Moderate open issues; Old releases,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-04-30T21:52:32Z,2018-01-09T17:58:59Z,No vulnerabilities found,None
90,azerothcore/acore-cms,https://github.com/azerothcore/acore-cms,https://github.com/azerothcore/acore-cms.git,62,21,PHP,ACore CMS based on Wordpress,31,57,15,25,0,0,,32.9,High-risk application type (+16); High-risk language: php (+10); Vulnerable age: 5.2y (+5),Recently active (last 30 days); Moderate open issues; No releases,No security policy found; No branch protection; Has CI/CD workflows,No releases found; No testing infrastructure detected; Comprehensive README,2025-07-21T07:10:29Z,2020-05-30T10:24:05Z,No vulnerabilities found,None
91,simplygoodwork/craft-donkeytail,https://github.com/simplygoodwork/craft-donkeytail,https://github.com/simplygoodwork/craft-donkeytail.git,52,13,PHP,A Craft CMS fieldtype for content managing points on images.,18,52,20,70,0,0,,32.9,High-risk application type (+8); High-risk language: php (+10),Low activity (last year); Moderate open issues; Release within year,No security policy found; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-04-16T14:50:56Z,2016-06-29T16:08:27Z,No vulnerabilities found,None
92,OFFLINE-GmbH/oc-bootstrapper,https://github.com/OFFLINE-GmbH/oc-bootstrapper,https://github.com/OFFLINE-GmbH/oc-bootstrapper.git,81,7,PHP,Easily bootstrap a new October CMS project,18,57,15,65,0,0,,32.4,High-risk application type (+8); High-risk language: php (+10),Moderately active (last 90 days); Few open issues; Old releases,No security policy found; No branch protection; Has CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-06-28T13:19:34Z,2016-02-24T15:17:35Z,No vulnerabilities found,None
93,lautaroangelico/WebEngine,https://github.com/lautaroangelico/WebEngine,https://github.com/lautaroangelico/WebEngine.git,134,2,PHP,"WebEngine is an open-source, fast and secure CMS for private Mu Online game servers.",28,50,0,50,0,0,,31.5,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Low activity (last year); Few open issues; Release within year,No security policy found; No branch protection; No CI/CD workflows,Follows semantic versioning; Has release notes; No testing infrastructure detected,2025-04-25T05:49:10Z,2017-05-14T11:34:27Z,No vulnerabilities found,None
94,justitems/midrub_cms,https://github.com/justitems/midrub_cms,https://github.com/justitems/midrub_cms.git,56,0,PHP,Midrub - the first CMS only for business,23,55,15,45,0,0,,31.5,High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 5.5y (+5),Low activity (last year); No open issues; Old releases,No security policy found; No branch protection; No CI/CD workflows,Some semantic versioning; No testing infrastructure detected; Comprehensive README,2025-04-22T10:35:36Z,2020-01-20T12:27:37Z,No vulnerabilities found,None
95,maxsite/cms,https://github.com/maxsite/cms,https://github.com/maxsite/cms.git,146,2,PHP,MaxSite CMS. Free CMS for your website. Smart alternative to WordPress,36,52,0,20,0,0,,31.4,High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10),Recently active (last 30 days); Few open issues; No releases,No security policy found; No branch protection; No CI/CD workflows,No releases found; No testing infrastructure detected; Comprehensive README,2025-07-15T07:21:00Z,2012-11-14T12:12:35Z,No vulnerabilities found,None
96,xpertbot/craft-wheelform,https://github.com/xpertbot/craft-wheelform,https://github.com/xpertbot/craft-wheelform.git,66,24,PHP,Craft CMS 4 Form with Database integration,18,60,30,35,0,0,,30.75,High-risk application type (+8); High-risk language: php (+10),Moderately active (last 90 days); Moderate open issues; No releases,No security policy found; No branch protection; Has CI/CD workflows,No releases found; No testing infrastructure detected; Comprehensive README,2025-05-21T19:55:12Z,2018-02-04T22:16:50Z,No vulnerabilities found,None
97,corbado/passkeys-php-laravel,https://github.com/corbado/passkeys-php-laravel,https://github.com/corbado/passkeys-php-laravel.git,81,0,PHP,Add passkeys to your PHP laravel application using the Corbado passkey-first authentication solution.,17,60,20,40,0,0,,29.5,Critical features (+4); Vulnerability keywords (+3); High-risk language: php (+10),Moderately active (last 90 days); No open issues; No releases,No security policy found; No branch protection; No CI/CD workflows,No releases found; Has testing infrastructure; Comprehensive README,2025-06-02T09:36:24Z,2024-12-30T09:42:35Z,No vulnerabilities found,None
98,plan2net/webp,https://github.com/plan2net/webp,https://github.com/plan2net/webp.git,65,13,PHP,Create a WebP copy for images (TYPO3 CMS),18,70,5,30,0,0,,28.25,High-risk application type (+8); High-risk language: php (+10),Recently active (last 30 days); Moderate open issues; No releases,No security policy found; No branch protection; No CI/CD workflows,No releases found; No testing infrastructure detected; Comprehensive README,2025-06-30T19:49:29Z,2018-03-13T19:45:34Z,No vulnerabilities found,None
99,TeamEasy/EasyCMS,https://github.com/TeamEasy/EasyCMS,https://github.com/TeamEasy/EasyCMS.git,102,0,PHP,cms by thinkphp3.1.3+dwz+bootstrap,28,50,5,15,0,0,,27.0,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),Moderately active (last 90 days); No open issues; No releases,No security policy found; No branch protection; No CI/CD workflows,No releases found; No testing infrastructure detected; Basic README,2025-06-07T10:49:18Z,2014-04-14T00:13:44Z,No vulnerabilities found,None
100,romadebrian/WEB-Sekolah,https://github.com/romadebrian/WEB-Sekolah,https://github.com/romadebrian/WEB-Sekolah.git,65,3,PHP,Source Code untuk Web sekolah (CMS),18,50,0,40,0,0,,25.0,High-risk application type (+8); High-risk language: php (+10),Moderately active (last 90 days); Few open issues; Old releases,No security policy found; No branch protection; No CI/CD workflows,Some semantic versioning; Has release notes; No testing infrastructure detected,2025-06-03T23:22:18Z,2018-07-16T12:39:46Z,No vulnerabilities found,None
