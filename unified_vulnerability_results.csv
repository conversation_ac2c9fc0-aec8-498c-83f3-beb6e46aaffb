rank,repo_name,repo_url,stars,issues,language,description,risk_score,semgrep_vulnerabilities,semgrep_severity_score,vulnerability_categories,final_score,risk_factors,last_updated,created_at,semgrep_findings_summary,detailed_vulnerabilities
1,uasoft-indonesia/badaso,https://github.com/uasoft-indonesia/badaso,1237,1,PHP,"Laravel Vue headless CMS / admin panel / dashboard / builder / API CRUD generator, anything !",59,0,0,,59.0,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+6),2025-06-13T09:54:58Z,2021-03-15T04:40:50Z,No vulnerabilities found,None
2,moonshine-software/moonshine,https://github.com/moonshine-software/moonshine,1089,8,PHP,"Laravel Admin panel and more. Simple for beginners and powerful for experts. Using Blade, Alpine.js and Tailwind CSS.",56,0,0,,56.0,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+3),2025-07-27T18:55:38Z,2022-05-12T15:31:35Z,No vulnerabilities found,None
3,alperenersoy/filament-export,https://github.com/alperenersoy/filament-export,257,17,PHP,Customizable export and print functionality for Filament Admin Panel,44,0,0,,44.0,High-risk application type (+8); Critical features (+8); Vulnerability keywords (+3),2025-06-30T18:46:38Z,2022-05-06T19:54:09Z,No vulnerabilities found,None
4,e107inc/e107,https://github.com/e107inc/e107,329,691,PHP,"e107 Bootstrap CMS (Content Management System) v2 with PHP, MySQL, HTML5, jQuery and Twitter Bootstrap. Issue Discussion Room: https://gitter.im/e107inc/e107 ",44,0,0,,44.0,High-risk application type (+8); Vulnerability keywords (+6); Optimal star range (+10),2025-07-20T18:33:58Z,2012-11-16T19:49:01Z,No vulnerabilities found,None
5,lara-zeus/sky,https://github.com/lara-zeus/sky,191,1,PHP,"CMS for your website. it include posts, pages, tags, and categories. with a frontend scaffolding ready to use",44,0,0,,44.0,High-risk application type (+16); Vulnerability keywords (+3); Optimal star range (+10),2025-07-08T10:22:33Z,2022-03-28T21:38:35Z,No vulnerabilities found,None
6,ClassicPress/ClassicPress,https://github.com/ClassicPress/ClassicPress,807,39,PHP,The CMS for Creators. Stable. Lightweight. Instantly Familiar. Forked from WordPress.,41,0,0,,41.0,High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10),2025-07-27T10:05:16Z,2018-08-29T10:02:51Z,No vulnerabilities found,None
7,stephenjude/filament-blog,https://github.com/stephenjude/filament-blog,189,2,PHP,A faceless blog content manager with configurable richtext and markdown support for filament admin panel,40,0,0,,40.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-06-17T08:42:27Z,2022-02-12T19:22:31Z,No vulnerabilities found,None
8,bezhanSalleh/filament-exceptions,https://github.com/bezhanSalleh/filament-exceptions,182,2,PHP,A Simple & Beautiful Pluggable Exception Viewer for FilamentPHP's Admin Panel,40,0,0,,40.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-06-16T10:45:33Z,2022-09-04T05:40:51Z,No vulnerabilities found,None
9,et-nik/gameap,https://github.com/et-nik/gameap,115,40,PHP,Game Admin Panel (GameAP) is the opensource game servers control panel.,40,0,0,,40.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-05-15T07:09:45Z,2018-11-21T12:01:13Z,No vulnerabilities found,None
10,saade/filament-laravel-log,https://github.com/saade/filament-laravel-log,111,9,PHP,Read Laravel logs from the Filament admin panel,40,0,0,,40.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-07-21T18:47:54Z,2022-03-18T03:09:11Z,No vulnerabilities found,None
