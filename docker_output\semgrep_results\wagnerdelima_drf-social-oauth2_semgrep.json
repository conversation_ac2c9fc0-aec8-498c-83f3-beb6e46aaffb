{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/wagnerdelima_drf-social-oauth2/api.yaml", "start": {"line": 235, "col": 27, "offset": 6924}, "end": {"line": 235, "col": 121, "offset": 7018}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/wagnerdelima_drf-social-oauth2/api.yaml", "start": {"line": 253, "col": 27, "offset": 7821}, "end": {"line": 253, "col": 121, "offset": 7915}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/wagnerdelima_drf-social-oauth2/.flake8", "downloaded_repos/wagnerdelima_drf-social-oauth2/.github/FUNDING.yml", "downloaded_repos/wagnerdelima_drf-social-oauth2/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/wagnerdelima_drf-social-oauth2/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/wagnerdelima_drf-social-oauth2/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/wagnerdelima_drf-social-oauth2/.github/workflows/codecov.yml", "downloaded_repos/wagnerdelima_drf-social-oauth2/.github/workflows/pythonapp.yml", "downloaded_repos/wagnerdelima_drf-social-oauth2/.gitignore", "downloaded_repos/wagnerdelima_drf-social-oauth2/.pre-commit-config.yaml", "downloaded_repos/wagnerdelima_drf-social-oauth2/.readthedocs.yaml", "downloaded_repos/wagnerdelima_drf-social-oauth2/CHANGELOG.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/CODE_OF_CONDUCT.md", "downloaded_repos/wagnerdelima_drf-social-oauth2/CONTRIBUTING.md", "downloaded_repos/wagnerdelima_drf-social-oauth2/Dockerfile", "downloaded_repos/wagnerdelima_drf-social-oauth2/LICENSE.txt", "downloaded_repos/wagnerdelima_drf-social-oauth2/MANIFEST.in", "downloaded_repos/wagnerdelima_drf-social-oauth2/Makefile", "downloaded_repos/wagnerdelima_drf-social-oauth2/README.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/SECURITY.md", "downloaded_repos/wagnerdelima_drf-social-oauth2/api.yaml", "downloaded_repos/wagnerdelima_drf-social-oauth2/components.sh", "downloaded_repos/wagnerdelima_drf-social-oauth2/docker-compose.tests.yml", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/application.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/authenticating.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/conf.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/customization.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/index.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/installation.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/integration.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/new_application.png", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/openapi-specs.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/setup-testing.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/swagger.png", "downloaded_repos/wagnerdelima_drf-social-oauth2/docs/source/tests.rst", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/__init__.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/authentication.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/backends.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/management/commands/createapp.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/oauth2_backends.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/oauth2_endpoints.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/oauth2_grants.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/serializers.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/settings.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/test_settings.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/urls.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/drf_social_oauth2/views.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/make.bat", "downloaded_repos/wagnerdelima_drf-social-oauth2/manage_test.py", "downloaded_repos/wagnerdelima_drf-social-oauth2/pyproject.toml", "downloaded_repos/wagnerdelima_drf-social-oauth2/pytest.ini", "downloaded_repos/wagnerdelima_drf-social-oauth2/requirements.test.txt", "downloaded_repos/wagnerdelima_drf-social-oauth2/requirements.txt", "downloaded_repos/wagnerdelima_drf-social-oauth2/setup.py"], "skipped": [{"path": "downloaded_repos/wagnerdelima_drf-social-oauth2/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wagnerdelima_drf-social-oauth2/tests/drf_social_oauth2/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wagnerdelima_drf-social-oauth2/tests/drf_social_oauth2/drf_fixtures.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wagnerdelima_drf-social-oauth2/tests/drf_social_oauth2/test_authentication.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wagnerdelima_drf-social-oauth2/tests/drf_social_oauth2/test_oauth2_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/wagnerdelima_drf-social-oauth2/tests/drf_social_oauth2/test_views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.134636878967285, "profiling_times": {"config_time": 6.702951431274414, "core_time": 3.3665382862091064, "ignores_time": 0.0022432804107666016, "total_time": 10.07286548614502}, "parsing_time": {"total_time": 0.5620763301849365, "per_file_time": {"mean": 0.02341984709103902, "std_dev": 0.00030556196579557674}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.1039505004882812, "per_file_time": {"mean": 0.02387654231144832, "std_dev": 0.003622760580082508}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7179374694824219, "per_file_and_rule_time": {"mean": 0.0020931121559254282, "std_dev": 2.2070379200512784e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.18688154220581055, "per_def_and_rule_time": {"mean": 0.0006627005042759239, "std_dev": 2.6493436871411817e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}