{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/feincms_feincms3/feincms3/admin.py", "start": {"line": 202, "col": 16, "offset": 6593}, "end": {"line": 202, "col": 44, "offset": 6621}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.query-set-extra.avoid-query-set-extra", "path": "downloaded_repos/feincms_feincms3/feincms3/admin.py", "start": {"line": 643, "col": 25, "offset": 21970}, "end": {"line": 645, "col": 14, "offset": 22104}, "extra": {"message": "QuerySet.extra' does not provide safeguards against SQL injection and requires very careful use. SQL injection can lead to critical data being stolen by attackers. Instead of using '.extra', use the Django ORM and parameterized queries such as `People.objects.get(name='<PERSON>')`.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b610_django_extra_used.html", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/models/querysets/#django.db.models.query.QuerySet.extra", "https://semgrep.dev/blog/2020/preventing-sql-injection-a-django-authors-perspective/"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.django.security.audit.query-set-extra.avoid-query-set-extra", "shortlink": "https://sg.run/kXZP"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/feincms_feincms3/feincms3/applications.py", "start": {"line": 296, "col": 19, "offset": 9490}, "end": {"line": 296, "col": 55, "offset": 9526}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/feincms_feincms3/feincms3/applications.py", "start": {"line": 651, "col": 30, "offset": 22285}, "end": {"line": 651, "col": 57, "offset": 22312}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms3/.github/workflows/tests.yml", "start": {"line": 35, "col": 50, "offset": 751}, "end": {"line": 35, "col": 73, "offset": 774}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms3/.github/workflows/tests.yml:35:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.python-version` was unexpected", "path": "downloaded_repos/feincms_feincms3/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/feincms_feincms3/.github/workflows/tests.yml", "start": {"line": 35, "col": 50, "offset": 751}, "end": {"line": 35, "col": 73, "offset": 774}}]}], "paths": {"scanned": ["downloaded_repos/feincms_feincms3/.editorconfig", "downloaded_repos/feincms_feincms3/.github/workflows/tests.yml", "downloaded_repos/feincms_feincms3/.gitignore", "downloaded_repos/feincms_feincms3/.pre-commit-config.yaml", "downloaded_repos/feincms_feincms3/.readthedocs.yaml", "downloaded_repos/feincms_feincms3/CHANGELOG.rst", "downloaded_repos/feincms_feincms3/LICENSE", "downloaded_repos/feincms_feincms3/MANIFEST.in", "downloaded_repos/feincms_feincms3/README.rst", "downloaded_repos/feincms_feincms3/biome.json", "downloaded_repos/feincms_feincms3/docs/Makefile", "downloaded_repos/feincms_feincms3/docs/_static/.gitignore", "downloaded_repos/feincms_feincms3/docs/build-your-cms.rst", "downloaded_repos/feincms_feincms3/docs/conf.py", "downloaded_repos/feincms_feincms3/docs/guides/apps-and-instances.rst", "downloaded_repos/feincms_feincms3/docs/guides/apps-form-builder.rst", "downloaded_repos/feincms_feincms3/docs/guides/apps-introduction.rst", "downloaded_repos/feincms_feincms3/docs/guides/cookie-control.rst", "downloaded_repos/feincms_feincms3/docs/guides/meta-opengraph-tags.rst", "downloaded_repos/feincms_feincms3/docs/guides/multilingual-sites.rst", "downloaded_repos/feincms_feincms3/docs/guides/multisite-custom.rst", "downloaded_repos/feincms_feincms3/docs/guides/multisite.rst", "downloaded_repos/feincms_feincms3/docs/guides/navigation.rst", "downloaded_repos/feincms_feincms3/docs/guides/plugins.rst", "downloaded_repos/feincms_feincms3/docs/guides/redirects.rst", "downloaded_repos/feincms_feincms3/docs/guides/rendering.rst", "downloaded_repos/feincms_feincms3/docs/guides/templates-and-regions.rst", "downloaded_repos/feincms_feincms3/docs/guides/urls-and-views.rst", "downloaded_repos/feincms_feincms3/docs/index.rst", "downloaded_repos/feincms_feincms3/docs/installation.rst", "downloaded_repos/feincms_feincms3/docs/introduction.rst", "downloaded_repos/feincms_feincms3/docs/make.bat", "downloaded_repos/feincms_feincms3/docs/project/changelog.rst", "downloaded_repos/feincms_feincms3/docs/project/contributing.rst", "downloaded_repos/feincms_feincms3/docs/ref/admin.rst", "downloaded_repos/feincms_feincms3/docs/ref/applications.rst", "downloaded_repos/feincms_feincms3/docs/ref/cleansing.rst", "downloaded_repos/feincms_feincms3/docs/ref/embedding.rst", "downloaded_repos/feincms_feincms3/docs/ref/inline_ckeditor.rst", "downloaded_repos/feincms_feincms3/docs/ref/mixins.rst", "downloaded_repos/feincms_feincms3/docs/ref/pages.rst", "downloaded_repos/feincms_feincms3/docs/ref/plugins.rst", "downloaded_repos/feincms_feincms3/docs/ref/regions.rst", "downloaded_repos/feincms_feincms3/docs/ref/renderer.rst", "downloaded_repos/feincms_feincms3/docs/ref/root.rst", "downloaded_repos/feincms_feincms3/docs/ref/shortcuts.rst", "downloaded_repos/feincms_feincms3/docs/ref/templatetags.rst", "downloaded_repos/feincms_feincms3/docs/ref/utils.rst", "downloaded_repos/feincms_feincms3/docs/requirements.txt", "downloaded_repos/feincms_feincms3/feincms3/__init__.py", "downloaded_repos/feincms_feincms3/feincms3/admin.py", "downloaded_repos/feincms_feincms3/feincms3/applications.py", "downloaded_repos/feincms_feincms3/feincms3/cleanse.py", "downloaded_repos/feincms_feincms3/feincms3/embedding.py", "downloaded_repos/feincms_feincms3/feincms3/inline_ckeditor.py", "downloaded_repos/feincms_feincms3/feincms3/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms3/feincms3/locale/de/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms3/feincms3/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms3/feincms3/locale/en/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms3/feincms3/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms3/feincms3/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms3/feincms3/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms3/feincms3/locale/it/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms3/feincms3/locale/nb_NO/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms3/feincms3/locale/nb_NO/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms3/feincms3/mixins.py", "downloaded_repos/feincms_feincms3/feincms3/pages.py", "downloaded_repos/feincms_feincms3/feincms3/plugins/__init__.py", "downloaded_repos/feincms_feincms3/feincms3/plugins/external.py", "downloaded_repos/feincms_feincms3/feincms3/plugins/html.py", "downloaded_repos/feincms_feincms3/feincms3/plugins/image.py", "downloaded_repos/feincms_feincms3/feincms3/plugins/old_richtext.py", "downloaded_repos/feincms_feincms3/feincms3/plugins/richtext.py", "downloaded_repos/feincms_feincms3/feincms3/plugins/snippet.py", "downloaded_repos/feincms_feincms3/feincms3/regions.py", "downloaded_repos/feincms_feincms3/feincms3/renderer.py", "downloaded_repos/feincms_feincms3/feincms3/root/__init__.py", "downloaded_repos/feincms_feincms3/feincms3/root/middleware.py", "downloaded_repos/feincms_feincms3/feincms3/root/passthru.py", "downloaded_repos/feincms_feincms3/feincms3/shortcuts.py", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/admin.css", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/admin.js", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/inline-ckeditor-contents.css", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/inline-ckeditor.css", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/inline-ckeditor.js", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/move-form.css", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/plugin-ckeditor.css", "downloaded_repos/feincms_feincms3/feincms3/static/feincms3/static-path-style.js", "downloaded_repos/feincms_feincms3/feincms3/templatetags/__init__.py", "downloaded_repos/feincms_feincms3/feincms3/templatetags/feincms3.py", "downloaded_repos/feincms_feincms3/feincms3/utils.py", "downloaded_repos/feincms_feincms3/pyproject.toml", "downloaded_repos/feincms_feincms3/tox.ini"], "skipped": [{"path": "downloaded_repos/feincms_feincms3/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms3/tests/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/importable_module.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/articles_urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/renderer.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/base.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/pages/standard.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/pages/with-sidebar.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/renderer/html.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/renderer/image.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/renderer/richtext.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/snippet.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/testapp/article_detail.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templates/testapp/article_list.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templatetags/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/templatetags/menus.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_applications.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_cleanse.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_embedding.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_inline_ckeditor.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_languages.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_mixins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_oembed.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_oldregions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_pages.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_passthru.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_region_renderer.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/translated_articles_urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms3/tests/testapp/utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.895348072052002, "profiling_times": {"config_time": 7.1083362102508545, "core_time": 3.5987300872802734, "ignores_time": 0.002310514450073242, "total_time": 10.71030306816101}, "parsing_time": {"total_time": 0.6545534133911133, "per_file_time": {"mean": 0.020454794168472287, "std_dev": 0.000409838722460698}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 5.645135164260864, "per_file_time": {"mean": 0.025895115432389287, "std_dev": 0.007482849318216662}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.0170297622680664, "per_file_and_rule_time": {"mean": 0.005198530315123882, "std_dev": 0.00017632866262636755}, "very_slow_stats": {"time_ratio": 0.07231127621679151, "count_ratio": 0.002577319587628866}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/feincms_feincms3/feincms3/plugins/external.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.14585399627685547}]}, "tainting_time": {"total_time": 0.8744003772735596, "per_def_and_rule_time": {"mean": 0.0008572552718368229, "std_dev": 4.329661038464653e-05}, "very_slow_stats": {"time_ratio": 0.23269223375808215, "count_ratio": 0.000980392156862745}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/feincms_feincms3/feincms3/pages.py", "fline": 135, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20346617698669434}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}