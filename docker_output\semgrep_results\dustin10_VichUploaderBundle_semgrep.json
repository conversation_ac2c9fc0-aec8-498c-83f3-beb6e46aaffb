{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugClassCommand.php", "start": {"line": 17, "col": 41, "offset": 0}, "end": {"line": 17, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugClassCommand.php:17:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugClassCommand.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugClassCommand.php", "start": {"line": 17, "col": 41, "offset": 0}, "end": {"line": 17, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugCommand.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 55, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugCommand.php:17:\n `array` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugCommand.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugCommand.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 55, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingListClassesCommand.php", "start": {"line": 14, "col": 41, "offset": 0}, "end": {"line": 14, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingListClassesCommand.php:14:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingListClassesCommand.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingListClassesCommand.php", "start": {"line": 14, "col": 41, "offset": 0}, "end": {"line": 14, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/DataCollector/MappingCollector.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/DataCollector/MappingCollector.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/DataCollector/MappingCollector.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/DataCollector/MappingCollector.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php", "start": {"line": 17, "col": 52, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 6}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php", "start": {"line": 17, "col": 78, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php:17:\n `object` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php", "start": {"line": 17, "col": 52, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 6}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php", "start": {"line": 17, "col": 78, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 18, "col": 28, "offset": 0}, "end": {"line": 18, "col": 34, "offset": 6}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 27, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 27, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 21, "col": 19, "offset": 0}, "end": {"line": 21, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php:18:\n `string` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 18, "col": 28, "offset": 0}, "end": {"line": 18, "col": 34, "offset": 6}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 27, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 27, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "start": {"line": 21, "col": 19, "offset": 0}, "end": {"line": 21, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php", "start": {"line": 18, "col": 26, "offset": 0}, "end": {"line": 18, "col": 30, "offset": 4}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 30, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php:18:\n `bool` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php", "start": {"line": 18, "col": 26, "offset": 0}, "end": {"line": 18, "col": 30, "offset": 4}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 30, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 28, "col": 15, "offset": 0}, "end": {"line": 28, "col": 23, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 31, "col": 19, "offset": 0}, "end": {"line": 31, "col": 27, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 28, "col": 15, "offset": 0}, "end": {"line": 28, "col": 23, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 31, "col": 19, "offset": 0}, "end": {"line": 31, "col": 27, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichImageType.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichImageType.php:33:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichImageType.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichImageType.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php", "start": {"line": 16, "col": 19, "offset": 0}, "end": {"line": 16, "col": 27, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php", "start": {"line": 17, "col": 19, "offset": 0}, "end": {"line": 17, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php", "start": {"line": 16, "col": 19, "offset": 0}, "end": {"line": 16, "col": 27, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php", "start": {"line": 17, "col": 19, "offset": 0}, "end": {"line": 17, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php", "start": {"line": 26, "col": 19, "offset": 0}, "end": {"line": 26, "col": 27, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php", "start": {"line": 27, "col": 19, "offset": 0}, "end": {"line": 27, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php", "start": {"line": 26, "col": 19, "offset": 0}, "end": {"line": 26, "col": 27, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php", "start": {"line": 27, "col": 19, "offset": 0}, "end": {"line": 27, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Injector/FileInjector.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Injector/FileInjector.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Injector/FileInjector.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Injector/FileInjector.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php:24:\n `string` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php", "start": {"line": 12, "col": 50, "offset": 0}, "end": {"line": 12, "col": 56, "offset": 6}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php", "start": {"line": 12, "col": 71, "offset": 0}, "end": {"line": 12, "col": 79, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php:12:\n `string` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php", "start": {"line": 12, "col": 50, "offset": 0}, "end": {"line": 12, "col": 56, "offset": 6}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php", "start": {"line": 12, "col": 71, "offset": 0}, "end": {"line": 12, "col": 79, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/MetadataReader.php", "start": {"line": 24, "col": 41, "offset": 0}, "end": {"line": 24, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/MetadataReader.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/MetadataReader.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/MetadataReader.php", "start": {"line": 24, "col": 41, "offset": 0}, "end": {"line": 24, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/CurrentDateTimeDirectoryNamer.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Naming/CurrentDateTimeDirectoryNamer.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/CurrentDateTimeDirectoryNamer.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/CurrentDateTimeDirectoryNamer.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/OrignameNamer.php", "start": {"line": 19, "col": 41, "offset": 0}, "end": {"line": 19, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Naming/OrignameNamer.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/OrignameNamer.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/OrignameNamer.php", "start": {"line": 19, "col": 41, "offset": 0}, "end": {"line": 19, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php", "start": {"line": 23, "col": 13, "offset": 0}, "end": {"line": 23, "col": 21, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php", "start": {"line": 25, "col": 87, "offset": 0}, "end": {"line": 25, "col": 95, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php", "start": {"line": 23, "col": 13, "offset": 0}, "end": {"line": 23, "col": 21, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php", "start": {"line": 25, "col": 87, "offset": 0}, "end": {"line": 25, "col": 95, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyNamer.php", "start": {"line": 22, "col": 41, "offset": 0}, "end": {"line": 22, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyNamer.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyNamer.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyNamer.php", "start": {"line": 22, "col": 41, "offset": 0}, "end": {"line": 22, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "start": {"line": 17, "col": 41, "offset": 0}, "end": {"line": 17, "col": 49, "offset": 8}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "start": {"line": 17, "col": 99, "offset": 0}, "end": {"line": 17, "col": 105, "offset": 6}}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "start": {"line": 17, "col": 133, "offset": 0}, "end": {"line": 17, "col": 139, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php:17:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "start": {"line": 17, "col": 41, "offset": 0}, "end": {"line": 17, "col": 49, "offset": 8}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "start": {"line": 17, "col": 99, "offset": 0}, "end": {"line": 17, "col": 105, "offset": 6}}, {"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "start": {"line": 17, "col": 133, "offset": 0}, "end": {"line": 17, "col": 139, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SmartUniqueNamer.php", "start": {"line": 18, "col": 41, "offset": 0}, "end": {"line": 18, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SmartUniqueNamer.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SmartUniqueNamer.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SmartUniqueNamer.php", "start": {"line": 18, "col": 41, "offset": 0}, "end": {"line": 18, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/AbstractStorage.php", "start": {"line": 19, "col": 43, "offset": 0}, "end": {"line": 19, "col": 51, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Storage/AbstractStorage.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/AbstractStorage.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/AbstractStorage.php", "start": {"line": 19, "col": 43, "offset": 0}, "end": {"line": 19, "col": 51, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Templating/Helper/UploaderHelper.php", "start": {"line": 14, "col": 41, "offset": 0}, "end": {"line": 14, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Templating/Helper/UploaderHelper.php:14:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Templating/Helper/UploaderHelper.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Templating/Helper/UploaderHelper.php", "start": {"line": 14, "col": 41, "offset": 0}, "end": {"line": 14, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Twig/Extension/UploaderExtensionRuntime.php", "start": {"line": 13, "col": 41, "offset": 0}, "end": {"line": 13, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Twig/Extension/UploaderExtensionRuntime.php:13:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Twig/Extension/UploaderExtensionRuntime.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Twig/Extension/UploaderExtensionRuntime.php", "start": {"line": 13, "col": 41, "offset": 0}, "end": {"line": 13, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Util/Transliterator.php", "start": {"line": 12, "col": 41, "offset": 0}, "end": {"line": 12, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/dustin10_VichUploaderBundle/src/Util/Transliterator.php:12:\n `readonly` was unexpected", "path": "downloaded_repos/dustin10_VichUploaderBundle/src/Util/Transliterator.php", "spans": [{"file": "downloaded_repos/dustin10_VichUploaderBundle/src/Util/Transliterator.php", "start": {"line": 12, "col": 41, "offset": 0}, "end": {"line": 12, "col": 49, "offset": 8}}]}], "paths": {"scanned": ["downloaded_repos/dustin10_VichUploaderBundle/.gitattributes", "downloaded_repos/dustin10_VichUploaderBundle/.github/ISSUE_TEMPLATE/BC_Break.md", "downloaded_repos/dustin10_VichUploaderBundle/.github/ISSUE_TEMPLATE/Bug.md", "downloaded_repos/dustin10_VichUploaderBundle/.github/ISSUE_TEMPLATE/Feature_Request.md", "downloaded_repos/dustin10_VichUploaderBundle/.github/ISSUE_TEMPLATE/Support_Question.md", "downloaded_repos/dustin10_VichUploaderBundle/.github/PULL_REQUEST_TEMPLATE/Failing_Test.md", "downloaded_repos/dustin10_VichUploaderBundle/.github/PULL_REQUEST_TEMPLATE/Improvement.md", "downloaded_repos/dustin10_VichUploaderBundle/.github/PULL_REQUEST_TEMPLATE/New_Feature.md", "downloaded_repos/dustin10_VichUploaderBundle/.github/workflows/build.yaml", "downloaded_repos/dustin10_VichUploaderBundle/.github/workflows/config/config.json", "downloaded_repos/dustin10_VichUploaderBundle/.gitignore", "downloaded_repos/dustin10_VichUploaderBundle/.php-cs-fixer.php", "downloaded_repos/dustin10_VichUploaderBundle/CONTRIBUTING.md", "downloaded_repos/dustin10_VichUploaderBundle/LICENSE", "downloaded_repos/dustin10_VichUploaderBundle/Makefile", "downloaded_repos/dustin10_VichUploaderBundle/README.md", "downloaded_repos/dustin10_VichUploaderBundle/UPGRADE.md", "downloaded_repos/dustin10_VichUploaderBundle/composer.json", "downloaded_repos/dustin10_VichUploaderBundle/config/adapter.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/collector.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/command.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/doctrine/File.orm.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/factory.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/flysystem.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/form.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/gaufrette.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/handler.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/injector.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/listener.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/mapping.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/namer.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/storage.xml", "downloaded_repos/dustin10_VichUploaderBundle/config/twig.xml", "downloaded_repos/dustin10_VichUploaderBundle/docker/Dockerfile81", "downloaded_repos/dustin10_VichUploaderBundle/docker/Dockerfile82", "downloaded_repos/dustin10_VichUploaderBundle/docker/Dockerfile83", "downloaded_repos/dustin10_VichUploaderBundle/docker/Dockerfile84", "downloaded_repos/dustin10_VichUploaderBundle/docs/commands.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/configuration_reference.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/directory_namer/howto/create_a_custom_directory_namer.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/downloads/serving_files_with_a_controller.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/events/events.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/events/howto/remove_files_asynchronously.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/file_namer/howto/create_a_custom_file_namer.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/form/vich_file_type.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/form/vich_image_type.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/generating_urls.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/index.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/installation.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/known_issues.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/mapping/xml.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/mapping/yaml.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/namers.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/other_usages/replacing_file.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/storage/custom.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/storage/flysystem.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/storage/gaufrette.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/symfony_support_policy.md", "downloaded_repos/dustin10_VichUploaderBundle/docs/usage.md", "downloaded_repos/dustin10_VichUploaderBundle/phpstan-baseline.neon", "downloaded_repos/dustin10_VichUploaderBundle/phpstan.neon", "downloaded_repos/dustin10_VichUploaderBundle/phpunit.xml.dist", "downloaded_repos/dustin10_VichUploaderBundle/src/Adapter/AdapterInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Adapter/ODM/MongoDB/MongoDBAdapter.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Adapter/ORM/DoctrineORMAdapter.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Adapter/PHPCR/PHPCRAdapter.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugClassCommand.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugCommand.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingListClassesCommand.php", "downloaded_repos/dustin10_VichUploaderBundle/src/DataCollector/MappingCollector.php", "downloaded_repos/dustin10_VichUploaderBundle/src/DependencyInjection/Compiler/RegisterFlysystemRegistryPass.php", "downloaded_repos/dustin10_VichUploaderBundle/src/DependencyInjection/Compiler/RegisterMappingDriversPass.php", "downloaded_repos/dustin10_VichUploaderBundle/src/DependencyInjection/Compiler/RegisterSluggerPass.php", "downloaded_repos/dustin10_VichUploaderBundle/src/DependencyInjection/Configuration.php", "downloaded_repos/dustin10_VichUploaderBundle/src/DependencyInjection/VichUploaderExtension.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Entity/File.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Event/ErrorEvent.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Events.php", "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/CleanListener.php", "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/InjectListener.php", "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/RemoveListener.php", "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/UploadListener.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Exception/MappingNotFoundException.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Exception/MissingPackageException.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Exception/NameGenerationException.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Exception/NoFileFoundException.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Exception/NotUploadableException.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Exception/VichUploaderExceptionInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Form/DataTransformer/FileTransformer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichImageType.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/DownloadHandler.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Injector/FileInjector.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Injector/FileInjectorInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/Uploadable.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/AnnotationInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMapping.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolverInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/ClassMetadata.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AbstractYamlDriver.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AttributeReader.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/XmlDriver.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/YamlDriver.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/YmlDriver.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/MetadataReader.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/Base64Namer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/ConfigurableDirectoryNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/ConfigurableInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/CurrentDateTimeDirectoryNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/DirectoryNamerInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/HashNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/NamerInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/OrignameNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/Polyfill/FileExtensionTrait.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SmartUniqueNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SubdirDirectoryNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/UniqidNamer.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Resources/config/doctrine/File.orm.xml", "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/AbstractStorage.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/FileSystemStorage.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/FlysystemStorage.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/GaufretteStorage.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/StorageInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Templating/Helper/UploaderHelper.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Templating/Helper/UploaderHelperInterface.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Twig/Extension/UploaderExtension.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Twig/Extension/UploaderExtensionRuntime.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Util/ClassUtils.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Util/PropertyPathUtils.php", "downloaded_repos/dustin10_VichUploaderBundle/src/Util/Transliterator.php", "downloaded_repos/dustin10_VichUploaderBundle/src/VichUploaderBundle.php", "downloaded_repos/dustin10_VichUploaderBundle/templates/Collector/ICON_LICENSE.txt", "downloaded_repos/dustin10_VichUploaderBundle/templates/Collector/icon.svg", "downloaded_repos/dustin10_VichUploaderBundle/templates/Collector/mapping_collector.html.twig", "downloaded_repos/dustin10_VichUploaderBundle/templates/Form/fields.html.twig", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.ar.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.bg.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.ca.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.cs.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.de.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.en.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.es.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.fi.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.fr.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.hu.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.it.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.lt.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.nl.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.pl.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.pt.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.ru.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.sl.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.tr.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.uk.yaml", "downloaded_repos/dustin10_VichUploaderBundle/translations/messages.vn.yaml", "downloaded_repos/dustin10_VichUploaderBundle/vich_uploader.xsd"], "skipped": [{"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugClassCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingDebugCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Command/MappingListClassesCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/DataCollector/MappingCollector.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Event/Event.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/EventListener/Doctrine/BaseListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/FileAbstraction/ReplacingFile.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichFileType.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Form/Type/VichImageType.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/AbstractHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Handler/UploadHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Injector/FileInjector.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/Annotation/UploadableField.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Mapping/PropertyMappingResolver.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/CacheWarmer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/Driver/AnnotationDriver.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Metadata/MetadataReader.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/CurrentDateTimeDirectoryNamer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/OrignameNamer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyDirectoryNamer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/PropertyNamer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SlugNamer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Naming/SmartUniqueNamer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Storage/AbstractStorage.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Templating/Helper/UploaderHelper.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Twig/Extension/UploaderExtensionRuntime.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/src/Util/Transliterator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Adapter/ODM/MongoDB/MongoDBAdapterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Adapter/ORM/DoctrineORMAdapterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/BypassFinalExtension.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Command/AbstractCommandTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Command/MappingDebugClassCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Command/MappingDebugCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Command/MappingListClassesCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/DependencyInjection/VichUploaderExtensionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/DummyAttributeEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/DummyEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/DummyFile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/DummyImageFile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/EventListener/Doctrine/CleanListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/EventListener/Doctrine/InjectListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/EventListener/Doctrine/ListenerTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/EventListener/Doctrine/RemoveListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/EventListener/Doctrine/UploadListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/app/AppKernel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/app/Resources/images/symfony_black_03.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/app/config/config.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/app/config/routing.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/templates/base.html.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/templates/default/edit.html.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/templates/default/edit_with_property_path.html.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/templates/default/upload.html.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/App/templates/default/upload_with_property_path.html.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/config/doctrine/Image.orm.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/config/vich_uploader/Entity.Article.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/config/vich_uploader/Entity.Article.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/config/vich_uploader/Entity.Product.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/config/vich_uploader/Entity.Product.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/src/Controller/DefaultController.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/src/Entity/Article.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/src/Entity/Image.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/src/Entity/Product.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/src/Naming/DummyNamer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/src/Resources/config/doctrine/Image.orm.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Fixtures/TestBundle/src/VichTestBundle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Form/Type/VichFileTypeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Form/Type/VichImageTypeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Functional/UploadTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Functional/WebTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Handler/DownloadHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Handler/UploadHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Injector/FileInjectorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Kernel/AppKernelTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Kernel/FilesystemAppKernel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Kernel/FlysystemOfficialAppKernel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Kernel/FlysystemOneUpAppKernel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Kernel/SimpleAppKernel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Mapping/PropertyMappingFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Mapping/PropertyMappingTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/CacheWarmerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/ClassMetadataTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/Driver/AnnotationDriverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/Driver/AttributeReaderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/Driver/FileDriverTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/Driver/XmlDriverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/Driver/YamlDriverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Metadata/MetadataReaderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/Base64NamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/ConfigurableDirectoryNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/CurrentDateTimeDirectoryNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/HashNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/OrignameNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/PropertyDirectoryNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/PropertyNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/SlugNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/SmartUniqidNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/SubdirDirectoryNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Naming/UniqidNamerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Storage/FileSystemStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Storage/Flysystem/AbstractFlysystemStorageTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Storage/Flysystem/MountManagerFlysystemStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Storage/Flysystem/PsrContainerFlysystemStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Storage/GaufretteStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Storage/StorageTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Templating/Helper/UploadHelperTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/Twig/Extension/UploaderExtensionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/dustin10_VichUploaderBundle/tests/VichUploaderBundleTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9783399105072021, "profiling_times": {"config_time": 5.8933327198028564, "core_time": 3.191642999649048, "ignores_time": 0.0016033649444580078, "total_time": 9.087714433670044}, "parsing_time": {"total_time": 0.9151923656463623, "per_file_time": {"mean": 0.00871611776806059, "std_dev": 0.0002537875463730252}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.9768848419189453, "per_file_time": {"mean": 0.004462493999817038, "std_dev": 0.00018359206987243913}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.26930832862854004, "per_file_and_rule_time": {"mean": 0.0003740393453174166, "std_dev": 2.272947374874606e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.006144046783447266, "per_def_and_rule_time": {"mean": 0.00036141451667336854, "std_dev": 3.174567307560273e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}