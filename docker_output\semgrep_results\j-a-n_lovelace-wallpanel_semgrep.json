{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/docs/media-sources.md", "start": {"line": 191, "col": 8, "offset": 7936}, "end": {"line": 191, "col": 59, "offset": 7987}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/docs/media-sources.md", "start": {"line": 202, "col": 8, "offset": 8180}, "end": {"line": 202, "col": 59, "offset": 8231}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/docs/media-sources.md", "start": {"line": 212, "col": 8, "offset": 8426}, "end": {"line": 212, "col": 59, "offset": 8477}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/docs/media-sources.md", "start": {"line": 223, "col": 8, "offset": 8705}, "end": {"line": 223, "col": 59, "offset": 8756}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "start": {"line": 1401, "col": 4, "offset": 46523}, "end": {"line": 1465, "col": 6, "offset": 48209}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "start": {"line": 2267, "col": 9, "offset": 75795}, "end": {"line": 2267, "col": 23, "offset": 75809}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "start": {"line": 2296, "col": 5, "offset": 76512}, "end": {"line": 2296, "col": 34, "offset": 76541}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "start": {"line": 3312, "col": 4, "offset": 111069}, "end": {"line": 3312, "col": 31, "offset": 111096}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "start": {"line": 3317, "col": 4, "offset": 111234}, "end": {"line": 3317, "col": 29, "offset": 111259}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "start": {"line": 3560, "col": 5, "offset": 119220}, "end": {"line": 3560, "col": 36, "offset": 119251}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "start": {"line": 3977, "col": 14, "offset": 131060}, "end": {"line": 3977, "col": 52, "offset": 131098}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js:\n ", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js:\n ", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js:\n ", "path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js"}], "paths": {"scanned": ["downloaded_repos/j-a-n_lovelace-wallpanel/.github/workflows/docs.yml", "downloaded_repos/j-a-n_lovelace-wallpanel/.github/workflows/validate.yml", "downloaded_repos/j-a-n_lovelace-wallpanel/.gitignore", "downloaded_repos/j-a-n_lovelace-wallpanel/DEVELOPMENT.md", "downloaded_repos/j-a-n_lovelace-wallpanel/LICENSE", "downloaded_repos/j-a-n_lovelace-wallpanel/README.md", "downloaded_repos/j-a-n_lovelace-wallpanel/babel.config.json", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/additional-resources.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/browser-mod.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/camera-motion-detection.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/configuration.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/credits.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/dark-style.png", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/faq.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/grid-layout.png", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/index.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/info-box.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/installation.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/media-info.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/media-sources.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/other-configuration.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/quick-start.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/screensaver-screenshot.png", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/screensaver.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/styling.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/transparent-style.png", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/troubleshooting.md", "downloaded_repos/j-a-n_lovelace-wallpanel/docs/wallpanel.png", "downloaded_repos/j-a-n_lovelace-wallpanel/eslint.config.mjs", "downloaded_repos/j-a-n_lovelace-wallpanel/hacs.json", "downloaded_repos/j-a-n_lovelace-wallpanel/mkdocs.yml", "downloaded_repos/j-a-n_lovelace-wallpanel/package-lock.json", "downloaded_repos/j-a-n_lovelace-wallpanel/package.json", "downloaded_repos/j-a-n_lovelace-wallpanel/rollup.config.js", "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js"], "skipped": [{"path": "downloaded_repos/j-a-n_lovelace-wallpanel/docs/light-style.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/j-a-n_lovelace-wallpanel/docs/screensaver-video.webm", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6503360271453857, "profiling_times": {"config_time": 5.69806694984436, "core_time": 24.22845697402954, "ignores_time": 0.0036895275115966797, "total_time": 29.931366205215454}, "parsing_time": {"total_time": 0.603384256362915, "per_file_time": {"mean": 0.06033842563629151, "std_dev": 0.014229238312100847}, "very_slow_stats": {"time_ratio": 0.6811299454673406, "count_ratio": 0.1}, "very_slow_files": [{"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "ftime": 0.4109830856323242}]}, "scanning_time": {"total_time": 29.28463578224182, "per_file_time": {"mean": 0.35282693713544366, "std_dev": 6.023999909221432}, "very_slow_stats": {"time_ratio": 0.9705157016890154, "count_ratio": 0.024096385542168676}, "very_slow_files": [{"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "ftime": 6.917212963104248}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel.js", "ftime": 21.50398588180542}]}, "matching_time": {"total_time": 3.3882644176483154, "per_file_and_rule_time": {"mean": 0.02606357244344859, "std_dev": 0.007295110263719302}, "very_slow_stats": {"time_ratio": 0.8152365025541799, "count_ratio": 0.07692307692307693}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.13585805892944336}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.browser.security.open-redirect.js-open-redirect", "time": 0.16231393814086914}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.16262388229370117}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.19017696380615234}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.19057393074035645}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.2496650218963623}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.261152982711792}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.2653179168701172}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.48120808601379395}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6633460521697998}]}, "tainting_time": {"total_time": 2.8089773654937744, "per_def_and_rule_time": {"mean": 0.0014122560912487553, "std_dev": 6.507498377371556e-05}, "very_slow_stats": {"time_ratio": 0.21486677861990264, "count_ratio": 0.0015082956259426848}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "fline": 4499, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.20064997673034668}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "fline": 4499, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.20138788223266602}, {"fpath": "downloaded_repos/j-a-n_lovelace-wallpanel/wallpanel-src.js", "fline": 4499, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.20151805877685547}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}