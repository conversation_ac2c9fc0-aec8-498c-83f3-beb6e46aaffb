{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.js", "start": {"line": 496, "col": 9, "offset": 13187}, "end": {"line": 496, "col": 39, "offset": 13217}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-general.js", "start": {"line": 2, "col": 8281, "offset": 8351}, "end": {"line": 2, "col": 8333, "offset": 8403}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-general.js", "start": {"line": 2, "col": 18665, "offset": 18735}, "end": {"line": 2, "col": 18701, "offset": 18771}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js", "start": {"line": 122, "col": 13, "offset": 4956}, "end": {"line": 122, "col": 182, "offset": 5125}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js", "start": {"line": 2568, "col": 45, "offset": 91036}, "end": {"line": 2568, "col": 90, "offset": 91081}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "start": {"line": 1, "col": 21953, "offset": 21952}, "end": {"line": 1, "col": 21991, "offset": 21990}, "extra": {"message": "RegExp() called with a `e` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "start": {"line": 1, "col": 35036, "offset": 35035}, "end": {"line": 1, "col": 35072, "offset": 35071}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "start": {"line": 1, "col": 51574, "offset": 51573}, "end": {"line": 1, "col": 51647, "offset": 51646}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "start": {"line": 1, "col": 55080, "offset": 55079}, "end": {"line": 1, "col": 55109, "offset": 55108}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "start": {"line": 1, "col": 72356, "offset": 72355}, "end": {"line": 1, "col": 72408, "offset": 72407}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "start": {"line": 1, "col": 78714, "offset": 78713}, "end": {"line": 1, "col": 78735, "offset": 78734}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "start": {"line": 1, "col": 6343, "offset": 6342}, "end": {"line": 1, "col": 6379, "offset": 6378}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "start": {"line": 1, "col": 33815, "offset": 33814}, "end": {"line": 1, "col": 33853, "offset": 33852}, "extra": {"message": "RegExp() called with a `e` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/codepress_admin-columns/assets/js/select2.js", "start": {"line": 149, "col": 130, "offset": 7683}, "end": {"line": 149, "col": 140, "offset": 7693}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "start": {"line": 1, "col": 6127, "offset": 6126}, "end": {"line": 1, "col": 6163, "offset": 6162}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "start": {"line": 1, "col": 11131, "offset": 11130}, "end": {"line": 1, "col": 11261, "offset": 11260}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "start": {"line": 1, "col": 16035, "offset": 16034}, "end": {"line": 1, "col": 16087, "offset": 16086}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "start": {"line": 1, "col": 17451, "offset": 17450}, "end": {"line": 1, "col": 17483, "offset": 17482}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "start": {"line": 1, "col": 19547, "offset": 19546}, "end": {"line": 1, "col": 19565, "offset": 19564}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "start": {"line": 1, "col": 48699, "offset": 48698}, "end": {"line": 1, "col": 48737, "offset": 48736}, "extra": {"message": "RegExp() called with a `e` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Database.php", "start": {"line": 170, "col": 15, "offset": 4255}, "end": {"line": 170, "col": 73, "offset": 4313}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Database.php", "start": {"line": 197, "col": 15, "offset": 5104}, "end": {"line": 197, "col": 72, "offset": 5161}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/admin/columns/column.ts", "start": {"line": 144, "col": 13, "offset": 3644}, "end": {"line": 144, "col": 44, "offset": 3675}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/label.ts", "start": {"line": 49, "col": 76, "offset": 2045}, "end": {"line": 49, "col": 110, "offset": 2079}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/date.ts", "start": {"line": 110, "col": 13, "offset": 3299}, "end": {"line": 110, "col": 38, "offset": 3324}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/date.ts", "start": {"line": 158, "col": 13, "offset": 4561}, "end": {"line": 158, "col": 52, "offset": 4600}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/label.ts", "start": {"line": 137, "col": 13, "offset": 3910}, "end": {"line": 137, "col": 60, "offset": 3957}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/width.ts", "start": {"line": 49, "col": 9, "offset": 1384}, "end": {"line": 49, "col": 86, "offset": 1461}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/helpers/html-element.ts", "start": {"line": 75, "col": 9, "offset": 1868}, "end": {"line": 75, "col": 39, "offset": 1898}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/modules/addon-download.ts", "start": {"line": 88, "col": 13, "offset": 2546}, "end": {"line": 88, "col": 180, "offset": 2713}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/modules/toggle-box-link.ts", "start": {"line": 69, "col": 9, "offset": 1690}, "end": {"line": 69, "col": 50, "offset": 1731}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/modules/toggle-box-link.ts", "start": {"line": 88, "col": 9, "offset": 2093}, "end": {"line": 88, "col": 74, "offset": 2158}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/plugin/show-more.ts", "start": {"line": 58, "col": 13, "offset": 1488}, "end": {"line": 58, "col": 37, "offset": 1512}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/plugin/tooltip.ts", "start": {"line": 56, "col": 5, "offset": 1744}, "end": {"line": 56, "col": 29, "offset": 1768}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/codepress_admin-columns/src/js/table/cell.ts", "start": {"line": 62, "col": 9, "offset": 1474}, "end": {"line": 62, "col": 35, "offset": 1500}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/codepress_admin-columns/src/scripts/webfont.js", "start": {"line": 45, "col": 40, "offset": 1002}, "end": {"line": 45, "col": 44, "offset": 1006}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/codepress_admin-columns/assets/js/select2.js:\n ", "path": "downloaded_repos/codepress_admin-columns/assets/js/select2.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/codepress_admin-columns/assets/js/table.js:\n ", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/codepress_admin-columns/assets/js/table.js:\n ", "path": "downloaded_repos/codepress_admin-columns/assets/js/table.js"}], "paths": {"scanned": ["downloaded_repos/codepress_admin-columns/.gitignore", "downloaded_repos/codepress_admin-columns/README.MD", "downloaded_repos/codepress_admin-columns/api.php", "downloaded_repos/codepress_admin-columns/assets/css/ac-jquery-ui.css", "downloaded_repos/codepress_admin-columns/assets/css/acui.css", "downloaded_repos/codepress_admin-columns/assets/css/admin-general.css", "downloaded_repos/codepress_admin-columns/assets/css/admin-page-addons.css", "downloaded_repos/codepress_admin-columns/assets/css/admin-page-columns.css", "downloaded_repos/codepress_admin-columns/assets/css/admin-page-help.css", "downloaded_repos/codepress_admin-columns/assets/css/admin-page-settings.css", "downloaded_repos/codepress_admin-columns/assets/css/components/ajax-loader.css", "downloaded_repos/codepress_admin-columns/assets/css/components/json-viewer.css", "downloaded_repos/codepress_admin-columns/assets/css/components/value-modal.css", "downloaded_repos/codepress_admin-columns/assets/css/notice.css", "downloaded_repos/codepress_admin-columns/assets/css/select2.css", "downloaded_repos/codepress_admin-columns/assets/css/table.css", "downloaded_repos/codepress_admin-columns/assets/css/utilities.css", "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.css", "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.js", "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.min.css", "downloaded_repos/codepress_admin-columns/assets/fonts/cpac_icon.woff2", "downloaded_repos/codepress_admin-columns/assets/fonts/cpac_icons.eot", "downloaded_repos/codepress_admin-columns/assets/fonts/cpac_icons.woff", "downloaded_repos/codepress_admin-columns/assets/fonts/cpac_icons.woff2", "downloaded_repos/codepress_admin-columns/assets/fonts/cpacicon.woff2", "downloaded_repos/codepress_admin-columns/assets/images/addons/acf-icon.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/acf-v2.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/acf.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/buddypress-icon.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/buddypress.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/events-calendar-icon.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/events-calendar.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/gravityforms.svg", "downloaded_repos/codepress_admin-columns/assets/images/addons/jetengine.svg", "downloaded_repos/codepress_admin-columns/assets/images/addons/metabox.svg", "downloaded_repos/codepress_admin-columns/assets/images/addons/mla.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/ninja-forms-icon.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/ninja-forms.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/pods-icon.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/pods.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/toolset-types-icon-alt.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/toolset-types-icon.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/toolset-types.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/woocommerce-icon.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/woocommerce.png", "downloaded_repos/codepress_admin-columns/assets/images/addons/yoast-seo.png", "downloaded_repos/codepress_admin-columns/assets/images/external.svg", "downloaded_repos/codepress_admin-columns/assets/images/logo-ac-light.svg", "downloaded_repos/codepress_admin-columns/assets/images/logo-ac.svg", "downloaded_repos/codepress_admin-columns/assets/images/page-menu-icon.svg", "downloaded_repos/codepress_admin-columns/assets/images/question-light.svg", "downloaded_repos/codepress_admin-columns/assets/images/question.svg", "downloaded_repos/codepress_admin-columns/assets/images/symbols.svg", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/bulk-edit-author.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/bulk-edit.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/export-csv.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/export.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/filter.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/inline-edit-toggle.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/inline-edit.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/reset-sorting.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/save-filters.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/smart-filters.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/sort-preference.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/sort-table.png", "downloaded_repos/codepress_admin-columns/assets/images/tooltip/sorting-include-empty.png", "downloaded_repos/codepress_admin-columns/assets/images/ui-icons_444444_256x240.png", "downloaded_repos/codepress_admin-columns/assets/images/ui-icons_555555_256x240.png", "downloaded_repos/codepress_admin-columns/assets/images/ui-icons_777620_256x240.png", "downloaded_repos/codepress_admin-columns/assets/images/ui-icons_777777_256x240.png", "downloaded_repos/codepress_admin-columns/assets/images/ui-icons_cc0000_256x240.png", "downloaded_repos/codepress_admin-columns/assets/images/ui-icons_ffffff_256x240.png", "downloaded_repos/codepress_admin-columns/assets/images/warning.svg", "downloaded_repos/codepress_admin-columns/assets/js/admin-general.js", "downloaded_repos/codepress_admin-columns/assets/js/admin-general.js.LICENSE.txt", "downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js", "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "downloaded_repos/codepress_admin-columns/assets/js/global-translations.js", "downloaded_repos/codepress_admin-columns/assets/js/initialize-columns.js", "downloaded_repos/codepress_admin-columns/assets/js/message-review.js", "downloaded_repos/codepress_admin-columns/assets/js/notice-dismissible.js", "downloaded_repos/codepress_admin-columns/assets/js/select2.js", "downloaded_repos/codepress_admin-columns/assets/js/select2_conflict_fix.js", "downloaded_repos/codepress_admin-columns/assets/js/table.js", "downloaded_repos/codepress_admin-columns/assets/ui-theme/images/ui-bg_flat_75_ffffff_40x100.png", "downloaded_repos/codepress_admin-columns/assets/ui-theme/images/ui-bg_glass_55_fbf9ee_1x400.png", "downloaded_repos/codepress_admin-columns/assets/ui-theme/images/ui-bg_glass_65_ffffff_1x400.png", "downloaded_repos/codepress_admin-columns/assets/ui-theme/images/ui-bg_glass_75_dadada_1x400.png", "downloaded_repos/codepress_admin-columns/assets/ui-theme/images/ui-bg_glass_75_e6e6e6_1x400.png", "downloaded_repos/codepress_admin-columns/assets/ui-theme/images/ui-bg_glass_95_fef1ec_1x400.png", "downloaded_repos/codepress_admin-columns/assets/ui-theme/images/ui-bg_highlight-soft_75_cccccc_1x100.png", "downloaded_repos/codepress_admin-columns/assets/ui-theme/jquery-ui-1.8.18.custom.css", "downloaded_repos/codepress_admin-columns/classes/Admin/Admin.php", "downloaded_repos/codepress_admin-columns/classes/Admin/AdminLoader.php", "downloaded_repos/codepress_admin-columns/classes/Admin/AdminNetwork.php", "downloaded_repos/codepress_admin-columns/classes/Admin/AdminScripts.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Asset/Addons.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Asset/Columns.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Asset/Script/SettingsFactory.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Banner.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/ColorCollection.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/ColorReader.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/ColorRepository.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/Colors.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/Shipped/ColorParser.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/Shipped/ColorUpdater.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/Storage/OptionFactory.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/StyleInjector.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Colors/Type/Color.php", "downloaded_repos/codepress_admin-columns/classes/Admin/HelpTab.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Helpable.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Menu/Item.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Menu.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuFactory.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuFactoryInterface.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuGroupFactory/Aggregate.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuGroupFactory/DefaultGroups.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuGroupFactory.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuListFactory.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuListItems.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuPageFactory/Menu.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuPageFactory/SubMenu.php", "downloaded_repos/codepress_admin-columns/classes/Admin/MenuPageFactory.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Notice/DatabaseMissing.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Notice/ReadOnlyListScreen.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Page/Addons.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Page/Columns.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Page/Help.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Page/Settings.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageFactory/Addons.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageFactory/Columns.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageFactory/Help.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageFactory/Settings.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageFactoryInterface.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageNetworkRequestHandler.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageNetworkRequestHandlers.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageRequestHandler.php", "downloaded_repos/codepress_admin-columns/classes/Admin/PageRequestHandlers.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Preference/ListScreen.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Preference/ScreenOptions.php", "downloaded_repos/codepress_admin-columns/classes/Admin/RenderableHead.php", "downloaded_repos/codepress_admin-columns/classes/Admin/RequestHandlerInterface.php", "downloaded_repos/codepress_admin-columns/classes/Admin/ScreenOption/ColumnId.php", "downloaded_repos/codepress_admin-columns/classes/Admin/ScreenOption/ColumnType.php", "downloaded_repos/codepress_admin-columns/classes/Admin/ScreenOption/ListScreenId.php", "downloaded_repos/codepress_admin-columns/classes/Admin/ScreenOption/ListScreenSource.php", "downloaded_repos/codepress_admin-columns/classes/Admin/ScreenOption/ListScreenType.php", "downloaded_repos/codepress_admin-columns/classes/Admin/ScreenOption.php", "downloaded_repos/codepress_admin-columns/classes/Admin/ScreenOptions.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Scripts.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Section/AddonStatus.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Section/General.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Section/Partial/Menu.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Section/Partial/ShowEditButton.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Section/ProCta.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Section/Restore.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Section.php", "downloaded_repos/codepress_admin-columns/classes/Admin/SectionCollection.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Table.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Tooltip.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Type/MenuGroup.php", "downloaded_repos/codepress_admin-columns/classes/Admin/Type/MenuListItem.php", "downloaded_repos/codepress_admin-columns/classes/Admin/UninitializedScreens.php", "downloaded_repos/codepress_admin-columns/classes/Admin/View/Menu.php", "downloaded_repos/codepress_admin-columns/classes/AdminColumns.php", "downloaded_repos/codepress_admin-columns/classes/Ajax/Handler.php", "downloaded_repos/codepress_admin-columns/classes/Ajax/NullHandler.php", "downloaded_repos/codepress_admin-columns/classes/Ajax/NumberFormat.php", "downloaded_repos/codepress_admin-columns/classes/ApplyFilter/ColumnSeparator.php", "downloaded_repos/codepress_admin-columns/classes/ApplyFilter/PostTypes.php", "downloaded_repos/codepress_admin-columns/classes/ApplyFilter/QueryTotalNumber.php", "downloaded_repos/codepress_admin-columns/classes/ApplyFilter/ValidAudioMimetypes.php", "downloaded_repos/codepress_admin-columns/classes/ApplyFilter/ValidVideoMimetypes.php", "downloaded_repos/codepress_admin-columns/classes/ArrayIterator.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Assets.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Enqueueable.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Enqueueables.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Location/Absolute.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Location.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Script/GlobalTranslationFactory.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Script/Inline/Position.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Script/Localize/Translation.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Script.php", "downloaded_repos/codepress_admin-columns/classes/Asset/ScriptFactory.php", "downloaded_repos/codepress_admin-columns/classes/Asset/Style.php", "downloaded_repos/codepress_admin-columns/classes/Capabilities/Manage.php", "downloaded_repos/codepress_admin-columns/classes/Capabilities.php", "downloaded_repos/codepress_admin-columns/classes/Check/AddonAvailable.php", "downloaded_repos/codepress_admin-columns/classes/Check/Promotion.php", "downloaded_repos/codepress_admin-columns/classes/Check/Review.php", "downloaded_repos/codepress_admin-columns/classes/Collection.php", "downloaded_repos/codepress_admin-columns/classes/Column/Actions.php", "downloaded_repos/codepress_admin-columns/classes/Column/AjaxValue.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Agent.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Approved.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Author.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/AuthorAvatar.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/AuthorEmail.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/AuthorIP.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/AuthorName.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/AuthorUrl.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Comment.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Date.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/DateGmt.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Excerpt.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/ID.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Post.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/ReplyTo.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Response.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Status.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/Type.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/User.php", "downloaded_repos/codepress_admin-columns/classes/Column/Comment/WordCount.php", "downloaded_repos/codepress_admin-columns/classes/Column/CustomField.php", "downloaded_repos/codepress_admin-columns/classes/Column/LabelEncoder.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Album.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/AlternateText.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Artist.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/AudioPlayer.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Author.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/AuthorName.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/AvailableSizes.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Caption.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Comments.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Date.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Description.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Dimensions.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Download.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/ExifData.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/FileMeta.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/FileMetaAudio.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/FileMetaVideo.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/FileName.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/FileSize.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/FullPath.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Height.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/ID.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Image.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/MediaParent.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Menu.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Meta.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/MetaValue.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/MimeType.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Preview.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Title.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/VideoPlayer.php", "downloaded_repos/codepress_admin-columns/classes/Column/Media/Width.php", "downloaded_repos/codepress_admin-columns/classes/Column/Menu.php", "downloaded_repos/codepress_admin-columns/classes/Column/Meta.php", "downloaded_repos/codepress_admin-columns/classes/Column/Placeholder.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Attachment.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Author.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/AuthorName.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/BeforeMoreTag.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Categories.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/CommentCount.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/CommentStatus.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Comments.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Content.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Date.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/DatePublished.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Depth.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/EstimatedReadingTime.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Excerpt.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/FeaturedImage.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Formats.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/ID.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/LastModifiedAuthor.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Menu.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Modified.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Order.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/PageTemplate.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/PasswordProtected.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Path.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Permalink.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/PingStatus.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/PostParent.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Shortcodes.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Shortlink.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Slug.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Status.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Sticky.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Tags.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/Title.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/TitleRaw.php", "downloaded_repos/codepress_admin-columns/classes/Column/Post/WordCount.php", "downloaded_repos/codepress_admin-columns/classes/Column/Relation.php", "downloaded_repos/codepress_admin-columns/classes/Column/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/CommentCount.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Description.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/DisplayName.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Email.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/FirstName.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/FirstPost.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/FullName.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/ID.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/LastName.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/LastPost.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Login.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Name.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Nicename.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Nickname.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/PostCount.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Posts.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Registered.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/RichEditing.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Role.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/ShowToolbar.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Url.php", "downloaded_repos/codepress_admin-columns/classes/Column/User/Username.php", "downloaded_repos/codepress_admin-columns/classes/Column.php", "downloaded_repos/codepress_admin-columns/classes/ColumnCollection.php", "downloaded_repos/codepress_admin-columns/classes/ColumnFactory.php", "downloaded_repos/codepress_admin-columns/classes/ColumnGroups.php", "downloaded_repos/codepress_admin-columns/classes/ColumnIterator/ProxyColumnIterator.php", "downloaded_repos/codepress_admin-columns/classes/ColumnIterator.php", "downloaded_repos/codepress_admin-columns/classes/ColumnNamesTrait.php", "downloaded_repos/codepress_admin-columns/classes/ColumnRepository/EncodedData.php", "downloaded_repos/codepress_admin-columns/classes/ColumnRepository/Filter.php", "downloaded_repos/codepress_admin-columns/classes/ColumnRepository/Sort/ColumnNames.php", "downloaded_repos/codepress_admin-columns/classes/ColumnRepository/Sort/ManualOrder.php", "downloaded_repos/codepress_admin-columns/classes/ColumnRepository/Sort.php", "downloaded_repos/codepress_admin-columns/classes/ColumnRepository.php", "downloaded_repos/codepress_admin-columns/classes/ColumnSize/ListStorage.php", "downloaded_repos/codepress_admin-columns/classes/ColumnSize/UserPreference.php", "downloaded_repos/codepress_admin-columns/classes/ColumnSize/UserStorage.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypeCollection.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory/Aggregate.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory/CommentFactory.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory/IntegrationsFactory.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory/MediaFactory.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory/OriginalsFactory.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory/PostFactory.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory/UserFactory.php", "downloaded_repos/codepress_admin-columns/classes/ColumnTypesFactory.php", "downloaded_repos/codepress_admin-columns/classes/Config.php", "downloaded_repos/codepress_admin-columns/classes/Container.php", "downloaded_repos/codepress_admin-columns/classes/Controller/AjaxColumnModalValue.php", "downloaded_repos/codepress_admin-columns/classes/Controller/AjaxColumnRequest.php", "downloaded_repos/codepress_admin-columns/classes/Controller/AjaxColumnValue.php", "downloaded_repos/codepress_admin-columns/classes/Controller/AjaxGeneralOptions.php", "downloaded_repos/codepress_admin-columns/classes/Controller/AjaxRequestCustomFieldKeys.php", "downloaded_repos/codepress_admin-columns/classes/Controller/AjaxScreenOptions.php", "downloaded_repos/codepress_admin-columns/classes/Controller/ColumnRequest/Refresh.php", "downloaded_repos/codepress_admin-columns/classes/Controller/ColumnRequest/Select.php", "downloaded_repos/codepress_admin-columns/classes/Controller/DefaultColumns.php", "downloaded_repos/codepress_admin-columns/classes/Controller/ListScreen/Sanitize/FormData.php", "downloaded_repos/codepress_admin-columns/classes/Controller/ListScreen/Save.php", "downloaded_repos/codepress_admin-columns/classes/Controller/ListScreenRestoreColumns.php", "downloaded_repos/codepress_admin-columns/classes/Controller/Middleware/ListScreenAdmin.php", "downloaded_repos/codepress_admin-columns/classes/Controller/Middleware/ListScreenTable.php", "downloaded_repos/codepress_admin-columns/classes/Controller/Middleware/TableScreenAdmin.php", "downloaded_repos/codepress_admin-columns/classes/Controller/RestoreSettingsRequest.php", "downloaded_repos/codepress_admin-columns/classes/Controller/TableListScreenSetter.php", "downloaded_repos/codepress_admin-columns/classes/DefaultColumnsRepository.php", "downloaded_repos/codepress_admin-columns/classes/Dependencies.php", "downloaded_repos/codepress_admin-columns/classes/Deprecated/Hook/Action.php", "downloaded_repos/codepress_admin-columns/classes/Deprecated/Hook/Filter.php", "downloaded_repos/codepress_admin-columns/classes/Deprecated/Hook.php", "downloaded_repos/codepress_admin-columns/classes/Deprecated/Hooks.php", "downloaded_repos/codepress_admin-columns/classes/Entity/Plugin.php", "downloaded_repos/codepress_admin-columns/classes/Exception/InvalidListScreenException.php", "downloaded_repos/codepress_admin-columns/classes/Exception/MissingListScreenIdException.php", "downloaded_repos/codepress_admin-columns/classes/Exception/RequestException.php", "downloaded_repos/codepress_admin-columns/classes/Exception/SourceNotAvailableException.php", "downloaded_repos/codepress_admin-columns/classes/Expirable.php", "downloaded_repos/codepress_admin-columns/classes/Form/Element/Checkbox.php", "downloaded_repos/codepress_admin-columns/classes/Form/Element/Input.php", "downloaded_repos/codepress_admin-columns/classes/Form/Element/MultiSelect.php", "downloaded_repos/codepress_admin-columns/classes/Form/Element/Radio.php", "downloaded_repos/codepress_admin-columns/classes/Form/Element/Select.php", "downloaded_repos/codepress_admin-columns/classes/Form/Element/Toggle.php", "downloaded_repos/codepress_admin-columns/classes/Form/Element.php", "downloaded_repos/codepress_admin-columns/classes/Form/Nonce.php", "downloaded_repos/codepress_admin-columns/classes/Form/NonceFactory.php", "downloaded_repos/codepress_admin-columns/classes/Groups.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Arrays.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Date.php", "downloaded_repos/codepress_admin-columns/classes/Helper/File.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Html.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Icon.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Image.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Media.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Menu.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Network.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Post.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/ArrayMapper.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Generic/GroupFormatter/BlogSite.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Generic/GroupFormatter/VisibilityType.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Generic/GroupFormatter.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Generic/Groups.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Generic/LabelFormatter/Url.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Generic/LabelFormatter.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Option.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/OptionGroup.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Options/Paginated.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Options.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Paginated.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Select/Response.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Strings.php", "downloaded_repos/codepress_admin-columns/classes/Helper/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Helper/User.php", "downloaded_repos/codepress_admin-columns/classes/Helper.php", "downloaded_repos/codepress_admin-columns/classes/Integration/ACF.php", "downloaded_repos/codepress_admin-columns/classes/Integration/BuddyPress.php", "downloaded_repos/codepress_admin-columns/classes/Integration/EventsCalendar.php", "downloaded_repos/codepress_admin-columns/classes/Integration/Filter/IsActive.php", "downloaded_repos/codepress_admin-columns/classes/Integration/Filter/IsPluginActive.php", "downloaded_repos/codepress_admin-columns/classes/Integration/Filter/IsPluginNotActive.php", "downloaded_repos/codepress_admin-columns/classes/Integration/Filter.php", "downloaded_repos/codepress_admin-columns/classes/Integration/GravityForms.php", "downloaded_repos/codepress_admin-columns/classes/Integration/JetEngine.php", "downloaded_repos/codepress_admin-columns/classes/Integration/MediaLibraryAssistant.php", "downloaded_repos/codepress_admin-columns/classes/Integration/MetaBox.php", "downloaded_repos/codepress_admin-columns/classes/Integration/Pods.php", "downloaded_repos/codepress_admin-columns/classes/Integration/Types.php", "downloaded_repos/codepress_admin-columns/classes/Integration/WooCommerce.php", "downloaded_repos/codepress_admin-columns/classes/Integration/YoastSeo.php", "downloaded_repos/codepress_admin-columns/classes/Integration.php", "downloaded_repos/codepress_admin-columns/classes/IntegrationRepository.php", "downloaded_repos/codepress_admin-columns/classes/Integrations.php", "downloaded_repos/codepress_admin-columns/classes/Iterator.php", "downloaded_repos/codepress_admin-columns/classes/ListKeyCollection.php", "downloaded_repos/codepress_admin-columns/classes/ListKeysFactory/Aggregate.php", "downloaded_repos/codepress_admin-columns/classes/ListKeysFactory/BaseFactory.php", "downloaded_repos/codepress_admin-columns/classes/ListKeysFactory.php", "downloaded_repos/codepress_admin-columns/classes/ListScreen.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenCollection.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Database.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Filter/ListScreenId.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Filter/ListScreenKey.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Filter/Network.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Filter/Site.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Filter/UserAssigned.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Filter.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/ListScreenRepositoryTrait.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Rule/EqualGroup.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Rule/EqualId.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Rule/EqualType.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Rule.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Rules.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Sort/Label.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Sort/ListIds.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Sort/ManualOrder.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Sort/Nullable.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Sort/UserOrder.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Sort.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Storage/ListScreenRepository.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Storage/ListScreenRepositoryFactory.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Storage.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Types.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepository.php", "downloaded_repos/codepress_admin-columns/classes/ListScreenRepositoryWritable.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/Comment.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/Media.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/NetworkSite.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/NetworkUser.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/Post.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/User.php", "downloaded_repos/codepress_admin-columns/classes/ListTable/WpListTableTrait.php", "downloaded_repos/codepress_admin-columns/classes/ListTable.php", "downloaded_repos/codepress_admin-columns/classes/ListTableFactory.php", "downloaded_repos/codepress_admin-columns/classes/Message/InlineMessage.php", "downloaded_repos/codepress_admin-columns/classes/Message/Notice/Dismissible.php", "downloaded_repos/codepress_admin-columns/classes/Message/Notice.php", "downloaded_repos/codepress_admin-columns/classes/Message/Plugin.php", "downloaded_repos/codepress_admin-columns/classes/Message.php", "downloaded_repos/codepress_admin-columns/classes/Meta/Query.php", "downloaded_repos/codepress_admin-columns/classes/Meta/QueryMetaFactory.php", "downloaded_repos/codepress_admin-columns/classes/MetaType.php", "downloaded_repos/codepress_admin-columns/classes/Middleware.php", "downloaded_repos/codepress_admin-columns/classes/Nonce/Ajax.php", "downloaded_repos/codepress_admin-columns/classes/OpCacheInvalidateTrait.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Install/Capabilities.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Install/Database.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Install.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/InstallCollection.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/PluginHeader.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Setup/Network.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Setup/Site.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Setup.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/SetupFactory/AdminColumns.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/SetupFactory.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Update/V3005.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Update/V3007.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Update/V3201.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Update/V4000.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Update.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/UpdateCollection.php", "downloaded_repos/codepress_admin-columns/classes/Plugin/Version.php", "downloaded_repos/codepress_admin-columns/classes/PluginActionLinks.php", "downloaded_repos/codepress_admin-columns/classes/PluginActionUpgrade.php", "downloaded_repos/codepress_admin-columns/classes/PluginUpdate.php", "downloaded_repos/codepress_admin-columns/classes/PostType.php", "downloaded_repos/codepress_admin-columns/classes/PostTypeRepository.php", "downloaded_repos/codepress_admin-columns/classes/Preferences/Network.php", "downloaded_repos/codepress_admin-columns/classes/Preferences/Site.php", "downloaded_repos/codepress_admin-columns/classes/Preferences/User.php", "downloaded_repos/codepress_admin-columns/classes/Preferences.php", "downloaded_repos/codepress_admin-columns/classes/Promo/BlackFriday.php", "downloaded_repos/codepress_admin-columns/classes/Promo.php", "downloaded_repos/codepress_admin-columns/classes/PromoCollection.php", "downloaded_repos/codepress_admin-columns/classes/Registerable.php", "downloaded_repos/codepress_admin-columns/classes/Relation/Post.php", "downloaded_repos/codepress_admin-columns/classes/Relation/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Relation.php", "downloaded_repos/codepress_admin-columns/classes/Renderable.php", "downloaded_repos/codepress_admin-columns/classes/Request/Parameters.php", "downloaded_repos/codepress_admin-columns/classes/Request.php", "downloaded_repos/codepress_admin-columns/classes/RequestAjaxHandler.php", "downloaded_repos/codepress_admin-columns/classes/RequestAjaxHandlers.php", "downloaded_repos/codepress_admin-columns/classes/RequestAjaxParser.php", "downloaded_repos/codepress_admin-columns/classes/RequestHandler/Ajax/ListScreenDelete.php", "downloaded_repos/codepress_admin-columns/classes/RequestHandler.php", "downloaded_repos/codepress_admin-columns/classes/RequestHandlerFactory.php", "downloaded_repos/codepress_admin-columns/classes/Response/Json.php", "downloaded_repos/codepress_admin-columns/classes/Sanitize/Kses.php", "downloaded_repos/codepress_admin-columns/classes/Sanitize.php", "downloaded_repos/codepress_admin-columns/classes/Screen/QuickEdit.php", "downloaded_repos/codepress_admin-columns/classes/Screen.php", "downloaded_repos/codepress_admin-columns/classes/ScreenController.php", "downloaded_repos/codepress_admin-columns/classes/Service/Colors.php", "downloaded_repos/codepress_admin-columns/classes/Service/ColumnsMockup.php", "downloaded_repos/codepress_admin-columns/classes/Service/CommonAssets.php", "downloaded_repos/codepress_admin-columns/classes/Service/NoticeChecks.php", "downloaded_repos/codepress_admin-columns/classes/Service/Setup.php", "downloaded_repos/codepress_admin-columns/classes/Service/TableRows.php", "downloaded_repos/codepress_admin-columns/classes/Services.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/ActionIcons.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/AttachmentDisplay.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/BeforeAfter/Aperture.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/BeforeAfter/FocalLength.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/BeforeAfter/ISO.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/BeforeAfter/ShutterSpeed.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/BeforeAfter.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/CharacterLimit.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Comment.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/CommentCount.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/CommentLink.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/CustomField.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/CustomFieldType.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Date.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/DateFormat.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/DateTimeFormat.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/ExifData.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/FileMeta.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/FileMetaAudio.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/FileMetaVideo.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Image.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Images.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Label.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/LinkLabel.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/LinkToMenu.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/MediaLink.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Message.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Meta.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/MissingImageSize.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/NumberFormat.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/NumberOfItems.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Password.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/PathScope.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Post.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/PostFormatIcon.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/PostLink.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/PostStatus.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/PostType.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Pro/BulkEditing.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Pro/Export.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Pro/InlineEditing.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Pro/SmartFiltering.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Pro/Sorting.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Pro.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Separator.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/StatusIcon.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/StringLimit.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Term.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/TermLink.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Time.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Toggle.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Type.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/User.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/UserLink.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/VideoDisplay.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/Width.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/WordLimit.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column/WordsPerMinute.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Column.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Form/Element/Select.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Form/Instruction.php", "downloaded_repos/codepress_admin-columns/classes/Settings/FormatCollection.php", "downloaded_repos/codepress_admin-columns/classes/Settings/FormatValue.php", "downloaded_repos/codepress_admin-columns/classes/Settings/GeneralOption.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Header.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Option/EditButton.php", "downloaded_repos/codepress_admin-columns/classes/Settings/Option.php", "downloaded_repos/codepress_admin-columns/classes/Storage/Encoder/BaseEncoder.php", "downloaded_repos/codepress_admin-columns/classes/Storage/Encoder.php", "downloaded_repos/codepress_admin-columns/classes/Storage/EncoderFactory/BaseEncoderFactory.php", "downloaded_repos/codepress_admin-columns/classes/Storage/EncoderFactory.php", "downloaded_repos/codepress_admin-columns/classes/Storage/KeyValueFactory.php", "downloaded_repos/codepress_admin-columns/classes/Storage/KeyValuePair.php", "downloaded_repos/codepress_admin-columns/classes/Storage/ListColumnOrder.php", "downloaded_repos/codepress_admin-columns/classes/Storage/ListScreenOrder.php", "downloaded_repos/codepress_admin-columns/classes/Storage/NetworkOptionFactory.php", "downloaded_repos/codepress_admin-columns/classes/Storage/Option.php", "downloaded_repos/codepress_admin-columns/classes/Storage/OptionFactory.php", "downloaded_repos/codepress_admin-columns/classes/Storage/SiteOption.php", "downloaded_repos/codepress_admin-columns/classes/Storage/Table.php", "downloaded_repos/codepress_admin-columns/classes/Storage/TableListOrder.php", "downloaded_repos/codepress_admin-columns/classes/Storage/Timestamp.php", "downloaded_repos/codepress_admin-columns/classes/Storage/Transaction.php", "downloaded_repos/codepress_admin-columns/classes/Storage/UserColumnOrder.php", "downloaded_repos/codepress_admin-columns/classes/Storage/UserMeta.php", "downloaded_repos/codepress_admin-columns/classes/Stringable.php", "downloaded_repos/codepress_admin-columns/classes/Table/AdminHeadStyle.php", "downloaded_repos/codepress_admin-columns/classes/Table/Button.php", "downloaded_repos/codepress_admin-columns/classes/Table/InlineStyle/ColumnSize.php", "downloaded_repos/codepress_admin-columns/classes/Table/LayoutPreference.php", "downloaded_repos/codepress_admin-columns/classes/Table/ManageValue/Comment.php", "downloaded_repos/codepress_admin-columns/classes/Table/ManageValue/Media.php", "downloaded_repos/codepress_admin-columns/classes/Table/ManageValue/MsSite.php", "downloaded_repos/codepress_admin-columns/classes/Table/ManageValue/Post.php", "downloaded_repos/codepress_admin-columns/classes/Table/ManageValue/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Table/ManageValue/User.php", "downloaded_repos/codepress_admin-columns/classes/Table/ManageValue.php", "downloaded_repos/codepress_admin-columns/classes/Table/PrimaryColumn.php", "downloaded_repos/codepress_admin-columns/classes/Table/PrimaryColumnFactory.php", "downloaded_repos/codepress_admin-columns/classes/Table/Screen.php", "downloaded_repos/codepress_admin-columns/classes/Table/ScreenPreferences.php", "downloaded_repos/codepress_admin-columns/classes/Table/ScreenTools.php", "downloaded_repos/codepress_admin-columns/classes/Table/TableFormView.php", "downloaded_repos/codepress_admin-columns/classes/Table/TableScreenCollection.php", "downloaded_repos/codepress_admin-columns/classes/Table/TableScreenRepository.php", "downloaded_repos/codepress_admin-columns/classes/Table/TableScreensFactory.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/Comment.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/ListTable.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/ManageValue.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/Media.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/MetaType.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/Post.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRows/Comment.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRows/Media.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRows/Post.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRows/User.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRows.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRowsFactory/Aggregate.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRowsFactory/BaseFactory.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/TableRowsFactory.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen/User.php", "downloaded_repos/codepress_admin-columns/classes/TableScreen.php", "downloaded_repos/codepress_admin-columns/classes/TableScreenFactory/Aggregate.php", "downloaded_repos/codepress_admin-columns/classes/TableScreenFactory/CommentFactory.php", "downloaded_repos/codepress_admin-columns/classes/TableScreenFactory/MediaFactory.php", "downloaded_repos/codepress_admin-columns/classes/TableScreenFactory/PostFactory.php", "downloaded_repos/codepress_admin-columns/classes/TableScreenFactory/UserFactory.php", "downloaded_repos/codepress_admin-columns/classes/TableScreenFactory.php", "downloaded_repos/codepress_admin-columns/classes/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/AdvancedCustomFields.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/ColumnTypesFactory.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/ListKeysFactory.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/ListTable.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/ManageValue.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/MediaLibraryAssistant.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/TableScreen.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/TableScreenFactory.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/MediaLibraryAssistant/WpListTableFactory.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/NinjaForms.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/WPML.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/WPMLColumn.php", "downloaded_repos/codepress_admin-columns/classes/ThirdParty/WooCommerce.php", "downloaded_repos/codepress_admin-columns/classes/Transient/User.php", "downloaded_repos/codepress_admin-columns/classes/Transient.php", "downloaded_repos/codepress_admin-columns/classes/Type/ColumnWidth.php", "downloaded_repos/codepress_admin-columns/classes/Type/DateRange.php", "downloaded_repos/codepress_admin-columns/classes/Type/EditorUrlFactory.php", "downloaded_repos/codepress_admin-columns/classes/Type/Labels.php", "downloaded_repos/codepress_admin-columns/classes/Type/ListKey.php", "downloaded_repos/codepress_admin-columns/classes/Type/ListScreenId.php", "downloaded_repos/codepress_admin-columns/classes/Type/QueryAware.php", "downloaded_repos/codepress_admin-columns/classes/Type/QueryAwareTrait.php", "downloaded_repos/codepress_admin-columns/classes/Type/ToggleOptions.php", "downloaded_repos/codepress_admin-columns/classes/Type/Uri.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/CouponCode.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/Documentation.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/Editor.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/EditorColumns.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/EditorNetwork.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/EditorNetworkColumns.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/External.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/Fragment.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/ListTable/Media.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/ListTable/Post.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/ListTable/Taxonomy.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/ListTable.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/ListTableNetwork.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/Path.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/PluginSearch.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/Site.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/Tweet.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/UtmTags.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/WordpressPluginRepo.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url/WordpressPluginReview.php", "downloaded_repos/codepress_admin-columns/classes/Type/Url.php", "downloaded_repos/codepress_admin-columns/classes/Type/UserId.php", "downloaded_repos/codepress_admin-columns/classes/TypedArrayIterator.php", "downloaded_repos/codepress_admin-columns/classes/View/Embed/Video.php", "downloaded_repos/codepress_admin-columns/classes/View.php", "downloaded_repos/codepress_admin-columns/classes/ViewCollection.php", "downloaded_repos/codepress_admin-columns/classes/WpListTableFactory.php", "downloaded_repos/codepress_admin-columns/codepress-admin-columns.php", "downloaded_repos/codepress_admin-columns/composer.json", "downloaded_repos/codepress_admin-columns/composer.lock", "downloaded_repos/codepress_admin-columns/getlangs", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ar_AR.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ar_AR.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-bg_BG.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-bg_BG.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-bt_BR.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-bt_BR.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-da_DK.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-da_DK.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-de_DE.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-de_DE.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-en_NZ.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-en_NZ.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-es_ES.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-es_ES.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-fa_IR.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-fa_IR.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-fr_FR.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-fr_FR.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-he_IL.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-he_IL.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-hu_HU.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-hu_HU.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-it_IT.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-it_IT.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ja_JA.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ja_JA.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-nb_NO.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-nb_NO.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-nl_NL.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-nl_NL.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-pl_PL.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-pl_PL.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-pt_BR.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-pt_BR.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-pt_PT.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-pt_PT.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ro_RO.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ro_RO.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ru_RU.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-ru_RU.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-sv_SE.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-sv_SE.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-tr_TR.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-tr_TR.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-zh_CN.mo", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns-zh_CN.po", "downloaded_repos/codepress_admin-columns/languages/codepress-admin-columns.pot", "downloaded_repos/codepress_admin-columns/license.txt", "downloaded_repos/codepress_admin-columns/readme.txt", "downloaded_repos/codepress_admin-columns/scoper.inc.php", "downloaded_repos/codepress_admin-columns/settings/translations/global.php", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/ajax.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/column-configurator.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/column.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/clone.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/indicator.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/label.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/refresh.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/remove.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/toggle.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/type-selector.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/feedback.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/form.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/helper/translation.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/listscreen-initialize.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/screen-options.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/component/SelectOptions.svelte", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/custom-field.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/date.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/image-size.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/label.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/multi-select.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/number-format.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/pro.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/select-options.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/sub-setting-toggle.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/type.ts", "downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/width.ts", "downloaded_repos/codepress_admin-columns/src/js/admin-general.ts", "downloaded_repos/codepress_admin-columns/src/js/admin-page-addons.ts", "downloaded_repos/codepress_admin-columns/src/js/admin-page-columns.ts", "downloaded_repos/codepress_admin-columns/src/js/admin-page-settings.ts", "downloaded_repos/codepress_admin-columns/src/js/ajax/settings.ts", "downloaded_repos/codepress_admin-columns/src/js/components/ConfirmationModal.svelte", "downloaded_repos/codepress_admin-columns/src/js/components/JsonValue.svelte", "downloaded_repos/codepress_admin-columns/src/js/components/PointerModal.svelte", "downloaded_repos/codepress_admin-columns/src/js/components/ValueModal.svelte", "downloaded_repos/codepress_admin-columns/src/js/constants.ts", "downloaded_repos/codepress_admin-columns/src/js/global-translations.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/admin-columns.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/animations.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/elements.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/events.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/global.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/html-element.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/string.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/table.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/translations.ts", "downloaded_repos/codepress_admin-columns/src/js/helpers/types.ts", "downloaded_repos/codepress_admin-columns/src/js/message-review.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/ac-hookable-filters.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/ac-pointer.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/ac-section.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/ac-services.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/addon-download.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/addon-downloader.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/json-viewer.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/modal.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/modals.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/notice.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/screen-option.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/service-container.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/toggle-box-link.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/tooltips.ts", "downloaded_repos/codepress_admin-columns/src/js/modules/value-modals.ts", "downloaded_repos/codepress_admin-columns/src/js/notice-dismissible.ts", "downloaded_repos/codepress_admin-columns/src/js/plugin/ac-confirmation.ts", "downloaded_repos/codepress_admin-columns/src/js/plugin/ajax-loader.ts", "downloaded_repos/codepress_admin-columns/src/js/plugin/dismissible-notice.ts", "downloaded_repos/codepress_admin-columns/src/js/plugin/show-more.ts", "downloaded_repos/codepress_admin-columns/src/js/plugin/tooltip.ts", "downloaded_repos/codepress_admin-columns/src/js/select2/excludegroup.matcher.js", "downloaded_repos/codepress_admin-columns/src/js/table/actions.ts", "downloaded_repos/codepress_admin-columns/src/js/table/cell.ts", "downloaded_repos/codepress_admin-columns/src/js/table/cells.ts", "downloaded_repos/codepress_admin-columns/src/js/table/columns.ts", "downloaded_repos/codepress_admin-columns/src/js/table/functions.ts", "downloaded_repos/codepress_admin-columns/src/js/table/row-selection.ts", "downloaded_repos/codepress_admin-columns/src/js/table/screen-options-columns.ts", "downloaded_repos/codepress_admin-columns/src/js/table/table.ts", "downloaded_repos/codepress_admin-columns/src/js/table.ts", "downloaded_repos/codepress_admin-columns/src/js/types/admin-columns.ts", "downloaded_repos/codepress_admin-columns/src/js/types/table.ts", "downloaded_repos/codepress_admin-columns/src/js/ui-wrapper/functions.ts", "downloaded_repos/codepress_admin-columns/src/js/ui-wrapper/toggle-buttons.ts", "downloaded_repos/codepress_admin-columns/src/languages.js", "downloaded_repos/codepress_admin-columns/src/package-lock.json", "downloaded_repos/codepress_admin-columns/src/package-scripts.js", "downloaded_repos/codepress_admin-columns/src/package.json", "downloaded_repos/codepress_admin-columns/src/scripts/webfont.js", "downloaded_repos/codepress_admin-columns/src/scss/_admin-rtl.scss", "downloaded_repos/codepress_admin-columns/src/scss/_admin-variables.scss", "downloaded_repos/codepress_admin-columns/src/scss/_animations.scss", "downloaded_repos/codepress_admin-columns/src/scss/_articles.scss", "downloaded_repos/codepress_admin-columns/src/scss/_colors.scss", "downloaded_repos/codepress_admin-columns/src/scss/_modals.scss", "downloaded_repos/codepress_admin-columns/src/scss/_settings.scss", "downloaded_repos/codepress_admin-columns/src/scss/_webfont.scss", "downloaded_repos/codepress_admin-columns/src/scss/_wordpress-vars.scss", "downloaded_repos/codepress_admin-columns/src/scss/ac-jquery-ui.scss", "downloaded_repos/codepress_admin-columns/src/scss/acui/components/_datepicker.scss", "downloaded_repos/codepress_admin-columns/src/scss/acui/components/_dropdown.scss", "downloaded_repos/codepress_admin-columns/src/scss/acui/components/_progress.scss", "downloaded_repos/codepress_admin-columns/src/scss/acui/components/_toggle-buttons.scss", "downloaded_repos/codepress_admin-columns/src/scss/acui/components/_tooltip.scss", "downloaded_repos/codepress_admin-columns/src/scss/acui.scss", "downloaded_repos/codepress_admin-columns/src/scss/admin-general.scss", "downloaded_repos/codepress_admin-columns/src/scss/admin-page-addons.scss", "downloaded_repos/codepress_admin-columns/src/scss/admin-page-columns.scss", "downloaded_repos/codepress_admin-columns/src/scss/admin-page-help.scss", "downloaded_repos/codepress_admin-columns/src/scss/admin-page-settings.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_ajax-loader.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_confirmation.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_forms.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_json-viewer.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_pointer.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_toggle.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_tooltip.scss", "downloaded_repos/codepress_admin-columns/src/scss/components/_value-modal.scss", "downloaded_repos/codepress_admin-columns/src/scss/notice.scss", "downloaded_repos/codepress_admin-columns/src/scss/select2.scss", "downloaded_repos/codepress_admin-columns/src/scss/table.scss", "downloaded_repos/codepress_admin-columns/src/scss/utilities/_flex.scss", "downloaded_repos/codepress_admin-columns/src/scss/utilities/_spacing.scss", "downloaded_repos/codepress_admin-columns/src/svg/bulk-edit.svg", "downloaded_repos/codepress_admin-columns/src/svg/delete.svg", "downloaded_repos/codepress_admin-columns/src/svg/download.svg", "downloaded_repos/codepress_admin-columns/src/svg/draft.svg", "downloaded_repos/codepress_admin-columns/src/svg/duplicate.svg", "downloaded_repos/codepress_admin-columns/src/svg/filter.svg", "downloaded_repos/codepress_admin-columns/src/svg/more.svg", "downloaded_repos/codepress_admin-columns/src/svg/move.svg", "downloaded_repos/codepress_admin-columns/src/svg/rename.svg", "downloaded_repos/codepress_admin-columns/src/svg/reset-password.svg", "downloaded_repos/codepress_admin-columns/src/svg/segment.svg", "downloaded_repos/codepress_admin-columns/src/svg/smart-filter.svg", "downloaded_repos/codepress_admin-columns/src/svg/text-clip.svg", "downloaded_repos/codepress_admin-columns/src/svg/text-wrap.svg", "downloaded_repos/codepress_admin-columns/src/svg/user-switch.svg", "downloaded_repos/codepress_admin-columns/src/svg-symbols/arrow-left-top.svg", "downloaded_repos/codepress_admin-columns/src/svg-symbols/zebra-thumbs-up.svg", "downloaded_repos/codepress_admin-columns/src/tailwind/ac-utilities.css", "downloaded_repos/codepress_admin-columns/src/tailwind/output.css", "downloaded_repos/codepress_admin-columns/src/tailwind.config.js", "downloaded_repos/codepress_admin-columns/src/tsconfig.json", "downloaded_repos/codepress_admin-columns/src/ui/AcIcon.svelte", "downloaded_repos/codepress_admin-columns/src/ui/AcModal.svelte", "downloaded_repos/codepress_admin-columns/src/ui/AcProgressBar.svelte", "downloaded_repos/codepress_admin-columns/src/ui/AcTimer.svelte", "downloaded_repos/codepress_admin-columns/src/ui/AcTooltip.svelte", "downloaded_repos/codepress_admin-columns/src/ui/acui-date-picker/AcDateModal.svelte", "downloaded_repos/codepress_admin-columns/src/ui/acui-date-picker/AcDateMonths.svelte", "downloaded_repos/codepress_admin-columns/src/ui/acui-date-picker/AcMonthPicker.svelte", "downloaded_repos/codepress_admin-columns/src/ui/acui-dropdown/AcDropdown.svelte", "downloaded_repos/codepress_admin-columns/src/ui/acui-dropdown/AcDropdownItem.svelte", "downloaded_repos/codepress_admin-columns/src/ui/acui-dropdown/AcDropdownMenu.svelte", "downloaded_repos/codepress_admin-columns/src/ui/acui-toggle-buttons/AcToggleButtons.svelte", "downloaded_repos/codepress_admin-columns/src/ui/element/AcButton.svelte", "downloaded_repos/codepress_admin-columns/src/ui/element/AcCheckbox.svelte", "downloaded_repos/codepress_admin-columns/src/ui/element/AcSelect.svelte", "downloaded_repos/codepress_admin-columns/src/ui/element/AcToggle.svelte", "downloaded_repos/codepress_admin-columns/src/ui/helpers/input.ts", "downloaded_repos/codepress_admin-columns/src/ui/tsconfig.json", "downloaded_repos/codepress_admin-columns/src/webfont/fonts/cpac_icon.woff2", "downloaded_repos/codepress_admin-columns/src/webfont/fonts/cpac_icons.woff", "downloaded_repos/codepress_admin-columns/src/webfont/fonts/cpac_icons.woff2", "downloaded_repos/codepress_admin-columns/src/webfont/fonts/cpacicon.woff2", "downloaded_repos/codepress_admin-columns/src/webfont/scss/template.scss", "downloaded_repos/codepress_admin-columns/src/webfont/template.scss.njk", "downloaded_repos/codepress_admin-columns/src/webpack.config.js", "downloaded_repos/codepress_admin-columns/templates/admin/edit-actions.php", "downloaded_repos/codepress_admin-columns/templates/admin/edit-addon.php", "downloaded_repos/codepress_admin-columns/templates/admin/edit-column.php", "downloaded_repos/codepress_admin-columns/templates/admin/edit-columns.php", "downloaded_repos/codepress_admin-columns/templates/admin/edit-menu.php", "downloaded_repos/codepress_admin-columns/templates/admin/edit-submenu.php", "downloaded_repos/codepress_admin-columns/templates/admin/edit-tabmenu.php", "downloaded_repos/codepress_admin-columns/templates/admin/header.php", "downloaded_repos/codepress_admin-columns/templates/admin/help-tab/basics.php", "downloaded_repos/codepress_admin-columns/templates/admin/help-tab/custom-fields.php", "downloaded_repos/codepress_admin-columns/templates/admin/help-tab/introduction.php", "downloaded_repos/codepress_admin-columns/templates/admin/list-screen-settings-mockup.php", "downloaded_repos/codepress_admin-columns/templates/admin/loading-message.php", "downloaded_repos/codepress_admin-columns/templates/admin/menu.php", "downloaded_repos/codepress_admin-columns/templates/admin/modal-pro.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/activate-disabled.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/activate.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/active-label.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/deactivate.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/install.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/missing-license.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/more-info.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/network-activate-disabled.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/network-active-label.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/addon/network-install.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/component/pro-feature-list-item.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/settings-section-general.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/settings-section-pro-cta.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/settings-section-restore.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/settings-section.php", "downloaded_repos/codepress_admin-columns/templates/admin/page/settings.php", "downloaded_repos/codepress_admin-columns/templates/admin/settings/setting-row.php", "downloaded_repos/codepress_admin-columns/templates/admin/side-banner.php", "downloaded_repos/codepress_admin-columns/templates/admin/side-feedback.php", "downloaded_repos/codepress_admin-columns/templates/admin/side-support.php", "downloaded_repos/codepress_admin-columns/templates/admin/table.php", "downloaded_repos/codepress_admin-columns/templates/admin/tooltip-body.php", "downloaded_repos/codepress_admin-columns/templates/admin/tooltip-label.php", "downloaded_repos/codepress_admin-columns/templates/admin/wrap.php", "downloaded_repos/codepress_admin-columns/templates/component/toggle-v2.php", "downloaded_repos/codepress_admin-columns/templates/embed/video.php", "downloaded_repos/codepress_admin-columns/templates/message/notice/dismissible.php", "downloaded_repos/codepress_admin-columns/templates/message/notice/inline.php", "downloaded_repos/codepress_admin-columns/templates/message/notice.php", "downloaded_repos/codepress_admin-columns/templates/message/plugin.php", "downloaded_repos/codepress_admin-columns/templates/settings/header-icon.php", "downloaded_repos/codepress_admin-columns/templates/settings/header.php", "downloaded_repos/codepress_admin-columns/templates/settings/section.php", "downloaded_repos/codepress_admin-columns/templates/settings/setting-date.php", "downloaded_repos/codepress_admin-columns/templates/settings/setting-label-icons.php", "downloaded_repos/codepress_admin-columns/templates/settings/setting-label.php", "downloaded_repos/codepress_admin-columns/templates/settings/setting-message.php", "downloaded_repos/codepress_admin-columns/templates/settings/setting-pro.php", "downloaded_repos/codepress_admin-columns/templates/settings/setting-width.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/bulk-editing.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/conditional-format.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/custom-field.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/export-disabled.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/export.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/filtering.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/inline-editing.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/smart-filtering.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/sorting-include-empty.php", "downloaded_repos/codepress_admin-columns/templates/tooltip/sorting.php"], "skipped": [{"path": "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codepress_admin-columns/assets/js/select2.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 6.293233156204224, "profiling_times": {"config_time": 5.437920570373535, "core_time": 52.30344319343567, "ignores_time": 0.0018336772918701172, "total_time": 57.74399161338806}, "parsing_time": {"total_time": 12.017396688461304, "per_file_time": {"mean": 0.015211894542356077, "std_dev": 0.015820887320320717}, "very_slow_stats": {"time_ratio": 0.6275894975093055, "count_ratio": 0.007594936708860759}, "very_slow_files": [{"fpath": "downloaded_repos/codepress_admin-columns/src/package-lock.json", "ftime": 0.44403600692749023}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-general.js", "ftime": 0.5926101207733154}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "ftime": 1.1870808601379395}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js", "ftime": 1.2684030532836914}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.js", "ftime": 1.408052921295166}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "ftime": 2.6418089866638184}]}, "scanning_time": {"total_time": 149.28729033470154, "per_file_time": {"mean": 0.05339316535575871, "std_dev": 1.1002512097621902}, "very_slow_stats": {"time_ratio": 0.8543855518855222, "count_ratio": 0.002503576537911302}, "very_slow_files": [{"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-general.js", "ftime": 4.474548816680908}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.js", "ftime": 12.633918046951294}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/select2.js", "ftime": 14.617144107818604}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "ftime": 15.802141189575195}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js", "ftime": 15.961710929870605}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "ftime": 23.904090881347656}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "ftime": 40.15534996986389}]}, "matching_time": {"total_time": 54.57696056365967, "per_file_and_rule_time": {"mean": 0.035953201952344964, "std_dev": 0.039414382285731735}, "very_slow_stats": {"time_ratio": 0.8516014932247172, "count_ratio": 0.05533596837944664}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.0093579292297363}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.1131670475006104}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.4147870540618896}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.4715561866760254}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.7502179145812988}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.89323091506958}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.9141528606414795}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 2.121222972869873}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 2.9794540405273438}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "time": 4.3226001262664795}]}, "tainting_time": {"total_time": 50.44145894050598, "per_def_and_rule_time": {"mean": 0.0011847949203858219, "std_dev": 0.0002679471464131518}, "very_slow_stats": {"time_ratio": 0.596694468333273, "count_ratio": 0.003076995349274205}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.5368828773498535}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/select2.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.5410420894622803}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.621502161026001}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.6385397911071777}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.6598429679870605}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.6827600002288818}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.7114489078521729}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/select2.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.8007919788360596}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.818289041519165}, {"fpath": "downloaded_repos/codepress_admin-columns/assets/js/table.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.9052600860595703}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1119443008}, "engine_requested": "OSS", "skipped_rules": []}