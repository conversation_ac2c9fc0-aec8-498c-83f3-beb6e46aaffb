{"version": "1.130.0", "results": [{"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/middleware.py", "start": {"line": 114, "col": 9, "offset": 4096}, "end": {"line": 117, "col": 29, "offset": 4205}, "extra": {"message": "Data from request (url) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/settings.py", "start": {"line": 82, "col": 15, "offset": 2528}, "end": {"line": 82, "col": 46, "offset": 2559}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v1.py", "start": {"line": 104, "col": 9, "offset": 2656}, "end": {"line": 104, "col": 49, "offset": 2696}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Expired token: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v1.py", "start": {"line": 107, "col": 9, "offset": 2758}, "end": {"line": 107, "col": 45, "offset": 2794}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Bad token: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v1.py", "start": {"line": 130, "col": 9, "offset": 3509}, "end": {"line": 130, "col": 49, "offset": 3549}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Invalid token: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v1.py", "start": {"line": 132, "col": 5, "offset": 3574}, "end": {"line": 132, "col": 61, "offset": 3630}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Valid token for user %s: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v2.py", "start": {"line": 231, "col": 13, "offset": 7547}, "end": {"line": 231, "col": 72, "offset": 7606}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Valid token for user %s %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v2.py", "start": {"line": 235, "col": 5, "offset": 7711}, "end": {"line": 235, "col": 66, "offset": 7772}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Invalid token for user %s %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/aaugustin_django-sesame/.gitignore", "downloaded_repos/aaugustin_django-sesame/.readthedocs.yaml", "downloaded_repos/aaugustin_django-sesame/LICENSE", "downloaded_repos/aaugustin_django-sesame/Makefile", "downloaded_repos/aaugustin_django-sesame/README.rst", "downloaded_repos/aaugustin_django-sesame/docs/Makefile", "downloaded_repos/aaugustin_django-sesame/docs/changelog.rst", "downloaded_repos/aaugustin_django-sesame/docs/conf.py", "downloaded_repos/aaugustin_django-sesame/docs/contributing.rst", "downloaded_repos/aaugustin_django-sesame/docs/faq.rst", "downloaded_repos/aaugustin_django-sesame/docs/howto.rst", "downloaded_repos/aaugustin_django-sesame/docs/index.rst", "downloaded_repos/aaugustin_django-sesame/docs/make.bat", "downloaded_repos/aaugustin_django-sesame/docs/reference.rst", "downloaded_repos/aaugustin_django-sesame/docs/requirements.txt", "downloaded_repos/aaugustin_django-sesame/docs/spelling_wordlist.txt", "downloaded_repos/aaugustin_django-sesame/docs/topics.rst", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/add_booking.png", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/admin.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/models.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/share_booking.html", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/share_booking.png", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/view_booking.png", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/views.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/views_decorator.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/views_generic.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/auth_links/views_generic_decorator.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/email_login/email_login.html", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/email_login/email_login.png", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/email_login/email_login_success.html", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/email_login/email_login_success.png", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/email_login/forms.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial/email_login/views.py", "downloaded_repos/aaugustin_django-sesame/docs/tutorial.rst", "downloaded_repos/aaugustin_django-sesame/logo/favicon.ico", "downloaded_repos/aaugustin_django-sesame/logo/github-social-preview.html", "downloaded_repos/aaugustin_django-sesame/logo/github-social-preview.png", "downloaded_repos/aaugustin_django-sesame/logo/horizontal.svg", "downloaded_repos/aaugustin_django-sesame/logo/icon.html", "downloaded_repos/aaugustin_django-sesame/logo/icon.svg", "downloaded_repos/aaugustin_django-sesame/logo/vertical.svg", "downloaded_repos/aaugustin_django-sesame/pyproject.toml", "downloaded_repos/aaugustin_django-sesame/src/sesame/__init__.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/backends.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/decorators.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/middleware.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/packers.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/settings.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v1.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/tokens_v2.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/utils.py", "downloaded_repos/aaugustin_django-sesame/src/sesame/views.py", "downloaded_repos/aaugustin_django-sesame/tox.ini"], "skipped": [{"path": "downloaded_repos/aaugustin_django-sesame/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/mixins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_backends.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_decorators.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_packers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_tokens.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_tokens_v1.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_tokens_v2.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aaugustin_django-sesame/tests/views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8603870868682861, "profiling_times": {"config_time": 6.972914218902588, "core_time": 2.917663335800171, "ignores_time": 0.0017371177673339844, "total_time": 9.894028186798096}, "parsing_time": {"total_time": 0.39408373832702637, "per_file_time": {"mean": 0.01876589230128697, "std_dev": 0.0001224082436004389}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.267544984817505, "per_file_time": {"mean": 0.01692197749863809, "std_dev": 0.001644690654292254}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.48079919815063477, "per_file_and_rule_time": {"mean": 0.0021180581416327523, "std_dev": 1.4305708008833333e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.11513137817382812, "per_def_and_rule_time": {"mean": 0.0003586647295134833, "std_dev": 4.853670534675003e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}