{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/waza-ari_fastapi-keycloak-middleware/.github/workflows/python-lint.properties.json", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/.github/workflows/python-lint.yml", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/.github/workflows/release.yml", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/.gitignore", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/.pre-commit-config.yaml", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/.readthedocs.yaml", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/LICENSE", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/README.md", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/Makefile", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/_static/.gitkeep", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/advanced_topics.rst", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/api.rst", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/authorization.rst", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/conf.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/index.rst", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/intro.rst", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/make.bat", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/requirements.txt", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/testing.rst", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/docs/usage.rst", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/__init__.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/decorators/__init__.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/decorators/require_permission.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/decorators/strip_request.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/dependencies/__init__.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/dependencies/check_permission.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/dependencies/get_auth.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/dependencies/get_authorization_result.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/dependencies/get_user.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/exceptions.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/fast_api_user.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/keycloak_backend.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/middleware.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/schemas/__init__.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/schemas/authorization_methods.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/schemas/authorization_result.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/schemas/exception_response.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/schemas/keycloak_configuration.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/schemas/match_strategy.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/fastapi_keycloak_middleware/setup.py", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/poetry.lock", "downloaded_repos/waza-ari_fastapi-keycloak-middleware/pyproject.toml"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.1494710445404053, "profiling_times": {"config_time": 7.650360822677612, "core_time": 3.2073869705200195, "ignores_time": 0.0017518997192382812, "total_time": 10.860388278961182}, "parsing_time": {"total_time": 0.3364737033843994, "per_file_time": {"mean": 0.012941296284015363, "std_dev": 0.00011453583742946478}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.5907533168792725, "per_file_time": {"mean": 0.023552302880720664, "std_dev": 0.002797388557211034}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6293869018554688, "per_file_and_rule_time": {"mean": 0.0019668340682983403, "std_dev": 2.2595439283534803e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.15619564056396484, "per_def_and_rule_time": {"mean": 0.0006646623002721909, "std_dev": 1.418145584610504e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}