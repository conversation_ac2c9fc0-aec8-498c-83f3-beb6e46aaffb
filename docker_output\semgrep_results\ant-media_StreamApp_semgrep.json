{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/js/utility.js", "start": {"line": 31, "col": 25, "offset": 912}, "end": {"line": 31, "col": 30, "offset": 917}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/cam_play.html", "start": {"line": 153, "col": 22, "offset": 4349}, "end": {"line": 153, "col": 27, "offset": 4354}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-player.html", "start": {"line": 105, "col": 11, "offset": 2249}, "end": {"line": 105, "col": 51, "offset": 2289}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-player.html", "start": {"line": 197, "col": 4, "offset": 5839}, "end": {"line": 197, "col": 53, "offset": 5888}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-player.html", "start": {"line": 322, "col": 22, "offset": 9086}, "end": {"line": 322, "col": 27, "offset": 9091}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-publish.html", "start": {"line": 117, "col": 11, "offset": 2421}, "end": {"line": 117, "col": 51, "offset": 2461}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-publish.html", "start": {"line": 221, "col": 8, "offset": 6524}, "end": {"line": 221, "col": 57, "offset": 6573}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-publish.html", "start": {"line": 453, "col": 22, "offset": 13945}, "end": {"line": 453, "col": 27, "offset": 13950}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference-deprecated.html", "start": {"line": 119, "col": 10, "offset": 2398}, "end": {"line": 119, "col": 50, "offset": 2438}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference-deprecated.html", "start": {"line": 207, "col": 4, "offset": 6265}, "end": {"line": 208, "col": 16, "offset": 6337}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference-deprecated.html", "start": {"line": 528, "col": 22, "offset": 16057}, "end": {"line": 528, "col": 27, "offset": 16062}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference-room.html", "start": {"line": 710, "col": 21, "offset": 23816}, "end": {"line": 710, "col": 26, "offset": 23821}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference.html", "start": {"line": 117, "col": 10, "offset": 2348}, "end": {"line": 117, "col": 50, "offset": 2388}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference.html", "start": {"line": 225, "col": 4, "offset": 6541}, "end": {"line": 226, "col": 16, "offset": 6613}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel_example.html", "start": {"line": 115, "col": 19, "offset": 2419}, "end": {"line": 115, "col": 59, "offset": 2459}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel_example.html", "start": {"line": 750, "col": 8, "offset": 23696}, "end": {"line": 750, "col": 13, "offset": 23701}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer-widget.js", "start": {"line": 198, "col": 9, "offset": 5192}, "end": {"line": 198, "col": 64, "offset": 5247}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.html", "start": {"line": 820, "col": 10, "offset": 26838}, "end": {"line": 820, "col": 98, "offset": 26926}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "start": {"line": 2733, "col": 21, "offset": 95972}, "end": {"line": 2733, "col": 53, "offset": 96004}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "start": {"line": 3228, "col": 17, "offset": 229807}, "end": {"line": 3228, "col": 51, "offset": 229841}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "start": {"line": 3330, "col": 17, "offset": 233678}, "end": {"line": 3330, "col": 51, "offset": 233712}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "start": {"line": 3996, "col": 5, "offset": 256277}, "end": {"line": 4139, "col": 14, "offset": 260449}, "extra": {"message": "No validation of origin is done by the addEventListener API. It may be possible to exploit this flaw to perform Cross Origin attacks such as Cross-Site Scripting(XSS).", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "shortlink": "https://sg.run/gL9x"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "start": {"line": 4006, "col": 17, "offset": 256572}, "end": {"line": 4006, "col": 52, "offset": 256607}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "start": {"line": 4023, "col": 13, "offset": 257002}, "end": {"line": 4026, "col": 20, "offset": 257108}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "start": {"line": 4166, "col": 9, "offset": 261096}, "end": {"line": 4169, "col": 16, "offset": 261202}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "start": {"line": 726, "col": 25, "offset": 32978}, "end": {"line": 726, "col": 59, "offset": 33012}, "extra": {"message": "RegExp() called with a `internalId` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "start": {"line": 726, "col": 25, "offset": 32978}, "end": {"line": 726, "col": 59, "offset": 33012}, "extra": {"message": "RegExp() called with a `pc` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "start": {"line": 738, "col": 25, "offset": 33439}, "end": {"line": 738, "col": 59, "offset": 33473}, "extra": {"message": "RegExp() called with a `internalId` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "start": {"line": 738, "col": 25, "offset": 33439}, "end": {"line": 738, "col": 59, "offset": 33473}, "extra": {"message": "RegExp() called with a `pc` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "start": {"line": 190, "col": 16, "offset": 6486}, "end": {"line": 190, "col": 41, "offset": 6511}, "extra": {"message": "RegExp() called with a `configTypes` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "start": {"line": 191, "col": 16, "offset": 6606}, "end": {"line": 191, "col": 41, "offset": 6631}, "extra": {"message": "RegExp() called with a `configTypes` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/mcu.html", "start": {"line": 9, "col": 2, "offset": 244}, "end": {"line": 9, "col": 76, "offset": 318}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/mcu.html", "start": {"line": 10, "col": 2, "offset": 320}, "end": {"line": 11, "col": 85, "offset": 412}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/mcu.html", "start": {"line": 96, "col": 11, "offset": 2014}, "end": {"line": 96, "col": 51, "offset": 2054}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/mcu.html", "start": {"line": 167, "col": 4, "offset": 4994}, "end": {"line": 168, "col": 16, "offset": 5066}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/mcu.html", "start": {"line": 407, "col": 22, "offset": 12080}, "end": {"line": 407, "col": 27, "offset": 12085}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/merge_streams.html", "start": {"line": 77, "col": 11, "offset": 1474}, "end": {"line": 77, "col": 51, "offset": 1514}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/merge_streams.html", "start": {"line": 146, "col": 5, "offset": 3871}, "end": {"line": 146, "col": 54, "offset": 3920}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multipeerplay.html", "start": {"line": 19, "col": 11, "offset": 617}, "end": {"line": 19, "col": 51, "offset": 657}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multipeerplay.html", "start": {"line": 83, "col": 22, "offset": 2215}, "end": {"line": 83, "col": 27, "offset": 2220}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrack-play.html", "start": {"line": 8, "col": 5, "offset": 165}, "end": {"line": 8, "col": 132, "offset": 292}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrack-play.html", "start": {"line": 73, "col": 5, "offset": 2161}, "end": {"line": 73, "col": 105, "offset": 2261}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrack-play.html", "start": {"line": 324, "col": 25, "offset": 11887}, "end": {"line": 324, "col": 30, "offset": 11892}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrackplayer.html", "start": {"line": 89, "col": 10, "offset": 1793}, "end": {"line": 89, "col": 50, "offset": 1833}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrackplayer.html", "start": {"line": 122, "col": 5, "offset": 2522}, "end": {"line": 123, "col": 17, "offset": 2595}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrackplayer.html", "start": {"line": 191, "col": 23, "offset": 4175}, "end": {"line": 191, "col": 28, "offset": 4180}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/peer.html", "start": {"line": 20, "col": 11, "offset": 610}, "end": {"line": 20, "col": 51, "offset": 650}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/peer.html", "start": {"line": 123, "col": 22, "offset": 3701}, "end": {"line": 123, "col": 27, "offset": 3706}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "start": {"line": 11, "col": 1, "offset": 395}, "end": {"line": 11, "col": 62, "offset": 456}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "start": {"line": 145, "col": 5, "offset": 4934}, "end": {"line": 145, "col": 49, "offset": 4978}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "start": {"line": 333, "col": 23, "offset": 10454}, "end": {"line": 333, "col": 28, "offset": 10459}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player_with_timestamp.html", "start": {"line": 69, "col": 11, "offset": 1326}, "end": {"line": 69, "col": 51, "offset": 1366}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player_with_timestamp.html", "start": {"line": 189, "col": 5, "offset": 6061}, "end": {"line": 189, "col": 54, "offset": 6110}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player_with_timestamp.html", "start": {"line": 358, "col": 23, "offset": 11025}, "end": {"line": 358, "col": 28, "offset": 11030}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_with_timestamp.html", "start": {"line": 64, "col": 11, "offset": 1492}, "end": {"line": 64, "col": 51, "offset": 1532}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_with_timestamp.html", "start": {"line": 100, "col": 7, "offset": 2831}, "end": {"line": 100, "col": 75, "offset": 2899}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_with_timestamp.html", "start": {"line": 210, "col": 23, "offset": 6020}, "end": {"line": 210, "col": 28, "offset": 6025}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "start": {"line": 46, "col": 11, "offset": 1113}, "end": {"line": 46, "col": 51, "offset": 1153}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "start": {"line": 185, "col": 7, "offset": 7629}, "end": {"line": 185, "col": 56, "offset": 7678}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/datachannel_only_webrtc_frame.html", "start": {"line": 66, "col": 6, "offset": 2932}, "end": {"line": 66, "col": 67, "offset": 2993}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_audio.html", "start": {"line": 37, "col": 6, "offset": 1528}, "end": {"line": 37, "col": 67, "offset": 1589}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_webrtc.html", "start": {"line": 40, "col": 6, "offset": 1585}, "end": {"line": 40, "col": 67, "offset": 1646}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_audio.html", "start": {"line": 51, "col": 6, "offset": 2354}, "end": {"line": 51, "col": 67, "offset": 2415}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc.html", "start": {"line": 141, "col": 6, "offset": 6313}, "end": {"line": 141, "col": 67, "offset": 6374}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_deep_ar_effects_frame.html", "start": {"line": 10, "col": 5, "offset": 400}, "end": {"line": 10, "col": 106, "offset": 501}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_deep_ar_effects_frame.html", "start": {"line": 108, "col": 17, "offset": 4032}, "end": {"line": 108, "col": 78, "offset": 4093}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_deep_ar_effects_frame.html", "start": {"line": 284, "col": 15, "offset": 11191}, "end": {"line": 284, "col": 70, "offset": 11246}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_virtual_background_frame.html", "start": {"line": 80, "col": 6, "offset": 4100}, "end": {"line": 80, "col": 67, "offset": 4161}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/rtmp_srt_publish_and_play_sample.html", "start": {"line": 35, "col": 6, "offset": 1554}, "end": {"line": 35, "col": 67, "offset": 1615}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/webrtc-test-tool.html", "start": {"line": 568, "col": 20, "offset": 24308}, "end": {"line": 568, "col": 25, "offset": 24313}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js:\n ", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js:\n ", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js:\n ", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml", "start": {"line": 34, "col": 51, "offset": 1170}, "end": {"line": 34, "col": 84, "offset": 1203}}, {"path": "downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml", "start": {"line": 34, "col": 93, "offset": 1170}, "end": {"line": 34, "col": 126, "offset": 1203}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml:34:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ env.OLD_EMBEDDED_PLAYER_VERSION` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml", "start": {"line": 34, "col": 51, "offset": 1170}, "end": {"line": 34, "col": 84, "offset": 1203}}, {"file": "downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml", "start": {"line": 34, "col": 93, "offset": 1170}, "end": {"line": 34, "col": 126, "offset": 1203}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 36, "offset": 12}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 86, "offset": 12}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html:5:\n `> Play Audio` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 36, "offset": 12}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 86, "offset": 12}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 39, "offset": 15}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 89, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html:5:\n `> Audio Publish` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 39, "offset": 15}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 89, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 43, "offset": 19}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 19}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html:5:\n `> Data Channel Only` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 43, "offset": 19}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 19}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html", "start": {"line": 16, "col": 85, "offset": 0}, "end": {"line": 16, "col": 103, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html:5:\n `> Publish` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html", "start": {"line": 16, "col": 85, "offset": 0}, "end": {"line": 16, "col": 103, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "start": {"line": 4, "col": 23, "offset": 0}, "end": {"line": 4, "col": 29, "offset": 6}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "start": {"line": 19, "col": 48, "offset": 0}, "end": {"line": 19, "col": 54, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html:4:\n `> Play` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "start": {"line": 4, "col": 23, "offset": 0}, "end": {"line": 4, "col": 29, "offset": 6}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "start": {"line": 19, "col": 48, "offset": 0}, "end": {"line": 19, "col": 54, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 98, "offset": 24}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html:5:\n `> Publish` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 98, "offset": 24}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 102, "offset": 28}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html:5:\n `> Publish` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html", "start": {"line": 16, "col": 74, "offset": 0}, "end": {"line": 16, "col": 102, "offset": 28}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 40, "offset": 16}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html", "start": {"line": 16, "col": 85, "offset": 0}, "end": {"line": 16, "col": 86, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html:5:\n `> Publish & Play` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 40, "offset": 16}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html", "start": {"line": 16, "col": 85, "offset": 0}, "end": {"line": 16, "col": 86, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "start": {"line": 64, "col": 116, "offset": 0}, "end": {"line": 64, "col": 168, "offset": 52}}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "start": {"line": 127, "col": 72, "offset": 0}, "end": {"line": 128, "col": 115, "offset": 178}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html:64:\n `& WebRTC ingesting, Stream sources, IP Camera, etc.)` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "start": {"line": 64, "col": 116, "offset": 0}, "end": {"line": 64, "col": 168, "offset": 52}}, {"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "start": {"line": 127, "col": 72, "offset": 0}, "end": {"line": 128, "col": 115, "offset": 178}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/datachannel_only_webrtc_frame.html", "start": {"line": 4, "col": 23, "offset": 0}, "end": {"line": 4, "col": 42, "offset": 19}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/datachannel_only_webrtc_frame.html:4:\n `> Data Channel Only` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/datachannel_only_webrtc_frame.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/datachannel_only_webrtc_frame.html", "start": {"line": 4, "col": 23, "offset": 0}, "end": {"line": 4, "col": 42, "offset": 19}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_audio.html", "start": {"line": 3, "col": 23, "offset": 0}, "end": {"line": 3, "col": 35, "offset": 12}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_audio.html:3:\n `> Audio Play` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_audio.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_audio.html", "start": {"line": 3, "col": 23, "offset": 0}, "end": {"line": 3, "col": 35, "offset": 12}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_webrtc.html", "start": {"line": 3, "col": 23, "offset": 0}, "end": {"line": 3, "col": 29, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_webrtc.html:3:\n `> Play` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_webrtc.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_webrtc.html", "start": {"line": 3, "col": 23, "offset": 0}, "end": {"line": 3, "col": 29, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_audio.html", "start": {"line": 3, "col": 24, "offset": 0}, "end": {"line": 3, "col": 39, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_audio.html:3:\n `> Publish Audio` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_audio.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_audio.html", "start": {"line": 3, "col": 24, "offset": 0}, "end": {"line": 3, "col": 39, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc.html:5:\n `> Publish` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc.html", "start": {"line": 5, "col": 24, "offset": 0}, "end": {"line": 5, "col": 33, "offset": 9}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_virtual_background_frame.html", "start": {"line": 4, "col": 27, "offset": 0}, "end": {"line": 4, "col": 55, "offset": 28}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_virtual_background_frame.html:4:\n `> Publish Virtual Background` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_virtual_background_frame.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_virtual_background_frame.html", "start": {"line": 4, "col": 27, "offset": 0}, "end": {"line": 4, "col": 55, "offset": 28}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/rtmp_srt_publish_and_play_sample.html", "start": {"line": 3, "col": 23, "offset": 0}, "end": {"line": 3, "col": 35, "offset": 12}}]], "message": "Syntax error at line downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/rtmp_srt_publish_and_play_sample.html:3:\n `> Audio Play` was unexpected", "path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/rtmp_srt_publish_and_play_sample.html", "spans": [{"file": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/rtmp_srt_publish_and_play_sample.html", "start": {"line": 3, "col": 23, "offset": 0}, "end": {"line": 3, "col": 35, "offset": 12}}]}], "paths": {"scanned": ["downloaded_repos/ant-media_StreamApp/.babelrc", "downloaded_repos/ant-media_StreamApp/.classpath", "downloaded_repos/ant-media_StreamApp/.github/workflows/main.yaml", "downloaded_repos/ant-media_StreamApp/.github/workflows/release-package.yml", "downloaded_repos/ant-media_StreamApp/.github/workflows/release-snapshot.yml", "downloaded_repos/ant-media_StreamApp/.github/workflows/run-js-test.yml", "downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml", "downloaded_repos/ant-media_StreamApp/.gitignore", "downloaded_repos/ant-media_StreamApp/.npmignore", "downloaded_repos/ant-media_StreamApp/.project", "downloaded_repos/ant-media_StreamApp/.settings/.gitignore", "downloaded_repos/ant-media_StreamApp/README.md", "downloaded_repos/ant-media_StreamApp/api-extractor.json", "downloaded_repos/ant-media_StreamApp/codecov.yml", "downloaded_repos/ant-media_StreamApp/codesigning.asc.enc", "downloaded_repos/ant-media_StreamApp/embedded-player/.gitignore", "downloaded_repos/ant-media_StreamApp/embedded-player/embedded-player.js", "downloaded_repos/ant-media_StreamApp/embedded-player/package-lock.json", "downloaded_repos/ant-media_StreamApp/embedded-player/package.json", "downloaded_repos/ant-media_StreamApp/embedded-player/rollup.config.module.cjs", "downloaded_repos/ant-media_StreamApp/karma.conf.cjs", "downloaded_repos/ant-media_StreamApp/mvn-settings.xml", "downloaded_repos/ant-media_StreamApp/package-lock.json", "downloaded_repos/ant-media_StreamApp/package.json", "downloaded_repos/ant-media_StreamApp/pom.xml", "downloaded_repos/ant-media_StreamApp/redeploy.sh", "downloaded_repos/ant-media_StreamApp/rollup.config.browser.cjs", "downloaded_repos/ant-media_StreamApp/rollup.config.module.cjs", "downloaded_repos/ant-media_StreamApp/src/main/.npmignore", "downloaded_repos/ant-media_StreamApp/src/main/java/io/antmedia/SecurityConfiguration.java", "downloaded_repos/ant-media_StreamApp/src/main/java/io/antmedia/enterprise/streamapp/AMSEndpointConfigurator.java", "downloaded_repos/ant-media_StreamApp/src/main/java/io/antmedia/enterprise/streamapp/WebSocketLocalHandler.java", "downloaded_repos/ant-media_StreamApp/src/main/java/io/antmedia/enterprise/streamapp/WebSocketSignalingLocalHandler.java", "downloaded_repos/ant-media_StreamApp/src/main/js/fetch.stream.js", "downloaded_repos/ant-media_StreamApp/src/main/js/index.js", "downloaded_repos/ant-media_StreamApp/src/main/js/media_manager.js", "downloaded_repos/ant-media_StreamApp/src/main/js/peer_stats.js", "downloaded_repos/ant-media_StreamApp/src/main/js/soundmeter.js", "downloaded_repos/ant-media_StreamApp/src/main/js/stream_merger.js", "downloaded_repos/ant-media_StreamApp/src/main/js/utility.js", "downloaded_repos/ant-media_StreamApp/src/main/js/video-effect.js", "downloaded_repos/ant-media_StreamApp/src/main/js/volume-meter-processor.js", "downloaded_repos/ant-media_StreamApp/src/main/js/webrtc_adaptor.js", "downloaded_repos/ant-media_StreamApp/src/main/js/websocket_adaptor.js", "downloaded_repos/ant-media_StreamApp/src/main/resources/probe-template.xml", "downloaded_repos/ant-media_StreamApp/src/main/webapp/.npmignore", "downloaded_repos/ant-media_StreamApp/src/main/webapp/META-INF/context.xml", "downloaded_repos/ant-media_StreamApp/src/main/webapp/WEB-INF/red5-web.properties", "downloaded_repos/ant-media_StreamApp/src/main/webapp/WEB-INF/red5-web.xml", "downloaded_repos/ant-media_StreamApp/src/main/webapp/WEB-INF/web.xml", "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/cam_play.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-player.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/canvas-publish.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference-deprecated.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference-room.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/conference.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/common.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/animate.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap3/bootstrap.min.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-grid.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-grid.css.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-grid.min.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-grid.min.css.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-reboot.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-reboot.css.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-reboot.min.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap-reboot.min.css.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap.css.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap.min.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/bootstrap4/bootstrap.min.css.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/external/video-js.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/player.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/samples.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/style.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/style.css.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/style.scss", "downloaded_repos/ant-media_StreamApp/src/main/webapp/css/videojs-webrtc-plugin.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel_example.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/ant-media-emblem.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/ant-media-logo.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/ant-media-video-call.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/audio.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/blur-background.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/favicon.ico", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/favicon.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/camera-off.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/camera-on.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/chat.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/close-call.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/close.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/copy.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/fullscreen.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/microphone-off.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/microphone-on.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/mute-icon.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/screen-share-off.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/screen-share-on.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/send.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/settings.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/share.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/speaker-icon.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/icons/users.svg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/image-upload.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/loading.gif", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/login/login-bg.jpg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/logo.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/noeffect-background.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/on-air-light.jpg", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/play.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/slight-blur-background.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/virtual-background.png", "downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/.gitignore", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer-widget.js", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/.gitignore", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.min.js.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.min.js.map", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/deepar-effects/Ping_Pong.deepar", "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js", "downloaded_repos/ant-media_StreamApp/src/main/webapp/mcu.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/merge_streams.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/multipeerplay.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrack-play.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/multitrackplayer.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/peer.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/play.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/player_with_timestamp.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_with_timestamp.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/push-notification-register-and-send-draft.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/datachannel_only_webrtc_frame.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_audio.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_webrtc.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_audio.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_deep_ar_effects_frame.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_virtual_background_frame.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/rtmp_srt_publish_and_play_sample.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/styles.css", "downloaded_repos/ant-media_StreamApp/src/main/webapp/webrtc-test-tool.html", "downloaded_repos/ant-media_StreamApp/src/main/webapp/whip.html", "downloaded_repos/ant-media_StreamApp/tsconfig.json"], "skipped": [{"path": "downloaded_repos/ant-media_StreamApp/.github/workflows/update-embedded-player.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/js/external/loglevel.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_player.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/audio_publish.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/datachannel.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/images/cloud-background.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/aws-sdk-2.839.0.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/deepar-effects/flower_face.deepar", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/jquery-3.7.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/jquery-3.7.1.slim.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/notify.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/popper.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/player.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_deep_ar_effects.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/publish_webrtc_virtual_background.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/rtmp_srt_publish_and_play.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/datachannel_only_webrtc_frame.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_audio.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/play_webrtc.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_audio.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/publish_webrtc_virtual_background_frame.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples/rtmp_srt_publish_and_play_sample.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/main/webapp/samples.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ant-media_StreamApp/src/test/js/fetch.stream.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/test/js/media_manager.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/test/js/selenium.spec.removed-js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/test/js/soundmeter.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/test/js/stream.merger.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/test/js/utility.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ant-media_StreamApp/src/test/js/webrtc_adaptor.test.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.648850917816162, "profiling_times": {"config_time": 6.222496032714844, "core_time": 23.40612006187439, "ignores_time": 0.0020627975463867188, "total_time": 29.631617546081543}, "parsing_time": {"total_time": 8.779592275619507, "per_file_time": {"mean": 0.12026838733725348, "std_dev": 0.11304491874074049}, "very_slow_stats": {"time_ratio": 0.7288484046082749, "count_ratio": 0.0684931506849315}, "very_slow_files": [{"fpath": "downloaded_repos/ant-media_StreamApp/src/main/js/webrtc_adaptor.js", "ftime": 0.5167229175567627}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "ftime": 1.2190370559692383}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "ftime": 1.4421398639678955}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "ftime": 1.4850990772247314}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "ftime": 1.7359929084777832}]}, "scanning_time": {"total_time": 79.60595393180847, "per_file_time": {"mean": 0.20411783059438057, "std_dev": 2.430738167283067}, "very_slow_stats": {"time_ratio": 0.8284744540616151, "count_ratio": 0.015384615384615385}, "very_slow_files": [{"fpath": "downloaded_repos/ant-media_StreamApp/src/main/js/webrtc_adaptor.js", "ftime": 1.594365119934082}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "ftime": 7.061801910400391}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "ftime": 8.912522077560425}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "ftime": 10.860536098480225}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "ftime": 17.031245946884155}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/hls.js", "ftime": 20.49102807044983}]}, "matching_time": {"total_time": 23.416282176971436, "per_file_and_rule_time": {"mean": 0.050684593456648115, "std_dev": 0.041281630205756654}, "very_slow_stats": {"time_ratio": 0.7953975994258963, "count_ratio": 0.08441558441558442}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.5727431774139404}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.6283440589904785}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.6500508785247803}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.6545009613037109}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.6793608665466309}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.7355818748474121}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.8371109962463379}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.531181812286377}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.9985949993133545}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 2.8330090045928955}]}, "tainting_time": {"total_time": 16.552650928497314, "per_def_and_rule_time": {"mean": 0.0035098920543887436, "std_dev": 0.0011838993184992866}, "very_slow_stats": {"time_ratio": 0.7460552221510842, "count_ratio": 0.01102629346904156}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.3124380111694336}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/adapter-latest.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.33039212226867676}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.3359348773956299}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.3711419105529785}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.373291015625}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.3753030300140381}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.6079568862915039}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/external/bootstrap.bundle.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.6456999778747559}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.1462130546569824}, {"fpath": "downloaded_repos/ant-media_StreamApp/src/main/webapp/js/canvas-designer.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 1.231443166732788}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}