{"version": "1.130.0", "results": [{"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/pallets-eco_flask-wtf/examples/babel/templates/index.html", "start": {"line": 6, "col": 5, "offset": 100}, "end": {"line": 17, "col": 12, "offset": 383}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/pallets-eco_flask-wtf/examples/recaptcha/templates/index.html", "start": {"line": 6, "col": 9, "offset": 114}, "end": {"line": 21, "col": 16, "offset": 631}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/pallets-eco_flask-wtf/examples/uploadr/templates/index.html", "start": {"line": 8, "col": 9, "offset": 145}, "end": {"line": 19, "col": 16, "offset": 499}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/csrf.py", "start": {"line": 53, "col": 35, "offset": 1659}, "end": {"line": 53, "col": 63, "offset": 1687}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(o<PERSON><PERSON><PERSON><PERSON><PERSON>(64))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/csrf.py", "start": {"line": 58, "col": 35, "offset": 1823}, "end": {"line": 58, "col": 63, "offset": 1851}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(o<PERSON><PERSON><PERSON><PERSON><PERSON>(64))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.tainted-url-host.tainted-url-host", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/csrf.py", "start": {"line": 270, "col": 29, "offset": 8609}, "end": {"line": 270, "col": 55, "offset": 8635}, "extra": {"message": "User data flows into the host portion of this manually-constructed URL. This could allow an attacker to send data to their own server, potentially exposing sensitive data such as cookies or authorization information sent with this request. They could also probe internal servers or other resources that the server running this code can access. (This is called server-side request forgery, or SSRF.) Do not allow arbitrary hosts. Instead, create an allowlist for approved hosts, or hardcode the correct host.", "metadata": {"cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Server_Side_Request_Forgery_Prevention_Cheat_Sheet.html"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "impact": "MEDIUM", "likelihood": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/python.django.security.injection.tainted-url-host.tainted-url-host", "shortlink": "https://sg.run/oYz6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.injection.tainted-url-host.tainted-url-host", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/csrf.py", "start": {"line": 270, "col": 29, "offset": 8609}, "end": {"line": 270, "col": 55, "offset": 8635}, "extra": {"message": "User data flows into the host portion of this manually-constructed URL. This could allow an attacker to send data to their own server, potentially exposing sensitive data such as cookies or authorization information sent with this request. They could also probe internal servers or other resources that the server running this code can access. (This is called server-side request forgery, or SSRF.) Do not allow arbitrary hosts. Instead, create an allowlist for approved hosts, or hardcode the correct host.", "metadata": {"cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Server_Side_Request_Forgery_Prevention_Cheat_Sheet.html"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/python.flask.security.injection.tainted-url-host.tainted-url-host", "shortlink": "https://sg.run/RXpK"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.explicit-unescape-with-markup.explicit-unescape-with-markup", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/form.py", "start": {"line": 119, "col": 16, "offset": 3758}, "end": {"line": 119, "col": 80, "offset": 3822}, "extra": {"message": "Detected explicitly unescaped content using 'Markup()'. This permits the unescaped data to include unescaped HTML which could result in cross-site scripting. Ensure this data is not externally controlled, or consider rewriting to not use 'Markup()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://tedboy.github.io/flask/generated/generated/flask.Markup.html"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.explicit-unescape-with-markup.explicit-unescape-with-markup", "shortlink": "https://sg.run/AvZ8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/recaptcha/validators.py", "start": {"line": 61, "col": 25, "offset": 2015}, "end": {"line": 61, "col": 74, "offset": 2064}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.explicit-unescape-with-markup.explicit-unescape-with-markup", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/recaptcha/widgets.py", "start": {"line": 20, "col": 20, "offset": 494}, "end": {"line": 20, "col": 32, "offset": 506}, "extra": {"message": "Detected explicitly unescaped content using 'Markup()'. This permits the unescaped data to include unescaped HTML which could result in cross-site scripting. Ensure this data is not externally controlled, or consider rewriting to not use 'Markup()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://tedboy.github.io/flask/generated/generated/flask.Markup.html"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.explicit-unescape-with-markup.explicit-unescape-with-markup", "shortlink": "https://sg.run/AvZ8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.explicit-unescape-with-markup.explicit-unescape-with-markup", "path": "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/recaptcha/widgets.py", "start": {"line": 33, "col": 16, "offset": 1117}, "end": {"line": 33, "col": 73, "offset": 1174}, "extra": {"message": "Detected explicitly unescaped content using 'Markup()'. This permits the unescaped data to include unescaped HTML which could result in cross-site scripting. Ensure this data is not externally controlled, or consider rewriting to not use 'Markup()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://tedboy.github.io/flask/generated/generated/flask.Markup.html"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.explicit-unescape-with-markup.explicit-unescape-with-markup", "shortlink": "https://sg.run/AvZ8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/publish.yaml", "start": {"line": 31, "col": 73, "offset": 950}, "end": {"line": 31, "col": 76, "offset": 953}}]], "message": "Syntax error at line downloaded_repos/pallets-eco_flask-wtf/.github/workflows/publish.yaml:31:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/publish.yaml", "spans": [{"file": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/publish.yaml", "start": {"line": 31, "col": 73, "offset": 950}, "end": {"line": 31, "col": 76, "offset": 953}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml", "start": {"line": 37, "col": 49, "offset": 872}, "end": {"line": 37, "col": 50, "offset": 873}}, {"path": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml", "start": {"line": 37, "col": 72, "offset": 872}, "end": {"line": 37, "col": 76, "offset": 876}}]], "message": "Syntax error at line downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml:37:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `(` was unexpected", "path": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml", "spans": [{"file": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml", "start": {"line": 37, "col": 49, "offset": 872}, "end": {"line": 37, "col": 50, "offset": 873}}, {"file": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml", "start": {"line": 37, "col": 72, "offset": 872}, "end": {"line": 37, "col": 76, "offset": 876}}]}], "paths": {"scanned": ["downloaded_repos/pallets-eco_flask-wtf/.editorconfig", "downloaded_repos/pallets-eco_flask-wtf/.github/ISSUE_TEMPLATE/bug-report.md", "downloaded_repos/pallets-eco_flask-wtf/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/pallets-eco_flask-wtf/.github/ISSUE_TEMPLATE/feature-request.md", "downloaded_repos/pallets-eco_flask-wtf/.github/dependabot.yml", "downloaded_repos/pallets-eco_flask-wtf/.github/pull_request_template.md", "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/lock.yaml", "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/pre-commit.yaml", "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/publish.yaml", "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml", "downloaded_repos/pallets-eco_flask-wtf/.gitignore", "downloaded_repos/pallets-eco_flask-wtf/.pre-commit-config.yaml", "downloaded_repos/pallets-eco_flask-wtf/.readthedocs.yaml", "downloaded_repos/pallets-eco_flask-wtf/CONTRIBUTING.rst", "downloaded_repos/pallets-eco_flask-wtf/LICENSE.rst", "downloaded_repos/pallets-eco_flask-wtf/MANIFEST.in", "downloaded_repos/pallets-eco_flask-wtf/README.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/Makefile", "downloaded_repos/pallets-eco_flask-wtf/docs/_static/flask-wtf-icon.png", "downloaded_repos/pallets-eco_flask-wtf/docs/_static/flask-wtf.png", "downloaded_repos/pallets-eco_flask-wtf/docs/api.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/changes.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/conf.py", "downloaded_repos/pallets-eco_flask-wtf/docs/config.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/contributing.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/csrf.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/form.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/index.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/install.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/license.rst", "downloaded_repos/pallets-eco_flask-wtf/docs/make.bat", "downloaded_repos/pallets-eco_flask-wtf/docs/quickstart.rst", "downloaded_repos/pallets-eco_flask-wtf/examples/babel/app.py", "downloaded_repos/pallets-eco_flask-wtf/examples/babel/templates/index.html", "downloaded_repos/pallets-eco_flask-wtf/examples/recaptcha/app.py", "downloaded_repos/pallets-eco_flask-wtf/examples/recaptcha/templates/index.html", "downloaded_repos/pallets-eco_flask-wtf/examples/uploadr/app.py", "downloaded_repos/pallets-eco_flask-wtf/examples/uploadr/templates/index.html", "downloaded_repos/pallets-eco_flask-wtf/pyproject.toml", "downloaded_repos/pallets-eco_flask-wtf/requirements/dev.in", "downloaded_repos/pallets-eco_flask-wtf/requirements/dev.txt", "downloaded_repos/pallets-eco_flask-wtf/requirements/docs.in", "downloaded_repos/pallets-eco_flask-wtf/requirements/docs.txt", "downloaded_repos/pallets-eco_flask-wtf/requirements/style.in", "downloaded_repos/pallets-eco_flask-wtf/requirements/style.txt", "downloaded_repos/pallets-eco_flask-wtf/requirements/tests.in", "downloaded_repos/pallets-eco_flask-wtf/requirements/tests.txt", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/__init__.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/_compat.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/csrf.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/file.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/form.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/i18n.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/recaptcha/__init__.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/recaptcha/fields.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/recaptcha/validators.py", "downloaded_repos/pallets-eco_flask-wtf/src/flask_wtf/recaptcha/widgets.py", "downloaded_repos/pallets-eco_flask-wtf/tox.ini"], "skipped": [{"path": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/publish.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/.github/workflows/tests.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/tests/test_csrf_extension.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/tests/test_csrf_form.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/tests/test_file.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/tests/test_form.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/tests/test_i18n.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pallets-eco_flask-wtf/tests/test_recaptcha.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9175169467926025, "profiling_times": {"config_time": 7.683279275894165, "core_time": 3.0645129680633545, "ignores_time": 0.0020318031311035156, "total_time": 10.751038074493408}, "parsing_time": {"total_time": 0.37774229049682617, "per_file_time": {"mean": 0.016423577847688095, "std_dev": 0.00015183025462365535}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.3731579780578613, "per_file_time": {"mean": 0.016830907645800443, "std_dev": 0.0018733942512229485}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.5116024017333984, "per_file_and_rule_time": {"mean": 0.0012570083580673182, "std_dev": 9.599303101980452e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.12761449813842773, "per_def_and_rule_time": {"mean": 0.00024123723655657416, "std_dev": 1.216429101114615e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}