{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/explorer/js/aaguids-explorer.js", "start": {"line": 21, "col": 3, "offset": 397}, "end": {"line": 21, "col": 24, "offset": 418}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-changes.yml", "start": {"line": 88, "col": 45, "offset": 2959}, "end": {"line": 88, "col": 61, "offset": 2975}}]], "message": "Syntax error at line downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-changes.yml:88:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ env.COMMIT_MSG` was unexpected", "path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-changes.yml", "spans": [{"file": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-changes.yml", "start": {"line": 88, "col": 45, "offset": 2959}, "end": {"line": 88, "col": 61, "offset": 2975}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-cron.yml", "start": {"line": 88, "col": 45, "offset": 3115}, "end": {"line": 88, "col": 61, "offset": 3131}}]], "message": "Syntax error at line downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-cron.yml:88:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ env.COMMIT_MSG` was unexpected", "path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-cron.yml", "spans": [{"file": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-cron.yml", "start": {"line": 88, "col": 45, "offset": 3115}, "end": {"line": 88, "col": 61, "offset": 3131}}]}], "paths": {"scanned": ["downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-changes.yml", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-cron.yml", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/validate-json.yml", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/README.md", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/aaguid.json", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/aaguid.json.schema", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/exclude.txt", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/explorer/css/aaguids-explorer.css", "downloaded_repos/passkeydeveloper_passkey-authenticator-aagu<PERSON>/explorer/img/favicon.ico", "downloaded_repos/passkeydeveloper_passkey-authenticator-aagu<PERSON>/explorer/img/github-mark.svg", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/explorer/index.html", "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/explorer/js/aaguids-explorer.js"], "skipped": [{"path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-changes.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/.github/workflows/update-from-mds-cron.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/passkeydeveloper_passkey-authenticator-aaguids/combined_aaguid.json", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.8312439918518066, "profiling_times": {"config_time": 6.050867557525635, "core_time": 2.6899149417877197, "ignores_time": 0.0016047954559326172, "total_time": 8.7436203956604}, "parsing_time": {"total_time": 0.09131073951721191, "per_file_time": {"mean": 0.015218456586201986, "std_dev": 2.6186998968885037e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.3152625560760498, "per_file_time": {"mean": 0.01050875186920166, "std_dev": 0.0002757741184774432}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.06682443618774414, "per_file_and_rule_time": {"mean": 0.0006551415312523936, "std_dev": 2.024997780099707e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.00894308090209961, "per_def_and_rule_time": {"mean": 0.00018251185514489, "std_dev": 6.059222173752111e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}