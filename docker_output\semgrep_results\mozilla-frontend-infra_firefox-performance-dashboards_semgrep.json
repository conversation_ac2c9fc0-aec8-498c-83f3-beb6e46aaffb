{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.all-contributorsrc", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.babelrc", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.circleci/config.yml", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.codecov.yml", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.eslintrc.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.gitignore", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.nvmrc", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.renovaterc", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/.renovaterc.md", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/LICENSE", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/README.md", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/jest.config.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/netlify.toml", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/package.json", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/public/index.html", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/chartJs.test.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/components/Pickers.test.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/components/__snapshots__/Pickers.test.jsx.snap", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/config.test.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/fetchAndCache.test.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/fetchWrapper.test.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/localStorageUtils.test.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/mocks/mac/StyleBench/signatures.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/mocks/mac/StyleBench/urls.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/perfherder.test.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/__tests__/recordings/Perfherder-Tests_2140894445/recording.har", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/awfy.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/awsy.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/App/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/ChartJSWrapper/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/CircularIndeterminate/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Description/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/EmptyState/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Footer/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Legend/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Legend/legend.css", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Loading/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/MultipleSelectPicker/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Navigation/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/PerfherderGraph/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Picker/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Pickers/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/components/Router/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/config-utils.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/config.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/h3.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/index.jsx", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/utils/chartJs.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/utils/fetchAndCache.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/utils/fetchWrapper.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/utils/localStorageUtils.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/utils/perfherder.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/utils/timeRangeUtils.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/src/utils/validCombination.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/webpack.config.js", "downloaded_repos/mozilla-frontend-infra_firefox-performance-dashboards/yarn.lock"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7583839893341064, "profiling_times": {"config_time": 6.177748203277588, "core_time": 2.870720624923706, "ignores_time": 0.0018949508666992188, "total_time": 9.051497220993042}, "parsing_time": {"total_time": 0.9458291530609131, "per_file_time": {"mean": 0.021996026815370075, "std_dev": 0.0008510816767561566}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.0856616497039795, "per_file_time": {"mean": 0.026359107417445026, "std_dev": 0.004519602677794457}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.1083006858825684, "per_file_and_rule_time": {"mean": 0.0045052873409860495, "std_dev": 7.918814560086942e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.4652435779571533, "per_def_and_rule_time": {"mean": 0.00262849479071838, "std_dev": 1.7251514320435726e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}