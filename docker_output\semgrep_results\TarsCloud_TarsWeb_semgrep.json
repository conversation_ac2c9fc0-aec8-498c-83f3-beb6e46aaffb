{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/app/controller/patch/PatchController.js", "start": {"line": 481, "col": 65, "offset": 15785}, "end": {"line": 481, "col": 91, "offset": 15811}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/app/controller/patch/PatchController.js", "start": {"line": 481, "col": 93, "offset": 15813}, "end": {"line": 481, "col": 119, "offset": 15839}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/app/controller/patch/PatchController.js", "start": {"line": 481, "col": 121, "offset": 15841}, "end": {"line": 481, "col": 130, "offset": 15850}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/index.js", "start": {"line": 107, "col": 17, "offset": 2740}, "end": {"line": 107, "col": 74, "offset": 2797}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/app/init/tars.js", "start": {"line": 36, "col": 43, "offset": 1359}, "end": {"line": 36, "col": 46, "offset": 1362}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/app/init/tars.js", "start": {"line": 36, "col": 48, "offset": 1364}, "end": {"line": 36, "col": 52, "offset": 1368}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/app/init/tars.js", "start": {"line": 154, "col": 47, "offset": 5099}, "end": {"line": 154, "col": 51, "offset": 5103}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/api/util.js", "start": {"line": 326, "col": 15, "offset": 10388}, "end": {"line": 326, "col": 54, "offset": 10427}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/common/util.js", "start": {"line": 310, "col": 15, "offset": 9610}, "end": {"line": 310, "col": 54, "offset": 9649}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/components/diff/index.vue", "start": {"line": 3, "col": 5, "offset": 50}, "end": {"line": 3, "col": 17, "offset": 62}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/components/xterm.vue", "start": {"line": 39, "col": 18, "offset": 1036}, "end": {"line": 39, "col": 23, "offset": 1041}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/log.vue", "start": {"line": 76, "col": 16, "offset": 1682}, "end": {"line": 76, "col": 21, "offset": 1687}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/manage.vue", "start": {"line": 866, "col": 7, "offset": 28724}, "end": {"line": 866, "col": 20, "offset": 28737}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/publish.vue", "start": {"line": 156, "col": 19, "offset": 4880}, "end": {"line": 156, "col": 32, "offset": 4893}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/publish.vue", "start": {"line": 157, "col": 20, "offset": 4960}, "end": {"line": 157, "col": 40, "offset": 4980}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/publish.vue", "start": {"line": 174, "col": 19, "offset": 5636}, "end": {"line": 174, "col": 32, "offset": 5649}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/publish.vue", "start": {"line": 175, "col": 20, "offset": 5707}, "end": {"line": 175, "col": 40, "offset": 5727}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/publish.vue", "start": {"line": 176, "col": 19, "offset": 5764}, "end": {"line": 176, "col": 32, "offset": 5777}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/publish.vue", "start": {"line": 177, "col": 20, "offset": 5832}, "end": {"line": 177, "col": 40, "offset": 5852}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/lib/date.js", "start": {"line": 58, "col": 9, "offset": 1944}, "end": {"line": 58, "col": 36, "offset": 1971}, "extra": {"message": "RegExp() called with a `key` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/image.vue", "start": {"line": 270, "col": 13, "offset": 8900}, "end": {"line": 270, "col": 26, "offset": 8913}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/manage.vue", "start": {"line": 1056, "col": 7, "offset": 35151}, "end": {"line": 1056, "col": 20, "offset": 35164}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/publish.vue", "start": {"line": 186, "col": 21, "offset": 6373}, "end": {"line": 186, "col": 34, "offset": 6386}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/publish.vue", "start": {"line": 187, "col": 22, "offset": 6441}, "end": {"line": 187, "col": 42, "offset": 6461}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/publish.vue", "start": {"line": 188, "col": 21, "offset": 6506}, "end": {"line": 189, "col": 30, "offset": 6541}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/publish.vue", "start": {"line": 192, "col": 22, "offset": 6655}, "end": {"line": 192, "col": 42, "offset": 6675}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "start": {"line": 823, "col": 16, "offset": 24528}, "end": {"line": 823, "col": 37, "offset": 24549}, "extra": {"message": "RegExp() called with a `end` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "start": {"line": 1422, "col": 16, "offset": 48854}, "end": {"line": 1422, "col": 37, "offset": 48875}, "extra": {"message": "RegExp() called with a `end` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "start": {"line": 1426, "col": 16, "offset": 48960}, "end": {"line": 1426, "col": 39, "offset": 48983}, "extra": {"message": "RegExp() called with a `start` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/combobox.js", "start": {"line": 888, "col": 21, "offset": 40482}, "end": {"line": 888, "col": 30, "offset": 40491}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/json2.js", "start": {"line": 466, "col": 21, "offset": 16978}, "end": {"line": 466, "col": 43, "offset": 17000}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "start": {"line": 664, "col": 21, "offset": 23135}, "end": {"line": 664, "col": 56, "offset": 23170}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/TarsCloud_TarsWeb/docker/Dockerfile", "start": {"line": 28, "col": 1, "offset": 1628}, "end": {"line": 28, "col": 27, "offset": 1654}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/bin/entrypoint.sh\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/index/controller/locale/LocaleController.js", "start": {"line": 25, "col": 60, "offset": 1047}, "end": {"line": 25, "col": 68, "offset": 1055}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.telnet-request.telnet-request", "path": "downloaded_repos/TarsCloud_TarsWeb/k8s/service/node/NodeService.js", "start": {"line": 320, "col": 15, "offset": 9424}, "end": {"line": 320, "col": 46, "offset": 9455}, "extra": {"message": "Checks for creation of telnet servers or attempts to connect through telnet. This is insecure as the telnet protocol supports no encryption, and data passes through unencrypted.", "metadata": {"likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": "A03:2017 - Sensitive Data Exposure", "references": ["https://www.npmjs.com/package/telnet", "https://www.npmjs.com/package/telnet-client"], "subcategory": ["vuln"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.telnet-request.telnet-request", "shortlink": "https://sg.run/weoA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/logger/index.js", "start": {"line": 108, "col": 49, "offset": 3812}, "end": {"line": 108, "col": 57, "offset": 3820}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/TarsCloud_TarsWeb/market/service/MarketK8SService.js", "start": {"line": 52, "col": 11, "offset": 1897}, "end": {"line": 52, "col": 75, "offset": 1961}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/TarsCloud_TarsWeb/market/service/MarketK8SService.js", "start": {"line": 65, "col": 10, "offset": 2217}, "end": {"line": 65, "col": 39, "offset": 2246}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/TarsCloud_TarsWeb/midware/localeMidware.js", "start": {"line": 28, "col": 62, "offset": 1033}, "end": {"line": 28, "col": 70, "offset": 1041}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.jsonwebtoken.security.audit.jwt-exposed-data.jwt-exposed-data", "path": "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/token/TokenService.js", "start": {"line": 23, "col": 12, "offset": 546}, "end": {"line": 23, "col": 45, "offset": 579}, "extra": {"message": "The object is passed strictly to jsonwebtoken.sign(...) Make sure that sensitive information is not exposed through JWT token payload.", "metadata": {"owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "source-rule-url": "https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/", "asvs": {"control_id": "3.5.3 Insecue Stateless Session Tokens", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "category": "security", "technology": ["jwt"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.jsonwebtoken.security.audit.jwt-exposed-data.jwt-exposed-data", "shortlink": "https://sg.run/5Qkj"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "path": "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/token/TokenService.js", "start": {"line": 23, "col": 29, "offset": 563}, "end": {"line": 23, "col": 35, "offset": 569}, "extra": {"message": "A hard-coded credential was detected. It is not recommended to store credentials in source-code, as this risks secrets being leaked and used by either an internal or external malicious adversary. It is recommended to use environment variables to securely provide credentials or retrieve credentials from a secure vault or HSM (Hardware Security Module).", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "asvs": {"control_id": "3.5.2 Static API keys or secret", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "category": "security", "technology": ["jwt", "javascript", "secrets"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "shortlink": "https://sg.run/4xN9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "path": "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/token/TokenService.js", "start": {"line": 30, "col": 30, "offset": 860}, "end": {"line": 30, "col": 36, "offset": 866}, "extra": {"message": "A hard-coded credential was detected. It is not recommended to store credentials in source-code, as this risks secrets being leaked and used by either an internal or external malicious adversary. It is recommended to use environment variables to securely provide credentials or retrieve credentials from a secure vault or HSM (Hardware Security Module).", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "asvs": {"control_id": "3.5.2 Static API keys or secret", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "category": "security", "technology": ["jwt", "javascript", "secrets"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "shortlink": "https://sg.run/4xN9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.jsonwebtoken.security.audit.jwt-exposed-data.jwt-exposed-data", "path": "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/token/TokenService.js", "start": {"line": 43, "col": 12, "offset": 929}, "end": {"line": 43, "col": 45, "offset": 962}, "extra": {"message": "The object is passed strictly to jsonwebtoken.sign(...) Make sure that sensitive information is not exposed through JWT token payload.", "metadata": {"owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "source-rule-url": "https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/", "asvs": {"control_id": "3.5.3 Insecue Stateless Session Tokens", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "category": "security", "technology": ["jwt"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.jsonwebtoken.security.audit.jwt-exposed-data.jwt-exposed-data", "shortlink": "https://sg.run/5Qkj"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "path": "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/token/TokenService.js", "start": {"line": 43, "col": 29, "offset": 946}, "end": {"line": 43, "col": 35, "offset": 952}, "extra": {"message": "A hard-coded credential was detected. It is not recommended to store credentials in source-code, as this risks secrets being leaked and used by either an internal or external malicious adversary. It is recommended to use environment variables to securely provide credentials or retrieve credentials from a secure vault or HSM (Hardware Security Module).", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "asvs": {"control_id": "3.5.2 Static API keys or secret", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "category": "security", "technology": ["jwt", "javascript", "secrets"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "shortlink": "https://sg.run/4xN9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "path": "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/token/TokenService.js", "start": {"line": 50, "col": 30, "offset": 1243}, "end": {"line": 50, "col": 36, "offset": 1249}, "extra": {"message": "A hard-coded credential was detected. It is not recommended to store credentials in source-code, as this risks secrets being leaked and used by either an internal or external malicious adversary. It is recommended to use environment variables to securely provide credentials or retrieve credentials from a secure vault or HSM (Hardware Security Module).", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "asvs": {"control_id": "3.5.2 Static API keys or secret", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "category": "security", "technology": ["jwt", "javascript", "secrets"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret", "shortlink": "https://sg.run/4xN9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "message": "Timeout when running javascript.aws-lambda.security.tainted-eval.tainted-eval on downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js:\n ", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-response.tainted-html-response on downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js:\n ", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js:\n ", "path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TarsCloud_TarsWeb/.github/workflows/tarsweb.yml", "start": {"line": 15, "col": 53, "offset": 239}, "end": {"line": 15, "col": 56, "offset": 242}}]], "message": "Syntax error at line downloaded_repos/TarsCloud_TarsWeb/.github/workflows/tarsweb.yml:15:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/TarsCloud_TarsWeb/.github/workflows/tarsweb.yml", "spans": [{"file": "downloaded_repos/TarsCloud_TarsWeb/.github/workflows/tarsweb.yml", "start": {"line": 15, "col": 53, "offset": 239}, "end": {"line": 15, "col": 56, "offset": 242}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_uninstall.sh", "start": {"line": 4, "col": 14, "offset": 0}, "end": {"line": 4, "col": 17, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_uninstall.sh:4:\n `(C)` was unexpected", "path": "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_uninstall.sh", "spans": [{"file": "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_uninstall.sh", "start": {"line": 4, "col": 14, "offset": 0}, "end": {"line": 4, "col": 17, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/TarsCloud_TarsWeb/client/public/index.html", "start": {"line": 8, "col": 12, "offset": 0}, "end": {"line": 8, "col": 50, "offset": 38}}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/public/index.html", "start": {"line": 12, "col": 31, "offset": 0}, "end": {"line": 12, "col": 149, "offset": 118}}]], "message": "Syntax error at line downloaded_repos/TarsCloud_TarsWeb/client/public/index.html:8:\n `<%= htmlWebpackPlugin.options.title %>` was unexpected", "path": "downloaded_repos/TarsCloud_TarsWeb/client/public/index.html", "spans": [{"file": "downloaded_repos/TarsCloud_TarsWeb/client/public/index.html", "start": {"line": 8, "col": 12, "offset": 0}, "end": {"line": 8, "col": 50, "offset": 38}}, {"file": "downloaded_repos/TarsCloud_TarsWeb/client/public/index.html", "start": {"line": 12, "col": 31, "offset": 0}, "end": {"line": 12, "col": 149, "offset": 118}}]}], "paths": {"scanned": ["downloaded_repos/TarsCloud_TarsWeb/.github/workflows/gitee-sync.yml", "downloaded_repos/TarsCloud_TarsWeb/.github/workflows/tarsweb.yml", "downloaded_repos/TarsCloud_TarsWeb/.gitignore", "downloaded_repos/TarsCloud_TarsWeb/ChangeList.md", "downloaded_repos/TarsCloud_TarsWeb/Contributing.md", "downloaded_repos/TarsCloud_TarsWeb/How2DebugInK8S.md", "downloaded_repos/TarsCloud_TarsWeb/LICENSE", "downloaded_repos/TarsCloud_TarsWeb/README.md", "downloaded_repos/TarsCloud_TarsWeb/README.zh.md", "downloaded_repos/TarsCloud_TarsWeb/app/controller/adapter/AdapterController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/application/ApplicationController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/business/BusinessController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/businessRelation/BusinessRelationController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/config/ConfigController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/deploy/DeployServerController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/expand/ExpandServerController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/idc/IDCController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/image/ImageController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/logview/LogviewController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/monitor/MonitorController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/notify/NotifyController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/patch/PatchController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/resource/ResourceController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/server/ServerController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/server/TreeController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/task/TaskController.js", "downloaded_repos/TarsCloud_TarsWeb/app/controller/template/TemplateController.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/AdapterDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/ApplicationDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/BaseImageDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/BusinessDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/BusinessRelationDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/ConfigDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/DockerRegistryDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/FrameworkDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/IDCDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/NodeInfoDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/NotifyDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/PatchDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/PluginDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/ResourceDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/ServerDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/TaskDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/TemplateDao.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_adapter_conf.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_base_image.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_config_files.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_config_history_files.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_config_references.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_docker_registry.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_framework_key.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_group_priority.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_machine_tars_info.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_node_info.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_profile_template.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_registry_info.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_server_conf.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_server_group_relation.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_server_group_rule.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_server_notifys.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_server_patchs.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_task.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_task_item.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_models/t_web_release_conf.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_web_models/t_application.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_web_models/t_business.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_web_models/t_business_application_relation.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_web_models/t_patch_task.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/db_tars_web_models/t_plugin.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/index.js", "downloaded_repos/TarsCloud_TarsWeb/app/dao/db/update.js", "downloaded_repos/TarsCloud_TarsWeb/app/index.js", "downloaded_repos/TarsCloud_TarsWeb/app/init/tars.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/adapter/AdapterService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/admin/AdminService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/application/ApplicationService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/auth/AuthService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/business/BusinessService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/businessRelation/BusinessRelationService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/config/ConfigService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/expand/ExpandService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/idc/IDCService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/image/ImageService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/logview/LogviewService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/monitor/MonitorPropertyService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/monitor/MonitorStatService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/notify/NotifyService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/patch/CompileService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/patch/PatchService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/ResourceService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_connect.sh", "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_install.sh", "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_uninstall.sh", "downloaded_repos/TarsCloud_TarsWeb/app/service/server/ServerService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/server/TreeService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/task/KafkaConsumer.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/task/KafkaProducer.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/task/TaskService.js", "downloaded_repos/TarsCloud_TarsWeb/app/service/template/TemplateService.js", "downloaded_repos/TarsCloud_TarsWeb/app.js", "downloaded_repos/TarsCloud_TarsWeb/bin/www", "downloaded_repos/TarsCloud_TarsWeb/client/.browserslistrc", "downloaded_repos/TarsCloud_TarsWeb/client/.postcssrc.js", "downloaded_repos/TarsCloud_TarsWeb/client/README.md", "downloaded_repos/TarsCloud_TarsWeb/client/README.zh.md", "downloaded_repos/TarsCloud_TarsWeb/client/babel.config.js", "downloaded_repos/TarsCloud_TarsWeb/client/package.json", "downloaded_repos/TarsCloud_TarsWeb/client/public/index.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/api/ajax.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/api/fetch.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/api/util.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/css/let-ui.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/css/reset.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/css/variable.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/.font-spider/Lato-Bold.ttf", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/.font-spider/Lato-Light.ttf", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/.font-spider/Lato-Regular.ttf", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Bold.eot", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Bold.svg", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Bold.ttf", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Bold.woff", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Bold.woff2", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Light.eot", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Light.svg", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Light.ttf", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Light.woff", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Light.woff2", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Regular.eot", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Regular.svg", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Regular.ttf", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Regular.woff", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato-Regular.woff2", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/Lato.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/font/lato/index.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/icon/default.svg", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/icon/icon_active.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/icon/icon_inactive.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/icon/spinner.svg", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/icon/zoom-in.svg", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/cache-g.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/cache-l.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/create-g.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/create-l.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/dcache-logo.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/focus-bg.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/opa-icon.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/operator-g.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/operator-l.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/package-g.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/package-l.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/package.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/server-icon.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/tars-logo.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/img/tree-icon-2.png", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/alert.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/aside.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/autocomplete.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/avatar.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/backtop.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/badge.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/base.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/breadcrumb-item.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/breadcrumb.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/button-group.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/button.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/calendar.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/card.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/carousel-item.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/carousel.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/cascader-panel.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/cascader.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/checkbox-button.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/checkbox-group.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/checkbox.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/col.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/collapse-item.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/collapse.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/color-picker.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/container.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/date-picker.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/dialog.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/display.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/divider.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/drawer.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/dropdown-item.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/dropdown-menu.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/dropdown.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/fonts/element-icons.ttf", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/fonts/element-icons.woff", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/footer.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/form-item.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/form.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/header.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/icon.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/image.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/index.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/infinite-scroll.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/infiniteScroll.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/input-number.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/input.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/link.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/loading.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/main.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/menu-item-group.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/menu-item.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/menu.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/message-box.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/message.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/notification.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/option-group.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/option.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/page-header.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/pagination.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/popconfirm.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/popover.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/popper.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/progress.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/radio-button.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/radio-group.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/radio.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/rate.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/reset.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/row.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/scrollbar.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/select-dropdown.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/select.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/slider.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/spinner.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/step.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/steps.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/submenu.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/switch.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/tab-pane.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/table-column.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/table.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/tabs.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/tag.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/time-picker.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/time-select.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/timeline-item.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/timeline.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/tooltip.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/transfer.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/tree.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/assets/theme/element-to-let/upload.css", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/callchain/all.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/callchain/gantt-chart.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/callchain/homeMain.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/callchain/opendiv.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/callchain.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/monitor-property.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/monitor-server.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/operationPlugins.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/plugins-manage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/plugins.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/serverPlugins.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/user-manage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/common/util.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/app/footer.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/app/header.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/app/k8s-header.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/app/sso-header.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/charts/compare-chart.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/diff/index.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/editor/yaml-editor.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/extention-select.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/hours-filter.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/icon.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/k8s-yaml-edit.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/locale-select.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/section-wrappper.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/set-inputer.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/tars-form-item.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/components/xterm.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/error.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/errorApp.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/index.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/indexApp.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/abilityAffinity.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/base.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/disk.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/hpa.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/index.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/install.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/jobModel.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/launcherType.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/networkMapping.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/pull.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/replicas.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/resource.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/k8s/stacked.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/inc/log.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/application.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/business.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/businessRelation.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/components/node-detail.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/deploy.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/event.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/frameworkConfig.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/history.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/image.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/index.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/node.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/templates.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/operation/undeploy.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/config.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/history.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/index.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/manage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/publish.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/serverHistory.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s/server/user-manage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8s.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/k8sApp.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/lib/ajax.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/lib/axios.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/lib/date.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/locale/i18n.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/logView.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/logView.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/market/market.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/inc/batchOperation.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/inc/status.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/inc/tarsReleaseProgress.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/application.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/business.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/businessRelation.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/check.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/deploy.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/expand.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/idc.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/image.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/index.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/nodes.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/operation/templates.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/config.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/history.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/index.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/manage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/pages/server/publish.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/plugins/ajax.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/plugins/charts.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/plugins/common.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/plugins/ui.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/router/index.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/router/k8s.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/adminPass/adminPass.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/adminPass/adminPass.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/adminPass/adminPass.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/App.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/auth.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/auth.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/pages/infoManage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/pages/setManage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/pages/tokenManage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/pages/userManage.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/auth/router/index.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/login/index.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/login/login.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/login/login.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/login/login.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/pass/pass.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/pass/pass.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/pass/pass.vue", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/register/register.html", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/register/register.js", "downloaded_repos/TarsCloud_TarsWeb/client/src/sso/register/register.vue", "downloaded_repos/TarsCloud_TarsWeb/client/static/favicon.ico", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/K8S.png", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/cpu_tuner.ico", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/current.gif", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/dcache-logo.png", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/new.gif", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/space.png", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/tars-logo.png", "downloaded_repos/TarsCloud_TarsWeb/client/static/img/tree-icon-2.png", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/blue/cloudjs.css", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.css", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/combobox.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/font/fontawesome-webfont.woff2", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/font-awesome.min.css", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/json2.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/localLogView.css", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/localLogView.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/message.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/wsd.css", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/font/fontawesome-webfont.woff", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/font/fontawesome-webfont.woff2", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/lib/utils.js", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/logview.html", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/stylesheets/bootstrap.min.css", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/stylesheets/style.css", "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/test.html", "downloaded_repos/TarsCloud_TarsWeb/client/static/market/index.html", "downloaded_repos/TarsCloud_TarsWeb/client/static/market/k8s.html", "downloaded_repos/TarsCloud_TarsWeb/client/store/store.js", "downloaded_repos/TarsCloud_TarsWeb/client/vue.config.js", "downloaded_repos/TarsCloud_TarsWeb/config/authConf.js", "downloaded_repos/TarsCloud_TarsWeb/config/compileConf.js", "downloaded_repos/TarsCloud_TarsWeb/config/dcacheConf.js", "downloaded_repos/TarsCloud_TarsWeb/config/hpaConfig.js", "downloaded_repos/TarsCloud_TarsWeb/config/k8s.conf", "downloaded_repos/TarsCloud_TarsWeb/config/loginConf.js", "downloaded_repos/TarsCloud_TarsWeb/config/tars-dev.conf", "downloaded_repos/TarsCloud_TarsWeb/config/tars-k8s.conf", "downloaded_repos/TarsCloud_TarsWeb/config/tars-remote.conf", "downloaded_repos/TarsCloud_TarsWeb/config/tars.conf", "downloaded_repos/TarsCloud_TarsWeb/config/webConf.js", "downloaded_repos/TarsCloud_TarsWeb/docker/Dockerfile", "downloaded_repos/TarsCloud_TarsWeb/docker/entrypoint.sh", "downloaded_repos/TarsCloud_TarsWeb/docs/TARS", "downloaded_repos/TarsCloud_TarsWeb/docs/TARS 用户体系模块+资源模块使用指引.md", "downloaded_repos/TarsCloud_TarsWeb/docs/api.md", "downloaded_repos/TarsCloud_TarsWeb/docs/images/i1.png", "downloaded_repos/TarsCloud_TarsWeb/docs/images/i2.png", "downloaded_repos/TarsCloud_TarsWeb/docs/images/i3.png", "downloaded_repos/TarsCloud_TarsWeb/docs/images/i4.png", "downloaded_repos/TarsCloud_TarsWeb/docs/接口调试使用文档.md", "downloaded_repos/TarsCloud_TarsWeb/execLog.js", "downloaded_repos/TarsCloud_TarsWeb/execPod.js", "downloaded_repos/TarsCloud_TarsWeb/execUpload.js", "downloaded_repos/TarsCloud_TarsWeb/execWebSocket.js", "downloaded_repos/TarsCloud_TarsWeb/index/controller/auth/AuthController.js", "downloaded_repos/TarsCloud_TarsWeb/index/controller/callchain/CallChainController.js", "downloaded_repos/TarsCloud_TarsWeb/index/controller/locale/LocaleController.js", "downloaded_repos/TarsCloud_TarsWeb/index/controller/login/LoginController.js", "downloaded_repos/TarsCloud_TarsWeb/index/controller/monitor/MonitorController.js", "downloaded_repos/TarsCloud_TarsWeb/index/controller/page/PageController.js", "downloaded_repos/TarsCloud_TarsWeb/index/index.js", "downloaded_repos/TarsCloud_TarsWeb/index/json/cn.json", "downloaded_repos/TarsCloud_TarsWeb/index/json/en.json", "downloaded_repos/TarsCloud_TarsWeb/index/service/monitor/MonitorPropertyService.js", "downloaded_repos/TarsCloud_TarsWeb/index/service/monitor/MonitorStatService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/adapter/AdapterController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/application/ApplicationController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/auth/AuthController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/business/BusinessController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/config/ConfigController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/default/DefaultController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/deploy/DeployController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/event/EventController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/framework/FrameworkController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/hpa/HPAController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/image/ImageController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/k8s/K8sController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/login/LoginController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/node/NodeController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/notify/NotifyController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/page/PageController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/patch/PatchController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/pod/PodController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/server/ServerController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/template/TemplateController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/controller/tree/TreeController.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/index.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/adapter/AdapterService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/application/ApplicationService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/auth/AuthService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/business/BusinessService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/common/CommonService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/config/ConfigService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/deploy/DeployService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/event/EventService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/framework/FrameworkService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/hpa/HPAService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/image/ImageService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/k8s/K8sService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/node/NodeService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/patch/PatchService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/pod/PodService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/server/ServerService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/template/TemplateService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s/service/tree/TreeService.js", "downloaded_repos/TarsCloud_TarsWeb/k8s-debug.yaml", "downloaded_repos/TarsCloud_TarsWeb/ldap/CronService.js", "downloaded_repos/TarsCloud_TarsWeb/ldap/LdapService.js", "downloaded_repos/TarsCloud_TarsWeb/logger/index.js", "downloaded_repos/TarsCloud_TarsWeb/market/controller/MarketController.js", "downloaded_repos/TarsCloud_TarsWeb/market/index.js", "downloaded_repos/TarsCloud_TarsWeb/market/service/MarketK8SService.js", "downloaded_repos/TarsCloud_TarsWeb/market/service/MarketService.js", "downloaded_repos/TarsCloud_TarsWeb/midware/authMidware.js", "downloaded_repos/TarsCloud_TarsWeb/midware/index.js", "downloaded_repos/TarsCloud_TarsWeb/midware/limitMidware.js", "downloaded_repos/TarsCloud_TarsWeb/midware/localeMidware.js", "downloaded_repos/TarsCloud_TarsWeb/midware/noCacheMidware.js", "downloaded_repos/TarsCloud_TarsWeb/midware/paramsMidware.js", "downloaded_repos/TarsCloud_TarsWeb/midware/ssoMidware.js", "downloaded_repos/TarsCloud_TarsWeb/package.json", "downloaded_repos/TarsCloud_TarsWeb/plugin/controller/PluginController.js", "downloaded_repos/TarsCloud_TarsWeb/plugin/index.js", "downloaded_repos/TarsCloud_TarsWeb/plugin/service/PluginK8SService.js", "downloaded_repos/TarsCloud_TarsWeb/plugin/service/PluginService.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/index.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/k8s.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/AdminReg.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/AdminRegProxy.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/ConfigF.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/ConfigFProxy.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/MonitorQuery.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/MonitorQueryProxy.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/NodeDescriptor.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/NodeDescriptorTars.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/Patch.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/PatchProxy.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/create.sh", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/getservant/lib/EndpointF.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/getservant/lib/EndpointFTars.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/getservant/lib/QueryF.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/getservant/lib/QueryFProxy.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/getservant/lib/config.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/getservant/lib/create.sh", "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/getservant/lib/getEndpoint.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/service.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/struct.js", "downloaded_repos/TarsCloud_TarsWeb/rpc/topology/Topology.tars", "downloaded_repos/TarsCloud_TarsWeb/rpc/topology/TopologyProxy.js", "downloaded_repos/TarsCloud_TarsWeb/sql/db_base.sql", "downloaded_repos/TarsCloud_TarsWeb/sql/db_cache_web.sql", "downloaded_repos/TarsCloud_TarsWeb/sql/db_tars_web.sql", "downloaded_repos/TarsCloud_TarsWeb/sql/db_user_system.sql", "downloaded_repos/TarsCloud_TarsWeb/sso/controller/auth/AuthController.js", "downloaded_repos/TarsCloud_TarsWeb/sso/controller/ldap/LdapController.js", "downloaded_repos/TarsCloud_TarsWeb/sso/controller/login/LoginController.js", "downloaded_repos/TarsCloud_TarsWeb/sso/controller/set/SetController.js", "downloaded_repos/TarsCloud_TarsWeb/sso/controller/token/TokenController.js", "downloaded_repos/TarsCloud_TarsWeb/sso/controller/user/UserController.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/AuthDao.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/LoginDao.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/SetDao.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/TokenDao.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/UserDao.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/db/db_user_system_models/t_auth.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/db/db_user_system_models/t_login_temp_info.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/db/db_user_system_models/t_setting.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/db/db_user_system_models/t_token.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/db/db_user_system_models/t_user_info.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/db/index.js", "downloaded_repos/TarsCloud_TarsWeb/sso/dao/db/update.js", "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/auth/AuthService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/login/LoginService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/set/SetService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/token/TokenService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/db-service/user/UserService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/index.js", "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/auth/AuthService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/login/LoginService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/set/SetService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/set/update.js", "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/token/TokenService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/k8s-service/user/UserService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/service/auth/AuthService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/service/login/LoginService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/service/set/SetService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/service/token/TokenService.js", "downloaded_repos/TarsCloud_TarsWeb/sso/service/user/UserService.js", "downloaded_repos/TarsCloud_TarsWeb/tools/util.js"], "skipped": [{"path": "downloaded_repos/TarsCloud_TarsWeb/.github/workflows/tarsweb.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/app/service/resource/tarsnode_uninstall.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/adminPass.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/auth.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/error.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/k8s.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/login.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/logview.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/pass.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/adminPass.f536fb93.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/auth.3fbe7593.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/chunk-common.7202a68f.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/chunk-vendors.186e0277.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/index.4ddbcab3.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/k8s.cb162fd9.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/logView.757ba434.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/login.af8ef18e.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/css/pass.26f06ac6.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/favicon.ico", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Bold.669c66bf.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Bold.a09a0562.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Bold.b9acd01a.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Bold.d4b71a2a.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Light.c91b75fb.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Light.cb62f955.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Light.f7fd5b09.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Light.f96b03f9.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Regular.13f766c6.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Regular.5db8b689.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Regular.5ddccc61.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/Lato-Regular.eb924c6f.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/element-icons.f1a45d74.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/fonts/element-icons.ff18efd1.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/K8S.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/Lato-Bold.5a66ec8c.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/Lato-Light.dc0e102e.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/Lato-Regular.4c8616a0.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/cpu_tuner.ico", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/current.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/dcache-logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/iconfont.0dc6059d.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/new.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/space.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/tars-logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/tree-icon-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/img/zoom-in.93460806.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/adminPass.89634711.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/auth.ade08c03.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/chunk-common.9c593749.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/chunk-vendors.90f8ffd4.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/error.7e5eb209.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/index.1719291f.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/k8s.e11ecf7e.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/logView.e97502f9.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/login.33911a92.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/js/pass.cfc8c39c.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/blue/cloudjs.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/cloudjs.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/cloudjs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/combobox.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/font/fontawesome-webfont.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/font-awesome.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/json2.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/localLogView.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/localLogView.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/message.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/resizable.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/Logview_files/wsd.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/font/fontawesome-webfont.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/font/fontawesome-webfont.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/javascripts/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/javascripts/jquery.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/javascripts/lib/utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/logview.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/stylesheets/bootstrap.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/stylesheets/style.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/logview/test.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/market/axios.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/market/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/market/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/static/market/k8s.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/dist/tars.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/package-lock.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/public/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/static/market/axios.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TarsCloud_TarsWeb/client/static/market/jquery.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8351149559020996, "profiling_times": {"config_time": 6.0977396965026855, "core_time": 10.683761358261108, "ignores_time": 0.0017437934875488281, "total_time": 16.784425497055054}, "parsing_time": {"total_time": 12.954060792922974, "per_file_time": {"mean": 0.04780096233550913, "std_dev": 0.00968204201057312}, "very_slow_stats": {"time_ratio": 0.203197878633071, "count_ratio": 0.014760147601476014}, "very_slow_files": [{"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/combobox.js", "ftime": 0.31537389755249023}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "ftime": 0.3755779266357422}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "ftime": 0.692490816116333}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/AdminRegProxy.js", "ftime": 1.2487950325012207}]}, "scanning_time": {"total_time": 80.70472979545593, "per_file_time": {"mean": 0.057564001280639036, "std_dev": 0.10386048973973049}, "very_slow_stats": {"time_ratio": 0.3584344688558518, "count_ratio": 0.005706134094151213}, "very_slow_files": [{"fpath": "downloaded_repos/TarsCloud_TarsWeb/app/controller/patch/PatchController.js", "ftime": 1.5239121913909912}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/k8s/service/common/CommonService.js", "ftime": 1.700812816619873}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/app/controller/server/ServerController.js", "ftime": 2.3593451976776123}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/combobox.js", "ftime": 3.1476809978485107}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/AdminRegProxy.js", "ftime": 3.7076339721679688}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/javascripts/jquery.js", "ftime": 5.089096784591675}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "ftime": 5.2348480224609375}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "ftime": 6.164026975631714}]}, "matching_time": {"total_time": 34.216532468795776, "per_file_and_rule_time": {"mean": 0.017619223722345922, "std_dev": 0.0028567220140838117}, "very_slow_stats": {"time_ratio": 0.4161507636518973, "count_ratio": 0.032955715756951595}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.32662200927734375}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.3443942070007324}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.36388492584228516}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/app/controller/server/ServerController.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.3814990520477295}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.42610812187194824}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/app/controller/server/ServerController.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.4350290298461914}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/combobox.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.4740128517150879}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/AdminRegProxy.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.78578782081604}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/AdminRegProxy.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.0169999599456787}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.059664011001587}]}, "tainting_time": {"total_time": 16.491233348846436, "per_def_and_rule_time": {"mean": 0.00818829858433289, "std_dev": 0.000590993050717873}, "very_slow_stats": {"time_ratio": 0.32870286022779543, "count_ratio": 0.020854021847070508}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/TarsCloud_TarsWeb/midware/paramsMidware.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.19690608978271484}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/rpc/proxy/AdminRegProxy.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.19954204559326172}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.20033693313598633}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "fline": 401, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.21831893920898438}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.23107504844665527}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.23391389846801758}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.2404191493988037}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.26343393325805664}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/cloudjs.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.3209540843963623}, {"fpath": "downloaded_repos/TarsCloud_TarsWeb/client/static/logview/Logview_files/resizable.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.5720469951629639}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1092000256}, "engine_requested": "OSS", "skipped_rules": []}