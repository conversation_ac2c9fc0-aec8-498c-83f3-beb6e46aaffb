{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "start": {"line": 19, "col": 5, "offset": 480}, "end": {"line": 19, "col": 95, "offset": 570}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "start": {"line": 20, "col": 5, "offset": 575}, "end": {"line": 20, "col": 89, "offset": 659}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "start": {"line": 21, "col": 5, "offset": 664}, "end": {"line": 21, "col": 90, "offset": 749}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/corbado_passkeys-python-django/project/templates/base.html:3:\n `{% load static %}` was unexpected", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "spans": [{"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html:1:\n `{% extends 'base.html' %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html", "spans": [{"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html", "start": {"line": 17, "col": 1, "offset": 0}, "end": {"line": 17, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html:1:\n `{% extends 'base.html' %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html", "spans": [{"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html", "start": {"line": 17, "col": 1, "offset": 0}, "end": {"line": 17, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html:1:\n `{% extends 'base.html' %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html", "spans": [{"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html:1:\n `{% extends 'base.html' %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html", "spans": [{"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html:1:\n `{% extends 'base.html' %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html", "spans": [{"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 46}}, {"file": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/corbado_passkeys-python-django/.env.example", "downloaded_repos/corbado_passkeys-python-django/.gitignore", "downloaded_repos/corbado_passkeys-python-django/README.md", "downloaded_repos/corbado_passkeys-python-django/__init__.py", "downloaded_repos/corbado_passkeys-python-django/main/__init__.py", "downloaded_repos/corbado_passkeys-python-django/main/admin.py", "downloaded_repos/corbado_passkeys-python-django/main/apps.py", "downloaded_repos/corbado_passkeys-python-django/main/decorators.py", "downloaded_repos/corbado_passkeys-python-django/main/migrations/0001_initial.py", "downloaded_repos/corbado_passkeys-python-django/main/migrations/0002_alter_user_city.py", "downloaded_repos/corbado_passkeys-python-django/main/migrations/__init__.py", "downloaded_repos/corbado_passkeys-python-django/main/mixins.py", "downloaded_repos/corbado_passkeys-python-django/main/models.py", "downloaded_repos/corbado_passkeys-python-django/main/tests.py", "downloaded_repos/corbado_passkeys-python-django/main/urls.py", "downloaded_repos/corbado_passkeys-python-django/main/views.py", "downloaded_repos/corbado_passkeys-python-django/manage.py", "downloaded_repos/corbado_passkeys-python-django/project/__init__.py", "downloaded_repos/corbado_passkeys-python-django/project/asgi.py", "downloaded_repos/corbado_passkeys-python-django/project/context_processors.py", "downloaded_repos/corbado_passkeys-python-django/project/middleware.py", "downloaded_repos/corbado_passkeys-python-django/project/settings.py", "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/onboarding.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/profile.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_authenticated.html", "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html", "downloaded_repos/corbado_passkeys-python-django/project/urls.py", "downloaded_repos/corbado_passkeys-python-django/project/utils/__init__.py", "downloaded_repos/corbado_passkeys-python-django/project/utils/authentication.py", "downloaded_repos/corbado_passkeys-python-django/project/wsgi.py", "downloaded_repos/corbado_passkeys-python-django/requirements.txt"], "skipped": [{"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_authenticated.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/home_guest.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/signup.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/corbado_passkeys-python-django/project/templates/main/userarea_guest.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.1263680458068848, "profiling_times": {"config_time": 6.622554779052734, "core_time": 3.2430341243743896, "ignores_time": 0.0025954246520996094, "total_time": 9.869789838790894}, "parsing_time": {"total_time": 0.457489013671875, "per_file_time": {"mean": 0.015775483230064655, "std_dev": 0.00021372266497074414}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.5497140884399414, "per_file_time": {"mean": 0.02451648161961482, "std_dev": 0.0024486017329078363}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.2728919982910156, "per_file_and_rule_time": {"mean": 0.0018192799886067714, "std_dev": 1.937311124173801e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.05004382133483887, "per_def_and_rule_time": {"mean": 0.0002943754196166992, "std_dev": 6.941218335628839e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}