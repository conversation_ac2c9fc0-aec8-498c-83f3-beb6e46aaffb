{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.xss.template-autoescape-off.template-autoescape-off", "path": "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/templates/djangocms_page_meta/meta.html", "start": {"line": 3, "col": 2, "offset": 37}, "end": {"line": 3, "col": 22, "offset": 57}, "extra": {"message": "Detected a template block where autoescaping is explicitly disabled with '{% autoescape off %}'. This allows rendering of raw HTML in this segment. Turn autoescaping on to prevent cross-site scripting (XSS). If you must do this, consider instead, using `mark_safe` in Python code.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/ref/templates/builtins/#autoescape"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-autoescape-off.template-autoescape-off", "shortlink": "https://sg.run/Q5WZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/utils.py", "start": {"line": 153, "col": 12, "offset": 6676}, "end": {"line": 155, "col": 6, "offset": 6805}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/lint.yml", "start": {"line": 41, "col": 19, "offset": 1422}, "end": {"line": 41, "col": 22, "offset": 1425}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-page-meta/.github/workflows/lint.yml:41:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/lint.yml", "spans": [{"file": "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/lint.yml", "start": {"line": 41, "col": 19, "offset": 1422}, "end": {"line": 41, "col": 22, "offset": 1425}}]}], "paths": {"scanned": ["downloaded_repos/nephila_djangocms-page-meta/.checkignore", "downloaded_repos/nephila_djangocms-page-meta/.codeclimate.yml", "downloaded_repos/nephila_djangocms-page-meta/.coveragerc", "downloaded_repos/nephila_djangocms-page-meta/.csslintrc", "downloaded_repos/nephila_djangocms-page-meta/.editorconfig", "downloaded_repos/nephila_djangocms-page-meta/.github/ISSUE_TEMPLATE/---bug-report.md", "downloaded_repos/nephila_djangocms-page-meta/.github/ISSUE_TEMPLATE/---feature-request.md", "downloaded_repos/nephila_djangocms-page-meta/.github/dependabot.yml", "downloaded_repos/nephila_djangocms-page-meta/.github/pull_request_template.md", "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/codeql.yml", "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/lint.yml", "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/logger.yml", "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/publish.yml", "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/test.yml", "downloaded_repos/nephila_djangocms-page-meta/.gitignore", "downloaded_repos/nephila_djangocms-page-meta/.pre-commit-config.yaml", "downloaded_repos/nephila_djangocms-page-meta/.pyup.yml", "downloaded_repos/nephila_djangocms-page-meta/.readthedocs.yml", "downloaded_repos/nephila_djangocms-page-meta/.tx/config", "downloaded_repos/nephila_djangocms-page-meta/AUTHORS.rst", "downloaded_repos/nephila_djangocms-page-meta/CONTRIBUTING.rst", "downloaded_repos/nephila_djangocms-page-meta/HISTORY.rst", "downloaded_repos/nephila_djangocms-page-meta/LICENSE", "downloaded_repos/nephila_djangocms-page-meta/MANIFEST.in", "downloaded_repos/nephila_djangocms-page-meta/README.rst", "downloaded_repos/nephila_djangocms-page-meta/aldryn_config.py", "downloaded_repos/nephila_djangocms-page-meta/changes/.directory", "downloaded_repos/nephila_djangocms-page-meta/cms_helper.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/__init__.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/addon.json", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/admin.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/apps.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/cms_toolbars.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/forms.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/ar/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/ar/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/bg/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/bg/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/de/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/en/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/es/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/es/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/it/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/lt/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/lt/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/pt_BR/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/pt_BR/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/tr/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/locale/tr/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0001_initial.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0002_auto_20150807_0936.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0003_auto_20151220_1734.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0004_auto_20160409_1852.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0005_pagemeta_fb_pages.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0006_auto_20160423_1859.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0007_auto_20160530_2257.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0008_auto_20160609_0754.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0009_auto_20171230_1954.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0010_auto_20180108_2316.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0011_auto_20190218_1010.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0012_auto_20200706_1230.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0013_auto_20200821_1457.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0014_pagemeta_robots_alter_genericmetaattribute_id_and_more.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0015_defaultmetaimage.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/0016_auto_20230830_1007.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/migrations/__init__.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/models.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/settings.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/static/djangocms_page_meta/css/djangocms_page_meta_admin.css", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/templates/djangocms_page_meta/meta.html", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/templatetags/__init__.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/templatetags/page_meta_tags.py", "downloaded_repos/nephila_djangocms-page-meta/djangocms_page_meta/utils.py", "downloaded_repos/nephila_djangocms-page-meta/docs/Makefile", "downloaded_repos/nephila_djangocms-page-meta/docs/authors.rst", "downloaded_repos/nephila_djangocms-page-meta/docs/autodoc.rst", "downloaded_repos/nephila_djangocms-page-meta/docs/conf.py", "downloaded_repos/nephila_djangocms-page-meta/docs/configuration.rst", "downloaded_repos/nephila_djangocms-page-meta/docs/contributing.rst", "downloaded_repos/nephila_djangocms-page-meta/docs/history.rst", "downloaded_repos/nephila_djangocms-page-meta/docs/index.rst", "downloaded_repos/nephila_djangocms-page-meta/docs/make.bat", "downloaded_repos/nephila_djangocms-page-meta/docs/readme.rst", "downloaded_repos/nephila_djangocms-page-meta/docs/usage.rst", "downloaded_repos/nephila_djangocms-page-meta/pyproject.toml", "downloaded_repos/nephila_djangocms-page-meta/requirements-test.txt", "downloaded_repos/nephila_djangocms-page-meta/requirements.txt", "downloaded_repos/nephila_djangocms-page-meta/setup.cfg", "downloaded_repos/nephila_djangocms-page-meta/setup.py", "downloaded_repos/nephila_djangocms-page-meta/tasks.py", "downloaded_repos/nephila_djangocms-page-meta/tox.ini"], "skipped": [{"path": "downloaded_repos/nephila_djangocms-page-meta/.github/workflows/lint.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/test_adminpage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/test_general.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/test_templatetags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/test_toolbar.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/test_utils/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/test_utils/templates/page_meta.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-page-meta/tests/test_utils/urls.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7145631313323975, "profiling_times": {"config_time": 6.08280611038208, "core_time": 2.882272243499756, "ignores_time": 0.0018913745880126953, "total_time": 8.967860221862793}, "parsing_time": {"total_time": 0.3620026111602783, "per_file_time": {"mean": 0.008418665375820427, "std_dev": 0.00011235642095952922}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.087965726852417, "per_file_time": {"mean": 0.013084600537510234, "std_dev": 0.0011918365575377707}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7271559238433838, "per_file_and_rule_time": {"mean": 0.0015537519740243247, "std_dev": 2.4800822551130554e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.15936732292175293, "per_def_and_rule_time": {"mean": 0.0007661890525084276, "std_dev": 6.3125125438605105e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}