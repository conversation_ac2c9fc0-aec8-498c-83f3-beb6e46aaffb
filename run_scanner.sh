#!/bin/bash

# Unified Vulnerability Scanner - Quick Start Script
# ==================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
MAX_REPOS=25
PAGES=2
MIN_STARS=50
MAX_STARS=2000
CONCURRENT=2
USE_DOCKER=true
BUILD_ONLY=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Unified Vulnerability Scanner - Quick Start"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -r, --max-repos NUM     Maximum repositories to analyze (default: 25)"
    echo "  -p, --pages NUM         Search pages per query (default: 2)"
    echo "  -s, --min-stars NUM     Minimum star count (default: 50)"
    echo "  -S, --max-stars NUM     Maximum star count (default: 2000)"
    echo "  -c, --concurrent NUM    Max concurrent scans (default: 2)"
    echo "  -n, --no-docker         Don't use Docker (run directly)"
    echo "  -b, --build-only        Only build Docker image"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  GITHUB_TOKEN            Required: GitHub API token"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Quick scan (25 repos)"
    echo "  $0 -r 50 -p 3                       # Comprehensive scan"
    echo "  $0 -n                                # Run without Docker"
    echo "  $0 -b                                # Build Docker image only"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--max-repos)
            MAX_REPOS="$2"
            shift 2
            ;;
        -p|--pages)
            PAGES="$2"
            shift 2
            ;;
        -s|--min-stars)
            MIN_STARS="$2"
            shift 2
            ;;
        -S|--max-stars)
            MAX_STARS="$2"
            shift 2
            ;;
        -c|--concurrent)
            CONCURRENT="$2"
            shift 2
            ;;
        -n|--no-docker)
            USE_DOCKER=false
            shift
            ;;
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Banner
echo "🎯 UNIFIED VULNERABILITY SCANNER"
echo "=================================="
echo ""

# Check prerequisites
print_status "Checking prerequisites..."

# Check if GitHub token is set
if [ -z "$GITHUB_TOKEN" ]; then
    print_error "GITHUB_TOKEN environment variable is not set"
    echo "Please set it with: export GITHUB_TOKEN='your_token_here'"
    exit 1
fi
print_success "GitHub token is set"

# Check Docker if needed
if [ "$USE_DOCKER" = true ]; then
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        echo "Please install Docker or use --no-docker flag"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        echo "Please start Docker daemon"
        exit 1
    fi
    print_success "Docker is available"
fi

# Check Python if not using Docker
if [ "$USE_DOCKER" = false ]; then
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    print_success "Python 3 is available"
    
    # Check if requirements are installed
    if ! python3 -c "import requests, langdetect" &> /dev/null; then
        print_warning "Installing Python requirements..."
        pip3 install -r requirements.txt
    fi
fi

# Create output directory
mkdir -p output
print_success "Output directory created"

# Run the scanner
if [ "$USE_DOCKER" = true ]; then
    print_status "Running with Docker..."
    
    # Build and run using Python script
    python3 run_docker_scanner.py \
        --max-repos "$MAX_REPOS" \
        --pages "$PAGES" \
        --min-stars "$MIN_STARS" \
        --max-stars "$MAX_STARS" \
        --concurrent "$CONCURRENT" \
        $([ "$BUILD_ONLY" = true ] && echo "--build-only")
else
    print_status "Running directly with Python..."
    
    python3 unified_vulnerability_scanner.py \
        --max-repos "$MAX_REPOS" \
        --pages "$PAGES" \
        --min-stars "$MIN_STARS" \
        --max-stars "$MAX_STARS" \
        --concurrent "$CONCURRENT" \
        --no-docker
fi

# Show results if not build-only
if [ "$BUILD_ONLY" = false ]; then
    echo ""
    print_status "Scan completed! Results:"
    
    if [ -f "output/high_priority_unified_results.csv" ]; then
        HIGH_PRIORITY_COUNT=$(tail -n +2 "output/high_priority_unified_results.csv" | wc -l)
        print_success "High-priority targets: $HIGH_PRIORITY_COUNT"
    fi
    
    if [ -f "output/unified_vulnerability_results.csv" ]; then
        TOTAL_COUNT=$(tail -n +2 "output/unified_vulnerability_results.csv" | wc -l)
        print_success "Total repositories analyzed: $TOTAL_COUNT"
    fi
    
    echo ""
    echo "📁 Output files:"
    ls -la output/*.csv 2>/dev/null || true
    ls -la output/*.json 2>/dev/null || true
    
    echo ""
    print_success "Scan complete! Check the output/ directory for results."
    echo ""
    echo "🎯 Next steps:"
    echo "1. Review high_priority_unified_results.csv for immediate targets"
    echo "2. Check semgrep_results/ for detailed vulnerability analysis"
    echo "3. Cross-reference findings across multiple files"
    echo "4. Follow responsible disclosure practices"
fi
