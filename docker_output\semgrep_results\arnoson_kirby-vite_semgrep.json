{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/arnoson_kirby-vite/.gitattributes", "downloaded_repos/a<PERSON>son_kirby-vite/.github/logo-dark.svg", "downloaded_repos/a<PERSON>son_kirby-vite/.github/logo-light.svg", "downloaded_repos/a<PERSON>son_kirby-vite/.github/logo.svg", "downloaded_repos/a<PERSON>son_kirby-vite/.github/workflows/ci.yml", "downloaded_repos/arnoson_kirby-vite/.github/workflows/release.yml", "downloaded_repos/arnoson_kirby-vite/.gitignore", "downloaded_repos/arnoson_kirby-vite/.prettierignore", "downloaded_repos/arnoson_kirby-vite/.prettierrc", "downloaded_repos/a<PERSON>son_kirby-vite/.vscode/settings.json", "downloaded_repos/arnoson_kirby-vite/LICENSE.md", "downloaded_repos/arnoson_kirby-vite/README.md", "downloaded_repos/arnoson_kirby-vite/bump.config.ts", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/composer.json", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/composer.lock", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/.editorconfig", "downloaded_repos/arnoson_kirby-vite/example/.gitignore", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/.htaccess", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/composer.json", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/composer.lock", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/content/error/error.txt", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/content/home/<USER>", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/content/site.txt", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/index.php", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/media/index.html", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/package-lock.json", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/package.json", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/accounts/index.html", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/blueprints/pages/default.yml", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/blueprints/site.yml", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/cache/index.html", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/config/config.php", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/plugins/kirby-vite/index.php", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/sessions/index.html", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/site/templates/default.php", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/src/index.css", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/src/index.js", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/src/panel.css", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/src/panel.js", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/example/vite.config.js", "downloaded_repos/arnoson_kirby-vite/index.php", "downloaded_repos/a<PERSON>son_kirby-vite/package-lock.json", "downloaded_repos/a<PERSON>son_kirby-vite/package.json", "downloaded_repos/arnoson_kirby-vite/packages/kirby-vite/.gitignore", "downloaded_repos/a<PERSON>son_kirby-vite/packages/kirby-vite/Vite.php", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/packages/kirby-vite/composer.json", "downloaded_repos/a<PERSON><PERSON>_kirby-vite/packages/kirby-vite/composer.lock", "downloaded_repos/a<PERSON>son_kirby-vite/packages/kirby-vite/phpunit.xml", "downloaded_repos/arnoson_kirby-vite/packages/vite-plugin-kirby/.gitignore", "downloaded_repos/arnoson_kirby-vite/packages/vite-plugin-kirby/.prettierrc", "downloaded_repos/a<PERSON>son_kirby-vite/packages/vite-plugin-kirby/package-lock.json", "downloaded_repos/a<PERSON>son_kirby-vite/packages/vite-plugin-kirby/package.json", "downloaded_repos/a<PERSON>son_kirby-vite/packages/vite-plugin-kirby/src/index.ts", "downloaded_repos/a<PERSON>son_kirby-vite/packages/vite-plugin-kirby/tsconfig.json", "downloaded_repos/a<PERSON>son_kirby-vite/packages/vite-plugin-kirby/vite.config.ts"], "skipped": [{"path": "downloaded_repos/a<PERSON>son_kirby-vite/packages/kirby-vite/test/ViteTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/a<PERSON>son_kirby-vite/packages/kirby-vite/test/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/a<PERSON>son_kirby-vite/packages/kirby-vite/test/config/config.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/a<PERSON>son_kirby-vite/packages/kirby-vite/test/config/vite.config.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/a<PERSON>son_kirby-vite/packages/kirby-vite/test/public/dist/.vite/manifest.json", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6517651081085205, "profiling_times": {"config_time": 6.273658275604248, "core_time": 2.4555468559265137, "ignores_time": 0.0024683475494384766, "total_time": 8.732416152954102}, "parsing_time": {"total_time": 0.45310115814208984, "per_file_time": {"mean": 0.016781524375632958, "std_dev": 0.0005845083470184138}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.4969913959503174, "per_file_time": {"mean": 0.010616960254966787, "std_dev": 0.0006587678145310495}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.24770474433898926, "per_file_and_rule_time": {"mean": 0.001383825387368655, "std_dev": 1.0529769387004784e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.01109623908996582, "per_def_and_rule_time": {"mean": 0.0011096239089965821, "std_dev": 3.360411681683217e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}