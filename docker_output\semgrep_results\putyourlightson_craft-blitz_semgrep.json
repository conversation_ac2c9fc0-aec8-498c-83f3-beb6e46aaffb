{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/putyourlightson_craft-blitz/src/resources/js/blitzInjectScript.js", "start": {"line": 69, "col": 17, "offset": 3122}, "end": {"line": 69, "col": 131, "offset": 3236}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/putyourlightson_craft-blitz/src/resources/js/blitzInjectScript.js", "start": {"line": 72, "col": 17, "offset": 3286}, "end": {"line": 72, "col": 64, "offset": 3333}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/putyourlightson_craft-blitz/src/resources/js/blitzInjectScript.ts", "start": {"line": 84, "col": 13, "offset": 2428}, "end": {"line": 84, "col": 90, "offset": 2505}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/putyourlightson_craft-blitz/src/resources/js/blitzInjectScript.ts", "start": {"line": 86, "col": 13, "offset": 2535}, "end": {"line": 86, "col": 60, "offset": 2582}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/putyourlightson_craft-blitz/src/rewrite.php", "start": {"line": 85, "col": 14, "offset": 2700}, "end": {"line": 85, "col": 19, "offset": 2705}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/putyourlightson_craft-blitz/src/rewrite.php", "start": {"line": 89, "col": 24, "offset": 2748}, "end": {"line": 89, "col": 29, "offset": 2753}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/putyourlightson_craft-blitz/.gitattributes", "downloaded_repos/putyourlightson_craft-blitz/.github/ISSUE_TEMPLATE/bug-report.yaml", "downloaded_repos/putyourlightson_craft-blitz/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/putyourlightson_craft-blitz/.github/ISSUE_TEMPLATE/feature-request.yaml", "downloaded_repos/putyourlightson_craft-blitz/.github/ISSUE_TEMPLATE/support-request.yaml", "downloaded_repos/putyourlightson_craft-blitz/.github/workflows/code-analysis.yaml", "downloaded_repos/putyourlightson_craft-blitz/.github/workflows/create-release.yml", "downloaded_repos/putyourlightson_craft-blitz/.gitignore", "downloaded_repos/putyourlightson_craft-blitz/CHANGELOG.md", "downloaded_repos/putyourlightson_craft-blitz/LICENSE.md", "downloaded_repos/putyourlightson_craft-blitz/README.md", "downloaded_repos/putyourlightson_craft-blitz/composer.json", "downloaded_repos/putyourlightson_craft-blitz/ecs.php", "downloaded_repos/putyourlightson_craft-blitz/lib/craft/behaviors/CustomFieldBehavior.php", "downloaded_repos/putyourlightson_craft-blitz/lib/craft/bootstrap.php", "downloaded_repos/putyourlightson_craft-blitz/phpstan.neon", "downloaded_repos/putyourlightson_craft-blitz/src/Blitz.php", "downloaded_repos/putyourlightson_craft-blitz/src/assets/BlitzAsset.php", "downloaded_repos/putyourlightson_craft-blitz/src/batchers/SiteUriBatcher.php", "downloaded_repos/putyourlightson_craft-blitz/src/behaviors/BlitzCustomFieldBehavior.php", "downloaded_repos/putyourlightson_craft-blitz/src/behaviors/CloneBehavior.php", "downloaded_repos/putyourlightson_craft-blitz/src/behaviors/ElementChangedBehavior.php", "downloaded_repos/putyourlightson_craft-blitz/src/config.php", "downloaded_repos/putyourlightson_craft-blitz/src/console/controllers/CacheController.php", "downloaded_repos/putyourlightson_craft-blitz/src/controllers/CacheController.php", "downloaded_repos/putyourlightson_craft-blitz/src/controllers/CsrfController.php", "downloaded_repos/putyourlightson_craft-blitz/src/controllers/DiagnosticsController.php", "downloaded_repos/putyourlightson_craft-blitz/src/controllers/GeneratorController.php", "downloaded_repos/putyourlightson_craft-blitz/src/controllers/IncludeController.php", "downloaded_repos/putyourlightson_craft-blitz/src/controllers/SettingsController.php", "downloaded_repos/putyourlightson_craft-blitz/src/controllers/TestController.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/deployers/BaseDeployer.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/deployers/DeployerInterface.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/deployers/DeployerTrait.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/deployers/DummyDeployer.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/deployers/GitDeployer.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/generators/BaseCacheGenerator.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/generators/CacheGeneratorInterface.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/generators/CacheGeneratorTrait.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/generators/HttpGenerator.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/generators/LocalGenerator.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/generators/local-generator-script.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/integrations/BaseIntegration.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/integrations/CommerceIntegration.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/integrations/DatastarIntegration.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/integrations/FeedMeIntegration.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/integrations/IntegrationInterface.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/integrations/SeomaticIntegration.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/purgers/BaseCachePurger.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/purgers/CachePurgerInterface.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/purgers/CachePurgerTrait.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/purgers/CloudflarePurger.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/purgers/DummyPurger.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/storage/BaseCacheStorage.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/storage/CacheStorageInterface.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/storage/CacheStorageTrait.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/storage/DummyStorage.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/storage/FileStorage.php", "downloaded_repos/putyourlightson_craft-blitz/src/drivers/storage/YiiCacheStorage.php", "downloaded_repos/putyourlightson_craft-blitz/src/enums/HeaderEnum.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/RefreshCacheEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/RefreshCacheTagsEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/RefreshElementEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/RefreshSiteCacheEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/RegisterLiveStatusesEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/RegisterNonCacheableElementTypesEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/RegisterSourceIdAttributesEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/ResponseEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/SaveCacheEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/events/SiteUriEvent.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/BaseDriverHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/CacheGeneratorHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/CachePurgerHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/CacheStorageHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/DeployerHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/DiagnosticsHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/ElementQueryHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/ElementSidebarHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/ElementTypeHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/FieldHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/IntegrationHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/QueryStringHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/RefreshCacheHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/helpers/SiteUriHelper.php", "downloaded_repos/putyourlightson_craft-blitz/src/icon-mask.svg", "downloaded_repos/putyourlightson_craft-blitz/src/icon.svg", "downloaded_repos/putyourlightson_craft-blitz/src/jobs/DriverJob.php", "downloaded_repos/putyourlightson_craft-blitz/src/jobs/GenerateCacheJob.php", "downloaded_repos/putyourlightson_craft-blitz/src/jobs/RefreshCacheJob.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/Install.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/m240719_120000_remove_legacy_settings.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/m240731_120000_add_datecached_column.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/m240820_120000_add_fieldinstanceuid_column.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/m240826_120000_convert_cachecontrol_settings.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/m240905_120000_convert_enabled_settings.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/m250314_120000_remove_hints_table.php", "downloaded_repos/putyourlightson_craft-blitz/src/migrations/m250510_120000_increase_uri_column_length.php", "downloaded_repos/putyourlightson_craft-blitz/src/models/BaseDataModel.php", "downloaded_repos/putyourlightson_craft-blitz/src/models/CacheOptionsModel.php", "downloaded_repos/putyourlightson_craft-blitz/src/models/GenerateDataModel.php", "downloaded_repos/putyourlightson_craft-blitz/src/models/RefreshDataModel.php", "downloaded_repos/putyourlightson_craft-blitz/src/models/SettingsModel.php", "downloaded_repos/putyourlightson_craft-blitz/src/models/SiteUriModel.php", "downloaded_repos/putyourlightson_craft-blitz/src/models/VariableConfigModel.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/CacheRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/CacheTagRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/DriverDataRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementCacheRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementExpiryDateRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementFieldCacheRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementQueryAttributeRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementQueryCacheRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementQueryFieldRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementQueryRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/ElementQuerySourceRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/IncludeRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/records/SsiIncludeCacheRecord.php", "downloaded_repos/putyourlightson_craft-blitz/src/resources/css/cp.css", "downloaded_repos/putyourlightson_craft-blitz/src/resources/icons/archive.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/icons/diagnostics.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/icons/hints.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/icons/pages.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/icons/refresh.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/icons/target.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/images/putyourlightson-logo.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/images/race.svg", "downloaded_repos/putyourlightson_craft-blitz/src/resources/js/blitzInjectScript.js", "downloaded_repos/putyourlightson_craft-blitz/src/resources/js/blitzInjectScript.ts", "downloaded_repos/putyourlightson_craft-blitz/src/resources/js/cp.js", "downloaded_repos/putyourlightson_craft-blitz/src/rewrite.php", "downloaded_repos/putyourlightson_craft-blitz/src/services/CacheRequestService.php", "downloaded_repos/putyourlightson_craft-blitz/src/services/CacheTagsService.php", "downloaded_repos/putyourlightson_craft-blitz/src/services/ClearCacheService.php", "downloaded_repos/putyourlightson_craft-blitz/src/services/ExpireCacheService.php", "downloaded_repos/putyourlightson_craft-blitz/src/services/FlushCacheService.php", "downloaded_repos/putyourlightson_craft-blitz/src/services/GenerateCacheService.php", "downloaded_repos/putyourlightson_craft-blitz/src/services/RefreshCacheService.php", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/deployers/git/settings.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/generators/http/settings.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/generators/local/settings.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/purgers/cloudflare/settings.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/storage/file/settings.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/storage/file/utility.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/storage/file/widget.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_drivers/storage/yii-cache/settings.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_element-sidebar.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_includes/api-urls.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_includes/uri-pattern-info.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_includes/welcome/bottom.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_includes/welcome/step.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_includes/welcome/top.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_macros.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_settings.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/cache.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/actionPaths.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/actions.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/elementQueries.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/elements.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/includes.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/nestedElements.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/params.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/tags.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_components/uris.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_includes/copytext.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_includes/copytextarea.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_includes/footer.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/_includes/report.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/actions/action-path.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/actions/action.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/actions/element-query-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/actions/element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/actions/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/actions/nested-element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/element-queries/element-query-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/element-queries/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/element-queries/uris.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/elements/element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/elements/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/elements/nested-element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/elements/uris.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/includes/element-query-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/includes/element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/includes/include.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/includes/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/includes/nested-element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/action.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/base.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/element-queries.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/elements.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/include.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/page.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/param.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/layouts/tag.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/pages/element-query-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/pages/element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/pages/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/pages/nested-element-type.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/pages/page.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/params/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/params/param.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/report.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/tags/index.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_utilities/diagnostics/tags/tag.twig", "downloaded_repos/putyourlightson_craft-blitz/src/templates/_widget.twig", "downloaded_repos/putyourlightson_craft-blitz/src/utilities/CacheUtility.php", "downloaded_repos/putyourlightson_craft-blitz/src/utilities/DiagnosticsUtility.php", "downloaded_repos/putyourlightson_craft-blitz/src/variables/BlitzVariable.php", "downloaded_repos/putyourlightson_craft-blitz/src/widgets/CacheWidget.php", "downloaded_repos/putyourlightson_craft-blitz/tsconfig.json"], "skipped": [{"path": "downloaded_repos/putyourlightson_craft-blitz/src/test/templates/eager.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Architecture/ArchitectureTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Datasets/CacheStorageDrivers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Datasets/RefreshModes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Drivers/FileStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/BlitzVariableTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/CacheRequestTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/CacheStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/ExpireCacheTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/GenerateCacheTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/RefreshCacheTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/RefreshModeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/SettingsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Feature/SiteUriTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Helpers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Integration/CommerceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Integration/SeomaticTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Interface/WebResponseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/putyourlightson_craft-blitz/tests/TESTS.md", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6783478260040283, "profiling_times": {"config_time": 6.122335195541382, "core_time": 3.186502695083618, "ignores_time": 0.0017552375793457031, "total_time": 9.31160283088684}, "parsing_time": {"total_time": 1.4950244426727295, "per_file_time": {"mean": 0.01196019554138184, "std_dev": 0.000669409481710521}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.4133543968200684, "per_file_time": {"mean": 0.00626303559049554, "std_dev": 0.00042297743356702814}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.49369120597839355, "per_file_and_rule_time": {"mean": 0.0013828885321523628, "std_dev": 2.0608696911877684e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.10397124290466309, "per_def_and_rule_time": {"mean": 0.0007759047977959931, "std_dev": 1.343924280211366e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1091254080}, "engine_requested": "OSS", "skipped_rules": []}