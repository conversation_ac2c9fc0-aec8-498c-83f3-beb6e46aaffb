{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "start": {"line": 105, "col": 19, "offset": 2702}, "end": {"line": 105, "col": 130, "offset": 2813}}]], "message": "Syntax error at line downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml:105:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `\"RELEASE_TAG=$(if [ ${{ github.event_name }} = release ]; then echo latest; else echo beta; fi)\" >> $GITHUB_ENV` was unexpected", "path": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "spans": [{"file": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "start": {"line": 105, "col": 19, "offset": 2702}, "end": {"line": 105, "col": 130, "offset": 2813}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "start": {"line": 113, "col": 30, "offset": 3105}, "end": {"line": 113, "col": 33, "offset": 3108}}, {"path": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "start": {"line": 113, "col": 71, "offset": 3105}, "end": {"line": 113, "col": 74, "offset": 3108}}]], "message": "Syntax error at line downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml:113:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "spans": [{"file": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "start": {"line": 113, "col": 30, "offset": 3105}, "end": {"line": 113, "col": 33, "offset": 3108}}, {"file": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "start": {"line": 113, "col": 71, "offset": 3105}, "end": {"line": 113, "col": 74, "offset": 3108}}]}], "paths": {"scanned": ["downloaded_repos/apify_proxy-chain/.editorconfig", "downloaded_repos/apify_proxy-chain/.github/dependabot.yaml", "downloaded_repos/apify_proxy-chain/.github/scripts/before-beta-release.js", "downloaded_repos/apify_proxy-chain/.github/workflows/check.yaml", "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "downloaded_repos/apify_proxy-chain/.gitignore", "downloaded_repos/apify_proxy-chain/.mocharc.json", "downloaded_repos/apify_proxy-chain/.npmignore", "downloaded_repos/apify_proxy-chain/CHANGELOG.md", "downloaded_repos/apify_proxy-chain/LICENSE", "downloaded_repos/apify_proxy-chain/README.md", "downloaded_repos/apify_proxy-chain/eslint.config.mjs", "downloaded_repos/apify_proxy-chain/examples/apify_proxy_tunnel.js", "downloaded_repos/apify_proxy-chain/jest.config.ts", "downloaded_repos/apify_proxy-chain/package.json", "downloaded_repos/apify_proxy-chain/src/anonymize_proxy.ts", "downloaded_repos/apify_proxy-chain/src/chain.ts", "downloaded_repos/apify_proxy-chain/src/chain_socks.ts", "downloaded_repos/apify_proxy-chain/src/custom_connect.ts", "downloaded_repos/apify_proxy-chain/src/custom_response.ts", "downloaded_repos/apify_proxy-chain/src/direct.ts", "downloaded_repos/apify_proxy-chain/src/forward.ts", "downloaded_repos/apify_proxy-chain/src/forward_socks.ts", "downloaded_repos/apify_proxy-chain/src/index.ts", "downloaded_repos/apify_proxy-chain/src/request_error.ts", "downloaded_repos/apify_proxy-chain/src/server.ts", "downloaded_repos/apify_proxy-chain/src/socket.ts", "downloaded_repos/apify_proxy-chain/src/statuses.ts", "downloaded_repos/apify_proxy-chain/src/tcp_tunnel_tools.ts", "downloaded_repos/apify_proxy-chain/src/utils/count_target_bytes.ts", "downloaded_repos/apify_proxy-chain/src/utils/decode_uri_component_safe.ts", "downloaded_repos/apify_proxy-chain/src/utils/get_basic.ts", "downloaded_repos/apify_proxy-chain/src/utils/is_hop_by_hop_header.ts", "downloaded_repos/apify_proxy-chain/src/utils/nodeify.ts", "downloaded_repos/apify_proxy-chain/src/utils/normalize_url_port.ts", "downloaded_repos/apify_proxy-chain/src/utils/parse_authorization_header.ts", "downloaded_repos/apify_proxy-chain/src/utils/redact_url.ts", "downloaded_repos/apify_proxy-chain/src/utils/valid_headers_only.ts", "downloaded_repos/apify_proxy-chain/tsconfig.eslint.json", "downloaded_repos/apify_proxy-chain/tsconfig.json"], "skipped": [{"path": "downloaded_repos/apify_proxy-chain/.github/workflows/release.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/apify_proxy-chain/test/.eslintrc.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/Dockerfile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/anonymize_proxy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/anonymize_proxy_no_password.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/ee-memory-leak.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/phantom_get.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/server.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/socks.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/ssl.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/ssl.key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/tcp_tunnel.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/tools.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/utils/run_locally.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/utils/target_server.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/utils/testing_tcp_service.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apify_proxy-chain/test/utils/throws_async.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7661750316619873, "profiling_times": {"config_time": 5.985447406768799, "core_time": 2.8982532024383545, "ignores_time": 0.0017426013946533203, "total_time": 8.886369466781616}, "parsing_time": {"total_time": 0.791846513748169, "per_file_time": {"mean": 0.02328960334553438, "std_dev": 0.0009395110506058238}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.4611077308654785, "per_file_time": {"mean": 0.030360594130398932, "std_dev": 0.006412728899233565}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.3399772644042969, "per_file_and_rule_time": {"mean": 0.004542295811539991, "std_dev": 0.00016145416833048549}, "very_slow_stats": {"time_ratio": 0.1761493038059368, "count_ratio": 0.006779661016949152}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/apify_proxy-chain/src/chain.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.11569404602050781}, {"fpath": "downloaded_repos/apify_proxy-chain/src/server.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.12034201622009277}]}, "tainting_time": {"total_time": 0.3160130977630615, "per_def_and_rule_time": {"mean": 0.0008876772409074763, "std_dev": 5.389022817068894e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}