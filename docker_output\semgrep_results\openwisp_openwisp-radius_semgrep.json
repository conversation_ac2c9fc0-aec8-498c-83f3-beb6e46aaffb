{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/openwisp_openwisp-radius/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 26}, "end": {"line": 4, "col": 11, "offset": 34}, "extra": {"message": "Service 'influxdb' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/openwisp_openwisp-radius/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 26}, "end": {"line": 4, "col": 11, "offset": 34}, "extra": {"message": "Service 'influxdb' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/openwisp_openwisp-radius/docker-compose.yml", "start": {"line": 15, "col": 3, "offset": 269}, "end": {"line": 15, "col": 8, "offset": 274}, "extra": {"message": "Service 'redis' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/openwisp_openwisp-radius/docker-compose.yml", "start": {"line": 15, "col": 3, "offset": 269}, "end": {"line": 15, "col": 8, "offset": 274}, "extra": {"message": "Service 'redis' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/admin.py", "start": {"line": 531, "col": 12, "offset": 15564}, "end": {"line": 531, "col": 62, "offset": 15614}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "start": {"line": 991, "col": 13, "offset": 32530}, "end": {"line": 991, "col": 40, "offset": 32557}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n                user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "start": {"line": 995, "col": 13, "offset": 32759}, "end": {"line": 995, "col": 40, "offset": 32786}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n                user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/utils.py", "start": {"line": 46, "col": 12, "offset": 1133}, "end": {"line": 46, "col": 26, "offset": 1147}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256()", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.telnetlib.telnetlib", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/convert_called_station_id.py", "start": {"line": 26, "col": 14, "offset": 684}, "end": {"line": 26, "col": 77, "offset": 747}, "extra": {"message": "Telnet does not encrypt communications. Use SSH instead.", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L208", "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B312", "references": ["https://docs.python.org/3/library/telnetlib.html"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.telnetlib.telnetlib", "shortlink": "https://sg.run/Gelp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/social/views.py", "start": {"line": 26, "col": 20, "offset": 1017}, "end": {"line": 26, "col": 71, "offset": 1068}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/custom_password_reset_email.html", "start": {"line": 4, "col": 1, "offset": 53}, "end": {"line": 4, "col": 17, "offset": 69}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "start": {"line": 6, "col": 8, "offset": 134}, "end": {"line": 6, "col": 24, "offset": 150}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "start": {"line": 7, "col": 8, "offset": 257}, "end": {"line": 7, "col": 24, "offset": 273}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/logout_error.html", "start": {"line": 6, "col": 8, "offset": 127}, "end": {"line": 6, "col": 24, "offset": 143}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/logout_error.html", "start": {"line": 8, "col": 8, "offset": 271}, "end": {"line": 8, "col": 24, "offset": 287}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/logout_error.html", "start": {"line": 10, "col": 8, "offset": 427}, "end": {"line": 10, "col": 24, "offset": 443}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/logout_error.html", "start": {"line": 12, "col": 8, "offset": 615}, "end": {"line": 12, "col": 24, "offset": 631}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "start": {"line": 8, "col": 9, "offset": 201}, "end": {"line": 8, "col": 125, "offset": 317}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/radius_accounting_start.html", "start": {"line": 5, "col": 4, "offset": 167}, "end": {"line": 6, "col": 148, "offset": 493}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/radius_accounting_start.html", "start": {"line": 6, "col": 30, "offset": 375}, "end": {"line": 6, "col": 96, "offset": 441}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/utils.py", "start": {"line": 195, "col": 9, "offset": 6100}, "end": {"line": 195, "col": 33, "offset": 6124}, "extra": {"message": "The password on 'u' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=u):\n            u.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/utils.py", "start": {"line": 204, "col": 24, "offset": 6428}, "end": {"line": 204, "col": 73, "offset": 6477}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openwisp_openwisp-radius/.github/workflows/ci.yml", "start": {"line": 82, "col": 13, "offset": 1746}, "end": {"line": 82, "col": 16, "offset": 1749}}]], "message": "Syntax error at line downloaded_repos/openwisp_openwisp-radius/.github/workflows/ci.yml:82:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/openwisp_openwisp-radius/.github/workflows/ci.yml", "spans": [{"file": "downloaded_repos/openwisp_openwisp-radius/.github/workflows/ci.yml", "start": {"line": 82, "col": 13, "offset": 1746}, "end": {"line": 82, "col": 16, "offset": 1749}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 79}}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html:1:\n `{% extends \"account/base_entrance.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "spans": [{"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 79}}, {"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 77}}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "start": {"line": 11, "col": 89, "offset": 0}, "end": {"line": 11, "col": 94, "offset": 5}}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html:1:\n `{% extends \"account/base_manage.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "spans": [{"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 77}}, {"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "start": {"line": 11, "col": 89, "offset": 0}, "end": {"line": 11, "col": 94, "offset": 5}}, {"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 77}}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html:1:\n `{% extends \"account/base_manage.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html", "spans": [{"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 77}}, {"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 96}}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 44}}]], "message": "Syntax error at line downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html:1:\n `{% extends 'admin/change_form.html' %}\n{% load i18n %}\n{% block field_sets %}\n{% if help_text %}` was unexpected", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "spans": [{"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 96}}, {"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 44}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 36, "offset": 121}}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 44}}]], "message": "Syntax error at line downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html:1:\n `{% extends 'admin/change_form.html' %}\n{% load i18n %}\n{% block object-tools-items %}\n{% if download_rad_batch_pdf_url %}` was unexpected", "path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html", "spans": [{"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 36, "offset": 121}}, {"file": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 44}}]}], "paths": {"scanned": ["downloaded_repos/openwisp_openwisp-radius/.github/FUNDING.yml", "downloaded_repos/openwisp_openwisp-radius/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/openwisp_openwisp-radius/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/openwisp_openwisp-radius/.github/ISSUE_TEMPLATE/question.md", "downloaded_repos/openwisp_openwisp-radius/.github/dependabot.yml", "downloaded_repos/openwisp_openwisp-radius/.github/pull_request_template.md", "downloaded_repos/openwisp_openwisp-radius/.github/workflows/ci.yml", "downloaded_repos/openwisp_openwisp-radius/.github/workflows/pypi.yml", "downloaded_repos/openwisp_openwisp-radius/.github/workflows/version-branch.yml", "downloaded_repos/openwisp_openwisp-radius/.gitignore", "downloaded_repos/openwisp_openwisp-radius/.readthedocs.yaml", "downloaded_repos/openwisp_openwisp-radius/CHANGES.rst", "downloaded_repos/openwisp_openwisp-radius/CONTRIBUTING.rst", "downloaded_repos/openwisp_openwisp-radius/LICENSE", "downloaded_repos/openwisp_openwisp-radius/MANIFEST.in", "downloaded_repos/openwisp_openwisp-radius/README.rst", "downloaded_repos/openwisp_openwisp-radius/docker-compose.yml", "downloaded_repos/openwisp_openwisp-radius/docs/deploy/freeradius.rst", "downloaded_repos/openwisp_openwisp-radius/docs/deploy/freeradius_wpa_enterprise.rst", "downloaded_repos/openwisp_openwisp-radius/docs/developer/extending.rst", "downloaded_repos/openwisp_openwisp-radius/docs/developer/index.rst", "downloaded_repos/openwisp_openwisp-radius/docs/developer/installation.rst", "downloaded_repos/openwisp_openwisp-radius/docs/developer/utils.rst", "downloaded_repos/openwisp_openwisp-radius/docs/images/download_user_credentials_button.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/drf_api_interface.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/freeradius_allowed_hosts.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/mac-address-roaming.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/optional_fields.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/org_uuid.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/organization_coa_enabled.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/organization_registration_setting.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/organization_saml_setting.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/organization_sms_verification_setting.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/organization_social_login_setting.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/pdf_of_user_list.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/radius-dashboard-charts.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/radius-traffic-chart.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/swagger_api.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/token.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/total-user-registration-chart.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/unique-radius-session-chart.png", "downloaded_repos/openwisp_openwisp-radius/docs/images/user-registration-chart.png", "downloaded_repos/openwisp_openwisp-radius/docs/index.rst", "downloaded_repos/openwisp_openwisp-radius/docs/partials/developer-docs.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/change_of_authorization.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/enforcing_limits.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/generating_users.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/importing_users.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/intro.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/management_commands.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/radius_monitoring.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/registration.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/rest-api.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/saml.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/settings.rst", "downloaded_repos/openwisp_openwisp-radius/docs/user/social_login.rst", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/admin.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/exceptions.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/freeradius_views.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/permissions.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/serializers.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/swagger.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/urls.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/utils.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/views.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/apps.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/admin_filters.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/forms.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/validators.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/checks.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/base.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/exceptions.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/mysql/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/mysql/daily_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/mysql/daily_traffic_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/mysql/monthly_traffic_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/postgresql/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/postgresql/daily_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/postgresql/daily_traffic_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/postgresql/monthly_traffic_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/resets.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/sqlite/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/sqlite/daily_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/sqlite/daily_traffic_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/counters/sqlite/monthly_traffic_counter.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/exceptions.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/admin.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/apps.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/configuration.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/migrations/0001_initial.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/migrations/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/receivers.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/settings.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/static/radius-monitoring/css/device-change.css", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/static/radius-monitoring/js/device-change.js", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/tasks.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/templates/admin/config/radius-monitoring/device/change_form.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/utils.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/locale/de/LC_MESSAGES/django.po", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/locale/fur/LC_MESSAGES/django.po", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/locale/it/LC_MESSAGES/django.po", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/locale/sl/LC_MESSAGES/django.po", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/batch_add_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/cleanup_stale_radacct.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/convert_called_station_id.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/deactivate_expired_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/delete_old_postauth.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/delete_old_radacct.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/delete_old_radiusbatch_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/delete_unverified_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/base/prefix_add_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/batch_add_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/cleanup_stale_radacct.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/convert_called_station_id.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/deactivate_expired_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/delete_old_postauth.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/delete_old_radacct.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/delete_old_radiusbatch_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/delete_unverified_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/prefix_add_users.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/upgrade_from_django_freeradius.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0001_initial_freeradius.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0002_initial_openwisp_radius.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0003_default_radius_groups.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0004_default_permissions.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0005_radiustoken.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0006_add_radactt_fields.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0007_sms_verification.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0008_sms_sender.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0009_radbatch_user_credentials_field.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0010_csv_private_storage.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0011_add_null_uuid_field.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0012_populate_uuid_field.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0013_remove_null_uuid_field.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0014_radiustoken_freeradius_auth.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0015_freeradius_allowed_hosts.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0016_allowed_mobile_prefixes.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0017_phonetoken_phone_number.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0018_populate_phonetoken_phone_number.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0019_made_phonetoken_phone_number_required.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0020_added_optional_registration_fields.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0021_radius_user_group_unique_together.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0022_organizationradiussettings_registration_enabled.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0023_registered_user.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0024_registereduser_modified.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0025_sms_verification.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0026_login_status_url_org_settings.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0027_password_reset_url_org_settings.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0028_organizationradiussettings_saml_social_registration_enabled.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0029_remove_check_customizations.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0030_remove_radiuscheck_notes.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0031_added_fallback_model_fields.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0032_organizationradiussettings_sms_message.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0033_alter_organizationradiussettings_password_reset_url.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0034_organizationradiussettings_coa_enabled.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0035_organizationradiussettings_sms_cooldown.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0036_organizationradiussettings_mac_addr_roaming_enabled.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0037_alter_organizationradiussettings_allowed_mobile_prefixes_and_more.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0038_clean_fallbackfields.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0039_alter_radiusaccounting_called_station_id_and_more.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0040_rename_phonetoken_index.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/models.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/private_storage/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/private_storage/storage.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/private_storage/urls.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/private_storage/views.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/radclient/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/radclient/client.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/radclient/dictionary", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/receivers.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/registration.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/saml/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/saml/backends.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/saml/urls.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/saml/utils.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/saml/views.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/settings.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/signals.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/social/__init__.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/social/urls.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/social/views.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/static/openwisp-radius/css/freeradius.css", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/static/openwisp-radius/css/mode-switcher.css", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/static/openwisp-radius/css/radiusbatch.css", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/static/openwisp-radius/js/mode-switcher.js", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/static/openwisp-radius/js/organization-setting-inline.js", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/static/openwisp-radius/js/strategy-switcher.js", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tasks.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/custom_password_reset_email.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_error.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/logout_error.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/prefix_pdf.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/radius_accounting_start.html", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/urls.py", "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/utils.py", "downloaded_repos/openwisp_openwisp-radius/pyproject.toml", "downloaded_repos/openwisp_openwisp-radius/requirements-test.txt", "downloaded_repos/openwisp_openwisp-radius/run-qa-checks", "downloaded_repos/openwisp_openwisp-radius/runsphinx-build", "downloaded_repos/openwisp_openwisp-radius/runtests.py", "downloaded_repos/openwisp_openwisp-radius/setup.cfg", "downloaded_repos/openwisp_openwisp-radius/setup.py"], "skipped": [{"path": "downloaded_repos/openwisp_openwisp-radius/.github/workflows/ci.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openwisp_openwisp-radius/docs/images/add_users_csv.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openwisp_openwisp-radius/docs/images/add_users_prefix.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/tests/mixins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/integrations/monitoring/tests/test_metrics.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/auth_error.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/login_additional_info.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/djangosaml2/wayf.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_add_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/templates/openwisp-radius/admin/rad_batch_users_change_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/mixins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/openvpn.status", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/selenium/test_csv_user_generation.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/selenium/test_import_with_hashed_passwords.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/selenium/test_standard_csv_import.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_improper.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_invalid.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_invalid_format.pdf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_new.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_users.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_utf16_file1.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_utf16_file2.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/test_batch_utf8Sig_file2.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/upgrader_script/contenttype.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/upgrader_script/freeradius.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/upgrader_script/group.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/upgrader_script/permission.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/upgrader_script/site.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/upgrader_script/social.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/static/upgrader_script/user.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_api/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_api/test_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_api/test_freeradius_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_api/test_phone_verification.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_api/test_rest_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_api/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_batch_add_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_checks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_commands.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_counters/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_counters/test_base_counter.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_counters/test_mysql_counters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_counters/test_postgresql_counters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_counters/test_sqlite_counters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_counters/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_radclient.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_saml/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_saml/remote_idp_metadata.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_saml/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_saml/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_saml/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_selenium.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_social.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_tasks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_upgrader_script.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_users_integration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/celery.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/integrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/integrations/tests.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/local_settings.example.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/routing.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/api/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/api/views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/batch_add_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/cleanup_stale_radacct.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/convert_called_station_id.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/deactivate_expired_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/delete_old_postauth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/delete_old_radacct.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/delete_old_radiusbatch_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/delete_unverified_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/prefix_add_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/management/commands/upgrade_from_django_freeradius.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0001_initial_freeradius.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0002_initial_openwisp_app.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0003_default_groups_and_permissions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0004_allowed_mobile_prefixes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0005_phonetoken_phone_number.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0020_added_optional_registration_fields.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0021_organizationradiussettings_registration_enabled.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0022_registered_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0023_registereduser_modified.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0024_sms_verification.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0025_login_status_url_org_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0026_password_reset_url_org_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0027_remove_check_customizations.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0028_alter_organizationradiussettings_allowed_mobile_prefixes_and_more.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0029_alter_organizationradiussettings_allowed_mobile_prefixes_and_more.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/0030_alter_radiusaccounting_called_station_id_and_more.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/saml/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/saml/views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/social/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/social/views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_radius/tests.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/management/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/management/commands/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/management/commands/export_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/migrations/0004_default_groups.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/sample_users/tests.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openwisp_openwisp-radius/tests/openwisp2/views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.935542106628418, "profiling_times": {"config_time": 6.713006258010864, "core_time": 6.0141003131866455, "ignores_time": 0.0023603439331054688, "total_time": 12.73089861869812}, "parsing_time": {"total_time": 1.230043649673462, "per_file_time": {"mean": 0.008145984434923589, "std_dev": 0.0004208042043065425}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 16.223020792007446, "per_file_time": {"mean": 0.027265581163037744, "std_dev": 0.023901128679810854}, "very_slow_stats": {"time_ratio": 0.25387935247636756, "count_ratio": 0.0033613445378151263}, "very_slow_files": [{"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/views.py", "ftime": 1.815730094909668}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "ftime": 2.30295991897583}]}, "matching_time": {"total_time": 7.9761810302734375, "per_file_and_rule_time": {"mean": 0.007567534184320147, "std_dev": 0.0008190894580403197}, "very_slow_stats": {"time_ratio": 0.43938455804846505, "count_ratio": 0.018026565464895637}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/views.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.15547609329223633}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "rule_id": "python.django.security.injection.command.command-injection-os-system.command-injection-os-system", "time": 0.1566009521484375}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "rule_id": "python.django.security.passwords.password-empty-string.password-empty-string", "time": 0.15812397003173828}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.15962910652160645}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/serializers.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.2632770538330078}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.3039689064025879}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/freeradius_views.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.30724382400512695}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/base/models.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.30982112884521484}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/api/views.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.31244897842407227}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0001_initial_freeradius.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.3489840030670166}]}, "tainting_time": {"total_time": 1.622035264968872, "per_def_and_rule_time": {"mean": 0.0006009763856868735, "std_dev": 8.896404642890502e-06}, "very_slow_stats": {"time_ratio": 0.11362382628806071, "count_ratio": 0.0007410151908114116}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/migrations/0001_initial_freeradius.py", "fline": 14, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.07552385330200195}, {"fpath": "downloaded_repos/openwisp_openwisp-radius/openwisp_radius/management/commands/upgrade_from_django_freeradius.py", "fline": 68, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.10877799987792969}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}