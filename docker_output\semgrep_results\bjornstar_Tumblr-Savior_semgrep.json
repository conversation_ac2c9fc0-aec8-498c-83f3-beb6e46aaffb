{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/bjornstar_Tumblr-Savior/src/data/script.js", "start": {"line": 339, "col": 13, "offset": 9403}, "end": {"line": 339, "col": 28, "offset": 9418}, "extra": {"message": "RegExp() called with a `entry` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/bjornstar_Tumblr-Savior/.github/FUNDING.yml", "downloaded_repos/bjornstar_Tumblr-Savior/.github/workflows/nodejs.yml", "downloaded_repos/bjornstar_Tumblr-Savior/.gitignore", "downloaded_repos/bjornstar_Tumblr-Savior/.npmignore", "downloaded_repos/bjornstar_Tumblr-Savior/.npmrc", "downloaded_repos/bjornstar_Tumblr-Savior/CHANGELOG.md", "downloaded_repos/bjornstar_Tumblr-Savior/LICENSE", "downloaded_repos/bjornstar_Tumblr-Savior/PRIVACY.md", "downloaded_repos/bjornstar_Tumblr-Savior/README.md", "downloaded_repos/bjornstar_Tumblr-Savior/build.sh", "downloaded_repos/bjornstar_Tumblr-Savior/eslint.config.js", "downloaded_repos/bjornstar_Tumblr-Savior/media/icon-256.png", "downloaded_repos/bjornstar_Tumblr-Savior/media/screenshot-1024x768.png", "downloaded_repos/bjornstar_Tumblr-Savior/package.json", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/Icon-128.png", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/Icon-16.png", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/Icon-32.png", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/Icon-48.png", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/Icon-64.png", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/main.js", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/options.css", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/options.html", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/options.js", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/script.js", "downloaded_repos/bjornstar_Tumblr-Savior/src/data/x.png", "downloaded_repos/bjornstar_Tumblr-Savior/src/index.html", "downloaded_repos/bjornstar_Tumblr-Savior/src/manifest.json"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6641929149627686, "profiling_times": {"config_time": 5.6590752601623535, "core_time": 2.6584668159484863, "ignores_time": 0.0018951892852783203, "total_time": 8.320369482040405}, "parsing_time": {"total_time": 0.19345474243164062, "per_file_time": {"mean": 0.019345474243164063, "std_dev": 0.0001332248406106373}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.9433600902557373, "per_file_time": {"mean": 0.014513232157780578, "std_dev": 0.0032736248321930755}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.35935044288635254, "per_file_and_rule_time": {"mean": 0.0032373913773545275, "std_dev": 6.163851734809878e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.12218070030212402, "per_def_and_rule_time": {"mean": 0.00024985828282642953, "std_dev": 1.2571529559761038e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}