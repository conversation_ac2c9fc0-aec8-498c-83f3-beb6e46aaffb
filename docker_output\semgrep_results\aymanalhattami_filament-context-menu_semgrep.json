{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/aymanalhattami_filament-context-menu/bin/build.js", "start": {"line": 37, "col": 33, "offset": 1003}, "end": {"line": 37, "col": 126, "offset": 1096}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/aymanalhattami_filament-context-menu/configure.php", "start": {"line": 196, "col": 5, "offset": 8166}, "end": {"line": 196, "col": 21, "offset": 8182}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/aymanalhattami_filament-context-menu/configure.php", "start": {"line": 229, "col": 5, "offset": 8797}, "end": {"line": 229, "col": 47, "offset": 8839}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/aymanalhattami_filament-context-menu/configure.php", "start": {"line": 334, "col": 9, "offset": 11466}, "end": {"line": 334, "col": 26, "offset": 11483}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/aymanalhattami_filament-context-menu/configure.php", "start": {"line": 364, "col": 21, "offset": 12626}, "end": {"line": 364, "col": 49, "offset": 12654}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 38, "offset": 932}, "end": {"line": 38, "col": 57, "offset": 951}}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 24, "offset": 932}, "end": {"line": 39, "col": 43, "offset": 951}}]], "message": "Syntax error at line downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml:38:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 38, "offset": 932}, "end": {"line": 38, "col": 57, "offset": 951}}, {"file": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 24, "offset": 932}, "end": {"line": 39, "col": 43, "offset": 951}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 53, "offset": 1119}, "end": {"line": 43, "col": 69, "offset": 1135}}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 97, "offset": 1119}, "end": {"line": 43, "col": 115, "offset": 1137}}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 137, "offset": 1119}, "end": {"line": 43, "col": 152, "offset": 1134}}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 19, "offset": 1119}, "end": {"line": 44, "col": 22, "offset": 1122}}]], "message": "Syntax error at line downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml:43:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 53, "offset": 1119}, "end": {"line": 43, "col": 69, "offset": 1135}}, {"file": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 97, "offset": 1119}, "end": {"line": 43, "col": 115, "offset": 1137}}, {"file": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 137, "offset": 1119}, "end": {"line": 43, "col": 152, "offset": 1134}}, {"file": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 19, "offset": 1119}, "end": {"line": 44, "col": 22, "offset": 1122}}]}], "paths": {"scanned": ["downloaded_repos/aymanalhattami_filament-context-menu/.editorconfig", "downloaded_repos/aymanalhattami_filament-context-menu/.gitattributes", "downloaded_repos/aymanalhattami_filament-context-menu/.github/CONTRIBUTING.md", "downloaded_repos/aymanalhattami_filament-context-menu/.github/ISSUE_TEMPLATE/bug.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.github/SECURITY.md", "downloaded_repos/aymanalhattami_filament-context-menu/.github/dependabot.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/phpstan.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/update-changelog.yml", "downloaded_repos/aymanalhattami_filament-context-menu/.gitignore", "downloaded_repos/aymanalhattami_filament-context-menu/.prettierrc", "downloaded_repos/aymanalhattami_filament-context-menu/CHANGELOG.md", "downloaded_repos/aymanalhattami_filament-context-menu/LICENSE.md", "downloaded_repos/aymanalhattami_filament-context-menu/README.md", "downloaded_repos/aymanalhattami_filament-context-menu/TODO.md", "downloaded_repos/aymanalhattami_filament-context-menu/bin/build.js", "downloaded_repos/aymanalhattami_filament-context-menu/composer.json", "downloaded_repos/aymanalhattami_filament-context-menu/config/filament-context-menu.php", "downloaded_repos/aymanalhattami_filament-context-menu/configure.php", "downloaded_repos/aymanalhattami_filament-context-menu/package-lock.json", "downloaded_repos/aymanalhattami_filament-context-menu/package.json", "downloaded_repos/aymanalhattami_filament-context-menu/phpstan-baseline.neon", "downloaded_repos/aymanalhattami_filament-context-menu/phpstan.neon.dist", "downloaded_repos/aymanalhattami_filament-context-menu/phpunit.xml.dist", "downloaded_repos/aymanalhattami_filament-context-menu/pint.json", "downloaded_repos/aymanalhattami_filament-context-menu/resources/css/app.css", "downloaded_repos/aymanalhattami_filament-context-menu/resources/lang/en/skeleton.php", "downloaded_repos/aymanalhattami_filament-context-menu/resources/views/.gitkeep", "downloaded_repos/aymanalhattami_filament-context-menu/resources/views/components/context-menu.blade.php", "downloaded_repos/aymanalhattami_filament-context-menu/resources/views/components/divider.blade.php", "downloaded_repos/aymanalhattami_filament-context-menu/resources/views/filament/tables/columns/context-menu-column.blade.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Actions/CopyAction.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Actions/GoBackAction.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Actions/GoForwardAction.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Actions/RefreshAction.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuCheckboxColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuColorColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuIconColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuImageColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuSelectColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuTextColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuTextInputColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Columns/ContextMenuToggleColumn.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/ContextMenu.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/ContextMenuDivider.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/FilamentContextMenuServiceProvider.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Traits/ColumnHasContextMenu.php", "downloaded_repos/aymanalhattami_filament-context-menu/src/Traits/PageHasContextMenu.php", "downloaded_repos/aymanalhattami_filament-context-menu/stubs/.gitkeep", "downloaded_repos/aymanalhattami_filament-context-menu/tailwind.config.js"], "skipped": [{"path": "downloaded_repos/aymanalhattami_filament-context-menu/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/docs/filament-context-menu.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/docs/filament-context-menu.webm", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/resources/dist/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/resources/dist/app.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/Actions/CopyActionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/Actions/GoBackActionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/Actions/GoForwardActionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/Actions/RefreshActionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/Columns/ContextMenuTextColumnTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/ContextMenuDividerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/ContextMenuTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/aymanalhattami_filament-context-menu/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6166808605194092, "profiling_times": {"config_time": 6.095139265060425, "core_time": 2.409745216369629, "ignores_time": 0.0016586780548095703, "total_time": 8.507376432418823}, "parsing_time": {"total_time": 0.3828544616699219, "per_file_time": {"mean": 0.010075117412366367, "std_dev": 0.00021085618068950168}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.9261190891265869, "per_file_time": {"mean": 0.006343281432373887, "std_dev": 0.00022731340081668698}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.20908808708190918, "per_file_and_rule_time": {"mean": 0.0007494196669602481, "std_dev": 3.290280392463257e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0045282840728759766, "per_def_and_rule_time": {"mean": 0.000215632574898856, "std_dev": 2.0217758951204604e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1085719040}, "engine_requested": "OSS", "skipped_rules": []}