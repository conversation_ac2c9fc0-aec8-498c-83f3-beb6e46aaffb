{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/mdutt247_laravel-news/database/factories/UserFactory.php", "start": {"line": 30, "col": 28, "offset": 681}, "end": {"line": 30, "col": 88, "offset": 741}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/mdutt247_laravel-news/server.php", "start": {"line": 17, "col": 33, "offset": 473}, "end": {"line": 17, "col": 55, "offset": 495}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-response.tainted-html-response on downloaded_repos/mdutt247_laravel-news/public/js/app.js:\n ", "path": "downloaded_repos/mdutt247_laravel-news/public/js/app.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/mdutt247_laravel-news/public/js/app.js:\n ", "path": "downloaded_repos/mdutt247_laravel-news/public/js/app.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/mdutt247_laravel-news/public/js/app.js:\n ", "path": "downloaded_repos/mdutt247_laravel-news/public/js/app.js"}], "paths": {"scanned": ["downloaded_repos/mdutt247_laravel-news/.editorconfig", "downloaded_repos/mdutt247_laravel-news/.env.example", "downloaded_repos/mdutt247_laravel-news/.gitattributes", "downloaded_repos/mdutt247_laravel-news/.gitignore", "downloaded_repos/mdutt247_laravel-news/.styleci.yml", "downloaded_repos/mdutt247_laravel-news/LICENSE", "downloaded_repos/mdutt247_laravel-news/README.md", "downloaded_repos/mdutt247_laravel-news/app/Actions/Fortify/CreateNewUser.php", "downloaded_repos/mdutt247_laravel-news/app/Actions/Fortify/PasswordValidationRules.php", "downloaded_repos/mdutt247_laravel-news/app/Actions/Fortify/ResetUserPassword.php", "downloaded_repos/mdutt247_laravel-news/app/Actions/Fortify/UpdateUserPassword.php", "downloaded_repos/mdutt247_laravel-news/app/Actions/Fortify/UpdateUserProfileInformation.php", "downloaded_repos/mdutt247_laravel-news/app/Actions/Jetstream/DeleteUser.php", "downloaded_repos/mdutt247_laravel-news/app/Console/Kernel.php", "downloaded_repos/mdutt247_laravel-news/app/Exceptions/Handler.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Controllers/Api/CategoryApiController.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Controllers/Api/CommentApiController.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Controllers/Api/PostApiController.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Controllers/Api/TagApiController.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Controllers/Api/UserApiController.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Controllers/Controller.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Kernel.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Livewire/Categories/Categories.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Livewire/Categories/Categoryposts.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Livewire/Posts/Post.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Livewire/Posts/Posts.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Livewire/Tags/Tagposts.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Livewire/Tags/Tags.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/Authenticate.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/EncryptCookies.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/PreventRequestsDuringMaintenance.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/RedirectIfAuthenticated.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/TrimStrings.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/TrustHosts.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/TrustProxies.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Middleware/VerifyCsrfToken.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Resources/CategoryResource.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Resources/CommentResource.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Resources/ImageResource.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Resources/PostResource.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Resources/TagResource.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Resources/UserResource.php", "downloaded_repos/mdutt247_laravel-news/app/Http/Resources/VideoResource.php", "downloaded_repos/mdutt247_laravel-news/app/Models/Category.php", "downloaded_repos/mdutt247_laravel-news/app/Models/Comment.php", "downloaded_repos/mdutt247_laravel-news/app/Models/Image.php", "downloaded_repos/mdutt247_laravel-news/app/Models/Post.php", "downloaded_repos/mdutt247_laravel-news/app/Models/Tag.php", "downloaded_repos/mdutt247_laravel-news/app/Models/User.php", "downloaded_repos/mdutt247_laravel-news/app/Models/Video.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/AppServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/AuthServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/BroadcastServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/EventServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/FortifyServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/JetstreamServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/RouteServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/Providers/TelescopeServiceProvider.php", "downloaded_repos/mdutt247_laravel-news/app/View/Components/AppLayout.php", "downloaded_repos/mdutt247_laravel-news/app/View/Components/GuestLayout.php", "downloaded_repos/mdutt247_laravel-news/artisan", "downloaded_repos/mdutt247_laravel-news/bootstrap/app.php", "downloaded_repos/mdutt247_laravel-news/bootstrap/cache/.gitignore", "downloaded_repos/mdutt247_laravel-news/composer.json", "downloaded_repos/mdutt247_laravel-news/composer.lock", "downloaded_repos/mdutt247_laravel-news/config/app.php", "downloaded_repos/mdutt247_laravel-news/config/auth.php", "downloaded_repos/mdutt247_laravel-news/config/broadcasting.php", "downloaded_repos/mdutt247_laravel-news/config/cache.php", "downloaded_repos/mdutt247_laravel-news/config/cors.php", "downloaded_repos/mdutt247_laravel-news/config/database.php", "downloaded_repos/mdutt247_laravel-news/config/filesystems.php", "downloaded_repos/mdutt247_laravel-news/config/fortify.php", "downloaded_repos/mdutt247_laravel-news/config/hashing.php", "downloaded_repos/mdutt247_laravel-news/config/jetstream.php", "downloaded_repos/mdutt247_laravel-news/config/logging.php", "downloaded_repos/mdutt247_laravel-news/config/mail.php", "downloaded_repos/mdutt247_laravel-news/config/queue.php", "downloaded_repos/mdutt247_laravel-news/config/sanctum.php", "downloaded_repos/mdutt247_laravel-news/config/services.php", "downloaded_repos/mdutt247_laravel-news/config/session.php", "downloaded_repos/mdutt247_laravel-news/config/telescope.php", "downloaded_repos/mdutt247_laravel-news/config/view.php", "downloaded_repos/mdutt247_laravel-news/database/.gitignore", "downloaded_repos/mdutt247_laravel-news/database/factories/CategoryFactory.php", "downloaded_repos/mdutt247_laravel-news/database/factories/CommentFactory.php", "downloaded_repos/mdutt247_laravel-news/database/factories/ImageFactory.php", "downloaded_repos/mdutt247_laravel-news/database/factories/PostFactory.php", "downloaded_repos/mdutt247_laravel-news/database/factories/TagFactory.php", "downloaded_repos/mdutt247_laravel-news/database/factories/UserFactory.php", "downloaded_repos/mdutt247_laravel-news/database/factories/VideoFactory.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2014_10_12_000000_create_users_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2014_10_12_100000_create_password_resets_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2014_10_12_200000_add_two_factor_columns_to_users_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2019_08_19_000000_create_failed_jobs_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2019_12_14_000001_create_personal_access_tokens_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_17_152500_create_sessions_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_21_153832_create_posts_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_21_154827_create_categories_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_21_155318_create_comments_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_21_155930_create_images_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_21_160421_create_videos_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_21_161116_create_tags_table.php", "downloaded_repos/mdutt247_laravel-news/database/migrations/2020_11_21_161340_create_post_tag_table.php", "downloaded_repos/mdutt247_laravel-news/database/seeders/DatabaseSeeder.php", "downloaded_repos/mdutt247_laravel-news/package-lock.json", "downloaded_repos/mdutt247_laravel-news/package.json", "downloaded_repos/mdutt247_laravel-news/phpunit.xml", "downloaded_repos/mdutt247_laravel-news/public/.htaccess", "downloaded_repos/mdutt247_laravel-news/public/favicon.ico", "downloaded_repos/mdutt247_laravel-news/public/index.php", "downloaded_repos/mdutt247_laravel-news/public/js/app.js", "downloaded_repos/mdutt247_laravel-news/public/mix-manifest.json", "downloaded_repos/mdutt247_laravel-news/public/robots.txt", "downloaded_repos/mdutt247_laravel-news/public/web.config", "downloaded_repos/mdutt247_laravel-news/resources/css/app.css", "downloaded_repos/mdutt247_laravel-news/resources/js/app.js", "downloaded_repos/mdutt247_laravel-news/resources/js/bootstrap.js", "downloaded_repos/mdutt247_laravel-news/resources/lang/en/auth.php", "downloaded_repos/mdutt247_laravel-news/resources/lang/en/pagination.php", "downloaded_repos/mdutt247_laravel-news/resources/lang/en/passwords.php", "downloaded_repos/mdutt247_laravel-news/resources/lang/en/validation.php", "downloaded_repos/mdutt247_laravel-news/resources/views/api/api-token-manager.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/api/index.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/auth/forgot-password.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/auth/login.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/auth/register.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/auth/reset-password.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/auth/two-factor-challenge.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/auth/verify-email.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/dashboard.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/layouts/app.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/layouts/guest.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/livewire/categories/categories.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/livewire/categories/create.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/livewire/posts/create.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/livewire/posts/post.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/livewire/posts/posts.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/livewire/tags/create.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/livewire/tags/tags.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/navigation-dropdown.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/profile/delete-user-form.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/profile/logout-other-browser-sessions-form.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/profile/show.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/profile/two-factor-authentication-form.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/profile/update-password-form.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/profile/update-profile-information-form.blade.php", "downloaded_repos/mdutt247_laravel-news/resources/views/welcome.blade.php", "downloaded_repos/mdutt247_laravel-news/routes/api.php", "downloaded_repos/mdutt247_laravel-news/routes/channels.php", "downloaded_repos/mdutt247_laravel-news/routes/console.php", "downloaded_repos/mdutt247_laravel-news/routes/web.php", "downloaded_repos/mdutt247_laravel-news/server.php", "downloaded_repos/mdutt247_laravel-news/storage/app/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/app/public/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/framework/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/framework/cache/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/framework/cache/data/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/framework/sessions/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/framework/testing/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/framework/views/.gitignore", "downloaded_repos/mdutt247_laravel-news/storage/logs/.gitignore", "downloaded_repos/mdutt247_laravel-news/tailwind.config.js", "downloaded_repos/mdutt247_laravel-news/webpack.config.js", "downloaded_repos/mdutt247_laravel-news/webpack.mix.js"], "skipped": [{"path": "downloaded_repos/mdutt247_laravel-news/public/css/app.css", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/mdutt247_laravel-news/public/js/app.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mdutt247_laravel-news/public/vendor/telescope/app-dark.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/public/vendor/telescope/app.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/public/vendor/telescope/app.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/public/vendor/telescope/favicon.ico", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/public/vendor/telescope/mix-manifest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/tests/CreatesApplication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/tests/Feature/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mdutt247_laravel-news/tests/Unit/ExampleTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.711003065109253, "profiling_times": {"config_time": 6.170554161071777, "core_time": 22.10320258140564, "ignores_time": 0.001665353775024414, "total_time": 28.276724338531494}, "parsing_time": {"total_time": 0.8853137493133545, "per_file_time": {"mean": 0.006369163664124854, "std_dev": 0.000815135391692116}, "very_slow_stats": {"time_ratio": 0.3743283220337842, "count_ratio": 0.007194244604316547}, "very_slow_files": [{"fpath": "downloaded_repos/mdutt247_laravel-news/package-lock.json", "ftime": 0.33139801025390625}]}, "scanning_time": {"total_time": 23.37682342529297, "per_file_time": {"mean": 0.049737922181474395, "std_dev": 0.7787918620393883}, "very_slow_stats": {"time_ratio": 0.8187331370896073, "count_ratio": 0.002127659574468085}, "very_slow_files": [{"fpath": "downloaded_repos/mdutt247_laravel-news/public/js/app.js", "ftime": 19.13937997817993}]}, "matching_time": {"total_time": 0.352095365524292, "per_file_and_rule_time": {"mean": 0.0015648682912190748, "std_dev": 3.3159951287618454e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.007732391357421875, "per_def_and_rule_time": {"mean": 0.0005947993351862981, "std_dev": 2.2432516423513325e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}