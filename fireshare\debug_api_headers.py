#!/usr/bin/env python3
"""
🔍 Debug API Headers and Authentication
======================================

This script helps debug why the API upload isn't working by:
1. Capturing the exact headers used by the web UI
2. Testing different authentication methods
3. Analyzing the differences between working UI and failing API calls

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import json
import sys
import time

class APIDebugger:
    def __init__(self, target_url):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        
        print("🔍 API Headers and Authentication Debugger")
        print("=" * 45)
        print(f"Target: {self.target_url}")
        print()

    def test_authentication_methods(self, username="admin", password="admin"):
        """Test different authentication methods"""
        print("🔐 TESTING AUTHENTICATION METHODS")
        print("-" * 35)
        
        # Method 1: Standard login
        print("\n1️⃣ Testing standard login...")
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": username, "password": password},
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Headers: {dict(response.headers)}")
            print(f"   Cookies: {dict(response.cookies)}")
            
            if response.status_code == 200:
                print("   ✅ Login successful!")
                
                # Check what cookies we got
                print(f"   🍪 Session cookies: {list(self.session.cookies.keys())}")
                return True
            else:
                print(f"   ❌ Login failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Login error: {e}")
        
        return False

    def analyze_ui_request_pattern(self):
        """Analyze what headers the UI might be using"""
        print("\n🌐 ANALYZING UI REQUEST PATTERNS")
        print("-" * 32)
        
        print("📋 Headers that web UI typically sends:")
        print("   Content-Type: multipart/form-data")
        print("   Accept: application/json, text/plain, */*")
        print("   X-Requested-With: XMLHttpRequest")
        print("   Referer: http://localhost:8080/")
        print("   Origin: http://localhost:8080")
        print("   User-Agent: Mozilla/5.0...")
        print("   Cookie: session=...")
        
        print("\n💡 Common issues:")
        print("   - Missing CSRF tokens")
        print("   - Incorrect Content-Type")
        print("   - Missing session cookies")
        print("   - CORS headers required")

    def test_upload_with_ui_headers(self, username="admin", password="admin"):
        """Test upload with headers that mimic the UI"""
        print("\n🧪 TESTING UPLOAD WITH UI-LIKE HEADERS")
        print("-" * 40)
        
        # First authenticate
        if not self.test_authentication_methods(username, password):
            print("❌ Cannot test upload without authentication")
            return False
        
        # Create test file
        test_content = b"fake video content for testing"
        test_filename = 'test.mp4"; echo "HEADER_TEST_SUCCESS" > /tmp/header_test.txt #'
        
        print(f"📝 Testing with filename: {test_filename}")
        
        # Test with comprehensive headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }
        
        files = {
            'file': (test_filename, test_content, 'video/mp4')
        }
        
        print("\n📤 Testing authenticated upload with UI headers...")
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload",
                files=files,
                headers=headers,
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
            if response.status_code == 201:
                print("   ✅ Upload successful with UI headers!")
                return True
            else:
                print(f"   ❌ Upload failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Upload error: {e}")
        
        # Try public upload too
        print("\n📤 Testing public upload with UI headers...")
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload/public",
                files=files,
                headers=headers,
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
            if response.status_code == 201:
                print("   ✅ Public upload successful with UI headers!")
                return True
            elif response.status_code == 401:
                print("   🔒 Public upload disabled (expected)")
            else:
                print(f"   ❌ Public upload failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Public upload error: {e}")
        
        return False

    def test_csrf_token_requirement(self):
        """Check if CSRF tokens are required"""
        print("\n🛡️ TESTING CSRF TOKEN REQUIREMENTS")
        print("-" * 33)
        
        # Get the main page to see if there are any CSRF tokens
        try:
            response = self.session.get(self.target_url)
            print(f"   Main page status: {response.status_code}")
            
            # Look for CSRF tokens in the response
            if 'csrf' in response.text.lower():
                print("   ⚠️  CSRF tokens detected in page")
            else:
                print("   ✅ No obvious CSRF tokens found")
                
        except Exception as e:
            print(f"   ❌ Error checking main page: {e}")

    def test_session_persistence(self):
        """Test if session cookies are properly maintained"""
        print("\n🍪 TESTING SESSION PERSISTENCE")
        print("-" * 28)
        
        # Check current cookies
        print(f"   Current cookies: {list(self.session.cookies.keys())}")
        
        # Test a protected endpoint
        try:
            response = self.session.get(f"{self.target_url}/api/videos")
            print(f"   Protected endpoint status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Session is valid")
                return True
            else:
                print("   ❌ Session invalid or expired")
                return False
                
        except Exception as e:
            print(f"   ❌ Session test error: {e}")
            return False

    def comprehensive_debug(self, username="admin", password="admin"):
        """Run comprehensive debugging"""
        print("🚀 COMPREHENSIVE API DEBUGGING")
        print("=" * 32)
        
        # Step 1: Test authentication
        auth_success = self.test_authentication_methods(username, password)
        
        # Step 2: Check session persistence
        if auth_success:
            session_valid = self.test_session_persistence()
        else:
            session_valid = False
        
        # Step 3: Check CSRF requirements
        self.test_csrf_token_requirement()
        
        # Step 4: Analyze UI patterns
        self.analyze_ui_request_pattern()
        
        # Step 5: Test with UI headers
        if auth_success:
            upload_success = self.test_upload_with_ui_headers(username, password)
        else:
            upload_success = False
        
        return auth_success, session_valid, upload_success

    def show_working_curl_example(self):
        """Show a working curl example based on findings"""
        print("\n📋 WORKING CURL EXAMPLE")
        print("-" * 22)
        print()
        print("# Step 1: Login and save cookies")
        print("curl -c cookies.txt -X POST \\")
        print(f"  '{self.target_url}/api/login' \\")
        print("  -H 'Content-Type: application/json' \\")
        print("  -d '{\"username\":\"admin\",\"password\":\"admin\"}'")
        print()
        print("# Step 2: Upload with saved cookies")
        print("curl -b cookies.txt -X POST \\")
        print(f"  '{self.target_url}/api/upload' \\")
        print("  -H 'Accept: application/json' \\")
        print(f"  -H 'Origin: {self.target_url}' \\")
        print(f"  -H 'Referer: {self.target_url}/' \\")
        print("  -F 'file=@test.mp4'")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 debug_api_headers.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 debug_api_headers.py http://localhost:8080")
        print("  python3 debug_api_headers.py http://localhost:8080 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else "admin"
    password = sys.argv[3] if len(sys.argv) > 3 else "admin"
    
    debugger = APIDebugger(target_url)
    
    # Run comprehensive debugging
    auth_ok, session_ok, upload_ok = debugger.comprehensive_debug(username, password)
    
    # Show results
    print("\n📊 DEBUGGING RESULTS")
    print("=" * 20)
    print(f"✅ Authentication: {'Working' if auth_ok else 'Failed'}")
    print(f"✅ Session persistence: {'Working' if session_ok else 'Failed'}")
    print(f"✅ Upload with headers: {'Working' if upload_ok else 'Failed'}")
    
    if upload_ok:
        print("\n🎉 SUCCESS! The API is working with proper headers.")
        print("💡 Use the headers shown above in your scripts.")
    else:
        print("\n❌ API still not working. Possible issues:")
        print("   - Check if Fireshare is running properly")
        print("   - Verify credentials are correct")
        print("   - Try uploading via UI first to confirm it works")
        print("   - Check Docker logs: docker logs fireshare")
    
    # Show working example
    debugger.show_working_curl_example()
    
    print("\n💡 NEXT STEPS:")
    print("   1. Try the curl example above")
    print("   2. Update your Python scripts with the working headers")
    print("   3. Use browser dev tools to capture exact UI requests")

if __name__ == "__main__":
    main()
