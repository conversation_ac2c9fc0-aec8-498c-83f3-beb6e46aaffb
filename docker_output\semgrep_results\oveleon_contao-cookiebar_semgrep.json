{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "start": {"line": 936, "col": 17, "offset": 33076}, "end": {"line": 936, "col": 41, "offset": 33100}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "start": {"line": 1066, "col": 29, "offset": 37288}, "end": {"line": 1066, "col": 82, "offset": 37341}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "start": {"line": 1071, "col": 29, "offset": 37563}, "end": {"line": 1071, "col": 113, "offset": 37647}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/Command/AnonymizeIpCommand.php", "start": {"line": 36, "col": 41, "offset": 0}, "end": {"line": 36, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/Command/AnonymizeIpCommand.php:36:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/Command/AnonymizeIpCommand.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/Command/AnonymizeIpCommand.php", "start": {"line": 36, "col": 41, "offset": 0}, "end": {"line": 36, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/ContentElement/CookiebarOpenerController.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/Controller/ContentElement/CookiebarOpenerController.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/ContentElement/CookiebarOpenerController.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/ContentElement/CookiebarOpenerController.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php", "start": {"line": 35, "col": 17, "offset": 0}, "end": {"line": 35, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php", "start": {"line": 36, "col": 17, "offset": 0}, "end": {"line": 36, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php:35:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php", "start": {"line": 35, "col": 17, "offset": 0}, "end": {"line": 35, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php", "start": {"line": 36, "col": 17, "offset": 0}, "end": {"line": 36, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/FrontendModule/CookiebarOpenerController.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/Controller/FrontendModule/CookiebarOpenerController.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/FrontendModule/CookiebarOpenerController.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/FrontendModule/CookiebarOpenerController.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 36, "col": 17, "offset": 0}, "end": {"line": 36, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 37, "col": 17, "offset": 0}, "end": {"line": 37, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 40, "col": 26, "offset": 0}, "end": {"line": 40, "col": 29, "offset": 3}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 41, "col": 26, "offset": 0}, "end": {"line": 41, "col": 30, "offset": 4}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 42, "col": 26, "offset": 0}, "end": {"line": 42, "col": 32, "offset": 6}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 43, "col": 26, "offset": 0}, "end": {"line": 43, "col": 30, "offset": 4}}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 44, "col": 26, "offset": 0}, "end": {"line": 44, "col": 30, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php:36:\n `readonly` was unexpected", "path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "spans": [{"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 36, "col": 17, "offset": 0}, "end": {"line": 36, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 37, "col": 17, "offset": 0}, "end": {"line": 37, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 40, "col": 26, "offset": 0}, "end": {"line": 40, "col": 29, "offset": 3}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 41, "col": 26, "offset": 0}, "end": {"line": 41, "col": 30, "offset": 4}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 42, "col": 26, "offset": 0}, "end": {"line": 42, "col": 32, "offset": 6}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 43, "col": 26, "offset": 0}, "end": {"line": 43, "col": 30, "offset": 4}}, {"file": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "start": {"line": 44, "col": 26, "offset": 0}, "end": {"line": 44, "col": 30, "offset": 4}}]}], "paths": {"scanned": ["downloaded_repos/oveleon_contao-cookiebar/.editorconfig", "downloaded_repos/oveleon_contao-cookiebar/.gitattributes", "downloaded_repos/oveleon_contao-cookiebar/.github/FUNDING.yml", "downloaded_repos/oveleon_contao-cookiebar/.github/ISSUE_TEMPLATE/bug_report.yml", "downloaded_repos/oveleon_contao-cookiebar/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/oveleon_contao-cookiebar/.github/ISSUE_TEMPLATE/feature_request.yml", "downloaded_repos/oveleon_contao-cookiebar/.gitignore", "downloaded_repos/oveleon_contao-cookiebar/LICENSE", "downloaded_repos/oveleon_contao-cookiebar/README.md", "downloaded_repos/oveleon_contao-cookiebar/UPGRADE.md", "downloaded_repos/oveleon_contao-cookiebar/composer.json", "downloaded_repos/oveleon_contao-cookiebar/config/commands.yaml", "downloaded_repos/oveleon_contao-cookiebar/config/listener.yaml", "downloaded_repos/oveleon_contao-cookiebar/config/migrations.yaml", "downloaded_repos/oveleon_contao-cookiebar/config/routes.yaml", "downloaded_repos/oveleon_contao-cookiebar/config/services.yaml", "downloaded_repos/oveleon_contao-cookiebar/contao/config/config.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_content.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_cookie.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_cookie_config.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_cookie_group.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_cookie_log.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_cookiebar.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_module.php", "downloaded_repos/oveleon_contao-cookiebar/contao/dca/tl_page.php", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/default.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/explain.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/modules.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_content.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_cookie.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_cookie_config.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_cookie_group.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_cookie_log.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_cookiebar.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_module.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/de/tl_page.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/default.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/explain.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/modules.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_content.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_cookie.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_cookie_config.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_cookie_group.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_cookie_log.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_cookiebar.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_module.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/en/tl_page.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/default.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/explain.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/modules.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_content.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_cookie.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_cookie_config.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_cookie_group.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_cookie_log.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_cookiebar.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_module.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/fr/tl_page.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/default.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/explain.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/modules.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/tl_content.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/tl_cookie.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/tl_cookie_group.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/tl_cookie_log.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/tl_cookiebar.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/tl_module.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/it/tl_page.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/default.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/explain.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/modules.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_content.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_cookie.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_cookie_config.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_cookie_group.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_cookie_log.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_cookiebar.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_module.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/languages/sv/tl_page.xlf", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/cookiebar/ccb_element_blocker.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/cookiebar/ccb_opener_default.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/cookiebar/cookiebar_default.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/cookiebar/cookiebar_default_deny.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/cookiebar/cookiebar_simple.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/elements/ce_html_googlemaps.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/elements/ce_html_openstreetmap.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/modules/mod_html_googlemaps.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/modules/mod_html_openstreetmap.html5", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/twig/.twig-root", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/twig/content_element/html/googlemaps.html.twig", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/twig/content_element/html/openstreetmap.html.twig", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/twig/content_element/unfiltered_html/googlemaps.html.twig", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/twig/content_element/unfiltered_html/openstreetmap.html.twig", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/twig/frontend_module/unfiltered_html/googlemaps.html.twig", "downloaded_repos/oveleon_contao-cookiebar/contao/templates/twig/frontend_module/unfiltered_html/openstreetmap.html.twig", "downloaded_repos/oveleon_contao-cookiebar/depcheck.php", "downloaded_repos/oveleon_contao-cookiebar/docs/BASICS.md", "downloaded_repos/oveleon_contao-cookiebar/docs/CONFIGURATION.md", "downloaded_repos/oveleon_contao-cookiebar/docs/CONTENT_SECURITY_POLICY.md", "downloaded_repos/oveleon_contao-cookiebar/docs/COOKIE.md", "downloaded_repos/oveleon_contao-cookiebar/docs/CUSTOMIZATION.md", "downloaded_repos/oveleon_contao-cookiebar/docs/EXTENDED_USAGE.md", "downloaded_repos/oveleon_contao-cookiebar/docs/EXTEND_IFRAME.md", "downloaded_repos/oveleon_contao-cookiebar/docs/EXTEND_TYPE.md", "downloaded_repos/oveleon_contao-cookiebar/docs/GROUP.md", "downloaded_repos/oveleon_contao-cookiebar/docs/INSTALL.md", "downloaded_repos/oveleon_contao-cookiebar/docs/MOD_CE_MISC.md", "downloaded_repos/oveleon_contao-cookiebar/docs/README.md", "downloaded_repos/oveleon_contao-cookiebar/docs/logo/ccb_dark.svg", "downloaded_repos/oveleon_contao-cookiebar/docs/logo/ccb_light.svg", "downloaded_repos/oveleon_contao-cookiebar/gulpfile.js", "downloaded_repos/oveleon_contao-cookiebar/package.json", "downloaded_repos/oveleon_contao-cookiebar/public/images/default.png", "downloaded_repos/oveleon_contao-cookiebar/public/images/googlemaps.png", "downloaded_repos/oveleon_contao-cookiebar/public/images/openstreetmap.png", "downloaded_repos/oveleon_contao-cookiebar/public/images/vimeo.png", "downloaded_repos/oveleon_contao-cookiebar/public/images/youtube.png", "downloaded_repos/oveleon_contao-cookiebar/public/scripts/configPresets.js", "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "downloaded_repos/oveleon_contao-cookiebar/public/styles/_cookiebar.scss", "downloaded_repos/oveleon_contao-cookiebar/public/styles/cookiebar_default.css", "downloaded_repos/oveleon_contao-cookiebar/public/styles/cookiebar_default.scss", "downloaded_repos/oveleon_contao-cookiebar/public/styles/cookiebar_simple.css", "downloaded_repos/oveleon_contao-cookiebar/public/styles/cookiebar_simple.scss", "downloaded_repos/oveleon_contao-cookiebar/src/AbstractCookie.php", "downloaded_repos/oveleon_contao-cookiebar/src/Command/AnonymizeIpCommand.php", "downloaded_repos/oveleon_contao-cookiebar/src/ContaoCookiebar.php", "downloaded_repos/oveleon_contao-cookiebar/src/ContaoManager/Plugin.php", "downloaded_repos/oveleon_contao-cookiebar/src/Controller/ContentElement/CookiebarOpenerController.php", "downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php", "downloaded_repos/oveleon_contao-cookiebar/src/Controller/FrontendModule/CookiebarOpenerController.php", "downloaded_repos/oveleon_contao-cookiebar/src/Cookie.php", "downloaded_repos/oveleon_contao-cookiebar/src/Cookiebar.php", "downloaded_repos/oveleon_contao-cookiebar/src/DependencyInjection/Configuration.php", "downloaded_repos/oveleon_contao-cookiebar/src/DependencyInjection/ContaoCookiebarExtension.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieConfigCallbackListener.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarTrait.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/PageCallbackListener.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/InsertTagsListener.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "downloaded_repos/oveleon_contao-cookiebar/src/EventSubscriber/KernelRequestSubscriber.php", "downloaded_repos/oveleon_contao-cookiebar/src/Exception/NoCookiebarSpecifiedException.php", "downloaded_repos/oveleon_contao-cookiebar/src/Export/LogExport.php", "downloaded_repos/oveleon_contao-cookiebar/src/GlobalConfig.php", "downloaded_repos/oveleon_contao-cookiebar/src/Migration/ContaoCookiebarConsentModeMigration.php", "downloaded_repos/oveleon_contao-cookiebar/src/Migration/ContaoCookiebarOpenerMigration.php", "downloaded_repos/oveleon_contao-cookiebar/src/Model/CookieGroupModel.php", "downloaded_repos/oveleon_contao-cookiebar/src/Model/CookieLogModel.php", "downloaded_repos/oveleon_contao-cookiebar/src/Model/CookieModel.php", "downloaded_repos/oveleon_contao-cookiebar/src/Model/CookiebarModel.php", "downloaded_repos/oveleon_contao-cookiebar/src/Model/GlobalConfigModel.php", "downloaded_repos/oveleon_contao-cookiebar/src/Utils/ScriptUtils.php"], "skipped": [{"path": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/configPresets.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/Command/AnonymizeIpCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/ContentElement/CookiebarOpenerController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/CookiebarController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/Controller/FrontendModule/CookiebarOpenerController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieCallbackListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookieGroupCallbackListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/DataContainer/CookiebarCallbackListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oveleon_contao-cookiebar/src/EventListener/KernelRequestListener.php", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6254539489746094, "profiling_times": {"config_time": 6.690927267074585, "core_time": 9.166749715805054, "ignores_time": 0.0018894672393798828, "total_time": 15.8611581325531}, "parsing_time": {"total_time": 0.8209576606750488, "per_file_time": {"mean": 0.014926502921364526, "std_dev": 0.0008145580696850067}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 8.40052342414856, "per_file_time": {"mean": 0.023015132668900158, "std_dev": 0.11172442409888124}, "very_slow_stats": {"time_ratio": 0.7607656948621359, "count_ratio": 0.0027397260273972603}, "very_slow_files": [{"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "ftime": 6.390830039978027}]}, "matching_time": {"total_time": 4.368489742279053, "per_file_and_rule_time": {"mean": 0.01152635815904763, "std_dev": 0.004247337635053373}, "very_slow_stats": {"time_ratio": 0.7486133922734626, "count_ratio": 0.023746701846965697}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.15570783615112305}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.20772695541381836}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.20896601676940918}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.22376608848571777}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.23324298858642578}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.3554720878601074}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.4318361282348633}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.620621919631958}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.8329699039459229}]}, "tainting_time": {"total_time": 1.0860824584960938, "per_def_and_rule_time": {"mean": 0.010753291668278155, "std_dev": 0.0007090748445961833}, "very_slow_stats": {"time_ratio": 0.9613789030943767, "count_ratio": 0.1485148514851485}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "time": 0.055619001388549805}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.05639481544494629}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.express.security.express-wkhtml-injection.express-wkhtmltoimage-injection", "time": 0.05802607536315918}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.06020998954772949}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.06186103820800781}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.07009291648864746}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.07219505310058594}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.07308101654052734}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.12793302536010742}, {"fpath": "downloaded_repos/oveleon_contao-cookiebar/public/scripts/cookiebar.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.1414961814880371}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}