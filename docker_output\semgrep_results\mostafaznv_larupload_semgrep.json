{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/mostafaznv_larupload/src/Helpers/Utils.php", "start": {"line": 134, "col": 26, "offset": 3702}, "end": {"line": 134, "col": 43, "offset": 3719}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/mostafaznv_larupload/src/Helpers/Utils.php", "start": {"line": 148, "col": 18, "offset": 4135}, "end": {"line": 148, "col": 41, "offset": 4158}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/mostafaznv_larupload/src/Jobs/ProcessFFMpeg.php", "start": {"line": 38, "col": 33, "offset": 1133}, "end": {"line": 38, "col": 72, "offset": 1172}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "start": {"line": 224, "col": 18, "offset": 6841}, "end": {"line": 224, "col": 37, "offset": 6860}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 74, "col": 104, "offset": 2250}, "end": {"line": 74, "col": 107, "offset": 2253}}, {"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 78, "col": 18, "offset": 2250}, "end": {"line": 78, "col": 21, "offset": 2253}}, {"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 79, "col": 57, "offset": 2250}, "end": {"line": 79, "col": 60, "offset": 2253}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml:74:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 74, "col": 104, "offset": 2250}, "end": {"line": 74, "col": 107, "offset": 2253}}, {"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 78, "col": 18, "offset": 2250}, "end": {"line": 78, "col": 21, "offset": 2253}}, {"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 79, "col": 57, "offset": 2250}, "end": {"line": 79, "col": 60, "offset": 2253}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 86, "col": 76, "offset": 2746}, "end": {"line": 86, "col": 79, "offset": 2749}}, {"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 90, "col": 14, "offset": 2746}, "end": {"line": 90, "col": 17, "offset": 2749}}, {"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 92, "col": 63, "offset": 2746}, "end": {"line": 92, "col": 66, "offset": 2749}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml:86:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 86, "col": 76, "offset": 2746}, "end": {"line": 86, "col": 79, "offset": 2749}}, {"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 90, "col": 14, "offset": 2746}, "end": {"line": 90, "col": 17, "offset": 2749}}, {"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 92, "col": 63, "offset": 2746}, "end": {"line": 92, "col": 66, "offset": 2749}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 113, "col": 53, "offset": 3772}, "end": {"line": 113, "col": 69, "offset": 3788}}, {"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 113, "col": 97, "offset": 3772}, "end": {"line": 113, "col": 115, "offset": 3790}}, {"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 114, "col": 19, "offset": 3772}, "end": {"line": 114, "col": 22, "offset": 3775}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml:113:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 113, "col": 53, "offset": 3772}, "end": {"line": 113, "col": 69, "offset": 3788}}, {"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 113, "col": 97, "offset": 3772}, "end": {"line": 113, "col": 115, "offset": 3790}}, {"file": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "start": {"line": 114, "col": 19, "offset": 3772}, "end": {"line": 114, "col": 22, "offset": 3775}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/DeleteCoverAction.php", "start": {"line": 10, "col": 17, "offset": 0}, "end": {"line": 10, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/Cover/DeleteCoverAction.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/DeleteCoverAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/DeleteCoverAction.php", "start": {"line": 10, "col": 17, "offset": 0}, "end": {"line": 10, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 14, "col": 22, "offset": 0}, "end": {"line": 14, "col": 28, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 15, "col": 22, "offset": 0}, "end": {"line": 15, "col": 27, "offset": 5}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 22, "col": 41, "offset": 0}, "end": {"line": 22, "col": 49, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 22, "col": 78, "offset": 0}, "end": {"line": 22, "col": 86, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php:14:\n `string` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 14, "col": 22, "offset": 0}, "end": {"line": 14, "col": 28, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 15, "col": 22, "offset": 0}, "end": {"line": 15, "col": 27, "offset": 5}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 22, "col": 41, "offset": 0}, "end": {"line": 22, "col": 49, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "start": {"line": 22, "col": 78, "offset": 0}, "end": {"line": 22, "col": 86, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "start": {"line": 13, "col": 17, "offset": 0}, "end": {"line": 13, "col": 25, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "start": {"line": 14, "col": 26, "offset": 0}, "end": {"line": 14, "col": 31, "offset": 5}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php:13:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "start": {"line": 13, "col": 17, "offset": 0}, "end": {"line": 13, "col": 25, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "start": {"line": 14, "col": 26, "offset": 0}, "end": {"line": 14, "col": 31, "offset": 5}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php", "start": {"line": 16, "col": 79, "offset": 0}, "end": {"line": 16, "col": 87, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php", "start": {"line": 16, "col": 79, "offset": 0}, "end": {"line": 16, "col": 87, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "start": {"line": 18, "col": 26, "offset": 0}, "end": {"line": 18, "col": 32, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 32, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php:18:\n `string` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "start": {"line": 18, "col": 26, "offset": 0}, "end": {"line": 18, "col": 32, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 32, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 13, "col": 17, "offset": 0}, "end": {"line": 13, "col": 25, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 14, "col": 17, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php:13:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 13, "col": 17, "offset": 0}, "end": {"line": 13, "col": 25, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 14, "col": 17, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/GuessLaruploadFileTypeAction.php", "start": {"line": 10, "col": 41, "offset": 0}, "end": {"line": 10, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/GuessLaruploadFileTypeAction.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/GuessLaruploadFileTypeAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/GuessLaruploadFileTypeAction.php", "start": {"line": 10, "col": 41, "offset": 0}, "end": {"line": 10, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php", "start": {"line": 13, "col": 22, "offset": 0}, "end": {"line": 13, "col": 27, "offset": 5}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php:13:\n `array` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php", "start": {"line": 13, "col": 22, "offset": 0}, "end": {"line": 13, "col": 27, "offset": 5}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "start": {"line": 13, "col": 17, "offset": 0}, "end": {"line": 13, "col": 25, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "start": {"line": 14, "col": 17, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php:13:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "start": {"line": 13, "col": 17, "offset": 0}, "end": {"line": 13, "col": 25, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "start": {"line": 14, "col": 17, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 13, "col": 25, "offset": 0}, "end": {"line": 13, "col": 31, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 14, "col": 16, "offset": 0}, "end": {"line": 14, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 15, "col": 16, "offset": 0}, "end": {"line": 15, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 16, "col": 16, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 17, "col": 16, "offset": 0}, "end": {"line": 17, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 18, "col": 25, "offset": 0}, "end": {"line": 18, "col": 29, "offset": 4}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 19, "col": 25, "offset": 0}, "end": {"line": 19, "col": 29, "offset": 4}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 20, "col": 25, "offset": 0}, "end": {"line": 20, "col": 28, "offset": 3}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 21, "col": 16, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php:13:\n `string` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 13, "col": 25, "offset": 0}, "end": {"line": 13, "col": 31, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 14, "col": 16, "offset": 0}, "end": {"line": 14, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 15, "col": 16, "offset": 0}, "end": {"line": 15, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 16, "col": 16, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 17, "col": 16, "offset": 0}, "end": {"line": 17, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 18, "col": 25, "offset": 0}, "end": {"line": 18, "col": 29, "offset": 4}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 19, "col": 25, "offset": 0}, "end": {"line": 19, "col": 29, "offset": 4}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 20, "col": 25, "offset": 0}, "end": {"line": 20, "col": 28, "offset": 3}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "start": {"line": 21, "col": 16, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "start": {"line": 10, "col": 16, "offset": 0}, "end": {"line": 10, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "start": {"line": 11, "col": 16, "offset": 0}, "end": {"line": 11, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "start": {"line": 12, "col": 25, "offset": 0}, "end": {"line": 12, "col": 28, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "start": {"line": 10, "col": 16, "offset": 0}, "end": {"line": 10, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "start": {"line": 11, "col": 16, "offset": 0}, "end": {"line": 11, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "start": {"line": 12, "col": 25, "offset": 0}, "end": {"line": 12, "col": 28, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "start": {"line": 8, "col": 25, "offset": 0}, "end": {"line": 8, "col": 31, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "start": {"line": 9, "col": 25, "offset": 0}, "end": {"line": 9, "col": 31, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "start": {"line": 10, "col": 25, "offset": 0}, "end": {"line": 10, "col": 31, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php:8:\n `string` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "start": {"line": 8, "col": 25, "offset": 0}, "end": {"line": 8, "col": 31, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "start": {"line": 9, "col": 25, "offset": 0}, "end": {"line": 9, "col": 31, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "start": {"line": 10, "col": 25, "offset": 0}, "end": {"line": 10, "col": 31, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/AudioStyle.php", "start": {"line": 13, "col": 21, "offset": 0}, "end": {"line": 13, "col": 24, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/Style/AudioStyle.php:13:\n `Mp3` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/AudioStyle.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/AudioStyle.php", "start": {"line": 13, "col": 21, "offset": 0}, "end": {"line": 13, "col": 24, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/ImageStyle.php", "start": {"line": 10, "col": 12, "offset": 0}, "end": {"line": 10, "col": 20, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/Style/ImageStyle.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/ImageStyle.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/ImageStyle.php", "start": {"line": 10, "col": 12, "offset": 0}, "end": {"line": 10, "col": 20, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 12, "col": 25, "offset": 0}, "end": {"line": 12, "col": 31, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 13, "col": 25, "offset": 0}, "end": {"line": 13, "col": 28, "offset": 3}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 14, "col": 25, "offset": 0}, "end": {"line": 14, "col": 28, "offset": 3}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 15, "col": 25, "offset": 0}, "end": {"line": 15, "col": 29, "offset": 4}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 16, "col": 16, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 17, "col": 25, "offset": 0}, "end": {"line": 17, "col": 29, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php:12:\n `string` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 12, "col": 25, "offset": 0}, "end": {"line": 12, "col": 31, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 13, "col": 25, "offset": 0}, "end": {"line": 13, "col": 28, "offset": 3}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 14, "col": 25, "offset": 0}, "end": {"line": 14, "col": 28, "offset": 3}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 15, "col": 25, "offset": 0}, "end": {"line": 15, "col": 29, "offset": 4}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 16, "col": 16, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "start": {"line": 17, "col": 25, "offset": 0}, "end": {"line": 17, "col": 29, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 11, "col": 25, "offset": 0}, "end": {"line": 11, "col": 31, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 12, "col": 16, "offset": 0}, "end": {"line": 12, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 13, "col": 16, "offset": 0}, "end": {"line": 13, "col": 24, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 14, "col": 25, "offset": 0}, "end": {"line": 14, "col": 29, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php:11:\n `string` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 11, "col": 25, "offset": 0}, "end": {"line": 11, "col": 31, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 12, "col": 16, "offset": 0}, "end": {"line": 12, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 13, "col": 16, "offset": 0}, "end": {"line": 13, "col": 24, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "start": {"line": 14, "col": 25, "offset": 0}, "end": {"line": 14, "col": 29, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php", "start": {"line": 18, "col": 12, "offset": 0}, "end": {"line": 18, "col": 20, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php", "start": {"line": 19, "col": 21, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php", "start": {"line": 18, "col": 12, "offset": 0}, "end": {"line": 18, "col": 20, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php", "start": {"line": 19, "col": 21, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "start": {"line": 28, "col": 13, "offset": 0}, "end": {"line": 28, "col": 21, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "start": {"line": 30, "col": 22, "offset": 0}, "end": {"line": 30, "col": 28, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "start": {"line": 32, "col": 22, "offset": 0}, "end": {"line": 32, "col": 25, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "start": {"line": 28, "col": 13, "offset": 0}, "end": {"line": 28, "col": 21, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "start": {"line": 30, "col": 22, "offset": 0}, "end": {"line": 30, "col": 28, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "start": {"line": 32, "col": 22, "offset": 0}, "end": {"line": 32, "col": 25, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php", "start": {"line": 22, "col": 50, "offset": 0}, "end": {"line": 22, "col": 56, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php", "start": {"line": 22, "col": 83, "offset": 0}, "end": {"line": 22, "col": 89, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php:22:\n `FFMpeg` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php", "start": {"line": 22, "col": 50, "offset": 0}, "end": {"line": 22, "col": 56, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php", "start": {"line": 22, "col": 83, "offset": 0}, "end": {"line": 22, "col": 89, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 20, "col": 15, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 22, "col": 15, "offset": 0}, "end": {"line": 22, "col": 23, "offset": 8}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 24, "col": 24, "offset": 0}, "end": {"line": 24, "col": 30, "offset": 6}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 26, "col": 24, "offset": 0}, "end": {"line": 26, "col": 28, "offset": 4}}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 28, "col": 24, "offset": 0}, "end": {"line": 28, "col": 27, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Storage/Image.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 20, "col": 15, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 22, "col": 15, "offset": 0}, "end": {"line": 22, "col": 23, "offset": 8}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 24, "col": 24, "offset": 0}, "end": {"line": 24, "col": 30, "offset": 6}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 26, "col": 24, "offset": 0}, "end": {"line": 26, "col": 28, "offset": 4}}, {"file": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "start": {"line": 28, "col": 24, "offset": 0}, "end": {"line": 28, "col": 27, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentCover.php", "start": {"line": 10, "col": 41, "offset": 0}, "end": {"line": 10, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentCover.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentCover.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentCover.php", "start": {"line": 10, "col": 41, "offset": 0}, "end": {"line": 10, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentProxy.php", "start": {"line": 13, "col": 41, "offset": 0}, "end": {"line": 13, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentProxy.php:13:\n `readonly` was unexpected", "path": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentProxy.php", "spans": [{"file": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentProxy.php", "start": {"line": 13, "col": 41, "offset": 0}, "end": {"line": 13, "col": 49, "offset": 8}}]}], "paths": {"scanned": ["downloaded_repos/mostafaznv_larupload/.gitattributes", "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "downloaded_repos/mostafaznv_larupload/.gitignore", "downloaded_repos/mostafaznv_larupload/LICENSE", "downloaded_repos/mostafaznv_larupload/README.md", "downloaded_repos/mostafaznv_larupload/composer.json", "downloaded_repos/mostafaznv_larupload/config/config.php", "downloaded_repos/mostafaznv_larupload/docs/.gitbook/assets/file.excalidraw.svg", "downloaded_repos/mostafaznv_larupload/docs/README.md", "downloaded_repos/mostafaznv_larupload/docs/SUMMARY.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/README.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/complete-example.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/cover-style.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/disk.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/dominant-color-quality.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/dominant-color.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/generate-cover.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/image-processing-library.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/keep-old-files.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/lang.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/media-styles.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/naming-method.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/optimize-image.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/preserve-files.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/secureids-method.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/store-original-file-name.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/attachment/with-meta.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/concepts.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/README.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/camel-case-response.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/cover-style.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/disk.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/dominant-color-quality.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/dominant-color.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/README.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/ffmpeg-binaries.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/ffmpeg-capture-frame.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/ffmpeg-log-channel.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/ffmpeg-max-queue-number.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/ffmpeg-queue.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/ffmpeg-threads.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/ffmpeg/ffmpeg-timeout.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/generate-cover.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/hide-table-columns.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/image-processing-library.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/keep-old-files.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/lang.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/local-disk.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/mode.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/naming-method.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/optimize-image.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/preserve-files.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/secureids.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/store-original-file-name.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/configuration/with-meta.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/migrations/README.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/migrations/add-original-file-name-to-existing-tables.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/migrations/heavy-columns.md", "downloaded_repos/mostafaznv_larupload/docs/advanced-usage/migrations/light-columns.md", "downloaded_repos/mostafaznv_larupload/docs/api-resources.md", "downloaded_repos/mostafaznv_larupload/docs/basic-usage/database-preparation.md", "downloaded_repos/mostafaznv_larupload/docs/basic-usage/model-preparation.md", "downloaded_repos/mostafaznv_larupload/docs/basic-usage/upload.md", "downloaded_repos/mostafaznv_larupload/docs/cover/README.md", "downloaded_repos/mostafaznv_larupload/docs/cover/delete-cover.md", "downloaded_repos/mostafaznv_larupload/docs/cover/update-cover.md", "downloaded_repos/mostafaznv_larupload/docs/cover/upload-cover.md", "downloaded_repos/mostafaznv_larupload/docs/delete.md", "downloaded_repos/mostafaznv_larupload/docs/download/README.md", "downloaded_repos/mostafaznv_larupload/docs/download/generate-download-link/README.md", "downloaded_repos/mostafaznv_larupload/docs/download/generate-download-link/generate-url-for-all-styles.md", "downloaded_repos/mostafaznv_larupload/docs/download/generate-download-link/generate-url-for-particular-style.md", "downloaded_repos/mostafaznv_larupload/docs/download/generate-download-response.md", "downloaded_repos/mostafaznv_larupload/docs/get-attachments.md", "downloaded_repos/mostafaznv_larupload/docs/getting-started/installation.md", "downloaded_repos/mostafaznv_larupload/docs/getting-started/laravel-nova-integration.md", "downloaded_repos/mostafaznv_larupload/docs/getting-started/license.md", "downloaded_repos/mostafaznv_larupload/docs/getting-started/support-us.md", "downloaded_repos/mostafaznv_larupload/docs/image-optimization.md", "downloaded_repos/mostafaznv_larupload/docs/meta.md", "downloaded_repos/mostafaznv_larupload/docs/other/migration.md", "downloaded_repos/mostafaznv_larupload/docs/queue-ffmpeg-processes/README.md", "downloaded_repos/mostafaznv_larupload/docs/queue-ffmpeg-processes/ffmpeg-queue-relationships.md", "downloaded_repos/mostafaznv_larupload/docs/queue-ffmpeg-processes/job-completion-event.md", "downloaded_repos/mostafaznv_larupload/docs/secureids.md", "downloaded_repos/mostafaznv_larupload/docs/standalone-uploader/cover/README.md", "downloaded_repos/mostafaznv_larupload/docs/standalone-uploader/cover/delete-cover.md", "downloaded_repos/mostafaznv_larupload/docs/standalone-uploader/cover/update-cover.md", "downloaded_repos/mostafaznv_larupload/docs/standalone-uploader/customization.md", "downloaded_repos/mostafaznv_larupload/docs/standalone-uploader/delete.md", "downloaded_repos/mostafaznv_larupload/docs/standalone-uploader/introduction.md", "downloaded_repos/mostafaznv_larupload/docs/standalone-uploader/upload.md", "downloaded_repos/mostafaznv_larupload/docs/upload.md", "downloaded_repos/mostafaznv_larupload/migrations/2019_05_20_150317_create_ffmpeg_queue_table.php", "downloaded_repos/mostafaznv_larupload/phpunit.xml", "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/DeleteCoverAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/GuessLaruploadFileTypeAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php", "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/BaseLarupload.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/LaruploadObservers.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/LaruploadRelations.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/LaruploadTransformers.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Standalone/BaseStandaloneLarupload.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Standalone/BootStandaloneLarupload.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Standalone/StandaloneLaruploadCallables.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Standalone/StandaloneLaruploadCover.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Standalone/StandaloneLaruploadNotCallables.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/Attachment/AttachmentActions.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/Attachment/AttachmentEvents.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/Attachment/BaseAttachment.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/Attachment/CoverAttachment.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/Attachment/QueueAttachment.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/Attachment/RetrieveAttachment.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/Attachment/StyleAttachment.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/BaseUploadEntity.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/FFMpegUploadEntity.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/ImageUploadEntity.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/UploadEntityFileSystem.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/UploadEntityName.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/UploadEntityProperties.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/UploadEntityResponse.php", "downloaded_repos/mostafaznv_larupload/src/Concerns/Storage/UploadEntity/UploadEntityStyle.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/AudioStyle.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/ImageStyle.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php", "downloaded_repos/mostafaznv_larupload/src/Database/Schema/Blueprint.php", "downloaded_repos/mostafaznv_larupload/src/Enums/LaruploadFileType.php", "downloaded_repos/mostafaznv_larupload/src/Enums/LaruploadImageLibrary.php", "downloaded_repos/mostafaznv_larupload/src/Enums/LaruploadMediaStyle.php", "downloaded_repos/mostafaznv_larupload/src/Enums/LaruploadMode.php", "downloaded_repos/mostafaznv_larupload/src/Enums/LaruploadNamingMethod.php", "downloaded_repos/mostafaznv_larupload/src/Enums/LaruploadSecureIdsMethod.php", "downloaded_repos/mostafaznv_larupload/src/Events/LaruploadFFMpegQueueFinished.php", "downloaded_repos/mostafaznv_larupload/src/Helpers/Slug.php", "downloaded_repos/mostafaznv_larupload/src/Helpers/Utils.php", "downloaded_repos/mostafaznv_larupload/src/Jobs/ProcessFFMpeg.php", "downloaded_repos/mostafaznv_larupload/src/Larupload.php", "downloaded_repos/mostafaznv_larupload/src/LaruploadServiceProvider.php", "downloaded_repos/mostafaznv_larupload/src/Models/LaruploadFFMpegQueue.php", "downloaded_repos/mostafaznv_larupload/src/Storage/Attachment.php", "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php", "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentCover.php", "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentProxy.php", "downloaded_repos/mostafaznv_larupload/src/Traits/Larupload.php", "downloaded_repos/mostafaznv_larupload/src/UploadEntities.php", "downloaded_repos/mostafaznv_larupload/svgo.config.js", "downloaded_repos/mostafaznv_larupload/translations/en/messages.php"], "skipped": [{"path": "downloaded_repos/mostafaznv_larupload/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/DeleteCoverAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/GenerateCoverFromFileAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/SetCoverAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/Cover/UploadCoverAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/FixExceptionNamesAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/GenerateFileIdAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/GuessLaruploadFileTypeAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/OptimizeImageAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Actions/SetFileNameAction.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/CoverActionData.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegMeta.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/FFMpeg/FFMpegStreamRepresentation.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/AudioStyle.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/ImageStyle.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/StreamStyle.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/Style.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/DTOs/Style/VideoStyle.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/FFMpeg.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/FFMpeg/HLS.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Image.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentCover.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/src/Storage/Proxy/AttachmentProxy.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Datasets/Models.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/AudioStyleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/BlueprintTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/CoverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/DeleteTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/DominantColorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/DownloadTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/FFMpegQueueTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/FileNameTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/FileSizeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/FolderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/HideColumnsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/ImageDimensionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/MagicPropertiesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/OptimizeImageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/ResponseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/SecureIdsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/ServiceProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/UpdateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/UploadEntityTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/UploadTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Feature/VideoStyleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/audio-1.mp3", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/compress.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/farsi-name.jpeg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/image.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/image.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/image.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/image.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/image.webp", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/pdf-1.pdf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/php.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/square-image.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/vertical-image.jpeg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Data/video-1.mp4", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Enums/LaruploadTestModels.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/LaruploadTestConsts.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/LaruploadTestTablesMigration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Models/LaruploadHeavyTestModel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Models/LaruploadLightTestModel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Models/LaruploadQueueTestModel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Models/LaruploadRemoteQueueTestModel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Models/LaruploadSoftDeleteTestModel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/Models/Traits/TestAttachments.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Support/TestAttachmentBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/AudioStyleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/FFMpegTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/FixExceptionNamesActionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/GuessLaruploadFileTypeActionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/ImageStyleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/ImageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/OptimizeImageActionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/SlugTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/StandaloneLaruploadNotCallablesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/StreamStyleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/UtilsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mostafaznv_larupload/tests/Unit/VideoStyleTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.586564064025879, "profiling_times": {"config_time": 5.773064374923706, "core_time": 2.744572877883911, "ignores_time": 0.0017201900482177734, "total_time": 8.520253658294678}, "parsing_time": {"total_time": 0.5960743427276611, "per_file_time": {"mean": 0.00863875859025596, "std_dev": 0.0001417981031557382}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.3562977313995361, "per_file_time": {"mean": 0.0034866265588677013, "std_dev": 9.598588744027162e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.1579124927520752, "per_file_and_rule_time": {"mean": 0.0012241278507912807, "std_dev": 4.494720242730675e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.006644010543823242, "per_def_and_rule_time": {"mean": 0.00022910381185597386, "std_dev": 1.1263839391237148e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}