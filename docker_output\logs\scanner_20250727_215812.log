🐳 UNIFIED VULNERABILITY SCANNER - CONTAINER MODE
============================================================
Running inside Docker container with integrated Semgrep

✅ Using direct Semgrep execution (container mode)
🔍 Searching GitHub for vulnerable repositories...
  📋 Scanning PHP repositories...
  📋 Scanning JavaScript repositories...
    ⚠️ Rate limited, waiting...
  📋 Scanning Python repositories...
  📋 Scanning Ruby repositories...
    ⚠️ Rate limited, waiting...
  📋 Scanning Java repositories...
    ⚠️ Rate limited, waiting...
  ✅ Found 417 potentially vulnerable repositories
🎯 Limited to 20 repositories for analysis (will keep top 10)

🚀 Processing 20 repositories...
   Max concurrent scans: 1

🎯 Processing: uasoft-indonesia/badaso
    🔗 Clone URL: https://github.com/uasoft-indonesia/badaso.git
    📥 Cloning uasoft-indonesia/badaso...
    ✅ Successfully cloned uasoft-indonesia/badaso
    🔍 Running Semgrep on uasoft-indonesia/badaso...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/uasoft-indonesia_badaso_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 88 potential vulnerabilities
    🧹 Cleaned up uasoft-indonesia/badaso
    ✅ uasoft-indonesia/badaso - Final Score: 100.0 (Risk: 59, Semgrep: 100)

🎯 Processing: moonshine-software/moonshine  ✅ Completed: uasoft-indonesia/badaso (Score: 100.0)

    🔗 Clone URL: https://github.com/moonshine-software/moonshine.git
    📥 Cloning moonshine-software/moonshine...
    ✅ Successfully cloned moonshine-software/moonshine
    🔍 Running Semgrep on moonshine-software/moonshine...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/moonshine-software_moonshine_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 16 potential vulnerabilities
    🧹 Cleaned up moonshine-software/moonshine
    ✅ moonshine-software/moonshine - Final Score: 100.0 (Risk: 56, Semgrep: 100)

🎯 Processing: alperenersoy/filament-export
    🔗 Clone URL: https://github.com/alperenersoy/filament-export.git
    📥 Cloning alperenersoy/filament-export...
  ✅ Completed: moonshine-software/moonshine (Score: 100.0)
    ✅ Successfully cloned alperenersoy/filament-export
    🔍 Running Semgrep on alperenersoy/filament-export...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/alperenersoy_filament-export_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up alperenersoy/filament-export
    ✅ alperenersoy/filament-export - Final Score: 44.0 (Risk: 44, Semgrep: 0)

🎯 Processing: stephenjude/filament-blog
    🔗 Clone URL: https://github.com/stephenjude/filament-blog.git  ✅ Completed: alperenersoy/filament-export (Score: 44.0)

    📥 Cloning stephenjude/filament-blog...
    ✅ Successfully cloned stephenjude/filament-blog
    🔍 Running Semgrep on stephenjude/filament-blog...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/stephenjude_filament-blog_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up stephenjude/filament-blog
    ✅ stephenjude/filament-blog - Final Score: 40.0 (Risk: 40, Semgrep: 0)

🎯 Processing: JibayMcs/filament-tour
    🔗 Clone URL: https://github.com/JibayMcs/filament-tour.git
    📥 Cloning JibayMcs/filament-tour...  ✅ Completed: stephenjude/filament-blog (Score: 40.0)

    ✅ Successfully cloned JibayMcs/filament-tour
    🔍 Running Semgrep on JibayMcs/filament-tour...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/JibayMcs_filament-tour_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up JibayMcs/filament-tour
    ✅ JibayMcs/filament-tour - Final Score: 50.5 (Risk: 35, Semgrep: 31)

🎯 Processing: et-nik/gameap  ✅ Completed: JibayMcs/filament-tour (Score: 50.5)
    🔗 Clone URL: https://github.com/et-nik/gameap.git
    📥 Cloning et-nik/gameap...

    ✅ Successfully cloned et-nik/gameap
    🔍 Running Semgrep on et-nik/gameap...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/et-nik_gameap_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 24 potential vulnerabilities
    🧹 Cleaned up et-nik/gameap
    ✅ et-nik/gameap - Final Score: 90.0 (Risk: 40, Semgrep: 100)

🎯 Processing: saade/filament-laravel-log  ✅ Completed: et-nik/gameap (Score: 90.0)

    🔗 Clone URL: https://github.com/saade/filament-laravel-log.git
    📥 Cloning saade/filament-laravel-log...
    ✅ Successfully cloned saade/filament-laravel-log
    🔍 Running Semgrep on saade/filament-laravel-log...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/saade_filament-laravel-log_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up saade/filament-laravel-log
    ✅ saade/filament-laravel-log - Final Score: 40.5 (Risk: 40, Semgrep: 1)

🎯 Processing: aymanalhattami/filament-context-menu  ✅ Completed: saade/filament-laravel-log (Score: 40.5)

    🔗 Clone URL: https://github.com/aymanalhattami/filament-context-menu.git
    📥 Cloning aymanalhattami/filament-context-menu...
    ✅ Successfully cloned aymanalhattami/filament-context-menu
    🔍 Running Semgrep on aymanalhattami/filament-context-menu...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/aymanalhattami_filament-context-menu_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 5 potential vulnerabilities
    🧹 Cleaned up aymanalhattami/filament-context-menu
    ✅ aymanalhattami/filament-context-menu - Final Score: 38.0 (Risk: 25, Semgrep: 26)

🎯 Processing: stephenjude/filament-debugger  ✅ Completed: aymanalhattami/filament-context-menu (Score: 38.0)

    🔗 Clone URL: https://github.com/stephenjude/filament-debugger.git
    📥 Cloning stephenjude/filament-debugger...
    ✅ Successfully cloned stephenjude/filament-debugger
    🔍 Running Semgrep on stephenjude/filament-debugger...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/stephenjude_filament-debugger_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up stephenjude/filament-debugger
    ✅ stephenjude/filament-debugger - Final Score: 30.0 (Risk: 30, Semgrep: 0)

🎯 Processing: codepress/admin-columns
    🔗 Clone URL: https://github.com/codepress/admin-columns.git
    📥 Cloning codepress/admin-columns...
  ✅ Completed: stephenjude/filament-debugger (Score: 30.0)
    ✅ Successfully cloned codepress/admin-columns
    🔍 Running Semgrep on codepress/admin-columns...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/codepress_admin-columns_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 36 potential vulnerabilities
    🧹 Cleaned up codepress/admin-columns
    ✅ codepress/admin-columns - Final Score: 83.0 (Risk: 33, Semgrep: 100)

🎯 Processing: ashraf-kabir/personal-blog
    🔗 Clone URL: https://github.com/ashraf-kabir/personal-blog.git
    📥 Cloning ashraf-kabir/personal-blog...
  ✅ Completed: codepress/admin-columns (Score: 83.0)
    ✅ Successfully cloned ashraf-kabir/personal-blog
    🔍 Running Semgrep on ashraf-kabir/personal-blog...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ashraf-kabir_personal-blog_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up ashraf-kabir/personal-blog
    ✅ ashraf-kabir/personal-blog - Final Score: 58.5 (Risk: 36, Semgrep: 45)

🎯 Processing: fsi-open/admin-bundle
  ✅ Completed: ashraf-kabir/personal-blog (Score: 58.5)
    🔗 Clone URL: https://github.com/fsi-open/admin-bundle.git
    📥 Cloning fsi-open/admin-bundle...
    ✅ Successfully cloned fsi-open/admin-bundle
    🔍 Running Semgrep on fsi-open/admin-bundle...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/fsi-open_admin-bundle_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up fsi-open/admin-bundle
    ✅ fsi-open/admin-bundle - Final Score: 45.0 (Risk: 25, Semgrep: 40)

🎯 Processing: solutionforest/filament-scaffold  ✅ Completed: fsi-open/admin-bundle (Score: 45.0)
    🔗 Clone URL: https://github.com/solutionforest/filament-scaffold.git
    📥 Cloning solutionforest/filament-scaffold...

    ✅ Successfully cloned solutionforest/filament-scaffold
    🔍 Running Semgrep on solutionforest/filament-scaffold...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/solutionforest_filament-scaffold_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up solutionforest/filament-scaffold
    ✅ solutionforest/filament-scaffold - Final Score: 13.5 (Risk: 13, Semgrep: 1)

🎯 Processing: dustin10/VichUploaderBundle  ✅ Completed: solutionforest/filament-scaffold (Score: 13.5)

    🔗 Clone URL: https://github.com/dustin10/VichUploaderBundle.git
    📥 Cloning dustin10/VichUploaderBundle...
    ✅ Successfully cloned dustin10/VichUploaderBundle
    🔍 Running Semgrep on dustin10/VichUploaderBundle...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/dustin10_VichUploaderBundle_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up dustin10/VichUploaderBundle
    ✅ dustin10/VichUploaderBundle - Final Score: 30.0 (Risk: 30, Semgrep: 0)

🎯 Processing: spatie/livewire-filepond
  ✅ Completed: dustin10/VichUploaderBundle (Score: 30.0)
    🔗 Clone URL: https://github.com/spatie/livewire-filepond.git
    📥 Cloning spatie/livewire-filepond...
    ✅ Successfully cloned spatie/livewire-filepond
    🔍 Running Semgrep on spatie/livewire-filepond...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/spatie_livewire-filepond_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up spatie/livewire-filepond
    ✅ spatie/livewire-filepond - Final Score: 31.0 (Risk: 31, Semgrep: 0)

🎯 Processing: kleeja-official/kleeja
    🔗 Clone URL: https://github.com/kleeja-official/kleeja.git
    📥 Cloning kleeja-official/kleeja...  ✅ Completed: spatie/livewire-filepond (Score: 31.0)

    ✅ Successfully cloned kleeja-official/kleeja
    🔍 Running Semgrep on kleeja-official/kleeja...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/kleeja-official_kleeja_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 83 potential vulnerabilities
    🧹 Cleaned up kleeja-official/kleeja
    ✅ kleeja-official/kleeja - Final Score: 85.0 (Risk: 35, Semgrep: 100)

🎯 Processing: Ecodev/graphql-upload
    🔗 Clone URL: https://github.com/Ecodev/graphql-upload.git
    📥 Cloning Ecodev/graphql-upload...
  ✅ Completed: kleeja-official/kleeja (Score: 85.0)
    ✅ Successfully cloned Ecodev/graphql-upload
    🔍 Running Semgrep on Ecodev/graphql-upload...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Ecodev_graphql-upload_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up Ecodev/graphql-upload
    ✅ Ecodev/graphql-upload - Final Score: 25.0 (Risk: 25, Semgrep: 0)

🎯 Processing: mostafaznv/larupload  ✅ Completed: Ecodev/graphql-upload (Score: 25.0)
    🔗 Clone URL: https://github.com/mostafaznv/larupload.git

    📥 Cloning mostafaznv/larupload...
    ✅ Successfully cloned mostafaznv/larupload
    🔍 Running Semgrep on mostafaznv/larupload...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mostafaznv_larupload_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up mostafaznv/larupload
    ✅ mostafaznv/larupload - Final Score: 35.0 (Risk: 25, Semgrep: 20)

🎯 Processing: TypiCMS/Base
    🔗 Clone URL: https://github.com/TypiCMS/Base.git
    📥 Cloning TypiCMS/Base...
  ✅ Completed: mostafaznv/larupload (Score: 35.0)
    ✅ Successfully cloned TypiCMS/Base
    🔍 Running Semgrep on TypiCMS/Base...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/TypiCMS_Base_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up TypiCMS/Base
    ✅ TypiCMS/Base - Final Score: 28.0 (Risk: 28, Semgrep: 0)

🎯 Processing: danpros/htmly  ✅ Completed: TypiCMS/Base (Score: 28.0)

    🔗 Clone URL: https://github.com/danpros/htmly.git
    📥 Cloning danpros/htmly...
    ✅ Successfully cloned danpros/htmly
    🔍 Running Semgrep on danpros/htmly...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/danpros_htmly_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 93 potential vulnerabilities
    🧹 Cleaned up danpros/htmly
    ✅ danpros/htmly - Final Score: 78.0 (Risk: 28, Semgrep: 100)
  ✅ Completed: danpros/htmly (Score: 78.0)

📊 Saving results...
  🎯 Keeping top 10 repositories out of 20 analyzed
  ✅ CSV saved successfully: unified_vulnerability_results.csv
  ✅ Top 10 results: unified_vulnerability_results.csv
  📋 Detailed log: unified_scan_log.json

🏆 TOP 10 REPOSITORIES:
   1. uasoft-indonesia/badaso - Score: 100.0 (88 vulns)
   2. moonshine-software/moonshine - Score: 100.0 (16 vulns)
   3. et-nik/gameap - Score: 90.0 (24 vulns)
   4. kleeja-official/kleeja - Score: 85.0 (83 vulns)
   5. codepress/admin-columns - Score: 83.0 (36 vulns)
   6. danpros/htmly - Score: 78.0 (93 vulns)
   7. ashraf-kabir/personal-blog - Score: 58.5 (6 vulns)
   8. JibayMcs/filament-tour - Score: 50.5 (4 vulns)
   9. fsi-open/admin-bundle - Score: 45.0 (8 vulns)
  10. alperenersoy/filament-export - Score: 44.0 (0 vulns)

📊 SCAN SUMMARY
========================================
Repositories found: 417
Repositories cloned: 20
Semgrep scans completed: 20
Total vulnerabilities found: 369
High-risk repositories: 10
Average final score: 52.2

Top vulnerability categories:
  Other: 13
  Command Injection: 4
  XSS: 2
  Hardcoded Secrets: 2
  Path Traversal: 1

🎯 NEXT STEPS:
1. Review high_priority_unified_results.csv for immediate targets
2. Check Semgrep results in semgrep_results/ directory
3. Focus on repositories with both high risk scores and Semgrep findings
4. Follow responsible disclosure practices
