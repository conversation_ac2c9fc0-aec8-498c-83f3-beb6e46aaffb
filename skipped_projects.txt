shopperlabs/shopper - Headless e-commerce administration built with <PERSON><PERSON> to create and manage online store. \nRamonSilva20/mapos - Sistema de Controle de Ordens de Serviço\npH7Software/pH7-Social-Dating-CMS - 😻 pH7Builder (formerly pH7CMS) is a Professional & Open Source Social Dating CMS written in PHP 8 🚀 This Social Dating Script aims to be low resource-intensive, powerful and secure. pH7Builder includes over 40 modules. It is the first Professional, Free & Open Source Social Dating Site Builder Software and the first choice for enterprise level Da\ncknow/laravel-money - Laravel Money.\nakeneo/pim-community-dev - [Community Development Repository] The open source Product Information Management (PIM)\nspatie/laravel-failed-job-monitor - Get notified when a queued job fails\nzyx0814/Pichome - 一款图片与媒体文件管理功能强大的开源网盘程序\noroinc/crm-application - OroCRM - an open-source Customer Relationship Management application.\njikan-me/jikan - Unofficial MyAnimeList PHP+REST API which provides functions other than the official API\nmaglnet/ComposerRequireChecker - A CLI tool to check whether a specific composer package uses imported symbols that aren't part of its direct composer dependencies\nopnsense/plugins - OPNsense plugin collection\nnikic/php-ast - Extension exposing PHP 7 abstract syntax tree\nphp-mime-mail-parser/php-mime-mail-parser - A fully tested email parser for PHP 8.0+ (mailparse extension wrapper).\nsymfony/ux - Symfony UX initiative: a JavaScript ecosystem for Symfony\nsymfony/webpack-encore-bundle - Symfony integration with Webpack Encore!\nspatie/laravel-feed - Easily generate RSS feeds\nphp-lock/lock - Popular PHP library for serialized execution of critical code in concurrent situations\nsolariumphp/solarium - PHP Solr client library\nnetcccyun/dnsmgr - 彩虹聚合DNS管理系统\nnunomaduro/skeleton-php - ⚡️ This package provides a wonderful PHP skeleton to start building your next package idea.\njeffgreco13/filament-breezy - None\nweb-token/jwt-framework - JWT Framework\nmanualdousuario/marreta - Uma ferramenta que quebra barreiras de acesso e elementos que atrapalham a leitura! / Marreta is a tool that removes access barriers and visual distractions!\nnnjeim/world - A Laravel package which provides a list of the countries, states, cities, currencies, timezones and languages.\nnette/neon - 🍸 Encodes and decodes NEON file format.\nLabs64/laravel-boilerplate - Laravel Boilerplate / Starter Kit with Gentelella Admin Theme\nPaymenter/Paymenter - Free and open-source webshop solution for hostings\nbs-community/blessing-skin-server - Web application brings your custom skins back in offline Minecraft servers.\nnetz98/n98-magerun2 - The swiss army knife for Magento developers, sysadmins and devops. The tool provides a huge set of well tested command line commands which save hours of work time. All commands are extendable by a module API.\nTzwcard/ChinaTelecom-GuangdongIPTV-RTP-List - 广州电信广东IPTV列表（IGMP/RTP组播地址）\nryangjchandler/orbit - A flat-file database driver for Eloquent. 🗄\nroots/acorn - Integrate Laravel functionality into WordPress projects\nushahidi/Ushahidi_Web - Ushahidi v2. A platform that allows information collection, visualization and interactive mapping, allowing anyone to submit information through text messaging using a mobile phone, email or web form.\nFanchangWang/allinone_format - 本项目是对 https://hub.docker.com/r/youshandefeiyang/allinone /tv.m3u、/tptv.m3u、/migu.m3u 进行聚合 & 重新分组。\ntwigphp/twig-extra-bundle - The Twig bundle for official extra extensions\nrcrowe/TwigBridge - Give the power of Twig to Laravel\nbenjaminjonard/koillection - Koillection is a self-hosted service allowing users to manage any kind of collections.\nsymfony/polyfill-php81 - Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions\nlaravel/slack-notification-channel - Slack Notification Channel for laravel.\nspatie/laravel-package-tools - Tools for creating Laravel packages\nglorand/laravel-model-settings - Model Settings for your Laravel app\nbuggregator/server - Buggregator is a lightweight, standalone server that offers a range of debugging features for PHP applications.\nlaradumps/laradumps - 🛻 LaraDumps is a friendly app designed to boost your Laravel PHP coding and debugging experience.\nspatie/phpunit-watcher - A tool to automatically rerun PHPUnit tests when source code changes\ndspurl/tfshop - vue+php+uniapp轻量级多语言易二开跨终端商城系统,低代码，完全前后端分离，免费开源可商用，H5商城电商平台，微信小程序商城电商平台；支持H5、微信小程序，支付宝小程序、百度小程序、字节跳动小程序、安卓、IOS等等\nfacebook/facebook-php-business-sdk - PHP SDK for Meta Marketing API\naarondfrancis/sidecar - Deploy and execute AWS Lambda functions from your Laravel application.\nthelia/thelia - Thelia is an open source tool for creating e-business websites and managing online content. Repo containing the new major version (v2)\nspatie/laravel-event-sourcing - The easiest way to get started with event sourcing in Laravel\nlaravel/pail - Dive into your Laravel application's log files directly from the console. 🪣\nyajra/laravel-oci8 - Oracle DB driver for Laravel via OCI8\nopen-telemetry/opentelemetry-php - The OpenTelemetry PHP Library\nEventSaucePHP/EventSauce - A pragmatic event sourcing library for PHP with a focus on developer experience.\nnunomaduro/essentials - Just better defaults for your Laravel projects.\nvanilophp/framework - The truly Laravel E-commerce Framework\ngodruoyi/php-snowflake - ❄ An ID Generator for PHP based on Snowflake Algorithm (Twitter announced).\ndriftingly/rector-laravel - Rector upgrades rules for Laravel\nqruto/laravel-wave - Painless Laravel Broadcasting with SSE\nspatie/laravel-route-attributes - Use PHP 8 attributes to register routes in a Laravel app\nlaravel-doctrine/orm - An integration library for Laravel and Doctrine ORM\nhotwired-laravel/turbo-laravel - This package gives you a set of conventions to make the most out of Hotwire in Laravel.\nspatie/laravel-server-monitor - Don't let your servers just melt down\nspatie/laravel-pdf - Create PDF files in Laravel apps\nphparkitect/arkitect - Put your architectural rules under test!\nmulticaret/laravel-acquaintances - This package gives Eloquent models the ability to manage friendships (with groups), followships along with Likes, favorites..etc.\namphp/parallel - An advanced parallelization library for PHP, enabling efficient multitasking, optimizing resource use, and application responsiveness through multiple CPU threads.\nspatie/ssh - A lightweight package to execute commands over an SSH connection\nwapmorgan/Morphos - A morphological solution for Russian and English language written completely in PHP. Provides classes to inflect personal names, geographical names, decline and pluralize nouns, generate cardinal and ordinal numerals, spell out money amounts and time intervals. / Морфологическая библиотека для английского и русского языков. Склоняет имена собственные, названия географических объектов, склонение и плюрализация имен собственных и другое.\nDOMjudge/domjudge - DOMjudge programming contest jury system\njvoisin/snuffleupagus - Security module for php7 and php8 - Killing bugclasses and virtual-patching the rest!\nJaguarJack/catch-admin - CatchAdmin是一个基于对Laravel和Element Plus进行二次开发的后台管理系统。CatchAdmin仍然采用传统的前后端分离策略，其中Laravel框架仅用作Api输出。通过这种设计，成功将管理系统模块之间的耦合降至最低。\nfuel/core - Fuel PHP Framework - The core of the Fuel v1 framework\nspatie/laravel-html - Painless html generation\nspatie/geocoder - Geocode addresses to coordinates\nSmile-SA/elasticsuite - Smile ElasticSuite - Magento 2 merchandising and search engine built on ElasticSearch\nspatie/laravel-health - Check the health of your Laravel app\nspatie/laravel-csp - Set content security policy headers in a Laravel app\nconcretecms/concretecms - Official repository for Concrete CMS development\nMarcinOrlowski/laravel-api-response-builder - Builds nice, normalized and easy to consume REST JSON responses for Laravel powered APIs.\nromanzipp/Laravel-Queue-Monitor - Monitoring Laravel Jobs with your Database\nstatamic/statamic - Statamic: The New Site/App Package\nalaouy/YouTube - 🔥 Laravel PHP Facade/Wrapper for the Youtube Data API\noverblog/GraphQLBundle - This bundle provides tools to build a complete GraphQL API server in your Symfony App.\nsymfony/password-hasher - Provides password hashing utilities\nsymfony/notifier - Sends notifications via one or more channels (email, SMS, ...)\nxing61/zzz-api - 优质稳定的OpenAI、Gemini、Claude等的API接口-For企业和开发者。OpenAI的api proxy，支持ChatGPT的API调用，支持openai的API接口，支持：gpt-4，gpt-3.5。不需要openai Key, 不需要买openai的账号，不需要美元的银行卡，通通不用的，直接调用就行，稳定好用！！智增增\nlaravel/installer - The Laravel application installer.\nparti-renaissance/espace-adherent - Le site principal du parti Renaissance \njantinnerezo/livewire-alert - SweetAlert2 integration for Livewire\nflyntwp/flynt - Component based WordPress starter theme, powered by ACF Pro and Timber, optimized for performance and a11y.\nEC-CUBE/ec-cube - EC-CUBE is the most popular e-commerce solution in Japan\nrakutentech/laravel-request-docs - Effortless API documentation tool for Laravel applications. Swagger alternative.\nqcod/laravel-imageup - Auto Image & file upload, resize and crop for Laravel eloquent model using Intervention image\nsymfony/proxy-manager-bridge - Provides integration for ProxyManager with various Symfony components\nralphjsmit/laravel-seo - A package to handle the SEO in any Laravel application, big or small.\nCrestApps/laravel-code-generator - An efficient Laravel code generator, saving time by automating the creation of resources such as views, controllers, routes, migrations, languages, and form-requests. Highly flexible and customizable, it includes a cross-browser compatible template and client-side validation for application modernization.\ndefstudio/telegraph - Telegraph is a Laravel package for fluently interacting with Telegram Bots\ncode16/sharp - Laravel 11+ Content management framework\nhumbug/php-scoper - 🔨 Prefixes all PHP namespaces in a file/directory to isolate the code bundled in PHARs.\nspatie/menu - Html menu generator\ncraue/CraueFormFlowBundle - Multi-step forms for your Symfony project.\nphpstan/phpstan-symfony - Symfony extension for PHPStan\njaredhendrickson13/pfsense-api - The missing REST and GraphQL API package for pfSense\nMohmmedAshraf/laravel-translations - Laravel Translations UI package provides a user-friendly interface for managing translations in your Laravel application. It simplifies tasks such as adding, editing, deleting, and exporting translations. The package also includes a handy search feature and the ability to invite collaborators for easy translation management\nchevereto/chevereto - The mature, battle-tested, high-end, OG self-hosted image and video hosting solution trusted since 2007. Build your own Flickr or Imgur-style media sharing platform with complete control over your content, data, and platform rules.\nlaravel-notification-channels/webpush - Webpush notifications channel for Laravel.\nmatchish/laravel-scout-elasticsearch - Search among multiple models with ElasticSearch and Laravel Scout\nzenstruck/foundry - A model factory library for creating expressive, auto-completable, on-demand dev/test fixtures with Symfony and Doctrine.\nparagonie/easydb - Easy-to-use PDO wrapper for PHP projects.\nspatie/laravel-onboard - A Laravel package to help track user onboarding steps\nmaize-tech/laravel-markable - Integrate likes, bookmarks, favorites, reactions and custom made marks into your application\nloophp/collection - A (memory) friendly, easy, lazy and modular collection class.\nspatie/ssl-certificate - A class to validate SSL certificates\njakubkulhan/bunny - Performant pure-PHP AMQP (RabbitMQ) sync/async (ReactPHP) library\nlakasir/lakasir - POS open-source, simple things just need simple solutions\nwp-media/wp-rocket - Performance optimization plugin for WordPress\nline/line-bot-sdk-php - LINE Messaging API SDK for PHP\nsymfony/runtime - Enables decoupling PHP applications from global state\nludoguenet/laravel-zap - Lightning-fast schedule management for Laravel\nsilverstripe/silverstripe-framework - Silverstripe Framework, the MVC framework that powers Silverstripe CMS\nDigitalOceanPHP/Client - DigitalOcean API v2 client for PHP\namphp/http-client - An advanced async HTTP client library for PHP, enabling efficient, non-blocking, and concurrent requests and responses.\ngetsentry/sentry-symfony - The official Symfony SDK for Sentry (sentry.io)\nspatie/laravel-url-signer - Create and validate signed URLs with a limited lifetime\nWendellAdriel/laravel-validated-dto - Data Transfer Objects with validation for Laravel applications\nwillfarrell/alfred-pkgman-workflow - Package Manager Search\nKrook9d/PurpleLab -  PurpleLab is an efficient and readily deployable lab solution, providing a swift setup for cybersecurity professionals to test detection rules and undertake various security tasks, all accessible through a user-friendly web interface\nphp-gettext/Gettext - PHP library to collect and manipulate gettext (.po, .mo, .php, .json, etc)\nflow-php/flow - The most advanced data processing framework allowing to build scalable data processing pipelines and move data between various data sources and destinations.\nilluminate/contracts - [READ ONLY] Subtree split of the Illuminate Contracts component (see laravel/framework)\nawais-vteams/laravel-crud-generator - Laravel CRUD Generator\nmarkitosgv/JWTRefreshTokenBundle - Implements a Refresh Token system over Json Web Tokens in Symfony\nalcohol/iso3166 - A PHP library providing ISO 3166-1 data.\nwp-cli/php-cli-tools - A collection of tools to help with PHP command line utilities\njumbojett/OpenID-Connect-PHP - Minimalist OpenID Connect client\nAzuriom/Azuriom - Azuriom is a complete open-source web solution for game servers. Enjoy dozens of extensions for endless possibilities. Already trusted by over 1,800 servers totaling more than two million users.\nspatie/phpunit-snapshot-assertions - A way to test without writing actual test cases\nsymfony/polyfill-uuid - Symfony polyfill for uuid functions\nmeilisearch/meilisearch-php - PHP client for Meilisearch\nzoujingli/wechat-php-sdk -  PHP微信SDK（微信平台 + 微信支付）\nqcod/laravel-gamify - Gamify your Laravel app with Reputation Points & Achievements Badges support\nwoocommerce/action-scheduler - A scalable, traceable job queue for background processing large queues of tasks in WordPress. Specifically designed for distribution in WordPress plugins (and themes) - no server access required.\ngoogleads/googleads-php-lib - Google Ad Manager SOAP API Client Library for PHP\nIntervention/validation - Missing Laravel Validation Rules\noroinc/crm - Main OroCRM package with core functionality.\nstudio1902/statamic-peak - Statamic Peak is an opinionated starter kit for all your Statamic sites.\nrlanvin/php-rrule - Lightweight and fast recurring dates library for PHP (RFC 5545)\nSimpleMachines/SMF - Simple Machines Forum — SMF in short — is free and open-source community forum software, delivering professional grade features in a package that allows you to set up your own online community within minutes!\nlucasdotvin/laravel-soulbscription - A straightforward interface to handle subscriptions and feature consumption.\nportabilis/i-educar - Lançando o maior software livre de educação do Brasil!\ncivicrm/civicrm-core - CiviCRM (Core Application and Framework)\nlibvips/php-vips - php binding for libvips\nmateusjunges/laravel-kafka - Use Kafka Producers and Consumers in your laravel app with ease!\nyiisoft/yii2-app-basic - Yii 2.0 Basic Application Template\ntallstackui/tallstackui - TallStackUI is a powerful suite of Blade components that elevate your workflow of Livewire applications.\nGrafikart/Grafikart.fr - Dépôt pour la nouvelle version de Grafikart.fr\ninit-engineer/init.engineer - 這是一份純靠北工程師的專案，請好好愛護它，謝謝。\nguanguans/notify - Push notification SDK(AnPush、Bark、Chanify、DingTalk、Discord、Gitter、GoogleChat、IGot、Lark、Mattermost、MicrosoftTeams、NowPush、Ntfy、Push、Pushback、PushBullet、PushDeer、PushMe、Pushover、PushPlus、QQ、RocketChat、ServerChan、ShowdocPush、SimplePush、Slack、Telegram、WeWork、WPush、XiZhi、YiFengChuanHua、Zulip).\njanephp/janephp - :seedling: Jane is a set of libraries to generate Models & API Clients based on JSON Schema / OpenAPI specs\nWenPai-org/wp-china-yes - 文派叶子 🍃（WPCY.COM）是中国 WordPress 生态基础设施软件，犹如落叶新芽，生生不息。\naimeos/ai-client-html - Aimeos e-commerce HTML client components\ndvdoug/BoxPacker - 4D bin packing / knapsack problem solver\nsonata-project/EntityAuditBundle - Audit for Doctrine Entities\ntaksssss/iptv-tool - IPTV工具箱， Docker🐳部署，支持EPG管理、直播源管理、台标管理，兼容DIYP/百川、超级直播及xmltv格式。\noroinc/platform - Main OroPlatform package with core functionality.\nspatie/freek.dev - The sourcecode of freek.dev\nphpstan/phpstan-doctrine - Doctrine extensions for PHPStan\nmicrosoftgraph/msgraph-sdk-php - Microsoft Graph Library for PHP.\ncomposer/pcre - PCRE wrapping library that offers type-safe preg_* replacements.\nccxt/php-binance-api - PHP Binance API is an asynchronous PHP library for the Binance API designed to be easy to use. https://github.com/binance-exchange/php-binance-api\nsymfony/polyfill-apcu - Symfony polyfill backporting apcu_* functions to lower PHP versions\ngregsullivan/_tw - The _tw generator creates a WordPress starter theme optimized for modern development with Tailwind CSS, Tailwind Typography and the WordPress editor.\nlaravel-enso/tables - Bulma themed, VueJS powered Datatable with server-side loading and JSON template setup\nhekmatinasser/verta - تبدیل تاریخ میلادی به شمسی برای لاراول  و کار با تاریخ جلالی و اعتبارسنجی تاریخ\nTeam-Tea-Time/laravel-forum - A slim, lean forum package designed for quick and easy integration in Laravel projects\nKhronosGroup/glTF-Sample-Assets - An assortment of assets that demonstrate features and capabilities of the glTF format\nLaravelLegends/pt-br-validator - Uma biblioteca contendo validações de formatos Brasileiros, para o Laravel\nfisharebest/webtrees - Online genealogy\nBinarCode/laravel-restify - The fastest way to make a powerful JSON:API compatible Rest API with Laravel.\nlucatume/wp-browser - The easy and reliable way to test WordPress with Codeception. 10 years of proven success.\nGoogleChromeLabs/pwa-wp - WordPress feature plugin to bring Progressive Web Apps (PWA) to Core\nmattiverse/Laravel-Userstamps - Laravel Userstamps provides an Eloquent trait which automatically maintains created_by and updated_by columns on your model, populated by the currently authenticated user in your application.\neduardokum/laravel-boleto - Pacote para gerar boletos e remessas \nylsideas/feature-flags - A Laravel package for handling feature flags\nstaudenmeir/laravel-cte - Laravel queries with common table expressions\ndriesvints/blade-heroicons - A package to easily make use of Heroicons in your Laravel Blade views.\nsymfony/workflow - Provides tools for managing a workflow or finite state machine\nspatie/laravel-export - Create a static site bundle from a Laravel app\ncjmellor/level-up - Level-Up is a Laravel package introducing gamification into your applications. Users earn experience points (XP) and levels through interactions, while also unlocking achievements. It promotes engagement, competition, and fun through its dynamic leaderboard feature. Customisable to fit your specific needs\nlaravel/prompts - Beautiful and user-friendly forms for your command-line PHP applications.\naimeos/ai-laravel - Laravel adapter for Aimeos web shops and e-commerce solutions\naimeos/ai-controller-jobs - Aimeos e-commerce job controllers\nspatie/ray - Debug with Ray to fix problems faster\nzhiyi7/gfw-pac - 科学上网 PAC 成品文件以及生成器。可自定义代理域名和直连域名。不在自定义名单里的域名再按IP匹配，CNIP走直连，其他走代理，支持IPv6\npostmanlabs/openapi-to-postman - Plugin for converting OpenAPI 3.0 specs to the Postman Collection (v2) format\nolegakbarov/react-redux-starter-kit - My best-practices-included universal frontend starter kit \nalienkitty/alien.js - 👽 MVC design pattern and render pipeline\nexcalidraw/excalidraw-libraries - Collection of publicly available libraries\nEvercoder/culori - A comprehensive color library for JavaScript.\npingan8787/Leo-JavaScript - 欢迎关注公众号“前端自习课”，本仓库包含丰富的前端学习资料，包括 JavaScript、前端框架、HTTP、GraphQL、TS、Vue、React、Webpack等，还有很多我的原创文章，喜欢的朋友欢迎stat。:rocket:持续更新中...\nguybedford/es-module-lexer - Low-overhead lexer dedicated to ES module parsing for fast analysis\nobservablehq/stdlib - The Observable standard library.\nbadlogic/heissepreise - Jo eh.\ndeezertidal/Surge_Module - Surge模块 脚本 module sgmodule 面板 规则 分流 破解 解锁\nbrowserengineering/book - Web browser engineering (a book)\nriolubruh/YABDP4Nitro - Yet Another BetterDiscord Plugin for Nitro features. Unlock screensharing modes, use cross-server and gif emotes and much more!\nrabbitmq/rabbitmq-website - RabbitMQ website\ngkandemi/docker - Kablosuzkedi Kanalındaki Docker Serisi için Hazırlanmış Dokümantasyondur\nInSeong-So/IT-Note - 개발자를 위한 노트\nZGQ-inc/source - :package:个人搜集 | 书源、图源、订阅源、规则、直播源、各种源 大型整合\nihucos/counter.dev - Web Analytics made simple\nteslamotors/informed - A lightweight framework and utility for building powerful forms in React applications\nmsojocs/bilibili-linux - 基于哔哩哔哩官方客户端移植的Linux版本 支持漫游\ngeorgemandis/konami-js - Adding the Konami Code easter egg to your projects since 2009! Compatible with gestures on smartphones and tablets as well. Compatible with all front-end frameworks and vanilla JavaScript\nbe5invis/vscode-custom-css - Custom CSS Plugin for Visual Studio Code. Based on vscode-icon\nalexhua/Aria2-Explorer - An aria2 download task management extension for chromium based browser\nGoogleChromeLabs/chrome-for-testing - None\nreisxd/TizenTube - A TizenBrew module to remove ads and add support for SponsorBlock for your Tizen TV.\ntidev/alloy - Alloy is an MVC framework for the Appcelerator Titanium SDK\nShopify/shopify-app-template-node - None\ngenerative-design/Code-Package-p5.js - Code package of the book: Generative Design – Creative Coding for the Web with JavaScript in p5.js\nshanmiteko/LotteryAutoScript - Bili动态抽奖助手\nztoben/assets-webpack-plugin - Webpack plugin that emits a json file with assets paths\nSteamDatabase/BrowserExtension - 💻 SteamDB's extension for Steam websites\nKelvinTegelaar/CIPP - CIPP is a M365 multitenant management solution\naliyunvideo/AliyunPlayer_Web - The kinds of demo for H5 Aliplayer, which cover live, playback and multiple platforms, such as mobile, pc and weixin and so on\nhackademix/noscript - The popular NoScript Security Suite browser extension.\ngeotiffjs/geotiff.js - geotiff.js is a small library to parse TIFF files for visualization or analysis. It is written in pure JavaScript, and is usable in both the browser and node.js applications.\napache/cordova-cli - Apache Cordova CLI\nooade/NextSimpleStarter - :whale: Simple and Accessible PWA boilerplate with Nextjs 12 and MUI\nalphagov/accessible-autocomplete - An autocomplete component, built to be accessible.\nartiebits/fake-git-history - A command-line tool to generate GitHub and GitLab activity graph.\nauth0-samples/auth0-react-samples - Auth0 Integration Samples for React Applications\nttop32/MouseTooltipTranslator - Mouseover Translate Any Language At Once - Chrome Extension: PDF Translator, EBOOK, EPUB, OCR, TTS, NETFLIX, YOUTUBE DUAL SUBTITLES, GOOGLE DOCS, AI, VIEWER, GMAIL, WRITING, IMAGE, DUAL SUBS, MANGA, HOVER, DICTIONARY, WEBTOON, EDGE, JAPANESE, ENGLISH\nPhlow/feeling-responsive - »Feeling Responsive« is a free flexible theme for Jekyll built on Foundation framework. You can use it for your company site, as a portfolio or as a blog.\ninfokiller/web-search-navigator - Web extension that adds keyboard shortcuts to Google, YouTube, Github, Amazon, and others (Chrome/Firefox/Edge/Safari)\nNASAWorldWind/WebWorldWind - The NASA WorldWind Javascript SDK (WebWW) includes the library and examples for creating geo-browser web applications and for embedding a 3D globe in HTML5 web pages.\nbrowserpass/browserpass-extension - Browserpass web extension\nhoothin/SearchJumper - Yet another awesome browser extension for switching search engines, search everything (selection text / image / link / find in page) on any engine with a simple right click or a variety of menus and shortcuts. Build with React & Material-UI. (WIP).\ngooking/apifm-wxapi - 微信小程序云开发工具包，借此工具包，你将无需投入服务器、无需接口编程、无需开发后台，将传统开发小程序效率提升百倍\nsensorsdata/sa-sdk-javascript - 神策数据官方 Web JavaScript 埋点 SDK，是一款轻量级用于 Web 端和 H5 端的数据采集埋点 SDK。使用原生 JavaScript 技术实现代码埋点、全埋点、可视化全埋点、网页热力图和触达图等功能。\nsveltejs/vite-plugin-svelte - Svelte plugin for https://vite.dev\ndteviot/WebToEpub - A simple Chrome (and Firefox) Extension that converts Web Novels (and other web pages) into an EPUB.\nwanghao221/moyu - 2000个摸鱼小游戏、小工具源码集合及在线演示\nTerryZ/v-region - 提供 5 种应用形式的 4 级行政区划选择器 A simple region cascade selector, provide 4 levels Chinese administrative division data\nimacrayon/alpine-ajax - An Alpine.js plugin for building server-powered frontends.\ngauntface/simple-push-demo - A simple example of use push notifications on the web using Service Workers\ngithubdulong/Script - QuantumultX｜Surge｜Loon\nyylive/YYEVA - YYEVA（YY Effect Video Animate）是YYLive推出的一个开源的支持可插入动态元素的MP4动效播放器解决方案，包含设计资源输出的AE插件，客户端渲染引擎，在线预览工具。\nMeteor-Community-Packages/meteor-roles - Authorization package for Meteor, compatible with built-in accounts packages\ni18next/i18next-browser-languageDetector - language detector used in browser environment for i18next\nfirebase/quickstart-nodejs - None\njpush/jpush-phonegap-plugin - JPush's officially supported PhoneGap/Cordova plugin (Android & iOS).  极光推送官方支持的 PhoneGap/Cordova 插件（Android & iOS）。\ntschaub/mock-fs - Configurable mock for the fs module\nnotrab/create-react-app-redux - React Router, Redux, Redux Thunk & Create React App boilerplate\n89996462/Quantumult-X - None\nMeteor-Community-Packages/meteor-simple-schema - Meteor integration package for simpl-schema\nreactabular/reactabular - A framework for building the React table you need (MIT)\nhymbz/ComicReadScript - 为漫画站增加双页阅读模式及优化使用体验。\nwebdriverio/selenium-standalone - A Node.js based package and CLI library for launching Selenium with WebDrivers support (Chrome, Firefox, IE, Edge)\nBlueWallet/LndHub - Wrapper for Lightning Network Daemon. It provides separate accounts for end-users\nmaxzhang666/OneKeyVip - 一键Vip解析工具箱,功能介绍：1、一站式音乐搜索解决方案；2、bilibili视频封面获取；3、bilibili视频下载；4、商品历史价格展示(一次性告别虚假降价)；5、优惠券查询；6、CSDN界面清理(复制解锁)\ngoogle/blockly-samples - Plugins, codelabs, and examples related to the Blockly library.\nFirefoxCSS-Store/FirefoxCSS-Store.github.io - A collection site of Firefox userchrome themes, mostly from FirefoxCSS Reddit community.\nrebrowser/rebrowser-patches - Collection of patches for puppeteer and playwright to avoid automation detection and leaks. Helps to avoid Cloudflare and DataDome CAPTCHA pages. Easy to patch/unpatch, can be enabled/disabled on demand.\nmixpanel/mixpanel-js - Official Mixpanel JavaScript Client Library\npubkey/eth-crypto - Cryptographic javascript-functions for ethereum and tutorials to use them with web3js and solidity\nassetgraph/assetgraph - Optimization framework for web pages and applications\nJustOptimize/ShowHiddenChannels - A BetterDiscord plugin which displays all hidden channels and allows users to view information about them.\nwileyyugioh/zotmoov - Zotero plugin to automatically move attachments and link them\nymyuuu/Cloudflare-Workers-Proxy - 简单而功能强大的反代服务，允许你将一个网址映射到另一个网址，并处理跨域请求\nvfat-io/vfat-tools - None\nnestcn/docs.nestjs.cn - nestjs 中文文档\nzachleat/BigText - jQuery plugin, calculates the font-size and word-spacing needed to match a line of text to a specific width.\nneo4j/neo4j-javascript-driver - Neo4j Bolt driver for JavaScript\nttttmr/Wechat2RSS - 微信公众号转RSS\nQuiteAFancyEmerald/Holy-Unblocker - Holy Unblocker LTS is a web proxy service that helps you access websites that may be blocked by your network or policy extensions all within your browser with no download or setup. It does this securely and with additional privacy features. Browse Tor/Onion sites in any browser, hide browsing activity and bypass filters. (Star if you fork it!!!)\nxmflswood/pinyin-match - 拼音匹配，具备分词、缩写、多音字匹配能力，支持繁体版\nyjs/yjs-demos - A collection of demos for Yjs\nPactInteractive/image-downloader - ⬇️ Download images from the web with ease. A browser extension for Google Chrome, Microsoft Edge, and Brave.\ncodedthemes/mantis-free-react-admin-template - Mantis is React Dashboard Template having combine tone of 2 popular react component library - MUI and Ant Design principles.\nluigi-project/luigi - Micro frontend framework\nomarciovsena/abibliadigital - A RESTful API for Bible\nisaacs/node-tar - tar for node\nOfficeDev/Office-Add-in-samples - Code samples for Office Add-in development on the Microsoft 365 platform.\nstylelint-scss/stylelint-scss - A collection of SCSS specific linting rules for Stylelint\ndvt3d/dc-sdk - DC-SDK is based on the open source project Cesium for the second development of two three-dimensional WebGis application framework , the framework optimizes the use of Cesium and adds some additional features , designed for developers to quickly build WebGis application.🌎\ndxcweb/watermark - canvas图片水印，用于身份证等个人信息添加仅用于XXX等字样保护个人信息\ntbranyen/diffhtml - diffHTML is a web framework that helps you build applications and other interactive content\nwangwangit/SubsTracker - 基于Cloudflare Workers的轻量级订阅管理系统，帮助您轻松跟踪各类订阅服务的到期时间，并通过Telegram发送及时提醒。\nkeithamus/sort-package-json - Sort an Object or package.json based on the well-known package.json keys\nweb3swift-team/web3swift - Full featured library for Ethereum interaction with the JSON RPC API in swift. Native ABI parsing and smart contract interactions.\ncmliu/CF-Workers-TEXT2KV - 这个是一个通过 Cloudflare Workers 搭建，将文本文件存储到 Cloudflare Workers KV 键值存储中，并且可以通过 URL 请求读取或更新这些文本文件。\nsindresorhus/normalize-url - Normalize a URL\nhuglemon/inwind-landing-page - a free, open-source, powerful landing page template.\nBorisMoore/jsviews - Interactive data-driven views, MVVM and MVP, built on top of JsRender templates\ndimdenGD/ultimate-express - The Ultimate Express. Fastest http server with full Express compatibility, based on µWebSockets.\nsunzsh/internal-chat - 纯html写的局域网文字/文件p2p传输工具\nKlemen1337/node-thermal-printer - Node.js module for Epson, Star, Tanca, Drauma, Custom and Brother thermal printers command line printing.\nuappkit/uapp - uapp是一款基于uni-app跨平台开发的高效cli。 源自自有产品跨平台的最佳实践，通过集成 uni-app, electron, tauri，让开发者仅需维护一套代码，就能横跨所有平台。uapp助开发者无限制重构一切软件。\nsebastiancarlos/beachpatrol - 🏝️ A CLI Tool to Automate Your Everyday Web Browser.\nsketch-hq/SketchAPI - The JavaScript plugin library embedded in Sketch\ngulpjs/liftoff - Launch your command line tool with ease.\ndrudru/ansi_up - A javascript library that converts text with ANSI terminal codes into colorful HTML Zero dependencies.\nmarkodenic/awesome-tech-blogs - A list of Tech Blogs.\nzaproxy/community-scripts - A collection of ZAP scripts and tips provided by the community - pull requests very welcome!\nTencent/LightDiffusionFlow - This extension is developed for AUTOMATIC1111's Stable Diffusion web UI that provides import/export options for parameters.\neguadev/egua - Linguagem de programação em português,  simples e moderna\nFormstone/Formstone - Library of modular front end components. \nmatteodem/meteor-boilerplate - A lightweight boilerplate for meteor projects\ncoderitual/bounty - Javascript and SVG odometer effect library with motion blur\npaulmillr/noble-secp256k1 - Fastest 4KB JS implementation of secp256k1 signatures and ECDH\nrails/sdoc - Standalone sdoc generator\nthsmi/sieve - Sieve Script Editor\nGoogleCloudPlatform/nodejs-getting-started - A tutorial for creating a complete application using Node.js on Google Cloud Platform\nthumbsup/thumbsup - Generate static HTML photo / video galleries\nsqlite/sqlite-wasm - SQLite Wasm conveniently wrapped as an ES Module.\ntrailheadapps/ebikes-lwc - Sample application for Lightning Web Components and Experience Cloud on Salesforce Platform. Retail use case. Get inspired and learn best practices.\nalitajs/alita - A React framework based on umi. \nEvanBacon/expo-apple-targets - Config Plugin to setup Apple targets\nliujuntao123/smart-mermaid - 一款基于 AI 技术的 Web 应用程序，可将文本内容智能转换为 Mermaid 格式的代码，并将其渲染成可视化图表。\nLaravelRUS/SleepingOwlAdmin - 🦉 Administrative interface builder for Laravel (Laravel admin)\nxeokit/xeokit-sdk - 3D BIM IFC Viewer SDK for AEC engineering applications. Open Source JavaScript Toolkit based on pure WebGL for top performance, real-world coordinates and full double precision\ntesting-library/testing-playground - Simple and complete DOM testing playground that encourage good testing practices.\npmndrs/three-stdlib - 📚 Stand-alone library of threejs examples designed to run without transpilation in node & browser\n