{"version": "1.130.0", "results": [{"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/codeigniter4_shield/admin/prepare-release.php", "start": {"line": 29, "col": 1, "offset": 674}, "end": {"line": 29, "col": 45, "offset": 718}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/codeigniter4_shield/admin/prepare-release.php", "start": {"line": 40, "col": 1, "offset": 931}, "end": {"line": 40, "col": 61, "offset": 991}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/codeigniter4_shield/docs/addons/jwt.md", "start": {"line": 256, "col": 33, "offset": 7329}, "end": {"line": 256, "col": 163, "offset": 7459}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/codeigniter4_shield/src/Authentication/HMAC/HmacEncrypter.php", "start": {"line": 42, "col": 13, "offset": 0}, "end": {"line": 42, "col": 21, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/codeigniter4_shield/src/Authentication/HMAC/HmacEncrypter.php:42:\n `readonly` was unexpected", "path": "downloaded_repos/codeigniter4_shield/src/Authentication/HMAC/HmacEncrypter.php", "spans": [{"file": "downloaded_repos/codeigniter4_shield/src/Authentication/HMAC/HmacEncrypter.php", "start": {"line": 42, "col": 13, "offset": 0}, "end": {"line": 42, "col": 21, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/codeigniter4_shield/src/Collectors/Auth.php", "start": {"line": 56, "col": 13, "offset": 0}, "end": {"line": 56, "col": 21, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/codeigniter4_shield/src/Collectors/Auth.php:56:\n `readonly` was unexpected", "path": "downloaded_repos/codeigniter4_shield/src/Collectors/Auth.php", "spans": [{"file": "downloaded_repos/codeigniter4_shield/src/Collectors/Auth.php", "start": {"line": 56, "col": 13, "offset": 0}, "end": {"line": 56, "col": 21, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/codeigniter4_shield/src/Database/Migrations/2020-12-28-223112_create_auth_tables.php", "start": {"line": 27, "col": 22, "offset": 0}, "end": {"line": 27, "col": 27, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/codeigniter4_shield/src/Database/Migrations/2020-12-28-223112_create_auth_tables.php:27:\n `array` was unexpected", "path": "downloaded_repos/codeigniter4_shield/src/Database/Migrations/2020-12-28-223112_create_auth_tables.php", "spans": [{"file": "downloaded_repos/codeigniter4_shield/src/Database/Migrations/2020-12-28-223112_create_auth_tables.php", "start": {"line": 27, "col": 22, "offset": 0}, "end": {"line": 27, "col": 27, "offset": 5}}]}], "paths": {"scanned": ["downloaded_repos/codeigniter4_shield/.editorconfig", "downloaded_repos/codeigniter4_shield/.gitattributes", "downloaded_repos/codeigniter4_shield/.github/ISSUE_TEMPLATE/bug_report.yml", "downloaded_repos/codeigniter4_shield/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/codeigniter4_shield/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/codeigniter4_shield/.github/ISSUE_TEMPLATE/planned-work.md", "downloaded_repos/codeigniter4_shield/.github/dependabot.yml", "downloaded_repos/codeigniter4_shield/.github/prlint.json", "downloaded_repos/codeigniter4_shield/.github/release.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/deptrac.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/docs.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/no-merge-commits.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/phpcpd.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/phpcsfixer.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/phpstan.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/phpunit-lang.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/phpunit-lowest.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/phpunit.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/psalm.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/rector.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/smart-commenting.yml", "downloaded_repos/codeigniter4_shield/.github/workflows/unused.yml", "downloaded_repos/codeigniter4_shield/.gitignore", "downloaded_repos/codeigniter4_shield/.php-cs-fixer.dist.php", "downloaded_repos/codeigniter4_shield/CONTRIBUTING.md", "downloaded_repos/codeigniter4_shield/LICENSE", "downloaded_repos/codeigniter4_shield/README.md", "downloaded_repos/codeigniter4_shield/SECURITY.md", "downloaded_repos/codeigniter4_shield/UPGRADING.md", "downloaded_repos/codeigniter4_shield/admin/RELEASE.md", "downloaded_repos/codeigniter4_shield/admin/how_to_build_docs.md", "downloaded_repos/codeigniter4_shield/admin/pre-commit", "downloaded_repos/codeigniter4_shield/admin/prepare-release.php", "downloaded_repos/codeigniter4_shield/admin/setup.sh", "downloaded_repos/codeigniter4_shield/bin/update-en-comments", "downloaded_repos/codeigniter4_shield/composer-unused.php", "downloaded_repos/codeigniter4_shield/composer.json", "downloaded_repos/codeigniter4_shield/deptrac.yaml", "downloaded_repos/codeigniter4_shield/docs/CNAME", "downloaded_repos/codeigniter4_shield/docs/addons/jwt.md", "downloaded_repos/codeigniter4_shield/docs/assets/css/codeigniter.css", "downloaded_repos/codeigniter4_shield/docs/assets/css/codeigniter_dark_mode.css", "downloaded_repos/codeigniter4_shield/docs/assets/favicon.ico", "downloaded_repos/codeigniter4_shield/docs/assets/flame.svg", "downloaded_repos/codeigniter4_shield/docs/assets/js/hljs.js", "downloaded_repos/codeigniter4_shield/docs/customization/adding_attributes_to_users.md", "downloaded_repos/codeigniter4_shield/docs/customization/extending_controllers.md", "downloaded_repos/codeigniter4_shield/docs/customization/integrating_custom_view_libs.md", "downloaded_repos/codeigniter4_shield/docs/customization/login_identifier.md", "downloaded_repos/codeigniter4_shield/docs/customization/redirect_urls.md", "downloaded_repos/codeigniter4_shield/docs/customization/route_config.md", "downloaded_repos/codeigniter4_shield/docs/customization/table_names.md", "downloaded_repos/codeigniter4_shield/docs/customization/user_provider.md", "downloaded_repos/codeigniter4_shield/docs/customization/validation_rules.md", "downloaded_repos/codeigniter4_shield/docs/customization/views.md", "downloaded_repos/codeigniter4_shield/docs/getting_started/authenticators.md", "downloaded_repos/codeigniter4_shield/docs/getting_started/concepts.md", "downloaded_repos/codeigniter4_shield/docs/getting_started/configuration.md", "downloaded_repos/codeigniter4_shield/docs/getting_started/install.md", "downloaded_repos/codeigniter4_shield/docs/guides/api_hmac_keys.md", "downloaded_repos/codeigniter4_shield/docs/guides/api_tokens.md", "downloaded_repos/codeigniter4_shield/docs/guides/mobile_apps.md", "downloaded_repos/codeigniter4_shield/docs/guides/strengthen_password.md", "downloaded_repos/codeigniter4_shield/docs/index.md", "downloaded_repos/codeigniter4_shield/docs/quick_start_guide/using_authorization.md", "downloaded_repos/codeigniter4_shield/docs/quick_start_guide/using_session_auth.md", "downloaded_repos/codeigniter4_shield/docs/references/authentication/auth_actions.md", "downloaded_repos/codeigniter4_shield/docs/references/authentication/authentication.md", "downloaded_repos/codeigniter4_shield/docs/references/authentication/hmac.md", "downloaded_repos/codeigniter4_shield/docs/references/authentication/session.md", "downloaded_repos/codeigniter4_shield/docs/references/authentication/tokens.md", "downloaded_repos/codeigniter4_shield/docs/references/authorization.md", "downloaded_repos/codeigniter4_shield/docs/references/controller_filters.md", "downloaded_repos/codeigniter4_shield/docs/references/events.md", "downloaded_repos/codeigniter4_shield/docs/references/magic_link_login.md", "downloaded_repos/codeigniter4_shield/docs/references/testing.md", "downloaded_repos/codeigniter4_shield/docs/user_management/banning_users.md", "downloaded_repos/codeigniter4_shield/docs/user_management/forcing_password_reset.md", "downloaded_repos/codeigniter4_shield/docs/user_management/managing_users.md", "downloaded_repos/codeigniter4_shield/infection.json.dist", "downloaded_repos/codeigniter4_shield/mkdocs.yml", "downloaded_repos/codeigniter4_shield/phpstan-baseline.php", "downloaded_repos/codeigniter4_shield/phpstan.neon.dist", "downloaded_repos/codeigniter4_shield/phpunit.xml.dist", "downloaded_repos/codeigniter4_shield/psalm-baseline.xml", "downloaded_repos/codeigniter4_shield/psalm.xml", "downloaded_repos/codeigniter4_shield/psalm_autoload.php", "downloaded_repos/codeigniter4_shield/rector.php", "downloaded_repos/codeigniter4_shield/roave-bc-check.yaml", "downloaded_repos/codeigniter4_shield/src/Auth.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Actions/ActionInterface.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Actions/Email2FA.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Actions/EmailActivator.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Authentication.php", "downloaded_repos/codeigniter4_shield/src/Authentication/AuthenticationException.php", "downloaded_repos/codeigniter4_shield/src/Authentication/AuthenticatorInterface.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Authenticators/AccessTokens.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Authenticators/HmacSha256.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Authenticators/JWT.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Authenticators/Session.php", "downloaded_repos/codeigniter4_shield/src/Authentication/HMAC/HmacEncrypter.php", "downloaded_repos/codeigniter4_shield/src/Authentication/JWT/Adapters/FirebaseAdapter.php", "downloaded_repos/codeigniter4_shield/src/Authentication/JWT/Exceptions/InvalidTokenException.php", "downloaded_repos/codeigniter4_shield/src/Authentication/JWT/JWSAdapterInterface.php", "downloaded_repos/codeigniter4_shield/src/Authentication/JWT/JWSDecoder.php", "downloaded_repos/codeigniter4_shield/src/Authentication/JWT/JWSEncoder.php", "downloaded_repos/codeigniter4_shield/src/Authentication/JWTManager.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/BaseValidator.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/CompositionValidator.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/DictionaryValidator.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/NothingPersonalValidator.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/PwnedValidator.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/ValidationRules.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/ValidatorInterface.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Traits/HasAccessTokens.php", "downloaded_repos/codeigniter4_shield/src/Authentication/Traits/HasHmacTokens.php", "downloaded_repos/codeigniter4_shield/src/Authorization/AuthorizationException.php", "downloaded_repos/codeigniter4_shield/src/Authorization/Groups.php", "downloaded_repos/codeigniter4_shield/src/Authorization/Traits/Authorizable.php", "downloaded_repos/codeigniter4_shield/src/Collectors/Auth.php", "downloaded_repos/codeigniter4_shield/src/Commands/BaseCommand.php", "downloaded_repos/codeigniter4_shield/src/Commands/Exceptions/BadInputException.php", "downloaded_repos/codeigniter4_shield/src/Commands/Exceptions/CancelException.php", "downloaded_repos/codeigniter4_shield/src/Commands/Generators/UserModelGenerator.php", "downloaded_repos/codeigniter4_shield/src/Commands/Generators/Views/usermodel.tpl.php", "downloaded_repos/codeigniter4_shield/src/Commands/Hmac.php", "downloaded_repos/codeigniter4_shield/src/Commands/Setup/ContentReplacer.php", "downloaded_repos/codeigniter4_shield/src/Commands/Setup.php", "downloaded_repos/codeigniter4_shield/src/Commands/User.php", "downloaded_repos/codeigniter4_shield/src/Commands/Utils/InputOutput.php", "downloaded_repos/codeigniter4_shield/src/Config/Auth.php", "downloaded_repos/codeigniter4_shield/src/Config/AuthGroups.php", "downloaded_repos/codeigniter4_shield/src/Config/AuthJWT.php", "downloaded_repos/codeigniter4_shield/src/Config/AuthRoutes.php", "downloaded_repos/codeigniter4_shield/src/Config/AuthToken.php", "downloaded_repos/codeigniter4_shield/src/Config/BaseAuthToken.php", "downloaded_repos/codeigniter4_shield/src/Config/Registrar.php", "downloaded_repos/codeigniter4_shield/src/Config/Services.php", "downloaded_repos/codeigniter4_shield/src/Controllers/ActionController.php", "downloaded_repos/codeigniter4_shield/src/Controllers/LoginController.php", "downloaded_repos/codeigniter4_shield/src/Controllers/MagicLinkController.php", "downloaded_repos/codeigniter4_shield/src/Controllers/RegisterController.php", "downloaded_repos/codeigniter4_shield/src/Database/Migrations/2020-12-28-223112_create_auth_tables.php", "downloaded_repos/codeigniter4_shield/src/Entities/AccessToken.php", "downloaded_repos/codeigniter4_shield/src/Entities/Group.php", "downloaded_repos/codeigniter4_shield/src/Entities/Login.php", "downloaded_repos/codeigniter4_shield/src/Entities/User.php", "downloaded_repos/codeigniter4_shield/src/Entities/UserIdentity.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/BaseException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/GroupException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/InvalidArgumentException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/LogicException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/PermissionException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/RuntimeException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/SecurityException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/UserNotFoundException.php", "downloaded_repos/codeigniter4_shield/src/Exceptions/ValidationException.php", "downloaded_repos/codeigniter4_shield/src/Filters/AbstractAuthFilter.php", "downloaded_repos/codeigniter4_shield/src/Filters/AuthRates.php", "downloaded_repos/codeigniter4_shield/src/Filters/ChainAuth.php", "downloaded_repos/codeigniter4_shield/src/Filters/ForcePasswordResetFilter.php", "downloaded_repos/codeigniter4_shield/src/Filters/GroupFilter.php", "downloaded_repos/codeigniter4_shield/src/Filters/HmacAuth.php", "downloaded_repos/codeigniter4_shield/src/Filters/JWTAuth.php", "downloaded_repos/codeigniter4_shield/src/Filters/PermissionFilter.php", "downloaded_repos/codeigniter4_shield/src/Filters/SessionAuth.php", "downloaded_repos/codeigniter4_shield/src/Filters/TokenAuth.php", "downloaded_repos/codeigniter4_shield/src/Helpers/auth_helper.php", "downloaded_repos/codeigniter4_shield/src/Helpers/email_helper.php", "downloaded_repos/codeigniter4_shield/src/Language/ar/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/bg/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/cs/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/de/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/en/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/es/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/fa/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/fr/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/id/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/it/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/ja/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/lt/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/nl/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/pl/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/pt/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/pt-BR/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/ru/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/sk/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/sr/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/sv-SE/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/tr/Auth.php", "downloaded_repos/codeigniter4_shield/src/Language/uk/Auth.php", "downloaded_repos/codeigniter4_shield/src/Models/BaseModel.php", "downloaded_repos/codeigniter4_shield/src/Models/CheckQueryReturnTrait.php", "downloaded_repos/codeigniter4_shield/src/Models/DatabaseException.php", "downloaded_repos/codeigniter4_shield/src/Models/GroupModel.php", "downloaded_repos/codeigniter4_shield/src/Models/LoginModel.php", "downloaded_repos/codeigniter4_shield/src/Models/PermissionModel.php", "downloaded_repos/codeigniter4_shield/src/Models/RememberModel.php", "downloaded_repos/codeigniter4_shield/src/Models/TokenLoginModel.php", "downloaded_repos/codeigniter4_shield/src/Models/UserIdentityModel.php", "downloaded_repos/codeigniter4_shield/src/Models/UserModel.php", "downloaded_repos/codeigniter4_shield/src/Result.php", "downloaded_repos/codeigniter4_shield/src/Test/AuthenticationTesting.php", "downloaded_repos/codeigniter4_shield/src/Test/MockInputOutput.php", "downloaded_repos/codeigniter4_shield/src/Traits/Activatable.php", "downloaded_repos/codeigniter4_shield/src/Traits/Bannable.php", "downloaded_repos/codeigniter4_shield/src/Traits/Resettable.php", "downloaded_repos/codeigniter4_shield/src/Traits/Viewable.php", "downloaded_repos/codeigniter4_shield/src/Validation/ValidationRules.php", "downloaded_repos/codeigniter4_shield/src/Views/Email/email_2fa_email.php", "downloaded_repos/codeigniter4_shield/src/Views/Email/email_activate_email.php", "downloaded_repos/codeigniter4_shield/src/Views/Email/magic_link_email.php", "downloaded_repos/codeigniter4_shield/src/Views/email_2fa_show.php", "downloaded_repos/codeigniter4_shield/src/Views/email_2fa_verify.php", "downloaded_repos/codeigniter4_shield/src/Views/email_activate_show.php", "downloaded_repos/codeigniter4_shield/src/Views/layout.php", "downloaded_repos/codeigniter4_shield/src/Views/login.php", "downloaded_repos/codeigniter4_shield/src/Views/magic_link_form.php", "downloaded_repos/codeigniter4_shield/src/Views/magic_link_message.php", "downloaded_repos/codeigniter4_shield/src/Views/register.php"], "skipped": [{"path": "downloaded_repos/codeigniter4_shield/docs/assets/js/curl.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/src/Authentication/HMAC/HmacEncrypter.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/codeigniter4_shield/src/Authentication/Passwords/_dictionary.txt", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/codeigniter4_shield/src/Collectors/Auth.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/codeigniter4_shield/src/Database/Migrations/2020-12-28-223112_create_auth_tables.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/AccessTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/AuthHelperTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/AuthTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/AuthenticationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Authenticators/AccessTokenAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Authenticators/HmacAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Authenticators/JWTAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Authenticators/SessionAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/AbstractFilterTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/ChainFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/GroupFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/HmacFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/JWTFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/PermissionFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/SessionFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/Filters/TokenFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/ForcePasswordResetTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/HasAccessTokensTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/HasHmacTokensTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authentication/MagicLinkTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authorization/AuthorizableTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authorization/GroupTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Authorization/GroupsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Collectors/AuthTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Commands/HmacTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Commands/Setup/ContentReplacerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Commands/SetupTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Commands/UserModelGeneratorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Commands/UserTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Controllers/ActionsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Controllers/LoginTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Controllers/MagicLinkTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Controllers/RegisterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/AbstractTranslationTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/ArabicTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/BrazilianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/BulgarianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/CzechTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/DutchTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/FarsiTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/FrenchTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/GermanTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/IndonesianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/ItalianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/JapaneseTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/LithuanianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/PolishTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/PortugueseTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/RussianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/SerbianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/SlovakTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/SpanishTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/SwedishTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/TurkishTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Language/UkrainianTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/AuthRoutesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/Authentication/JWT/Adapters/FirebaseAdapaterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/Authentication/JWT/JWTManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/CompositionValidatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/DictionaryValidatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/EmailActivatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/FilterInCliTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/LoginModelTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/NothingPersonalValidatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/PasswordsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/PwnedValidatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/UserIdentityModelTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/UserModelTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/Unit/UserTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/_support/Config/Registrar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/_support/DatabaseTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/_support/FakeUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codeigniter4_shield/tests/_support/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.617908000946045, "profiling_times": {"config_time": 5.789687156677246, "core_time": 3.001561403274536, "ignores_time": 0.0023469924926757812, "total_time": 8.794414520263672}, "parsing_time": {"total_time": 1.1989967823028564, "per_file_time": {"mean": 0.00735580848038562, "std_dev": 0.0003183701189973908}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.717052936553955, "per_file_time": {"mean": 0.006143889151328851, "std_dev": 0.0003519413910518626}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7949881553649902, "per_file_and_rule_time": {"mean": 0.000952081623191605, "std_dev": 1.2221136574848456e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.17632341384887695, "per_def_and_rule_time": {"mean": 0.00043005710694848036, "std_dev": 5.854723695327555e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}