{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/extraEvents.js", "start": {"line": 7, "col": 19, "offset": 364}, "end": {"line": 7, "col": 28, "offset": 373}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/extraEvents.js", "start": {"line": 9, "col": 19, "offset": 507}, "end": {"line": 9, "col": 28, "offset": 516}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/extraEvents.js", "start": {"line": 11, "col": 19, "offset": 623}, "end": {"line": 11, "col": 28, "offset": 632}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/extraEvents.js", "start": {"line": 13, "col": 19, "offset": 683}, "end": {"line": 13, "col": 28, "offset": 692}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/keepAlive.js", "start": {"line": 3, "col": 7, "offset": 76}, "end": {"line": 3, "col": 22, "offset": 91}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/Persian-<PERSON>_Ticker-Boy/.github/FUNDING.yml", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/LICENSE", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/Procfile", "downloaded_repos/Persian-Caesar_Ticker-Boy/README.md", "downloaded_repos/Persian-Caesar_Ticker-Boy/app.json", "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Infos 📊/help.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Infos 📊/invite.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Infos 📊/ping.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/commands/Owner 👑/post.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/commands/Owner 👑/server.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Setup 💻/settings.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Ticket 🎫/ticket.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/button/interactionCreate.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/client/disconnect.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/client/error.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/client/rateLimit.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/client/reconnecting.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/client/warn.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/guild/guildCreate.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/guild/guildDelete.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/interaction/interactionCreate.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/menu/interactionCreate.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/message/messageCreate.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/modal/interactionCreate.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/ready/ready.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/shard/shardDisconnect.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/shard/shardError.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/shard/shardReady.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/shard/shardReconnecting.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/events/shard/shardResume.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/events/thread/threadCreate.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/example.env", "downloaded_repos/Persian-Caesar_Ticker-Boy/functions/functions.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/antiCrash.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/extraEvents.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/keepAlive.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/slashCommandHandler.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/index.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/install.bat", "downloaded_repos/Persian-Caesar_Ticker-Boy/package.json", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/replit.nix", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/run.bat", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/start/1-events.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/start/2-handlers.js", "downloaded_repos/Persian-Caesar_Ticker-Boy/storage/colors.json", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/storage/config.js", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/storage/embed.json", "downloaded_repos/Persian-Caesar_Ticker-Boy/storage/emotes.json", "downloaded_repos/Persian-Caesar_Ticker-Boy/storage/status.json", "downloaded_repos/Persian-<PERSON>_Ticker-Boy/update.bat"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7103190422058105, "profiling_times": {"config_time": 6.339917898178101, "core_time": 3.431248188018799, "ignores_time": 0.002052783966064453, "total_time": 9.775429964065552}, "parsing_time": {"total_time": 2.2772133350372314, "per_file_time": {"mean": 0.05421936511993409, "std_dev": 0.010717039019677967}, "very_slow_stats": {"time_ratio": 0.5319417986509725, "count_ratio": 0.07142857142857142}, "very_slow_files": [{"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Ticket 🎫/ticket.js", "ftime": 0.31893301010131836}, {"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Setup 💻/settings.js", "ftime": 0.42063212394714355}, {"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/events/button/interactionCreate.js", "ftime": 0.47177982330322266}]}, "scanning_time": {"total_time": 6.8783042430877686, "per_file_time": {"mean": 0.048438762275265966, "std_dev": 0.01997366278707134}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.9978253841400146, "per_file_and_rule_time": {"mean": 0.00984150435536953, "std_dev": 0.0003439956111424522}, "very_slow_stats": {"time_ratio": 0.07869204880919321, "count_ratio": 0.0049261083743842365}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Infos 📊/help.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.1572129726409912}]}, "tainting_time": {"total_time": 1.6902976036071777, "per_def_and_rule_time": {"mean": 0.07683170925487173, "std_dev": 0.014673210698218515}, "very_slow_stats": {"time_ratio": 0.9072135298609715, "count_ratio": 0.3181818181818182}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.0731959342956543}, {"fpath": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/slashCommandHandler.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.09047102928161621}, {"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/events/button/interactionCreate.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.2097470760345459}, {"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/events/ready/ready.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.21117496490478516}, {"fpath": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/start/1-events.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.2208709716796875}, {"fpath": "downloaded_repos/Persian-Caesar_Ticker-Boy/commands/Setup 💻/settings.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.23473191261291504}, {"fpath": "downloaded_repos/Persian-<PERSON>_Ticker-Boy/handlers/keepAlive.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.4932689666748047}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}