{"version": "1.130.0", "results": [{"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/helpers/gDrive_sql.py", "start": {"line": 30, "col": 24, "offset": 878}, "end": {"line": 30, "col": 48, "offset": 902}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/helpers/gDrive_sql.py", "start": {"line": 52, "col": 24, "offset": 1634}, "end": {"line": 52, "col": 66, "offset": 1676}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/LICENSE", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/Procfile", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/README.md", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/app.json", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/bot.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/config.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/helpers/__init__.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/helpers/gDrive_sql.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/helpers/parent_id_sql.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/plugins/copy.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/plugins/help.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/plugins/main.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/plugins/token.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/plugins/uploader.py", "downloaded_repos/cdfxscrq_GDrive-Uploader-TG-Bot/requirements.txt"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.896003007888794, "profiling_times": {"config_time": 5.993196964263916, "core_time": 2.740462303161621, "ignores_time": 0.002071380615234375, "total_time": 8.737003326416016}, "parsing_time": {"total_time": 0.20917296409606934, "per_file_time": {"mean": 0.019015724008733578, "std_dev": 3.124009885524617e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.1312971115112305, "per_file_time": {"mean": 0.02759261247588367, "std_dev": 0.0016640241185935814}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.17691731452941895, "per_file_and_rule_time": {"mean": 0.0018238898405094736, "std_dev": 8.402686854652947e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.048607826232910156, "per_def_and_rule_time": {"mean": 0.0008837786587801847, "std_dev": 2.1098147391050354e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}