# XZ Utils Czech translation
# This file is put in the public domain.
# <PERSON><PERSON> <<EMAIL>>, 2010.
#
msgid ""
msgstr ""
"Project-Id-Version: xz-utils\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2010-12-03 11:32+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech <<EMAIL>>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2\n"
"X-Poedit-Language: Czech\n"
"X-Poedit-SourceCharset: utf-8\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr ""

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr ""

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr ""

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr ""

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Neznámý typ formátu souboru"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Neznámý typ kontroly integrity"

#: src/xz/args.c
#, fuzzy
#| msgid "Only one file can be specified with `--files' or `--files0'."
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Spolu s přepínači „--files“ nebo „--files0“ může být zadán pouze jeden soubor"

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr ""

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "Proměnná prostředí %s obsahuje příliš mnoho argumentů"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr ""

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr ""

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr ""

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr ""

#: src/xz/args.c
#, fuzzy
#| msgid "%s: With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "%s: S přepínačem --format=raw je vyžadován --sufix=.PRIP, vyjma zápisu do standardního výstupu"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "Maximální počet filtrů je čtyři"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr ""

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "Omezení použitelné paměti je příliš malé pro dané nastavení filtru."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr ""

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Použití přednastavení v režimu raw je nevhodné."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "Přesné volby u přednastavení se mohou lišit mezi různými verzemi softwaru."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "Formát .lzma podporuje pouze filtr LZMA1"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA1 nelze použít s formátem .xz"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr ""

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr ""

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Unsupported options"
msgid "Unsupported options in filter chain %u"
msgstr "Nepodporovaná volba"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr ""

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Nepodporovaný omezující filtr nebo volby filtru"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "Dekomprimace bude vyžadovat %s MiB paměti."

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Přizpůsobit velikost slovníku LZMA%c z %s MiB na %s MiB, tak aby nebylo překročeno omezení použitelné paměti %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr ""

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Přizpůsobit velikost slovníku LZMA%c z %s MiB na %s MiB, tak aby nebylo překročeno omezení použitelné paměti %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Přizpůsobit velikost slovníku LZMA%c z %s MiB na %s MiB, tak aby nebylo překročeno omezení použitelné paměti %s MiB"

#: src/xz/coder.c
#, fuzzy, c-format
#| msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Přizpůsobit velikost slovníku LZMA%c z %s MiB na %s MiB, tak aby nebylo překročeno omezení použitelné paměti %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr ""

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr ""

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr ""

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Vypadá to, že soubor byl přesunut, proto nebude odstraněn"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Nelze odstranit: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: Nelze nastavit vlastníka souboru: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: Nelze nastavit skupinu souboru: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: Nelze nastavit oprávnění souboru: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Selhalo zavření souboru: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: Selhalo zavření souboru: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr ""

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Jedná se o symbolický odkaz, vynechává se"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Jedná se o složku, vynechává se"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Nejedná se o běžný soubor, vynechává se"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: Soubor má nastavený bit setuid nebo setgid, vynechává se"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: Soubor má nastavený bit sticky, vynechává se"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: Vstupní soubor má více než jeden pevný odkaz, vynechává se"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Prázdný název souboru, vynechává se"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr ""

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr ""

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Selhalo zavření souboru: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Not a regular file, skipping"
msgid "%s: Destination is not a regular file"
msgstr "%s: Nejedná se o běžný soubor, vynechává se"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Chyba při obnovení příznaku O_APPEND na standardní výstup: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Selhalo zavření souboru: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Selhalo nastavení pozice při pokusu o vytvoření souboru řídké matice: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Chyba čtení: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Chyba při posunu v rámci souboru: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Neočekávaný konec souboru"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Chyba zápisu: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Vypnuto"

#: src/xz/hardware.c
#, fuzzy
#| msgid "Total amount of physical memory (RAM): "
msgid "Amount of physical memory (RAM):"
msgstr "Celkové množství fyzické paměti (RAM):     "

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr ""

#: src/xz/hardware.c
msgid "Compression:"
msgstr ""

#: src/xz/hardware.c
msgid "Decompression:"
msgstr ""

#: src/xz/hardware.c
#, fuzzy
#| msgid "Memory usage limit for decompression:  "
msgid "Multi-threaded decompression:"
msgstr "Omezení použitelné paměti pro dekomprimaci:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr ""

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr ""

#: src/xz/hardware.c
#, fuzzy
#| msgid "Memory usage limit reached"
msgid "Memory usage limits:"
msgstr "Dosaženo omezení použitelné paměti"

#: src/xz/list.c
msgid "Streams:"
msgstr ""

#: src/xz/list.c
msgid "Blocks:"
msgstr ""

#: src/xz/list.c
#, fuzzy
#| msgid "  Compressed size:    %s\n"
msgid "Compressed size:"
msgstr "  Komprimovaná velikost:   %s\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Uncompressed size:  %s\n"
msgid "Uncompressed size:"
msgstr "  Nekomprimovaná velikost: %s\n"

#: src/xz/list.c
msgid "Ratio:"
msgstr ""

#: src/xz/list.c
msgid "Check:"
msgstr ""

#: src/xz/list.c
#, fuzzy
#| msgid "  Stream padding:     %s\n"
msgid "Stream Padding:"
msgstr "  Zarovnání proudu:        %s\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Memory needed:      %s MiB\n"
msgid "Memory needed:"
msgstr "  Potřebná paměť:          %s MiB\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Sizes in headers:   %s\n"
msgid "Sizes in headers:"
msgstr "  Velikosti v hlavičkách:  %s\n"

#: src/xz/list.c
#, fuzzy
#| msgid "  Number of files:    %s\n"
msgid "Number of files:"
msgstr "  Počet souborů:           %s\n"

#: src/xz/list.c
msgid "Stream"
msgstr ""

#: src/xz/list.c
msgid "Block"
msgstr ""

#: src/xz/list.c
msgid "Blocks"
msgstr ""

#: src/xz/list.c
msgid "CompOffset"
msgstr ""

#: src/xz/list.c
msgid "UncompOffset"
msgstr ""

#: src/xz/list.c
msgid "CompSize"
msgstr ""

#: src/xz/list.c
msgid "UncompSize"
msgstr ""

#: src/xz/list.c
#, fuzzy
#| msgid "Totals:"
msgid "TotalSize"
msgstr "Celkem:"

#: src/xz/list.c
msgid "Ratio"
msgstr ""

#: src/xz/list.c
msgid "Check"
msgstr ""

#: src/xz/list.c
msgid "CheckVal"
msgstr ""

#: src/xz/list.c
msgid "Padding"
msgstr ""

#: src/xz/list.c
msgid "Header"
msgstr ""

#: src/xz/list.c
msgid "Flags"
msgstr ""

#: src/xz/list.c
msgid "MemUsage"
msgstr ""

#: src/xz/list.c
msgid "Filters"
msgstr ""

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "žádná"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "neznámá-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "neznámá-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "neznámá-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "neznámá-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "neznámá-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "neznámá-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "neznámá-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "neznámá-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "neznámá-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "neznámá-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "neznámá-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "neznámá-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: Soubor je prázdný"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: Je příliš malý na to, aby to mohl být platný soubor .xz"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "Proud   Bloky      Komprim    Nekomprim  Poměr  Kontrl  Název souboru"

#: src/xz/list.c
msgid "Yes"
msgstr "Ano"

#: src/xz/list.c
msgid "No"
msgstr "Ne"

#: src/xz/list.c
msgid "Minimum XZ Utils version:"
msgstr ""

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s soubor\n"
msgstr[1] "%s soubory\n"
msgstr[2] "%s souborů\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Celkem:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list pracuje pouze se soubory .xz (--format=xz nebo --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr ""

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list nepodporuje čtení ze standardního vstupu"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Chyba při čtení názvů souborů: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Neočekávaný konec vstupu při čtení názvů souborů"

#: src/xz/main.c
#, fuzzy, c-format
#| msgid "%s: Null character found when reading filenames; maybe you meant to use `--files0' instead of `--files'?"
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Byl nalezen nulový znak při čtení názvů souborů; nechtěli jste náhodou použít „--files0“ místo „--files“?"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Komprimace a dekomprimace s přepínačem --robot není zatím podporovaná."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "Ze standardního vstupu nelze číst data, když se ze standardního vstupu načítají názvy souborů"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr ""

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Interní chyba"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "Nelze ustanovit ovladač signálu"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Žádná kontrola integrity; integrita souboru se nebude ověřovat"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "Nepodporovaný typ kontroly integrity; integrita souboru se nebude ověřovat"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Dosaženo omezení použitelné paměti"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "Formát souboru nebyl rozpoznán"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Nepodporovaná volba"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Komprimovaná data jsou poškozená"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Neočekávaný konec vstupu"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr ""

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "Je vyžadováno %s MiB paměti. Limit je %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Omezující filtr: %s\n"

#: src/xz/message.c
#, fuzzy, c-format
#| msgid "Try `%s --help' for more information."
msgid "Try '%s --help' for more information."
msgstr "Zkuste „%s --help“ pro více informací"

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr ""

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "Usage: %s [OPTION]... [FILE]...\n"
#| "Compress or decompress FILEs in the .xz format.\n"
#| "\n"
msgid "Compress or decompress FILEs in the .xz format."
msgstr ""
"Použití: %s [PŘEPÍNAČ]... [SOUBOR]...\n"
"Komprimuje nebo dekomprimuje SOUBORy ve formátu xz.\n"
"\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Mandatory arguments to long options are mandatory for short options too.\n"
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "Povinné argumenty pro dlouhé přepínače jsou povinné rovněž pro krátké přepínače.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "Operation mode:"
msgstr "Operační režim:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Memory usage limit for decompression:  "
msgid "force compression"
msgstr "Omezení použitelné paměti pro dekomprimaci:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Memory usage limit for decompression:  "
msgid "force decompression"
msgstr "Omezení použitelné paměti pro dekomprimaci:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Operation modifiers:\n"
msgid "Operation modifiers:"
msgstr ""
"\n"
"Modifikátory operací:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Writing to standard output failed"
msgid "write to standard output and don't delete input files"
msgstr "Zápis do standardního výstupu selhal"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr ""

#: src/xz/message.c
msgid ".SUF"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr ""

#: src/xz/message.c
msgid "FILE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Basic file format and compression options:\n"
msgid "Basic file format and compression options:"
msgstr ""
"\n"
"Základní přepínače pro formát souboru a komprimaci:\n"

#: src/xz/message.c
msgid "FORMAT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr ""

#: src/xz/message.c
msgid "NAME"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't verify the integrity check when decompressing"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -0 ... -9           compression preset; default is 6; take compressor *and*\n"
#| "                      decompressor memory usage into account before using 7-9!"
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr ""
" -0 .. -9              přednastavení komprimace; výchozí je 6; než použijete\n"
"                       hodnoty 7 – 9, vezměte do úvahy množství použité paměti"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -e, --extreme       try to improve compression ratio by using more CPU time;\n"
#| "                      does not affect decompressor memory requirements"
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr ""
" -e, --extreme         zkusit zlepšit poměr komprimace využitím více času\n"
"                       procesoru; nemá vliv na paměťové nároky dekomprimace"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr ""

#: src/xz/message.c
msgid "SIZE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr ""

#: src/xz/message.c
msgid "BLOCKS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr ""

#: src/xz/message.c
msgid "LIMIT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --no-adjust     if compression settings exceed the memory usage limit,\n"
#| "                      give an error instead of adjusting the settings downwards"
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr ""
"     --no-adjust       pokud nastavení komprimace přesáhne omezení použitelné\n"
"                       paměti, předat chybu namísto snížení nastavení"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Custom filter chain for compression (alternative for using presets):"
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr ""
"\n"
"Vlastní omezující filtr pro komprimaci (alternativa k použití přednastavených):"

#: src/xz/message.c
msgid "FILTERS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display more information about the liblzma filter string syntax and exit"
msgstr ""

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr ""

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr ""

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr ""

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of position bits"
msgstr ""

#: src/xz/message.c
msgid "MODE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "compression mode"
msgstr "Operační režim:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Other options:\n"
msgid "Other options:"
msgstr ""
"\n"
" Ostatní přepínače:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -Q, --no-warn       make warnings not affect the exit status"
msgid "make warnings not affect the exit status"
msgstr " -Q, --no-warn         způsobí, že varování neovlivní stav ukončení"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --robot         use machine-parsable messages (useful for scripts)"
msgid "use machine-parsable messages (useful for scripts)"
msgstr ""
"     --robot           použít strojově analyzovatelné zprávy (užitečné pro\n"
"                       skripty)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --info-memory   display the total amount of RAM and the currently active\n"
#| "                      memory usage limits, and exit"
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr ""
"     --info-memory     zobrazit celkové množství paměti RAM a současné aktivní\n"
"                       omezení použitelné paměti a skončit"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -V, --version       display the version number and exit"
msgid "display the version number and exit"
msgstr " -V, --version         zobrazit číslo verze a skončit"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "\n"
#| "With no FILE, or when FILE is -, read standard input.\n"
msgid "With no FILE, or when FILE is -, read standard input."
msgstr ""
"\n"
"Pokud SOUBOR není zadán nebo pokud je -, bude se číst ze standardního vstupu.\n"

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "Report bugs to <%s> (in English or Finnish).\n"
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr "Chyby hlaste na <%s> (v angličtině nebo finštině).\n"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "%s home page: <%s>\n"
msgid "%s home page: <%s>"
msgstr "Domovská stránka %s: <%s>\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, c-format
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "The supported filters and their options are:"
msgstr "Nepodporovaný omezující filtr nebo volby filtru"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Options must be `name=value' pairs separated with commas"
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "%s: Volby musí být páry „název=hodnota“ oddělené čárkami"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Neplatný název volby"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option value"
msgid "Invalid option value"
msgstr "%s: Neplatná hodnota volby"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "Nepodporované přednastavení LZMA1/LZMA2: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "Součet lc a lp nesmí překročit hodnotu 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: Název souboru má neznámou příponu, vynechává se"

#: src/xz/suffix.c
#, fuzzy, c-format
#| msgid "%s: File already has `%s' suffix, skipping"
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: Soubor již má příponu „%s“, vynechává se"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Neplatná přípona názvu souboru"

#: src/xz/util.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Value is not a non-negative decimal integer"
msgid "Value is not a non-negative decimal integer"
msgstr "%s: Hodnota není nezáporné desítkové číslo"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Neplatná jednotka s předponou"

#: src/xz/util.c
#, fuzzy
#| msgid "Valid suffixes are `KiB' (2^10), `MiB' (2^20), and `GiB' (2^30)."
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Platné jednotky s předponami jsou „KiB“ (2^10 B), „MiB“ (2^20 B) a „GiB“ (2^30 B)."

#: src/xz/util.c
#, fuzzy, c-format
#| msgid "Value of the option `%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "Hodnota volby „%s“ musí být v rozsahu [%<PRIu64>, %<PRIu64>]"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "Z terminálu nelze číst komprimovaná data"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "Do terminálu nelze zapisovat komprimovaná data"

#: src/lzmainfo/lzmainfo.c
#, c-format
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
msgid "Show information stored in the .lzma file header."
msgstr ""

#: src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid "%s: Too small to be a valid .xz file"
msgid "File is too small to be a .lzma file"
msgstr "%s: Je příliš malý na to, aby to mohl být platný soubor .xz"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr ""

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Zápis do standardního výstupu selhal"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Neznámá chyba"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported options"
msgid "Unsupported preset"
msgstr "Nepodporovaná volba"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "Unsupported flag in the preset"
msgstr "Nepodporovaný omezující filtr nebo volby filtru"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option name"
msgid "Unknown option name"
msgstr "%s: Neplatný název volby"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr ""

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid multiplier suffix"
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "%s: Neplatná jednotka s předponou"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Unknown file format type"
msgid "Unknown filter name"
msgstr "%s: Neznámý typ formátu souboru"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "LZMA1 cannot be used with the .xz format"
msgid "This filter cannot be used in the .xz format"
msgstr "LZMA1 nelze použít s formátem .xz"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr ""

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Maximum number of filters is four"
msgid "The maximum number of filters is four"
msgstr "Maximální počet filtrů je čtyři"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr ""

#~ msgid ""
#~ "  -z, --compress      force compression\n"
#~ "  -d, --decompress    force decompression\n"
#~ "  -t, --test          test compressed file integrity\n"
#~ "  -l, --list          list information about .xz files"
#~ msgstr ""
#~ " -z, --compress        provést komprimaci\n"
#~ " -d, --decompress      provést dekomprimaci\n"
#~ " -t, --test            testovat integritu komprimovaného souboru\n"
#~ " -l, --list            vypsat informace o souborech .xz"

#~ msgid ""
#~ "  -k, --keep          keep (don't delete) input files\n"
#~ "  -f, --force         force overwrite of output file and (de)compress links\n"
#~ "  -c, --stdout        write to standard output and don't delete input files"
#~ msgstr ""
#~ " -k, --keep            zachovat (nemazat) vstupní soubory\n"
#~ " -f, --force           vynutit přepis výstupního souboru a de/komprimovat odkazy\n"
#~ " -c, --stdout          zapisovat na standardní výstup a nemazat vstupní soubory"

#, fuzzy
#~| msgid ""
#~| "      --no-sparse     do not create sparse files when decompressing\n"
#~| "  -S, --suffix=.SUF   use the suffix `.SUF' on compressed files\n"
#~| "      --files[=FILE]  read filenames to process from FILE; if FILE is\n"
#~| "                      omitted, filenames are read from the standard input;\n"
#~| "                      filenames must be terminated with the newline character\n"
#~| "      --files0[=FILE] like --files but use the null character as terminator"
#~ msgid ""
#~ "      --no-sparse     do not create sparse files when decompressing\n"
#~ "  -S, --suffix=.SUF   use the suffix '.SUF' on compressed files\n"
#~ "      --files[=FILE]  read filenames to process from FILE; if FILE is\n"
#~ "                      omitted, filenames are read from the standard input;\n"
#~ "                      filenames must be terminated with the newline character\n"
#~ "      --files0[=FILE] like --files but use the null character as terminator"
#~ msgstr ""
#~ "     --no-sparse       nevytvářet při dekomprimaci soubory řídkých matic\n"
#~ " -S, --suffix=.PRIP    použít u komprimovaných souborů příponu „.PRIP“\n"
#~ "     --files[=SOUBOR]  číst názvy souborů, které se mají zpracovat, ze SOUBORu;\n"
#~ "                       pokud není SOUBOR zadán, čte se ze standardního vstupu;\n"
#~ "                       názvy souborů musí být zakončeny znakem nového řádku\n"
#~ "     --files0[=SOUBOR] stejné jako --files, ale použít k zakončování nulový znak"

#, fuzzy
#~| msgid ""
#~| "  -F, --format=FMT    file format to encode or decode; possible values are\n"
#~| "                      `auto' (default), `xz', `lzma', and `raw'\n"
#~| "  -C, --check=CHECK   integrity check type: `none' (use with caution),\n"
#~| "                      `crc32', `crc64' (default), or `sha256'"
#~ msgid ""
#~ "  -F, --format=FMT    file format to encode or decode; possible values are\n"
#~ "                      'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'\n"
#~ "  -C, --check=CHECK   integrity check type: 'none' (use with caution),\n"
#~ "                      'crc32', 'crc64' (default), or 'sha256'"
#~ msgstr ""
#~ " -F, --format=FORMÁT   formát souboru k zakódování nebo dekódování; možné\n"
#~ "                       hodnoty jsou „auto“ (výchozí), „xz“, „lzma“ a „raw“\n"
#~ " -C, --check=KONTROLA  typ kontroly integrity: „none“ (používejte s rozmyslem),\n"
#~ "                       „crc32“, „crc64“ (výchozí) nebo „sha256“"

#, fuzzy, no-c-format
#~| msgid ""
#~| "      --memlimit-compress=LIMIT\n"
#~| "      --memlimit-decompress=LIMIT\n"
#~| "  -M, --memlimit=LIMIT\n"
#~| "                      set memory usage limit for compression, decompression,\n"
#~| "                      or both; LIMIT is in bytes, % of RAM, or 0 for defaults"
#~ msgid ""
#~ "      --memlimit-compress=LIMIT\n"
#~ "      --memlimit-decompress=LIMIT\n"
#~ "      --memlimit-mt-decompress=LIMIT\n"
#~ "  -M, --memlimit=LIMIT\n"
#~ "                      set memory usage limit for compression, decompression,\n"
#~ "                      threaded decompression, or all of these; LIMIT is in\n"
#~ "                      bytes, % of RAM, or 0 for defaults"
#~ msgstr ""
#~ "     --memlimit-compress=LIMIT\n"
#~ "     --memlimit-decompress=LIMIT\n"
#~ " -M, --memlimit=LIMIT\n"
#~ "                       nastaví omezení použitelné paměti pro komprimaci,\n"
#~ "                       dekomprimaci nebo obojí; LIMIT je v bajtech, % z paměti\n"
#~ "                       RAM nebo 0 pro výchozí"

#~ msgid ""
#~ "\n"
#~ "  --lzma1[=OPTS]      LZMA1 or LZMA2; OPTS is a comma-separated list of zero or\n"
#~ "  --lzma2[=OPTS]      more of the following options (valid values; default):\n"
#~ "                        preset=PRE reset options to a preset (0-9[e])\n"
#~ "                        dict=NUM   dictionary size (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=NUM     number of literal context bits (0-4; 3)\n"
#~ "                        lp=NUM     number of literal position bits (0-4; 0)\n"
#~ "                        pb=NUM     number of position bits (0-4; 2)\n"
#~ "                        mode=MODE  compression mode (fast, normal; normal)\n"
#~ "                        nice=NUM   nice length of a match (2-273; 64)\n"
#~ "                        mf=NAME    match finder (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=NUM  maximum search depth; 0=automatic (default)"
#~ msgstr ""
#~ "\n"
#~ " --lzma1[=VOLBY]       LZMA1 nebo LZMA2; VOLBY je čárkou oddělovaný seznam žádné\n"
#~ " --lzma2[=VOLBY]       nebo více následujících voleb (platné hodnoty; výchozí):\n"
#~ "                         preset=PŘE změnit volby na PŘEdnastavené (0 – 9[e])\n"
#~ "                         dict=POČ   velikost slovníku (4 KiB – 1536 MiB; 8 MiB)\n"
#~ "                         lc=POČ     počet kontextových bitů literálu (0 – 4; 3)\n"
#~ "                         lp=POČ     počet pozičních bitů literálu (0 – 4; 0)\n"
#~ "                         pb=POČ     počet pozičních bitů (0 – 4; 2)\n"
#~ "                         mode=REŽIM režim komprimace (fast, normal; normal)\n"
#~ "                         nice=NUM   příznivá délka shody (2 – 273; 64)\n"
#~ "                         mf=NÁZEV   hledání shod (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                         depth=POČ  maximální hloubka prohledávání;\n"
#~ "                                    0 = automaticky (výchozí)"

#, fuzzy
#~| msgid ""
#~| "\n"
#~| "  --x86[=OPTS]        x86 BCJ filter (32-bit and 64-bit)\n"
#~| "  --powerpc[=OPTS]    PowerPC BCJ filter (big endian only)\n"
#~| "  --ia64[=OPTS]       IA-64 (Itanium) BCJ filter\n"
#~| "  --arm[=OPTS]        ARM BCJ filter (little endian only)\n"
#~| "  --armthumb[=OPTS]   ARM-Thumb BCJ filter (little endian only)\n"
#~| "  --sparc[=OPTS]      SPARC BCJ filter\n"
#~| "                      Valid OPTS for all BCJ filters:\n"
#~| "                        start=NUM  start offset for conversions (default=0)"
#~ msgid ""
#~ "\n"
#~ "  --x86[=OPTS]        x86 BCJ filter (32-bit and 64-bit)\n"
#~ "  --arm[=OPTS]        ARM BCJ filter\n"
#~ "  --armthumb[=OPTS]   ARM-Thumb BCJ filter\n"
#~ "  --arm64[=OPTS]      ARM64 BCJ filter\n"
#~ "  --powerpc[=OPTS]    PowerPC BCJ filter (big endian only)\n"
#~ "  --ia64[=OPTS]       IA-64 (Itanium) BCJ filter\n"
#~ "  --sparc[=OPTS]      SPARC BCJ filter\n"
#~ "  --riscv[=OPTS]      RISC-V BCJ filter\n"
#~ "                      Valid OPTS for all BCJ filters:\n"
#~ "                        start=NUM  start offset for conversions (default=0)"
#~ msgstr ""
#~ "\n"
#~ " --x86[=VOLBY]         Filtr x86 BCJ (32bitový a 64bitový)\n"
#~ " --powerpc[=VOLBY]     Filtr PowerPC BCJ (pouze big endian)\n"
#~ " --ia64[=VOLBY]        Filtr IA64 (Itanium) BCJ\n"
#~ " --arm[=VOLBY]         Filtr ARM BCJ (pouze little endian)\n"
#~ " --armthumb[=VOLBY]    Filtr ARM-Thumb BCJ (pouze little endian)\n"
#~ " --sparc[=VOLBY]       Filtr SPARC BCJ\n"
#~ "                       Platné volby pro všechny filtry BCJ:\n"
#~ "                         start=POČ  počáteční posun pro převody (výchozí=0)"

#~ msgid ""
#~ "\n"
#~ "  --delta[=OPTS]      Delta filter; valid OPTS (valid values; default):\n"
#~ "                        dist=NUM   distance between bytes being subtracted\n"
#~ "                                   from each other (1-256; 1)"
#~ msgstr ""
#~ "\n"
#~ " --delta[=VOLBY]       Filtr Delta; platné VOLBY (platné hodnoty; výchozí):\n"
#~ "                         dist=POČ   vzdálenost mezi bajty, které jsou odečítány\n"
#~ "                                    jeden od druhého (1 – 256; 1)"

#~ msgid ""
#~ "  -q, --quiet         suppress warnings; specify twice to suppress errors too\n"
#~ "  -v, --verbose       be verbose; specify twice for even more verbose"
#~ msgstr ""
#~ " -q, --quiet           potlačit varování; zadáním dvakrát, potlačíte i chyby\n"
#~ " -v, --verbose         podrobnější zprávy; zadáním dvakrát, budou ještě\n"
#~ "                       podrobnější"

#~ msgid ""
#~ "  -h, --help          display the short help (lists only the basic options)\n"
#~ "  -H, --long-help     display this long help and exit"
#~ msgstr ""
#~ " -h, --help            zobrazit krátkou nápovědu (vypíše jen základní přepínače)\n"
#~ " -H, --long-help       zobrazit tuto úplnou nápovědu a skončit"

#~ msgid ""
#~ "  -h, --help          display this short help and exit\n"
#~ "  -H, --long-help     display the long help (lists also the advanced options)"
#~ msgstr ""
#~ " -h, --help            zobrazit tuto zkrácenou nápovědu a skončit\n"
#~ " -H, --long-help       zobrazit úplnou nápovědu (vypíše i pokročilé přepínače)"

#~ msgid "Memory usage limit for compression:    "
#~ msgstr "Omezení použitelné paměti pro komprimaci:  "

#, c-format
#~ msgid "  Streams:            %s\n"
#~ msgstr "  Proudů:                  %s\n"

#, c-format
#~ msgid "  Blocks:             %s\n"
#~ msgstr "  Bloků:                   %s\n"

#, c-format
#~ msgid "  Ratio:              %s\n"
#~ msgstr "  Poměr komprimace:        %s\n"

#, c-format
#~ msgid "  Check:              %s\n"
#~ msgstr "  Typ kontroly:            %s\n"

#~ msgid ""
#~ "  Streams:\n"
#~ "    Stream    Blocks      CompOffset    UncompOffset        CompSize      UncompSize  Ratio  Check      Padding"
#~ msgstr ""
#~ "  Proudy:\n"
#~ "     Proud     Bloky     KomprPozice   NekomprPozice   KomprVelikost NekomprVelikost  Poměr  Kontrola   Zarovnání"

#, c-format
#~ msgid ""
#~ "  Blocks:\n"
#~ "    Stream     Block      CompOffset    UncompOffset       TotalSize      UncompSize  Ratio  Check"
#~ msgstr ""
#~ "  Bloky:\n"
#~ "     Proud      Blok     KomprPozice   NekomprPozice    CelkVelikost NekomprVelikost  Poměr  Kontrola"

#, c-format
#~ msgid "      CheckVal %*s Header  Flags        CompSize    MemUsage  Filters"
#~ msgstr "   KontrHod %*s Hlavič  Příznaky     KomprVel    PoužiPam  Filtry"

#, c-format
#~ msgid "The selected match finder requires at least nice=%<PRIu32>"
#~ msgstr "Vybraný vyhledávač shod vyžaduje minimálně nice=%<PRIu32>"

#~ msgid "Limit was %s MiB, but %s MiB would have been needed"
#~ msgstr "Limit byl %s MiB, ale bylo by zapotřebí %s MiB"

#~ msgid "%s MiB (%s bytes)\n"
#~ msgstr "%s MiB (%s bajtů)\n"

#~ msgid ""
#~ "  -e, --extreme       use more CPU time when encoding to increase compression\n"
#~ "                      ratio without increasing memory usage of the decoder"
#~ msgstr ""
#~ " -e, --extreme         využít více procesorového času pro kódování, čímž se\n"
#~ "                       zvýší kompresní poměr bez zvýšení paměti použité kodérem"

#~ msgid ""
#~ "  -M, --memory=NUM    use roughly NUM bytes of memory at maximum; 0 indicates\n"
#~ "                      the default setting, which is 40 % of total RAM"
#~ msgstr ""
#~ " -M, --memory=POČ      použít zhruba POČ bajtů paměti jako maximum; 0 znamená\n"
#~ "                       výchozí nastavení, což je 40% celkového množství paměti"

#~ msgid ""
#~ "\n"
#~ "  --subblock[=OPTS]   Subblock filter; valid OPTS (valid values; default):\n"
#~ "                        size=NUM   number of bytes of data per subblock\n"
#~ "                                   (1 - 256Mi; 4Ki)\n"
#~ "                        rle=NUM    run-length encoder chunk size (0-256; 0)"
#~ msgstr ""
#~ "\n"
#~ " --subblock[=VOLBY]    Subblokový filtr; platné VOLBY (platné hodnoty; výchozí):\n"
#~ "                         size=POČ   počet bajtů dat na subblok\n"
#~ "                                    (1 - 256 Mi; 4 Ki)\n"
#~ "                         rle=POČ    velikost dávky pro kodér run-length (0-256; 0)"

#~ msgid ""
#~ "On this system and configuration, this program will use a maximum of roughly\n"
#~ "%s MiB RAM and "
#~ msgstr ""
#~ "Na tomto systému a s tímto nastavením použije tento program maximum ze zhruba\n"
#~ "%s MiB RAM a "

#~ msgid ""
#~ "one thread.\n"
#~ "\n"
#~ msgstr ""
#~ "jedno vlákno.\n"
#~ "\n"

#~ msgid "%s: Invalid multiplier suffix. Valid suffixes:"
#~ msgstr "%s: Neplatná přípona. Platné přípony jsou:"
