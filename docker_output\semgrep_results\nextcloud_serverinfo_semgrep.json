{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/nextcloud_serverinfo/js/script.js", "start": {"line": 298, "col": 6, "offset": 9225}, "end": {"line": 298, "col": 72, "offset": 9291}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/nextcloud_serverinfo/js/script.js", "start": {"line": 299, "col": 6, "offset": 9297}, "end": {"line": 299, "col": 64, "offset": 9355}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/nextcloud_serverinfo/js/smoothie.js", "start": {"line": 643, "col": 7, "offset": 26615}, "end": {"line": 643, "col": 72, "offset": 26680}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/nextcloud_serverinfo/lib/OperatingSystems/FreeBSD.php", "start": {"line": 233, "col": 3, "offset": 5923}, "end": {"line": 233, "col": 50, "offset": 5970}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/nextcloud_serverinfo/lib/OperatingSystems/Linux.php", "start": {"line": 273, "col": 3, "offset": 6690}, "end": {"line": 273, "col": 50, "offset": 6737}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.phpinfo-use.phpinfo-use", "path": "downloaded_repos/nextcloud_serverinfo/lib/PhpInfoResponse.php", "start": {"line": 31, "col": 3, "offset": 693}, "end": {"line": 31, "col": 59, "offset": 749}, "extra": {"message": "The 'phpinfo' function may reveal sensitive information about your environment.", "metadata": {"cwe": ["CWE-200: Exposure of Sensitive Information to an Unauthorized Actor"], "references": ["https://www.php.net/manual/en/function.phpinfo", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/PhpinfosSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2021 - Broken Access Control"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.phpinfo-use.phpinfo-use", "shortlink": "https://sg.run/W82E"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mariadb.yml", "start": {"line": 144, "col": 26, "offset": 4876}, "end": {"line": 144, "col": 29, "offset": 4879}}]], "message": "Syntax error at line downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mariadb.yml:144:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mariadb.yml", "spans": [{"file": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mariadb.yml", "start": {"line": 144, "col": 26, "offset": 4876}, "end": {"line": 144, "col": 29, "offset": 4879}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mysql.yml", "start": {"line": 142, "col": 26, "offset": 4696}, "end": {"line": 142, "col": 29, "offset": 4699}}]], "message": "Syntax error at line downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mysql.yml:142:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mysql.yml", "spans": [{"file": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mysql.yml", "start": {"line": 142, "col": 26, "offset": 4696}, "end": {"line": 142, "col": 29, "offset": 4699}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-oci.yml", "start": {"line": 150, "col": 26, "offset": 4644}, "end": {"line": 150, "col": 29, "offset": 4647}}]], "message": "Syntax error at line downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-oci.yml:150:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-oci.yml", "spans": [{"file": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-oci.yml", "start": {"line": 150, "col": 26, "offset": 4644}, "end": {"line": 150, "col": 29, "offset": 4647}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-pgsql.yml", "start": {"line": 140, "col": 26, "offset": 4529}, "end": {"line": 140, "col": 29, "offset": 4532}}]], "message": "Syntax error at line downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-pgsql.yml:140:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-pgsql.yml", "spans": [{"file": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-pgsql.yml", "start": {"line": 140, "col": 26, "offset": 4529}, "end": {"line": 140, "col": 29, "offset": 4532}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-sqlite.yml", "start": {"line": 129, "col": 26, "offset": 4124}, "end": {"line": 129, "col": 29, "offset": 4127}}]], "message": "Syntax error at line downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-sqlite.yml:129:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-sqlite.yml", "spans": [{"file": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-sqlite.yml", "start": {"line": 129, "col": 26, "offset": 4124}, "end": {"line": 129, "col": 29, "offset": 4127}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/psalm.yml", "start": {"line": 56, "col": 55, "offset": 1917}, "end": {"line": 56, "col": 58, "offset": 1920}}]], "message": "Syntax error at line downloaded_repos/nextcloud_serverinfo/.github/workflows/psalm.yml:56:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/psalm.yml", "spans": [{"file": "downloaded_repos/nextcloud_serverinfo/.github/workflows/psalm.yml", "start": {"line": 56, "col": 55, "offset": 1917}, "end": {"line": 56, "col": 58, "offset": 1920}}]}], "paths": {"scanned": ["downloaded_repos/nextcloud_serverinfo/.git-blame-ignore-revs", "downloaded_repos/nextcloud_serverinfo/.gitattributes", "downloaded_repos/nextcloud_serverinfo/.github/CODEOWNERS", "downloaded_repos/nextcloud_serverinfo/.github/contributing.md", "downloaded_repos/nextcloud_serverinfo/.github/dependabot.yml", "downloaded_repos/nextcloud_serverinfo/.github/issue_template.md", "downloaded_repos/nextcloud_serverinfo/.github/issue_template.md.license", "downloaded_repos/nextcloud_serverinfo/.github/workflows/block-merge-freeze.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/block-unconventional-commits.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/dependabot-approve-merge.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/fixup.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/lint-php-cs.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/lint-php.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mariadb.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mysql.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-oci.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-pgsql.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-sqlite.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/pr-feedback.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/psalm.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/reuse.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/update-nextcloud-ocp-approve-merge.yml", "downloaded_repos/nextcloud_serverinfo/.github/workflows/update-nextcloud-ocp.yml", "downloaded_repos/nextcloud_serverinfo/.gitignore", "downloaded_repos/nextcloud_serverinfo/.php-cs-fixer.dist.php", "downloaded_repos/nextcloud_serverinfo/.tx/config", "downloaded_repos/nextcloud_serverinfo/AUTHORS.md", "downloaded_repos/nextcloud_serverinfo/COPYING", "downloaded_repos/nextcloud_serverinfo/LICENSES/AGPL-3.0-only.txt", "downloaded_repos/nextcloud_serverinfo/LICENSES/AGPL-3.0-or-later.txt", "downloaded_repos/nextcloud_serverinfo/LICENSES/CC0-1.0.txt", "downloaded_repos/nextcloud_serverinfo/LICENSES/MIT.txt", "downloaded_repos/nextcloud_serverinfo/README.md", "downloaded_repos/nextcloud_serverinfo/REUSE.toml", "downloaded_repos/nextcloud_serverinfo/appinfo/info.xml", "downloaded_repos/nextcloud_serverinfo/appinfo/routes.php", "downloaded_repos/nextcloud_serverinfo/composer.json", "downloaded_repos/nextcloud_serverinfo/composer.lock", "downloaded_repos/nextcloud_serverinfo/css/style.css", "downloaded_repos/nextcloud_serverinfo/img/app-dark.svg", "downloaded_repos/nextcloud_serverinfo/img/app.svg", "downloaded_repos/nextcloud_serverinfo/js/script.js", "downloaded_repos/nextcloud_serverinfo/js/smoothie.js", "downloaded_repos/nextcloud_serverinfo/l10n/.gitkeep", "downloaded_repos/nextcloud_serverinfo/l10n/af.js", "downloaded_repos/nextcloud_serverinfo/l10n/af.json", "downloaded_repos/nextcloud_serverinfo/l10n/an.js", "downloaded_repos/nextcloud_serverinfo/l10n/an.json", "downloaded_repos/nextcloud_serverinfo/l10n/ar.js", "downloaded_repos/nextcloud_serverinfo/l10n/ar.json", "downloaded_repos/nextcloud_serverinfo/l10n/ast.js", "downloaded_repos/nextcloud_serverinfo/l10n/ast.json", "downloaded_repos/nextcloud_serverinfo/l10n/az.js", "downloaded_repos/nextcloud_serverinfo/l10n/az.json", "downloaded_repos/nextcloud_serverinfo/l10n/be.js", "downloaded_repos/nextcloud_serverinfo/l10n/be.json", "downloaded_repos/nextcloud_serverinfo/l10n/bg.js", "downloaded_repos/nextcloud_serverinfo/l10n/bg.json", "downloaded_repos/nextcloud_serverinfo/l10n/bn_BD.js", "downloaded_repos/nextcloud_serverinfo/l10n/bn_BD.json", "downloaded_repos/nextcloud_serverinfo/l10n/br.js", "downloaded_repos/nextcloud_serverinfo/l10n/br.json", "downloaded_repos/nextcloud_serverinfo/l10n/ca.js", "downloaded_repos/nextcloud_serverinfo/l10n/ca.json", "downloaded_repos/nextcloud_serverinfo/l10n/cs.js", "downloaded_repos/nextcloud_serverinfo/l10n/cs.json", "downloaded_repos/nextcloud_serverinfo/l10n/cy_GB.js", "downloaded_repos/nextcloud_serverinfo/l10n/cy_GB.json", "downloaded_repos/nextcloud_serverinfo/l10n/da.js", "downloaded_repos/nextcloud_serverinfo/l10n/da.json", "downloaded_repos/nextcloud_serverinfo/l10n/de.js", "downloaded_repos/nextcloud_serverinfo/l10n/de.json", "downloaded_repos/nextcloud_serverinfo/l10n/de_DE.js", "downloaded_repos/nextcloud_serverinfo/l10n/de_DE.json", "downloaded_repos/nextcloud_serverinfo/l10n/el.js", "downloaded_repos/nextcloud_serverinfo/l10n/el.json", "downloaded_repos/nextcloud_serverinfo/l10n/en_GB.js", "downloaded_repos/nextcloud_serverinfo/l10n/en_GB.json", "downloaded_repos/nextcloud_serverinfo/l10n/eo.js", "downloaded_repos/nextcloud_serverinfo/l10n/eo.json", "downloaded_repos/nextcloud_serverinfo/l10n/es.js", "downloaded_repos/nextcloud_serverinfo/l10n/es.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_419.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_419.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_AR.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_AR.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_CL.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_CL.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_CO.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_CO.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_CR.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_CR.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_DO.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_DO.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_EC.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_EC.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_GT.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_GT.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_HN.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_HN.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_MX.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_MX.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_NI.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_NI.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_PA.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_PA.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_PE.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_PE.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_PR.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_PR.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_PY.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_PY.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_SV.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_SV.json", "downloaded_repos/nextcloud_serverinfo/l10n/es_UY.js", "downloaded_repos/nextcloud_serverinfo/l10n/es_UY.json", "downloaded_repos/nextcloud_serverinfo/l10n/et_EE.js", "downloaded_repos/nextcloud_serverinfo/l10n/et_EE.json", "downloaded_repos/nextcloud_serverinfo/l10n/eu.js", "downloaded_repos/nextcloud_serverinfo/l10n/eu.json", "downloaded_repos/nextcloud_serverinfo/l10n/fa.js", "downloaded_repos/nextcloud_serverinfo/l10n/fa.json", "downloaded_repos/nextcloud_serverinfo/l10n/fi.js", "downloaded_repos/nextcloud_serverinfo/l10n/fi.json", "downloaded_repos/nextcloud_serverinfo/l10n/fr.js", "downloaded_repos/nextcloud_serverinfo/l10n/fr.json", "downloaded_repos/nextcloud_serverinfo/l10n/ga.js", "downloaded_repos/nextcloud_serverinfo/l10n/ga.json", "downloaded_repos/nextcloud_serverinfo/l10n/gl.js", "downloaded_repos/nextcloud_serverinfo/l10n/gl.json", "downloaded_repos/nextcloud_serverinfo/l10n/he.js", "downloaded_repos/nextcloud_serverinfo/l10n/he.json", "downloaded_repos/nextcloud_serverinfo/l10n/hr.js", "downloaded_repos/nextcloud_serverinfo/l10n/hr.json", "downloaded_repos/nextcloud_serverinfo/l10n/hu.js", "downloaded_repos/nextcloud_serverinfo/l10n/hu.json", "downloaded_repos/nextcloud_serverinfo/l10n/hy.js", "downloaded_repos/nextcloud_serverinfo/l10n/hy.json", "downloaded_repos/nextcloud_serverinfo/l10n/ia.js", "downloaded_repos/nextcloud_serverinfo/l10n/ia.json", "downloaded_repos/nextcloud_serverinfo/l10n/id.js", "downloaded_repos/nextcloud_serverinfo/l10n/id.json", "downloaded_repos/nextcloud_serverinfo/l10n/is.js", "downloaded_repos/nextcloud_serverinfo/l10n/is.json", "downloaded_repos/nextcloud_serverinfo/l10n/it.js", "downloaded_repos/nextcloud_serverinfo/l10n/it.json", "downloaded_repos/nextcloud_serverinfo/l10n/ja.js", "downloaded_repos/nextcloud_serverinfo/l10n/ja.json", "downloaded_repos/nextcloud_serverinfo/l10n/ka.js", "downloaded_repos/nextcloud_serverinfo/l10n/ka.json", "downloaded_repos/nextcloud_serverinfo/l10n/ka_GE.js", "downloaded_repos/nextcloud_serverinfo/l10n/ka_GE.json", "downloaded_repos/nextcloud_serverinfo/l10n/kab.js", "downloaded_repos/nextcloud_serverinfo/l10n/kab.json", "downloaded_repos/nextcloud_serverinfo/l10n/km.js", "downloaded_repos/nextcloud_serverinfo/l10n/km.json", "downloaded_repos/nextcloud_serverinfo/l10n/ko.js", "downloaded_repos/nextcloud_serverinfo/l10n/ko.json", "downloaded_repos/nextcloud_serverinfo/l10n/lb.js", "downloaded_repos/nextcloud_serverinfo/l10n/lb.json", "downloaded_repos/nextcloud_serverinfo/l10n/lo.js", "downloaded_repos/nextcloud_serverinfo/l10n/lo.json", "downloaded_repos/nextcloud_serverinfo/l10n/lt_LT.js", "downloaded_repos/nextcloud_serverinfo/l10n/lt_LT.json", "downloaded_repos/nextcloud_serverinfo/l10n/lv.js", "downloaded_repos/nextcloud_serverinfo/l10n/lv.json", "downloaded_repos/nextcloud_serverinfo/l10n/mk.js", "downloaded_repos/nextcloud_serverinfo/l10n/mk.json", "downloaded_repos/nextcloud_serverinfo/l10n/mn.js", "downloaded_repos/nextcloud_serverinfo/l10n/mn.json", "downloaded_repos/nextcloud_serverinfo/l10n/ms_MY.js", "downloaded_repos/nextcloud_serverinfo/l10n/ms_MY.json", "downloaded_repos/nextcloud_serverinfo/l10n/my.js", "downloaded_repos/nextcloud_serverinfo/l10n/my.json", "downloaded_repos/nextcloud_serverinfo/l10n/nb.js", "downloaded_repos/nextcloud_serverinfo/l10n/nb.json", "downloaded_repos/nextcloud_serverinfo/l10n/nl.js", "downloaded_repos/nextcloud_serverinfo/l10n/nl.json", "downloaded_repos/nextcloud_serverinfo/l10n/nn_NO.js", "downloaded_repos/nextcloud_serverinfo/l10n/nn_NO.json", "downloaded_repos/nextcloud_serverinfo/l10n/oc.js", "downloaded_repos/nextcloud_serverinfo/l10n/oc.json", "downloaded_repos/nextcloud_serverinfo/l10n/pl.js", "downloaded_repos/nextcloud_serverinfo/l10n/pl.json", "downloaded_repos/nextcloud_serverinfo/l10n/ps.js", "downloaded_repos/nextcloud_serverinfo/l10n/ps.json", "downloaded_repos/nextcloud_serverinfo/l10n/pt_BR.js", "downloaded_repos/nextcloud_serverinfo/l10n/pt_BR.json", "downloaded_repos/nextcloud_serverinfo/l10n/pt_PT.js", "downloaded_repos/nextcloud_serverinfo/l10n/pt_PT.json", "downloaded_repos/nextcloud_serverinfo/l10n/ro.js", "downloaded_repos/nextcloud_serverinfo/l10n/ro.json", "downloaded_repos/nextcloud_serverinfo/l10n/ru.js", "downloaded_repos/nextcloud_serverinfo/l10n/ru.json", "downloaded_repos/nextcloud_serverinfo/l10n/sc.js", "downloaded_repos/nextcloud_serverinfo/l10n/sc.json", "downloaded_repos/nextcloud_serverinfo/l10n/si.js", "downloaded_repos/nextcloud_serverinfo/l10n/si.json", "downloaded_repos/nextcloud_serverinfo/l10n/sk.js", "downloaded_repos/nextcloud_serverinfo/l10n/sk.json", "downloaded_repos/nextcloud_serverinfo/l10n/sl.js", "downloaded_repos/nextcloud_serverinfo/l10n/sl.json", "downloaded_repos/nextcloud_serverinfo/l10n/sq.js", "downloaded_repos/nextcloud_serverinfo/l10n/sq.json", "downloaded_repos/nextcloud_serverinfo/l10n/sr.js", "downloaded_repos/nextcloud_serverinfo/l10n/sr.json", "downloaded_repos/nextcloud_serverinfo/l10n/<EMAIL>", "downloaded_repos/nextcloud_serverinfo/l10n/<EMAIL>", "downloaded_repos/nextcloud_serverinfo/l10n/sv.js", "downloaded_repos/nextcloud_serverinfo/l10n/sv.json", "downloaded_repos/nextcloud_serverinfo/l10n/sw.js", "downloaded_repos/nextcloud_serverinfo/l10n/sw.json", "downloaded_repos/nextcloud_serverinfo/l10n/th.js", "downloaded_repos/nextcloud_serverinfo/l10n/th.json", "downloaded_repos/nextcloud_serverinfo/l10n/tr.js", "downloaded_repos/nextcloud_serverinfo/l10n/tr.json", "downloaded_repos/nextcloud_serverinfo/l10n/ug.js", "downloaded_repos/nextcloud_serverinfo/l10n/ug.json", "downloaded_repos/nextcloud_serverinfo/l10n/uk.js", "downloaded_repos/nextcloud_serverinfo/l10n/uk.json", "downloaded_repos/nextcloud_serverinfo/l10n/uz.js", "downloaded_repos/nextcloud_serverinfo/l10n/uz.json", "downloaded_repos/nextcloud_serverinfo/l10n/vi.js", "downloaded_repos/nextcloud_serverinfo/l10n/vi.json", "downloaded_repos/nextcloud_serverinfo/l10n/zh_CN.js", "downloaded_repos/nextcloud_serverinfo/l10n/zh_CN.json", "downloaded_repos/nextcloud_serverinfo/l10n/zh_HK.js", "downloaded_repos/nextcloud_serverinfo/l10n/zh_HK.json", "downloaded_repos/nextcloud_serverinfo/l10n/zh_TW.js", "downloaded_repos/nextcloud_serverinfo/l10n/zh_TW.json", "downloaded_repos/nextcloud_serverinfo/lib/Commands/UpdateStorageStats.php", "downloaded_repos/nextcloud_serverinfo/lib/Controller/ApiController.php", "downloaded_repos/nextcloud_serverinfo/lib/Controller/PageController.php", "downloaded_repos/nextcloud_serverinfo/lib/DatabaseStatistics.php", "downloaded_repos/nextcloud_serverinfo/lib/FpmStatistics.php", "downloaded_repos/nextcloud_serverinfo/lib/Jobs/UpdateStorageStats.php", "downloaded_repos/nextcloud_serverinfo/lib/OperatingSystems/Dummy.php", "downloaded_repos/nextcloud_serverinfo/lib/OperatingSystems/FreeBSD.php", "downloaded_repos/nextcloud_serverinfo/lib/OperatingSystems/IOperatingSystem.php", "downloaded_repos/nextcloud_serverinfo/lib/OperatingSystems/Linux.php", "downloaded_repos/nextcloud_serverinfo/lib/Os.php", "downloaded_repos/nextcloud_serverinfo/lib/PhpInfoResponse.php", "downloaded_repos/nextcloud_serverinfo/lib/PhpStatistics.php", "downloaded_repos/nextcloud_serverinfo/lib/Resources/CPU.php", "downloaded_repos/nextcloud_serverinfo/lib/Resources/Disk.php", "downloaded_repos/nextcloud_serverinfo/lib/Resources/Memory.php", "downloaded_repos/nextcloud_serverinfo/lib/Resources/NetInterface.php", "downloaded_repos/nextcloud_serverinfo/lib/Resources/ThermalZone.php", "downloaded_repos/nextcloud_serverinfo/lib/SessionStatistics.php", "downloaded_repos/nextcloud_serverinfo/lib/Settings/AdminSection.php", "downloaded_repos/nextcloud_serverinfo/lib/Settings/AdminSettings.php", "downloaded_repos/nextcloud_serverinfo/lib/ShareStatistics.php", "downloaded_repos/nextcloud_serverinfo/lib/StorageStatistics.php", "downloaded_repos/nextcloud_serverinfo/lib/SystemStatistics.php", "downloaded_repos/nextcloud_serverinfo/psalm.xml", "downloaded_repos/nextcloud_serverinfo/templates/settings-admin.php", "downloaded_repos/nextcloud_serverinfo/vendor-bin/cs-fixer/composer.json", "downloaded_repos/nextcloud_serverinfo/vendor-bin/cs-fixer/composer.lock", "downloaded_repos/nextcloud_serverinfo/vendor-bin/phpunit/composer.json", "downloaded_repos/nextcloud_serverinfo/vendor-bin/phpunit/composer.lock", "downloaded_repos/nextcloud_serverinfo/vendor-bin/psalm/composer.json", "downloaded_repos/nextcloud_serverinfo/vendor-bin/psalm/composer.lock"], "skipped": [{"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mariadb.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-mysql.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-oci.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-pgsql.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/phpunit-sqlite.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_serverinfo/.github/workflows/psalm.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nextcloud_serverinfo/js/Chart.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/freebsd_interface_epair0b", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/freebsd_interface_pflog0", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/freebsd_meminfo", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/freebsd_net_get_interfaces.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/freebsd_swapinfo", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_cpuinfo", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_cpuinfo_one_core", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_cpuinfo_openpower", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_cpuinfo_pi3b", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_cpuinfo_pi4b", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_df_tp", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_meminfo", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_net_get_interfaces.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/linux_uptime", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/truenas_core_meminfo", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/data/truenas_core_swapinfo", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/lib/ApiControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/lib/DummyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/lib/FreeBSDTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/lib/LinuxTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/lib/NetInterfaceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/lib/SessionStatisticsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/phpunit.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/psalm-baseline.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_serverinfo/tests/stub.phpstub", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6090519428253174, "profiling_times": {"config_time": 5.664130687713623, "core_time": 3.9398064613342285, "ignores_time": 0.0016908645629882812, "total_time": 9.606772899627686}, "parsing_time": {"total_time": 1.457979679107666, "per_file_time": {"mean": 0.006177879996218923, "std_dev": 0.0004002795599975585}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 6.887151002883911, "per_file_time": {"mean": 0.009062040793268318, "std_dev": 0.0020457663972728514}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.269800901412964, "per_file_and_rule_time": {"mean": 0.0015740644253904052, "std_dev": 3.37183684404902e-05}, "very_slow_stats": {"time_ratio": 0.07066117590584686, "count_ratio": 0.0006934812760055479}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/nextcloud_serverinfo/js/smoothie.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1603868007659912}]}, "tainting_time": {"total_time": 0.46622419357299805, "per_def_and_rule_time": {"mean": 0.0008476803519509053, "std_dev": 7.71221215873234e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}