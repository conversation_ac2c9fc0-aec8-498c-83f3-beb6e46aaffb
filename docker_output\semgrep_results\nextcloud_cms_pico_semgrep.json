{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/nextcloud_cms_pico/js/pico.js", "start": {"line": 729, "col": 7, "offset": 15270}, "end": {"line": 729, "col": 30, "offset": 15293}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/nextcloud_cms_pico/lib/Files/LocalFile.php", "start": {"line": 61, "col": 9, "offset": 1666}, "end": {"line": 61, "col": 38, "offset": 1695}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/nextcloud_cms_pico/.gitattributes", "downloaded_repos/nextcloud_cms_pico/.github/CONTRIBUTING.md", "downloaded_repos/nextcloud_cms_pico/.github/workflows/stale.yml", "downloaded_repos/nextcloud_cms_pico/.github/workflows/test.yml", "downloaded_repos/nextcloud_cms_pico/.gitignore", "downloaded_repos/nextcloud_cms_pico/.phpcs.xml", "downloaded_repos/nextcloud_cms_pico/.scrutinizer.yml", "downloaded_repos/nextcloud_cms_pico/LICENSE", "downloaded_repos/nextcloud_cms_pico/Makefile", "downloaded_repos/nextcloud_cms_pico/README.md", "downloaded_repos/nextcloud_cms_pico/appdata/config/config.yml.template", "downloaded_repos/nextcloud_cms_pico/appdata/plugins/.gitignore", "downloaded_repos/nextcloud_cms_pico/appdata/templates/empty/assets/empty", "downloaded_repos/nextcloud_cms_pico/appdata/templates/empty/content/empty", "downloaded_repos/nextcloud_cms_pico/appdata/templates/sample_pico/assets/image.png", "downloaded_repos/nextcloud_cms_pico/appdata/templates/sample_pico/content/404.md", "downloaded_repos/nextcloud_cms_pico/appdata/templates/sample_pico/content/_meta.md", "downloaded_repos/nextcloud_cms_pico/appdata/templates/sample_pico/content/index.md", "downloaded_repos/nextcloud_cms_pico/appdata/templates/sample_pico/content/sub/index.md", "downloaded_repos/nextcloud_cms_pico/appdata/templates/sample_pico/content/sub/page.md", "downloaded_repos/nextcloud_cms_pico/appdata/templates/sample_pico/content/theme.md", "downloaded_repos/nextcloud_cms_pico/appdata/themes/.gitignore", "downloaded_repos/nextcloud_cms_pico/appdata_public/.gitignore", "downloaded_repos/nextcloud_cms_pico/appinfo/app.php", "downloaded_repos/nextcloud_cms_pico/appinfo/info.xml", "downloaded_repos/nextcloud_cms_pico/appinfo/routes.php", "downloaded_repos/nextcloud_cms_pico/composer.json", "downloaded_repos/nextcloud_cms_pico/composer.lock", "downloaded_repos/nextcloud_cms_pico/css/fontello.css", "downloaded_repos/nextcloud_cms_pico/css/pico.css", "downloaded_repos/nextcloud_cms_pico/css/pico.css.map", "downloaded_repos/nextcloud_cms_pico/css/pico.scss", "downloaded_repos/nextcloud_cms_pico/icon/COPYRIGHT.txt", "downloaded_repos/nextcloud_cms_pico/icon/LICENSE.MIT.txt", "downloaded_repos/nextcloud_cms_pico/icon/LICENSE.OFL-1.1.txt", "downloaded_repos/nextcloud_cms_pico/icon/config.json", "downloaded_repos/nextcloud_cms_pico/icon/fontello.eot", "downloaded_repos/nextcloud_cms_pico/icon/fontello.svg", "downloaded_repos/nextcloud_cms_pico/icon/fontello.ttf", "downloaded_repos/nextcloud_cms_pico/icon/fontello.woff", "downloaded_repos/nextcloud_cms_pico/icon/fontello.woff2", "downloaded_repos/nextcloud_cms_pico/img/pico_cms.svg", "downloaded_repos/nextcloud_cms_pico/js/admin.js", "downloaded_repos/nextcloud_cms_pico/js/personal.js", "downloaded_repos/nextcloud_cms_pico/js/pico.js", "downloaded_repos/nextcloud_cms_pico/l10n/.gitignore", "downloaded_repos/nextcloud_cms_pico/l10n/af.js", "downloaded_repos/nextcloud_cms_pico/l10n/af.json", "downloaded_repos/nextcloud_cms_pico/l10n/ar.js", "downloaded_repos/nextcloud_cms_pico/l10n/ar.json", "downloaded_repos/nextcloud_cms_pico/l10n/ast.js", "downloaded_repos/nextcloud_cms_pico/l10n/ast.json", "downloaded_repos/nextcloud_cms_pico/l10n/az.js", "downloaded_repos/nextcloud_cms_pico/l10n/az.json", "downloaded_repos/nextcloud_cms_pico/l10n/bg.js", "downloaded_repos/nextcloud_cms_pico/l10n/bg.json", "downloaded_repos/nextcloud_cms_pico/l10n/br.js", "downloaded_repos/nextcloud_cms_pico/l10n/br.json", "downloaded_repos/nextcloud_cms_pico/l10n/bs.js", "downloaded_repos/nextcloud_cms_pico/l10n/bs.json", "downloaded_repos/nextcloud_cms_pico/l10n/ca.js", "downloaded_repos/nextcloud_cms_pico/l10n/ca.json", "downloaded_repos/nextcloud_cms_pico/l10n/cs.js", "downloaded_repos/nextcloud_cms_pico/l10n/cs.json", "downloaded_repos/nextcloud_cms_pico/l10n/da.js", "downloaded_repos/nextcloud_cms_pico/l10n/da.json", "downloaded_repos/nextcloud_cms_pico/l10n/de.js", "downloaded_repos/nextcloud_cms_pico/l10n/de.json", "downloaded_repos/nextcloud_cms_pico/l10n/de_DE.js", "downloaded_repos/nextcloud_cms_pico/l10n/de_DE.json", "downloaded_repos/nextcloud_cms_pico/l10n/el.js", "downloaded_repos/nextcloud_cms_pico/l10n/el.json", "downloaded_repos/nextcloud_cms_pico/l10n/en_GB.js", "downloaded_repos/nextcloud_cms_pico/l10n/en_GB.json", "downloaded_repos/nextcloud_cms_pico/l10n/eo.js", "downloaded_repos/nextcloud_cms_pico/l10n/eo.json", "downloaded_repos/nextcloud_cms_pico/l10n/es.js", "downloaded_repos/nextcloud_cms_pico/l10n/es.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_419.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_419.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_AR.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_AR.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_CL.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_CL.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_CO.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_CO.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_CR.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_CR.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_DO.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_DO.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_EC.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_EC.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_GT.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_GT.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_HN.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_HN.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_MX.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_MX.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_NI.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_NI.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_PA.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_PA.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_PE.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_PE.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_PR.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_PR.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_PY.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_PY.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_SV.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_SV.json", "downloaded_repos/nextcloud_cms_pico/l10n/es_UY.js", "downloaded_repos/nextcloud_cms_pico/l10n/es_UY.json", "downloaded_repos/nextcloud_cms_pico/l10n/et_EE.js", "downloaded_repos/nextcloud_cms_pico/l10n/et_EE.json", "downloaded_repos/nextcloud_cms_pico/l10n/eu.js", "downloaded_repos/nextcloud_cms_pico/l10n/eu.json", "downloaded_repos/nextcloud_cms_pico/l10n/fa.js", "downloaded_repos/nextcloud_cms_pico/l10n/fa.json", "downloaded_repos/nextcloud_cms_pico/l10n/fi.js", "downloaded_repos/nextcloud_cms_pico/l10n/fi.json", "downloaded_repos/nextcloud_cms_pico/l10n/fr.js", "downloaded_repos/nextcloud_cms_pico/l10n/fr.json", "downloaded_repos/nextcloud_cms_pico/l10n/ga.js", "downloaded_repos/nextcloud_cms_pico/l10n/ga.json", "downloaded_repos/nextcloud_cms_pico/l10n/gl.js", "downloaded_repos/nextcloud_cms_pico/l10n/gl.json", "downloaded_repos/nextcloud_cms_pico/l10n/he.js", "downloaded_repos/nextcloud_cms_pico/l10n/he.json", "downloaded_repos/nextcloud_cms_pico/l10n/hr.js", "downloaded_repos/nextcloud_cms_pico/l10n/hr.json", "downloaded_repos/nextcloud_cms_pico/l10n/hu.js", "downloaded_repos/nextcloud_cms_pico/l10n/hu.json", "downloaded_repos/nextcloud_cms_pico/l10n/hy.js", "downloaded_repos/nextcloud_cms_pico/l10n/hy.json", "downloaded_repos/nextcloud_cms_pico/l10n/ia.js", "downloaded_repos/nextcloud_cms_pico/l10n/ia.json", "downloaded_repos/nextcloud_cms_pico/l10n/id.js", "downloaded_repos/nextcloud_cms_pico/l10n/id.json", "downloaded_repos/nextcloud_cms_pico/l10n/is.js", "downloaded_repos/nextcloud_cms_pico/l10n/is.json", "downloaded_repos/nextcloud_cms_pico/l10n/it.js", "downloaded_repos/nextcloud_cms_pico/l10n/it.json", "downloaded_repos/nextcloud_cms_pico/l10n/ja.js", "downloaded_repos/nextcloud_cms_pico/l10n/ja.json", "downloaded_repos/nextcloud_cms_pico/l10n/ka.js", "downloaded_repos/nextcloud_cms_pico/l10n/ka.json", "downloaded_repos/nextcloud_cms_pico/l10n/ka_GE.js", "downloaded_repos/nextcloud_cms_pico/l10n/ka_GE.json", "downloaded_repos/nextcloud_cms_pico/l10n/kab.js", "downloaded_repos/nextcloud_cms_pico/l10n/kab.json", "downloaded_repos/nextcloud_cms_pico/l10n/kn.js", "downloaded_repos/nextcloud_cms_pico/l10n/kn.json", "downloaded_repos/nextcloud_cms_pico/l10n/ko.js", "downloaded_repos/nextcloud_cms_pico/l10n/ko.json", "downloaded_repos/nextcloud_cms_pico/l10n/lo.js", "downloaded_repos/nextcloud_cms_pico/l10n/lo.json", "downloaded_repos/nextcloud_cms_pico/l10n/lt_LT.js", "downloaded_repos/nextcloud_cms_pico/l10n/lt_LT.json", "downloaded_repos/nextcloud_cms_pico/l10n/lv.js", "downloaded_repos/nextcloud_cms_pico/l10n/lv.json", "downloaded_repos/nextcloud_cms_pico/l10n/mk.js", "downloaded_repos/nextcloud_cms_pico/l10n/mk.json", "downloaded_repos/nextcloud_cms_pico/l10n/mn.js", "downloaded_repos/nextcloud_cms_pico/l10n/mn.json", "downloaded_repos/nextcloud_cms_pico/l10n/nb.js", "downloaded_repos/nextcloud_cms_pico/l10n/nb.json", "downloaded_repos/nextcloud_cms_pico/l10n/nl.js", "downloaded_repos/nextcloud_cms_pico/l10n/nl.json", "downloaded_repos/nextcloud_cms_pico/l10n/nn_NO.js", "downloaded_repos/nextcloud_cms_pico/l10n/nn_NO.json", "downloaded_repos/nextcloud_cms_pico/l10n/oc.js", "downloaded_repos/nextcloud_cms_pico/l10n/oc.json", "downloaded_repos/nextcloud_cms_pico/l10n/pl.js", "downloaded_repos/nextcloud_cms_pico/l10n/pl.json", "downloaded_repos/nextcloud_cms_pico/l10n/pt_BR.js", "downloaded_repos/nextcloud_cms_pico/l10n/pt_BR.json", "downloaded_repos/nextcloud_cms_pico/l10n/pt_PT.js", "downloaded_repos/nextcloud_cms_pico/l10n/pt_PT.json", "downloaded_repos/nextcloud_cms_pico/l10n/ro.js", "downloaded_repos/nextcloud_cms_pico/l10n/ro.json", "downloaded_repos/nextcloud_cms_pico/l10n/ru.js", "downloaded_repos/nextcloud_cms_pico/l10n/ru.json", "downloaded_repos/nextcloud_cms_pico/l10n/sc.js", "downloaded_repos/nextcloud_cms_pico/l10n/sc.json", "downloaded_repos/nextcloud_cms_pico/l10n/si.js", "downloaded_repos/nextcloud_cms_pico/l10n/si.json", "downloaded_repos/nextcloud_cms_pico/l10n/sk.js", "downloaded_repos/nextcloud_cms_pico/l10n/sk.json", "downloaded_repos/nextcloud_cms_pico/l10n/sl.js", "downloaded_repos/nextcloud_cms_pico/l10n/sl.json", "downloaded_repos/nextcloud_cms_pico/l10n/sq.js", "downloaded_repos/nextcloud_cms_pico/l10n/sq.json", "downloaded_repos/nextcloud_cms_pico/l10n/sr.js", "downloaded_repos/nextcloud_cms_pico/l10n/sr.json", "downloaded_repos/nextcloud_cms_pico/l10n/sv.js", "downloaded_repos/nextcloud_cms_pico/l10n/sv.json", "downloaded_repos/nextcloud_cms_pico/l10n/th.js", "downloaded_repos/nextcloud_cms_pico/l10n/th.json", "downloaded_repos/nextcloud_cms_pico/l10n/tr.js", "downloaded_repos/nextcloud_cms_pico/l10n/tr.json", "downloaded_repos/nextcloud_cms_pico/l10n/ug.js", "downloaded_repos/nextcloud_cms_pico/l10n/ug.json", "downloaded_repos/nextcloud_cms_pico/l10n/uk.js", "downloaded_repos/nextcloud_cms_pico/l10n/uk.json", "downloaded_repos/nextcloud_cms_pico/l10n/uz.js", "downloaded_repos/nextcloud_cms_pico/l10n/uz.json", "downloaded_repos/nextcloud_cms_pico/l10n/vi.js", "downloaded_repos/nextcloud_cms_pico/l10n/vi.json", "downloaded_repos/nextcloud_cms_pico/l10n/zh_CN.js", "downloaded_repos/nextcloud_cms_pico/l10n/zh_CN.json", "downloaded_repos/nextcloud_cms_pico/l10n/zh_HK.js", "downloaded_repos/nextcloud_cms_pico/l10n/zh_HK.json", "downloaded_repos/nextcloud_cms_pico/l10n/zh_TW.js", "downloaded_repos/nextcloud_cms_pico/l10n/zh_TW.json", "downloaded_repos/nextcloud_cms_pico/lib/AppInfo/Application.php", "downloaded_repos/nextcloud_cms_pico/lib/Controller/ControllerTrait.php", "downloaded_repos/nextcloud_cms_pico/lib/Controller/PicoController.php", "downloaded_repos/nextcloud_cms_pico/lib/Controller/PluginsController.php", "downloaded_repos/nextcloud_cms_pico/lib/Controller/SettingsController.php", "downloaded_repos/nextcloud_cms_pico/lib/Controller/TemplatesController.php", "downloaded_repos/nextcloud_cms_pico/lib/Controller/ThemesController.php", "downloaded_repos/nextcloud_cms_pico/lib/Controller/WebsitesController.php", "downloaded_repos/nextcloud_cms_pico/lib/Db/WebsitesRequest.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/AssetInvalidPathException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/AssetNotFoundException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/AssetNotPermittedException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/ComposerException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/FilesystemNotLocalException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/FilesystemNotWritableException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/PageInvalidPathException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/PageNotFoundException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/PageNotPermittedException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/PicoRuntimeException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/PluginAlreadyExistsException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/PluginNotCompatibleException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/PluginNotFoundException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/TemplateAlreadyExistsException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/TemplateNotCompatibleException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/TemplateNotFoundException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/ThemeAlreadyExistsException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/ThemeNotCompatibleException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/ThemeNotFoundException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/WebsiteAlreadyExistsException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/WebsiteForeignOwnerException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/WebsiteInvalidDataException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/WebsiteInvalidFilesystemException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/WebsiteInvalidOwnerException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/WebsiteNotFoundException.php", "downloaded_repos/nextcloud_cms_pico/lib/Exceptions/WebsiteNotPermittedException.php", "downloaded_repos/nextcloud_cms_pico/lib/ExternalStorage/Backend.php", "downloaded_repos/nextcloud_cms_pico/lib/ExternalStorage/BackendProvider.php", "downloaded_repos/nextcloud_cms_pico/lib/ExternalStorage/Storage.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/AbstractLocalNode.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/AbstractNode.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/AbstractStorageNode.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/FileInterface.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/FolderInterface.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/FolderTrait.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/Glob/GlobIterator.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/Glob/GlobPattern.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/LocalFile.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/LocalFolder.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/NodeInterface.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/StorageFile.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/StorageFolder.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/StorageScanner.php", "downloaded_repos/nextcloud_cms_pico/lib/Files/StorageUserFolder.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/InternalServerErrorResponse.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/NotFoundResponse.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/NotModifiedResponse.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/NotPermittedResponse.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/PicoAssetResponse.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/PicoContentSecurityPolicy.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/PicoErrorResponse.php", "downloaded_repos/nextcloud_cms_pico/lib/Http/PicoPageResponse.php", "downloaded_repos/nextcloud_cms_pico/lib/Listener/ExternalStorageBackendEventListener.php", "downloaded_repos/nextcloud_cms_pico/lib/Listener/GroupDeletedEventListener.php", "downloaded_repos/nextcloud_cms_pico/lib/Listener/UserDeletedEventListener.php", "downloaded_repos/nextcloud_cms_pico/lib/Migration/AppDataRepairStep.php", "downloaded_repos/nextcloud_cms_pico/lib/Migration/MigrationTrait.php", "downloaded_repos/nextcloud_cms_pico/lib/Migration/Version010000.php", "downloaded_repos/nextcloud_cms_pico/lib/Migration/Version010000From000908.php", "downloaded_repos/nextcloud_cms_pico/lib/Migration/Version010017.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/DummyPluginFile.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/PicoAsset.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/PicoPage.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/Plugin.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/Template.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/TemplateFile.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/Theme.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/Website.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/WebsiteCore.php", "downloaded_repos/nextcloud_cms_pico/lib/Model/WebsiteRequest.php", "downloaded_repos/nextcloud_cms_pico/lib/Pico.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/AssetsService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/ConfigService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/FileService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/MiscService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/PicoService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/PluginsService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/TemplatesService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/ThemesService.php", "downloaded_repos/nextcloud_cms_pico/lib/Service/WebsitesService.php", "downloaded_repos/nextcloud_cms_pico/lib/Settings/Admin.php", "downloaded_repos/nextcloud_cms_pico/lib/Settings/AdminSection.php", "downloaded_repos/nextcloud_cms_pico/lib/Settings/Personal.php", "downloaded_repos/nextcloud_cms_pico/lib/Settings/PersonalSection.php", "downloaded_repos/nextcloud_cms_pico/lib/functions.php", "downloaded_repos/nextcloud_cms_pico/screenshots/apache.png", "downloaded_repos/nextcloud_cms_pico/screenshots/custom_plugins.png", "downloaded_repos/nextcloud_cms_pico/screenshots/custom_templates.png", "downloaded_repos/nextcloud_cms_pico/screenshots/custom_themes.png", "downloaded_repos/nextcloud_cms_pico/screenshots/list_websites.png", "downloaded_repos/nextcloud_cms_pico/screenshots/new_website.png", "downloaded_repos/nextcloud_cms_pico/screenshots/settings.png", "downloaded_repos/nextcloud_cms_pico/screenshots/website.png", "downloaded_repos/nextcloud_cms_pico/templates/403.php", "downloaded_repos/nextcloud_cms_pico/templates/404.php", "downloaded_repos/nextcloud_cms_pico/templates/500.php", "downloaded_repos/nextcloud_cms_pico/templates/error.php", "downloaded_repos/nextcloud_cms_pico/templates/settings.admin.php", "downloaded_repos/nextcloud_cms_pico/templates/settings.personal.php"], "skipped": [{"path": "downloaded_repos/nextcloud_cms_pico/tests/Integration/Stage1/PicoControllerIntegrationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Integration/Stage1/PluginsControllerIntegrationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Integration/Stage1/TemplatesControllerIntegrationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Integration/Stage1/ThemesControllerIntegrationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Integration/Stage1/WebsitesControllerIntegrationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Integration/Stage2/PicoIntegrationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Integration/Stage3/AppTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Manager/TestConfigManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Manager/TestExtensionManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Manager/TestFilesManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Manager/TestManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Manager/TestServicesManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Manager/TestUsersManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Manager/TestWebsitesManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Mocks/Service/ConfigService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Mocks/Service/PluginsService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/Utils/Mocks/Service/ThemesService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/output/PicoIntegrationTest/testGetIndex.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/output/PicoIntegrationTest/testGetSubPage.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/plugins/PicoTest/PicoTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/templates/test/assets/image.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/templates/test/content/index.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/templates/test/content/sub/index.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/templates/test/content/sub/page.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/data/themes/test/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nextcloud_cms_pico/tests/phpunit.xml", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9891140460968018, "profiling_times": {"config_time": 6.04423189163208, "core_time": 4.6985132694244385, "ignores_time": 0.0018298625946044922, "total_time": 10.745751142501831}, "parsing_time": {"total_time": 3.768320083618164, "per_file_time": {"mean": 0.013555108214453824, "std_dev": 0.0005905802140315137}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 12.940147399902344, "per_file_time": {"mean": 0.014034867028093643, "std_dev": 0.0016648524664611057}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.0247414112091064, "per_file_and_rule_time": {"mean": 0.00266028268356122, "std_dev": 3.3354277200237874e-05}, "very_slow_stats": {"time_ratio": 0.03401905225833226, "count_ratio": 0.0008795074758135445}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/nextcloud_cms_pico/js/personal.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10289883613586426}]}, "tainting_time": {"total_time": 0.4338841438293457, "per_def_and_rule_time": {"mean": 0.0011358223660454074, "std_dev": 1.4343519717847945e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}