{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 2, "col": 12, "offset": 55}, "end": {"line": 2, "col": 63, "offset": 106}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 65, "col": 7, "offset": 1951}, "end": {"line": 65, "col": 70, "offset": 2014}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 65, "col": 22, "offset": 1966}, "end": {"line": 65, "col": 69, "offset": 2013}, "extra": {"message": "Detection of write from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use write, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 118, "col": 7, "offset": 3931}, "end": {"line": 118, "col": 71, "offset": 3995}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 119, "col": 7, "offset": 4002}, "end": {"line": 119, "col": 47, "offset": 4042}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 149, "col": 9, "offset": 4988}, "end": {"line": 149, "col": 37, "offset": 5016}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 181, "col": 9, "offset": 6336}, "end": {"line": 181, "col": 39, "offset": 6366}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 202, "col": 7, "offset": 6794}, "end": {"line": 202, "col": 99, "offset": 6886}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "start": {"line": 205, "col": 5, "offset": 6915}, "end": {"line": 205, "col": 68, "offset": 6978}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/athombv_homey.ink/www/index.html", "start": {"line": 13, "col": 3, "offset": 577}, "end": {"line": 13, "col": 95, "offset": 669}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/athombv_homey.ink/www/index.html", "start": {"line": 14, "col": 5, "offset": 674}, "end": {"line": 14, "col": 102, "offset": 771}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/athombv_homey.ink/www/js/homey.ink.js", "start": {"line": 2, "col": 14, "offset": 59}, "end": {"line": 2, "col": 65, "offset": 110}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/athombv_homey.ink/www/js/ini.js", "start": {"line": 130, "col": 7, "offset": 3351}, "end": {"line": 130, "col": 18, "offset": 3362}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/athombv_homey.ink/.github/workflows/aws-s3-deploy-nodejs.yml", "downloaded_repos/athombv_homey.ink/.gitignore", "downloaded_repos/athombv_homey.ink/README.md", "downloaded_repos/athombv_homey.ink/app/css/homey.ink.color.css", "downloaded_repos/athombv_homey.ink/app/css/homey.ink.css", "downloaded_repos/athombv_homey.ink/app/css/homey.ink.eink.css", "downloaded_repos/athombv_homey.ink/app/css/themes/kobo-aura-hd.css", "downloaded_repos/athombv_homey.ink/app/css/themes/kobo-h2o.css", "downloaded_repos/athombv_homey.ink/app/css/themes/web.css", "downloaded_repos/athombv_homey.ink/app/img/devices/3d-printer-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/3d-printer.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/3d-printer2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/3d-printer2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/aquarium-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/aquarium.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/assistant-amazon-alexa-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/assistant-amazon-alexa.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/assistant-google-home-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/assistant-google-home.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/aux-cable-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/aux-cable.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/avr-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/avr.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/blinds-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/blinds.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/camera-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/camera.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/cd-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/cd.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/christmas-lights-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/christmas-lights.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/christmas-tree-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/christmas-tree.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/climate-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/climate.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/climate2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/climate2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/coffee-machine-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/coffee-machine.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/coffee-machine2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/coffee-machine2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/curtains-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/curtains.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/door-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/door.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/doorbell-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/doorbell.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/doorbell2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/doorbell2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/dryer-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/dryer.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/dvd-bluray-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/dvd-bluray.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/fan-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/fan.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/fan2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/fan2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/fish-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/fish.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/fridge-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/fridge.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/game-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/game.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/garage-door-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/garage-door.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/generic-cube-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/generic-cube.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/generic-rectangle-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/generic-rectangle.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/heating-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/heating.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/ipod-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/ipod.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/kettle-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/kettle.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/keys-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/keys.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/laptop-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/laptop.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/letterbox-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/letterbox.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/letterbox2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/letterbox2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-bulb-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-bulb.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-bulb2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-bulb2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-bulb3-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-bulb3.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-desk-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-desk.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging3-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging3.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging4-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging4.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging5-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging5.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging6-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging6.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging7-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-hanging7.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-led-strip-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-led-strip.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-outdoor-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-outdoor.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-spot-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-spot.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-spot2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-spot2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-spot3-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-spot3.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-standing-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-standing.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-standing2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-standing2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-standing3-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-standing3.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-wall-mounted-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-wall-mounted.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-wall-mounted2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-wall-mounted2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/light-wall-mounted3-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/light-wall-mounted3.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/lock-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/lock.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/lp-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/lp.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/multimedia-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/multimedia.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/oven-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/oven.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/pc-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/pc.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/plant1-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/plant1.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/plant2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/plant2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/plant3-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/plant3.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/plug-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/plug.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/plug2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/plug2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/pre-amp-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/pre-amp.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/printer-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/printer.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/projector-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/projector.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/remote-control-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/remote-control.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/router-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/router.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/scale-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/scale.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/set-top-box-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/set-top-box.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/smart-meter-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/smart-meter.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/smoke-detector-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/smoke-detector.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/socket-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/socket.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/solar-panels-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/solar-panels.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/soundbar-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/soundbar.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/speaker-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/speaker-stand-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/speaker-stand.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/speaker.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/speakerset-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/speakerset.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/subwoofer-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/subwoofer.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/sunshade-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/sunshade.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/sunshade2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/sunshade2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/switch-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/switch-double-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/switch-double.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/switch-single-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/switch-single.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/switch.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/thermostat-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/thermostat.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/tv-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/tv-av-combo-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/tv-av-combo.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/tv.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/vacuum-cleaner-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/vacuum-cleaner.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/vcr-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/vcr.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/video-processor-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/video-processor.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/washing-machine-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/washing-machine.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/washing-machine2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/washing-machine2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/window-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/window.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/window2-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/window2.svg", "downloaded_repos/athombv_homey.ink/app/img/devices/window3-128.png", "downloaded_repos/athombv_homey.ink/app/img/devices/window3.svg", "downloaded_repos/athombv_homey.ink/app/img/favicon.png", "downloaded_repos/athombv_homey.ink/app/img/logo-white.png", "downloaded_repos/athombv_homey.ink/app/img/logo.png", "downloaded_repos/athombv_homey.ink/app/img/play-color.png", "downloaded_repos/athombv_homey.ink/app/img/play.png", "downloaded_repos/athombv_homey.ink/app/img/test.png", "downloaded_repos/athombv_homey.ink/app/index.html", "downloaded_repos/athombv_homey.ink/app/js/homey.ink.app.js", "downloaded_repos/athombv_homey.ink/app/js/homey.ink.helpers.js", "downloaded_repos/athombv_homey.ink/app/js/later.js", "downloaded_repos/athombv_homey.ink/app/js/polyfill.js", "downloaded_repos/athombv_homey.ink/assets/devices/kobo-aura-h2o/screenshot.png", "downloaded_repos/athombv_homey.ink/assets/devices/kobo-aura-hd/photo.png", "downloaded_repos/athombv_homey.ink/assets/devices/kobo-aura-hd/screenshot.png", "downloaded_repos/athombv_homey.ink/package.json", "downloaded_repos/athombv_homey.ink/tools/convert-icons.js", "downloaded_repos/athombv_homey.ink/www/css/homey.ink.css", "downloaded_repos/athombv_homey.ink/www/img/bg.png", "downloaded_repos/athombv_homey.ink/www/img/device-kobo-aura-hd.png", "downloaded_repos/athombv_homey.ink/www/img/device-kobo-h2o.png", "downloaded_repos/athombv_homey.ink/www/img/hero-device.png", "downloaded_repos/athombv_homey.ink/www/img/hero-room.jpg", "downloaded_repos/athombv_homey.ink/www/img/hero-tile.png", "downloaded_repos/athombv_homey.ink/www/img/oauth-icon.png", "downloaded_repos/athombv_homey.ink/www/img/og-image.png", "downloaded_repos/athombv_homey.ink/www/index.html", "downloaded_repos/athombv_homey.ink/www/js/homey.ink.helpers.js", "downloaded_repos/athombv_homey.ink/www/js/homey.ink.js", "downloaded_repos/athombv_homey.ink/www/js/ini.js"], "skipped": [{"path": "downloaded_repos/athombv_homey.ink/app/js/athom-api.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/athombv_homey.ink/app/js/moment.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/athombv_homey.ink/assets/Homey.ink.sketch", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.6571190357208252, "profiling_times": {"config_time": 6.281470537185669, "core_time": 3.6358225345611572, "ignores_time": 0.0019943714141845703, "total_time": 9.920118808746338}, "parsing_time": {"total_time": 0.604487419128418, "per_file_time": {"mean": 0.050373951594034835, "std_dev": 0.005029897688296438}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.6434590816497803, "per_file_time": {"mean": 0.0053728843122963004, "std_dev": 0.0014301455610744931}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6268837451934814, "per_file_and_rule_time": {"mean": 0.007043637586443611, "std_dev": 0.00033837337723308407}, "very_slow_stats": {"time_ratio": 0.2303870057174022, "count_ratio": 0.011235955056179775}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/athombv_homey.ink/app/js/later.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.1444258689880371}]}, "tainting_time": {"total_time": 0.1442711353302002, "per_def_and_rule_time": {"mean": 0.0013872224550980788, "std_dev": 2.6491844585669598e-05}, "very_slow_stats": {"time_ratio": 0.36071040806984433, "count_ratio": 0.009615384615384616}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/athombv_homey.ink/app/js/later.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.05204010009765625}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}