{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/WordPress_two-factor/.coveralls.yml", "downloaded_repos/WordPress_two-factor/.distignore", "downloaded_repos/WordPress_two-factor/.editorconfig", "downloaded_repos/WordPress_two-factor/.eslintrc.json", "downloaded_repos/WordPress_two-factor/.github/ISSUE_TEMPLATE/1-bug-report.yml", "downloaded_repos/WordPress_two-factor/.github/ISSUE_TEMPLATE/2-enhancement.yml", "downloaded_repos/WordPress_two-factor/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/WordPress_two-factor/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/WordPress_two-factor/.github/SECURITY.md", "downloaded_repos/WordPress_two-factor/.github/workflows/deploy.yml", "downloaded_repos/WordPress_two-factor/.github/workflows/test.yml", "downloaded_repos/WordPress_two-factor/.gitignore", "downloaded_repos/WordPress_two-factor/.nvmrc", "downloaded_repos/WordPress_two-factor/.wordpress-org/banner-1544x500.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/banner-772x250.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/icon-128x128.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/icon-256x256.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/icon.svg", "downloaded_repos/WordPress_two-factor/.wordpress-org/screenshot-1.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/screenshot-2.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/screenshot-3.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/screenshot-4.png", "downloaded_repos/WordPress_two-factor/.wordpress-org/screenshot-5.png", "downloaded_repos/WordPress_two-factor/.wp-env.json", "downloaded_repos/WordPress_two-factor/Gruntfile.js", "downloaded_repos/WordPress_two-factor/LICENSE.md", "downloaded_repos/WordPress_two-factor/RELEASING.md", "downloaded_repos/WordPress_two-factor/SECURITY.md", "downloaded_repos/WordPress_two-factor/assets/blueprints/blueprint.json", "downloaded_repos/WordPress_two-factor/class-two-factor-compat.php", "downloaded_repos/WordPress_two-factor/class-two-factor-core.php", "downloaded_repos/WordPress_two-factor/composer.json", "downloaded_repos/WordPress_two-factor/composer.lock", "downloaded_repos/WordPress_two-factor/includes/Google/u2f-api.js", "downloaded_repos/WordPress_two-factor/includes/Yubico/U2F.php", "downloaded_repos/WordPress_two-factor/includes/function.login-footer.php", "downloaded_repos/WordPress_two-factor/includes/function.login-header.php", "downloaded_repos/WordPress_two-factor/package-lock.json", "downloaded_repos/WordPress_two-factor/package.json", "downloaded_repos/WordPress_two-factor/phpcs.xml.dist", "downloaded_repos/WordPress_two-factor/phpstan.dist.neon", "downloaded_repos/WordPress_two-factor/phpunit-watcher.yml.dist", "downloaded_repos/WordPress_two-factor/phpunit.xml.dist", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-backup-codes.php", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-dummy.php", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-email.php", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-fido-u2f-admin-list-table.php", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-fido-u2f-admin.php", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-fido-u2f.php", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-provider.php", "downloaded_repos/WordPress_two-factor/providers/class-two-factor-totp.php", "downloaded_repos/WordPress_two-factor/providers/css/fido-u2f-admin.css", "downloaded_repos/WordPress_two-factor/providers/js/fido-u2f-admin-inline-edit.js", "downloaded_repos/WordPress_two-factor/providers/js/fido-u2f-admin.js", "downloaded_repos/WordPress_two-factor/providers/js/fido-u2f-login.js", "downloaded_repos/WordPress_two-factor/readme.md", "downloaded_repos/WordPress_two-factor/readme.txt", "downloaded_repos/WordPress_two-factor/two-factor.php", "downloaded_repos/WordPress_two-factor/user-edit.css"], "skipped": [{"path": "downloaded_repos/WordPress_two-factor/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/class-secure-dummy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/class-two-factor-core.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/logs/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-backup-codes-rest-api.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-backup-codes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-dummy-secure.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-dummy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-email.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-fido-u2f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-provider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-totp-rest-api.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/providers/class-two-factor-totp.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/WordPress_two-factor/tests/two-factor.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.647320032119751, "profiling_times": {"config_time": 6.054606914520264, "core_time": 3.2920725345611572, "ignores_time": 0.0019681453704833984, "total_time": 9.349423885345459}, "parsing_time": {"total_time": 1.20218825340271, "per_file_time": {"mean": 0.038780266238797094, "std_dev": 0.010558316859394857}, "very_slow_stats": {"time_ratio": 0.48173497915552355, "count_ratio": 0.03225806451612903}, "very_slow_files": [{"fpath": "downloaded_repos/WordPress_two-factor/package-lock.json", "ftime": 0.5791361331939697}]}, "scanning_time": {"total_time": 5.864785194396973, "per_file_time": {"mean": 0.039360974459040084, "std_dev": 0.016332569236010952}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.055238962173462, "per_file_and_rule_time": {"mean": 0.006896775040850544, "std_dev": 0.0003485298015638785}, "very_slow_stats": {"time_ratio": 0.20953465988468842, "count_ratio": 0.010067114093959731}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/WordPress_two-factor/includes/Google/u2f-api.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.12220096588134766}, {"fpath": "downloaded_repos/WordPress_two-factor/class-two-factor-core.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.14619088172912598}, {"fpath": "downloaded_repos/WordPress_two-factor/class-two-factor-core.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.16225194931030273}]}, "tainting_time": {"total_time": 0.5539829730987549, "per_def_and_rule_time": {"mean": 0.0004301110039586606, "std_dev": 9.386134540974659e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}