{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/scito_extract_otp_secrets/docker/Dockerfile", "start": {"line": 33, "col": 1, "offset": 1239}, "end": {"line": 33, "col": 57, "offset": 1295}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"python\", \"/extract/extract_otp_secrets.py\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 105, "col": 52, "offset": 3566}, "end": {"line": 105, "col": 104, "offset": 3618}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 106, "col": 9, "offset": 3566}, "end": {"line": 106, "col": 61, "offset": 3618}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml:105:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ steps.docker_build_qr_reader_latest.outputs.digest` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 105, "col": 52, "offset": 3566}, "end": {"line": 105, "col": 104, "offset": 3618}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 106, "col": 9, "offset": 3566}, "end": {"line": 106, "col": 61, "offset": 3618}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 230, "col": 61, "offset": 8026}, "end": {"line": 230, "col": 105, "offset": 8070}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 231, "col": 9, "offset": 8026}, "end": {"line": 231, "col": 61, "offset": 8078}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml:230:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ steps.docker_build_only_txt.outputs.digest` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 230, "col": 61, "offset": 8026}, "end": {"line": 230, "col": 105, "offset": 8070}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 231, "col": 9, "offset": 8026}, "end": {"line": 231, "col": 61, "offset": 8078}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 356, "col": 52, "offset": 12365}, "end": {"line": 356, "col": 104, "offset": 12417}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 357, "col": 9, "offset": 12365}, "end": {"line": 357, "col": 61, "offset": 12417}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml:356:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ steps.docker_build_qr_reader_latest.outputs.digest` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 356, "col": 52, "offset": 12365}, "end": {"line": 356, "col": 104, "offset": 12417}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "start": {"line": 357, "col": 9, "offset": 12365}, "end": {"line": 357, "col": 61, "offset": 12417}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 67, "col": 18, "offset": 2177}, "end": {"line": 67, "col": 35, "offset": 2194}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 68, "col": 63, "offset": 2177}, "end": {"line": 68, "col": 66, "offset": 2180}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:67:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ github.ref_name` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 67, "col": 18, "offset": 2177}, "end": {"line": 67, "col": 35, "offset": 2194}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 68, "col": 63, "offset": 2177}, "end": {"line": 68, "col": 66, "offset": 2180}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 77, "col": 29, "offset": 2865}, "end": {"line": 77, "col": 54, "offset": 2890}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 78, "col": 18, "offset": 2865}, "end": {"line": 78, "col": 46, "offset": 2893}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 79, "col": 25, "offset": 2865}, "end": {"line": 79, "col": 60, "offset": 2900}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 80, "col": 19, "offset": 2865}, "end": {"line": 80, "col": 48, "offset": 2894}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 81, "col": 22, "offset": 2865}, "end": {"line": 81, "col": 54, "offset": 2897}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 85, "col": 31, "offset": 2865}, "end": {"line": 85, "col": 53, "offset": 2887}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:77:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ steps.meta.outputs.date` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 77, "col": 29, "offset": 2865}, "end": {"line": 77, "col": 54, "offset": 2890}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 78, "col": 18, "offset": 2865}, "end": {"line": 78, "col": 46, "offset": 2893}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 79, "col": 25, "offset": 2865}, "end": {"line": 79, "col": 60, "offset": 2900}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 80, "col": 19, "offset": 2865}, "end": {"line": 80, "col": 48, "offset": 2894}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 81, "col": 22, "offset": 2865}, "end": {"line": 81, "col": 54, "offset": 2897}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 85, "col": 31, "offset": 2865}, "end": {"line": 85, "col": 53, "offset": 2887}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 170, "col": 44, "offset": 7900}, "end": {"line": 170, "col": 88, "offset": 7944}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:170:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ steps.docker_build_bullseye.outputs.digest` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 170, "col": 44, "offset": 7900}, "end": {"line": 170, "col": 88, "offset": 7944}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 182, "col": 20, "offset": 8837}, "end": {"line": 182, "col": 23, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 183, "col": 6, "offset": 8837}, "end": {"line": 183, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 184, "col": 6, "offset": 8837}, "end": {"line": 184, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 185, "col": 6, "offset": 8837}, "end": {"line": 185, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 186, "col": 6, "offset": 8837}, "end": {"line": 186, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 187, "col": 6, "offset": 8837}, "end": {"line": 187, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 188, "col": 6, "offset": 8837}, "end": {"line": 188, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 189, "col": 6, "offset": 8837}, "end": {"line": 189, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 190, "col": 6, "offset": 8837}, "end": {"line": 190, "col": 9, "offset": 8840}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 191, "col": 6, "offset": 8837}, "end": {"line": 191, "col": 9, "offset": 8840}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:182:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 182, "col": 20, "offset": 8837}, "end": {"line": 182, "col": 23, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 183, "col": 6, "offset": 8837}, "end": {"line": 183, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 184, "col": 6, "offset": 8837}, "end": {"line": 184, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 185, "col": 6, "offset": 8837}, "end": {"line": 185, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 186, "col": 6, "offset": 8837}, "end": {"line": 186, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 187, "col": 6, "offset": 8837}, "end": {"line": 187, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 188, "col": 6, "offset": 8837}, "end": {"line": 188, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 189, "col": 6, "offset": 8837}, "end": {"line": 189, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 190, "col": 6, "offset": 8837}, "end": {"line": 190, "col": 9, "offset": 8840}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 191, "col": 6, "offset": 8837}, "end": {"line": 191, "col": 9, "offset": 8840}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 216, "col": 31, "offset": 10609}, "end": {"line": 216, "col": 53, "offset": 10631}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 220, "col": 23, "offset": 10609}, "end": {"line": 220, "col": 26, "offset": 10612}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 221, "col": 26, "offset": 10609}, "end": {"line": 221, "col": 29, "offset": 10612}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:216:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ secrets.GITHUB_TOKEN` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 216, "col": 31, "offset": 10609}, "end": {"line": 216, "col": 53, "offset": 10631}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 220, "col": 23, "offset": 10609}, "end": {"line": 220, "col": 26, "offset": 10612}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 221, "col": 26, "offset": 10609}, "end": {"line": 221, "col": 29, "offset": 10612}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 328, "col": 20, "offset": 18352}, "end": {"line": 328, "col": 23, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 329, "col": 6, "offset": 18352}, "end": {"line": 329, "col": 9, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 330, "col": 6, "offset": 18352}, "end": {"line": 330, "col": 9, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 331, "col": 6, "offset": 18352}, "end": {"line": 331, "col": 9, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 332, "col": 6, "offset": 18352}, "end": {"line": 332, "col": 9, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 333, "col": 6, "offset": 18352}, "end": {"line": 333, "col": 9, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 334, "col": 6, "offset": 18352}, "end": {"line": 334, "col": 9, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 335, "col": 6, "offset": 18352}, "end": {"line": 335, "col": 9, "offset": 18355}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 336, "col": 6, "offset": 18352}, "end": {"line": 336, "col": 9, "offset": 18355}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:328:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 328, "col": 20, "offset": 18352}, "end": {"line": 328, "col": 23, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 329, "col": 6, "offset": 18352}, "end": {"line": 329, "col": 9, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 330, "col": 6, "offset": 18352}, "end": {"line": 330, "col": 9, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 331, "col": 6, "offset": 18352}, "end": {"line": 331, "col": 9, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 332, "col": 6, "offset": 18352}, "end": {"line": 332, "col": 9, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 333, "col": 6, "offset": 18352}, "end": {"line": 333, "col": 9, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 334, "col": 6, "offset": 18352}, "end": {"line": 334, "col": 9, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 335, "col": 6, "offset": 18352}, "end": {"line": 335, "col": 9, "offset": 18355}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 336, "col": 6, "offset": 18352}, "end": {"line": 336, "col": 9, "offset": 18355}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 340, "col": 20, "offset": 18945}, "end": {"line": 340, "col": 23, "offset": 18948}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:340:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 340, "col": 20, "offset": 18945}, "end": {"line": 340, "col": 23, "offset": 18948}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 89, "offset": 20297}, "end": {"line": 375, "col": 108, "offset": 20316}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 141, "offset": 20297}, "end": {"line": 375, "col": 163, "offset": 20319}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 239, "offset": 20297}, "end": {"line": 375, "col": 242, "offset": 20300}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 257, "offset": 20297}, "end": {"line": 375, "col": 260, "offset": 20300}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 294, "offset": 20297}, "end": {"line": 375, "col": 297, "offset": 20300}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:375:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.ASSET_MIME` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 89, "offset": 20297}, "end": {"line": 375, "col": 108, "offset": 20316}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 141, "offset": 20297}, "end": {"line": 375, "col": 163, "offset": 20319}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 239, "offset": 20297}, "end": {"line": 375, "col": 242, "offset": 20300}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 257, "offset": 20297}, "end": {"line": 375, "col": 260, "offset": 20300}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 375, "col": 294, "offset": 20297}, "end": {"line": 375, "col": 297, "offset": 20300}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 89, "offset": 20810}, "end": {"line": 380, "col": 108, "offset": 20829}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 141, "offset": 20810}, "end": {"line": 380, "col": 163, "offset": 20832}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 239, "offset": 20810}, "end": {"line": 380, "col": 242, "offset": 20813}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 257, "offset": 20810}, "end": {"line": 380, "col": 260, "offset": 20813}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 294, "offset": 20810}, "end": {"line": 380, "col": 297, "offset": 20813}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:380:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.ASSET_MIME` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 89, "offset": 20810}, "end": {"line": 380, "col": 108, "offset": 20829}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 141, "offset": 20810}, "end": {"line": 380, "col": 163, "offset": 20832}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 239, "offset": 20810}, "end": {"line": 380, "col": 242, "offset": 20813}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 257, "offset": 20810}, "end": {"line": 380, "col": 260, "offset": 20813}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 380, "col": 294, "offset": 20810}, "end": {"line": 380, "col": 297, "offset": 20813}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 401, "col": 28, "offset": 21821}, "end": {"line": 401, "col": 31, "offset": 21824}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 408, "col": 67, "offset": 21821}, "end": {"line": 408, "col": 70, "offset": 21824}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 430, "col": 113, "offset": 21821}, "end": {"line": 430, "col": 135, "offset": 21843}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 430, "col": 224, "offset": 21821}, "end": {"line": 430, "col": 227, "offset": 21824}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 433, "col": 113, "offset": 21821}, "end": {"line": 433, "col": 135, "offset": 21843}}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 433, "col": 224, "offset": 21821}, "end": {"line": 433, "col": 227, "offset": 21824}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml:401:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 401, "col": 28, "offset": 21821}, "end": {"line": 401, "col": 31, "offset": 21824}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 408, "col": 67, "offset": 21821}, "end": {"line": 408, "col": 70, "offset": 21824}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 430, "col": 113, "offset": 21821}, "end": {"line": 430, "col": 135, "offset": 21843}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 430, "col": 224, "offset": 21821}, "end": {"line": 430, "col": 227, "offset": 21824}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 433, "col": 113, "offset": 21821}, "end": {"line": 433, "col": 135, "offset": 21843}}, {"file": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "start": {"line": 433, "col": 224, "offset": 21821}, "end": {"line": 433, "col": 227, "offset": 21824}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scito_extract_otp_secrets/build.sh", "start": {"line": 662, "col": 63, "offset": 0}, "end": {"line": 662, "col": 66, "offset": 3}}, {"path": "downloaded_repos/scito_extract_otp_secrets/build.sh", "start": {"line": 670, "col": 59, "offset": 0}, "end": {"line": 670, "col": 62, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/scito_extract_otp_secrets/build.sh:662:\n `/ 2` was unexpected", "path": "downloaded_repos/scito_extract_otp_secrets/build.sh", "spans": [{"file": "downloaded_repos/scito_extract_otp_secrets/build.sh", "start": {"line": 662, "col": 63, "offset": 0}, "end": {"line": 662, "col": 66, "offset": 3}}, {"file": "downloaded_repos/scito_extract_otp_secrets/build.sh", "start": {"line": 670, "col": 59, "offset": 0}, "end": {"line": 670, "col": 62, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/scito_extract_otp_secrets/.devcontainer/Dockerfile", "downloaded_repos/scito_extract_otp_secrets/.devcontainer/devcontainer.json", "downloaded_repos/scito_extract_otp_secrets/.editorconfig", "downloaded_repos/scito_extract_otp_secrets/.envrc", "downloaded_repos/scito_extract_otp_secrets/.flake8", "downloaded_repos/scito_extract_otp_secrets/.github/copilot-instructions.md", "downloaded_repos/scito_extract_otp_secrets/.github/dependabot.yml", "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci.yml", "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "downloaded_repos/scito_extract_otp_secrets/.github/workflows/codeql-analysis.yml", "downloaded_repos/scito_extract_otp_secrets/.gitignore", "downloaded_repos/scito_extract_otp_secrets/.vscode/extensions.json", "downloaded_repos/scito_extract_otp_secrets/.vscode/launch.json", "downloaded_repos/scito_extract_otp_secrets/.vscode/settings.json", "downloaded_repos/scito_extract_otp_secrets/LICENSE", "downloaded_repos/scito_extract_otp_secrets/Pipfile", "downloaded_repos/scito_extract_otp_secrets/Pipfile.lock", "downloaded_repos/scito_extract_otp_secrets/README.md", "downloaded_repos/scito_extract_otp_secrets/build.sh", "downloaded_repos/scito_extract_otp_secrets/devbox.json", "downloaded_repos/scito_extract_otp_secrets/devenv.lock", "downloaded_repos/scito_extract_otp_secrets/devenv.nix", "downloaded_repos/scito_extract_otp_secrets/devenv.yaml", "downloaded_repos/scito_extract_otp_secrets/docker/.alias", "downloaded_repos/scito_extract_otp_secrets/docker/Dockerfile", "downloaded_repos/scito_extract_otp_secrets/docker/Dockerfile_only_txt", "downloaded_repos/scito_extract_otp_secrets/docs/Export-account-option-in-the-Google-Authenticator.webp", "downloaded_repos/scito_extract_otp_secrets/docs/Export-account-option-in-the-Google-Authenticator_300px.webp", "downloaded_repos/scito_extract_otp_secrets/docs/Exported-QR-codes.webp", "downloaded_repos/scito_extract_otp_secrets/docs/Exported-QR-codes_300px.webp", "downloaded_repos/scito_extract_otp_secrets/docs/README_TOC.md", "downloaded_repos/scito_extract_otp_secrets/docs/Transfer-accounts-option-in-the-Google-Authenticator.webp", "downloaded_repos/scito_extract_otp_secrets/docs/Transfer-accounts-option-in-the-Google-Authenticator_300px.webp", "downloaded_repos/scito_extract_otp_secrets/docs/cv2_capture_screenshot.png", "downloaded_repos/scito_extract_otp_secrets/docs/meta.md", "downloaded_repos/scito_extract_otp_secrets/example_export.png", "downloaded_repos/scito_extract_otp_secrets/example_export.txt", "downloaded_repos/scito_extract_otp_secrets/example_keepass_output.hotp.csv", "downloaded_repos/scito_extract_otp_secrets/example_keepass_output.totp.csv", "downloaded_repos/scito_extract_otp_secrets/example_output.csv", "downloaded_repos/scito_extract_otp_secrets/example_output.json", "downloaded_repos/scito_extract_otp_secrets/extract_otp_secrets.code-workspace", "downloaded_repos/scito_extract_otp_secrets/installer/build_dmg.sh", "downloaded_repos/scito_extract_otp_secrets/installer/extract_otp_secrets_macos_template.spec", "downloaded_repos/scito_extract_otp_secrets/installer/win_file_version_info_template.txt", "downloaded_repos/scito_extract_otp_secrets/mypy.ini", "downloaded_repos/scito_extract_otp_secrets/pyproject.toml", "downloaded_repos/scito_extract_otp_secrets/pytest.ini", "downloaded_repos/scito_extract_otp_secrets/requirements-dev.txt", "downloaded_repos/scito_extract_otp_secrets/requirements.txt", "downloaded_repos/scito_extract_otp_secrets/run_pytest.sh", "downloaded_repos/scito_extract_otp_secrets/setup.cfg", "downloaded_repos/scito_extract_otp_secrets/setup.py", "downloaded_repos/scito_extract_otp_secrets/src/extract_otp_secrets.py", "downloaded_repos/scito_extract_otp_secrets/src/google_auth.proto", "downloaded_repos/scito_extract_otp_secrets/src/protobuf_generated_python/google_auth_pb2.py", "downloaded_repos/scito_extract_otp_secrets/src/protobuf_generated_python/google_auth_pb2.pyi"], "skipped": [{"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_docker.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scito_extract_otp_secrets/.github/workflows/ci_release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scito_extract_otp_secrets/build.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/empty_file.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/example_export_only_totp.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/lena_std.tif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/only_comments.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output-n-v.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output-n-vv.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output-n-vvv.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output-n.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output-v.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output-vv.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output-vvv.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/print_verbose_output.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/printqr_output.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/qr_but_without_otp.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/test_export_wrong_content.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/test_export_wrong_data.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/test_export_wrong_prefix.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/test_googleauth_export.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/test_plus_problem_export.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/text_masquerading_as_image.jpeg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/data/url_list_output.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/extract_otp_secrets_img_unit_test.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/extract_otp_secrets_test.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/extract_otp_secrets_txt_unit_test.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scito_extract_otp_secrets/tests/utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.811357021331787, "profiling_times": {"config_time": 6.295001745223999, "core_time": 3.038884401321411, "ignores_time": 0.0019805431365966797, "total_time": 9.336854219436646}, "parsing_time": {"total_time": 0.49331092834472656, "per_file_time": {"mean": 0.023490996587844124, "std_dev": 0.001173279714502337}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.8317933082580566, "per_file_time": {"mean": 0.01337075407487633, "std_dev": 0.002504057812036597}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.39107584953308105, "per_file_and_rule_time": {"mean": 0.001678437122459575, "std_dev": 1.1354031841155013e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.21797728538513184, "per_def_and_rule_time": {"mean": 0.002691077597347307, "std_dev": 6.887936817975028e-05}, "very_slow_stats": {"time_ratio": 0.29432887473298164, "count_ratio": 0.012345679012345678}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/scito_extract_otp_secrets/src/extract_otp_secrets.py", "fline": 331, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.06415700912475586}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}