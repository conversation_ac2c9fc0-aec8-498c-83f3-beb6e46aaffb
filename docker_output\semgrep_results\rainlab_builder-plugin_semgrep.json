{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/rainlab_builder-plugin/classes/BlueprintGenerator.php", "start": {"line": 281, "col": 14, "offset": 7529}, "end": {"line": 281, "col": 27, "offset": 7542}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/rainlab_builder-plugin/classes/ComponentHelper.php", "start": {"line": 37, "col": 46, "offset": 807}, "end": {"line": 37, "col": 66, "offset": 827}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/rainlab_builder-plugin/classes/ComponentHelper.php", "start": {"line": 107, "col": 46, "offset": 2953}, "end": {"line": 107, "col": 66, "offset": 2973}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/rainlab_builder-plugin/classes/ControllerGenerator.php", "start": {"line": 303, "col": 14, "offset": 9159}, "end": {"line": 303, "col": 27, "offset": 9172}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.js", "start": {"line": 373, "col": 9, "offset": 13033}, "end": {"line": 373, "col": 48, "offset": 13072}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.js", "start": {"line": 409, "col": 56, "offset": 14282}, "end": {"line": 409, "col": 87, "offset": 14313}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.js", "start": {"line": 458, "col": 9, "offset": 16184}, "end": {"line": 458, "col": 52, "offset": 16227}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/rainlab_builder-plugin/models/CodeFileModel.php", "start": {"line": 171, "col": 18, "offset": 4498}, "end": {"line": 171, "col": 35, "offset": 4515}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/rainlab_builder-plugin/models/LocalizationModel.php", "start": {"line": 128, "col": 19, "offset": 3784}, "end": {"line": 128, "col": 36, "offset": 3801}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/rainlab_builder-plugin/models/MigrationModel.php", "start": {"line": 400, "col": 18, "offset": 11748}, "end": {"line": 400, "col": 49, "offset": 11779}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/rainlab_builder-plugin/models/MigrationModel.php", "start": {"line": 444, "col": 10, "offset": 12873}, "end": {"line": 444, "col": 33, "offset": 12896}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/rainlab_builder-plugin/models/MigrationModel.php", "start": {"line": 458, "col": 14, "offset": 13329}, "end": {"line": 458, "col": 37, "offset": 13352}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/models/pluginbasemodel/templates/plugin.php.tpl:1:\n `}` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/models/pluginbasemodel/templates/plugin.php.tpl"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl", "start": {"line": 20, "col": 2, "offset": 0}, "end": {"line": 20, "col": 21, "offset": 19}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl", "start": {"line": 25, "col": 11, "offset": 0}, "end": {"line": 25, "col": 12, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl:20:\n `% if useStructure %` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl", "start": {"line": 20, "col": 2, "offset": 0}, "end": {"line": 20, "col": 21, "offset": 19}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl", "start": {"line": 25, "col": 11, "offset": 0}, "end": {"line": 25, "col": 12, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 7, "col": 6, "offset": 5}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 7, "col": 32, "offset": 0}, "end": {"line": 7, "col": 42, "offset": 10}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 9, "col": 5, "offset": 0}, "end": {"line": 9, "col": 11, "offset": 6}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 9, "offset": 0}, "end": {"line": 10, "col": 27, "offset": 18}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 39, "offset": 0}, "end": {"line": 10, "col": 40, "offset": 1}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 41, "offset": 0}, "end": {"line": 10, "col": 71, "offset": 30}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 82, "offset": 0}, "end": {"line": 11, "col": 10, "offset": 13}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 11, "col": 18, "offset": 0}, "end": {"line": 11, "col": 21, "offset": 3}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 11, "col": 30, "offset": 0}, "end": {"line": 11, "col": 32, "offset": 2}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 15, "col": 11, "offset": 6}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 18, "col": 2, "offset": 0}, "end": {"line": 18, "col": 17, "offset": 15}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 19, "col": 2, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 23}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 21, "col": 10, "offset": 0}, "end": {"line": 21, "col": 11, "offset": 1}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 23, "col": 2, "offset": 0}, "end": {"line": 23, "col": 11, "offset": 9}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 24, "col": 2, "offset": 0}, "end": {"line": 24, "col": 11, "offset": 9}}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 26, "col": 4, "offset": 0}, "end": {"line": 26, "col": 22, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl:7:\n `class` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 7, "col": 6, "offset": 5}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 7, "col": 32, "offset": 0}, "end": {"line": 7, "col": 42, "offset": 10}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 9, "col": 5, "offset": 0}, "end": {"line": 9, "col": 11, "offset": 6}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 9, "offset": 0}, "end": {"line": 10, "col": 27, "offset": 18}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 39, "offset": 0}, "end": {"line": 10, "col": 40, "offset": 1}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 41, "offset": 0}, "end": {"line": 10, "col": 71, "offset": 30}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 10, "col": 82, "offset": 0}, "end": {"line": 11, "col": 10, "offset": 13}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 11, "col": 18, "offset": 0}, "end": {"line": 11, "col": 21, "offset": 3}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 11, "col": 30, "offset": 0}, "end": {"line": 11, "col": 32, "offset": 2}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 15, "col": 11, "offset": 6}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 18, "col": 2, "offset": 0}, "end": {"line": 18, "col": 17, "offset": 15}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 19, "col": 2, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 23}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 21, "col": 10, "offset": 0}, "end": {"line": 21, "col": 11, "offset": 1}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 23, "col": 2, "offset": 0}, "end": {"line": 23, "col": 11, "offset": 9}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 24, "col": 2, "offset": 0}, "end": {"line": 24, "col": 11, "offset": 9}}, {"file": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "start": {"line": 26, "col": 4, "offset": 0}, "end": {"line": 26, "col": 22, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 32, "offset": 359}}, {"path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 10}}, {"path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 17, "col": 5, "offset": 0}, "end": {"line": 20, "col": 30, "offset": 49}}, {"path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 22, "col": 35, "offset": 0}, "end": {"line": 22, "col": 41, "offset": 6}}, {"path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 32, "col": 32, "offset": 0}, "end": {"line": 32, "col": 56, "offset": 24}}, {"path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm:1:\n `{% set records = __SELF__.records %}\n{% set displayColumn = __SELF__.displayColumn %}\n{% set noRecordsMessage = __SELF__.noRecordsMessage %}\n{% set detailsPage = __SELF__.detailsPage %}\n{% set detailsKeyColumn = __SELF__.detailsKeyColumn %}\n{% set detailsUrlParameter = __SELF__.detailsUrlParameter %}\n\n<ul class=\"record-list\">\n    {% for record in records %}` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 32, "offset": 359}}, {"file": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 10}}, {"file": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 17, "col": 5, "offset": 0}, "end": {"line": 20, "col": 30, "offset": 49}}, {"file": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 22, "col": 35, "offset": 0}, "end": {"line": 22, "col": 41, "offset": 6}}, {"file": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 32, "col": 32, "offset": 0}, "end": {"line": 32, "col": 56, "offset": 24}}, {"file": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"path": "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 9, "col": 2, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl:1:\n `namespace` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"file": "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 9, "col": 2, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 19, "col": 2, "offset": 266}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl:1:\n `namespace` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"file": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 19, "col": 2, "offset": 266}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl", "start": {"line": 5, "col": 1, "offset": 0}, "end": {"line": 11, "col": 2, "offset": 75}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl:1:\n `namespace` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"file": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl", "start": {"line": 5, "col": 1, "offset": 0}, "end": {"line": 11, "col": 2, "offset": 75}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 18, "col": 2, "offset": 280}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl:1:\n `namespace` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"file": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 18, "col": 2, "offset": 280}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 23, "col": 2, "offset": 352}}]], "message": "Syntax error at line downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl:1:\n `namespace` was unexpected", "path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl", "spans": [{"file": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl", "start": {"line": 1, "col": 18, "offset": 0}, "end": {"line": 1, "col": 27, "offset": 9}}, {"file": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 23, "col": 2, "offset": 352}}]}], "paths": {"scanned": ["downloaded_repos/rainlab_builder-plugin/.travis.yml", "downloaded_repos/rainlab_builder-plugin/LICENCE.md", "downloaded_repos/rainlab_builder-plugin/Plugin.php", "downloaded_repos/rainlab_builder-plugin/README.md", "downloaded_repos/rainlab_builder-plugin/assets/css/builder.css", "downloaded_repos/rainlab_builder-plugin/assets/images/builder-icon.svg", "downloaded_repos/rainlab_builder-plugin/assets/images/loader-transparent.svg", "downloaded_repos/rainlab_builder-plugin/assets/images/tab-dark.png", "downloaded_repos/rainlab_builder-plugin/assets/images/tab.png", "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.codelist.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.dataregistry.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.base.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.code.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.controller.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.databasetable.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.imports.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.localization.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.menus.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.model.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.modelform.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.modellist.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.permission.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.plugin.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.version.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.inspector.editor.localization.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.localizationinput.js", "downloaded_repos/rainlab_builder-plugin/assets/js/builder.table.processor.localization.js", "downloaded_repos/rainlab_builder-plugin/assets/less/behaviors.less", "downloaded_repos/rainlab_builder-plugin/assets/less/builder.less", "downloaded_repos/rainlab_builder-plugin/assets/less/buildingarea.less", "downloaded_repos/rainlab_builder-plugin/assets/less/codelist.less", "downloaded_repos/rainlab_builder-plugin/assets/less/controlblueprint.less", "downloaded_repos/rainlab_builder-plugin/assets/less/imports.less", "downloaded_repos/rainlab_builder-plugin/assets/less/localization.less", "downloaded_repos/rainlab_builder-plugin/assets/less/menus.less", "downloaded_repos/rainlab_builder-plugin/assets/less/tabs.less", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexCodeOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexControllerOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexDataRegistry.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexDatabaseTableOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexImportsOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexLocalizationOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexMenusOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexModelFormOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexModelListOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexModelOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexPermissionsOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexPluginOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/IndexVersionsOperations.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexcodeoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexcodeoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexcontrolleroperations/partials/_create-controller-popup-form.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexcontrolleroperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexcontrolleroperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexdatabasetableoperations/partials/_migration-popup-form.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexdatabasetableoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexdatabasetableoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indeximportsoperations/partials/_import-blueprints-popup-form.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indeximportsoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indeximportsoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexlocalizationoperations/partials/_copy-strings-popup-form.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexlocalizationoperations/partials/_new-string-popup.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexlocalizationoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexlocalizationoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmenusoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmenusoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmodelformoperations/partials/_add-database-fields-popup-form.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmodelformoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmodelformoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmodellistoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmodellistoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexmodeloperations/partials/_model-popup-form.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexpermissionsoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexpermissionsoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexpluginoperations/partials/_plugin-popup-form.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexpluginoperations/partials/_plugin-update-hint.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexversionsoperations/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexversionsoperations/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/behaviors/indexversionsoperations/partials/_version-hint-block.php", "downloaded_repos/rainlab_builder-plugin/classes/BehaviorDesignTimeProviderBase.php", "downloaded_repos/rainlab_builder-plugin/classes/BlueprintDesignTimeProviderBase.php", "downloaded_repos/rainlab_builder-plugin/classes/BlueprintGenerator.php", "downloaded_repos/rainlab_builder-plugin/classes/ComponentHelper.php", "downloaded_repos/rainlab_builder-plugin/classes/ControlDesignTimeProviderBase.php", "downloaded_repos/rainlab_builder-plugin/classes/ControlLibrary.php", "downloaded_repos/rainlab_builder-plugin/classes/ControllerBehaviorLibrary.php", "downloaded_repos/rainlab_builder-plugin/classes/ControllerFileParser.php", "downloaded_repos/rainlab_builder-plugin/classes/ControllerGenerator.php", "downloaded_repos/rainlab_builder-plugin/classes/DatabaseTableSchemaCreator.php", "downloaded_repos/rainlab_builder-plugin/classes/EnumDbType.php", "downloaded_repos/rainlab_builder-plugin/classes/FilesystemGenerator.php", "downloaded_repos/rainlab_builder-plugin/classes/IconList.php", "downloaded_repos/rainlab_builder-plugin/classes/IndexOperationsBehaviorBase.php", "downloaded_repos/rainlab_builder-plugin/classes/LanguageMixer.php", "downloaded_repos/rainlab_builder-plugin/classes/MigrationColumnType.php", "downloaded_repos/rainlab_builder-plugin/classes/MigrationFileParser.php", "downloaded_repos/rainlab_builder-plugin/classes/ModelFileParser.php", "downloaded_repos/rainlab_builder-plugin/classes/PhpSourceStream.php", "downloaded_repos/rainlab_builder-plugin/classes/PluginCode.php", "downloaded_repos/rainlab_builder-plugin/classes/PluginVector.php", "downloaded_repos/rainlab_builder-plugin/classes/PluginVersion.php", "downloaded_repos/rainlab_builder-plugin/classes/StandardBehaviorsRegistry.php", "downloaded_repos/rainlab_builder-plugin/classes/StandardBlueprintsRegistry.php", "downloaded_repos/rainlab_builder-plugin/classes/StandardControlsRegistry.php", "downloaded_repos/rainlab_builder-plugin/classes/TableMigrationCodeGenerator.php", "downloaded_repos/rainlab_builder-plugin/classes/TailorBlueprintLibrary.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/ContainerUtils.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/ExpandoModelContainer.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/FilterElementContainer.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/FormElementContainer.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/HasControllers.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/HasExpandoModels.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/HasMigrations.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/HasModels.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/HasNavigation.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/HasPermissions.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/HasVersionFile.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/ListElementContainer.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/ModelContainer.php", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration-join.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration-repeater.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller-config-vars.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller-no-list.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller-permissions.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/doctrine/TimestampType.php", "downloaded_repos/rainlab_builder-plugin/classes/standardbehaviorsregistry/formcontroller/templates/create.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/standardbehaviorsregistry/formcontroller/templates/preview.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/standardbehaviorsregistry/formcontroller/templates/update.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/standardbehaviorsregistry/importexportcontroller/templates/export.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/standardbehaviorsregistry/importexportcontroller/templates/import.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/standardbehaviorsregistry/listcontroller/templates/_list_toolbar.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/standardbehaviorsregistry/listcontroller/templates/index.php.tpl", "downloaded_repos/rainlab_builder-plugin/classes/standardcontrolsregistry/HasFormFields.php", "downloaded_repos/rainlab_builder-plugin/classes/standardcontrolsregistry/HasFormUi.php", "downloaded_repos/rainlab_builder-plugin/classes/standardcontrolsregistry/HasFormWidgets.php", "downloaded_repos/rainlab_builder-plugin/components/RecordDetails.php", "downloaded_repos/rainlab_builder-plugin/components/RecordList.php", "downloaded_repos/rainlab_builder-plugin/components/recorddetails/default.htm", "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "downloaded_repos/rainlab_builder-plugin/composer.json", "downloaded_repos/rainlab_builder-plugin/controllers/Index.php", "downloaded_repos/rainlab_builder-plugin/controllers/index/_plugin-selector.php", "downloaded_repos/rainlab_builder-plugin/controllers/index/_sidepanel.php", "downloaded_repos/rainlab_builder-plugin/controllers/index/index.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/BlueprintBuilder.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/ControllerBuilder.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/FormBuilder.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/MenuEditor.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/blueprintbuilder/assets/js/blueprintbuilder.js", "downloaded_repos/rainlab_builder-plugin/formwidgets/blueprintbuilder/partials/_blueprint.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/blueprintbuilder/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/blueprintbuilder/partials/_buildingarea.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/blueprintbuilder/partials/_select_blueprint_form.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/controllerbuilder/assets/js/controllerbuilder.js", "downloaded_repos/rainlab_builder-plugin/formwidgets/controllerbuilder/partials/_behavior.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/controllerbuilder/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/controllerbuilder/partials/_buildingarea.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.controlpalette.js", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.domtopropertyjson.js", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.js", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.tabs.js", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_buildingarea.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_controlbody.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_controlcontainer.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_controllist.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_controlpalette.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_controlwrapper.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_tab.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_tabpanel.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/partials/_tabs.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/menueditor/assets/js/menubuilder.js", "downloaded_repos/rainlab_builder-plugin/formwidgets/menueditor/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/menueditor/partials/_mainmenuitem.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/menueditor/partials/_mainmenuitems.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/menueditor/partials/_submenuitem.php", "downloaded_repos/rainlab_builder-plugin/formwidgets/menueditor/partials/_submenuitems.php", "downloaded_repos/rainlab_builder-plugin/lang/cs/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/cs.json", "downloaded_repos/rainlab_builder-plugin/lang/en/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/en.json", "downloaded_repos/rainlab_builder-plugin/lang/es/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/es.json", "downloaded_repos/rainlab_builder-plugin/lang/fa/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/fa.json", "downloaded_repos/rainlab_builder-plugin/lang/nl/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/nl.json", "downloaded_repos/rainlab_builder-plugin/lang/pl/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/pl.json", "downloaded_repos/rainlab_builder-plugin/lang/pt-br/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/pt-br.json", "downloaded_repos/rainlab_builder-plugin/lang/zh-cn/lang.php", "downloaded_repos/rainlab_builder-plugin/lang/zh-cn.json", "downloaded_repos/rainlab_builder-plugin/models/BaseModel.php", "downloaded_repos/rainlab_builder-plugin/models/CodeFileModel.php", "downloaded_repos/rainlab_builder-plugin/models/ControllerModel.php", "downloaded_repos/rainlab_builder-plugin/models/DatabaseTableModel.php", "downloaded_repos/rainlab_builder-plugin/models/ImportsModel.php", "downloaded_repos/rainlab_builder-plugin/models/LocalizationModel.php", "downloaded_repos/rainlab_builder-plugin/models/MenusModel.php", "downloaded_repos/rainlab_builder-plugin/models/MigrationModel.php", "downloaded_repos/rainlab_builder-plugin/models/ModelFilterModel.php", "downloaded_repos/rainlab_builder-plugin/models/ModelFormModel.php", "downloaded_repos/rainlab_builder-plugin/models/ModelListModel.php", "downloaded_repos/rainlab_builder-plugin/models/ModelModel.php", "downloaded_repos/rainlab_builder-plugin/models/ModelYamlModel.php", "downloaded_repos/rainlab_builder-plugin/models/PermissionsModel.php", "downloaded_repos/rainlab_builder-plugin/models/PluginBaseModel.php", "downloaded_repos/rainlab_builder-plugin/models/PluginYamlModel.php", "downloaded_repos/rainlab_builder-plugin/models/Settings.php", "downloaded_repos/rainlab_builder-plugin/models/YamlModel.php", "downloaded_repos/rainlab_builder-plugin/models/codefilemodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/controllermodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/controllermodel/fields_new_controller.yaml", "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/migration-code.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/importsmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/importsmodel/fields_import.yaml", "downloaded_repos/rainlab_builder-plugin/models/importsmodel/fields_select.yaml", "downloaded_repos/rainlab_builder-plugin/models/localizationmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/localizationmodel/templates/lang.php", "downloaded_repos/rainlab_builder-plugin/models/menusmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/management-fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/modelformmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/modellistmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/multisite-definitions.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/no-timestamps.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/relation-definitions.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/soft-delete.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/validation-definitions.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/permissionsmodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/pluginbasemodel/fields.yaml", "downloaded_repos/rainlab_builder-plugin/models/pluginbasemodel/templates/lang.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/pluginbasemodel/templates/plugin.php.tpl", "downloaded_repos/rainlab_builder-plugin/models/pluginbasemodel/templates/version.yaml.tpl", "downloaded_repos/rainlab_builder-plugin/models/settings/fields.yaml", "downloaded_repos/rainlab_builder-plugin/phpcs.xml", "downloaded_repos/rainlab_builder-plugin/phpunit.xml", "downloaded_repos/rainlab_builder-plugin/rainlab-builder.mix.js", "downloaded_repos/rainlab_builder-plugin/rules/Reserved.php", "downloaded_repos/rainlab_builder-plugin/updates/version.yaml", "downloaded_repos/rainlab_builder-plugin/widgets/CodeList.php", "downloaded_repos/rainlab_builder-plugin/widgets/ControllerList.php", "downloaded_repos/rainlab_builder-plugin/widgets/DatabaseTableList.php", "downloaded_repos/rainlab_builder-plugin/widgets/DefaultBehaviorDesignTimeProvider.php", "downloaded_repos/rainlab_builder-plugin/widgets/DefaultBlueprintDesignTimeProvider.php", "downloaded_repos/rainlab_builder-plugin/widgets/DefaultControlDesignTimeProvider.php", "downloaded_repos/rainlab_builder-plugin/widgets/LanguageList.php", "downloaded_repos/rainlab_builder-plugin/widgets/ModelList.php", "downloaded_repos/rainlab_builder-plugin/widgets/PluginList.php", "downloaded_repos/rainlab_builder-plugin/widgets/VersionList.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_files.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_items.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_move_form.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_new_dir_form.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_rename_form.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/widgets/codelist/partials/_widget-contents.php", "downloaded_repos/rainlab_builder-plugin/widgets/controllerlist/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/widgets/controllerlist/partials/_controller-list.php", "downloaded_repos/rainlab_builder-plugin/widgets/controllerlist/partials/_items.php", "downloaded_repos/rainlab_builder-plugin/widgets/controllerlist/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/widgets/controllerlist/partials/_widget-contents.php", "downloaded_repos/rainlab_builder-plugin/widgets/databasetablelist/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/widgets/databasetablelist/partials/_items.php", "downloaded_repos/rainlab_builder-plugin/widgets/databasetablelist/partials/_table-list.php", "downloaded_repos/rainlab_builder-plugin/widgets/databasetablelist/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/widgets/databasetablelist/partials/_widget-contents.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultbehaviordesigntimeprovider/partials/_behavior-form-controller.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultbehaviordesigntimeprovider/partials/_behavior-import-export-controller.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultbehaviordesigntimeprovider/partials/_behavior-list-controller.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultbehaviordesigntimeprovider/partials/_behavior-unknown.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultblueprintdesigntimeprovider/partials/_blueprint-entry.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultblueprintdesigntimeprovider/partials/_blueprint-global.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultblueprintdesigntimeprovider/partials/_blueprint-unknown.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-balloon-selector.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-checkbox.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-checkboxlist.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-codeeditor.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-colorpicker.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-datatable.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-datepicker.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-dropdown.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-email.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-fileupload.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-hint.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-markdown.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-mediafinder.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-nestedform-static.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-nestedform.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-number.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-pagefinder.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-partial.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-password.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-radio.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-recordfinder.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-relation.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-repeater-static.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-repeater.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-richeditor.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-ruler.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-section.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-sensitive.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-switch.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-taglist.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-text.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-textarea.php", "downloaded_repos/rainlab_builder-plugin/widgets/defaultcontroldesigntimeprovider/partials/_control-unknowncontrol.php", "downloaded_repos/rainlab_builder-plugin/widgets/languagelist/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/widgets/languagelist/partials/_items.php", "downloaded_repos/rainlab_builder-plugin/widgets/languagelist/partials/_language-list.php", "downloaded_repos/rainlab_builder-plugin/widgets/languagelist/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/widgets/languagelist/partials/_widget-contents.php", "downloaded_repos/rainlab_builder-plugin/widgets/modellist/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/widgets/modellist/partials/_items.php", "downloaded_repos/rainlab_builder-plugin/widgets/modellist/partials/_model-list.php", "downloaded_repos/rainlab_builder-plugin/widgets/modellist/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/widgets/modellist/partials/_widget-contents.php", "downloaded_repos/rainlab_builder-plugin/widgets/pluginlist/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/widgets/pluginlist/partials/_items.php", "downloaded_repos/rainlab_builder-plugin/widgets/pluginlist/partials/_plugin-list.php", "downloaded_repos/rainlab_builder-plugin/widgets/pluginlist/partials/_toolbar-buttons.php", "downloaded_repos/rainlab_builder-plugin/widgets/pluginlist/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/widgets/versionlist/partials/_body.php", "downloaded_repos/rainlab_builder-plugin/widgets/versionlist/partials/_items.php", "downloaded_repos/rainlab_builder-plugin/widgets/versionlist/partials/_sort.php", "downloaded_repos/rainlab_builder-plugin/widgets/versionlist/partials/_toolbar.php", "downloaded_repos/rainlab_builder-plugin/widgets/versionlist/partials/_version-list.php", "downloaded_repos/rainlab_builder-plugin/widgets/versionlist/partials/_widget-contents.php"], "skipped": [{"path": "downloaded_repos/rainlab_builder-plugin/classes/blueprintgenerator/templates/migration.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/classes/controllergenerator/templates/controller.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/components/recordlist/default.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/models/databasetablemodel/templates/full-migration-code.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/migration.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/models/migrationmodel/templates/seeder.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/model.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/models/modelmodel/templates/settingmodel.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/models/pluginbasemodel/templates/plugin.php.tpl", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rainlab_builder-plugin/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rainlab_builder-plugin/tests/fixtures/MyMock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rainlab_builder-plugin/tests/fixtures/filesystemgenerator/templates/plugin.php.tpl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rainlab_builder-plugin/tests/fixtures/filesystemgenerator/temporary/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rainlab_builder-plugin/tests/unit/classes/FilesystemGeneratorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rainlab_builder-plugin/tests/unit/classes/ModelModelTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rainlab_builder-plugin/tests/unit/phpunit.xml", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6606431007385254, "profiling_times": {"config_time": 6.215374708175659, "core_time": 8.082663774490356, "ignores_time": 0.0019032955169677734, "total_time": 14.300788640975952}, "parsing_time": {"total_time": 3.9945333003997803, "per_file_time": {"mean": 0.012444028973208036, "std_dev": 0.0023223844122096837}, "very_slow_stats": {"time_ratio": 0.198016603153569, "count_ratio": 0.003115264797507788}, "very_slow_files": [{"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "ftime": 0.7909839153289795}]}, "scanning_time": {"total_time": 16.516067266464233, "per_file_time": {"mean": 0.01643389777757633, "std_dev": 0.026013813295328408}, "very_slow_stats": {"time_ratio": 0.29135968438797877, "count_ratio": 0.0009950248756218905}, "very_slow_files": [{"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "ftime": 4.8121161460876465}]}, "matching_time": {"total_time": 5.311507701873779, "per_file_and_rule_time": {"mean": 0.004922620669021113, "std_dev": 0.0005292947182634029}, "very_slow_stats": {"time_ratio": 0.3142090623796182, "count_ratio": 0.0074142724745134385}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.10028409957885742}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/builder.localizationinput.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.11075496673583984}, {"fpath": "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.12091803550720215}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/builder.index.entity.localization.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.15489482879638672}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.17366290092468262}, {"fpath": "downloaded_repos/rainlab_builder-plugin/formwidgets/formbuilder/assets/js/formbuilder.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.2598989009857178}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.2859320640563965}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.46257805824279785}]}, "tainting_time": {"total_time": 3.4168219566345215, "per_def_and_rule_time": {"mean": 0.021489446268141643, "std_dev": 0.006477932980380345}, "very_slow_stats": {"time_ratio": 0.5947885695465625, "count_ratio": 0.08176100628930817}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.05889296531677246}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.05987095832824707}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.062291860580444336}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.0625309944152832}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.06570196151733398}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.06609606742858887}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.08098316192626953}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.20544195175170898}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.24254608154296875}, {"fpath": "downloaded_repos/rainlab_builder-plugin/assets/js/build-min.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.9673008918762207}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1091254080}, "engine_requested": "OSS", "skipped_rules": []}