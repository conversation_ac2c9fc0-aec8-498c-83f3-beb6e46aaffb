{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/BeaconCMS_beacon_live_admin/assets/tailwind.config.js", "start": {"line": 47, "col": 69, "offset": 1853}, "end": {"line": 47, "col": 73, "offset": 1857}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/BeaconCMS_beacon_live_admin/.cursor/rules/elixir.mdc", "downloaded_repos/BeaconCMS_beacon_live_admin/.cursor/rules/global.mdc", "downloaded_repos/BeaconCMS_beacon_live_admin/.cursor/rules/svelte.mdc", "downloaded_repos/BeaconCMS_beacon_live_admin/.formatter.exs", "downloaded_repos/BeaconCMS_beacon_live_admin/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/BeaconCMS_beacon_live_admin/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/BeaconCMS_beacon_live_admin/.github/cherry-pick-bot.yml", "downloaded_repos/BeaconCMS_beacon_live_admin/.github/dependabot.yml", "downloaded_repos/BeaconCMS_beacon_live_admin/.github/workflows/ci.yml", "downloaded_repos/BeaconCMS_beacon_live_admin/.gitignore", "downloaded_repos/BeaconCMS_beacon_live_admin/CHANGELOG.md", "downloaded_repos/BeaconCMS_beacon_live_admin/LICENSE.md", "downloaded_repos/BeaconCMS_beacon_live_admin/README.md", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/.eslintrc.js", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/.prettierignore", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/.prettierrc.js", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/build.js", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/css/beacon_live_admin.css", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/css/station-ui-fonts.css", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/css/station-ui.css", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/images/beacon_logo.png", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/images/dockyard_logo.png", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/js/beacon_live_admin.js", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/js/server.js", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/js/station-ui.js", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/package-lock.json", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/package.json", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/Backdrop.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/BrowserFrame.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/CodeEditor.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/ComponentsSidebar.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/GoToParentButton.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/LayoutAstNode.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/PageAstNode.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/PagePreview.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/PageWrapper.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/Pill.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/ResetSelectionButton.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/SelectedElementFloatingMenu/DragMenuOption.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/SelectedElementFloatingMenu.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/SidebarSection.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/components/UiBuilder.svelte", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/stores/currentComponentCategory.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/stores/dragAndDrop.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/stores/live.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/stores/page.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/stores/tailwindConfig.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/stores/tailwindInput.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/types.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/utils/animations.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/utils/ast-helpers.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/utils/ast-manipulation.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/svelte/utils/drag-helpers.ts", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/tailwind.config.js", "downloaded_repos/BeaconCMS_beacon_live_admin/assets/tsconfig.json", "downloaded_repos/BeaconCMS_beacon_live_admin/config/config.exs", "downloaded_repos/BeaconCMS_beacon_live_admin/dev.exs", "downloaded_repos/BeaconCMS_beacon_live_admin/guides/introduction/installation.md", "downloaded_repos/BeaconCMS_beacon_live_admin/guides/recipes/additional-pages.md", "downloaded_repos/BeaconCMS_beacon_live_admin/guides/recipes/basic-auth.md", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/application.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/client/beacon_assigns.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/client/config.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/client/content.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/client/heex.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/client/media_library.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/cluster.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/admin_components.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/core_components.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/layouts/admin.html.heex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/layouts/app.html.heex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/layouts.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/accordion.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/avatar.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/banner.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/button.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/card.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/footer.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/form.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/icon.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/input.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/modal.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/navbar.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/notification_badge.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/pagination.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/spinner.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/status_badge.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/tab_group.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/table_cell.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/table_header.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/tag.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/toast.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/toolbar.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html/tooltip.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/station_ui/html.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/border_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/class_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/components/color_picker.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/components/control_section.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/components/heex_editor.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/components/input_with_units.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/components/toggle_group.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/css/typography.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/hooks/color_picker.js", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/hooks.js", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/id_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/layout_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/name_value_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/opacity_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/properties_sidebar_component.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/size_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/space_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components/visual_editor/typography_control.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/components.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/config.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/controllers/assets_controller.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/controllers/error_html.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/controllers/error_json.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/errors.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/gettext.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/instance_supervisor.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/component_editor_live/edit.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/component_editor_live/form_component.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/component_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/component_editor_live/new.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/component_editor_live/slot_attr.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/component_editor_live/slots.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/error_page_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/event_handler_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/home_live.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/info_handler_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/js_hook_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/layout_editor_live/edit.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/layout_editor_live/form_component.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/layout_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/layout_editor_live/meta_tags.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/layout_editor_live/new.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/layout_editor_live/resource_links.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/layout_editor_live/revisions.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/live_data_editor_live/assigns.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/live_data_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/media_library_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/media_library_live/show_component.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/media_library_live/upload_form_component.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/meta_tags_component.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/edit.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/form_component.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/index.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/meta_tags.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/new.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/revisions.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/schema.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_editor_live/variants.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_live.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/live/page_live.html.heex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/page_builder/table.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/page_builder.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/plug.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/pub_sub.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/registry.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/router.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/runtime_css.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/types.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/css/border.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/css/layout.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/css/size.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/css/space.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/heex/heex_decoder.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/heex/json_encoder.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/heex/lv_tokenizer.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor/heex/tokenizer.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/visual_editor.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin/web.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/beacon/live_admin.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/lib/mix/tasks/beacon_live_admin.install.ex", "downloaded_repos/BeaconCMS_beacon_live_admin/mix.exs", "downloaded_repos/BeaconCMS_beacon_live_admin/mix.lock", "downloaded_repos/BeaconCMS_beacon_live_admin/priv/gettext/en/LC_MESSAGES/errors.po", "downloaded_repos/BeaconCMS_beacon_live_admin/priv/gettext/errors.pot", "downloaded_repos/BeaconCMS_beacon_live_admin/priv/static/beacon_live_admin.css", "downloaded_repos/BeaconCMS_beacon_live_admin/priv/static/beacon_live_admin.min.css"], "skipped": [{"path": "downloaded_repos/BeaconCMS_beacon_live_admin/assets/vendor/topbar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/priv/static/beacon_live_admin.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/priv/static/beacon_live_admin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/cluster_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/component_editor_live/edit_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/component_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/component_editor_live/new_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/error_page_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/event_handler_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/home_live_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/info_handler_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/js_hook_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/layout_editor_live/edit_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/layout_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/layout_editor_live/new_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/live_data_editor_live/assigns_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/live_data_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/media_library_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/media_library_live/upload_form_component_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/page_editor_live/edit_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/page_editor_live/index_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/live/page_editor_live/new_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/page_builder/table_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/router_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/visual_editor/css/border_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/visual_editor/heex/heex_decoder_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/visual_editor/heex/json_encoder_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/visual_editor/heex/tokenizer_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/beacon/live_admin/visual_editor_test.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/cluster.ex", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/conn_case.ex", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/fixtures/image.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/fixtures/image.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/fixtures/image.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/fixtures/image.webp", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/fixtures.ex", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/migrations/001_add_beacon_tables.exs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/support/site.ex", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/BeaconCMS_beacon_live_admin/test/test_helper.exs", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6564521789550781, "profiling_times": {"config_time": 5.865288496017456, "core_time": 2.9745442867279053, "ignores_time": 0.0016980171203613281, "total_time": 8.84233283996582}, "parsing_time": {"total_time": 0.5656147003173828, "per_file_time": {"mean": 0.02175441155066857, "std_dev": 0.000540671447228327}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.0284841060638428, "per_file_time": {"mean": 0.007805371407381034, "std_dev": 0.0007177122897377684}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.567272424697876, "per_file_and_rule_time": {"mean": 0.003356641566259622, "std_dev": 6.430178285266321e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.07643556594848633, "per_def_and_rule_time": {"mean": 0.0014699147297785834, "std_dev": 3.0690672367728766e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}