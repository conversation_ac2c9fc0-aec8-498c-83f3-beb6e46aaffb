{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.xss.html-safe.html-safe", "path": "downloaded_repos/codingjoe_django-s3file/s3file/forms.py", "start": {"line": 18, "col": 1, "offset": 420}, "end": {"line": 42, "col": 59, "offset": 1098}, "extra": {"message": "`html_safe()` add the `__html__` magic method to the provided class. The `__html__` method indicates to the Django template engine that the value is 'safe' for rendering. This means that normal HTML escaping will not be applied to the return value. This exposes your application to cross-site scripting (XSS) vulnerabilities. If you need to render raw HTML, consider instead using `mark_safe()` which more clearly marks the intent to render raw HTML than a class with a magic method.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/_modules/django/utils/html/#html_safe", "https://gist.github.com/minusworld/7885d8a81dba3ea2d1e4b8fd3c218ef5"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.html-safe.html-safe", "shortlink": "https://sg.run/gLO0"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.html-safe.html-safe", "path": "downloaded_repos/codingjoe_django-s3file/s3file/forms.py", "start": {"line": 18, "col": 2, "offset": 421}, "end": {"line": 18, "col": 11, "offset": 430}, "extra": {"message": "`html_safe()` add the `__html__` magic method to the provided class. The `__html__` method indicates to the Django template engine that the value is 'safe' for rendering. This means that normal HTML escaping will not be applied to the return value. This exposes your application to cross-site scripting (XSS) vulnerabilities. If you need to render raw HTML, consider instead using `mark_safe()` which more clearly marks the intent to render raw HTML than a class with a magic method.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/_modules/django/utils/html/#html_safe", "https://gist.github.com/minusworld/7885d8a81dba3ea2d1e4b8fd3c218ef5"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.html-safe.html-safe", "shortlink": "https://sg.run/gLO0"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/codingjoe_django-s3file/s3file/views.py", "start": {"line": 46, "col": 16, "offset": 1471}, "end": {"line": 55, "col": 10, "offset": 1864}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.raw-html-format.raw-html-format", "path": "downloaded_repos/codingjoe_django-s3file/s3file/views.py", "start": {"line": 49, "col": 13, "offset": 1584}, "end": {"line": 49, "col": 62, "offset": 1633}, "extra": {"message": "Detected user input flowing into a manually constructed HTML string. You may be accidentally bypassing secure methods of rendering HTML by manually constructing HTML and this could create a cross-site scripting vulnerability, which could let attackers steal sensitive user data. To be sure this is safe, check that the HTML is rendered safely. Otherwise, use templates (`django.shortcuts.render`) which will safely render HTML instead.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["django"], "references": ["https://docs.djangoproject.com/en/3.2/topics/http/shortcuts/#render", "https://docs.djangoproject.com/en/3.2/topics/security/#cross-site-scripting-xss-protection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.injection.raw-html-format.raw-html-format", "shortlink": "https://sg.run/oYj1"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.raw-html-format.raw-html-format", "path": "downloaded_repos/codingjoe_django-s3file/s3file/views.py", "start": {"line": 51, "col": 13, "offset": 1725}, "end": {"line": 51, "col": 32, "offset": 1744}, "extra": {"message": "Detected user input flowing into a manually constructed HTML string. You may be accidentally bypassing secure methods of rendering HTML by manually constructing HTML and this could create a cross-site scripting vulnerability, which could let attackers steal sensitive user data. To be sure this is safe, check that the HTML is rendered safely. Otherwise, use templates (`django.shortcuts.render`) which will safely render HTML instead.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["django"], "references": ["https://docs.djangoproject.com/en/3.2/topics/http/shortcuts/#render", "https://docs.djangoproject.com/en/3.2/topics/security/#cross-site-scripting-xss-protection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.injection.raw-html-format.raw-html-format", "shortlink": "https://sg.run/oYj1"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.raw-html-format.raw-html-format", "path": "downloaded_repos/codingjoe_django-s3file/s3file/views.py", "start": {"line": 52, "col": 13, "offset": 1757}, "end": {"line": 52, "col": 37, "offset": 1781}, "extra": {"message": "Detected user input flowing into a manually constructed HTML string. You may be accidentally bypassing secure methods of rendering HTML by manually constructing HTML and this could create a cross-site scripting vulnerability, which could let attackers steal sensitive user data. To be sure this is safe, check that the HTML is rendered safely. Otherwise, use templates (`django.shortcuts.render`) which will safely render HTML instead.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["django"], "references": ["https://docs.djangoproject.com/en/3.2/topics/http/shortcuts/#render", "https://docs.djangoproject.com/en/3.2/topics/security/#cross-site-scripting-xss-protection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.injection.raw-html-format.raw-html-format", "shortlink": "https://sg.run/oYj1"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/codingjoe_django-s3file/.github/workflows/ci.yml", "start": {"line": 105, "col": 42, "offset": 2682}, "end": {"line": 105, "col": 45, "offset": 2685}}]], "message": "Syntax error at line downloaded_repos/codingjoe_django-s3file/.github/workflows/ci.yml:105:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/codingjoe_django-s3file/.github/workflows/ci.yml", "spans": [{"file": "downloaded_repos/codingjoe_django-s3file/.github/workflows/ci.yml", "start": {"line": 105, "col": 42, "offset": 2682}, "end": {"line": 105, "col": 45, "offset": 2685}}]}], "paths": {"scanned": ["downloaded_repos/codingjoe_django-s3file/.editorconfig", "downloaded_repos/codingjoe_django-s3file/.github/FUNDING.yml", "downloaded_repos/codingjoe_django-s3file/.github/dependabot.yml", "downloaded_repos/codingjoe_django-s3file/.github/workflows/ci.yml", "downloaded_repos/codingjoe_django-s3file/.github/workflows/release.yml", "downloaded_repos/codingjoe_django-s3file/.gitignore", "downloaded_repos/codingjoe_django-s3file/.nvmrc", "downloaded_repos/codingjoe_django-s3file/LICENSE", "downloaded_repos/codingjoe_django-s3file/MANIFEST.in", "downloaded_repos/codingjoe_django-s3file/README.md", "downloaded_repos/codingjoe_django-s3file/SECURITY.md", "downloaded_repos/codingjoe_django-s3file/linter-requirements.txt", "downloaded_repos/codingjoe_django-s3file/package-lock.json", "downloaded_repos/codingjoe_django-s3file/package.json", "downloaded_repos/codingjoe_django-s3file/pyproject.toml", "downloaded_repos/codingjoe_django-s3file/s3file/__init__.py", "downloaded_repos/codingjoe_django-s3file/s3file/apps.py", "downloaded_repos/codingjoe_django-s3file/s3file/checks.py", "downloaded_repos/codingjoe_django-s3file/s3file/forms.py", "downloaded_repos/codingjoe_django-s3file/s3file/middleware.py", "downloaded_repos/codingjoe_django-s3file/s3file/static/s3file/js/s3file.js", "downloaded_repos/codingjoe_django-s3file/s3file/storages.py", "downloaded_repos/codingjoe_django-s3file/s3file/storages_optimized.py", "downloaded_repos/codingjoe_django-s3file/s3file/views.py", "downloaded_repos/codingjoe_django-s3file/setup.cfg"], "skipped": [{"path": "downloaded_repos/codingjoe_django-s3file/.github/workflows/ci.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/__tests__/s3file.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/test_apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/test_checks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/test_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/test_storages.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/templates/form.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/codingjoe_django-s3file/tests/testapp/views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7789480686187744, "profiling_times": {"config_time": 6.036102533340454, "core_time": 2.796769380569458, "ignores_time": 0.002294778823852539, "total_time": 8.83626651763916}, "parsing_time": {"total_time": 0.38468289375305176, "per_file_time": {"mean": 0.025645526250203457, "std_dev": 0.000899307338900649}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.5285427570343018, "per_file_time": {"mean": 0.023516042415912333, "std_dev": 0.0020612044948042966}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.3427274227142334, "per_file_and_rule_time": {"mean": 0.0017575765267396585, "std_dev": 1.718149457631097e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.05824422836303711, "per_def_and_rule_time": {"mean": 0.0003806812311309616, "std_dev": 4.398628231031629e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}