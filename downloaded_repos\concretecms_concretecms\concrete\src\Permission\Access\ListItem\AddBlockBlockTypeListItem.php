<?php
namespace Concrete\Core\Permission\Access\ListItem;

class AddBlockBlockTypeListItem extends BlockTypeListItem
{
    protected $customBlockTypeArray = array();
    protected $blockTypesAllowedPermission = 'N';

    public function setBlockTypesAllowedPermission($permission)
    {
        $this->blockTypesAllowedPermission = $permission;
    }
    public function getBlockTypesAllowedPermission()
    {
        return $this->blockTypesAllowedPermission;
    }
    public function setBlockTypesAllowedArray($btIDs)
    {
        $this->customBlockTypeArray = $btIDs;
    }
    public function getBlockTypesAllowedArray()
    {
        return $this->customBlockTypeArray;
    }
}
