{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/alperenersoy_filament-export/.github/workflows/tests.yml", "downloaded_repos/alperenersoy_filament-export/.gitignore", "downloaded_repos/alperenersoy_filament-export/README.md", "downloaded_repos/alperenersoy_filament-export/composer.json", "downloaded_repos/alperenersoy_filament-export/composer.lock", "downloaded_repos/alperenersoy_filament-export/config/filament-export.php", "downloaded_repos/alperenersoy_filament-export/phpunit.xml", "downloaded_repos/alperenersoy_filament-export/resources/css/filament-export.css", "downloaded_repos/alperenersoy_filament-export/resources/js/filament-export.js", "downloaded_repos/alperenersoy_filament-export/resources/lang/ar/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/ar/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/de/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/de/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/en/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/en/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/es/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/es/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/fr/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/fr/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/id/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/id/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/it/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/it/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/ku/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/ku/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/nl/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/nl/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/pt_BR/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/pt_BR/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/tr/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/tr/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/views/components/table_view.blade.php", "downloaded_repos/alperenersoy_filament-export/resources/views/pdf.blade.php", "downloaded_repos/alperenersoy_filament-export/resources/views/print.blade.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableAdditionalColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFileName.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFileNamePrefix.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFilterColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFormats.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisablePreview.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableTableColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDownloadDirect.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanFormatStates.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanHaveExtraColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanHaveExtraViewData.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanModifyWriters.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanRefreshTable.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanShowHiddenColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanUseSnappy.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasAdditionalColumnsField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasCsvDelimiter.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasDefaultFormat.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasDefaultPageOrientation.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasExportModelActions.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFileName.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFileNameField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFilterColumnsField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFormatField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasPageOrientationField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasPaginator.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasRecords.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasTimeFormat.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasUniqueActionId.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/FilamentExportBulkAction.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/FilamentExportHeaderAction.php", "downloaded_repos/alperenersoy_filament-export/src/Components/Concerns/HasAction.php", "downloaded_repos/alperenersoy_filament-export/src/Components/Concerns/HasRecords.php", "downloaded_repos/alperenersoy_filament-export/src/Components/Concerns/HasUniqueActionId.php", "downloaded_repos/alperenersoy_filament-export/src/Components/TableView.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanDisableTableColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanFilterColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanFormatStates.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanHaveAdditionalColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanHaveExtraColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanHaveExtraViewData.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanModifyWriters.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanShowHiddenColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanUseSnappy.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasCsvDelimiter.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasData.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasFileName.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasFormat.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasPageOrientation.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasPaginator.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasTable.php", "downloaded_repos/alperenersoy_filament-export/src/FilamentExport.php", "downloaded_repos/alperenersoy_filament-export/src/FilamentExportServiceProvider.php"], "skipped": [{"path": "downloaded_repos/alperenersoy_filament-export/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/factories/PostFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/factories/UserFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/migrations/create_posts_table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/migrations/create_users_table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/routes/web.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/ExportTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/CreatePost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/EditPost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/ListPosts.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/ViewPost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/CreateUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/EditUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/ListUsers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/ViewUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/RelationManagers/PostsRelationManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Models/Post.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Models/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.5950629711151123, "profiling_times": {"config_time": 5.8220062255859375, "core_time": 2.3921220302581787, "ignores_time": 0.00183868408203125, "total_time": 8.21668004989624}, "parsing_time": {"total_time": 0.41020774841308594, "per_file_time": {"mean": 0.005002533517232755, "std_dev": 9.747238303888994e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.096890926361084, "per_file_time": {"mean": 0.004284730181097978, "std_dev": 0.00014638334074082104}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.10695075988769531, "per_file_and_rule_time": {"mean": 0.0008695183730706934, "std_dev": 3.790721646428188e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.002203226089477539, "per_def_and_rule_time": {"mean": 0.00018360217412312826, "std_dev": 1.788871352283018e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1085456896}, "engine_requested": "OSS", "skipped_rules": []}