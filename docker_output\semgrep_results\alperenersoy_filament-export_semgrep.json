{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/alperenersoy_filament-export/.github/workflows/tests.yml", "downloaded_repos/alperenersoy_filament-export/.gitignore", "downloaded_repos/alperenersoy_filament-export/README.md", "downloaded_repos/alperenersoy_filament-export/composer.json", "downloaded_repos/alperenersoy_filament-export/composer.lock", "downloaded_repos/alperenersoy_filament-export/config/filament-export.php", "downloaded_repos/alperenersoy_filament-export/phpunit.xml", "downloaded_repos/alperenersoy_filament-export/resources/css/filament-export.css", "downloaded_repos/alperenersoy_filament-export/resources/js/filament-export.js", "downloaded_repos/alperenersoy_filament-export/resources/lang/ar/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/ar/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/de/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/de/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/en/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/en/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/es/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/es/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/fr/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/fr/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/id/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/id/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/it/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/it/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/ku/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/ku/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/nl/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/nl/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/pt_BR/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/pt_BR/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/tr/export_action.php", "downloaded_repos/alperenersoy_filament-export/resources/lang/tr/table_view.php", "downloaded_repos/alperenersoy_filament-export/resources/views/components/table_view.blade.php", "downloaded_repos/alperenersoy_filament-export/resources/views/pdf.blade.php", "downloaded_repos/alperenersoy_filament-export/resources/views/print.blade.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableAdditionalColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFileName.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFileNamePrefix.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFilterColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableFormats.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisablePreview.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDisableTableColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanDownloadDirect.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanFormatStates.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanHaveExtraColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanHaveExtraViewData.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanModifyWriters.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanRefreshTable.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanShowHiddenColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/CanUseSnappy.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasAdditionalColumnsField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasCsvDelimiter.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasDefaultFormat.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasDefaultPageOrientation.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasExportModelActions.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFileName.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFileNameField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFilterColumnsField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasFormatField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasPageOrientationField.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasPaginator.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasRecords.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasTimeFormat.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/Concerns/HasUniqueActionId.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/FilamentExportBulkAction.php", "downloaded_repos/alperenersoy_filament-export/src/Actions/FilamentExportHeaderAction.php", "downloaded_repos/alperenersoy_filament-export/src/Components/Concerns/HasAction.php", "downloaded_repos/alperenersoy_filament-export/src/Components/Concerns/HasRecords.php", "downloaded_repos/alperenersoy_filament-export/src/Components/Concerns/HasUniqueActionId.php", "downloaded_repos/alperenersoy_filament-export/src/Components/TableView.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanDisableTableColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanFilterColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanFormatStates.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanHaveAdditionalColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanHaveExtraColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanHaveExtraViewData.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanModifyWriters.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanShowHiddenColumns.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/CanUseSnappy.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasCsvDelimiter.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasData.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasFileName.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasFormat.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasPageOrientation.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasPaginator.php", "downloaded_repos/alperenersoy_filament-export/src/Concerns/HasTable.php", "downloaded_repos/alperenersoy_filament-export/src/FilamentExport.php", "downloaded_repos/alperenersoy_filament-export/src/FilamentExportServiceProvider.php"], "skipped": [{"path": "downloaded_repos/alperenersoy_filament-export/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/factories/PostFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/factories/UserFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/migrations/create_posts_table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/database/migrations/create_users_table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/routes/web.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/ExportTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/CreatePost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/EditPost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/ListPosts.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource/Pages/ViewPost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/PostResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/CreateUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/EditUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/ListUsers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/Pages/ViewUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource/RelationManagers/PostsRelationManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Filament/Resources/UserResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Models/Post.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/Models/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/alperenersoy_filament-export/tests/src/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7553791999816895, "profiling_times": {"config_time": 5.694041967391968, "core_time": 2.5418448448181152, "ignores_time": 0.0018351078033447266, "total_time": 8.238561630249023}, "parsing_time": {"total_time": 0.3874833583831787, "per_file_time": {"mean": 0.004725406809550961, "std_dev": 8.02723746082318e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.035275936126709, "per_file_time": {"mean": 0.004044046625494958, "std_dev": 0.00012241046442387432}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.10944485664367676, "per_file_and_rule_time": {"mean": 0.0008897955824689167, "std_dev": 3.5824394910284967e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0025849342346191406, "per_def_and_rule_time": {"mean": 0.00021541118621826172, "std_dev": 4.847476494281485e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}