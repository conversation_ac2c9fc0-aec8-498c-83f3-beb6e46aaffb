{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/guide.md", "start": {"line": 97, "col": 1, "offset": 2967}, "end": {"line": 97, "col": 363, "offset": 3329}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/generate-jwt-token.md", "start": {"line": 41, "col": 1, "offset": 1086}, "end": {"line": 41, "col": 363, "offset": 1448}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docusaurus.config.js", "start": {"line": 30, "col": 7, "offset": 841}, "end": {"line": 30, "col": 49, "offset": 883}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/guide.md", "start": {"line": 98, "col": 1, "offset": 2753}, "end": {"line": 98, "col": 363, "offset": 3115}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/generate-jwt-token.md", "start": {"line": 41, "col": 1, "offset": 1037}, "end": {"line": 41, "col": 363, "offset": 1399}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/AppRegisterModal.vue", "start": {"line": 20, "col": 11, "offset": 635}, "end": {"line": 20, "col": 35, "offset": 659}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-billing/index.vue", "start": {"line": 11, "col": 13, "offset": 342}, "end": {"line": 11, "col": 23, "offset": 352}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-secret/index.vue", "start": {"line": 11, "col": 13, "offset": 331}, "end": {"line": 11, "col": 23, "offset": 341}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-secret/index.vue", "start": {"line": 25, "col": 13, "offset": 849}, "end": {"line": 25, "col": 23, "offset": 859}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/home/<USER>", "start": {"line": 18, "col": 9, "offset": 440}, "end": {"line": 21, "col": 18, "offset": 540}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/home/<USER>", "start": {"line": 69, "col": 11, "offset": 1839}, "end": {"line": 69, "col": 23, "offset": 1851}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/utils/tools.js", "start": {"line": 16, "col": 17, "offset": 386}, "end": {"line": 16, "col": 53, "offset": 422}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/external/_category_.json:1:\n missing element", "path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/external/_category_.json"}], "paths": {"scanned": ["downloaded_repos/MixinNetwork_developers.mixin.one/.github/dependabot.yml", "downloaded_repos/MixinNetwork_developers.mixin.one/.gitignore", "downloaded_repos/MixinNetwork_developers.mixin.one/LICENSE", "downloaded_repos/MixinNetwork_developers.mixin.one/README.md", "downloaded_repos/MixinNetwork_developers.mixin.one/SDK.zh.md", "downloaded_repos/MixinNetwork_developers.mixin.one/app.one.yaml", "downloaded_repos/MixinNetwork_developers.mixin.one/deploy.production.sh", "downloaded_repos/MixinNetwork_developers.mixin.one/deploy.sh", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/.env", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/.env.developement", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/.env.production", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/.eslintrc.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/README.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/babel.config.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/index.html", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/package.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/pnpm-lock.yaml", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/public/favicon.ico", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/public/js/webview.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/404.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/App.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/api/config.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/api/hooks.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/api/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Bold.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Bold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Light.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Light.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Regular.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Regular.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Semibold.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/SF-Pro/SF-Pro-Text-Semibold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/dashboard.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/home.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/maven-pro/MavenPro-Black.ttf", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/maven-pro/MavenPro-Bold.ttf", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/maven-pro/MavenPro-Medium.ttf", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/maven-pro/MavenPro-Regular.ttf", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/maven-pro/MavenPro.ttf", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-100.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-100.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-300.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-300.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-500.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-500.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-regular.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-mono-v4-latin-regular.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-100.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-100.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-300.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-300.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-500.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-500.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-regular.woff", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/font/roboto/roboto-v16-latin-regular.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/app-svg/add.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/app-svg/close.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/app-svg/help.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/app-svg/left.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/app-svg/logout.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/app-svg/profile.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/app-svg/right.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/btc.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/btc_.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/coder.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/doge.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/eos.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/eth.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/ltc.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/nem.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/sia.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/xrp.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/coin/zec.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/default_avatar.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/default_robot.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/footer/facebook.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/footer/github.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/footer/reddit.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/footer/telegram.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/footer/twitter.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/footer/youtube.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/ic_copy.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/ic_qr_code.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/ic_v.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/logo.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/logo.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/masthead.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryBOOKS.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryBUSINESS.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryEDUCATION.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryGAMES.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryMUSIC.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryNEWS.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryOTHER.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryPHOTO.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategorySHOPPING.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategorySOCIAL.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryTOOLS.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryTRADING.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryVIDEO.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/CategoryWALLET.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/Intersect.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/add.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/black_logo.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/bottom.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/close.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/doc-menus.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/home_bg_bottom.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/home_bg_top.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/ic_account.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/img.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/logo.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/logout.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/menus.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/reset.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/right.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/robot.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/search.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/search_black.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/secret.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/selected.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/session.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/svg/triangle.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/img/text-segmentation.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/scss/markdown.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/assets/scss/theme.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/AppForm/croppie.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/AppForm/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/AppForm/input.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/AppForm/script.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/AppForm/select.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/AppForm/style.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Auth.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/DHeader.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/MainFooter.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/MainHeader.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/AppRegisterModal.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/BuyAppModal.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/ConfirmModal.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/DModal.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/SecretModal.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/UpdateTokenModal.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/WithdrawalModal.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Modals/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/Pages.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/SideBar.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/i18n/en/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/i18n/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/i18n/zh/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/layout/DashboardLayout.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/layout/DocumentLayout.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/main.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-billing/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-information/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-secret/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-secret/script.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-secret/style.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-wallet/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-wallet/script.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-wallet/style.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/script.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/style.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app-creation/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/dashboard/index.vue", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/home/<USER>", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/home/<USER>", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/home/<USER>", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/router.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/layout.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/load.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/buy.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/confirm.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/register.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/secret.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/updateToken.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/withdrawal.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/utils/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/utils/localStorage.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/utils/tools.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/vite.config.mjs", "downloaded_repos/MixinNetwork_developers.mixin.one/developers/yarn.lock", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/.gitignore", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/README.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/babel.config.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2017-12-08-blockchain-Ethereum.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2017-12-30-blockchain-Bitcoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-04-16-blockchain-Ripple.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-04-20-blockchain-Bitcoin-Cash.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-04-20-blockchain-Litecoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-04-21-blockchain-Ethereum-Classic.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-04-24-blockchain-Siacoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-07-18-blockchain-EOS.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-07-24-blockchain-Dash.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-08-05-blockchain-Zcash.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-08-26-blockchain-Dogecoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-08-30-blockchain-NEM.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-09-25-blockchain-Tron.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2018-09-29-blockchain-Horizen.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-02-28-withdrawal-sponsor copy.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-02-30-hackerone.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-06-14-blockchain-Stellar.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-06-19-blockchain-MassGrid.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-07-05-blockchain-Bytom1.0.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-07-13-blockchain-BitcoinSV.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-07-13-blockchain-Cosmos.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-07-20-blockchain-Decred.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-07-24-blockchain-Binance-Chain.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-08-21-blockchain-Monero.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2019-08-27-blockchain-BitShares.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-01-06-blockchain-ravencoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-01-27-blockchain-grin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-02-02-blockchain-vcash.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-03-09-blockchain-nervos.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-03-23-blockchain-handshake.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-03-31-dgd-dissolution.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-03-31-network-multisig-box.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-04-21-network-explorer-blockchair.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-04-27-messenger-bottom-navigation.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-04-29-messenger-currency-and-localemd", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-08-29-blockchain-Namecoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-09-17-blockchain-Tezos.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-10-16-blockchain-Filecoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2020-11-06-blockchain-Polkadot.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-01-16-blockchain-MobileCoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-03-21-blockchain-Kusama.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-05-05-blockchain-Solana.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-05-15-blockchain-Arweave.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-07-15-blockchain-Algorand.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-08-26-blockchain-Bytom2.0 copy.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-09-05-blockchain-Akash.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-09-06-blockchain-Avalanche.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2021-11-02-blockchain-NEAR.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2022-10-03-blockchain-XDC-Networ.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/blog/2022-10-20-blockchain-Aptos.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_req.encrypted.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_req.inscription.collection.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_req.inscription.item.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_req.msgs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.addr.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.addrs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.album.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.apps.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.asset.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.assets-network.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.assets.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.attachment.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.circle.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.circles.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.codes-collectible.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.codes-conversation.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.codes-multisig.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.codes-payment.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.codes-user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.conv.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.encrypted.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.fee.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/audio.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/buttons.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/card.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/contact.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/encrypt.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/file.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/image.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/live.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/location.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/post.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/sticker.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/text.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msg.d/video.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.msgs-encrypted.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.deposits.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.entry.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.ghosts.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.me.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.multisig.request.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.multisig.requests.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.outputs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.request.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.requests.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.safe.snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.sessions.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.snapshot.deposit.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.snapshot.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.snapshot.raw.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.snapshot.transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.snapshot.withdrawal.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.transfer-mainnet.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.transfer-multisig.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.user-extra.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.user-net.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/_partials/_resp.users.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/assets/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/assets/asset.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/assets/assets.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/assets/fee.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/add-to-remove-from-circles.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/create.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/delete.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/list-items.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/list.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/single.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/circles/update.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/codes.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/collectibles/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/collectibles/outputs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/collectibles/request.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/conversations/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/conversations/create.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/conversations/group.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/conversations/read.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/error-codes.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/external/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/external/address.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/external/fiats.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/external/pending-deposits.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/inscription.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/attachment-download.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/attachment-upload.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/category.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/encrypt.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/encrypted.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/read.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/messages/send.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/multisigs/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/multisigs/outputs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/multisigs/request.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/network/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/network/assets.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/network/chains.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/network/snapshot.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/network/snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/network/ticker.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/network/top.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/oauth/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/oauth/oauth.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/oauth/scope.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/outputs/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/outputs/ghost.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/outputs/outputs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/pin/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/pin/logs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/pin/pin-update.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/pin/pin-verify.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/pin/tip.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/safe-apis.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/session-secret-migration.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/shared-bots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/transfer/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/transfer/payment.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/transfer/raw-transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/transfer/snapshot.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/transfer/snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/transfer/transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/contacts.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/intro.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/network-user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/profile.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/relationships.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/search.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/sessions.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/users/users.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/withdrawal/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/withdrawal/address-add.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/withdrawal/address-delete.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/withdrawal/address.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/withdrawal/addresses.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/withdrawal/withdrawal.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/zip-album.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api-overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/community/articles.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/community/contributing.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/community/support.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/bot-overview-samples.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/color-app-button-preview.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/color-app-button.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/color.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/dark-mode.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/dark-mode.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/floating-menu.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/immersive-mode.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/immersive-mode.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/overview-nav-capsule.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/overview-point.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/sticker-padding.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/sticker.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/title-bar.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/title-bar.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-add-bot.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-chat-bot.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-chat-menu.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-cmd.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-drag.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-notice.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-profile.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-reply.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction-welcome.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/design/user-interaction.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/getting-started/create-dapp.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/getting-started/messages.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/getting-started/oauth.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/getting-started/read-info.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/getting-started/transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/create-network-user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/deposit-withdrawal.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/generate-jwt-token.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/message-loop.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/multisig-guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/pin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/guide/sync-snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/sdk/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/sdk/go.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/sdk/kotlin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/sdk/nodejs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/sdk/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/sdk/php.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/dapp/sdk/ruby.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/donate-cafe.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/donate-cafe.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/mixcoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/mixcoin.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/oceanone.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/oceanone.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/online-wallet.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/online-wallet.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/prsdigg.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/prsdigg.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/super-group.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/examples/super-group.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/introduction.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/js-bridge.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/chain.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/chain.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/full-node-dag.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/full-node-transaction.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/full-node.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/how-it-works-nodes.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/how-it-works.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/how-it-works.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/multisig.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/price.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/tip.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/workflow.jpg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/concepts/xin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/guide/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/guide/full-node-join.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/guide/mtg-guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/guide/submit-asset-icon.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/guide/sync-full-node.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/amm.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/chains.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/exchange.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/lend.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/overview-architecture.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/overview-mutisig-transaction.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/stablecoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg/wappercoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mtg.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/images/initialize.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/images/network.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/images/setting.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/metamask.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/remix/using-remix-1.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/remix/using-remix-2.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/remix/using-remix-3.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/remix/using-remix-4.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/remix/using-remix-5.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/remix.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/mvm/setup.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/mainnet-rpc.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/resources/sdk.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/schema.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docusaurus.config.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/en/code.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/en/docusaurus-plugin-content-docs/current.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/en/docusaurus-theme-classic/footer.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/en/docusaurus-theme-classic/navbar.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/code.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_req.encrypted.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_req.msgs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.addr.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.addrs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.album.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.apps.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.asset.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.assets-network.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.assets.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.attachment.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.circle.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.circles.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.codes-collectible.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.codes-conversation.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.codes-multisig.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.codes-payment.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.codes-user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.conv.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.encrypted.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.fee.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/audio.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/buttons.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/card.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/contact.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/encrypt.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/file.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/image.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/live.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/location.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/post.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/sticker.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/text.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msg.d/video.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.msgs-encrypted.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.sessions.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.snapshot.deposit.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.snapshot.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.snapshot.raw.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.snapshot.transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.snapshot.withdrawal.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.transfer-mainnet.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.transfer-multisig.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.user-extra.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.user-net.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/_partials/_resp.users.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/assets/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/assets/asset.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/assets/assets.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/assets/fee.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/add-to-remove-from-circles.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/create.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/delete.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/list-items.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/list.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/single.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/circles/update.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/codes.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/collectibles/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/collectibles/outputs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/collectibles/request.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/conversations/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/conversations/create.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/conversations/group.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/conversations/read.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/error-codes.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/external/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/external/address.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/external/fiats.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/external/pending-deposits.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/inscription.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/attachment-download.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/attachment-upload.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/category.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/encrypt.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/read.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/messages/send.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/multisigs/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/multisigs/outputs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/multisigs/request.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/network/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/network/assets.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/network/chains.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/network/snapshot.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/network/snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/network/ticker.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/oauth/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/oauth/oauth.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/oauth/scope.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/outputs/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/outputs/ghost.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/outputs/outputs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/pin/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/pin/logs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/pin/pin-update.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/pin/pin-verify.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/pin/tip.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/safe-apis.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/session-secret-migration.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/shared-bots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/transfer/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/transfer/payment.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/transfer/raw-transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/transfer/snapshot.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/transfer/snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/transfer/transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/contacts.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/intro.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/network-user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/profile.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/relationships.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/search.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/sessions.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/users/users.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/withdrawal/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/withdrawal/address-add.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/withdrawal/address-delete.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/withdrawal/address.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/withdrawal/addresses.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/withdrawal/withdrawal.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api/zip-album.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/api-overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/community/articles.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/community/contributing.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/community/support.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/bot-overview-samples.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/color-app-button-preview.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/color-app-button.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/color.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/dark-mode.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/dark-mode.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/floating-menu.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/immersive-mode.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/immersive-mode.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/overview-nav-capsule.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/overview-point.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/sticker-padding.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/sticker.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/title-bar.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/title-bar.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-add-bot.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-chat-bot.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-chat-menu.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-cmd.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-drag.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-notice.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-profile.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-reply.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction-welcome.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/design/user-interaction.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/getting-started/create-dapp.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/getting-started/messages.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/getting-started/oauth.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/getting-started/read-info.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/getting-started/transfer.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/create-network-user.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/deposit-withdrawal.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/generate-jwt-token.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/message-loop.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/multisig-guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/pin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/guide/sync-snapshots.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/sdk/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/sdk/go.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/sdk/kotlin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/sdk/nodejs.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/sdk/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/sdk/php.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/dapp/sdk/ruby.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/donate-cafe.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/donate-cafe.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/mixcoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/mixcoin.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/oceanone.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/oceanone.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/online-wallet.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/online-wallet.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/prsdigg.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/prsdigg.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/super-group.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/examples/super-group.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/introduction.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/js-bridge.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/chain.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/d3m-pin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/full-node-dag.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/full-node-transaction.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/full-node.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/how-it-works-nodes.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/how-it-works.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/how-it-works.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/multisig.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/price.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/concepts/xin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/guide/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/guide/full-node-join.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/guide/mtg-guide.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/guide/submit-asset-icon.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/guide/sync-full-node.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/amm.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/chains.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/exchange.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/lend.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/overview-architecture.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/overview-mutisig-transaction.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/stablecoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg/wappercoin.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mtg.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/_category_.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/images/initialize.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/images/network.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/images/setting.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/metamask.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/remix/using-remix-1.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/remix/using-remix-2.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/remix/using-remix-3.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/remix/using-remix-4.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/remix/using-remix-5.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/remix.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/mvm/setup.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet/overview.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/mainnet-rpc.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/resources/sdk.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current/schema.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-plugin-content-docs/current.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-theme-classic/footer.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/i18n/zh-CN/docusaurus-theme-classic/navbar.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/package.json", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/sidebar.docs.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/sidebars.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/HomepageFeatures.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/HomepageFeatures.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIEndpoint/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIEndpoint/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIMetaPanel/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIMetaPanel/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIParams/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIParams/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIPayload/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIPayload/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIRequest/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIRequest/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIResponse/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIResponse/responses.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/APIResponse/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/api/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/showcase/ShowcaseCard/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/showcase/ShowcaseCard/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/showcase/ShowcaseCheckbox/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/showcase/ShowcaseCheckbox/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/showcase/ShowcaseSelect/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/components/showcase/ShowcaseSelect/styles.module.css", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/css/custom.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/4swap.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/bwatch.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/exinearn.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/exinlocal.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/exinone.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/exinpool.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/exinswap.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/leaf.pando.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/mixswap.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/showcase/prsdigg.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/data/users.tsx", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/pages/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/pages/index.module.scss", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/pages/markdown-page.md", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/pages/showcase/index.js", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/src/utils/jsUtils.ts", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/.nojekyll", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/CNAME", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-Black.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-BlackItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-Bold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-BoldItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-ExtraBold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-ExtraBoldItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-ExtraLight.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-ExtraLightItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-Italic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-Light.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-LightItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-Medium.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-MediumItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-Regular.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-SemiBold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-SemiBoldItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-Thin.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/Inter-ThinItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-Black.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-BlackItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-Bold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-BoldItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-ExtraBold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-ExtraBoldItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-ExtraLight.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-ExtraLightItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-Italic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-Light.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-LightItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-Medium.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-MediumItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-Regular.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-SemiBold.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-SemiBoldItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-Thin.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterDisplay-ThinItalic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterVariable-Italic.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/font/InterVariable.woff2", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/docusaurus.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/favicon.ico", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/home_head.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/logo.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/4swap.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/bwatch.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/exinearn.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/exinlocal.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/exinone.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/exinpool.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/exinswap.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/leaf.pando.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/mixswap.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/showcase/prsdigg.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/tutorial/docsVersionDropdown.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/tutorial/localeDropdown.png", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/undraw_docusaurus_mountain.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/undraw_docusaurus_react.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/static/images/undraw_docusaurus_tree.svg", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/sync.py", "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/yarn.lock"], "skipped": [{"path": "downloaded_repos/MixinNetwork_developers.mixin.one/developers-docs/docs/api/external/_category_.json", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.008335828781128, "profiling_times": {"config_time": 5.73277473449707, "core_time": 6.359269142150879, "ignores_time": 0.0020537376403808594, "total_time": 12.094921588897705}, "parsing_time": {"total_time": 1.266282320022583, "per_file_time": {"mean": 0.011407948829032283, "std_dev": 0.0005550239126828176}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 12.391583442687988, "per_file_time": {"mean": 0.006846178697617669, "std_dev": 0.0022577205092975073}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.7686684131622314, "per_file_and_rule_time": {"mean": 0.0079007723546378, "std_dev": 0.00036513569742273777}, "very_slow_stats": {"time_ratio": 0.1829452010986819, "count_ratio": 0.010482180293501049}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-wallet/script.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.10160493850708008}, {"fpath": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/stores/modals/withdrawal.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.10718393325805664}, {"fpath": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/pnpm-lock.yaml", "rule_id": "yaml.docker-compose.security.exposing-docker-socket-volume.exposing-docker-socket-volume", "time": 0.10824704170227051}, {"fpath": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/pages/app/app-secret/script.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.18007397651672363}, {"fpath": "downloaded_repos/MixinNetwork_developers.mixin.one/developers/src/components/AppForm/script.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.19234991073608398}]}, "tainting_time": {"total_time": 0.5760438442230225, "per_def_and_rule_time": {"mean": 0.0024829476044095793, "std_dev": 1.3063724447512518e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1098394880}, "engine_requested": "OSS", "skipped_rules": []}