{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "start": {"line": 7, "col": 3, "offset": 213}, "end": {"line": 7, "col": 127, "offset": 337}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "start": {"line": 8, "col": 3, "offset": 340}, "end": {"line": 8, "col": 92, "offset": 429}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "start": {"line": 9, "col": 3, "offset": 432}, "end": {"line": 9, "col": 109, "offset": 538}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 8, "col": 7, "offset": 201}, "end": {"line": 8, "col": 31, "offset": 225}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 10, "col": 6, "offset": 321}, "end": {"line": 10, "col": 30, "offset": 345}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 14, "col": 7, "offset": 525}, "end": {"line": 28, "col": 64, "offset": 1183}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 15, "col": 6, "offset": 577}, "end": {"line": 15, "col": 29, "offset": 600}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 28, "col": 9, "offset": 1128}, "end": {"line": 28, "col": 33, "offset": 1152}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 30, "col": 9, "offset": 1251}, "end": {"line": 30, "col": 33, "offset": 1275}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 32, "col": 9, "offset": 1367}, "end": {"line": 32, "col": 33, "offset": 1391}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 34, "col": 9, "offset": 1493}, "end": {"line": 34, "col": 33, "offset": 1517}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 37, "col": 9, "offset": 1713}, "end": {"line": 37, "col": 33, "offset": 1737}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "start": {"line": 6, "col": 6, "offset": 164}, "end": {"line": 6, "col": 30, "offset": 188}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/secret.html", "start": {"line": 9, "col": 6, "offset": 182}, "end": {"line": 9, "col": 30, "offset": 206}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/fake.py", "start": {"line": 31, "col": 9, "offset": 835}, "end": {"line": 31, "col": 90, "offset": 916}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret 'Fake call to %s: \"Your token is: %s\"' being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/fake.py", "start": {"line": 35, "col": 9, "offset": 977}, "end": {"line": 35, "col": 89, "offset": 1057}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret 'Fake SMS to %s: \"Your token is: %s\"' being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/forms.py", "start": {"line": 42, "col": 16, "offset": 1226}, "end": {"line": 42, "col": 50, "offset": 1260}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "sha256(str(user.pk).encode('utf-8'))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_base.html", "start": {"line": 6, "col": 3, "offset": 154}, "end": {"line": 6, "col": 127, "offset": 278}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_base.html", "start": {"line": 7, "col": 3, "offset": 281}, "end": {"line": 7, "col": 92, "offset": 370}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_base.html", "start": {"line": 8, "col": 3, "offset": 373}, "end": {"line": 8, "col": 109, "offset": 479}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 6, "col": 6, "offset": 156}, "end": {"line": 6, "col": 30, "offset": 180}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 18, "col": 8, "offset": 672}, "end": {"line": 18, "col": 24, "offset": 688}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/login.html", "start": {"line": 13, "col": 8, "offset": 279}, "end": {"line": 13, "col": 24, "offset": 295}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/login.html", "start": {"line": 17, "col": 8, "offset": 482}, "end": {"line": 17, "col": 32, "offset": 506}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "start": {"line": 7, "col": 6, "offset": 161}, "end": {"line": 7, "col": 30, "offset": 185}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "start": {"line": 11, "col": 6, "offset": 398}, "end": {"line": 11, "col": 30, "offset": 422}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/phone_register.html", "start": {"line": 8, "col": 10, "offset": 207}, "end": {"line": 8, "col": 34, "offset": 231}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/phone_register.html", "start": {"line": 12, "col": 10, "offset": 467}, "end": {"line": 12, "col": 34, "offset": 491}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 11, "col": 8, "offset": 281}, "end": {"line": 11, "col": 32, "offset": 305}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 15, "col": 8, "offset": 529}, "end": {"line": 15, "col": 32, "offset": 553}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 18, "col": 8, "offset": 705}, "end": {"line": 18, "col": 32, "offset": 729}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 22, "col": 8, "offset": 970}, "end": {"line": 22, "col": 32, "offset": 994}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 25, "col": 8, "offset": 1236}, "end": {"line": 25, "col": 24, "offset": 1252}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 28, "col": 8, "offset": 1370}, "end": {"line": 28, "col": 32, "offset": 1394}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 32, "col": 8, "offset": 1601}, "end": {"line": 32, "col": 32, "offset": 1625}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 37, "col": 12, "offset": 1892}, "end": {"line": 37, "col": 36, "offset": 1916}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 40, "col": 12, "offset": 2071}, "end": {"line": 40, "col": 36, "offset": 2095}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 44, "col": 51, "offset": 2272}, "end": {"line": 44, "col": 75, "offset": 2296}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 51, "col": 8, "offset": 2676}, "end": {"line": 51, "col": 32, "offset": 2700}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 7, "col": 6, "offset": 176}, "end": {"line": 7, "col": 30, "offset": 200}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 14, "col": 8, "offset": 490}, "end": {"line": 14, "col": 32, "offset": 514}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/disable.html", "start": {"line": 6, "col": 6, "offset": 176}, "end": {"line": 6, "col": 30, "offset": 200}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 9, "col": 8, "offset": 209}, "end": {"line": 9, "col": 62, "offset": 263}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 13, "col": 10, "offset": 413}, "end": {"line": 13, "col": 34, "offset": 437}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 36, "col": 7, "offset": 1326}, "end": {"line": 36, "col": 31, "offset": 1350}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 38, "col": 7, "offset": 1471}, "end": {"line": 38, "col": 59, "offset": 1523}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 48, "col": 8, "offset": 1871}, "end": {"line": 48, "col": 32, "offset": 1895}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 53, "col": 8, "offset": 2191}, "end": {"line": 53, "col": 32, "offset": 2215}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/twilio/press_a_key.xml", "start": {"line": 4, "col": 34, "offset": 152}, "end": {"line": 4, "col": 50, "offset": 168}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/twilio/sms_message.html", "start": {"line": 2, "col": 1, "offset": 16}, "end": {"line": 2, "col": 25, "offset": 40}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/core.py", "start": {"line": 682, "col": 13, "offset": 27169}, "end": {"line": 682, "col": 57, "offset": 27213}, "extra": {"message": "Data from request ($DATA) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/core.py", "start": {"line": 682, "col": 20, "offset": 27176}, "end": {"line": 682, "col": 57, "offset": 27213}, "extra": {"message": "Data from request ($DATA) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 20, "col": 3, "offset": 0}, "end": {"line": 24, "col": 35, "offset": 193}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html:1:\n `{% extends \"_base.html\" %}\n{% load i18n %}\n\n{% block title %}{% trans \"django-two-factor-auth – Demo Application\" %}{% endblock %}\n{% block nav_home %}active{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 20, "col": 3, "offset": 0}, "end": {"line": 24, "col": 35, "offset": 193}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html:1:\n `{% extends \"two_factor/_base_focus.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 28, "offset": 82}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html:1:\n `{% extends \"two_factor/_base.html\" %}\n{% load i18n %}\n\n{% block content_wrapper %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 28, "offset": 82}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/create_credential.js", "start": {"line": 1, "col": 34, "offset": 0}, "end": {"line": 1, "col": 70, "offset": 36}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/create_credential.js:1:\n `{ credential_creation_options|safe }` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/create_credential.js", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/create_credential.js", "start": {"line": 1, "col": 34, "offset": 0}, "end": {"line": 1, "col": 70, "offset": 36}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/get_credential.js", "start": {"line": 1, "col": 33, "offset": 0}, "end": {"line": 1, "col": 68, "offset": 35}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/get_credential.js:1:\n `{ credential_request_options|safe }` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/get_credential.js", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/get_credential.js", "start": {"line": 1, "col": 33, "offset": 0}, "end": {"line": 1, "col": 68, "offset": 35}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 36}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 27, "offset": 38}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 11, "offset": 10}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html:1:\n `{% load i18n %}\n\n{% if cancel_url %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 36}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 27, "offset": 38}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 11, "offset": 10}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 12, "col": 34, "offset": 31}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 19, "col": 3, "offset": 0}, "end": {"line": 19, "col": 13, "offset": 10}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 21, "col": 3, "offset": 0}, "end": {"line": 21, "col": 14, "offset": 11}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html:1:\n `{% extends \"two_factor/_base_focus.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 12, "col": 34, "offset": 31}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 19, "col": 3, "offset": 0}, "end": {"line": 19, "col": 13, "offset": 10}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 21, "col": 3, "offset": 0}, "end": {"line": 21, "col": 14, "offset": 11}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html:1:\n `{% extends \"two_factor/_base_focus.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 139}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 42}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 14, "col": 3, "offset": 0}, "end": {"line": 14, "col": 46, "offset": 43}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 17, "col": 3, "offset": 0}, "end": {"line": 17, "col": 49, "offset": 46}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 27, "col": 3, "offset": 0}, "end": {"line": 27, "col": 43, "offset": 40}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 31, "col": 3, "offset": 0}, "end": {"line": 31, "col": 44, "offset": 41}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 34, "col": 3, "offset": 0}, "end": {"line": 36, "col": 39, "offset": 119}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 39, "col": 7, "offset": 0}, "end": {"line": 39, "col": 40, "offset": 33}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 42, "col": 7, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 26}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 50, "col": 47, "offset": 58}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 54, "col": 3, "offset": 0}, "end": {"line": 54, "col": 14, "offset": 11}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 64, "col": 1, "offset": 0}, "end": {"line": 64, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html:1:\n `{% extends \"two_factor/_base_focus.html\" %}\n{% load i18n %}\n\n{% block extra_media %}\n  {{ form.media }}\n{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 139}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 42}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 14, "col": 3, "offset": 0}, "end": {"line": 14, "col": 46, "offset": 43}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 17, "col": 3, "offset": 0}, "end": {"line": 17, "col": 49, "offset": 46}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 27, "col": 3, "offset": 0}, "end": {"line": 27, "col": 43, "offset": 40}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 31, "col": 3, "offset": 0}, "end": {"line": 31, "col": 44, "offset": 41}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 34, "col": 3, "offset": 0}, "end": {"line": 36, "col": 39, "offset": 119}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 39, "col": 7, "offset": 0}, "end": {"line": 39, "col": 40, "offset": 33}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 42, "col": 7, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 26}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 50, "col": 47, "offset": 58}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 54, "col": 3, "offset": 0}, "end": {"line": 54, "col": 14, "offset": 11}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "start": {"line": 64, "col": 1, "offset": 0}, "end": {"line": 64, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 29, "offset": 26}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 13, "col": 3, "offset": 0}, "end": {"line": 13, "col": 13, "offset": 10}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 22, "col": 3, "offset": 0}, "end": {"line": 24, "col": 15, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html:1:\n `{% extends \"two_factor/_base_focus.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 29, "offset": 26}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 13, "col": 3, "offset": 0}, "end": {"line": 13, "col": 13, "offset": 10}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "start": {"line": 22, "col": 3, "offset": 0}, "end": {"line": 24, "col": 15, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 101}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 8, "col": 3, "offset": 0}, "end": {"line": 8, "col": 26, "offset": 23}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 11, "col": 5, "offset": 0}, "end": {"line": 11, "col": 37, "offset": 32}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 15, "col": 7, "offset": 0}, "end": {"line": 15, "col": 29, "offset": 22}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 29, "col": 7, "offset": 0}, "end": {"line": 29, "col": 18, "offset": 11}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 32, "col": 5, "offset": 0}, "end": {"line": 32, "col": 16, "offset": 11}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 52, "col": 3, "offset": 0}, "end": {"line": 52, "col": 13, "offset": 10}}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 59, "col": 3, "offset": 0}, "end": {"line": 60, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html:1:\n `{% extends \"two_factor/_base.html\" %}\n{% load i18n %}\n{% load two_factor_tags %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "spans": [{"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 101}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 8, "col": 3, "offset": 0}, "end": {"line": 8, "col": 26, "offset": 23}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 11, "col": 5, "offset": 0}, "end": {"line": 11, "col": 37, "offset": 32}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 15, "col": 7, "offset": 0}, "end": {"line": 15, "col": 29, "offset": 22}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 29, "col": 7, "offset": 0}, "end": {"line": 29, "col": 18, "offset": 11}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 32, "col": 5, "offset": 0}, "end": {"line": 32, "col": 16, "offset": 11}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 52, "col": 3, "offset": 0}, "end": {"line": 52, "col": 13, "offset": 10}}, {"file": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "start": {"line": 59, "col": 3, "offset": 0}, "end": {"line": 60, "col": 15, "offset": 26}}]}], "paths": {"scanned": ["downloaded_repos/jazzband_django-two-factor-auth/.codecov.yml", "downloaded_repos/jazzband_django-two-factor-auth/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/jazzband_django-two-factor-auth/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/jazzband_django-two-factor-auth/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/jazzband_django-two-factor-auth/.github/workflows/build.yml", "downloaded_repos/jazzband_django-two-factor-auth/.github/workflows/release.yml", "downloaded_repos/jazzband_django-two-factor-auth/.gitignore", "downloaded_repos/jazzband_django-two-factor-auth/.pre-commit-config.yaml", "downloaded_repos/jazzband_django-two-factor-auth/.readthedocs.yml", "downloaded_repos/jazzband_django-two-factor-auth/.tx/config", "downloaded_repos/jazzband_django-two-factor-auth/CHANGELOG.md", "downloaded_repos/jazzband_django-two-factor-auth/CONTRIBUTING.rst", "downloaded_repos/jazzband_django-two-factor-auth/LICENSE", "downloaded_repos/jazzband_django-two-factor-auth/MANIFEST.in", "downloaded_repos/jazzband_django-two-factor-auth/Makefile", "downloaded_repos/jazzband_django-two-factor-auth/README.rst", "downloaded_repos/jazzband_django-two-factor-auth/docs/Makefile", "downloaded_repos/jazzband_django-two-factor-auth/docs/class-reference.rst", "downloaded_repos/jazzband_django-two-factor-auth/docs/conf.py", "downloaded_repos/jazzband_django-two-factor-auth/docs/configuration.rst", "downloaded_repos/jazzband_django-two-factor-auth/docs/extensions/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/docs/extensions/settings.py", "downloaded_repos/jazzband_django-two-factor-auth/docs/implementing.rst", "downloaded_repos/jazzband_django-two-factor-auth/docs/index.rst", "downloaded_repos/jazzband_django-two-factor-auth/docs/installation.rst", "downloaded_repos/jazzband_django-two-factor-auth/docs/management-commands.rst", "downloaded_repos/jazzband_django-two-factor-auth/docs/requirements.rst", "downloaded_repos/jazzband_django-two-factor-auth/example/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/example/gateways.py", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ar/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ar/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/as/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/as/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ca_ES/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ca_ES/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/cs/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/cs/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/da_DK/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/da_DK/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/de/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/en/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/en_GB/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/en_GB/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/es/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/es/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/fa/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/fa/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/fi/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/fi/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ha/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ha/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/he_IL/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/he_IL/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/hi_IN/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/hi_IN/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/hu_HU/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/hu_HU/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/it/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ja/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ja/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/lt/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/lt/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/nb/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/nb/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/pl/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/pl/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/pt_BR/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/pt_BR/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ro/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ro/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/sv/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/sv/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/tr/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/tr/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/vi/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/vi/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/zh_CN/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/example/locale/zh_CN/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/example/manage.py", "downloaded_repos/jazzband_django-two-factor-auth/example/settings.py", "downloaded_repos/jazzband_django-two-factor-auth/example/settings_private.py.dist", "downloaded_repos/jazzband_django-two-factor-auth/example/settings_webauthn.py", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_messages.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration/logged_out.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/secret.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_wizard_forms.html", "downloaded_repos/jazzband_django-two-factor-auth/example/templates/user_sessions/_base.html", "downloaded_repos/jazzband_django-two-factor-auth/example/urls.py", "downloaded_repos/jazzband_django-two-factor-auth/example/views.py", "downloaded_repos/jazzband_django-two-factor-auth/pyproject.toml", "downloaded_repos/jazzband_django-two-factor-auth/requirements_dev.txt", "downloaded_repos/jazzband_django-two-factor-auth/requirements_e2e.txt", "downloaded_repos/jazzband_django-two-factor-auth/tox.ini", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/admin.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/apps.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/forms.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/fake.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/twilio/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/twilio/gateway.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/twilio/urls.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/gateways/twilio/views.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ar/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ar/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/as/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/as/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ca_ES/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ca_ES/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/cs/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/cs/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/da_DK/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/da_DK/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/de/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/en/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/en_GB/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/en_GB/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/es/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/es/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/fa/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/fa/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/fi/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/fi/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ha/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ha/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/he_IL/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/he_IL/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/hi_IN/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/hi_IN/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/hu_HU/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/hu_HU/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/it/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ja/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ja/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/lt/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/lt/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/nb/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/nb/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/pl/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/pl/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/pt_BR/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/pt_BR/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ro/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ro/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/sv/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/sv/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/tr/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/tr/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/vi/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/vi/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/zh_CN/LC_MESSAGES/django.mo", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/locale/zh_CN/LC_MESSAGES/django.po", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/management/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/management/commands/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/management/commands/two_factor_disable.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/management/commands/two_factor_status.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/middleware/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/middleware/threadlocals.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0001_initial.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0001_squashed_0008_delete_phonedevice.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0002_auto_20150110_0810.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0003_auto_20150817_1733.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0004_auto_20160205_1827.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0005_auto_20160224_0450.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0006_phonedevice_key_default.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0007_auto_20201201_1019.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/0008_delete_phonedevice.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/migrations/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/email/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/email/apps.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/email/forms.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/email/method.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/email/utils.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/admin.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/apps.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/forms.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/method.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/migrations/0001_initial.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/migrations/0001_squashed_0001_initial.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/migrations/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/models.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/templatetags/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/templatetags/phonenumber.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/urls.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/utils.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/validators.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/views.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/registry.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/admin.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/apps.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/forms.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/method.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/migrations/0001_initial.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/migrations/0002_alter_webauthndevice_public_key.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/migrations/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/models.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/static/two_factor/js/webauthn_utils.js", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/create_credential.js", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/get_credential.js", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/urls.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/utils.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/views.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/yubikey/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/yubikey/apps.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/yubikey/forms.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/yubikey/method.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/signals.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_base.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_base_focus.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_forms.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/login.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/phone_register.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/disable.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/twilio/press_a_key.xml", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/twilio/sms_message.html", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/twilio/token.xml", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templatetags/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templatetags/two_factor_tags.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/urls.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/utils.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/__init__.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/core.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/mixins.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/profile.py", "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/utils.py"], "skipped": [{"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/_base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/home.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/registration_complete.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/example/templates/two_factor/_base_focus.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/templates/secure.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_commands.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_email.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_gateways.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_registry.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_totpdeviceform.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_validators.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_backuptokens.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_disable.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_login.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_mixins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_phone.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_profile.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_qrcode.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_views_setup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/test_yubikey.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/urls_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/urls_otp_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/tests/views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/phonenumber/tests/test_method.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/create_credential.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/templates/two_factor_webauthn/get_credential.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/tests/test_e2e.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/tests/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/plugins/webauthn/tests/test_views_setup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/_wizard_actions.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/backup_tokens.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/otp_required.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/core/setup_complete.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/templates/two_factor/profile/profile.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.608484983444214, "profiling_times": {"config_time": 7.118921518325806, "core_time": 6.475430488586426, "ignores_time": 0.0026879310607910156, "total_time": 13.598280191421509}, "parsing_time": {"total_time": 1.1642673015594482, "per_file_time": {"mean": 0.011303566034557748, "std_dev": 0.0005724761128781576}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 13.136866807937622, "per_file_time": {"mean": 0.02078618165812916, "std_dev": 0.007357609643015224}, "very_slow_stats": {"time_ratio": 0.1314779181631046, "count_ratio": 0.0015822784810126582}, "very_slow_files": [{"fpath": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/core.py", "ftime": 1.727207899093628}]}, "matching_time": {"total_time": 3.020719528198242, "per_file_and_rule_time": {"mean": 0.004189624865739588, "std_dev": 0.00023547073461652475}, "very_slow_stats": {"time_ratio": 0.14904991516846022, "count_ratio": 0.0027739251040221915}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/core.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.1669468879699707}, {"fpath": "downloaded_repos/jazzband_django-two-factor-auth/two_factor/views/core.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.2832911014556885}]}, "tainting_time": {"total_time": 0.858802318572998, "per_def_and_rule_time": {"mean": 0.00048355986406137285, "std_dev": 2.282413297304679e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}