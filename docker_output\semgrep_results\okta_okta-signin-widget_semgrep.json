{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/.testcaferc.js", "start": {"line": 35, "col": 9, "offset": 1041}, "end": {"line": 35, "col": 24, "offset": 1056}, "extra": {"message": "RegExp() called with a `strings` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/okta_okta-signin-widget/env/index.js", "start": {"line": 41, "col": 17, "offset": 1102}, "end": {"line": 41, "col": 28, "offset": 1113}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "start": {"line": 373, "col": 7, "offset": 9162}, "end": {"line": 373, "col": 18, "offset": 9173}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "start": {"line": 454, "col": 13, "offset": 10612}, "end": {"line": 454, "col": 22, "offset": 10621}, "extra": {"message": "RegExp() called with a `n` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "start": {"line": 455, "col": 13, "offset": 10635}, "end": {"line": 455, "col": 27, "offset": 10649}, "extra": {"message": "RegExp() called with a `n` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "start": {"line": 937, "col": 9, "offset": 21798}, "end": {"line": 937, "col": 17, "offset": 21806}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "start": {"line": 979, "col": 15, "offset": 22650}, "end": {"line": 979, "col": 127, "offset": 22762}, "extra": {"message": "RegExp() called with a `r` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "start": {"line": 979, "col": 15, "offset": 22650}, "end": {"line": 979, "col": 127, "offset": 22762}, "extra": {"message": "RegExp() called with a `t` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/framework/Model.js", "start": {"line": 98, "col": 7, "offset": 3367}, "end": {"line": 98, "col": 22, "offset": 3382}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/framework/Model.js", "start": {"line": 220, "col": 22, "offset": 7305}, "end": {"line": 220, "col": 76, "offset": 7359}, "extra": {"message": "RegExp() called with a `item` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/pseudo-loc/pseudo-loc.js", "start": {"line": 12, "col": 14, "offset": 415}, "end": {"line": 12, "col": 26, "offset": 427}, "extra": {"message": "Detected calls to child_process from a function argument `bundle`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/templateHelper.js", "start": {"line": 42, "col": 10, "offset": 991}, "end": {"line": 85, "col": 13, "offset": 2319}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-app-link.json", "start": {"line": 125, "col": 451, "offset": 3985}, "end": {"line": 125, "col": 1924, "offset": 5458}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-custom-uri.json", "start": {"line": 125, "col": 461, "offset": 4119}, "end": {"line": 125, "col": 1934, "offset": 5592}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-universal-link.json", "start": {"line": 125, "col": 461, "offset": 4120}, "end": {"line": 125, "col": 1934, "offset": 5593}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-credential-sso-extension.json", "start": {"line": 15, "col": 123, "offset": 476}, "end": {"line": 15, "col": 919, "offset": 1272}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-credential-sso-extension.json", "start": {"line": 22, "col": 30, "offset": 1859}, "end": {"line": 22, "col": 1030, "offset": 2859}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-redirect-sso-extension.json", "start": {"line": 15, "col": 124, "offset": 470}, "end": {"line": 15, "col": 920, "offset": 1266}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-redirect-sso-extension.json", "start": {"line": 22, "col": 33, "offset": 1866}, "end": {"line": 22, "col": 829, "offset": 2662}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-https-loopback.json", "start": {"line": 64, "col": 34, "offset": 2115}, "end": {"line": 64, "col": 678, "offset": 2759}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-2.json", "start": {"line": 64, "col": 34, "offset": 2115}, "end": {"line": 64, "col": 678, "offset": 2759}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-3.json", "start": {"line": 64, "col": 34, "offset": 2115}, "end": {"line": 64, "col": 678, "offset": 2759}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-4.json", "start": {"line": 64, "col": 34, "offset": 2115}, "end": {"line": 64, "col": 678, "offset": 2759}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback.json", "start": {"line": 64, "col": 34, "offset": 2115}, "end": {"line": 64, "col": 678, "offset": 2759}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/mdm-enrollment.json", "start": {"line": 2, "col": 447, "offset": 448}, "end": {"line": 2, "col": 1920, "offset": 1921}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/mdm-enrollment.json", "start": {"line": 27, "col": 453, "offset": 2978}, "end": {"line": 27, "col": 1926, "offset": 4451}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/oda-enrollment-android.json", "start": {"line": 2, "col": 447, "offset": 448}, "end": {"line": 2, "col": 1920, "offset": 1921}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/oda-enrollment-android.json", "start": {"line": 27, "col": 453, "offset": 2975}, "end": {"line": 27, "col": 1926, "offset": 4448}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/oda-enrollment-ios.json", "start": {"line": 2, "col": 447, "offset": 448}, "end": {"line": 2, "col": 1920, "offset": 1921}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/oda-enrollment-ios.json", "start": {"line": 26, "col": 453, "offset": 2934}, "end": {"line": 26, "col": 1926, "offset": 4407}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/ws1-device-integration-mobile-enrollment.json", "start": {"line": 2, "col": 449, "offset": 450}, "end": {"line": 2, "col": 1922, "offset": 1923}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/ws1-device-integration-mobile-enrollment.json", "start": {"line": 27, "col": 455, "offset": 3042}, "end": {"line": 27, "col": 1928, "offset": 4515}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/success-tokens.json", "start": {"line": 4, "col": 20, "offset": 69}, "end": {"line": 4, "col": 464, "offset": 513}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/success-tokens.json", "start": {"line": 6, "col": 16, "offset": 908}, "end": {"line": 6, "col": 598, "offset": 1490}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/server.js", "start": {"line": 26, "col": 58, "offset": 676}, "end": {"line": 26, "col": 70, "offset": 688}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/server.js", "start": {"line": 35, "col": 55, "offset": 1033}, "end": {"line": 35, "col": 67, "offset": 1045}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/server.js", "start": {"line": 35, "col": 69, "offset": 1047}, "end": {"line": 35, "col": 79, "offset": 1057}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/server.js", "start": {"line": 66, "col": 16, "offset": 1965}, "end": {"line": 66, "col": 20, "offset": 1969}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/server.js", "start": {"line": 78, "col": 7, "offset": 2201}, "end": {"line": 78, "col": 22, "offset": 2216}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/generate-phone-codes.js", "start": {"line": 20, "col": 7, "offset": 995}, "end": {"line": 20, "col": 17, "offset": 1005}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.aws-lambda.security.detect-child-process.detect-child-process", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/start.js", "start": {"line": 51, "col": 12, "offset": 1273}, "end": {"line": 51, "col": 15, "offset": 1276}, "extra": {"message": "Allowing spawning arbitrary programs or running shell processes with arbitrary arguments may end up in a command injection vulnerability. Try to avoid non-literal values for the command string. If it is not possible, then do not let running arbitrary commands, use a white list for inputs.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "category": "security", "technology": ["javascript", "aws-lambda"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "HIGH", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.aws-lambda.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/Ggoq"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/start.js", "start": {"line": 51, "col": 12, "offset": 1273}, "end": {"line": 51, "col": 15, "offset": 1276}, "extra": {"message": "Detected calls to child_process from a function argument `argv`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/test.js", "start": {"line": 50, "col": 47, "offset": 1107}, "end": {"line": 50, "col": 62, "offset": 1122}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/test.js", "start": {"line": 70, "col": 19, "offset": 1816}, "end": {"line": 70, "col": 55, "offset": 1852}, "extra": {"message": "RegExp() called with a `arg` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/test.js", "start": {"line": 86, "col": 16, "offset": 2317}, "end": {"line": 86, "col": 22, "offset": 2323}, "extra": {"message": "Detected calls to child_process from a function argument `script`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.aws-lambda.security.detect-child-process.detect-child-process", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/test.js", "start": {"line": 91, "col": 12, "offset": 2393}, "end": {"line": 91, "col": 15, "offset": 2396}, "extra": {"message": "Allowing spawning arbitrary programs or running shell processes with arbitrary arguments may end up in a command injection vulnerability. Try to avoid non-literal values for the command string. If it is not possible, then do not let running arbitrary commands, use a white list for inputs.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "category": "security", "technology": ["javascript", "aws-lambda"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "HIGH", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.aws-lambda.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/Ggoq"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/test.js", "start": {"line": 91, "col": 12, "offset": 2393}, "end": {"line": 91, "col": 15, "offset": 2396}, "extra": {"message": "Detected calls to child_process from a function argument `arg`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/test.js", "start": {"line": 91, "col": 12, "offset": 2393}, "end": {"line": 91, "col": 15, "offset": 2396}, "extra": {"message": "Detected calls to child_process from a function argument `argv`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "bash.lang.security.ifs-tampering.ifs-tampering", "path": "downloaded_repos/okta_okta-signin-widget/scripts/verify-package.sh", "start": {"line": 83, "col": 26, "offset": 2299}, "end": {"line": 83, "col": 32, "offset": 2305}, "extra": {"message": "The special variable IFS affects how splitting takes place when expanding unquoted variables. Don't set it globally. Prefer a dedicated utility such as 'cut' or 'awk' if you need to split input data. If you must use 'read', set IFS locally using e.g. 'IFS=\",\" read -a my_array'.", "metadata": {"cwe": ["CWE-20: Improper Input Validation"], "category": "security", "technology": ["bash"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/bash.lang.security.ifs-tampering.ifs-tampering", "shortlink": "https://sg.run/Q9pq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/src/v1/util/RegistrationFormFactory.js", "start": {"line": 92, "col": 12, "offset": 3094}, "end": {"line": 92, "col": 29, "offset": 3111}, "extra": {"message": "RegExp() called with a `subSchema` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/FormInputFactory.ts", "start": {"line": 19, "col": 3, "offset": 1008}, "end": {"line": 19, "col": 25, "offset": 1030}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/EndUserRemediationMessageViewUtil.js", "start": {"line": 78, "col": 7, "offset": 2773}, "end": {"line": 78, "col": 74, "offset": 2840}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/OktaSignIn/index.ts", "start": {"line": 273, "col": 25, "offset": 8017}, "end": {"line": 273, "col": 82, "offset": 8074}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/bin/properties-to-json.js", "start": {"line": 29, "col": 55, "offset": 1051}, "end": {"line": 29, "col": 58, "offset": 1054}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/bin/properties-to-json.js", "start": {"line": 29, "col": 60, "offset": 1056}, "end": {"line": 29, "col": 64, "offset": 1060}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/bin/properties-to-json.js", "start": {"line": 59, "col": 9, "offset": 1772}, "end": {"line": 59, "col": 13, "offset": 1776}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/cancel/default.json", "start": {"line": 178, "col": 73, "offset": 25460}, "end": {"line": 178, "col": 1496, "offset": 26883}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/error-invalid-username.json", "start": {"line": 163, "col": 73, "offset": 4853}, "end": {"line": 163, "col": 1681, "offset": 6461}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/error-wrong-password.json", "start": {"line": 196, "col": 73, "offset": 5114}, "end": {"line": 196, "col": 1681, "offset": 6722}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/all-enabled-features.json", "start": {"line": 178, "col": 73, "offset": 25460}, "end": {"line": 178, "col": 1496, "offset": 26883}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/identify-with-piv-cac.json", "start": {"line": 141, "col": 73, "offset": 4240}, "end": {"line": 141, "col": 1681, "offset": 5848}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-android-applink.json", "start": {"line": 2, "col": 447, "offset": 448}, "end": {"line": 2, "col": 1920, "offset": 1921}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-android-applink.json", "start": {"line": 28, "col": 453, "offset": 3003}, "end": {"line": 28, "col": 1926, "offset": 4476}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-android-loopback.json", "start": {"line": 2, "col": 447, "offset": 448}, "end": {"line": 2, "col": 1920, "offset": 1921}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-android-loopback.json", "start": {"line": 27, "col": 453, "offset": 2975}, "end": {"line": 27, "col": 1926, "offset": 4448}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-ios.json", "start": {"line": 2, "col": 447, "offset": 448}, "end": {"line": 2, "col": 1920, "offset": 1921}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-ios.json", "start": {"line": 26, "col": 453, "offset": 2934}, "end": {"line": 26, "col": 1926, "offset": 4407}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/token/default.json", "start": {"line": 4, "col": 20, "offset": 69}, "end": {"line": 4, "col": 508, "offset": 557}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/token/default.json", "start": {"line": 6, "col": 16, "offset": 952}, "end": {"line": 6, "col": 660, "offset": 1596}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/passwordUtils.ts", "start": {"line": 58, "col": 17, "offset": 2493}, "end": {"line": 58, "col": 47, "offset": 2523}, "extra": {"message": "RegExp() called with a `limit` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/passwordUtils.ts", "start": {"line": 106, "col": 24, "offset": 4084}, "end": {"line": 106, "col": 67, "offset": 4127}, "extra": {"message": "RegExp() called with a `attributeVal` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/probeLoopbackAndExecute.ts", "start": {"line": 60, "col": 9, "offset": 2281}, "end": {"line": 60, "col": 54, "offset": 2326}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 10, "col": 34, "offset": 376}, "end": {"line": 10, "col": 45, "offset": 387}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 10, "col": 47, "offset": 389}, "end": {"line": 10, "col": 63, "offset": 405}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 14, "col": 36, "offset": 518}, "end": {"line": 14, "col": 47, "offset": 529}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 14, "col": 49, "offset": 531}, "end": {"line": 14, "col": 65, "offset": 547}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 18, "col": 32, "offset": 661}, "end": {"line": 18, "col": 43, "offset": 672}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 18, "col": 45, "offset": 674}, "end": {"line": 18, "col": 61, "offset": 690}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 23, "col": 5, "offset": 775}, "end": {"line": 23, "col": 28, "offset": 798}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "start": {"line": 24, "col": 5, "offset": 804}, "end": {"line": 24, "col": 67, "offset": 866}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-empty-response.json:1:\n missing element", "path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-empty-response.json"}], "paths": {"scanned": ["downloaded_repos/okta_okta-signin-widget/.bacon.yml", "downloaded_repos/okta_okta-signin-widget/.editorconfig", "downloaded_repos/okta_okta-signin-widget/.eslintignore", "downloaded_repos/okta_okta-signin-widget/.eslintrc.js", "downloaded_repos/okta_okta-signin-widget/.gitattributes", "downloaded_repos/okta_okta-signin-widget/.github/CODEOWNERS", "downloaded_repos/okta_okta-signin-widget/.github/ISSUE_TEMPLATE/bug-report.yml", "downloaded_repos/okta_okta-signin-widget/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/okta_okta-signin-widget/.github/ISSUE_TEMPLATE/feature-request.yml", "downloaded_repos/okta_okta-signin-widget/.github/PULL_REQUEST_TEMPLATE/pr_external.md", "downloaded_repos/okta_okta-signin-widget/.github/pull_request_template.md", "downloaded_repos/okta_okta-signin-widget/.gitignore", "downloaded_repos/okta_okta-signin-widget/.npmignore", "downloaded_repos/okta_okta-signin-widget/.retireignore.json", "downloaded_repos/okta_okta-signin-widget/.stylelintignore", "downloaded_repos/okta_okta-signin-widget/.testcaferc.js", "downloaded_repos/okta_okta-signin-widget/.vscode/launch.json", "downloaded_repos/okta_okta-signin-widget/.widgetrc.sample.js", "downloaded_repos/okta_okta-signin-widget/CONTRIBUTING.md", "downloaded_repos/okta_okta-signin-widget/Gruntfile.js", "downloaded_repos/okta_okta-signin-widget/LICENSE", "downloaded_repos/okta_okta-signin-widget/MIGRATING.md", "downloaded_repos/okta_okta-signin-widget/README.md", "downloaded_repos/okta_okta-signin-widget/THIRD-PARTY-NOTICES", "downloaded_repos/okta_okta-signin-widget/assets/css/okta-plugin-a11y.css", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-light-webfont.eot", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-light-webfont.otf", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-light-webfont.svg", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-light-webfont.ttf", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-light-webfont.woff", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-regular-webfont.eot", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-regular-webfont.otf", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-regular-webfont.svg", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-regular-webfont.ttf", "downloaded_repos/okta_okta-signin-widget/assets/font/montserrat-okta-regular-webfont.woff", "downloaded_repos/okta_okta-signin-widget/assets/font/okticon.eot", "downloaded_repos/okta_okta-signin-widget/assets/font/okticon.svg", "downloaded_repos/okta_okta-signin-widget/assets/font/okticon.ttf", "downloaded_repos/okta_okta-signin-widget/assets/font/okticon.woff", "downloaded_repos/okta_okta-signin-widget/assets/img/appstore/apple-app-store.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/appstore/google-play-store.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/appstore/osx-app-store.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/appstore/windows-app-store.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/16x16/error-01.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/identifier/user-icon.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/adobe_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/amazon_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/apple_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/clear.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/default_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/discord_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/facebook_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/github_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/gitlab_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/google_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/incode.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/line_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/linkedin_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/microsoft_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/orcid_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/paypal_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/paypal_sandbox_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/persona.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/quickbooks_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/salesforce_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/smartcard_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/spotify_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/tooltip_close.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/xero_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/yahoo_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/login/yahoojp_logo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/androidActive_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/android_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/app_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/app_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/app_76x76.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/blackberryActive_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/blackberry_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/custom-app-default-logo.svg", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/customFactor_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/customFactor_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/desktop_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/desktop_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/desktop_76x76.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/duo_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/duo_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/email_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/email_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/googleAuth_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/googleAuth_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/hotpFactor_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/hotpFactor_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/iOSActive_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/iOS_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/location_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/location_76x76.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/oktaVerify_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/oktaVerify_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/oktaVerify_authenticator.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/onprem_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/onprem_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/password_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/password_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/phone_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/question_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/question_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/rsa_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/rsa_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/smartphone_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/smartphone_76x76.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/sms_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/sms_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/symantec_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/symantec_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/u2f_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/u2f_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/u2f_bluetooth.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/u2f_usb.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/voicecall_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/voicecall_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/webauthn_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/webauthn_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/webauthn_authenticator.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/windowsActive_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/windowsHello_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/windowsHello_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/windows_60x60.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/yubico_38x38.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/yubico_70x70.png", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/icons/mfa/yubikeyDemo.png", "downloaded_repos/okta_okta-signin-widget/assets/img/logo_widgico.png", "downloaded_repos/okta_okta-signin-widget/assets/img/okta-signin-widget.png", "downloaded_repos/okta_okta-signin-widget/assets/img/security/default.png", "downloaded_repos/okta_okta-signin-widget/assets/img/security/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/security/unknown-device.png", "downloaded_repos/okta_okta-signin-widget/assets/img/security/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/button/okta-buttons-sprite-001.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/checkbox-01.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/checkbox-sign-in-widget-a11y.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/checkbox-sign-in-widget.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/chosen-sprite.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/radiobutton-01-a11y.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/radiobutton-01.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/forms/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/ajax-loader-processing-16.gif", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/ajax-loader-processing-24.gif", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/checkmark.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/clock.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/drop-menu-s-white.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/drop-menu-s.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/<EMAIL>", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/scanerror.png", "downloaded_repos/okta_okta-signin-widget/assets/img/ui/indicators/sign-on-widget-spinner.gif", "downloaded_repos/okta_okta-signin-widget/assets/sass/_base.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/_container.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/_fonts.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/_helpers.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/_ie.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/_layout.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/_variables.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/icons/_all.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/icons/_classes-social.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/icons/_classes.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/icons/_functions.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/icons/_variables-theme.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/icons/_variables-unicode-social.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/icons/_variables-unicode.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/admin/modules/infobox/_infobox.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/enduser/_helpers.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/enduser/_reset.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/enduser/_responsive-variables.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/foo.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/shared/helpers/_all.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/shared/helpers/_mixins.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/shared/helpers/_variables.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/shared/o-forms/_o-form-variable.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/common/shared/o-forms/_o-form.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_accessibility.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_admin-consent.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_app-login-banner.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_beacon.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_btns.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_consent.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_email-factor.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_enroll.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_factors-dropdown.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_footer.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_forgot-password.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_forms.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_header.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_infobox.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_mfa-challenge-forms.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_okta-footer.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_password-requirements.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_qtip.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_registration.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/modules/_social.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/okta-sign-in.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/okta-theme.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v1/_all.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v1/_device-code-terminal.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_all.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_beacon.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_callout.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_captcha.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_challenge-authenticator-email.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_consent-email.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_custom-access-denied-error-message.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_device-challenge.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_device-code-terminal.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_device-enrollment-terminal.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_duo.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_end-user-remediation-messages-view.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_enroll-phone.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_enroll-profile.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_factor-poll-verification.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_footer.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_forms.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_google-authenticator.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_identify.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_number-challenge.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_okta-verify.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_password.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_phone-challenge.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_piv-idp.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_select-authenticator.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_success-redirect.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_webauthn.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/v2/_yubikey.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/widgets/_chosen.scss", "downloaded_repos/okta_okta-signin-widget/assets/sass/widgets/_mega-drop-down.scss", "downloaded_repos/okta_okta-signin-widget/babel.config.js", "downloaded_repos/okta_okta-signin-widget/docs/classic.md", "downloaded_repos/okta_okta-signin-widget/docs/manage-stateHandle-in-sessionStorage.org", "downloaded_repos/okta_okta-signin-widget/docs/manage-stateHandle-in-sessionStorage.png", "downloaded_repos/okta_okta-signin-widget/env/env.defaults.js", "downloaded_repos/okta_okta-signin-widget/env/index.js", "downloaded_repos/okta_okta-signin-widget/env/package.json", "downloaded_repos/okta_okta-signin-widget/eslint-local-rules.js", "downloaded_repos/okta_okta-signin-widget/jest.config.js", "downloaded_repos/okta_okta-signin-widget/package.json", "downloaded_repos/okta_okta-signin-widget/packages/@okta/babel-plugin-handlebars-inline-precompile/README.md", "downloaded_repos/okta_okta-signin-widget/packages/@okta/babel-plugin-handlebars-inline-precompile/handlebars/index.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/babel-plugin-handlebars-inline-precompile/handlebars/patch-precompile.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/babel-plugin-handlebars-inline-precompile/hbs.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/babel-plugin-handlebars-inline-precompile/package.json", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/.gitattributes", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/_commonjsHelpers.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/backbone.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/base.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/block-helper-missing.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/create-new-lookup-object.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/decorators.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/each.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/exception.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/handlebars.runtime.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/helper-missing.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/helpers.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/if.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/inline.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/js.cookie.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/log.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/logger.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/lookup.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/no-conflict.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/proto-access.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/runtime.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/safe-string.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/underscore-min.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/utils.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/with.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/_virtual/wrapHelper.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/CourageForSigninWidget.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/framework/Collection.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/framework/ListView.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/framework/Model.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/framework/View.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/BaseCollection.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/BaseModel.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/BaseSchema.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/Model.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/SchemaProperty.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/BaseController.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/BaseRouter.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/ButtonFactory.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/Class.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/Clipboard.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/Cookie.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/Keys.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/Logger.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/SchemaUtil.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/SettingsModel.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/StateMachine.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/StringUtil.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/TemplateUtil.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/Time.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/Util.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/ViewUtil.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/handlebars/handle-url.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/handlebars/helper-i18n.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/jquery-wrapper.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/util/underscore-wrapper.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/Backbone.ListView.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/BaseView.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/components/BaseButtonLink.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/components/BaseDropDown.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/components/Callout.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/components/Notification.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseForm.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseInput.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/components/ReadModeBar.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/components/Toolbar.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/EnumTypeHelper.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/ErrorBanner.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/ErrorParser.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/FormUtil.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/InputContainer.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/InputFactory.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/InputLabel.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/InputRegistry.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/InputWrapper.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/helpers/SchemaFormFactory.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/BooleanSelect.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/CheckBox.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/DeletableBox.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/InputGroup.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/PasswordBox.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/Radio.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/Select.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/TextBox.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/TextBoxSet.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/empty.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/index.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/util/scrollParent.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/package.json", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/CourageForSigninWidget.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/framework/Collection.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/framework/ListView.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/framework/Model.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/framework/View.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/models/BaseCollection.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/models/BaseModel.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/models/BaseSchema.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/models/Model.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/models/SchemaProperty.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/okta-i18n-bundles.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/BaseController.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/BaseRouter.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/ButtonFactory.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/Class.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/Clipboard.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/Cookie.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/Keys.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/Logger.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/SchemaUtil.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/SettingsModel.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/StateMachine.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/StringUtil.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/TemplateUtil.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/Time.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/Util.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/ViewUtil.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/handlebars/handle-url.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/handlebars/helper-i18n.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/jquery-wrapper.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/util/underscore-wrapper.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/Backbone.ListView.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/BaseView.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/components/BaseButtonLink.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/components/BaseDropDown.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/components/BaseFormDialog.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/components/BaseModalDialog.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/components/Callout.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/components/ConfirmationDialog.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/components/Notification.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/BaseForm.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/BaseInput.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/components/ReadModeBar.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/components/Toolbar.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/EnumTypeHelper.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/ErrorBanner.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/ErrorParser.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/FormUtil.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/InputContainer.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/InputFactory.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/InputLabel.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/InputRegistry.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/InputWrapper.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/helpers/SchemaFormFactory.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/BooleanSelect.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/CheckBox.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/DeletableBox.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/InputGroup.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/PasswordBox.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/Radio.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/Select.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/TextBox.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/inputs/TextBoxSet.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/views/forms/types.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/index.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/util/handlebars-inline-precompile.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/util/scrollParent.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/.eslintignore", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/.eslintrc", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/.gitignore", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/README.md", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/babel.config.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/package.json", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/rollup.config.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/scripts/patch.sh", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/src/CourageForSigninWidget.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/src/empty.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/src/index.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/src/types/global.d.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/src/util/handlebars-inline-precompile.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/src/util/scrollParent.ts", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/tsconfig.json", "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-for-signin-widget/yarn.lock", "downloaded_repos/okta_okta-signin-widget/packages/@okta/eslint-plugin-okta-ui/lib/rules/no-bare-templates.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/eslint-plugin-okta-ui/lib/rules/no-missing-api-keys.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/eslint-plugin-okta-ui/lib/rules/no-missing-keys.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/i18n.config.json", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_cs.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_da.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_de.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_el.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_es.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_eu.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_fi.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_fr.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ht.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_hu.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_id.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_it.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ja.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ko.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ms.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_nb.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_nl_NL.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ok_PL.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ok_SK.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_pl.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_pt_BR.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ro.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_ru.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_sv.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_th.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_tr.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_uk.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_vi.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_zh_CN.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/country_zh_TW.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_cs.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_da.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_de.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_el.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_es.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_eu.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_fi.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_fr.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ht.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_hu.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_id.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_it.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ja.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ko.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ms.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_nb.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_nl_NL.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ok_PL.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ok_SK.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_pl.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_pt_BR.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ro.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_ru.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_sv.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_th.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_tr.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_uk.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_vi.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_zh_CN.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/i18n/src/properties/login_zh_TW.properties", "downloaded_repos/okta_okta-signin-widget/packages/@okta/pseudo-loc/package.json", "downloaded_repos/okta_okta-signin-widget/packages/@okta/pseudo-loc/pseudo-loc.js", "downloaded_repos/okta_okta-signin-widget/packages/@okta/pseudo-loc/yarn.lock", "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/LICENSE", "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/README.md", "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/package.json", "downloaded_repos/okta_okta-signin-widget/patches/dompurify+2.5.8.patch", "downloaded_repos/okta_okta-signin-widget/playground/LocaleUtils/index.js", "downloaded_repos/okta_okta-signin-widget/playground/README.md", "downloaded_repos/okta_okta-signin-widget/playground/cdn.html", "downloaded_repos/okta_okta-signin-widget/playground/error.html", "downloaded_repos/okta_okta-signin-widget/playground/external-page.html", "downloaded_repos/okta_okta-signin-widget/playground/favicon.ico", "downloaded_repos/okta_okta-signin-widget/playground/hooks/css/customize.css", "downloaded_repos/okta_okta-signin-widget/playground/hooks/index.ts", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/all.js", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/all.ts", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/challenge-authenticator.js", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/challenge-authenticator.ts", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/enroll-authenticator.js", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/enroll-authenticator.ts", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/enroll-profile.js", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/enroll-profile.ts", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/identify-recovery.js", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/identify-recovery.ts", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/identify.js", "downloaded_repos/okta_okta-signin-widget/playground/hooks/pages/identify.ts", "downloaded_repos/okta_okta-signin-widget/playground/img/logos/default.png", "downloaded_repos/okta_okta-signin-widget/playground/index.html", "downloaded_repos/okta_okta-signin-widget/playground/main.ts", "downloaded_repos/okta_okta-signin-widget/playground/mockServiceWorker.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/app/UserHome.html", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/responseConfig.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/templateHelper.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ChallengeAuthenticatorEmail.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ChallengeAuthenticatorPassword.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ChallengeAuthenticatorSecurityQuestion.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ChallengeCustomAppPush.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ChallengeOktaVerifyFastPassView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ChallengeOktaVerifySSOExtensionView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/DeviceChallengePollView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/DeviceChallengePollViewFailure.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/DeviceSSOExtensionView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/EnrollAuthenticatorEmail.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/EnrollAuthenticatorPhoneView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/EnrollAuthenticatorWebAuthn.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/Identify.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/IdentifyRegistrationHooks.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/IdentifyUnknownUser.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/IdentifyWithRememberUsername.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/README.md", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ReEnrollAuthenticatorPasswordView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ReEnrollAuthenticatorWarningPasswordView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/ResetAuthenticatorPasswordView.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/SelectAuthenticatorForEnroll.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/SessionStorage.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/UserUnlockAccountLandingApp.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/WidgetCustomization.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/config/test-configs/index.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/.well-known/webfinger/forced-idp-discovery-linkedin-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/.well-known/webfinger/forced-idp-discovery-okta-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/admin-consent-required.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/cancel.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/consent-required-granular.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/consent-required.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/device-code-activate-userCode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/device-code-activate.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/error-authentication-failed.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/error-device-code-activate.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/error-invalid-device-code.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-all-factors.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-challenge-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-enroll-activate-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-enroll-activate-sms.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-enroll-email-with-sms-enrolled.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-enroll-ov-manual.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-enroll-qr-okta-verify.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-enroll-sms.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-enroll-totp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-number-challenge-ov.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-required-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/mfa-required-oktaverify.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/recovery-answer.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/recovery-challenge-sms-pwd.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/recovery-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/success-001.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/terminal-device-activated.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/terminal-device-not-activated-consent-denied.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/terminal-device-not-activated-internal-error.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/unauthenticated-using-device-flow.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/authn/unauthenticated.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/registration/form.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/api/v1/registration/register.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/_ui-demo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/apple-sso-extension-verify-cancel.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-custom-app-push.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-data-phone-voice.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-data-phone.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-duo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-email-emailmagiclink-false.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-email-emailmagiclink-true.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-email-first-emailmagiclink-true.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-email-first.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-google-authenticator.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-grace-period-with-skip.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-grace-period.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-idp-custom-logo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-on-prem.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-device-bootstrap-multiple-devices.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-device-bootstrap.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-email-enable-biometrics.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-email-version-upgrade-non-ios.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-email-version-upgrade.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-qr-enable-biometrics.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-qr-version-upgrade-non-ios.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-qr-version-upgrade.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-qr-with-same-device.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-qr.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-same-device-android-high-security.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-same-device-ios-any-security.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-same-device-osx-any-security.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-same-device-windows-high-security.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-same-device.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-sms-enable-biometrics.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-sms-version-upgrade-non-ios.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-sms-version-upgrade.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-sms.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-via-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-ov-via-sms.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-password-with-ad-req.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-phone-voice.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-phone.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-rsa.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-security-question.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-select-authenticator-with-skip.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-select-authenticator-with-usage-info.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-select-authenticator.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-smartcard.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-symantec-vip.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-webauthn.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-enroll-yubikey.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expired-custom-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expired-password-no-complexity.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expired-password-with-ad-req.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expired-password-with-enrollment-authenticator.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expired-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expiry-warning-custom-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expiry-warning-password-with-ad-req.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-expiry-warning-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-recovery-password-failure.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-reset-google.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-reset-password-with-ad-req.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-reset-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-custom-app-push-autochallenge.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-custom-app-push-reject.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-custom-app-push.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-custom-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-custom-push-autoChallenge-off.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-email-no-profile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-email-with-secondary-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-okta-verify-push-autoChallenge-off.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-okta-verify-push-only-without-autochallenge.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-ov-only-with-device-known.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-ov-only-without-device-known.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-phone-sms-then-voice-nickname.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-phone-sms-then-voice-no-profile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-phone-sms-then-voice.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-phone-voice-only.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-phone-voice-then-sms-no-profile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-data-phone-voice-then-sms.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-duo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-no-profile-no-emailmagiclink.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-no-profile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-polling-long.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-polling-short.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-polling-very-short.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-polling.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-with-secondary-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-without-emailmagiclink.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email-without-resend.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-google-authenticator.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-idp-custom-logo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-idp-single-remediation.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-idp-with-clear.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-idp-with-incode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-idp-with-persona.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-number-challenge.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-push-autoChallenge-on.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-push.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-reject-push.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-app-link.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-credential-sso-extension.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-custom-uri.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-loopback-second-challenge.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-loopback-with-enhanced-polling.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-loopback.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-signed-nonce-universal-link.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-totp-onlyOV.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-okta-verify-totp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-on-prem.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-phone-sms-nickname.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-phone-sms-no-profile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-phone-sms.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-phone-voice-no-profile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-phone-voice.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-rsa.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-security-question.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator-custom-logo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator-for-recovery.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator-no-number.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator-ov-m2.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator-with-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator-with-nickname.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator-without-signed-nonce.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-select-authenticator.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-smartcard.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-symantec-vip.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-tac.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-webauthn.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/authenticator-verification-yubikey.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/consent-admin.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/consent-enduser-custom-scopes.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/consent-enduser.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/consent-granular.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/custom-app-uv-verify-enable-biometrics.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/device-assurance-grace-period-multiple-options.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/device-assurance-grace-period-one-option.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/device-code-activate-userCode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/device-code-activate.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/device-probing-chrome-dtc.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/email-challenge-consent.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-adp-remediation-app-link-fallback-single-option-with-grace-period.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-adp-remediation-app-link-fallback-with-multiple-options.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-adp-remediation-app-link-fallback-with-single-option.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-adp-remediation-message-fallback-multiple-options-with-grace-period.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-adp-remediation-message-fallback-with-multiple-options.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-adp-remediation-message-fallback-with-single-option.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-custom-message-custom-url.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-custom-message-multiple-options.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-custom-message-no-url.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-default-message-custom-url.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-multiple-options-with-custom-help-url.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-multiple-options.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-no-options.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/end-user-remediation-one-option.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-all-base-attributes.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-new-additional-fields.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-new-boolean-fields.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-new-checkbox.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-new-custom-labels.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-new-with-hcaptcha.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-new-with-recaptcha-v2.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-new.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-submit.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-update-all-optional-params.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-update-params.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-with-idps.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-with-password-returns-error.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-with-password-returns-multiple-errors.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile-with-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-profile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/enroll-security-question-with-character-limit-error.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-bad-request.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-fake-error.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-okta-verify-uv-fastpass-verify-enable-biometrics-mobile.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-unauthorized-client.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-unlock-account.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-user-not-assigned-2.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-user-not-assigned-3.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-400-user-not-assigned.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-401-apple-sso-extension-verify.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-401-invalid-email-otp-passcode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-401-invalid-otp-passcode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-401-okta-verify-apple-sso-step-up.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-401-session-expired.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-403-access-denied.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-403-security-access-denied.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-404-verification-timed-out.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-429-api-limit-exceeded.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-429-authenticator-verification-email-polling.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-429-too-many-request-operation-ratelimit.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-429-too-many-request.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-account-creation.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-challenge-phone-invalid-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-duo-verification-failed.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-duo-verification-timeout.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-custom-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-email-invalid-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-google-invalid-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-idp-custom-logo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-passcode-change-on-prem.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-passcode-change-rsa.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-password-common.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-phone-invalid-number.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-phone-invalid-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-security-question-create-question.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-security-question-html-tags.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-enroll-security-question.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-phone-sms-ratelimit.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-phone-voice-ratelimit.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-reset-google-invalid-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-reset-password-requirement.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-custom-otp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-idp-custom-logo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-on-google-otp-invalid-passcode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-on-google-otp-used-passcode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-on-prem.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-passcode-change-on-prem.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-passcode-change-rsa.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-password-too-many-attempts.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-rsa.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-symantec-vip-invalid-passcode.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verification-tac.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-verify-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-authenticator-webauthn-failure.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-change-password-not-allowed.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-custom-access-denied-success-redirect.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-device-code-activate.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-empty-response.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-enroll-regisration-unavailable.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-feature-not-enabled.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-forgot-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-identify-access-denied-custom-message.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-identify-access-denied.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-identify-multiple-errors.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-identify-with-only-one-third-party-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-identify-with-piv.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-internal-server-error.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-invalid-device-code.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-new-signup-email-exists.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-new-signup-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-okta-verify-totp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-okta-verify-uv-fastpass-verify-enable-biometrics-desktop.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-okta-verify-uv-fastpass-verify-enable-biometrics-mobile-multiple-errors.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-okta-verify-uv-totp-verify-enable-biometrics.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-password-reset-failed.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-poll-400-device-account-invalid.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-pre-versioning-ff-session-expired.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-request-activation-email-expired-token.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-request-activation-email-invalid.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-request-activation-email-suspended.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-request-not-completed.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-reset-password-not-allowed.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-safe-mode-polling.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-terminal-multiple-errors.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-unable-to-authenticate-user.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-unlock-account-failed-permissions.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-unlock-account.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-user-is-not-assigned.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-with-failure-redirect.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/errors-authenticator-verification-okta-verify-reject-push.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/failure-redirect-remediation.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-recovery-with-hcaptcha.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-recovery-with-recaptcha-v2.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-recovery.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-unknown-user.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-app-link.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-credential-sso-extension.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-redirect-sso-extension-2.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-redirect-sso-extension.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-sso-extension-fallback-no-link.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-apple-sso-extension-fallback.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-launch-authenticator.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-https-loopback.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-2.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-3.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-4.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-challenge-not-received-android-no-link.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-challenge-not-received-android.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-challenge-not-received-with-remember-me.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback-challenge-not-received.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-device-probing-loopback.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-no-sso-extension.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-only-one-third-party-idp-app-user.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-only-one-third-party-idp.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-only-third-party-idps.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-password-with-hcaptcha.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-password-with-recaptcha-v2.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-password.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-piv-only.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-remember-me.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-third-party-idps.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-universal-link-w-session.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-universal-link.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-unlock-account-link.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-user.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-username.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-webauthn-autofill.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-webauthn-launch-authenticator.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify-with-webauthn-residentkey.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/identify.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/mdm-enrollment.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/oda-enrollment-android.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/oda-enrollment-ios.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/okta-verify-uv-verify-enable-biometrics.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/okta-verify-version-upgrade.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/post-auth-keep-me-signed-in.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/profile-enrollment-string-fields-options.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/request-activation-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/safe-mode-credential-enrollment-intent.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/safe-mode-optional-enrollment.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/safe-mode-polling-refreshed-interval.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/safe-mode-polling.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/safe-mode-required-enrollment.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/smart-probing-required.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/success-redirect-remediation.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/success-with-app-user.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/success-with-interaction-code.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/success.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-device-activated.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-device-not-activated-consent-denied.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-device-not-activated-internal-error.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-enduser-email-consent-denied.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-invalid-forgot-password-token.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-invalid-reset-password-token.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-okta-verify-enrollment-android-device.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-polling-window-expired.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-registration.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-request-activation-email-submitted.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-reset-password-inactive-user.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-reset-password-success.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-email-consent-denied.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-email-consent.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-error-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-expired-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-only-full-location-mobile-icon-authentication.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-only-full-location-mobile-icon-enrollment.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-only-full-location-mobile-icon-recovery.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-only-full-location-mobile-icon-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-only-full-location.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-only-no-location.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-only-partial-location.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-otp-unexpected-response.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-return-stale-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/terminal-transfered-email.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-answer-1-choose-auth-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-answer-2-choose-auth-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-answer-choose-auth-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-challenge-choose-auth-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-challenge-direct-landing-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-choose-auth-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-direct-landing-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-introspect-choose-auth-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-introspect-direct-landing-after-unlock.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-success-land-on-app.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-account-unlock-success.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-unlock-account-choose-auth-identifier-first.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-unlock-account-identifier-first.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/user-unlock-account.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/ws1-device-integration-mobile-enrollment.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/error-activation-token-invalid.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/error-feature-not-enabled.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/error-recovery-token-invalid.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/error-token-invalid-grant-pkce.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/interact.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/success-tokens.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/oauth2/well-known-openid-configuration.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/labels/json/country_foo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/labels/json/login_foo.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/package.json", "downloaded_repos/okta_okta-signin-widget/playground/mocks/server.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-device-authenticator/README.md", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-device-authenticator/challenge.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-device-authenticator/launch-okta-verify.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-device-authenticator/probe.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-duo/duo-iframe.html", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-duo/duo-mock.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/api/v1/authn/index.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/api/v1/registration/index.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/app/userHome.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/auth/services/devicefingerprint.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/idp/idx/index.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/login/getimage/index.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/login/sessionCookieRedirect/index.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/oauth2/index.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/oauth2/v1/authorize/callback.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/sso/idps.js", "downloaded_repos/okta_okta-signin-widget/playground/mocks/spec-okta-api/wellKnown/index.js", "downloaded_repos/okta_okta-signin-widget/playground/okta-verify.html", "downloaded_repos/okta_okta-signin-widget/playground/package.json", "downloaded_repos/okta_okta-signin-widget/playground/runtime.js", "downloaded_repos/okta_okta-signin-widget/playground/tsconfig.json", "downloaded_repos/okta_okta-signin-widget/polyfill/README.md", "downloaded_repos/okta_okta-signin-widget/polyfill/debugger.js", "downloaded_repos/okta_okta-signin-widget/polyfill/index.js", "downloaded_repos/okta_okta-signin-widget/polyfill/modern.js", "downloaded_repos/okta_okta-signin-widget/rollup.common.js", "downloaded_repos/okta_okta-signin-widget/rollup.config.js", "downloaded_repos/okta_okta-signin-widget/rollup.release.config.js", "downloaded_repos/okta_okta-signin-widget/scripts/backport-siw-next.sh", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/.eslintrc.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/generate-language-config.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/generate-phone-codes.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/prepack.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/pseudo-loc.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/start.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/test.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/commands/verify-package.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/eslint/.eslintrc.cdn.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/eslint/.eslintrc.esm.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/eslint/.eslintrc.types.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/index.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/terser/config.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/tsc/handlebars-inline-precompile.ts", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/webpack/FailOnBuildFailPlugin.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/webpack/plugins.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/webpack/polyfill.js", "downloaded_repos/okta_okta-signin-widget/scripts/buildtools/webpack/runtime.js", "downloaded_repos/okta_okta-signin-widget/scripts/codegen.sh", "downloaded_repos/okta_okta-signin-widget/scripts/detect-english-leaks-v3.sh", "downloaded_repos/okta_okta-signin-widget/scripts/detect-english-leaks.sh", "downloaded_repos/okta_okta-signin-widget/scripts/downstream/create-downstream-for-authjs.sh", "downloaded_repos/okta_okta-signin-widget/scripts/downstream/create-downstream-for-courage.sh", "downloaded_repos/okta_okta-signin-widget/scripts/downstream/create-downstream-for-dockolith.sh", "downloaded_repos/okta_okta-signin-widget/scripts/downstream/create-downstream-for-monolith.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e-lang.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e-monolith.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e-saucelabs-mobile-v3.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e-saucelabs-mobile.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e-saucelabs-v3.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e-saucelabs.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e-v3.sh", "downloaded_repos/okta_okta-signin-widget/scripts/e2e.sh", "downloaded_repos/okta_okta-signin-widget/scripts/find-internal-packages.js", "downloaded_repos/okta_okta-signin-widget/scripts/find-internal-packages.sh", "downloaded_repos/okta_okta-signin-widget/scripts/lint-v3.sh", "downloaded_repos/okta_okta-signin-widget/scripts/lint.sh", "downloaded_repos/okta_okta-signin-widget/scripts/monolith/create-testenv.sh", "downloaded_repos/okta_okta-signin-widget/scripts/monolith/deploy-widget.sh", "downloaded_repos/okta_okta-signin-widget/scripts/monolith/install-dockolith.sh", "downloaded_repos/okta_okta-signin-widget/scripts/monolith/lib/common-widget-setup.sh", "downloaded_repos/okta_okta-signin-widget/scripts/monolith/start.sh", "downloaded_repos/okta_okta-signin-widget/scripts/parity-v3-mobile.sh", "downloaded_repos/okta_okta-signin-widget/scripts/parity-v3.sh", "downloaded_repos/okta_okta-signin-widget/scripts/publish.sh", "downloaded_repos/okta_okta-signin-widget/scripts/release-update.sh", "downloaded_repos/okta_okta-signin-widget/scripts/semgrep.sh", "downloaded_repos/okta_okta-signin-widget/scripts/setup.sh", "downloaded_repos/okta_okta-signin-widget/scripts/snyk.sh", "downloaded_repos/okta_okta-signin-widget/scripts/testcafe-mobile.sh", "downloaded_repos/okta_okta-signin-widget/scripts/testcafe.sh", "downloaded_repos/okta_okta-signin-widget/scripts/tsd.sh", "downloaded_repos/okta_okta-signin-widget/scripts/unit-jest.sh", "downloaded_repos/okta_okta-signin-widget/scripts/unit-karma.sh", "downloaded_repos/okta_okta-signin-widget/scripts/unit-v3.sh", "downloaded_repos/okta_okta-signin-widget/scripts/verify-package.sh", "downloaded_repos/okta_okta-signin-widget/scripts/verify-registry-install.sh", "downloaded_repos/okta_okta-signin-widget/scripts/vrt-v3-get-screenshots.sh", "downloaded_repos/okta_okta-signin-widget/scripts/vrt-v3.sh", "downloaded_repos/okta_okta-signin-widget/src/authClient/classic.ts", "downloaded_repos/okta_okta-signin-widget/src/authClient/default.ts", "downloaded_repos/okta_okta-signin-widget/src/authClient/oie.ts", "downloaded_repos/okta_okta-signin-widget/src/exports/cdn/classic.ts", "downloaded_repos/okta_okta-signin-widget/src/exports/cdn/default.ts", "downloaded_repos/okta_okta-signin-widget/src/exports/cdn/oie.ts", "downloaded_repos/okta_okta-signin-widget/src/exports/classic.ts", "downloaded_repos/okta_okta-signin-widget/src/exports/default.ts", "downloaded_repos/okta_okta-signin-widget/src/exports/oie.ts", "downloaded_repos/okta_okta-signin-widget/src/models/Hooks.ts", "downloaded_repos/okta_okta-signin-widget/src/models/Settings.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginA11y.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginDebugger/console.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginDebugger/fetch.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginDebugger/index.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginDebugger/messages.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginDebugger/styles.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginDebugger/ui.ts", "downloaded_repos/okta_okta-signin-widget/src/plugins/OktaPluginDebugger/utils.ts", "downloaded_repos/okta_okta-signin-widget/src/router/classic.ts", "downloaded_repos/okta_okta-signin-widget/src/router/default.ts", "downloaded_repos/okta_okta-signin-widget/src/router/oie.ts", "downloaded_repos/okta_okta-signin-widget/src/types/api.ts", "downloaded_repos/okta_okta-signin-widget/src/types/appState.ts", "downloaded_repos/okta_okta-signin-widget/src/types/authClient.ts", "downloaded_repos/okta_okta-signin-widget/src/types/errors.ts", "downloaded_repos/okta_okta-signin-widget/src/types/events.ts", "downloaded_repos/okta_okta-signin-widget/src/types/global.d.ts", "downloaded_repos/okta_okta-signin-widget/src/types/index.ts", "downloaded_repos/okta_okta-signin-widget/src/types/options.ts", "downloaded_repos/okta_okta-signin-widget/src/types/registration.ts", "downloaded_repos/okta_okta-signin-widget/src/types/results.ts", "downloaded_repos/okta_okta-signin-widget/src/types/router.ts", "downloaded_repos/okta_okta-signin-widget/src/util/Animations.js", "downloaded_repos/okta_okta-signin-widget/src/util/BrowserFeatures.ts", "downloaded_repos/okta_okta-signin-widget/src/util/Bundles.ts", "downloaded_repos/okta_okta-signin-widget/src/util/ColorsUtil.js", "downloaded_repos/okta_okta-signin-widget/src/util/CookieUtil.js", "downloaded_repos/okta_okta-signin-widget/src/util/CountryUtil.js", "downloaded_repos/okta_okta-signin-widget/src/util/CryptoUtil.js", "downloaded_repos/okta_okta-signin-widget/src/util/Enums.js", "downloaded_repos/okta_okta-signin-widget/src/util/ErrorCodes.js", "downloaded_repos/okta_okta-signin-widget/src/util/Errors.ts", "downloaded_repos/okta_okta-signin-widget/src/util/FactorUtil.js", "downloaded_repos/okta_okta-signin-widget/src/util/FidoUtil.js", "downloaded_repos/okta_okta-signin-widget/src/util/Hooks.js", "downloaded_repos/okta_okta-signin-widget/src/util/IDP.js", "downloaded_repos/okta_okta-signin-widget/src/util/LanguageUtil.ts", "downloaded_repos/okta_okta-signin-widget/src/util/Logger.js", "downloaded_repos/okta_okta-signin-widget/src/util/OAuth2Util.js", "downloaded_repos/okta_okta-signin-widget/src/util/OAuthErrors.ts", "downloaded_repos/okta_okta-signin-widget/src/util/TimeUtil.js", "downloaded_repos/okta_okta-signin-widget/src/util/Util.js", "downloaded_repos/okta_okta-signin-widget/src/util/countryCallingCodes.js", "downloaded_repos/okta_okta-signin-widget/src/util/loc.ts", "downloaded_repos/okta_okta-signin-widget/src/util/webauthn.js", "downloaded_repos/okta_okta-signin-widget/src/v1/BaseLoginRouter.js", "downloaded_repos/okta_okta-signin-widget/src/v1/LoginRouter.ts", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/AccountUnlockedController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/ActivateTotpController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/AdminConsentRequiredController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/BarcodePushController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/BarcodeTotpController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/ConsentRequiredController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/CustomPasswordExpiredController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/DeviceActivateController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/DeviceActivateTerminalController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollActivateCustomFactorController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollActivateEmailController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollCallAndSmsController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollChoicesController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollCustomFactorController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollDuoController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollEmailController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollHotpController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollOnPremController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollPasswordController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollQuestionController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollSymantecVipController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollTotpController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollU2FController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollUserController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollWebauthnController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollWindowsHelloController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollYubikeyController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnrollmentLinkSentController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/EnterPasscodePushFlowController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/ErrorStateController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/ForceIDPDiscoveryController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/ForgotPasswordController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/IDPDiscoveryController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/ManualSetupPushController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/ManualSetupTotpController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/MfaVerifyController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/PasswordExpiredController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/PasswordResetController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/PollController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/PrimaryAuthController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/PwdResetEmailSentController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/RecoveryChallengeController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/RecoveryLoadingController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/RecoveryQuestionController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/RefreshAuthStateController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/RegistrationCompleteController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/RegistrationController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/UnlockAccountController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/UnlockEmailSentController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/VerifyCustomFactorController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/VerifyDuoController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/VerifyPIVController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/VerifyU2FController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/VerifyWebauthnController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/controllers/VerifyWindowsHelloController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/AppState.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/BaseLoginModel.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/EnrollUser.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/Factor.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/IDPDiscovery.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/LoginModel.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/PrimaryAuth.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/ProfileSchema.js", "downloaded_repos/okta_okta-signin-widget/src/v1/models/RegistrationSchema.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/BaseLoginController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/DeviceFingerprint.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/FormController.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/FormType.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/RegistrationFormFactory.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/RouterUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/StoreLinks.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/TypingUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v1/util/ValidationUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/ResendEmailView.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/admin-consent/ScopeItem.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/admin-consent/ScopeList.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/consent/ScopeCheckBox.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/consent/ScopeItem.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/consent/ScopeList.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-choices/FactorList.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-choices/Footer.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-choices/RequiredFactorList.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-factors/BarcodeView.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-factors/EnterPasscodeForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-factors/Footer.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-factors/ManualSetupFooter.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-factors/ManualSetupPushFooter.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enroll-factors/PhoneTextBox.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/enrollUser/EnrollUserForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/expired-password/Footer.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/factor-verify/EmailMagicLinkForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/idp-discovery/IDPDiscoveryForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/HtmlErrorMessageView.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/InlineTOTPForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/NumberChallengeView.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/PassCodeForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/PasswordForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/PushForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/SecurityQuestionForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/SendEmailAndVerifyCodeForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/TOTPForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/YubikeyForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/dropdown/FactorsDropDown.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/mfa-verify/dropdown/FactorsDropDownOptions.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/primary-auth/CustomButtons.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/primary-auth/PrimaryAuthForm.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/registration/SubSchema.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/AuthContainer.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/ContactSupport.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/FactorBeacon.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/Footer.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/FooterMFA.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/FooterRegistration.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/FooterSignout.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/FooterWithBackLink.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/FooterWithBackLinkOnError.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/Header.ts", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/LoadingBeacon.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/PIVBeacon.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/PasswordRequirements.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/SecurityBeacon.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/SkipLink.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/Spinner.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/TextBox.js", "downloaded_repos/okta_okta-signin-widget/src/v1/views/shared/templates/consentLogoHeaderTemplate.js", "downloaded_repos/okta_okta-signin-widget/src/v2/BaseLoginRouter.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/WidgetRouter.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/client/constants.js", "downloaded_repos/okta_okta-signin-widget/src/v2/client/emailVerifyCallback.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/client/formatError.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/client/handleConfiguredFlow.js", "downloaded_repos/okta_okta-signin-widget/src/v2/client/index.js", "downloaded_repos/okta_okta-signin-widget/src/v2/client/interactionCodeFlow.js", "downloaded_repos/okta_okta-signin-widget/src/v2/client/sessionStorageHelper.js", "downloaded_repos/okta_okta-signin-widget/src/v2/client/startLoginFlow.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/client/updateAppState.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/controllers/FormController.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/IonResponseHelper.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/RemediationConstants.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/ViewClassNamesFactory.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/i18nTransformer.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/i18nUtils.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/payloadTransformer.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/responseTransformer.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/transformIdxResponse.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/ui-schema/ion-boolean-handler.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/ui-schema/ion-object-handler.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/ui-schema/ion-string-handler.js", "downloaded_repos/okta_okta-signin-widget/src/v2/ion/uiSchemaTransformer.js", "downloaded_repos/okta_okta-signin-widget/src/v2/mixins/mixins.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/models/AppState.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/ViewFactory.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/AuthenticatorEnrollOptions.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/AuthenticatorEnrollOptionsContainer.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/AuthenticatorFooter.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/AuthenticatorVerifyOptions.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/BaseAuthenticatorView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/ChallengeWebauthnFooter.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/EmailAuthenticatorHeader.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/HeaderBeacon.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/IdentifierFooter.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/Link.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/MdmOktaVerifyTerminalView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/OdaOktaVerifyTerminalView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/OktaVerifyAuthenticatorHeader.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/RemindMeLaterButton.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/ScopeCheckBox.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/SkipOptionalEnrollmentButton.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/components/ToggleTextLink.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/BaseFooter.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/BaseForm.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/BaseFormWithPolling.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/BaseHeader.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/BaseModel.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/BaseOktaVerifyChallengeView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/BaseView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/FormInputFactory.ts", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/internals/index.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/AuthenticatorUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/ChallengeViewUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/Constants.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/DeviceFingerprinting.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/EndUserRemediationMessageViewUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/IdpUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/LinksUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/utils/TimeZone.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/AutoRedirectView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/EnrollProfileUpdateView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/EnrollProfileView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/IdentifierView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/IdentifyRecoveryView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/PollView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/RedirectIdPView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/SelectAuthenticatorEnrollView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/SelectAuthenticatorVerifyView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/TerminalView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/activation/RequestActivationEmailView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/authenticator/SelectAuthenticatorUnlockAccountView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/authenticator/UnlockAccountView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/captcha/CaptchaView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/AdminConsentView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/AdminConsentViewHeader.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/ConsentViewForm.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/EmailMagicLinkOTPTerminalView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/EnduserConsentAgreementText.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/EnduserConsentView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/EnduserConsentViewFooter.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/EnduserConsentViewHeader.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/EnduserEmailConsentView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/consent/GranularConsentView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/custom-app/ChallengeCustomAppResendPushView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/custom-otp/ChallengeCustomOTPAuthenticatorView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/custom-password/ReEnrollCustomPasswordExpiryView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/custom-password/ReEnrollCustomPasswordExpiryWarningView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/device/DeviceChallengePollView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/device/DeviceCodeActivateView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/device/DeviceEnrollmentTerminalView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/device/SSOExtensionView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/device/SignInDeviceView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/device-assurance-grace-period/DeviceAssuranceGracePeriodView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/duo/BaseDuoAuthenticatorForm.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/duo/ChallengeDuoAuthenticatorView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/duo/EnrollDuoAuthenticatorView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/email/AuthenticatorEmailViewUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/email/BaseAuthenticatorEmailView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/email/ChallengeAuthenticatorDataEmailView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/email/ChallengeAuthenticatorEmailView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/email/EnrollAuthenticatorEmailView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/end-user-remediation/EndUserRemediationMessages.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/enroll-profile/i18nBaseAttributeMappings.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/google-authenticator/ChallengeGoogleAuthenticatorView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/google-authenticator/EnrollAuthenticatorGoogleAuthenticatorView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/google-authenticator/EnrollAuthenticatorManualSetupView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/google-authenticator/EnrollGoogleAuthenticatorBarcodeView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/idp/AuthenticatorIdPEnrollView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/idp/AuthenticatorIdPVerifyView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/idp/BaseIdpAuthenticator.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/idp/RedirectIdvView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/keep-me-signed-in/PostAuthKeepMeSignedInView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/on-prem/ChallengeAuthenticatorOnPremView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/on-prem/EnrollAuthenticatorOnPremView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/ChallengeOktaVerifyFastPassView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/ChallengeOktaVerifyResendPushView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/ChallengeOktaVerifySSOExtensionView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/ChallengeOktaVerifyTotpView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/ChallengeOktaVerifyView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/EnrollChannelPollDescriptionView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/EnrollPollOktaVerifyView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/EnrollementChannelDataOktaVerifyView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/NumberChallengePhoneView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/NumberChallengePushView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/OVResendView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/ResendNumberChallengeView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/SameDeviceEnrollLink.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/SelectEnrollmentChannelOktaVerifyView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/ov/SwitchEnrollChannelLinkView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/password/ChallengeAuthenticatorPasswordView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/password/EnrollAuthenticatorPasswordView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/password/PasswordPolicyUtil.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/password/ReEnrollAuthenticatorPasswordView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/password/ReEnrollAuthenticatorWarningPasswordView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/password/ResetAuthenticatorPasswordView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/phone/ChallengeAuthenticatorDataPhoneView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/phone/ChallengeAuthenticatorPhoneView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/phone/EnrollAuthenticatorDataPhoneView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/phone/EnrollAuthenticatorPhoneView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/piv/ChallengePIVView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/piv/PIVHeader.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/security-question/ChallengeAuthenticatorSecurityQuestion.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/security-question/EnrollAuthenticatorSecurityQuestionView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/BaseResendView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/ChallengeOktaVerifyCustomAppDataView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/ChallengeOktaVerifyCustomAppFormView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/ChallengeOktaVerifyCustomAppPushOnlyFormView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/ChallengePushView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/CustomAccessDeniedErrorMessage.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/TODO.md", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/email.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/shared/polling.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/signin/CustomButtons.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/signin/SignInWithDeviceOption.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/signin/SignInWithIdps.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/signin/SignInWithWebAuthn.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/symantec/AuthenticatorSymantecView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/tac/ChallengeAuthenticatorTacView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/webauthn/ChallengeWebauthnInfoView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/webauthn/ChallengeWebauthnView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/webauthn/EnrollWebAuthnResidentkeyLinkView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/webauthn/EnrollWebauthnInfoView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/webauthn/EnrollWebauthnView.js", "downloaded_repos/okta_okta-signin-widget/src/v2/view-builder/views/yubikey/AuthenticatorYubiKeyView.js", "downloaded_repos/okta_okta-signin-widget/src/v3/.editorconfig", "downloaded_repos/okta_okta-signin-widget/src/v3/.eslintignore", "downloaded_repos/okta_okta-signin-widget/src/v3/.eslintrc.js", "downloaded_repos/okta_okta-signin-widget/src/v3/.gitignore", "downloaded_repos/okta_okta-signin-widget/src/v3/.npmignore", "downloaded_repos/okta_okta-signin-widget/src/v3/.postcssrc.js", "downloaded_repos/okta_okta-signin-widget/src/v3/Makefile", "downloaded_repos/okta_okta-signin-widget/src/v3/README.md", "downloaded_repos/okta_okta-signin-widget/src/v3/config/header.js", "downloaded_repos/okta_okta-signin-widget/src/v3/config/jsdom-env-with-polyfills.js", "downloaded_repos/okta_okta-signin-widget/src/v3/global.d.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/jest.config.js", "downloaded_repos/okta_okta-signin-widget/src/v3/jest.setup.js", "downloaded_repos/okta_okta-signin-widget/src/v3/licenses.txt", "downloaded_repos/okta_okta-signin-widget/src/v3/package.json", "downloaded_repos/okta_okta-signin-widget/src/v3/screenshots/base/UI_demo/UI_demo_RTL_VRT.png", "downloaded_repos/okta_okta-signin-widget/src/v3/screenshots/base/UI_demo/UI_demo_VRT.png", "downloaded_repos/okta_okta-signin-widget/src/v3/scripts/buildtools/lint-report.sh", "downloaded_repos/okta_okta-signin-widget/src/v3/src/OktaSignIn/index.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/OktaSignIn/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/__mocks__/duo_web_sdk/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/bin/properties-to-json.js", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ActionPending/ActionPending.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthCoin/AuthCoin.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthCoin/AuthCoin.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthCoin/__snapshots__/AuthCoin.test.tsx.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthCoin/authCoinConfigUtil.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthContainer/AuthContainer.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthContent/AuthContent.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthHeader/AuthHeader.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthHeader/AuthHeader.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthenticatorButton/AuthenticatorButton.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthenticatorButton/AuthenticatorButtonList.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AuthenticatorButton/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AutoSubmit/AutoSubmit.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/AutoSubmit/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Button/Button.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Button/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/CaptchaContainer/CaptchaContainer.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/CaptchaContainer/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Checkbox/Checkbox.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Checkbox/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ChromeDtcContainer/ChromeDtcContainer.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ChromeDtcContainer/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ConsentHeader/ConsentHeader.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ConsentHeader/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Divider/Divider.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Divider/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/DuoWindow/DuoWindow.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/DuoWindow/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/Accordion.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/ElementContainer.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/Form.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/Layout.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/LayoutContainer.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/Stepper.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Form/renderers.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Heading/Heading.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Heading/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/HiddenIFrame/HiddenIFrame.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/HiddenIFrame/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/HiddenInput/HiddenInput.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/HiddenInput/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/AppleStoreIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/ClearIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/CustomAppIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/CustomOTPIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/DuoIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/EmailIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/GoogleOTPIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/GoogleStoreIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/IDPIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/IncodeIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/OSXStoreIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/OktaVerifyIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/OnPremMFAIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/PasswordIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/PersonaIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/PhoneIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/RSAIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/SecurityKeyOrBiometricsIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/SecurityQuestionIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/SmartCardIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/SymantecIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/WindowsStoreIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/YubiKeyIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Icon/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/IdentifierContainer/IdentifierContainer.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/IdentifierContainer/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Image/Image.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Image/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ImageLink/ImageLink.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ImageLink/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ImageWithText/ImageWithText.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ImageWithText/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Images/AppIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Images/DeviceIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Images/LocationIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Images/PhoneIcon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Images/YubikeyDemoImage.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Images/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InfoBox/InfoBox.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InfoBox/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InfoSection/InfoSection.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InformationalText/InformationalText.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InformationalText/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InputPassword/InputPassword.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InputPassword/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InputText/InputText.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InputText/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InputTextWithValidation/InputTextWithValidation.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/InputTextWithValidation/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/LaunchAuthenticatorButton/LaunchAuthenticatorButton.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/LaunchAuthenticatorButton/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Link/Link.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Link/Link.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Link/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/List/List.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/List/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/LoopbackProbe/LoopbackProbe.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/LoopbackProbe/LoopbackProbe.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/LoopbackProbe/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/OpenOktaVerifyFPButton/OpenOktaVerifyFPButton.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/OpenOktaVerifyFPButton/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PIVButton/PIVButton.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PIVButton/PIVButton.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PIVButton/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PasswordRequirements/Icon.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PasswordRequirements/PasswordMatches.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PasswordRequirements/PasswordRequirementListItem.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PasswordRequirements/PasswordRequirements.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PasswordRequirements/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PhoneAuthenticator/PhoneAuthenticator.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PhoneAuthenticator/PhoneAuthenticator.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PhoneAuthenticator/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/QRCode/QRCode.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/QRCode/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Radio/Radio.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Radio/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Redirect/Redirect.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Redirect/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ReminderPrompt/ReminderPrompt.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ReminderPrompt/ReminderPrompt.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ReminderPrompt/__snapshots__/ReminderPrompt.test.tsx.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/ReminderPrompt/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Select/Select.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Select/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Spinner/Spinner.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Spinner/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperButton/StepperButton.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperButton/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperLink/StepperLink.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperLink/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperNavigator/StepperNavigator.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperNavigator/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperRadio/StepperRadio.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/StepperRadio/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/TextWithActionLink/TextWithActionLink.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/TextWithActionLink/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Title/Title.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Title/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WebAuthNAutofill/WebAuthNAutofill.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WebAuthNAutofill/WebAuthNAutofill.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WebAuthNAutofill/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WebAuthNSubmitButton/WebAuthNSubmitButton.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WebAuthNSubmitButton/WebAuthNSubmitButton.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WebAuthNSubmitButton/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Widget/GlobalStyles.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Widget/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Widget/style.scss", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WidgetMessageContainer/WidgetMessageContainer.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/WidgetMessageContainer/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/hocs/getDisplayName.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/hocs/index.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/hocs/withFormValidationState.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/hocs/withFormValidationState.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/constants/idpConstants.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/constants/idxConstants.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/constants/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/constants/passwordConstants.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/constants/webAuthNConstants.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/contexts.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/global.d.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useAutoFocus.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useFormFieldValidation.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useInteractionCodeFlow.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useInteractionCodeFlow.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useOnChange.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useOnSubmit.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useOnSubmit.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useOnSubmitValidation.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useOnce.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/usePolling.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/usePolling.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useStateHandle.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useValue.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/smartCardButtonIcon.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/adobe.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/amazon.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/apple.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/discord.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/facebook.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/github.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/gitlab.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/google.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/line.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/linkedin.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/ms.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/okta.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/orcid.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/paypal.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/quickbooks.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/salesforce.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/spotify.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/xero.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/yahoo-japan.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/img/socialIcons/yahoo.svg", "downloaded_repos/okta_okta-signin-widget/src/v3/src/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/manifest.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/.eslintrc.js", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/authenticatorList/authenticator-list.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/browser.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/registry.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-enroll-select-authenticator-with-skip.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-enroll-select-authenticator.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-enroll-webauthn-userverification-required.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-enrollment-not-allowed.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-password-reset-revoke-sessions.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-piv-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-select-piv.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-verification-ov-resend-push-notification.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-verification-select-authenticator.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-verification-select-ov-code-and-push.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticator-verification-unlock-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticators/okta-verify/launch/device-challenge-poll-custom-uri.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/authenticators/poll/verify-ov-push-manual-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/cancel/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/authenticator-enroll-password-requirements-not-met.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/authenticator-expired-password.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/enroll-okta-verify-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/enroll-password-remaining.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/enroll-phone-sms-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/enroll-security-question-with-character-limit-error.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/enroll-sms-remaining.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/error-password-reset-failed.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/google-auth-scan-enroll.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/google-auth-verify-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/google-auth-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/password-reset-email.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/password-reset.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/securityquestion-enroll-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-email-additional-verification.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-email-verify-webauthn-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-email-verify-webauthn.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-google-auth-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-sms-ga-verify-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-sms-ga-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-sms-verify-webauthn-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-sms-verify-webauthn.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/unlock-account-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/webauthn-enroll-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/answer/webauthn-verify-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/authenticator-verification-email-magic-link-false.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/error-401-invalid-otp-passcode.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/google-auth-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/password-reset-email-challenge.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/password-reset-email-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/poll/enroll-ov-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/send/enroll-ov-email-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/send/enroll-ov-sms-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/sms-challenge.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/sms-method.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/unlock-account-email-verify-webauthn.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/unlock-account-email.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/unlock-account-google-auth-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/unlock-account-sms-verify-webauthn.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/unlock-account-sms-with-ga-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/unlock-account-sms.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/verify-ov-code-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/verify-ov-push-code-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/verify-ov-push-manual.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/verify-ov-push-method.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/verify-ov-push-poll.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/verify-ov-select-method.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/challenge/verify-ov-send-push.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/consent/denied.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/consent/success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/enroll-okta-verify-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/enroll-ov-email-channel.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/enroll-ov-qr-version-upgrade.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/enroll-ov-sms-channel.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/enroll-phone-sms-code-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/enroll-phone-voice-code-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/enroll-phone-voice-sms-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/google-auth-scan-enroll.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/request-email-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/request-password.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/request-phone.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/request-sms-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/securityquestion-enroll-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/credential/enroll/webauthn-enroll-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/enroll/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/enroll/enroll-profile-with-password-full-requirements.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/enroll/new/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/enroll/new/error-account-creation.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/enroll/new/error-request-not-completed.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/authenticator-expired-password-no-complexity.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/authenticator-expired-password-with-enrollment-authenticator.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/authenticator-expired-password.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/authenticator-expiry-warning-password.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/authenticator-verification-data-with-email.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/authenticator-verification-password.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/enroll-phone-voice-sms-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/error-invalid-username.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/error-session-expired.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/error-wrong-password.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/google-auth-scan-enroll.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/google-auth-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/password-reset.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/securityquestion-custom-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/securityquestion-enroll-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/securityquestion-verify.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/select/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/unlock-account.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/verify-ov-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/webauthn-enroll-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/identify/webauthn-verify-mfa.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/all-enabled-features.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/consent-granular.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/email-challenge-consent.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/enroll-phone.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/error-403-security-access-denied.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/identify-with-piv-cac.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/identify-with-username.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/identify-with-webauthn-autofill.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-android-applink.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-android-loopback.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/oda-enrollment-ios.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/select-authenticator.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-enduser-email-consent-denied.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-invalid-forgot-password-token.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-invalid-reset-password-token.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-registration.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-reset-password-inactive-user.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-reset-password-success.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-email-consent-denied.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-email-error.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-email.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-expired-email.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-otp-only-full-location-enrollment.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-otp-only-full-location-recovery.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-otp-only-full-location-unlock.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-otp-only-full-location.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-otp-only-no-location.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-otp-only-partial-location.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-return-stale-email.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/introspect/terminal-transferred-email.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/recover/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/idp/idx/unlock-account/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/interact/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/interact/error-400-unauthorized-client.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/interact/error-activation-token-invalid.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/interact/error-feature-not-enabled.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/interact/error-recovery-token-invalid.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/keys/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/v1/token/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/response/oauth2/default/well-known/openid-configuration/default.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/auth-with-securityquestion.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-expired-password-no-complexity.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-expired-password-with-enrollment-authenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-expired-password.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-expiry-warning-password.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-piv-cac-verification.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-verification-email-magiclink-false.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-verification-email.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-verification-google-authenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-verification-password.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/authenticator-verification-phone-sms.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/development.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/email-challenge-consent.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/enroll-okta-verify-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/enroll-ov-version-upgrade.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/enroll-phone-multi-options-sms-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/enroll-phone-multi-options-voice-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-400-unauthorized-client.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-401-invalid-otp-passcode.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-403-security-access-denied.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-account-creation.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-activation-token-invalid.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-feature-not-enabled.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-password-reset-failed.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-recovery-token-invalid.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-request-not-completed.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/error-session-expired.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/google-auth-scan-enroll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/granular-consent.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/identify-with-username-enrollment-not-allowed-error.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/identify-with-username.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/piv-as-authenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/register-with-email-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/remove-security-question-challenge-flow.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/resetpassword-email-google-auth-verify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/resetpassword-email-securityquestion.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/securityquestion-enroll-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/select-authenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-enduser-email-consent-denied.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-invalid-forgot-password-token.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-invalid-reset-password-token.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-registration.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-reset-password-inactive-user.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-reset-password-success.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-email-error.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-email.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-expired-email.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-otp-only-full-location-enrollment.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-otp-only-full-location-recovery.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-otp-only-full-location-unlock.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-otp-only-full-location.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-otp-only-no-location.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-otp-only-partial-location.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-return-stale-email.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/terminal-transferred-email.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/unlock-account-email-poll-with-webauthn-challenge.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/unlock-account-email-securityquestion.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/unlock-account-email-verify-with-ga.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/unlock-account-email-verify-with-webauthn.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/unlock-account-sms-verify-with-ga.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/unlock-account-sms-verify-with-webauthn.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/unlock-account-sms.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/verify-ov-code-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/verify-ov-push-code-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/verify-ov-push-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/webauthn-enroll-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/scenario/webauthn-verify-mfa.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/server.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/transformationExamples/idxChallengeDefault.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/mocks/utils/utils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/services/validationService.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/__snapshots__/transformIDPButtons.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transform.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformCancelButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformFactorPageCustomLink.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformForgotPasswordButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformHelpLinks.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformIDPButtons.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformIDPButtons.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformRegisterButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformReturnToAuthenticatorListButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformSubmitButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformUnlockAccountButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/button/transformVerifyWithOtherButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/captcha/__snapshots__/transformCaptcha.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/captcha/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/captcha/transformCaptcha.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/captcha/transformCaptcha.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/dataSchema/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/dataSchema/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/field/attributes.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/field/attributes.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/field/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/field/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/field/type.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/field/type.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/__snapshots__/transformAuthenticatorButton.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/__snapshots__/transformField.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/__snapshots__/transformGranularConsentFields.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/isCustomizedI18nKey.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/isCustomizedI18nKey.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transactionMessageTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transform.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformAuthenticatorButton.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformAuthenticatorButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformDefaultSelectOptionLabel.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformField.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformField.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformGranularConsentFields.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformGranularConsentFields.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformIdentifierHint.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformInputPassword.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformLaunchAuthenticatorButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformOpenOktaVerifyFPButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformPasscodeHint.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformPasswordMatches.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformPhoneAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformQRCode.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformSecondEmailInputExplain.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/transformWebAuthNSubmitButton.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/i18n/util.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/identify/__snapshots__/transformIdentify.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/identify/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/identify/transformIdentify.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/identify/transformIdentify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/__snapshots__/transformAdminConsent.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/__snapshots__/transformEnduserConsent.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/__snapshots__/transformGranularConsent.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/transformAdminConsent.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/transformAdminConsent.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/transformEnduserConsent.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/transformEnduserConsent.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/transformGranularConsent.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/consent/transformGranularConsent.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/development/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/development/transformEnumerateComponents.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/device/__snapshots__/transformDeviceCodeAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/device/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/device/transformDeviceCodeAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/device/transformDeviceCodeAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/deviceAssuranceGracePeriod/__snapshots__/transformDeviceAssuranceGracePeriod.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/deviceAssuranceGracePeriod/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/deviceAssuranceGracePeriod/transformDeviceAssuranceGracePeriod.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/deviceAssuranceGracePeriod/transformDeviceAssuranceGracePeriod.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/duo/__snapshots__/transformDuoAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/duo/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/duo/transformDuoAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/duo/transformDuoAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/__snapshots__/emailChallengeConsentTransformer.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/__snapshots__/emailVerificationTransformer.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/__snapshots__/transformEmailAuthenticatorEnroll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/__snapshots__/transformEmailAuthenticatorVerify.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/emailChallengeConsentTransformer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/emailChallengeConsentTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/emailVerificationTransformer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/emailVerificationTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/getEmailAuthenticatorSubtitle.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/getEmailAuthenticatorSubtitle.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/transformEmailAuthenticatorEnroll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/transformEmailAuthenticatorEnroll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/transformEmailAuthenticatorVerify.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/transformEmailAuthenticatorVerify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/googleAuthenticator/__snapshots__/transformGoogleAuthenticatorEnroll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/googleAuthenticator/__snapshots__/transformGoogleAuthenticatorVerify.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/googleAuthenticator/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/googleAuthenticator/transformGoogleAuthenticatorEnroll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/googleAuthenticator/transformGoogleAuthenticatorEnroll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/googleAuthenticator/transformGoogleAuthenticatorVerify.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/googleAuthenticator/transformGoogleAuthenticatorVerify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/__snapshots__/transformIdpAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/__snapshots__/transformIdpRedirect.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/__snapshots__/transformIdvIdpAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/transformIdpAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/transformIdpAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/transformIdpRedirect.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/transformIdpRedirect.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/transformIdvIdpAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idp/transformIdvIdpAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/idxTransformerMapping.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/keepMeSignedIn/__snapshots__/transformKeepMeSignedIn.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/keepMeSignedIn/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/keepMeSignedIn/transformKeepMeSignedIn.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/keepMeSignedIn/transformKeepMeSignedIn.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/__snapshots__/transformOktaVerifyDeviceChallengePoll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/__snapshots__/transformOktaVerifyFPLaunchAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/__snapshots__/transformOktaVerifyFPLoopbackPoll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/transformOktaVerifyDeviceChallengePoll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/transformOktaVerifyDeviceChallengePoll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/transformOktaVerifyFPLaunchAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/transformOktaVerifyFPLaunchAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/transformOktaVerifyFPLoopbackPoll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/oktaVerify/transformOktaVerifyFPLoopbackPoll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/otp/__snapshots__/transformCustomOtpAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/otp/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/otp/transformCustomOtpAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/otp/transformCustomOtpAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/piv/__snapshots__/transformPIVAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/piv/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/piv/transformPIVAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/piv/transformPIVAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/recovery/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/recovery/transformIdentityRecovery.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/recovery/transformIdentityRecovery.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/recovery/transformRequestActivation.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/recovery/transformRequestActivation.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/rsa/__snapshots__/transformRSAAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/rsa/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/rsa/transformRSAAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/rsa/transformRSAAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/safeMode/__snapshots__/transformSafeModePoll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/safeMode/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/safeMode/transformSafeModePoll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/safeMode/transformSafeModePoll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/securityQuestion/__snapshots__/securityQuestionEnroll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/securityQuestion/__snapshots__/securityQuestionVerify.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/securityQuestion/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/securityQuestion/securityQuestionEnroll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/securityQuestion/securityQuestionEnroll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/securityQuestion/securityQuestionVerify.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/securityQuestion/securityQuestionVerify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/symantecVip/__snapshots__/transformSymantecVipAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/symantecVip/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/symantecVip/transformSymantecVipAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/symantecVip/transformSymantecVipAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/tac/__snapshots__/transformTacAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/tac/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/tac/transformTacAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/tac/transformTacAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/unlockAccount/__snapshots__/transformUnlockAccount.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/unlockAccount/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/unlockAccount/transformUnlockAccount.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/unlockAccount/transformUnlockAccount.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/main.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/main.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/messages/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/messages/transform.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/messages/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/__snapshots__/transformAppleSsoExtension.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/__snapshots__/transformOktaVerifyChannelSelection.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/__snapshots__/transformOktaVerifyCustomAppChallengePoll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/__snapshots__/transformOktaVerifyCustomAppResendPush.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/__snapshots__/transformOktaVerifyEnrollChannel.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/__snapshots__/transformOktaVerifyEnrollPoll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/__snapshots__/transformTOTPChallenge.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformAppleSsoExtension.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformAppleSsoExtension.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyChannelSelection.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyChannelSelection.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyCustomAppChallengePoll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyCustomAppChallengePoll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyCustomAppResendPush.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyCustomAppResendPush.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyEnrollChannel.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyEnrollChannel.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyEnrollPoll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyEnrollPoll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformTOTPChallenge.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformTOTPChallenge.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/__snapshots__/transformEnrollPasswordAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/__snapshots__/transformExpiredCustomPassword.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/__snapshots__/transformExpiredCustomPasswordWarning.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/__snapshots__/transformExpiredPasswordAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/__snapshots__/transformExpiredPasswordWarningAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/__snapshots__/transformPasswordChallenge.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/__snapshots__/transformResetPasswordAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/passwordSettingsUtils.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/passwordSettingsUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformEnrollPasswordAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformEnrollPasswordAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredCustomPassword.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredCustomPassword.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredCustomPasswordWarning.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredCustomPasswordWarning.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredPasswordAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredPasswordAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredPasswordWarningAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformExpiredPasswordWarningAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformPasswordChallenge.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformPasswordChallenge.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformResetPasswordAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/password/transformResetPasswordAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/__snapshots__/phoneChallengeTransformer.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/__snapshots__/phoneEnrollmentCodeTransformer.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/__snapshots__/phoneEnrollmentTransformer.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/__snapshots__/phoneVerificationTransformer.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneChallengeTransformer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneChallengeTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneEnrollmentCodeTransformer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneEnrollmentCodeTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneEnrollmentTransformer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneEnrollmentTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneVerificationTransformer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/phone/phoneVerificationTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/profile/__snapshots__/transformEnrollProfile.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/profile/__snapshots__/transformEnrollProfileUpdate.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/profile/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/profile/transformEnrollProfile.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/profile/transformEnrollProfile.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/profile/transformEnrollProfileUpdate.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/profile/transformEnrollProfileUpdate.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/redirect/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/redirect/redirectTransformer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/redirect/redirectTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/redirect/transformAutoRedirect.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/__snapshots__/transformSelectAuthenticatorEnroll.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/__snapshots__/transformSelectAuthenticatorUnlockVerify.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/__snapshots__/transformSelectAuthenticatorVerify.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/__snapshots__/transformSelectOVCustomAppMethodVerify.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/__snapshots__/utils.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectAuthenticatorEnroll.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectAuthenticatorEnroll.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectAuthenticatorUnlockVerify.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectAuthenticatorUnlockVerify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectAuthenticatorVerify.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectAuthenticatorVerify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectOVCustomAppMethodVerify.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectOVCustomAppMethodVerify.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/utils.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/utils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/__snapshots__/transformEmailMagicLinkOTPOnlyElements.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/__snapshots__/transformMdmTerminalView.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/__snapshots__/transformTerminalMessages.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/__snapshots__/transformTerminalTransaction.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/__snapshots__/transformUnhandledErrors.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/__snapshots__/transformOdaEnrollmentAndroidAppLink.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/__snapshots__/transformOdaEnrollmentLoopback.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/transformOdaEnrollment.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/transformOdaEnrollment.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/transformOdaEnrollmentAndroidAppLink.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/transformOdaEnrollmentAndroidAppLink.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/transformOdaEnrollmentLoopback.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/odaEnrollment/transformOdaEnrollmentLoopback.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformEmailMagicLinkOTPOnlyElements.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformEmailMagicLinkOTPOnlyElements.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformMdmTerminalView.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformMdmTerminalView.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformTerminalMessages.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformTerminalMessages.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformTerminalTransaction.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformTerminalTransaction.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformUnhandledErrors.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/terminal/transformUnhandledErrors.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/testAttribute/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/testAttribute/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/transaction/__snapshots__/transform.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/transaction/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/transaction/transform.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/transaction/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/__snapshots__/createIdentifierContainer.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/__snapshots__/overwriteAutocomplete.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/__snapshots__/updateElementKeys.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/__snapshots__/updatePasswordDescribedByValue.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/addIdToElements.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/addKeyToElement.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/addWebAuthNAutofillHandler.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/addWebAuthNAutofillHandler.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/applyAsteriskToFieldElements.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/createIdentifierContainer.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/createIdentifierContainer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/createTextElementKeys.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/overwriteAutocomplete.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/overwriteAutocomplete.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/setFocusOnFirstElement.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/transform.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/transform.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/updateCustomFields.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/updateElementKeys.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/updateElementKeys.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/updatePasswordDescribedByValue.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/uischema/updatePasswordDescribedByValue.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/util/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/util/traverseLayout.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/utils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/webauthn/__snapshots__/transformWebAuthNAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/webauthn/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/webauthn/transformWebAuthNAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/webauthn/transformWebAuthNAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/yubikey/__snapshots__/transformYubikeyOtpAuthenticator.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/yubikey/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/yubikey/transformYubikeyOtpAuthenticator.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/yubikey/transformYubikeyOtpAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/tsconfig.json", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/.eslintrc.js", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/api.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/appInfo.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/authcoin.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/component.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/context.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/error.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/handlers.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/hooks.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/image.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/ion.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/json.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/jsonforms.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/password.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/schema.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/stepTransformer.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/userInfo.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/types/widget.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/__snapshots__/idxUtils.test.ts.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/browserUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/buildErrorMessageIds.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/buildFieldLevelErrorMessages.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/buildPasswordRequirementNotMetErrorList.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/clipboard.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/configuredFlowUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/cookieUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/cssInterpolate.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/deviceFingerprintingUtils.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/deviceFingerprintingUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/environmentUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/escape.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/escape.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/extractPageTitle.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/extractPageTitle.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/flattenInputs.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/flattenInputs.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/formUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/formatError.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/generateRandomString.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getAuthenticatorKey.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getAuthenticatorMethod.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getCurrentAuthenticator.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getElementKey.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getEventContext.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getImmutableData.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getLanguageDirection.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getPasswordExpiryContentTitleAndParams.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getTranslation.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/getValidationMessages.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/htmlContentParserUtils.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/idpIconMap.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/idxUtils.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/idxUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/inflect.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/inflect.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/isInteractiveElement.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/isLtrField.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/isPasswordRecovery.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/isPollingStep.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/languageUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/locUtil.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/locUtil.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/makeRequest.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/mergeThemes.test.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/mergeThemes.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/passwordUtils.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/passwordUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/probeLoopbackAndExecute.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/punctuate.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/punctuate.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/removeFieldLevelMessages.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/rendererUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/resetMessagesToInputs.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/sessionStorage.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/setUrlQueryParams.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/settingsUtils.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/settingsUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/shouldShowCancelLink.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/stylisPlugins.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/theme.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/theme.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/toNestedObject.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/toNestedObject.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/webauthnUtils.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/webauthnUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/widgetHooks.test.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/widgetHooks.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/widgetUtils.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/src/widget.d.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylelint.config.js", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/.eslintrc.js", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/README.md", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/jest.config.js", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/__tests__/emotion/__snapshots__/emotion.tsx.snap", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/__tests__/emotion/emotion.tsx", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/__tests__/plugin.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/constants.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/plugin.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/testUtils/index.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/utils/transformHelpers.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/src/utils/transforms.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/stylis.d.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/stylis-logical-plugin/tsconfig.json", "downloaded_repos/okta_okta-signin-widget/src/v3/svgMockTransformer.js", "downloaded_repos/okta_okta-signin-widget/src/v3/svgo-plugins/addClassNamesByAttr.js", "downloaded_repos/okta_okta-signin-widget/src/v3/svgo-plugins/addClassNamesByAttr.test.js", "downloaded_repos/okta_okta-signin-widget/src/v3/svgo.config.js", "downloaded_repos/okta_okta-signin-widget/src/v3/tsconfig.base.json", "downloaded_repos/okta_okta-signin-widget/src/v3/tsconfig.node.json", "downloaded_repos/okta_okta-signin-widget/src/v3/webpack.common.config.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/webpack.dev.config.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/webpack.release.config.ts", "downloaded_repos/okta_okta-signin-widget/src/v3/yarn.lock", "downloaded_repos/okta_okta-signin-widget/src/widget/OktaSignIn.ts", "downloaded_repos/okta_okta-signin-widget/src/widget/buildRenderOptions.js", "downloaded_repos/okta_okta-signin-widget/src/widget/copyright.txt", "downloaded_repos/okta_okta-signin-widget/src/widget/createRouter.js", "downloaded_repos/okta_okta-signin-widget/src/widget/getAuthClient.ts", "downloaded_repos/okta_okta-signin-widget/stylelint-local-rules.js", "downloaded_repos/okta_okta-signin-widget/stylelint.config.js", "downloaded_repos/okta_okta-signin-widget/tsconfig.json", "downloaded_repos/okta_okta-signin-widget/types/.eslintrc.js", "downloaded_repos/okta_okta-signin-widget/types/README.md", "downloaded_repos/okta_okta-signin-widget/types/tsconfig.json", "downloaded_repos/okta_okta-signin-widget/vrtUtil/ImageDiff.js", "downloaded_repos/okta_okta-signin-widget/vrtUtil/vrtUtil.js", "downloaded_repos/okta_okta-signin-widget/webpack.common.config.js", "downloaded_repos/okta_okta-signin-widget/webpack.dev.config.js", "downloaded_repos/okta_okta-signin-widget/webpack.playground.config.js", "downloaded_repos/okta_okta-signin-widget/webpack.release.config.js", "downloaded_repos/okta_okta-signin-widget/webpack.test.config.js"], "skipped": [{"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/base.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/decorators/inline.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/decorators.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/exception.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers/block-helper-missing.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers/each.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers/helper-missing.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers/if.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers/log.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers/lookup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers/with.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/helpers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/internal/create-new-lookup-object.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/internal/proto-access.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/internal/wrapHelper.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/logger.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/no-conflict.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/runtime.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/safe-string.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars/utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/handlebars/dist/cjs/handlebars.runtime.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/vendor/lib/backbone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/vendor/lib/js.cookie.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/vendor/plugins/chosen.jquery.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/vendor/plugins/jquery.custominput.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/vendor/plugins/jquery.placeholder.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/vendor/lib/js.cookie.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/vendor/plugins/chosen.jquery.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/vendor/plugins/jquery.custominput.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/vendor/plugins/jquery.placeholder.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/types/courage/vendor/plugins/jquery.simplemodal.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/dist/jquery.qtip.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/dist/jquery.qtip.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/dist/jquery.qtip.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/dist/jquery.qtip.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/@okta/qtip2/dist/jquery.qtip.min.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/vendor/TypingDnaRecorder-JavaScript/typingdna.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/packages/vendor/duo_web_sdk/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/playground/mocks/data/idp/idx/error-empty-response.json", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/.testcaferc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-email-verification-data.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-data-phone.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-google-authenticator-challenge.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-google-authenticator-qrcode.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-google-authenticator-secret-key.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-phone-sms.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-phone-voice.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-security-question.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-select-authenticator-with-skip.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-enroll-select-authenticator.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-expired-password-no-complexity.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-expired-password-with-enrollment-authenticator.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-expired-password.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-expiry-warning-password.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-reset-password-revoke-sessions.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-reset-password.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-data-phone-sms-only.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-data-with-email.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-email-step1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-email-step2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-google-authenticator.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-okta-verify-push-code.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-okta-verify-push-poll.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-okta-verify-push.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-okta-verify-totp.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-ov-resend-push-notification.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-phone-sms.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-security-question.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-select-authenticator.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/authenticator-verification-unlock-success.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/device-challenge-poll-custom-uri.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/email-challenge-consent.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/enroll-profile.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/error-400-unauthorized-client.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/error-401-invalid-otp-passcode.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/error-account-creation.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/error-feature-not-enabled.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/error-recovery-token-invalid.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/error-session-expired.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/identify-with-password.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/oda-enrollment-android-applink-without-account.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/oda-enrollment-android-applink.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/oda-enrollment-android-loopback.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/oda-enrollment-ios.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/okta-verify-email-channel-enrollment.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/okta-verify-enrollment-version-upgrade.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/okta-verify-poll-enrollment.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/okta-verify-sms-channel-enrollment.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-email-error.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-email.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-otp-only-full-location-enrollment.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-otp-only-full-location-recovery.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-otp-only-full-location-unlock.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-otp-only-full-location.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-otp-only-no-location.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/terminal-return-otp-only-partial-location.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/unlock-account-success.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/user-unlock-account.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/screenshots/webauthn-enroll.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/start-app-e2e.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/tests/pagetitle.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/tests/theming.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/tsconfig.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/e2e/util/A11y.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/admin-consent-without-logo.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/admin-consent.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-apple-sso-extension.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-email-verification-data.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-data-phone.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-email-magic-link-false.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-email.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-google-authenticator.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-password-requirements-not-met.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-phone-sms.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-phone-voice.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-security-question-error.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-security-question.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-select-authenticator-with-skip.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-select-authenticator.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-enroll-yubikey-otp.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-expired-password-no-complexity.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-expired-password-with-enrollment-authenticator.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-expired-password.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-expiry-warning-password.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-piv-cac-verification.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-reset-password-revoke-sessions.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-reset-password.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-custom-otp.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-data-ov-only-with-device-known.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-data-ov-only-without-device-known.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-data-phone-sms-only.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-data-with-email.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-email-magic-link-false.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-email.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-google-authenticator.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-okta-verify-push-code.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-okta-verify-push-poll.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-okta-verify-push.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-okta-verify-totp-only-ov.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-okta-verify-totp.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-ov-resend-push-notification.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-ov-totp-biometrics-error.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-phone-sms.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-security-question.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-select-authenticator.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-tac.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-unlock-success.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-webauthn.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/authenticator-verification-yubikey-otp.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/byol.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/device-code-activate.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/email-challenge-consent.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enduser-consent-without-logo.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enduser-consent.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enroll-profile-new-additional-fields.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enroll-profile-new.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enroll-profile-registration-callbacks.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enroll-profile-update-optional-fields.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enroll-profile-update-required-field.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enroll-profile-update.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/enroll-profile-with-password.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/error-400-unauthorized-client.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/error-account-creation.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/error-activation-token-invalid.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/error-authenticator-enrollment-not-allowed.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/error-feature-not-enabled.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/error-recovery-token-invalid.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/error-session-expired.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/flow-unlock-account-email-polling-to-mfa-authenticator.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/flow-verify-with-piv-as-authenticator.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/granular-consent-without-logo.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/granular-consent.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/identify-recovery.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/identify-with-password.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/oda-enrollment-android-applink.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/oda-enrollment-android-loopback.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/oda-enrollment-ios.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/okta-verify-email-channel-enrollment.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/okta-verify-enrollment-version-upgrade.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/okta-verify-sms-channel-enrollment.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/request-activation-email.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-email-error.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-email.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-otp-only-full-location-enrollment.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-otp-only-full-location-recovery.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-otp-only-full-location-unlock.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-otp-only-full-location.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-otp-only-no-location.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/terminal-return-otp-only-partial-location..test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/trigger-email-verify-callback.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/unlock-account-success.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/user-unlock-account.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/__snapshots__/webauthn-enroll.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/admin-consent-without-logo.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/admin-consent.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-apple-sso-extension.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-email-verification-data.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-data-phone.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-email-magic-link-false.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-email.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-google-authenticator.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-password-requirements-not-met.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-phone-sms.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-phone-voice.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-security-question-error.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-security-question.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-select-authenticator-with-skip.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-select-authenticator.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-enroll-yubikey-otp.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-expired-password-no-complexity.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-expired-password-with-enrollment-authenticator.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-expired-password.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-expiry-warning-password.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-piv-cac-verification.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-reset-password-revoke-sessions.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-reset-password.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-custom-otp.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-data-ov-only-with-device-known.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-data-ov-only-without-device-known.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-data-phone-sms-only.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-data-with-email.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-email-magic-link-false.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-email.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-google-authenticator.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-okta-verify-push-code.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-okta-verify-push-poll.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-okta-verify-push.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-okta-verify-totp-only-ov.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-okta-verify-totp.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-ov-resend-push-notification.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-ov-totp-biometrics-error.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-phone-sms.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-security-question.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-select-authenticator.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-tac.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-unlock-success.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-webauthn.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/authenticator-verification-yubikey-otp.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/byol.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/device-code-activate.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/email-challenge-consent.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enduser-consent-without-logo.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enduser-consent.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enroll-profile-new-additional-fields.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enroll-profile-new.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enroll-profile-registration-callbacks.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enroll-profile-update-optional-fields.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enroll-profile-update-required-field.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enroll-profile-update.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/enroll-profile-with-password.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/error-400-unauthorized-client.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/error-account-creation.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/error-activation-token-invalid.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/error-authenticator-enrollment-not-allowed.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/error-feature-not-enabled.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/error-recovery-token-invalid.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/error-session-expired.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-enroll-profile-identify-transition-clear-errors.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-entry-payload.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-identifier-first-authentication.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-okta-verify-enrollment.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-password-authentication-security-question-authentication.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-transition-focus.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-transition.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-unlock-account-email-polling-to-mfa-authenticator.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-verify-with-piv-as-authenticator.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flow-yubikey-otp-to-password-verification.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/flows.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/granular-consent-without-logo.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/granular-consent.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/identify-recovery.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/identify-with-password-error-flow.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/identify-with-password-ext.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/identify-with-password.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/identify-with-webauthn-autofill.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/oda-enrollment-android-applink.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/oda-enrollment-android-loopback.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/oda-enrollment-ios.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/okta-verify-email-channel-enrollment.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/okta-verify-enrollment-version-upgrade.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/okta-verify-sms-channel-enrollment.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/request-activation-email.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-email-error.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-email.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-otp-only-full-location-enrollment.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-otp-only-full-location-recovery.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-otp-only-full-location-unlock.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-otp-only-full-location.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-otp-only-no-location.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/terminal-return-otp-only-partial-location..test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/trigger-email-verify-callback.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/tsconfig.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/unlock-account-success.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/user-unlock-account.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/util/createAuthClient.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/util/createAuthJsPayload.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/util/getMockCredentialsResponse.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/util/index.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/util/setup.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/util/updateDynamicAttribute.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/integration/webauthn-enroll.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/src/v3/test/vrt/.testcaferc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/babel.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/index.hbs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/public/oidc-app.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/.eslintrc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/TestApp.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/config.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/configArea.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/configForm.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/constants.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/getOktaSignInFromCDN.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/getOktaSignInFromNPM.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/index.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/types.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/src/util.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/tsconfig.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/app/webpack.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/.eslintrc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/babel.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/cucumber.wdio.conf.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/basic.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/csp.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/embedded/account-recovery-flow.embedded.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/embedded/authenticator-page-link.embedded.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/embedded/help-links.embedded.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/embedded/show-identifier.embedded.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/embedded/username-password-fields.embedded.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/hosted/account-recovery-flow.hosted.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/hosted/authenticator-page-link.hosted.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/hosted/help-links.hosted.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/hosted/show-identifier.hosted.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/customizations/hosted/username-password-fields.hosted.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/email-login.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/interaction-code-flow.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/multi-tabs-flow.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/oidc.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/self-service-password-recovery.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/self-service-registration.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/self-service-unlock.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/webauthn-login.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/features/widget-flows.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/biometric-authenticator.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/challenge-email-authenticator.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/enroll-password-authenticator.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/enroll-phone-authenticator.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/forgot-password.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/okta-home.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/primary-auth-oie.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/primary-auth.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/registration.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/reset-password.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/setup-authenticator.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/test-app.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/unlock.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/verify-email-authenticator.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/page-objects/verify-phone-authenticator.page.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/runner.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/runner.langtests.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/sauce.wdio.conf.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/specs/basic-dev.e2e.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/specs/basic.disabled_e2e.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/specs/csp-issues.e2e.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/specs/language.e2e.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/specs/oidc.e2e.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/steps/.eslintrc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/steps/after.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/steps/before.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/steps/given.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/steps/then.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/steps/when.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/.eslintrc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/a18nClient.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/context.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/assignAppToGroup.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/createApp.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/createCredentials.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/createGroup.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/createUser.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/deleteApp.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/deleteGroup.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/deleteUser.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/deleteUserAndCredentials.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/fetchGroup.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/fetchPolicy.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/getOktaClient.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/management-api/getOrCreateNoResetPasswordPolicy.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/monolith/create-testenv.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/monolith/disable-oie.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/monolith/enable-oie.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/monolith/get-email-messages.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/monolith/get-sms-messages.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/monolith/monolithClient.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/screenshots.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/support/wait/waitForOneSecond.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/tsconfig.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/util/configUtil.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/util/elementExists.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/util/locUtil.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/util/random.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/util/waitUtil.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/e2e/wdio.conf.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/cjs/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/cjs/test-require.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/cjs/webpack.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/esm/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/esm/test-import.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/esm/webpack.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/tsc/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/tsc/test-import-node16.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/tsc/test-import.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/package/tsc/tsconfig.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/.eslintrc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/a11y.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/BYOLPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/BasePageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeCustomAppPushPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeCustomOTPPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeEmailPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeFactorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeGoogleAuthenticatorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeOktaVerifyPushPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeOktaVerifyTotpPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeOnPremPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengePasswordPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengePhonePageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeSecurityQuestionPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ChallengeTacPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ConsentPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/CustomizedEnrollProfileViewPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/CustomizedIdentifyPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/DeviceAssuranceGracePeriodPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/DeviceChallengePollPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/DeviceCodeActivatePageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/DeviceEnrollmentTerminalPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/DuoPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnduserConsentPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollCustomPasswordPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollEmailPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollGoogleAuthenticatorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollOVViaEmailPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollOVViaSMSPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollOktaVerifyPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollOnPremPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollPhonePageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollProfileUpdateViewPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollProfileViewPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollSecurityQuestionPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/EnrollWebauthnPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/FactorEnrollPasswordPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/FactorEnrollPhonePageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/IdPAuthenticatorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/IdentityPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/PIVPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/PlaygroundErrorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/PollingPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/PostAuthKeepMeSignedInPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/ProfileEnrollmentStringOptionsViewPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/RegistrationPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/RequestActivationEmailPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/SelectAuthenticatorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/SelectFactorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/SignInDevicePageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/SignInWebAuthnPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/SuccessPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/SwitchOVEnrollChannelPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/SymantecAuthenticatorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/TerminalOtpOnlyPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/TerminalPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/TerminalPageObjectV3.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/UiDemoPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/YubiKeyAuthenticatorPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/components/BaseFormObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/components/CalloutObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/components/DebuggerObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects/components/FooterLinkObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/DeviceCodeActivatePageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/ForgotPasswordPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/MFAOktaVerifyPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/MFAVerifyPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/PrimaryAuthPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/ResetPasswordPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/SmsEnrollPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/page-objects-v1/UnlockAccountPageObject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/framework/shared/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/AdminConsentView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/AuthenticatorIdPView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/AuthenticatorIdvView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/AuthenticatorSymantecView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/AuthenticatorYubikeyView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/BYOL_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorDuo_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorEmail_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorOktaVerifyPushOnly_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorOktaVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorOnPrem_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorPassword_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorPhone_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorRsa_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeAuthenticatorSecurityQuestion_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeCustomAppPush_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeCustomOTPAuthenticatorView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeGoogleAuthenticatorOtp_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeOktaVerifyFastPassView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeOktaVerifyPush_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeOktaVerifyResendPushView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeOktaVerifySSOExtensionView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeOktaVerifyTotp_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengePIV_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ChallengeTacAuthenticatorView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceAssuranceAZTRemediation_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceAssuranceGracePeriod_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceChallengePollViewFailure_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceChallengePollView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceCodeActivate_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceEnrollmentTerminalView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceProbingChromeDTC_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceSSOExtensionVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/DeviceSSOExtensionView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EmailMagicLinkOTPTerminalView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnduserConsentView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnduserEmailConsentView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorDataPhoneView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorDuo_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorEmailFirst_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorEmail_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorGoogleAuthenticator_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorOktaVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorOnPremMfaView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorPasswordView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorPhoneView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorRsaView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorSecurityQuestion_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollAuthenticatorWebAuthn_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollProfileUpdateView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/EnrollProfileView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/FailureRedirect_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/GenericErrorHandling_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/GranularConsentView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyOktaVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyRecoveryWithCaptcha_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyRecovery_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyRegistrationHooks_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyRegistrationWithCaptcha_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyRegistration_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyUnknownUser_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyWithCaptcha_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyWithPassword_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyWithRememberUsername_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdentifyWithThirdPartyIdps_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/Identify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/IdxSessionExpired_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/Interact_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/Introspect_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/LaunchOVWithRememberMe_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/NumberChallengePushView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/OktaPluginDebugger_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/OktaSignIn_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/PollView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/PostAuthKeepMeSignedInView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ProfileEnrollmentWithStringOptions_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ReEnrollAuthenticatorPasswordView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ReEnrollAuthenticatorWarningPasswordView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ReEnrollCustomPasswordExpiryView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ReEnrollCustomPasswordExpiryWarningView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/Redirect_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/RequestActivationEmail_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ResetAuthenticatorGoogleView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/ResetAuthenticatorPasswordView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/SafeMode_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/SelectAuthenticatorForEnroll_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/SelectAuthenticatorForVerification_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/SessionStorage_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/SignInDeviceView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/SignInWebAuthnView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/Smoke_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/Success_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/TerminalView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/UnlockAccountChallengeIdentifierFirst_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/UnlockAccountChallenge_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/WidgetCustomization_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/WidgetHooks_gen3_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/uiDemo_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/v1/Animations_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/v1/DeviceCodeActivate_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/v1/MFAOktaVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/v1/MFAVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/v1/PrimaryAuthForm_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/v1/ResetPassword_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec/v1/UsingDeviceFlow_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/testcafe/spec-en-leaks/EnglishLeaks_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/types/api.test-d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/types/jest.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/types/language.test-d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/types/options.test-d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/types/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/types/registration.test-d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/types/tsconfig.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/.eslintrc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/assets/1x1.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/assets/1x1v2.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/assets/default.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/assets/logo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/AccountRecoveryForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/AdminConsentRequiredForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/AuthContainer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/Beacon.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/ConsentRequiredForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/DeviceCodeActivateForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/DeviceCodeActivateTerminalForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/Dom.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollActivateEmailForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollCallForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollChoicesForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollCustomFactorForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollDuoForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollEmailForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollHotpForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollPasswordForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollPushLinkSentForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollQuestionsForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollSmsForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollTokenFactorForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollTotpBarcodeForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollTotpDeviceTypeForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollTotpManualSetupForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollTotpPassCodeForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollU2FForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollUserForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollWebauthnForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/EnrollWindowsHelloForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/ErrorStateForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/Form.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/GranularConsentRequiredForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/IDPDiscoveryForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/MfaVerifyForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/PasswordExpiredForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/PasswordResetForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/PivForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/PollingForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/PrimaryAuthForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/RecoveryChallengeForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/RecoveryQuestionForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/RegistrationForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/VerifyCustomFactorForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/v2/IdentifierForm.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/dom/v2/TerminalView.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/getAuthClient.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/mocks/AuthClient.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/mocks/Util.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/sandbox.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/util/Expect.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/util/jquery.okta.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/v2/MockUtil.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/v2/idx/fullFlowResponse.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/200.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ACCOUNT_LOCKED_OUT.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ADMIN_CONSENT_REQUIRED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/CANCEL.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/CONSENT_REQUIRED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/CUSTOM_PASSWORD_EXPIRED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/CUSTOM_PASSWORD_WARN.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/DEVICE_CODE_ACTIVATE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/DEVICE_CODE_ACTIVATE_invalidCode.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/DEVICE_CODE_TERMINAL_activated.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/DEVICE_CODE_TERMINAL_consentDenied.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/DEVICE_CODE_TERMINAL_invalidCode.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/DEVICE_CODE_TERMINAL_notActivated.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_INVALID_EMAIL_DOMAIN.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_INVALID_TEXT_RESPONSE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_NON_JSON_RESPONSE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_OPERATION_NOT_ALLOWED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_VALID_RESPONSE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_invalid_token.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_notUnique.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_throttle.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/ERROR_webfinger.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_ACTIVATE_call.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_ACTIVATE_push.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_ACTIVATE_push_timeout.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_ACTIVATE_push_waiting.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_ACTIVATE_sms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_allFactors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_call.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_callFactor_existingPhone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_password_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_push.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_push_success_newqr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_sms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_smsFactor_existingPhone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_ENROLL_totp_success.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/FACTOR_REQUIRED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/GRANULAR_CONSENT_REQUIRED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/IDPDiscoverySuccessRepost_IWA.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/IDPDiscoverySuccess_IWA.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/IDPDiscoverySuccess_OktaIDP.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/IDPDiscoverySuccess_SAML.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_ClaimsProvider.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_call.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_customOIDCFactor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_customSAMLFactor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_duo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_email.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_multipleU2F.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_multipleWebauthn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_push.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_push_rejected.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_push_timeout.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_push_with_number_challenge.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_sms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_u2f.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_webauthn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_CHALLENGE_windows_hello.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_ClaimsProvider.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_CustomOidc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_CustomSaml.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_OnPrem_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_call_success.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_duo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_email.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_errorActivate.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_invalid_phone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_push_email.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_push_sms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_push_timeout.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_success.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_totp_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_u2f.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_webauthn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_ACTIVATE_windows_hello.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_U2F.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_allFactors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_allFactorsProfile.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_allFactors_OnPrem.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_callFactor_existingPhone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_email.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_enrolledHotp.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_multipleOktaVerify.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_multipleU2F.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_multipleWebauthn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_multipleWebauthnProfile.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_push.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_push_success.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_push_success_newqr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_question_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_question_questions.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_smsFactor_existingPhone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_totp_success.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_ENROLL_webauthn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_LOCKED_FAILED_ATEMPTS.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_U2F.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_allFactors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_allFactors_OnPrem.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_duo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_multipleFactors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_multipleOktaVerify.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_multipleU2F.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_multipleWebauthn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_multipleWebauthn_question.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_oktaPassword.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_oktaVerify.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_oktaVerifyPushOnly.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_oktaVerifyTotpOnly.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_policy_always.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_REQUIRED_windows_hello.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_RESEND_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_VERIFY_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_VERIFY_invalid_answer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_VERIFY_invalid_password.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/MFA_VERIFY_totp_invalid_answer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/NO_PERMISSION_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORDLESS_UNAUTHENTICATED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_EXPIRED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_EXPIRED_error_complexity.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_EXPIRED_error_noCause.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_EXPIRED_error_oldpass.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_RESET.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_RESET_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_RESET_error_noCause.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_RESET_withComplexity.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PASSWORD_WARN.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PIV_GET.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PIV_POST.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PIV_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/POLLING.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PROFILE_REQUIRED_NEW.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/PROFILE_REQUIRED_UPDATE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_ANSWER_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_CHALLENGE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_CHALLENGE_CALL_PWD.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_CHALLENGE_CALL_UNLOCK.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_CHALLENGE_EMAIL_PWD.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_CHALLENGE_EMAIL_UNLOCK.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_CHALLENGE_SMS_PWD.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_CHALLENGE_SMS_UNLOCK.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_EXPIRED_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RECOVERY_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/RSA_ERROR_change_pin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SESSION_ACTIVE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SESSION_DELETED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SESSION_NOT_FOUND.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SUCCESS.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SUCCESS_next.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SUCCESS_original.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SUCCESS_session_step_up.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SUCCESS_unlock.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/SUCCESS_with_STAF.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/UNAUTHENTICATED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/UNAUTHENTICATED_IDX.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/UNAUTHORIZED_ERROR.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/keys.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/labels_country_ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/labels_login_ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/security_image.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/security_image_error.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/security_image_fail.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/security_image_new_user.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/v2/ERROR_FEATURE_NOT_ENABLED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/v2/IDX_ERROR_SESSION_EXPIRED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/v2/IDX_ERROR_USER_IS_NOT_ASSIGNED.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/v2/IDX_IDENTIFY.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/v2/IDX_SUCCESS_INTERACTION_CODE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/v2/IDX_VERIFY_PASSWORD.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/well-known-shared-resource.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/helpers/xhr/well-known.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/jest/jest-global-setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/jest/jest-setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/ColorsUtil_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/CourageForSigninWidget_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/CryptoUtil_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/EnrollOktaVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/FidoUtil_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/Hooks_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/LanguageUtil_spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/OAuth2Util_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/OktaSignInV1Bootstrap_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/OktaSignInV2Bootstrap_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/OktaSignIn_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/Settings_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/TimeUtil_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/UtilBrowserFeatures_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/Util_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/plugins/OktaPluginDebugger/utils_spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/router/classic_spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/router/default_spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/router/oie_spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/AdminConsentRequired_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/AppState_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/ConsentRequired_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/DeviceCodeActivateTerminal_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/DeviceCodeActivate_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/DeviceFingerprint_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollActivateClaimsFactor_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollCall_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollChoices_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollCustomFactor_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollDuo_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollEmail_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollHotpController_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollOnPrem_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollPassword_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollQuestions_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollSms_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollSymantecVip_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollTotpController_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollU2F_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollUser_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollWebauthn_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollWindowsHello_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/EnrollYubikey_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/ForgotPassword_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/GranularConsentRequired_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/IDPDiscovery_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/LoginRouter_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/MfaVerifyEmail_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/MfaVerify_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/PasswordExpired_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/PasswordReset_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/PollController_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/PrimaryAuth_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/RecoveryChallenge_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/RecoveryLoading_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/RecoveryQuestion_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/RefreshAuthState_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/RegistrationFormFactory_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/RegistrationSchema_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/Registration_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/Spinner_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/TextBox_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/UnlockAccount_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/VerifyCustomFactor_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/VerifyPIV_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v1/VerifyWebauthn_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/BaseLoginRouter_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/WidgetRouter_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/client/emailVerifyCallback_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/client/handleConfiguredFlow_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/client/interactionCodeFlow_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/client/startLoginFlow_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/ion/IonResponseHelper_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/ion/ViewClassNamesFactory_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/ion/i18nTransformer_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/ion/responseTransformer_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/ion/uiSchemaTransformer_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/models/AppState_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/components/AuthenticatorEnrollOptionsContainer_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/components/__snapshots__/AuthenticatorEnrollOptionsContainer_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/internals/BaseFooter_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/internals/BaseModel_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/internals/FormInputFactory_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/utils/AuthenticatorUtil_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/utils/ChallengeViewUtil_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/utils/DeviceFingerprinting_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/utils/LinksUtil_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/AuthenticatorIdpVerifyView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/AutoRedirectAfterUnlockView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/AutoRedirectView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/CaptchaView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/ChallengeWebauthnView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/EnrollWebAuthnResidentkeyLinkView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/EnrollWebauthnView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/IdentifierView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/PostAuthKeepMeSignedInView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/RedirectIdvView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/SelectEnrollmentChannelOktaVerifyView_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/SignInWithWebAuthn_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/__snapshots__/AutoRedirectAfterUnlockView_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/__snapshots__/AutoRedirectView_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/__snapshots__/CaptchaView_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/__snapshots__/EnrollWebAuthnResidentkeyLinkView_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/__snapshots__/PostAuthKeepMeSignedInView_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/__snapshots__/RedirectIdvView_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/__snapshots__/SignInWithWebAuthn_spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/v2/view-builder/views/shared/polling_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/webauthn_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/widget/buildRenderOptions_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/widget/createRouter_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/test/unit/spec/widget/getAuthClient_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/okta_okta-signin-widget/yarn.lock", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.7560720443725586, "profiling_times": {"config_time": 6.087028503417969, "core_time": 23.14958691596985, "ignores_time": 0.0019235610961914062, "total_time": 29.239567518234253}, "parsing_time": {"total_time": 37.65666627883911, "per_file_time": {"mean": 0.01998761479768528, "std_dev": 0.0022795186392569186}, "very_slow_stats": {"time_ratio": 0.11950187429675013, "count_ratio": 0.005307855626326964}, "very_slow_files": [{"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/transformSelectAuthenticatorEnroll.test.ts", "ftime": 0.32261204719543457}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v1/models/AppState.js", "ftime": 0.32449793815612793}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/oktaVerify/transformOktaVerifyEnrollPoll.test.ts", "ftime": 0.3252909183502197}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/transformEmailAuthenticatorVerify.test.ts", "ftime": 0.3416941165924072}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/passwordUtils.test.ts", "ftime": 0.391448974609375}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/SchemaProperty.js", "ftime": 0.42400407791137695}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/layout/email/transformEmailAuthenticatorEnroll.test.ts", "ftime": 0.42506885528564453}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseForm.js", "ftime": 0.43453001976013184}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/utils.test.ts", "ftime": 0.6735491752624512}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "ftime": 0.837346076965332}]}, "scanning_time": {"total_time": 201.22220826148987, "per_file_time": {"mean": 0.030594831725937313, "std_dev": 0.028397297450639708}, "very_slow_stats": {"time_ratio": 0.1931113810738956, "count_ratio": 0.002432720085145203}, "very_slow_files": [{"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/yarn.lock", "ftime": 1.6656579971313477}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v2/controllers/FormController.ts", "ftime": 1.7131688594818115}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useOnSubmit.ts", "ftime": 1.9719631671905518}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/formUtils.ts", "ftime": 2.0079550743103027}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/Select.js", "ftime": 2.066847085952759}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/models/Settings.ts", "ftime": 2.09000301361084}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v1/models/AppState.js", "ftime": 2.6910388469696045}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/SchemaProperty.js", "ftime": 2.8459391593933105}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "ftime": 5.100148916244507}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseForm.js", "ftime": 7.2663891315460205}]}, "matching_time": {"total_time": 77.7339379787445, "per_file_and_rule_time": {"mean": 0.008421878437567127, "std_dev": 0.0010958948966756016}, "very_slow_stats": {"time_ratio": 0.26648371700007994, "count_ratio": 0.011375947995666305}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/hooks/useOnSubmit.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.2845590114593506}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseForm.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.3023200035095215}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v1/models/AppState.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.3363499641418457}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/models/SchemaProperty.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5279948711395264}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v1/models/AppState.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.532088041305542}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseForm.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.5463969707489014}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/Widget/index.tsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5882890224456787}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.6325781345367432}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseForm.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6489741802215576}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 2.108203887939453}]}, "tainting_time": {"total_time": 17.738919734954834, "per_def_and_rule_time": {"mean": 0.0036825658573707344, "std_dev": 0.00022492602763643388}, "very_slow_stats": {"time_ratio": 0.2382571766279329, "count_ratio": 0.007681129333610131}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/LoopbackProbe/LoopbackProbe.test.tsx", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.10084986686706543}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/framework/Model.js", "fline": 202, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.13016986846923828}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/components/PhoneAuthenticator/PhoneAuthenticator.test.tsx", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.14228105545043945}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/transformer/selectAuthenticator/utils.test.ts", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.1583998203277588}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "fline": 1, "rule_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "time": 0.16447019577026367}, {"fpath": "downloaded_repos/okta_okta-signin-widget/playground/mocks/server.js", "fline": 24, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.2007770538330078}, {"fpath": "downloaded_repos/okta_okta-signin-widget/src/v3/src/util/passwordUtils.test.ts", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.34185194969177246}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/lib/underscore/underscore-min.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.3765530586242676}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/inputs/DeletableBox.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.43862485885620117}, {"fpath": "downloaded_repos/okta_okta-signin-widget/packages/@okta/courage-dist/esm/src/courage/views/forms/BaseForm.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.48157405853271484}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1147694016}, "engine_requested": "OSS", "skipped_rules": []}