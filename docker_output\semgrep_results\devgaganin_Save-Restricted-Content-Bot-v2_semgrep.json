{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/Dockerfile", "start": {"line": 14, "col": 1, "offset": 373}, "end": {"line": 14, "col": 55, "offset": 427}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD flask run -h 0.0.0.0 -p 8000 & python3 -m devgagan", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "path": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/app.py", "start": {"line": 14, "col": 5, "offset": 351}, "end": {"line": 14, "col": 39, "offset": 385}, "extra": {"message": "Running flask app with host 0.0.0.0 could expose the server publicly.", "metadata": {"cwe": ["CWE-668: Exposure of Resource to Wrong Sphere"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "shortlink": "https://sg.run/eLby"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/__main__.py", "start": {"line": 37, "col": 9, "offset": 1157}, "end": {"line": 37, "col": 66, "offset": 1214}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.exec-detected.exec-detected", "path": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/eval.py", "start": {"line": 25, "col": 5, "offset": 818}, "end": {"line": 28, "col": 6, "offset": 931}, "extra": {"message": "Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.exec-detected.exec-detected", "shortlink": "https://sg.run/ndRX"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/Dockerfile", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/LICENSE", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/Procfile", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/README.md", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/TERMS_OF_USE.md", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/app.json", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/app.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/config.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/__init__.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/__main__.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/__init__.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/func.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/get_func.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/mongo/__init__.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/mongo/db.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/mongo/plans_db.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/mongo/users_db.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/__init__.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/eval.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/gcast.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/login.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/main.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/plans.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/shrink.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/speedtest.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/start.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/stats.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/ytdl.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/heroku.yml", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/requirements.txt", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/settings.jpg", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/templates/welcome.html"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.076080799102783, "profiling_times": {"config_time": 6.646393060684204, "core_time": 3.6898982524871826, "ignores_time": 0.002168416976928711, "total_time": 10.339921474456787}, "parsing_time": {"total_time": 0.654076337814331, "per_file_time": {"mean": 0.025156782223628115, "std_dev": 0.0005789278996006943}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.546539545059204, "per_file_time": {"mean": 0.050517106056213366, "std_dev": 0.015260264809313074}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.7801856994628906, "per_file_and_rule_time": {"mean": 0.005993891243982799, "std_dev": 0.00021934013590556441}, "very_slow_stats": {"time_ratio": 0.15230371358469139, "count_ratio": 0.006734006734006734}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/core/func.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.10035896301269531}, {"fpath": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/ytdl.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.17076992988586426}]}, "tainting_time": {"total_time": 0.5805988311767578, "per_def_and_rule_time": {"mean": 0.0011208471644338952, "std_dev": 1.5278037585363517e-05}, "very_slow_stats": {"time_ratio": 0.12042913788062456, "count_ratio": 0.0019305019305019305}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v2/devgagan/modules/main.py", "fline": 174, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.06992101669311523}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}