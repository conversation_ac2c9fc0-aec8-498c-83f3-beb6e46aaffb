{"version": "1.130.0", "results": [], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "message": "Timeout when running javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag on downloaded_repos/ayoubhayda_react-admin-dashboard/src/data/mockGeoFeatures.js:\n ", "path": "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/mockGeoFeatures.js"}], "paths": {"scanned": ["downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/.gitignore", "downloaded_repos/ayoubhay<PERSON>_react-admin-dashboard/README.md", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/index.html", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/package-lock.json", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/package.json", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/public/logo.svg", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/App.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/Router.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/assets/images/avatar.png", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/assets/images/logo.png", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/src/components/GeographyChart.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/components/Header.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/components/ProgressCircle.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/components/StatBox.jsx", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/src/components/index.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/allGeneratorsData.js", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/availableMetrics.js", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/fleetData.js", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/mockData.js", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/mockGeoFeatures.js", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/src/index.css", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/main.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/dashboard/avgEngineLoadsChart.jsx", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/src/scenes/dashboard/avgEngineLoadsFleetFuelLevel.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/dashboard/feetOverviewDataBox.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/dashboard/fleetFuelLevelChart.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/dashboard/generatorStatus.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/dashboard/index.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/dashboard/metricTrendModal.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/geography/index.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/index.js", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/scenes/layout/navbar/index.jsx", "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/theme.js", "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/vite.config.js"], "skipped": [{"path": "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/mockGeoFeatures.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.7352750301361084, "profiling_times": {"config_time": 5.981776475906372, "core_time": 7.64080548286438, "ignores_time": 0.0018241405487060547, "total_time": 13.625331401824951}, "parsing_time": {"total_time": 0.8536155223846436, "per_file_time": {"mean": 0.03161538971794976, "std_dev": 0.004203256987059349}, "very_slow_stats": {"time_ratio": 0.39944217323409087, "count_ratio": 0.037037037037037035}, "very_slow_files": [{"fpath": "downloaded_repos/ayoub<PERSON><PERSON>_react-admin-dashboard/package-lock.json", "ftime": 0.3409700393676758}]}, "scanning_time": {"total_time": 7.60425329208374, "per_file_time": {"mean": 0.07921097179253897, "std_dev": 0.2856433676532035}, "very_slow_stats": {"time_ratio": 0.6896571984868888, "count_ratio": 0.010416666666666666}, "very_slow_files": [{"fpath": "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/mockGeoFeatures.js", "ftime": 5.244328022003174}]}, "matching_time": {"total_time": 0.5918691158294678, "per_file_and_rule_time": {"mean": 0.009546276061765611, "std_dev": 0.0003914544634182396}, "very_slow_stats": {"time_ratio": 0.17754792689082163, "count_ratio": 0.016129032258064516}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/ayo<PERSON><PERSON><PERSON>_react-admin-dashboard/src/data/mockGeoFeatures.js", "rule_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "time": 0.10508513450622559}]}, "tainting_time": {"total_time": 0.01205301284790039, "per_def_and_rule_time": {"mean": 0.001205301284790039, "std_dev": 1.2764457346747804e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}