"""empty message

Revision ID: a4503f708aee
Revises: d5598643eeee
Create Date: 2022-06-09 18:56:24.929143

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a4503f708aee'
down_revision = 'd5598643eeee'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('video', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('video', sa.Column('updated_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('video', 'created_at')
    op.drop_column('video', 'updated_at')
    # ### end Alembic commands ###
