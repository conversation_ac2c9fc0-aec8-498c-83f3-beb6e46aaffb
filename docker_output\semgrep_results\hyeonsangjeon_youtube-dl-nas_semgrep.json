{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/hyeonsangjeon_youtube-dl-nas/Dockerfile", "start": {"line": 43, "col": 1, "offset": 958}, "end": {"line": 43, "col": 29, "offset": 986}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/bin/bash\", \"/run.sh\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/hyeonsangjeon_youtube-dl-nas/.gitignore", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/Auth.json", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/DISCLAIMER.md", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/Dockerfile", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/LICENSE", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/README.md", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/bottle_websocket/__init__.py", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/bottle_websocket/plugin.py", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/bottle_websocket/server.py", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/examples/chat/README.md", "downloaded_repos/hyeonsang<PERSON>on_youtube-dl-nas/examples/chat/chat.py", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/examples/chat/index.tpl", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/examples/chat/requirements.txt", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/examples/echo/README.md", "downloaded_repos/hyeonsang<PERSON>on_youtube-dl-nas/examples/echo/echo.py", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/examples/echo/index.tpl", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/examples/echo/requirements.txt", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/init_update.txt", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/metadata/download_history.json", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/pic/Architecture-Youtube-dl-nas.png", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/pic/TermsNConditions.png", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/pic/id_pw_set_synology.png", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/pic/volume_set_synology.png", "downloaded_repos/hyeonsang<PERSON>on_youtube-dl-nas/pic/youtube-dl-server-login.png", "downloaded_repos/hyeonsang<PERSON>on_youtube-dl-nas/pic/youtube-dl-server.png", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/requirements.txt", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/run.sh", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/static/css/bootstrap.min.css", "downloaded_repos/hyeonsang<PERSON>on_youtube-dl-nas/static/css/signin.css", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/static/css/style.css", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/static/js/ie-emulation-modes-warning.js", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/static/js/ie10-viewport-bug-workaround.js", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/static/logical_js/logic.js", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/static/template/index.tpl", "downloaded_repos/hyeonsang<PERSON>on_youtube-dl-nas/static/template/login.tpl", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/static/template/terms.tpl", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/subber", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/upd_schedule.py", "downloaded_repos/hyeonsangjeon_youtube-dl-nas/youtube-dl-server.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.8963491916656494, "profiling_times": {"config_time": 7.208148717880249, "core_time": 3.8795828819274902, "ignores_time": 0.0029633045196533203, "total_time": 11.091804504394531}, "parsing_time": {"total_time": 0.39757657051086426, "per_file_time": {"mean": 0.02092508265846654, "std_dev": 0.0004196403612687388}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.0104448795318604, "per_file_time": {"mean": 0.031035514221977943, "std_dev": 0.01791130001919673}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.0969562530517578, "per_file_and_rule_time": {"mean": 0.009218119773544184, "std_dev": 0.0002981198656846589}, "very_slow_stats": {"time_ratio": 0.09819716198852067, "count_ratio": 0.008403361344537815}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/hyeonsangjeon_youtube-dl-nas/youtube-dl-server.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.10771799087524414}]}, "tainting_time": {"total_time": 0.45435619354248047, "per_def_and_rule_time": {"mean": 0.0005396154317606656, "std_dev": 1.1734897469175133e-05}, "very_slow_stats": {"time_ratio": 0.213753103833326, "count_ratio": 0.0011876484560570072}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/hyeonsangjeon_youtube-dl-nas/youtube-dl-server.py", "fline": 537, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.09712004661560059}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}