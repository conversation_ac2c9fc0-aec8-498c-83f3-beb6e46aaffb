{"version": "1.130.0", "results": [{"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/DashboardController.java", "start": {"line": 14, "col": 6, "offset": 348}, "end": {"line": 14, "col": 20, "offset": 362}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/DashboardController.java", "start": {"line": 19, "col": 6, "offset": 454}, "end": {"line": 19, "col": 20, "offset": 468}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/NotificationController.java", "start": {"line": 25, "col": 6, "offset": 791}, "end": {"line": 25, "col": 20, "offset": 805}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/NotificationController.java", "start": {"line": 31, "col": 6, "offset": 1009}, "end": {"line": 31, "col": 20, "offset": 1023}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/NotificationController.java", "start": {"line": 37, "col": 6, "offset": 1240}, "end": {"line": 37, "col": 20, "offset": 1254}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/PageController.java", "start": {"line": 66, "col": 6, "offset": 2187}, "end": {"line": 66, "col": 20, "offset": 2201}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/PageController.java", "start": {"line": 110, "col": 6, "offset": 4032}, "end": {"line": 110, "col": 20, "offset": 4046}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/ProductController.java", "start": {"line": 37, "col": 6, "offset": 1146}, "end": {"line": 37, "col": 20, "offset": 1160}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/ProductController.java", "start": {"line": 45, "col": 6, "offset": 1325}, "end": {"line": 45, "col": 20, "offset": 1339}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/UserController.java", "start": {"line": 27, "col": 6, "offset": 783}, "end": {"line": 27, "col": 20, "offset": 797}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/UserController.java", "start": {"line": 33, "col": 6, "offset": 952}, "end": {"line": 33, "col": 20, "offset": 966}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.audit.spring-unvalidated-redirect.spring-unvalidated-redirect", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/controller/AbstractController.java", "start": {"line": 6, "col": 5, "offset": 72}, "end": {"line": 8, "col": 6, "offset": 148}, "extra": {"message": "Application redirects a user to a destination URL specified by a user supplied parameter that is not validated.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#UNVALIDATED_REDIRECT", "category": "security", "technology": ["spring"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/java.spring.security.audit.spring-unvalidated-redirect.spring-unvalidated-redirect", "shortlink": "https://sg.run/9oXz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/AboutController.java", "start": {"line": 14, "col": 6, "offset": 355}, "end": {"line": 14, "col": 20, "offset": 369}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/ContactController.java", "start": {"line": 31, "col": 6, "offset": 918}, "end": {"line": 31, "col": 20, "offset": 932}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/HomepageController.java", "start": {"line": 14, "col": 6, "offset": 358}, "end": {"line": 14, "col": 20, "offset": 372}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "path": "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/ShopFrontController.java", "start": {"line": 27, "col": 6, "offset": 726}, "end": {"line": 27, "col": 20, "offset": 740}, "extra": {"message": "Detected a method annotated with 'RequestMapping' that does not specify the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS, and by default all HTTP methods are allowed when the HTTP method is not explicitly specified. This means that a method that performs state changes could be vulnerable to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method (such as 'RequestMethod.POST').", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "references": ["https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING"], "category": "security", "technology": ["spring"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/java.spring.security.unrestricted-request-mapping.unrestricted-request-mapping", "shortlink": "https://sg.run/2xlq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/buttasam_cms-boot/src/main/resources/data.sql", "start": {"line": 31, "col": 48, "offset": 961}, "end": {"line": 31, "col": 108, "offset": 1021}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/buttasam_cms-boot/src/main/resources/data.sql", "start": {"line": 32, "col": 44, "offset": 1068}, "end": {"line": 32, "col": 104, "offset": 1128}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/fragments/header.html", "start": {"line": 17, "col": 5, "offset": 730}, "end": {"line": 17, "col": 79, "offset": 804}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/fragments/header.html", "start": {"line": 111, "col": 41, "offset": 5147}, "end": {"line": 113, "col": 48, "offset": 5344}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/front/fragments/header.html", "start": {"line": 10, "col": 5, "offset": 320}, "end": {"line": 10, "col": 80, "offset": 395}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/front/fragments/header.html", "start": {"line": 13, "col": 5, "offset": 456}, "end": {"line": 13, "col": 94, "offset": 545}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/buttasam_cms-boot/gradlew", "start": {"line": 74, "col": 5, "offset": 0}, "end": {"line": 74, "col": 93, "offset": 88}}, {"path": "downloaded_repos/buttasam_cms-boot/gradlew", "start": {"line": 180, "col": 5, "offset": 0}, "end": {"line": 180, "col": 12, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/buttasam_cms-boot/gradlew:74:\n `APP_HOME=${app_path%\"${app_path##*/}\"}  # leaves a trailing /; empty if no leading path\n` was unexpected", "path": "downloaded_repos/buttasam_cms-boot/gradlew", "spans": [{"file": "downloaded_repos/buttasam_cms-boot/gradlew", "start": {"line": 74, "col": 5, "offset": 0}, "end": {"line": 74, "col": 93, "offset": 88}}, {"file": "downloaded_repos/buttasam_cms-boot/gradlew", "start": {"line": 180, "col": 5, "offset": 0}, "end": {"line": 180, "col": 12, "offset": 7}}]}], "paths": {"scanned": ["downloaded_repos/buttasam_cms-boot/.gitattributes", "downloaded_repos/buttasam_cms-boot/.github/workflows/gradle.yml", "downloaded_repos/buttasam_cms-boot/.gitignore", "downloaded_repos/buttasam_cms-boot/LICENSE", "downloaded_repos/buttasam_cms-boot/README.md", "downloaded_repos/buttasam_cms-boot/build.gradle.kts", "downloaded_repos/buttasam_cms-boot/doc/img/admin.png", "downloaded_repos/buttasam_cms-boot/gradle/wrapper/gradle-wrapper.jar", "downloaded_repos/buttasam_cms-boot/gradle/wrapper/gradle-wrapper.properties", "downloaded_repos/buttasam_cms-boot/gradlew", "downloaded_repos/buttasam_cms-boot/gradlew.bat", "downloaded_repos/buttasam_cms-boot/settings.gradle.kts", "downloaded_repos/buttasam_cms-boot/src/main/java/app/Application.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/DashboardController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/NotificationController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/PageController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/ProductController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/UserController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/common/AdminCommonController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/parent/AdminAbstractController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/validation/PageTextValidator.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/controller/validation/PageValidator.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/form/PageForm.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/form/PageTextForm.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/admin/form/ProductForm.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/controller/AbstractController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/controller/CommonAbstractController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/controller/FileController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/cms/PageServiceImpl.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/cms/StorageServiceImpl.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/cms/api/PageService.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/cms/api/StorageService.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/eshop/ProductServiceImpl.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/eshop/api/ProductService.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/notification/NotificationServiceImpl.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/common/service/notification/api/NotificationService.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/config/BeanConfig.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/config/MvcConfig.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/config/WebSecurityConfig.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/config/anotation/AdminController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/config/anotation/FrontController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/AboutController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/ContactController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/HomepageController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/ShopFrontController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/common/FrontCommonController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/controller/parent/FrontAbstractController.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/front/form/ContactForm.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/auth/Role.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/auth/RoleType.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/auth/User.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/cms/Page.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/cms/PageImage.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/cms/PageText.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/cms/PageTextType.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/eshop/Product.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/message/Notification.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/entity/message/NotificationStatus.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/repository/auth/RoleRepository.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/repository/auth/UserRepository.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/repository/cms/PageImageRepository.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/repository/cms/PageRepository.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/repository/cms/PageTextRepository.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/repository/eshop/ProductRepository.java", "downloaded_repos/buttasam_cms-boot/src/main/java/app/persistence/repository/message/NotificationRepository.java", "downloaded_repos/buttasam_cms-boot/src/main/resources/META-INF/additional-spring-configuration-metadata.json", "downloaded_repos/buttasam_cms-boot/src/main/resources/application.yaml", "downloaded_repos/buttasam_cms-boot/src/main/resources/data.sql", "downloaded_repos/buttasam_cms-boot/src/main/resources/init.sql", "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/css/custom.css", "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/css/custom.min.css", "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/addPage.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/addProduct.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/all.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/dashboard.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/fragments/footer.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/fragments/header.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/notification.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/page.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/admin/product/all.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/front/about.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/front/contact.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/front/fragments/header.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/front/index.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/front/products.html", "downloaded_repos/buttasam_cms-boot/src/main/resources/templates/login.html", "downloaded_repos/buttasam_cms-boot/work/image/dummy1.png", "downloaded_repos/buttasam_cms-boot/work/image/test.png"], "skipped": [{"path": "downloaded_repos/buttasam_cms-boot/gradlew", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/buttasam_cms-boot/src/test/java/app/SmokeTest.java", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/buttasam_cms-boot/src/test/java/app/common/service/cms/StorageServiceImplTest.java", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6681900024414062, "profiling_times": {"config_time": 5.960076332092285, "core_time": 5.182497024536133, "ignores_time": 0.14308643341064453, "total_time": 11.28697681427002}, "parsing_time": {"total_time": 0.8384313583374023, "per_file_time": {"mean": 0.012703505429354583, "std_dev": 0.0015120265882636354}, "very_slow_stats": {"time_ratio": 0.3726874205917246, "count_ratio": 0.015151515151515152}, "very_slow_files": [{"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "ftime": 0.3124728202819824}]}, "scanning_time": {"total_time": 4.866057395935059, "per_file_time": {"mean": 0.019233428442431068, "std_dev": 0.027801204617054452}, "very_slow_stats": {"time_ratio": 0.5440343044325342, "count_ratio": 0.003952569169960474}, "very_slow_files": [{"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "ftime": 2.6473021507263184}]}, "matching_time": {"total_time": 1.9853179454803467, "per_file_and_rule_time": {"mean": 0.0054994956938513775, "std_dev": 0.0005710857928389052}, "very_slow_stats": {"time_ratio": 0.5745548801511031, "count_ratio": 0.019390581717451522}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "rule_id": "javascript.lang.security.audit.code-string-concat.code-string-concat", "time": 0.10032296180725098}, {"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.11806106567382812}, {"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.13157105445861816}, {"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.14252305030822754}, {"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.1944730281829834}, {"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.2212679386138916}, {"fpath": "downloaded_repos/buttasam_cms-boot/src/main/resources/static/admin/js/custom.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.23245501518249512}]}, "tainting_time": {"total_time": 0.7882463932037354, "per_def_and_rule_time": {"mean": 0.0005458770036036949, "std_dev": 2.092703332800334e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}