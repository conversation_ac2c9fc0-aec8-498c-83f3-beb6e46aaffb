{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/ollieread_multiauth/.gitattributes", "downloaded_repos/ollieread_multiauth/.github/FUNDING.yml", "downloaded_repos/ollieread_multiauth/.github/workflows/static-analysis.yml", "downloaded_repos/ollieread_multiauth/.github/workflows/tests.yml", "downloaded_repos/ollieread_multiauth/.gitignore", "downloaded_repos/ollieread_multiauth/.travis.yml", "downloaded_repos/ollieread_multiauth/CHANGELOG.md", "downloaded_repos/ollieread_multiauth/LICENSE.md", "downloaded_repos/ollieread_multiauth/README.md", "downloaded_repos/ollieread_multiauth/composer.json", "downloaded_repos/ollieread_multiauth/phpstan-baseline.neon", "downloaded_repos/ollieread_multiauth/phpstan.neon", "downloaded_repos/ollieread_multiauth/phpunit.xml", "downloaded_repos/ollieread_multiauth/src/MultiauthServiceProvider.php", "downloaded_repos/ollieread_multiauth/testbench.yaml", "downloaded_repos/ollieread_multiauth/workbench/app/Models/.gitkeep", "downloaded_repos/ollieread_multiauth/workbench/app/Models/User.php", "downloaded_repos/ollieread_multiauth/workbench/app/Providers/WorkbenchServiceProvider.php", "downloaded_repos/ollieread_multiauth/workbench/bootstrap/.gitkeep", "downloaded_repos/ollieread_multiauth/workbench/database/factories/.gitkeep", "downloaded_repos/ollieread_multiauth/workbench/database/factories/UserFactory.php", "downloaded_repos/ollieread_multiauth/workbench/database/migrations/.gitkeep", "downloaded_repos/ollieread_multiauth/workbench/database/seeders/DatabaseSeeder.php", "downloaded_repos/ollieread_multiauth/workbench/resources/views/.gitkeep", "downloaded_repos/ollieread_multiauth/workbench/routes/console.php", "downloaded_repos/ollieread_multiauth/workbench/routes/web.php"], "skipped": [{"path": "downloaded_repos/ollieread_multiauth/tests/Feature/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ollieread_multiauth/tests/Unit/.gitkeep", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.977802038192749, "profiling_times": {"config_time": 6.014578819274902, "core_time": 2.6140682697296143, "ignores_time": 0.0018393993377685547, "total_time": 8.631186246871948}, "parsing_time": {"total_time": 0.21025800704956055, "per_file_time": {"mean": 0.016173692849966195, "std_dev": 2.0248997644509244e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.44231152534484863, "per_file_time": {"mean": 0.0068047926976130555, "std_dev": 0.00014797249208250838}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.1089010238647461, "per_file_and_rule_time": {"mean": 0.0006892469864857347, "std_dev": 3.1130537120189995e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0006902217864990234, "per_def_and_rule_time": {"mean": 0.00017255544662475586, "std_dev": 3.565493145174514e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}