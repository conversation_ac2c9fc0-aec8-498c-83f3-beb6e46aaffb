{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 19, "col": 15, "offset": 417}, "end": {"line": 21, "col": 4, "offset": 512}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml:19:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `if ! [[ ${{ github.ref }} =~ ^refs/tags/[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}$ ]]; then\n  exit 1\nfi\n` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 19, "col": 15, "offset": 417}, "end": {"line": 21, "col": 4, "offset": 512}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 30, "col": 56, "offset": 730}, "end": {"line": 30, "col": 59, "offset": 733}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 33, "col": 36, "offset": 730}, "end": {"line": 33, "col": 49, "offset": 743}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 33, "col": 58, "offset": 730}, "end": {"line": 33, "col": 83, "offset": 755}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml:30:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 30, "col": 56, "offset": 730}, "end": {"line": 30, "col": 59, "offset": 733}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 33, "col": 36, "offset": 730}, "end": {"line": 33, "col": 49, "offset": 743}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 33, "col": 58, "offset": 730}, "end": {"line": 33, "col": 83, "offset": 755}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 54, "col": 91, "offset": 1498}, "end": {"line": 54, "col": 94, "offset": 1501}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml:54:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "start": {"line": 54, "col": 91, "offset": 1498}, "end": {"line": 54, "col": 94, "offset": 1501}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html", "start": {"line": 59, "col": 80, "offset": 0}, "end": {"line": 59, "col": 97, "offset": 17}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html", "start": {"line": 62, "col": 23, "offset": 0}, "end": {"line": 62, "col": 40, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html:59:\n `> f:format.raw()}` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html", "start": {"line": 59, "col": 80, "offset": 0}, "end": {"line": 59, "col": 97, "offset": 17}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html", "start": {"line": 62, "col": 23, "offset": 0}, "end": {"line": 62, "col": 40, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/templates/frontend.html", "start": {"line": 15, "col": 38, "offset": 0}, "end": {"line": 15, "col": 55, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/templates/frontend.html:15:\n `> f:format.raw()}` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/templates/frontend.html", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/templates/frontend.html", "start": {"line": 15, "col": 38, "offset": 0}, "end": {"line": 15, "col": 55, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html", "start": {"line": 40, "col": 27, "offset": 0}, "end": {"line": 40, "col": 44, "offset": 17}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html", "start": {"line": 44, "col": 29, "offset": 0}, "end": {"line": 44, "col": 46, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html:40:\n `> f:format.raw()}` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html", "start": {"line": 40, "col": 27, "offset": 0}, "end": {"line": 40, "col": 44, "offset": 17}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html", "start": {"line": 44, "col": 29, "offset": 0}, "end": {"line": 44, "col": 46, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/templates/frontend.html", "start": {"line": 20, "col": 90, "offset": 0}, "end": {"line": 20, "col": 91, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/templates/frontend.html:20:\n `>` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/templates/frontend.html", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/templates/frontend.html", "start": {"line": 20, "col": 90, "offset": 0}, "end": {"line": 20, "col": 91, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/PageLayout.php", "start": {"line": 41, "col": 1, "offset": 0}, "end": {"line": 41, "col": 9, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/PageLayout.php:41:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/PageLayout.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/PageLayout.php", "start": {"line": 41, "col": 1, "offset": 0}, "end": {"line": 41, "col": 9, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php", "start": {"line": 55, "col": 19, "offset": 0}, "end": {"line": 55, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php", "start": {"line": 57, "col": 9, "offset": 0}, "end": {"line": 57, "col": 27, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php:55:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php", "start": {"line": 55, "col": 19, "offset": 0}, "end": {"line": 55, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php", "start": {"line": 57, "col": 9, "offset": 0}, "end": {"line": 57, "col": 27, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/LoadedBasic.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/LoadedBasic.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/LoadedBasic.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/LoadedBasic.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 61, "col": 19, "offset": 0}, "end": {"line": 61, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 62, "col": 19, "offset": 0}, "end": {"line": 62, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 63, "col": 19, "offset": 0}, "end": {"line": 63, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 64, "col": 19, "offset": 0}, "end": {"line": 64, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 65, "col": 19, "offset": 0}, "end": {"line": 65, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 66, "col": 19, "offset": 0}, "end": {"line": 66, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 154, "col": 75, "offset": 0}, "end": {"line": 154, "col": 78, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 168, "col": 73, "offset": 0}, "end": {"line": 168, "col": 76, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php:61:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 61, "col": 19, "offset": 0}, "end": {"line": 61, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 62, "col": 19, "offset": 0}, "end": {"line": 62, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 63, "col": 19, "offset": 0}, "end": {"line": 63, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 64, "col": 19, "offset": 0}, "end": {"line": 64, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 65, "col": 19, "offset": 0}, "end": {"line": 65, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 66, "col": 19, "offset": 0}, "end": {"line": 66, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 154, "col": 75, "offset": 0}, "end": {"line": 154, "col": 78, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "start": {"line": 168, "col": 73, "offset": 0}, "end": {"line": 168, "col": 76, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 49, "col": 19, "offset": 0}, "end": {"line": 49, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 50, "col": 19, "offset": 0}, "end": {"line": 50, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 51, "col": 19, "offset": 0}, "end": {"line": 51, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 52, "col": 19, "offset": 0}, "end": {"line": 52, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 53, "col": 19, "offset": 0}, "end": {"line": 53, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 54, "col": 19, "offset": 0}, "end": {"line": 54, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php:49:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 49, "col": 19, "offset": 0}, "end": {"line": 49, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 50, "col": 19, "offset": 0}, "end": {"line": 50, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 51, "col": 19, "offset": 0}, "end": {"line": 51, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 52, "col": 19, "offset": 0}, "end": {"line": 52, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 53, "col": 19, "offset": 0}, "end": {"line": 53, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "start": {"line": 54, "col": 19, "offset": 0}, "end": {"line": 54, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/ListContentBlocksCommand.php", "start": {"line": 40, "col": 19, "offset": 0}, "end": {"line": 40, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/ListContentBlocksCommand.php:40:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/ListContentBlocksCommand.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/ListContentBlocksCommand.php", "start": {"line": 40, "col": 19, "offset": 0}, "end": {"line": 40, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php", "start": {"line": 39, "col": 19, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php", "start": {"line": 40, "col": 19, "offset": 0}, "end": {"line": 40, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php:39:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php", "start": {"line": 39, "col": 19, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php", "start": {"line": 40, "col": 19, "offset": 0}, "end": {"line": 40, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 40, "col": 17, "offset": 0}, "end": {"line": 40, "col": 25, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 41, "col": 17, "offset": 0}, "end": {"line": 41, "col": 25, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 42, "col": 17, "offset": 0}, "end": {"line": 42, "col": 25, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 43, "col": 17, "offset": 0}, "end": {"line": 43, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php:39:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 40, "col": 17, "offset": 0}, "end": {"line": 40, "col": 25, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 41, "col": 17, "offset": 0}, "end": {"line": 41, "col": 25, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 42, "col": 17, "offset": 0}, "end": {"line": 42, "col": 25, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "start": {"line": 43, "col": 17, "offset": 0}, "end": {"line": 43, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlocksDataProcessor.php", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 30, "col": 9, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlocksDataProcessor.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlocksDataProcessor.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlocksDataProcessor.php", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 30, "col": 9, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php", "start": {"line": 34, "col": 19, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php:33:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php", "start": {"line": 34, "col": 19, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/GridProcessor.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/GridProcessor.php:32:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/GridProcessor.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/GridProcessor.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/NativeTableCapabilityProxy.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/NativeTableCapabilityProxy.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/NativeTableCapabilityProxy.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/NativeTableCapabilityProxy.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentElementDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentElementDefinition.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentElementDefinition.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentElementDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageIconSet.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageIconSet.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageIconSet.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageIconSet.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageTypeDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageTypeDefinition.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageTypeDefinition.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageTypeDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/RecordTypeDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/RecordTypeDefinition.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/RecordTypeDefinition.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/RecordTypeDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/CompilationResult.php", "start": {"line": 22, "col": 7, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/CompilationResult.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/CompilationResult.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/CompilationResult.php", "start": {"line": 22, "col": 7, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/LanguagePath.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/LanguagePath.php:26:\n `string` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/LanguagePath.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/LanguagePath.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedFieldsResult.php", "start": {"line": 64, "col": 38, "offset": 0}, "end": {"line": 64, "col": 39, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedFieldsResult.php:64:\n `,` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedFieldsResult.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedFieldsResult.php", "start": {"line": 64, "col": 38, "offset": 0}, "end": {"line": 64, "col": 39, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "start": {"line": 46, "col": 9, "offset": 0}, "end": {"line": 46, "col": 27, "offset": 18}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "start": {"line": 47, "col": 19, "offset": 0}, "end": {"line": 47, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "start": {"line": 48, "col": 19, "offset": 0}, "end": {"line": 48, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php:46:\n `protected readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "start": {"line": 46, "col": 9, "offset": 0}, "end": {"line": 46, "col": 27, "offset": 18}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "start": {"line": 47, "col": 19, "offset": 0}, "end": {"line": 47, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "start": {"line": 48, "col": 19, "offset": 0}, "end": {"line": 48, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinition.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinition.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinition.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinition.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinitionCollection.php", "start": {"line": 34, "col": 41, "offset": 0}, "end": {"line": 34, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinitionCollection.php:34:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinitionCollection.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinitionCollection.php", "start": {"line": 34, "col": 41, "offset": 0}, "end": {"line": 34, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TcaFieldDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TcaFieldDefinition.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TcaFieldDefinition.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TcaFieldDefinition.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/BaseFieldTypeRegistry.php", "start": {"line": 22, "col": 7, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/BaseFieldTypeRegistry.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/BaseFieldTypeRegistry.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/BaseFieldTypeRegistry.php", "start": {"line": 22, "col": 7, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldType.php", "start": {"line": 24, "col": 1, "offset": 0}, "end": {"line": 24, "col": 9, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldType.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldType.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldType.php", "start": {"line": 24, "col": 1, "offset": 0}, "end": {"line": 24, "col": 9, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php", "start": {"line": 27, "col": 9, "offset": 0}, "end": {"line": 27, "col": 16, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php", "start": {"line": 27, "col": 9, "offset": 0}, "end": {"line": 27, "col": 16, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Form/FormDataProvider/AllowedRecordTypesInCollection.php", "start": {"line": 25, "col": 7, "offset": 0}, "end": {"line": 25, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Form/FormDataProvider/AllowedRecordTypesInCollection.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Form/FormDataProvider/AllowedRecordTypesInCollection.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Form/FormDataProvider/AllowedRecordTypesInCollection.php", "start": {"line": 25, "col": 7, "offset": 0}, "end": {"line": 25, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/LanguageFileGenerator.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/LanguageFileGenerator.php:32:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/LanguageFileGenerator.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/LanguageFileGenerator.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/SqlGenerator.php", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 9, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/SqlGenerator.php:32:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/SqlGenerator.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/SqlGenerator.php", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 9, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/TcaGenerator.php", "start": {"line": 102, "col": 1, "offset": 0}, "end": {"line": 102, "col": 9, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/TcaGenerator.php:102:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/TcaGenerator.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/TcaGenerator.php", "start": {"line": 102, "col": 1, "offset": 0}, "end": {"line": 102, "col": 9, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 75, "col": 9, "offset": 0}, "end": {"line": 75, "col": 27, "offset": 18}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 76, "col": 19, "offset": 0}, "end": {"line": 76, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 77, "col": 19, "offset": 0}, "end": {"line": 77, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 78, "col": 19, "offset": 0}, "end": {"line": 78, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 79, "col": 19, "offset": 0}, "end": {"line": 79, "col": 27, "offset": 8}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 80, "col": 19, "offset": 0}, "end": {"line": 80, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php:75:\n `protected readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 75, "col": 9, "offset": 0}, "end": {"line": 75, "col": 27, "offset": 18}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 76, "col": 19, "offset": 0}, "end": {"line": 76, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 77, "col": 19, "offset": 0}, "end": {"line": 77, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 78, "col": 19, "offset": 0}, "end": {"line": 78, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 79, "col": 19, "offset": 0}, "end": {"line": 79, "col": 27, "offset": 8}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "start": {"line": 80, "col": 19, "offset": 0}, "end": {"line": 80, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/LoadedContentBlock.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/LoadedContentBlock.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/LoadedContentBlock.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/LoadedContentBlock.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/AutomaticLanguageSource.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/AutomaticLanguageSource.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/AutomaticLanguageSource.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/AutomaticLanguageSource.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/FieldCapability.php", "start": {"line": 25, "col": 7, "offset": 0}, "end": {"line": 25, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/FieldCapability.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/FieldCapability.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/FieldCapability.php", "start": {"line": 25, "col": 7, "offset": 0}, "end": {"line": 25, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/TcaField.php", "start": {"line": 25, "col": 7, "offset": 0}, "end": {"line": 25, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/TcaField.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/TcaField.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/TcaField.php", "start": {"line": 25, "col": 7, "offset": 0}, "end": {"line": 25, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchema.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchema.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchema.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchema.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchemaFactory.php", "start": {"line": 41, "col": 9, "offset": 0}, "end": {"line": 41, "col": 27, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchemaFactory.php:41:\n `protected readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchemaFactory.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchemaFactory.php", "start": {"line": 41, "col": 9, "offset": 0}, "end": {"line": 41, "col": 27, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 65, "col": 60, "offset": 0}, "end": {"line": 65, "col": 63, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 66, "col": 68, "offset": 0}, "end": {"line": 66, "col": 71, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 67, "col": 77, "offset": 0}, "end": {"line": 67, "col": 80, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 68, "col": 78, "offset": 0}, "end": {"line": 68, "col": 81, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 69, "col": 83, "offset": 0}, "end": {"line": 69, "col": 86, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 70, "col": 92, "offset": 0}, "end": {"line": 70, "col": 95, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 71, "col": 70, "offset": 0}, "end": {"line": 71, "col": 73, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 72, "col": 75, "offset": 0}, "end": {"line": 72, "col": 78, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 73, "col": 78, "offset": 0}, "end": {"line": 73, "col": 81, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 74, "col": 98, "offset": 0}, "end": {"line": 74, "col": 101, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 75, "col": 139, "offset": 0}, "end": {"line": 75, "col": 142, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 76, "col": 102, "offset": 0}, "end": {"line": 76, "col": 105, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 77, "col": 84, "offset": 0}, "end": {"line": 77, "col": 87, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 78, "col": 49, "offset": 0}, "end": {"line": 78, "col": 52, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 85, "col": 70, "offset": 0}, "end": {"line": 85, "col": 73, "offset": 3}}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 86, "col": 66, "offset": 0}, "end": {"line": 86, "col": 69, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php:65:\n `...` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 65, "col": 60, "offset": 0}, "end": {"line": 65, "col": 63, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 66, "col": 68, "offset": 0}, "end": {"line": 66, "col": 71, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 67, "col": 77, "offset": 0}, "end": {"line": 67, "col": 80, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 68, "col": 78, "offset": 0}, "end": {"line": 68, "col": 81, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 69, "col": 83, "offset": 0}, "end": {"line": 69, "col": 86, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 70, "col": 92, "offset": 0}, "end": {"line": 70, "col": 95, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 71, "col": 70, "offset": 0}, "end": {"line": 71, "col": 73, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 72, "col": 75, "offset": 0}, "end": {"line": 72, "col": 78, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 73, "col": 78, "offset": 0}, "end": {"line": 73, "col": 81, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 74, "col": 98, "offset": 0}, "end": {"line": 74, "col": 101, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 75, "col": 139, "offset": 0}, "end": {"line": 75, "col": 142, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 76, "col": 102, "offset": 0}, "end": {"line": 76, "col": 105, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 77, "col": 84, "offset": 0}, "end": {"line": 77, "col": 87, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 78, "col": 49, "offset": 0}, "end": {"line": 78, "col": 52, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 85, "col": 70, "offset": 0}, "end": {"line": 85, "col": 73, "offset": 3}}, {"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "start": {"line": 86, "col": 66, "offset": 0}, "end": {"line": 86, "col": 69, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Update/ContentBlockFolderStructureMigration.php", "start": {"line": 26, "col": 1, "offset": 0}, "end": {"line": 26, "col": 9, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Update/ContentBlockFolderStructureMigration.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Update/ContentBlockFolderStructureMigration.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Update/ContentBlockFolderStructureMigration.php", "start": {"line": 26, "col": 1, "offset": 0}, "end": {"line": 26, "col": 9, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/UserFunction/ContentWhere.php", "start": {"line": 26, "col": 1, "offset": 0}, "end": {"line": 26, "col": 9, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/UserFunction/ContentWhere.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/UserFunction/ContentWhere.php", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/UserFunction/ContentWhere.php", "start": {"line": 26, "col": 1, "offset": 0}, "end": {"line": 26, "col": 9, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/RecordDefault/Header.html", "start": {"line": 3, "col": 22, "offset": 0}, "end": {"line": 3, "col": 39, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/RecordDefault/Header.html:3:\n `> f:format.raw()}` was unexpected", "path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/RecordDefault/Header.html", "spans": [{"file": "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/RecordDefault/Header.html", "start": {"line": 3, "col": 22, "offset": 0}, "end": {"line": 3, "col": 39, "offset": 17}}]}], "paths": {"scanned": ["downloaded_repos/FriendsOfTYPO3_content-blocks/.ddev/addon-metadata/phpmyadmin/manifest.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/.ddev/commands/host/phpmyadmin", "downloaded_repos/FriendsOfTYPO3_content-blocks/.ddev/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/.ddev/docker-compose.phpmyadmin-norouter.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/.ddev/docker-compose.phpmyadmin.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/.ddev/homeadditions/.bashrc.d/path.sh", "downloaded_repos/FriendsOfTYPO3_content-blocks/.editorconfig", "downloaded_repos/FriendsOfTYPO3_content-blocks/.gitattributes", "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/test-documentation.yml", "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/tests.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/.gitignore", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/Scripts/runTests.sh", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/config.example/sites/main/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/config.example/system/additional.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/.gitignore", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/Configuration/Sets/Example/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/Configuration/Sets/Example/setup.typoscript", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/assets/accordion_item.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/assets/frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/assets/frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/assets/preview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/accordion/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/assets/card_group_item.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/assets/frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/assets/preview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/partials/LinkWrap.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/assets/frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/assets/preview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/assets/frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/assets/icon_group_item.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/assets/preview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/assets/Library/SwiffySlider/swiffy-slider.min.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/assets/frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/assets/preview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/assets/frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/assets/frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/assets/tabs_item.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/text/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/text/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/text/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/text/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/FileTypes/file-type-image/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/FileTypes/file-type-image/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/PageTypes/example/assets/icon-hide-in-menu.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/PageTypes/example/assets/icon-root.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/PageTypes/example/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/PageTypes/example/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/PageTypes/example/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/notype/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/notype/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/notype/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/record1/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/record1/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/record1/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/record2/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/record2/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/RecordTypes/record2/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/README.md", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/Resources/Private/Templates/Pages/Default.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/Resources/Public/Icons/Extension.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/composer.json", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/php-cs-fixer/config.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/php-cs-fixer/header-comment.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/phpstan/phpstan-constants.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/phpstan/phpstan.neon", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/phpunit/FunctionalTests.xml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/phpunit/FunctionalTestsBootstrap.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/phpunit/UnitTests.xml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/phpunit/UnitTestsBootstrap.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/testing-docker/docker-compose.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/PageLayout.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/PreviewRenderer.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/RootPathsSettings.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsRegistry.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsService.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/LoadedBasic.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Builder/ConfigBuilder.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Builder/ContentBlockBuilder.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Builder/DefaultsLoader.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/ListContentBlocksCommand.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockData.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecoratorSession.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlocksDataProcessor.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessorSession.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentTypeResolver.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/GridFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/GridProcessor.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/RelationGrid.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/RenderedGridItem.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ResolvedContentBlockDataRelation.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/LabelCapability.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/NativeTableCapabilityProxy.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/RootLevelCapability.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/RootLevelType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/SystemFieldPalettesInterface.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/TableDefinitionCapability.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentElementDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentTypeDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentTypeDefinitionCollection.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentTypeIcon.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentTypeInterface.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageIconSet.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageTypeDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/RecordTypeDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/CompilationResult.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/ContentBlockCompiler.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/ContentTypeFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/FlexFormSubType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/LanguagePath.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/PrefixType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedContentType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedFieldsResult.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedTableDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessingInput.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TcaFieldFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/UniqueIdentifierCreator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/FlexForm/ContainerDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/FlexForm/FlexFormDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/FlexForm/SectionDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/FlexForm/SheetDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/PaletteDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/PaletteDefinitionCollection.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/SqlColumnDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/SqlColumnDefinitionCollection.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TCA/LinebreakDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TCA/TabDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinitionCollection.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TcaFieldDefinition.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TcaFieldDefinitionCollection.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DependencyInjection/FieldTypeCompilerPass.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/AbstractFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/BaseFieldTypeRegistry.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/BaseFieldTypeRegistryFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/CategoryFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/CheckboxFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/CollectionFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/ColorFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/DateTimeFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/EmailFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeInterface.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FileFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FlexFormFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FolderFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/ImageManipulationFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/JsonFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/LanguageFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/LinebreakFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/LinkFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/NumberFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/PaletteFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/PassFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/PasswordFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/RadioFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/RelationFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/SelectFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/SelectNumberFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/SlugFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/SpecialFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/TabFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/TextFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/TextareaFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/UuidFieldType.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/WithCommonProperties.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/WithCustomProperties.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/WithNullableProperty.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Form/FormDataProvider/AllowedRecordTypeFilter.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Form/FormDataProvider/AllowedRecordTypesInCollection.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/FlexFormGenerator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/HtmlTemplateCodeGenerator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/LanguageFileGenerator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/SqlGenerator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/TcaGenerator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/AssetPublisher.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/LoadedContentBlock.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/AutomaticLanguageKeysRegistry.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/AutomaticLanguageSource.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/ContentBlockRegistry.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/LanguageFileRegistry.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/LanguageFileRegistryFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/FieldCapability.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/LanguageAwareSchemaCapability.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/SchemaCapabilityInterface.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Exception/UndefinedFieldException.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Exception/UndefinedSchemaException.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/FieldCollection.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/TcaField.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/TcaFieldTypeInterface.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/FieldTypeResolver.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchema.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchemaFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Service/Icon/ContentTypeIconResolver.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Service/Icon/ContentTypeIconResolverInput.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Service/Icon/IconProcessor.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Service/PackageResolver.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Service/SystemExtensionAvailability.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Update/ContentBlockFolderStructureMigration.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/UserFunction/ContentWhere.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Utility/ContentBlockPathUtility.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Utility/LocalLangPathUtility.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Validation/ContentBlockNameValidator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Validation/PageTypeNameValidator.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ViewHelpers/AssetPathViewHelper.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ViewHelpers/LanguagePathViewHelper.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ViewHelpers/Link/EditRecordViewHelper.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Configuration/Services.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Configuration/Services.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/ContentBlocks/Basics/Categories.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/ContentBlocks/Basics/ContentElements/Appearance.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/ContentBlocks/Basics/ContentElements/Header.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/ContentBlocks/Basics/ContentElements/Links.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/ContentBlocks/Basics/PageTypes/External.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/ContentBlocks/Basics/PageTypes/Shortcut.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/AutomaticLanguageKeys/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/BackendPreview/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/Basics/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/ContentElements/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/FileTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/NestedContentElements/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/NestedContentElements/NestedContentPreview.png", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/PageTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/Prefixing/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/RecordTypes/CustomNewsRecord.png", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/RecordTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/ReuseExistingFields/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/TypeOverrides/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/_Images/file-type-image-palette.jpg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/API/_Images/page-type-preview.jpg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Appendix/CoreContentTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Appendix/History/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Appendix/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Appendix/TcaTypeMapping/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/ChangeLog/1.0/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/ChangeLog/1.1/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/ChangeLog/1.2/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/ChangeLog/1.3/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/ChangeLog/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Commands/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Commands/LanguageGenerate/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Commands/List/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Commands/Make/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Commands/PublishAssets/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Definition/Assets/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Definition/ConfigYaml/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Definition/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Definition/Language/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Definition/Templates/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/DeveloperCorner/DataProcessing/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/DeveloperCorner/ExtendFieldTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/DeveloperCorner/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/FAQ/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/AddingNewGroups/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/CreateExtbasePlugin/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/ExtendContentBlockTCA/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/ExtendContentBlockTypoScript/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/FluidStyledContent/ContentBlockTypoScript.png", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/FluidStyledContent/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/NoFluidStyledContent/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/PageTypeTemplates/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Guides/SharedPartials/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Includes.rst.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Installation/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Introduction/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/KnownProblems/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Migrations/ContentBlocks12/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Migrations/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Migrations/Mask/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Migrations/Mask/MaskExample.png", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Registration/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Sitemap.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/Templating/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/ContentTypes/ContentElements/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/ContentTypes/FileTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/ContentTypes/PageTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/ContentTypes/RecordTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Basic/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Category/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Checkbox/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Collection/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Color/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/DateTime/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Email/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/File/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/FlexForm/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/FlexForm/Section/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/FlexForm/Sheet/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Folder/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Json/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Language/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Linebreak/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Link/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Number/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Palette/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Pass/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Password/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Radio/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Relation/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Select/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/SelectNumber/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Slug/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Tab/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Text/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Textarea/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/FieldTypes/Uuid/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/YamlReference/Root/Index.rst", "downloaded_repos/FriendsOfTYPO3_content-blocks/Documentation/guides.xml", "downloaded_repos/FriendsOfTYPO3_content-blocks/LICENSE", "downloaded_repos/FriendsOfTYPO3_content-blocks/Makefile", "downloaded_repos/FriendsOfTYPO3_content-blocks/README.md", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Language/locallang.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Layouts/Preview/Content/Preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Layouts/Preview/Footer/Preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Layouts/Preview/Header/Preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/Grid.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/Record.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/RecordDefault/Header.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Public/Icons/DefaultContentElementIcon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Public/Icons/DefaultPageTypeIcon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Public/Icons/DefaultPageTypeIconHideInMenu.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Public/Icons/DefaultRecordTypeIcon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Public/Icons/Extension.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/.gitignore", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/assets/EditorPreview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/assets/Frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/assets/Frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/ContentBlocks/ContentElements/content-element-a/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_a/composer.json", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/Configuration/TCA/native_record.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/assets/EditorPreview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/assets/Frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/assets/Frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/templates/backend-preview.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/ContentElements/content-element-b/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/PageTypes/example/assets/icon-hide-in-menu.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/PageTypes/example/assets/icon-root.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/PageTypes/example/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/PageTypes/example/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/PageTypes/example/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ContentBlocks/RecordTypes/test-record/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/Resources/Private/Templates/Pages/Default.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/composer.json", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_b/ext_tables.sql", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/Basics/SubFolder/Simple.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple/assets/EditorPreview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple/assets/Frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple/assets/Frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-relation/assets/EditorPreview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-relation/assets/Frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-relation/assets/Frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-relation/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-relation/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-relation/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-relation/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-vendor/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-vendor/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-vendor/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-vendor/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-with-basics/assets/EditorPreview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-with-basics/assets/Frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-with-basics/assets/Frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-with-basics/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-with-basics/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-with-basics/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple-with-basics/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple2/assets/EditorPreview.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple2/assets/Frontend.css", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple2/assets/Frontend.js", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple2/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple2/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple2/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/ContentElements/simple2/templates/frontend.html", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/RecordTypes/record1/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/RecordTypes/record1/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ContentBlocks/RecordTypes/record2/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/composer.json", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/Extensions/test_content_blocks_c/ext_localconf.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/TestFolder/file.jpg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/TestFolder/sub/subfile.jpg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Fixtures/be_users.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/CacheCommandTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/CreateContentBlockCommandTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Extensions/command_test/ContentBlocks/ContentElements/command-test-assets/assets/icon.svg", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Extensions/command_test/ContentBlocks/ContentElements/command-test-assets/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Extensions/command_test/ContentBlocks/ContentElements/command-test-language-1/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Extensions/command_test/ContentBlocks/ContentElements/command-test-language-2/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Extensions/command_test/composer.json", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Language/1_GenerateLanguageFileGenerateWithContentBlock.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Language/2_GenerateLanguageFileGenerateWithExtension.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Language/3_GenerateLanguageFilePrintWithContentBlock.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Language/InvalidXmlCharactersAreEscaped.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Table/1_ListWithDefaultOrder.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Table/2_ListWithVendorOrder.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Table/3_ListWithNameOrder.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Table/4_ListWithTableOrder.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Table/5_ListWithTypeNameOrder.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Table/6_ListWithContentTypeOrder.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/Fixtures/Table/7_ListWithExtensionOrder.txt", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/GenerateLanguageFileCommandTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/ListContentBlocksCommandTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Command/PublishAssetsCommandTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/ContentBlockFrontendRenderingTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/base.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/categories.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/circular_relation.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/collections.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/collections_one_to_one.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/collections_recursive.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/file_references.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/foreign_table_select.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/foreign_table_select_multiple.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/frontend_simple_element.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/mixed-relation.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/pass.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/relation.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/relation_tt-content.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/select-static-relation.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/DataSet/vendor_prefix.csv", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Frontend/Fixtures/frontend.typoscript", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_1/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_2/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_3/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_3/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_4/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_4/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_5/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_5/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_6/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_6/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_7/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_7/language/labels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/ContentBlocks/ContentElements/test_8/config.yaml", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Extensions/language_test/composer.json", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/1_EmptyLanguageFile.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/2_EmptyLanguageFileWithLabels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/3_LanguageFileWithoutLabels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/4_LanguageFileWithLabels.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/5_LanguageFileWithLabelsWithCustomTranslations.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/6_LanguageFileWithLabelsWithDescriptions.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/7_LanguageFileWithoutLabelsWithoutDescriptions.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/Fixtures/Language/8_InvalidXmlCharactersAreEscaped.xlf", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/IconGeneratorTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/LanguageFileGeneratorTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Generator/TcaGeneratorTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/Registry/ContentBlockRegistryTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/ViewHelpers/AssetPathViewHelperTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Functional/ViewHelpers/LanguagePathViewHelperTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Basics/BasicsServiceTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Builder/ConfigBuilderTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Definition/Factory/ContentBlockCompilerTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Definition/Factory/DisplayCondPrefixEvaluationTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Definition/Factory/UniqueIdentifierCreatorTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Definition/TableDefinitionCollectionTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/CategoryFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/CheckboxFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/CollectionFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/ColorFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/DateTimeFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/EmailFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/FileFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/FlexFormFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/FolderFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/JsonFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/LanguageFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/LinkFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/NumberFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/PassFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/PasswordFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/RadioFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/RelationFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/SelectFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/SelectNumberFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/SlugFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/TextFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/TextareaFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/FieldTypes/UuidFieldTypeTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Fixtures/FieldTypeRegistryTestFactory.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Fixtures/NoopLanguageFileRegistry.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Fixtures/TestSystemExtensionAvailability.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Form/FormDataProvider/AllowedRecordTypeFilterTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Generator/SqlGeneratorTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Generator/TcaGeneratorTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Registry/ContentBlockRegistryTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Service/ContentElementParentFieldServiceTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Validation/ContentBlockNameValidatorTest.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/composer.json", "downloaded_repos/FriendsOfTYPO3_content-blocks/ext_emconf.php", "downloaded_repos/FriendsOfTYPO3_content-blocks/ext_localconf.php"], "skipped": [{"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/.github/workflows/publish.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/card_group/templates/frontend.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/cta/templates/frontend.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/icon_group/templates/frontend.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/imageslider/assets/Library/SwiffySlider/swiffy-slider.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Build/content-blocks-examples/ContentBlocks/ContentElements/tabs/templates/frontend.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Backend/Preview/PageLayout.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/BasicsLoader.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Basics/LoadedBasic.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/CreateContentBlockCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/GenerateLanguageFileCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/ListContentBlocksCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Command/PublishAssetsCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlockDataDecorator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentBlocksDataProcessor.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/ContentObjectProcessor.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/DataProcessing/GridProcessor.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Capability/NativeTableCapabilityProxy.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/ContentElementDefinition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageIconSet.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/PageTypeDefinition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/ContentType/RecordTypeDefinition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/CompilationResult.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/LanguagePath.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/Processing/ProcessedFieldsResult.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/Factory/TableDefinitionCollectionFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TableDefinitionCollection.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Definition/TcaFieldDefinition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/BaseFieldTypeRegistry.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldType.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/FieldType/FieldTypeRegistry.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Form/FormDataProvider/AllowedRecordTypesInCollection.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/LanguageFileGenerator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/SqlGenerator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Generator/TcaGenerator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/ContentBlockLoader.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Loader/LoadedContentBlock.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Registry/AutomaticLanguageSource.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Capability/FieldCapability.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/Field/TcaField.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchema.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Schema/SimpleTcaSchemaFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/ServiceProvider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/Update/ContentBlockFolderStructureMigration.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Classes/UserFunction/ContentWhere.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/FriendsOfTYPO3_content-blocks/Resources/Private/Partials/PageLayout/RecordDefault/Header.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.777223825454712, "profiling_times": {"config_time": 5.873248815536499, "core_time": 4.526081800460815, "ignores_time": 0.0017669200897216797, "total_time": 10.402374744415283}, "parsing_time": {"total_time": 3.3969838619232178, "per_file_time": {"mean": 0.011437656100751582, "std_dev": 0.001615949475727826}, "very_slow_stats": {"time_ratio": 0.16244439651563314, "count_ratio": 0.003367003367003367}, "very_slow_files": [{"fpath": "downloaded_repos/FriendsOfTYPO3_content-blocks/Tests/Unit/Generator/TcaGeneratorTest.php", "ftime": 0.5518209934234619}]}, "scanning_time": {"total_time": 8.115143299102783, "per_file_time": {"mean": 0.005784136350037625, "std_dev": 0.0005878515509386338}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.2151854038238525, "per_file_and_rule_time": {"mean": 0.000659709774062895, "std_dev": 3.8765269985694865e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.11580586433410645, "per_def_and_rule_time": {"mean": 0.000845298279810996, "std_dev": 7.930118723583133e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1096270592}, "engine_requested": "OSS", "skipped_rules": []}