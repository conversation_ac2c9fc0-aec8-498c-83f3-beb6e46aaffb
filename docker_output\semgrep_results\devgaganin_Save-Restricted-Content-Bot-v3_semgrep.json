{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/Dockerfile", "start": {"line": 14, "col": 1, "offset": 364}, "end": {"line": 14, "col": 51, "offset": 414}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD flask run -h 0.0.0.0 -p 5000 & python3 main.py", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "path": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/app.py", "start": {"line": 18, "col": 5, "offset": 555}, "end": {"line": 18, "col": 39, "offset": 589}, "extra": {"message": "Running flask app with host 0.0.0.0 could expose the server publicly.", "metadata": {"cwe": ["CWE-668: Exposure of Resource to Wrong Sphere"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "shortlink": "https://sg.run/eLby"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/main.py", "start": {"line": 17, "col": 18, "offset": 526}, "end": {"line": 17, "col": 62, "offset": 570}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/Dockerfile", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/LICENSE", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/Procfile", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/README.md", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/app.json", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/app.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/config.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/heroku.yml", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/main.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/batch.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/login.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/pay.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/premium.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/settings.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/start.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/stats.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/ytdl.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/requirements.txt", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/shared_client.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/templates/welcome.html", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/utils/custom_filters.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/utils/encrypt.py", "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/utils/func.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.779944896697998, "profiling_times": {"config_time": 5.989733695983887, "core_time": 3.224327802658081, "ignores_time": 0.0016448497772216797, "total_time": 9.216476678848267}, "parsing_time": {"total_time": 0.5184469223022461, "per_file_time": {"mean": 0.02728668012117085, "std_dev": 0.00033121974092392166}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.4929745197296143, "per_file_time": {"mean": 0.05062281912651614, "std_dev": 0.01680410599682601}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.2334721088409424, "per_file_and_rule_time": {"mean": 0.005556180670454695, "std_dev": 0.00019044889845859137}, "very_slow_stats": {"time_ratio": 0.11781700675183437, "count_ratio": 0.0045045045045045045}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/ytdl.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.1453239917755127}]}, "tainting_time": {"total_time": 0.47311997413635254, "per_def_and_rule_time": {"mean": 0.0009856666127840677, "std_dev": 1.0236941951369016e-05}, "very_slow_stats": {"time_ratio": 0.11791520800399515, "count_ratio": 0.0020833333333333333}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/devgaganin_Save-Restricted-Content-Bot-v3/plugins/batch.py", "fline": 242, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.05578804016113281}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}