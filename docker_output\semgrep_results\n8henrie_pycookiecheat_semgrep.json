{"version": "1.130.0", "results": [{"check_id": "python.cryptography.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/n8henrie_pycookiecheat/src/pycookiecheat/chrome.py", "start": {"line": 309, "col": 19, "offset": 10557}, "end": {"line": 309, "col": 23, "offset": 10561}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "SHA256", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "references": ["https://cryptography.io/en/latest/hazmat/primitives/cryptographic-hashes/#sha-1", "https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["cryptography"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "functional-categories": ["crypto::search::symmetric-algorithm::cryptography"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.cryptography.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/J9Qy"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/n8henrie_pycookiecheat/.editorconfig", "downloaded_repos/n8henrie_pycookiecheat/.github/FUNDING.yml", "downloaded_repos/n8henrie_pycookiecheat/.github/ISSUE_TEMPLATE.md", "downloaded_repos/n8henrie_pycookiecheat/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/n8henrie_pycookiecheat/.github/dependabot.yml", "downloaded_repos/n8henrie_pycookiecheat/.github/stale.yml", "downloaded_repos/n8henrie_pycookiecheat/.github/workflows/python-package.yml", "downloaded_repos/n8henrie_pycookiecheat/.github/workflows/python-publish.yml", "downloaded_repos/n8henrie_pycookiecheat/.gitignore", "downloaded_repos/n8henrie_pycookiecheat/.python-version", "downloaded_repos/n8henrie_pycookiecheat/.readthedocs.yml", "downloaded_repos/n8henrie_pycookiecheat/AUTHORS.md", "downloaded_repos/n8henrie_pycookiecheat/CHANGELOG.md", "downloaded_repos/n8henrie_pycookiecheat/CONTRIBUTING.md", "downloaded_repos/n8henrie_pycookiecheat/LICENSE", "downloaded_repos/n8henrie_pycookiecheat/README.md", "downloaded_repos/n8henrie_pycookiecheat/TODO.txt", "downloaded_repos/n8henrie_pycookiecheat/flake.lock", "downloaded_repos/n8henrie_pycookiecheat/flake.nix", "downloaded_repos/n8henrie_pycookiecheat/pyproject.toml", "downloaded_repos/n8henrie_pycookiecheat/src/pycookiecheat/__init__.py", "downloaded_repos/n8henrie_pycookiecheat/src/pycookiecheat/__main__.py", "downloaded_repos/n8henrie_pycookiecheat/src/pycookiecheat/chrome.py", "downloaded_repos/n8henrie_pycookiecheat/src/pycookiecheat/common.py", "downloaded_repos/n8henrie_pycookiecheat/src/pycookiecheat/firefox.py", "downloaded_repos/n8henrie_pycookiecheat/tox.ini"], "skipped": [{"path": "downloaded_repos/n8henrie_pycookiecheat/tests/test_chrome.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/n8henrie_pycookiecheat/tests/test_common.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/n8henrie_pycookiecheat/tests/test_firefox.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8395111560821533, "profiling_times": {"config_time": 6.493063688278198, "core_time": 2.995978832244873, "ignores_time": 0.0017015933990478516, "total_time": 9.491475105285645}, "parsing_time": {"total_time": 0.15939593315124512, "per_file_time": {"mean": 0.014490539377385918, "std_dev": 1.558224190387042e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.9795794486999512, "per_file_time": {"mean": 0.015548880138094464, "std_dev": 0.0026574755464873917}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.3612799644470215, "per_file_and_rule_time": {"mean": 0.001400309939717138, "std_dev": 1.6146453568925312e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.08942365646362305, "per_def_and_rule_time": {"mean": 0.0006932066392528918, "std_dev": 1.2649717954245548e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}