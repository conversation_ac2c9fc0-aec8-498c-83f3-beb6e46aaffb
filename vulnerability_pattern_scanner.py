#!/usr/bin/env python3

"""
Vulnerability Pattern Scanner
============================

This scanner identifies GitHub projects based on specific vulnerability patterns
that have been commonly exploited in recent CVEs (2023-2025). It focuses on
reducing false positives by targeting only the most vulnerable patterns.
"""

import requests
import json
import csv
import os
import re
from datetime import datetime, timedelta

# Configuration
PATTERN_CONFIG = {
    "github_token": os.getenv("GITHUB_TOKEN", "your_github_token_here"),
    "output_file": "vulnerability_patterns_found.csv",
    "detailed_log": "pattern_analysis.json",
    "max_repos_per_pattern": 50
}

HEADERS = {"Authorization": f"token {PATTERN_CONFIG['github_token']}"}

# Vulnerability patterns based on recent CVE analysis
VULNERABILITY_PATTERNS = {
    "file_upload_vulnerabilities": {
        "description": "File upload functionality without proper validation",
        "search_terms": [
            "file upload php",
            "image upload vulnerability",
            "document upload security",
            "media upload filter",
            "avatar upload validation"
        ],
        "code_patterns": [
            "move_uploaded_file",
            "$_FILES",
            "file_get_contents($_FILES",
            "upload_max_filesize",
            "tmp_name"
        ],
        "risk_score": 95
    },
    
    "sql_injection_prone": {
        "description": "Database queries vulnerable to SQL injection",
        "search_terms": [
            "mysql_query php",
            "SELECT * FROM users WHERE",
            "mysqli_query vulnerable",
            "PDO query injection",
            "database search filter"
        ],
        "code_patterns": [
            "mysql_query($_GET",
            "mysqli_query($_POST",
            "SELECT.*WHERE.*\\$_",
            "query.*\\$_GET",
            "WHERE.*\\$_POST"
        ],
        "risk_score": 90
    },
    
    "authentication_bypass": {
        "description": "Weak authentication mechanisms",
        "search_terms": [
            "admin login bypass",
            "authentication vulnerability",
            "password reset token",
            "session management php",
            "jwt token validation"
        ],
        "code_patterns": [
            "if.*\\$_SESSION.*admin",
            "password.*md5",
            "auth.*\\$_COOKIE",
            "login.*\\$_GET",
            "admin.*\\$_REQUEST"
        ],
        "risk_score": 85
    },
    
    "command_injection": {
        "description": "System command execution vulnerabilities",
        "search_terms": [
            "exec php vulnerability",
            "system command injection",
            "shell_exec security",
            "passthru exploit",
            "eval base64_decode"
        ],
        "code_patterns": [
            "exec\\(\\$_",
            "system\\(\\$_",
            "shell_exec\\(\\$_",
            "passthru\\(\\$_",
            "eval\\(.*\\$_"
        ],
        "risk_score": 98
    },
    
    "path_traversal": {
        "description": "Directory traversal vulnerabilities",
        "search_terms": [
            "file include vulnerability",
            "path traversal php",
            "directory listing security",
            "file download exploit",
            "include_once vulnerability"
        ],
        "code_patterns": [
            "include.*\\$_GET",
            "require.*\\$_POST",
            "file_get_contents.*\\$_",
            "readfile.*\\$_",
            "fopen.*\\$_"
        ],
        "risk_score": 88
    },
    
    "xss_vulnerabilities": {
        "description": "Cross-site scripting vulnerabilities",
        "search_terms": [
            "echo $_GET php",
            "print $_POST vulnerability",
            "javascript injection php",
            "html output security",
            "user input display"
        ],
        "code_patterns": [
            "echo.*\\$_GET",
            "print.*\\$_POST",
            "\\?>.*\\$_",
            "innerHTML.*\\$_",
            "document.write.*\\$_"
        ],
        "risk_score": 75
    },
    
    "deserialization_attacks": {
        "description": "Unsafe deserialization vulnerabilities",
        "search_terms": [
            "unserialize php vulnerability",
            "pickle python security",
            "json decode exploit",
            "object deserialization",
            "serialize user input"
        ],
        "code_patterns": [
            "unserialize\\(\\$_",
            "pickle.loads",
            "json_decode.*\\$_",
            "yaml.load",
            "eval.*json"
        ],
        "risk_score": 92
    }
}

def search_github_code(pattern_name, pattern_data):
    """
    Search GitHub for code patterns that indicate vulnerabilities
    """
    results = []
    
    for search_term in pattern_data["search_terms"]:
        print(f"  🔍 Searching: {search_term}")
        
        params = {
            "q": search_term,
            "sort": "indexed",
            "order": "desc",
            "per_page": 30
        }
        
        try:
            response = requests.get(
                "https://api.github.com/search/code",
                headers=HEADERS,
                params=params
            )
            
            if response.status_code == 200:
                items = response.json().get("items", [])
                for item in items:
                    repo_info = item.get("repository", {})
                    
                    # Skip if already found this repo for this pattern
                    if any(r["repo_name"] == repo_info.get("full_name") for r in results):
                        continue
                    
                    result = {
                        "pattern": pattern_name,
                        "repo_name": repo_info.get("full_name"),
                        "repo_url": repo_info.get("html_url"),
                        "file_path": item.get("path"),
                        "file_url": item.get("html_url"),
                        "search_term": search_term,
                        "stars": repo_info.get("stargazers_count", 0),
                        "language": repo_info.get("language"),
                        "risk_score": pattern_data["risk_score"],
                        "description": pattern_data["description"]
                    }
                    results.append(result)
                    
                    if len(results) >= PATTERN_CONFIG["max_repos_per_pattern"]:
                        break
            
            elif response.status_code == 403:
                print(f"    ⚠️ Rate limited, skipping remaining searches for {pattern_name}")
                break
            else:
                print(f"    ❌ Error {response.status_code}: {response.text}")
        
        except Exception as e:
            print(f"    ❌ Exception: {e}")
        
        if len(results) >= PATTERN_CONFIG["max_repos_per_pattern"]:
            break
    
    return results

def analyze_repository_details(repo_name):
    """
    Get additional details about a repository to assess vulnerability likelihood
    """
    try:
        response = requests.get(
            f"https://api.github.com/repos/{repo_name}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            repo_data = response.json()
            return {
                "stars": repo_data.get("stargazers_count", 0),
                "forks": repo_data.get("forks_count", 0),
                "issues": repo_data.get("open_issues_count", 0),
                "last_updated": repo_data.get("pushed_at"),
                "created_at": repo_data.get("created_at"),
                "description": repo_data.get("description", ""),
                "topics": repo_data.get("topics", []),
                "language": repo_data.get("language"),
                "size": repo_data.get("size", 0)
            }
    except:
        pass
    
    return {}

def calculate_vulnerability_score(result, repo_details):
    """
    Calculate a comprehensive vulnerability score for a repository
    """
    base_score = result["risk_score"]
    
    # Adjust based on repository characteristics
    stars = repo_details.get("stars", 0)
    issues = repo_details.get("issues", 0)
    
    # Popular but not enterprise-maintained repos are higher risk
    if 50 <= stars <= 2000:
        base_score += 5
    elif stars > 5000:  # Enterprise projects usually better maintained
        base_score -= 10
    
    # High issue count indicates potential problems
    if issues > 50:
        base_score += min(issues // 10, 15)
    
    # Check if recently updated (maintained projects are lower risk)
    try:
        last_updated = repo_details.get("last_updated")
        if last_updated:
            last_update = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
            days_since_update = (datetime.now(last_update.tzinfo) - last_update).days
            if days_since_update > 365:  # Not updated in over a year
                base_score += 10
            elif days_since_update < 30:  # Recently updated
                base_score -= 5
    except:
        pass
    
    return min(base_score, 100)

def save_results(all_results):
    """
    Save vulnerability pattern results to CSV and JSON
    """
    # Sort by vulnerability score (highest first)
    sorted_results = sorted(all_results, key=lambda x: x.get("final_score", 0), reverse=True)
    
    # Save CSV
    with open(PATTERN_CONFIG["output_file"], "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = [
            "repo_name", "repo_url", "pattern", "risk_score", "final_score",
            "stars", "issues", "language", "file_path", "search_term", "description"
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in sorted_results:
            writer.writerow({
                "repo_name": result["repo_name"],
                "repo_url": result["repo_url"],
                "pattern": result["pattern"],
                "risk_score": result["risk_score"],
                "final_score": result.get("final_score", result["risk_score"]),
                "stars": result["stars"],
                "issues": result.get("repo_details", {}).get("issues", 0),
                "language": result["language"],
                "file_path": result["file_path"],
                "search_term": result["search_term"],
                "description": result["description"]
            })
    
    # Save detailed JSON
    with open(PATTERN_CONFIG["detailed_log"], "w", encoding="utf-8") as jsonfile:
        json.dump(sorted_results, jsonfile, indent=2, default=str)

def main():
    print("🎯 Vulnerability Pattern Scanner")
    print("=" * 40)
    print("Searching for specific vulnerability patterns in GitHub repositories")
    print()
    
    all_results = []
    pattern_stats = {}
    
    for pattern_name, pattern_data in VULNERABILITY_PATTERNS.items():
        print(f"🔍 Scanning for: {pattern_data['description']}")
        
        results = search_github_code(pattern_name, pattern_data)
        pattern_stats[pattern_name] = len(results)
        
        # Enhance results with repository details
        for result in results:
            repo_details = analyze_repository_details(result["repo_name"])
            result["repo_details"] = repo_details
            result["final_score"] = calculate_vulnerability_score(result, repo_details)
            
            # Update basic info with detailed repo data
            if repo_details:
                result["stars"] = repo_details.get("stars", result["stars"])
                result["language"] = repo_details.get("language", result["language"])
        
        all_results.extend(results)
        print(f"  ✅ Found {len(results)} repositories with this pattern")
        print()
    
    # Remove duplicates (same repo found for multiple patterns)
    unique_results = {}
    for result in all_results:
        repo_name = result["repo_name"]
        if repo_name not in unique_results or result["final_score"] > unique_results[repo_name]["final_score"]:
            unique_results[repo_name] = result
    
    final_results = list(unique_results.values())
    
    # Summary
    print("📊 SCAN SUMMARY")
    print("=" * 30)
    print(f"Total repositories found: {len(final_results)}")
    print(f"Unique repositories: {len(unique_results)}")
    print()
    
    print("Pattern breakdown:")
    for pattern, count in pattern_stats.items():
        print(f"  {pattern}: {count} repositories")
    
    if final_results:
        avg_score = sum(r["final_score"] for r in final_results) / len(final_results)
        high_risk_count = sum(1 for r in final_results if r["final_score"] >= 80)
        print(f"\nAverage vulnerability score: {avg_score:.1f}")
        print(f"High-risk repositories (≥80): {high_risk_count}")
    
    # Save results
    save_results(final_results)
    
    print(f"\n✅ Results saved:")
    print(f"   📄 CSV: {PATTERN_CONFIG['output_file']}")
    print(f"   📋 Detailed: {PATTERN_CONFIG['detailed_log']}")
    
    # Show top 10 highest risk repositories
    if final_results:
        print(f"\n🔥 TOP 10 HIGHEST RISK REPOSITORIES:")
        top_10 = sorted(final_results, key=lambda x: x["final_score"], reverse=True)[:10]
        for i, repo in enumerate(top_10, 1):
            print(f"{i:2d}. {repo['repo_name']} (Score: {repo['final_score']}) - {repo['pattern']}")
    
    return final_results

if __name__ == "__main__":
    main()
