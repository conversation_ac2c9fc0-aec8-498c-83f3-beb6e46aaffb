{"version": "1.130.0", "results": [{"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/boogheta_coronavirus-countries/index.html", "start": {"line": 358, "col": 56, "offset": 19416}, "end": {"line": 358, "col": 123, "offset": 19483}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/boogheta_coronavirus-countries/index.html", "start": {"line": 253, "col": 229, "offset": 0}, "end": {"line": 253, "col": 230, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/boogheta_coronavirus-countries/index.html:253:\n `\"` was unexpected", "path": "downloaded_repos/boogheta_coronavirus-countries/index.html", "spans": [{"file": "downloaded_repos/boogheta_coronavirus-countries/index.html", "start": {"line": 253, "col": 229, "offset": 0}, "end": {"line": 253, "col": 230, "offset": 1}}]}], "paths": {"scanned": ["downloaded_repos/boogheta_coronavirus-countries/.github/workflows/update.yml", "downloaded_repos/boogheta_coronavirus-countries/LICENSE", "downloaded_repos/boogheta_coronavirus-countries/README.md", "downloaded_repos/boogheta_coronavirus-countries/bin/build_data.sh", "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_france.py", "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_germany.py", "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_spain.py", "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_uk.py", "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_vaccines.py", "downloaded_repos/boogheta_coronavirus-countries/bin/export-main-countries.py", "downloaded_repos/boogheta_coronavirus-countries/bin/update_sources.sh", "downloaded_repos/boogheta_coronavirus-countries/css/roboto.css", "downloaded_repos/boogheta_coronavirus-countries/css/style.css", "downloaded_repos/boogheta_coronavirus-countries/css/vuetify.min.css", "downloaded_repos/boogheta_coronavirus-countries/data/covid-19-indicators-uk.csv", "downloaded_repos/boogheta_coronavirus-countries/data/covid-19-vaccines-uk.csv", "downloaded_repos/boogheta_coronavirus-countries/data/germany.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-Australia.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-Canada.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-China.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-France.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-Germany.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-Italy.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-Spain.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-UK.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-USA.csv", "downloaded_repos/boogheta_coronavirus-countries/data/population-World.csv", "downloaded_repos/boogheta_coronavirus-countries/data/spain.csv", "downloaded_repos/boogheta_coronavirus-countries/data/time_series_covid19_testing_US.csv", "downloaded_repos/boogheta_coronavirus-countries/data/uk.csv", "downloaded_repos/boogheta_coronavirus-countries/favicon.ico", "downloaded_repos/boogheta_coronavirus-countries/img/covid19.png", "downloaded_repos/boogheta_coronavirus-countries/img/loader.gif", "downloaded_repos/boogheta_coronavirus-countries/img/medialab.png", "downloaded_repos/boogheta_coronavirus-countries/img/screenshotv2.png", "downloaded_repos/boogheta_coronavirus-countries/index.html", "downloaded_repos/boogheta_coronavirus-countries/js/corona.js"], "skipped": [{"path": "downloaded_repos/boogheta_coronavirus-countries/data/casos_hosp_uci_def_sexo_edad_provres.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/chiffres-cles.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/coronavirus-countries.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/dpc-covid19-ita-regioni.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-cas_positifs.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-deces.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-ehpad.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-hospitalisations.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-retour_a_domicile.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-soins_critiques.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-vaccins_premiere_dose.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france-vaccins_vaccines.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/france.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/time_series_covid19_confirmed_US.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/time_series_covid19_confirmed_global.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/time_series_covid19_deaths_US.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/time_series_covid19_deaths_global.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/time_series_covid19_recovered_global.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/vaccinations.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/vaccines.csv", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/data/vaccines.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/js/d3-simple-slider.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/js/d3.v4.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/js/vue.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/boogheta_coronavirus-countries/js/vuetify.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7383620738983154, "profiling_times": {"config_time": 9.80217695236206, "core_time": 4.203467130661011, "ignores_time": 0.0015912055969238281, "total_time": 14.008200645446777}, "parsing_time": {"total_time": 0.4635496139526367, "per_file_time": {"mean": 0.042140873995694245, "std_dev": 0.0035738338790066745}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.035353422164917, "per_file_time": {"mean": 0.04747474614311668, "std_dev": 0.04133330701315118}, "very_slow_stats": {"time_ratio": 0.4470007649405196, "count_ratio": 0.011764705882352941}, "very_slow_files": [{"fpath": "downloaded_repos/boogheta_coronavirus-countries/js/corona.js", "ftime": 1.8038060665130615}]}, "matching_time": {"total_time": 0.9110605716705322, "per_file_and_rule_time": {"mean": 0.011247661378648544, "std_dev": 0.0010724825443674734}, "very_slow_stats": {"time_ratio": 0.524949460453541, "count_ratio": 0.037037037037037035}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/boogheta_coronavirus-countries/js/corona.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.12057685852050781}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/js/corona.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.16168904304504395}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/js/corona.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.19599485397338867}]}, "tainting_time": {"total_time": 1.2910714149475098, "per_def_and_rule_time": {"mean": 0.04303571383158365, "std_dev": 0.0036091250813332304}, "very_slow_stats": {"time_ratio": 0.7172748435591579, "count_ratio": 0.23333333333333334}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_vaccines.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.05102205276489258}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/js/corona.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.06828594207763672}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/js/corona.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.08977413177490234}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_spain.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.10829591751098633}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/js/corona.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.18019795417785645}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/bin/consolidate_france.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21361017227172852}, {"fpath": "downloaded_repos/boogheta_coronavirus-countries/bin/export-main-countries.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21486687660217285}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}