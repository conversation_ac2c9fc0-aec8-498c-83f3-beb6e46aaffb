{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/fief-dev_fief/.devcontainer/docker-compose.yml", "start": {"line": 21, "col": 3, "offset": 578}, "end": {"line": 21, "col": 5, "offset": 580}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/fief-dev_fief/.devcontainer/docker-compose.yml", "start": {"line": 21, "col": 3, "offset": 578}, "end": {"line": 21, "col": 5, "offset": 580}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/fief-dev_fief/.devcontainer/docker-compose.yml", "start": {"line": 34, "col": 3, "offset": 984}, "end": {"line": 34, "col": 8, "offset": 989}, "extra": {"message": "Service 'redis' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/fief-dev_fief/.devcontainer/docker-compose.yml", "start": {"line": 34, "col": 3, "offset": 984}, "end": {"line": 34, "col": 8, "offset": 989}, "extra": {"message": "Service 'redis' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/fief-dev_fief/docker/Dockerfile", "start": {"line": 23, "col": 1, "offset": 484}, "end": {"line": 23, "col": 51, "offset": 534}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"supervisord\", \"-c\", \"/etc/supervisord.conf\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2024-08-29_add_unique_constraint_on_oauthaccount_.py", "start": {"line": 24, "col": 5, "offset": 505}, "end": {"line": 29, "col": 6, "offset": 626}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2024-08-29_add_unique_constraint_on_oauthaccount_.py", "start": {"line": 24, "col": 5, "offset": 505}, "end": {"line": 29, "col": 6, "offset": 626}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/services/email_template/renderers.py", "start": {"line": 74, "col": 16, "offset": 2282}, "end": {"line": 74, "col": 60, "offset": 2326}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/services/email_template/renderers.py", "start": {"line": 139, "col": 16, "offset": 4476}, "end": {"line": 139, "col": 68, "offset": 4528}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/services/theme_preview.py", "start": {"line": 48, "col": 16, "offset": 1837}, "end": {"line": 48, "col": 40, "offset": 1861}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/services/theme_preview.py", "start": {"line": 70, "col": 16, "offset": 2691}, "end": {"line": 70, "col": 40, "offset": 2715}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/services/theme_preview.py", "start": {"line": 83, "col": 16, "offset": 3154}, "end": {"line": 83, "col": 40, "offset": 3178}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/services/theme_preview.py", "start": {"line": 96, "col": 16, "offset": 3614}, "end": {"line": 96, "col": 40, "offset": 3638}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/services/theme_preview.py", "start": {"line": 115, "col": 16, "offset": 4355}, "end": {"line": 115, "col": 40, "offset": 4379}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fief-dev_fief/fief/tasks/base.py", "start": {"line": 82, "col": 26, "offset": 2365}, "end": {"line": 84, "col": 10, "offset": 2481}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/permissions.html", "start": {"line": 8, "col": 1, "offset": 145}, "end": {"line": 23, "col": 8, "offset": 524}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/roles.html", "start": {"line": 8, "col": 1, "offset": 145}, "end": {"line": 23, "col": 8, "offset": 512}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/consent.html", "start": {"line": 10, "col": 3, "offset": 264}, "end": {"line": 26, "col": 10, "offset": 992}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "start": {"line": 10, "col": 3, "offset": 232}, "end": {"line": 21, "col": 10, "offset": 829}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/login.html", "start": {"line": 20, "col": 3, "offset": 790}, "end": {"line": 32, "col": 10, "offset": 1416}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/reset_password.html", "start": {"line": 10, "col": 3, "offset": 230}, "end": {"line": 20, "col": 10, "offset": 679}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/base.html:1:\n Failure: not a program", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/base.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/get.html:1:\n Failure: not a program", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/get.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/base.html:1:\n Failure: not a program", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/base.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/get.html:1:\n Failure: not a program", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/get.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/base.html:1:\n Failure: not a program", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/base.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fief-dev_fief/fief/templates/auth/login.html:1:\n Failure: not a program", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/login.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fief-dev_fief/fief/templates/macros/pagination.html:1:\n Failure: not a program", "path": "downloaded_repos/fief-dev_fief/fief/templates/macros/pagination.html"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 148}}, {"path": "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html:1:\n `{% extends \"BASE\" %}\n\n{% block preheader %}Use this link to reset your password. This link is only valid for 1 hour.{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 148}}, {"file": "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 152}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}API Keys · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 152}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 36, "offset": 252}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 33, "col": 3, "offset": 167}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n\n{% macro name_column(api_key) %}\n{{ api_key.name }}\n{% endmacro %}\n\n{% macro created_at_column(api_key) %}\n{{ api_key.created_at.strftime('%x %X') }}\n{% endmacro %}\n\n{% macro actions_column(api_key) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 36, "offset": 252}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 33, "col": 3, "offset": 167}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 60}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html", "start": {"line": 93, "col": 1, "offset": 0}, "end": {"line": 93, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html:1:\n `{% extends \"admin/clients/get/base.html\" %}\n\n{% block tab %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 60}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html", "start": {"line": 93, "col": 1, "offset": 0}, "end": {"line": 93, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 151}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}Clients · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 151}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 32, "offset": 82}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 28, "offset": 27}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "start": {"line": 17, "col": 1, "offset": 0}, "end": {"line": 40, "col": 3, "offset": 420}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n\n{% macro name_column(client) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 32, "offset": 82}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 28, "offset": 27}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "start": {"line": 17, "col": 1, "offset": 0}, "end": {"line": 40, "col": 3, "offset": 420}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 22, "offset": 196}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 24, "col": 1, "offset": 0}, "end": {"line": 34, "col": 32, "offset": 259}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 38, "col": 17, "offset": 32}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 107, "col": 1, "offset": 0}, "end": {"line": 107, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html:1:\n `{% import \"macros/alerts.html\" as alerts %}\n{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/forms.html\" as forms %}\n{% import \"macros/icons.html\" as icons %}\n\n{% macro preview() %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 22, "offset": 196}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 24, "col": 1, "offset": 0}, "end": {"line": 34, "col": 32, "offset": 259}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 38, "col": 17, "offset": 32}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "start": {"line": 107, "col": 1, "offset": 0}, "end": {"line": 107, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 43, "offset": 282}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 30, "col": 3, "offset": 181}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n\n{% macro type_column(email_template) %}\n{{ email_template.get_type_display_name() }}\n{% endmacro %}\n\n{% macro subject_column(email_template) %}\n{{ email_template.subject }}\n{% endmacro %}\n\n{% macro actions_column(email_template) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 43, "offset": 282}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 30, "col": 3, "offset": 181}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/header.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 42, "offset": 41}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/header.html:1:\n `{% import \"macros/icons.html\" as icons %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/header.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/header.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 42, "offset": 41}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 110}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/index.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/index.html:1:\n `{% extends layout %}\n\n{% block head_title_content %}Dashboard · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/index.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 110}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/index.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 159}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 63}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}OAuth Providers · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 159}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 63}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 44, "offset": 182}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 29, "col": 3, "offset": 263}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/icons.html\" as icons %}\n{% import \"macros/datatable.html\" as datatable %}\n\n{% macro provider_column(oauth_provider) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 44, "offset": 182}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 29, "col": 3, "offset": 263}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 39, "offset": 245}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 33, "col": 3, "offset": 173}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n\n{% macro name_column(permission) %}\n{{ permission.name }}\n{% endmacro %}\n\n{% macro codename_column(permission) %}\n{{ permission.codename }}\n{% endmacro %}\n\n{% macro actions_column(permission) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 39, "offset": 245}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 33, "col": 3, "offset": 173}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 75, "offset": 425}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 7, "offset": 6}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "start": {"line": 44, "col": 1, "offset": 0}, "end": {"line": 44, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/icons.html\" as icons %}\n\n{% extends \"admin/roles/list.html\" %}\n\n{% block head_title_content %}{{ role.name }} · {{ super() }}{% endblock %}\n\n{% set open_aside = true %}\n\n{% block aside %}\n<h2 class=\"text-slate-800 font-semibold text-center mb-6\">{{ role.name }}</h2>\n<div class=\"mt-6\">\n  <div class=\"text-sm font-semibold text-slate-800 mb-1\">Permissions</div>` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 75, "offset": 425}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 7, "offset": 6}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "start": {"line": 44, "col": 1, "offset": 0}, "end": {"line": 44, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 17, "offset": 237}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 53}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/forms.html\" as forms %}\n{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}Roles · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 17, "offset": 237}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 53}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 30, "offset": 80}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 31, "col": 3, "offset": 340}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n\n{% macro name_column(role) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 30, "offset": 80}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 31, "col": 3, "offset": 340}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 42, "offset": 41}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 47, "col": 1, "offset": 0}, "end": {"line": 47, "col": 68, "offset": 67}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 63, "col": 1, "offset": 0}, "end": {"line": 65, "col": 41, "offset": 56}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 75, "col": 1, "offset": 0}, "end": {"line": 77, "col": 56, "offset": 71}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 107, "col": 1, "offset": 0}, "end": {"line": 107, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html:1:\n `{% import \"macros/icons.html\" as icons %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 42, "offset": 41}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 47, "col": 1, "offset": 0}, "end": {"line": 47, "col": 68, "offset": 67}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 63, "col": 1, "offset": 0}, "end": {"line": 65, "col": 41, "offset": 56}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 75, "col": 1, "offset": 0}, "end": {"line": 77, "col": 56, "offset": 71}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "start": {"line": 107, "col": 1, "offset": 0}, "end": {"line": 107, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 16, "offset": 147}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 78, "offset": 77}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 31, "offset": 30}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 57, "col": 1, "offset": 0}, "end": {"line": 58, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html:1:\n `{% import \"macros/alerts.html\" as alerts %}\n{% import \"macros/forms.html\" as forms %}\n\n{% extends \"admin/tenants/get/base.html\" %}\n\n{% block tab %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 16, "offset": 147}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 78, "offset": 77}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 31, "offset": 30}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "start": {"line": 57, "col": 1, "offset": 0}, "end": {"line": 58, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 60}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html", "start": {"line": 73, "col": 1, "offset": 0}, "end": {"line": 73, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html:1:\n `{% extends \"admin/tenants/get/base.html\" %}\n\n{% block tab %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 60}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html", "start": {"line": 73, "col": 1, "offset": 0}, "end": {"line": 73, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 151}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}Tenants · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 151}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 32, "offset": 170}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 24, "offset": 23}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 23, "col": 39, "offset": 102}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 46, "col": 3, "offset": 352}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/icons.html\" as icons %}\n{% import \"macros/datatable.html\" as datatable %}\n\n{% macro name_column(tenant) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 32, "offset": 170}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 24, "offset": 23}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 23, "col": 39, "offset": 102}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 46, "col": 3, "offset": 352}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 22, "offset": 152}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "start": {"line": 26, "col": 1, "offset": 0}, "end": {"line": 36, "col": 17, "offset": 207}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "start": {"line": 100, "col": 1, "offset": 0}, "end": {"line": 100, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/forms.html\" as forms %}\n{% import \"macros/icons.html\" as icons %}\n\n{% macro preview() %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 22, "offset": 152}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "start": {"line": 26, "col": 1, "offset": 0}, "end": {"line": 36, "col": 17, "offset": 207}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "start": {"line": 100, "col": 1, "offset": 0}, "end": {"line": 100, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 150}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 54}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}Themes · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 150}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 54}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 16, "col": 34, "offset": 349}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "start": {"line": 27, "col": 8, "offset": 0}, "end": {"line": 27, "col": 38, "offset": 30}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 42, "col": 3, "offset": 163}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n{% import \"macros/icons.html\" as icons %}\n\n{% macro name_column(theme) %}\n{{ theme.name }}\n{% endmacro %}\n\n{% macro default_column(theme) %}\n{% if theme.default %}\n  {{ icons.check('w-4 h-4') }}\n{% else %}\n  {{ icons.x_mark('w-4 h-4') }}\n{% endif %}\n{% endmacro %}\n\n{% macro actions_column(theme) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 16, "col": 34, "offset": 349}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "start": {"line": 27, "col": 8, "offset": 0}, "end": {"line": 27, "col": 38, "offset": 30}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 42, "col": 3, "offset": 163}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 155}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 59}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}User fields · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 155}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 59}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 36, "offset": 174}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 33, "col": 3, "offset": 340}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/icons.html\" as icons %}\n{% import \"macros/datatable.html\" as datatable %}\n\n{% macro name_column(user_field) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 36, "offset": 174}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 33, "col": 3, "offset": 340}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 16, "offset": 101}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html", "start": {"line": 74, "col": 1, "offset": 0}, "end": {"line": 74, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends \"admin/users/get/base.html\" %}\n\n{% block tab %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 16, "offset": 101}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html", "start": {"line": 74, "col": 1, "offset": 0}, "end": {"line": 74, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 41, "offset": 173}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 22, "col": 17, "offset": 32}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 66, "col": 28, "offset": 0}, "end": {"line": 69, "col": 87, "offset": 376}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 116, "col": 1, "offset": 0}, "end": {"line": 117, "col": 15, "offset": 53}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}Users · {{ super() }}{% endblock %}\n\n{% macro column_switcher(label, slug) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 41, "offset": 173}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 22, "col": 17, "offset": 32}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 66, "col": 28, "offset": 0}, "end": {"line": 69, "col": 87, "offset": 376}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "start": {"line": 116, "col": 1, "offset": 0}, "end": {"line": 117, "col": 15, "offset": 53}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 31, "offset": 169}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 57, "col": 44, "offset": 1097}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 62, "col": 7, "offset": 0}, "end": {"line": 62, "col": 27, "offset": 20}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 64, "col": 7, "offset": 0}, "end": {"line": 64, "col": 18, "offset": 11}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 66, "col": 5, "offset": 0}, "end": {"line": 80, "col": 3, "offset": 220}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/icons.html\" as icons %}\n{% import \"macros/datatable.html\" as datatable %}\n\n{% macro email_column(user) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 31, "offset": 169}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 57, "col": 44, "offset": 1097}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 62, "col": 7, "offset": 0}, "end": {"line": 62, "col": 27, "offset": 20}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 64, "col": 7, "offset": 0}, "end": {"line": 64, "col": 18, "offset": 11}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "start": {"line": 66, "col": 5, "offset": 0}, "end": {"line": 80, "col": 3, "offset": 220}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 70, "offset": 427}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 7, "offset": 6}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "start": {"line": 56, "col": 1, "offset": 0}, "end": {"line": 56, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/icons.html\" as icons %}\n\n{% extends \"admin/webhooks/list.html\" %}\n\n{% block head_title_content %}{{ webhook.url }} · {{ super() }}{% endblock %}\n\n{% set open_aside = true %}\n\n{% block aside %}\n<h2 class=\"text-slate-800 font-semibold text-center mb-6\">{{ webhook.url }}</h2>\n<div class=\"mt-6\">\n  <div class=\"text-sm font-semibold text-slate-800 mb-1\">Events</div>` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 70, "offset": 427}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 7, "offset": 6}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "start": {"line": 56, "col": 1, "offset": 0}, "end": {"line": 56, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 152}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}Webhooks · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 152}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 156}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 24, "col": 15, "offset": 61}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html:1:\n `{% import \"macros/icons.html\" as icons %}\n\n{% extends layout %}\n\n{% block head_title_content %}Webhook logs · {{ super() }}{% endblock %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 156}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 24, "col": 15, "offset": 61}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 24, "col": 34, "offset": 589}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "start": {"line": 26, "col": 3, "offset": 0}, "end": {"line": 31, "col": 40, "offset": 88}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "start": {"line": 43, "col": 1, "offset": 0}, "end": {"line": 53, "col": 3, "offset": 175}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n\n{% macro created_at_column(webhook_log) %}\n  {{ webhook_log.created_at.strftime('%x %X') }}\n{% endmacro %}\n\n{% macro event_column(webhook_log) %}\n  {{ webhook_log.event }}\n{% endmacro %}\n\n{% macro success_column(webhook_log) %}\n{% if webhook_log.success %}\n{{ icons.check('w-4 h-4 text-green-500') }}\n{% else %}\n{{ icons.x_mark('w-4 h-4 text-red-500') }}\n{% endif %}\n{% endmacro %}\n\n{% macro attempt_column(webhook_log) %}\n  {{ webhook_log.attempt }}\n{% endmacro %}\n\n{% macro error_column(webhook_log) %}\n  {% if webhook_log.error_type %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 24, "col": 34, "offset": 589}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "start": {"line": 26, "col": 3, "offset": 0}, "end": {"line": 31, "col": 40, "offset": 88}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "start": {"line": 43, "col": 1, "offset": 0}, "end": {"line": 53, "col": 3, "offset": 175}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 32, "offset": 82}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 23, "col": 36, "offset": 234}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 44, "col": 3, "offset": 167}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html:1:\n `{% import \"macros/datatable.html\" as datatable %}\n\n{% macro url_column(webhook) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 32, "offset": 82}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 23, "col": 36, "offset": 234}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 44, "col": 3, "offset": 167}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/change.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 42, "offset": 87}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/change.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/forms.html\" as forms %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/change.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/change.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 42, "offset": 87}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/verify.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 56, "offset": 187}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/verify.html:1:\n `{% import \"macros/alerts.html\" as alerts %}\n{% import \"macros/buttons.html\" as buttons %}\n{% import \"macros/forms.html\" as forms %}\n{% import \"macros/verify_email.html\" as verify_email %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/verify.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/verify.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 56, "offset": 187}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 22, "offset": 229}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "start": {"line": 22, "col": 1, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html:1:\n `{% import 'macros/forms.html' as forms %}\n\n{% extends \"auth/layout.html\" %}\n\n{% block head_title_content %}{{ _('Forgot password') }}{% endblock %}\n\n{% block title %}{{ _('Forgot password') }}{% endblock %}\n\n{% block auth_form %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 22, "offset": 229}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "start": {"line": 22, "col": 1, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 21, "col": 22, "offset": 383}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "start": {"line": 46, "col": 3, "offset": 0}, "end": {"line": 46, "col": 57, "offset": 54}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "start": {"line": 58, "col": 3, "offset": 0}, "end": {"line": 59, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/auth/register.html:1:\n `{% import 'macros/forms.html' as forms %}\n\n{% extends \"auth/layout.html\" %}\n\n{% block head_title_content %}\n  {% if finalize %}\n    {{ _('Finalize sign up') }}\n  {% else %}\n    {{ _('Sign up') }}\n  {% endif %}\n{% endblock %}\n\n{% block title %}\n  {% if finalize %}\n    {{ _('Finalize sign up') }}\n  {% else %}\n    {{ _('Sign up') }}\n  {% endif %}\n{% endblock %}\n\n{% block auth_form %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 21, "col": 22, "offset": 383}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "start": {"line": 46, "col": 3, "offset": 0}, "end": {"line": 46, "col": 57, "offset": 54}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "start": {"line": 58, "col": 3, "offset": 0}, "end": {"line": 59, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 22, "offset": 335}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html", "start": {"line": 51, "col": 1, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html:1:\n `{% import \"macros/buttons.html\" as buttons %}\n{% import 'macros/forms.html' as forms %}\n{% import \"macros/verify_email.html\" as verify_email %}\n\n{% extends \"auth/layout.html\" %}\n\n{% block head_title_content %}{{ _('Verify your email') }}{% endblock %}\n\n{% block title %}{{ _('Verify your email') }}{% endblock %}\n\n{% block auth_form %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 22, "offset": 335}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html", "start": {"line": 51, "col": 1, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 23, "offset": 22}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html:1:\n `{% macro branding() %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 23, "offset": 22}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 69, "offset": 163}}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html", "start": {"line": 50, "col": 1, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html:1:\n `{% import \"macros/icons.html\" as icons %}\n{% import \"macros/pagination.html\" as pagination %}\n\n{% macro datatable(data, count, query_parameters, title, columns) %}` was unexpected", "path": "downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html", "spans": [{"file": "downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 69, "offset": 163}}, {"file": "downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html", "start": {"line": 50, "col": 1, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/fief-dev_fief/.all-contributorsrc", "downloaded_repos/fief-dev_fief/.devcontainer/Dockerfile", "downloaded_repos/fief-dev_fief/.devcontainer/devcontainer.json", "downloaded_repos/fief-dev_fief/.devcontainer/docker-compose.yml", "downloaded_repos/fief-dev_fief/.devcontainer/hatch.config.toml", "downloaded_repos/fief-dev_fief/.devcontainer/post-create.sh", "downloaded_repos/fief-dev_fief/.editorconfig", "downloaded_repos/fief-dev_fief/.env.dist", "downloaded_repos/fief-dev_fief/.github/FUNDING.yml", "downloaded_repos/fief-dev_fief/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/fief-dev_fief/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/fief-dev_fief/.github/actions/docker-build/action.yml", "downloaded_repos/fief-dev_fief/.github/actions/lint/action.yml", "downloaded_repos/fief-dev_fief/.github/dependabot.yml", "downloaded_repos/fief-dev_fief/.github/workflows/build.yml", "downloaded_repos/fief-dev_fief/.github/workflows/codeql-analysis.yml", "downloaded_repos/fief-dev_fief/.github/workflows/docker.yml", "downloaded_repos/fief-dev_fief/.github/workflows/sentry.yml", "downloaded_repos/fief-dev_fief/.gitignore", "downloaded_repos/fief-dev_fief/.gitmodules", "downloaded_repos/fief-dev_fief/.vscode/settings.json", "downloaded_repos/fief-dev_fief/LICENSE.md", "downloaded_repos/fief-dev_fief/README.md", "downloaded_repos/fief-dev_fief/SECURITY.md", "downloaded_repos/fief-dev_fief/babel.cfg", "downloaded_repos/fief-dev_fief/docker/Dockerfile", "downloaded_repos/fief-dev_fief/docker/supervisord.conf", "downloaded_repos/fief-dev_fief/docker-compose.yml", "downloaded_repos/fief-dev_fief/fief/__init__.py", "downloaded_repos/fief-dev_fief/fief/alembic/README", "downloaded_repos/fief-dev_fief/fief/alembic/env.py", "downloaded_repos/fief-dev_fief/fief/alembic/script.py.mako", "downloaded_repos/fief-dev_fief/fief/alembic/table_prefix_codemod.py", "downloaded_repos/fief-dev_fief/fief/alembic/versions/2023-08-28_initial_migration.py", "downloaded_repos/fief-dev_fief/fief/alembic/versions/2024-01-03_add_adminsessiontoken_and_adminapikey_.py", "downloaded_repos/fief-dev_fief/fief/alembic/versions/2024-08-29_add_unique_constraint_on_oauthaccount_.py", "downloaded_repos/fief-dev_fief/fief/alembic.ini", "downloaded_repos/fief-dev_fief/fief/app.py", "downloaded_repos/fief-dev_fief/fief/apps/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/api/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/api/app.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/clients.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/email_templates.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/oauth_providers.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/permissions.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/roles.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/tenants.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/user_fields.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/users.py", "downloaded_repos/fief-dev_fief/fief/apps/api/routers/webhooks.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/app.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/exception_handlers.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/forms/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/forms/auth.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/forms/password.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/forms/profile.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/forms/register.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/forms/reset.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/forms/verify_email.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/responses.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/auth.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/dashboard.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/oauth.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/register.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/reset.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/token.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/user.py", "downloaded_repos/fief-dev_fief/fief/apps/auth/routers/well_known.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/app.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/dependencies.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/exception_handlers.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/api_key.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/client.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/email_template.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/oauth_provider.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/permission.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/role.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/tenant.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/theme.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/user.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/user_field.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/forms/webhook.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/responses.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/__init__.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/api_keys.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/auth.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/clients.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/email_templates.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/oauth_providers.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/permissions.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/roles.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/tenants.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/themes.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/user_fields.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/users.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/routers/webhooks.py", "downloaded_repos/fief-dev_fief/fief/apps/dashboard/validators.py", "downloaded_repos/fief-dev_fief/fief/cli/__init__.py", "downloaded_repos/fief-dev_fief/fief/cli/__main__.py", "downloaded_repos/fief-dev_fief/fief/cli/admin.py", "downloaded_repos/fief-dev_fief/fief/cli/quickstart.py", "downloaded_repos/fief-dev_fief/fief/crypto/__init__.py", "downloaded_repos/fief-dev_fief/fief/crypto/access_token.py", "downloaded_repos/fief-dev_fief/fief/crypto/code_challenge.py", "downloaded_repos/fief-dev_fief/fief/crypto/encryption.py", "downloaded_repos/fief-dev_fief/fief/crypto/id_token.py", "downloaded_repos/fief-dev_fief/fief/crypto/jwk.py", "downloaded_repos/fief-dev_fief/fief/crypto/password.py", "downloaded_repos/fief-dev_fief/fief/crypto/token.py", "downloaded_repos/fief-dev_fief/fief/crypto/verify_code.py", "downloaded_repos/fief-dev_fief/fief/db/__init__.py", "downloaded_repos/fief-dev_fief/fief/db/engine.py", "downloaded_repos/fief-dev_fief/fief/db/main.py", "downloaded_repos/fief-dev_fief/fief/db/migration.py", "downloaded_repos/fief-dev_fief/fief/db/types.py", "downloaded_repos/fief-dev_fief/fief/dependencies/__init__.py", "downloaded_repos/fief-dev_fief/fief/dependencies/admin_api_key.py", "downloaded_repos/fief-dev_fief/fief/dependencies/admin_authentication.py", "downloaded_repos/fief-dev_fief/fief/dependencies/admin_session.py", "downloaded_repos/fief-dev_fief/fief/dependencies/auth.py", "downloaded_repos/fief-dev_fief/fief/dependencies/authentication_flow.py", "downloaded_repos/fief-dev_fief/fief/dependencies/branding.py", "downloaded_repos/fief-dev_fief/fief/dependencies/client.py", "downloaded_repos/fief-dev_fief/fief/dependencies/db.py", "downloaded_repos/fief-dev_fief/fief/dependencies/email_provider.py", "downloaded_repos/fief-dev_fief/fief/dependencies/email_template.py", "downloaded_repos/fief-dev_fief/fief/dependencies/fief.py", "downloaded_repos/fief-dev_fief/fief/dependencies/logger.py", "downloaded_repos/fief-dev_fief/fief/dependencies/login_hint.py", "downloaded_repos/fief-dev_fief/fief/dependencies/oauth.py", "downloaded_repos/fief-dev_fief/fief/dependencies/oauth_provider.py", "downloaded_repos/fief-dev_fief/fief/dependencies/pagination.py", "downloaded_repos/fief-dev_fief/fief/dependencies/permission.py", "downloaded_repos/fief-dev_fief/fief/dependencies/register.py", "downloaded_repos/fief-dev_fief/fief/dependencies/repositories.py", "downloaded_repos/fief-dev_fief/fief/dependencies/request.py", "downloaded_repos/fief-dev_fief/fief/dependencies/role.py", "downloaded_repos/fief-dev_fief/fief/dependencies/session_token.py", "downloaded_repos/fief-dev_fief/fief/dependencies/tasks.py", "downloaded_repos/fief-dev_fief/fief/dependencies/telemetry.py", "downloaded_repos/fief-dev_fief/fief/dependencies/tenant.py", "downloaded_repos/fief-dev_fief/fief/dependencies/tenant_email_domain.py", "downloaded_repos/fief-dev_fief/fief/dependencies/theme.py", "downloaded_repos/fief-dev_fief/fief/dependencies/token.py", "downloaded_repos/fief-dev_fief/fief/dependencies/user_field.py", "downloaded_repos/fief-dev_fief/fief/dependencies/user_roles.py", "downloaded_repos/fief-dev_fief/fief/dependencies/users.py", "downloaded_repos/fief-dev_fief/fief/dependencies/webhook.py", "downloaded_repos/fief-dev_fief/fief/dependencies/webhooks.py", "downloaded_repos/fief-dev_fief/fief/errors.py", "downloaded_repos/fief-dev_fief/fief/exceptions.py", "downloaded_repos/fief-dev_fief/fief/forms.py", "downloaded_repos/fief-dev_fief/fief/lifespan.py", "downloaded_repos/fief-dev_fief/fief/locale/de_DE/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/en_US/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/es_ES/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/fr_FR/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/it_IT/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/pl_PL/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/pt_BR/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/pt_PT/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/zh_CN/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale/zh_TW/LC_MESSAGES/messages.po", "downloaded_repos/fief-dev_fief/fief/locale.py", "downloaded_repos/fief-dev_fief/fief/logger.py", "downloaded_repos/fief-dev_fief/fief/middlewares/__init__.py", "downloaded_repos/fief-dev_fief/fief/middlewares/cors.py", "downloaded_repos/fief-dev_fief/fief/middlewares/csrf.py", "downloaded_repos/fief-dev_fief/fief/middlewares/locale.py", "downloaded_repos/fief-dev_fief/fief/middlewares/security_headers.py", "downloaded_repos/fief-dev_fief/fief/middlewares/x_forwarded_host.py", "downloaded_repos/fief-dev_fief/fief/models/__init__.py", "downloaded_repos/fief-dev_fief/fief/models/admin_api_key.py", "downloaded_repos/fief-dev_fief/fief/models/admin_session_token.py", "downloaded_repos/fief-dev_fief/fief/models/audit_log.py", "downloaded_repos/fief-dev_fief/fief/models/authorization_code.py", "downloaded_repos/fief-dev_fief/fief/models/base.py", "downloaded_repos/fief-dev_fief/fief/models/client.py", "downloaded_repos/fief-dev_fief/fief/models/email_domain.py", "downloaded_repos/fief-dev_fief/fief/models/email_template.py", "downloaded_repos/fief-dev_fief/fief/models/email_verification.py", "downloaded_repos/fief-dev_fief/fief/models/generics.py", "downloaded_repos/fief-dev_fief/fief/models/grant.py", "downloaded_repos/fief-dev_fief/fief/models/login_session.py", "downloaded_repos/fief-dev_fief/fief/models/oauth_account.py", "downloaded_repos/fief-dev_fief/fief/models/oauth_provider.py", "downloaded_repos/fief-dev_fief/fief/models/oauth_session.py", "downloaded_repos/fief-dev_fief/fief/models/permission.py", "downloaded_repos/fief-dev_fief/fief/models/refresh_token.py", "downloaded_repos/fief-dev_fief/fief/models/registration_session.py", "downloaded_repos/fief-dev_fief/fief/models/role.py", "downloaded_repos/fief-dev_fief/fief/models/session_token.py", "downloaded_repos/fief-dev_fief/fief/models/tenant.py", "downloaded_repos/fief-dev_fief/fief/models/theme.py", "downloaded_repos/fief-dev_fief/fief/models/user.py", "downloaded_repos/fief-dev_fief/fief/models/user_field.py", "downloaded_repos/fief-dev_fief/fief/models/user_field_value.py", "downloaded_repos/fief-dev_fief/fief/models/user_permission.py", "downloaded_repos/fief-dev_fief/fief/models/user_role.py", "downloaded_repos/fief-dev_fief/fief/models/webhook.py", "downloaded_repos/fief-dev_fief/fief/models/webhook_log.py", "downloaded_repos/fief-dev_fief/fief/paths.py", "downloaded_repos/fief-dev_fief/fief/py.typed", "downloaded_repos/fief-dev_fief/fief/repositories/__init__.py", "downloaded_repos/fief-dev_fief/fief/repositories/admin_api_key.py", "downloaded_repos/fief-dev_fief/fief/repositories/admin_session_token.py", "downloaded_repos/fief-dev_fief/fief/repositories/audit_log.py", "downloaded_repos/fief-dev_fief/fief/repositories/authorization_code.py", "downloaded_repos/fief-dev_fief/fief/repositories/base.py", "downloaded_repos/fief-dev_fief/fief/repositories/client.py", "downloaded_repos/fief-dev_fief/fief/repositories/email_domain.py", "downloaded_repos/fief-dev_fief/fief/repositories/email_template.py", "downloaded_repos/fief-dev_fief/fief/repositories/email_verification.py", "downloaded_repos/fief-dev_fief/fief/repositories/grant.py", "downloaded_repos/fief-dev_fief/fief/repositories/login_session.py", "downloaded_repos/fief-dev_fief/fief/repositories/oauth_account.py", "downloaded_repos/fief-dev_fief/fief/repositories/oauth_provider.py", "downloaded_repos/fief-dev_fief/fief/repositories/oauth_session.py", "downloaded_repos/fief-dev_fief/fief/repositories/permission.py", "downloaded_repos/fief-dev_fief/fief/repositories/refresh_token.py", "downloaded_repos/fief-dev_fief/fief/repositories/registration_session.py", "downloaded_repos/fief-dev_fief/fief/repositories/role.py", "downloaded_repos/fief-dev_fief/fief/repositories/session_token.py", "downloaded_repos/fief-dev_fief/fief/repositories/tenant.py", "downloaded_repos/fief-dev_fief/fief/repositories/theme.py", "downloaded_repos/fief-dev_fief/fief/repositories/user.py", "downloaded_repos/fief-dev_fief/fief/repositories/user_field.py", "downloaded_repos/fief-dev_fief/fief/repositories/user_permission.py", "downloaded_repos/fief-dev_fief/fief/repositories/user_role.py", "downloaded_repos/fief-dev_fief/fief/repositories/webhook.py", "downloaded_repos/fief-dev_fief/fief/repositories/webhook_log.py", "downloaded_repos/fief-dev_fief/fief/scheduler.py", "downloaded_repos/fief-dev_fief/fief/schemas/__init__.py", "downloaded_repos/fief-dev_fief/fief/schemas/auth.py", "downloaded_repos/fief-dev_fief/fief/schemas/client.py", "downloaded_repos/fief-dev_fief/fief/schemas/email_template.py", "downloaded_repos/fief-dev_fief/fief/schemas/generics.py", "downloaded_repos/fief-dev_fief/fief/schemas/oauth.py", "downloaded_repos/fief-dev_fief/fief/schemas/oauth_account.py", "downloaded_repos/fief-dev_fief/fief/schemas/oauth_provider.py", "downloaded_repos/fief-dev_fief/fief/schemas/permission.py", "downloaded_repos/fief-dev_fief/fief/schemas/role.py", "downloaded_repos/fief-dev_fief/fief/schemas/tenant.py", "downloaded_repos/fief-dev_fief/fief/schemas/user.py", "downloaded_repos/fief-dev_fief/fief/schemas/user_field.py", "downloaded_repos/fief-dev_fief/fief/schemas/user_permission.py", "downloaded_repos/fief-dev_fief/fief/schemas/user_role.py", "downloaded_repos/fief-dev_fief/fief/schemas/webhook.py", "downloaded_repos/fief-dev_fief/fief/schemas/webhook_log.py", "downloaded_repos/fief-dev_fief/fief/schemas/well_known.py", "downloaded_repos/fief-dev_fief/fief/services/__init__.py", "downloaded_repos/fief-dev_fief/fief/services/acr.py", "downloaded_repos/fief-dev_fief/fief/services/admin.py", "downloaded_repos/fief-dev_fief/fief/services/authentication_flow.py", "downloaded_repos/fief-dev_fief/fief/services/email/__init__.py", "downloaded_repos/fief-dev_fief/fief/services/email/base.py", "downloaded_repos/fief-dev_fief/fief/services/email/null.py", "downloaded_repos/fief-dev_fief/fief/services/email/postmark.py", "downloaded_repos/fief-dev_fief/fief/services/email/sendgrid.py", "downloaded_repos/fief-dev_fief/fief/services/email/smtp.py", "downloaded_repos/fief-dev_fief/fief/services/email_template/__init__.py", "downloaded_repos/fief-dev_fief/fief/services/email_template/contexts.py", "downloaded_repos/fief-dev_fief/fief/services/email_template/initializer.py", "downloaded_repos/fief-dev_fief/fief/services/email_template/renderers.py", "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/base.html", "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html", "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/verify_email.html", "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/welcome.html", "downloaded_repos/fief-dev_fief/fief/services/email_template/types.py", "downloaded_repos/fief-dev_fief/fief/services/initializer.py", "downloaded_repos/fief-dev_fief/fief/services/localhost.py", "downloaded_repos/fief-dev_fief/fief/services/oauth_provider.py", "downloaded_repos/fief-dev_fief/fief/services/password.py", "downloaded_repos/fief-dev_fief/fief/services/posthog.py", "downloaded_repos/fief-dev_fief/fief/services/registration_flow.py", "downloaded_repos/fief-dev_fief/fief/services/response_type.py", "downloaded_repos/fief-dev_fief/fief/services/tenant_email_domain.py", "downloaded_repos/fief-dev_fief/fief/services/theme.py", "downloaded_repos/fief-dev_fief/fief/services/theme_preview.py", "downloaded_repos/fief-dev_fief/fief/services/user_manager.py", "downloaded_repos/fief-dev_fief/fief/services/user_role_permissions.py", "downloaded_repos/fief-dev_fief/fief/services/user_roles.py", "downloaded_repos/fief-dev_fief/fief/services/webhooks/__init__.py", "downloaded_repos/fief-dev_fief/fief/services/webhooks/delivery.py", "downloaded_repos/fief-dev_fief/fief/services/webhooks/models.py", "downloaded_repos/fief-dev_fief/fief/services/webhooks/trigger.py", "downloaded_repos/fief-dev_fief/fief/settings.py", "downloaded_repos/fief-dev_fief/fief/settings_class.py", "downloaded_repos/fief-dev_fief/fief/static/favicon.svg", "downloaded_repos/fief-dev_fief/fief/static/fief-logo.svg", "downloaded_repos/fief-dev_fief/fief/static/illustrations/broken-soldier.svg", "downloaded_repos/fief-dev_fief/fief/static/illustrations/castle.svg", "downloaded_repos/fief-dev_fief/fief/static/illustrations/grass.svg", "downloaded_repos/fief-dev_fief/fief/tasks/__init__.py", "downloaded_repos/fief-dev_fief/fief/tasks/audit_log.py", "downloaded_repos/fief-dev_fief/fief/tasks/base.py", "downloaded_repos/fief-dev_fief/fief/tasks/cleanup.py", "downloaded_repos/fief-dev_fief/fief/tasks/email_verification.py", "downloaded_repos/fief-dev_fief/fief/tasks/forgot_password.py", "downloaded_repos/fief-dev_fief/fief/tasks/heartbeat.py", "downloaded_repos/fief-dev_fief/fief/tasks/register.py", "downloaded_repos/fief-dev_fief/fief/tasks/roles.py", "downloaded_repos/fief-dev_fief/fief/tasks/user_roles.py", "downloaded_repos/fief-dev_fief/fief/tasks/webhooks.py", "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/token.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/base.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/encryption_key.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/base.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/lifetimes.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list_combobox.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/error.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/header.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/index.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/layout.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/layout_boost.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/get.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list_combobox.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/list_combobox.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list_combobox.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/base.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email_domain_authentication.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list_combobox.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list_combobox.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/get.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/access_token.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/access_token_result.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/base.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/oauth_accounts.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/permissions.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/roles.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/verify_email_requested.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/create.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/delete.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/edit.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/get.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/secret.html", "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/authorize.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/base.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/consent.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/change.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/verify.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/index.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/layout.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/password.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/sidebar.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/layout.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/login.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/logout.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/reset_password.html", "downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/alerts.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/buttons.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/forms.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/icons.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/modal.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/pagination.html", "downloaded_repos/fief-dev_fief/fief/templates/macros/verify_email.html", "downloaded_repos/fief-dev_fief/fief/templates.py", "downloaded_repos/fief-dev_fief/fief/worker.py", "downloaded_repos/fief-dev_fief/js/code-editor.mjs", "downloaded_repos/fief-dev_fief/js/dependencies.mjs", "downloaded_repos/fief-dev_fief/package-lock.json", "downloaded_repos/fief-dev_fief/package.json", "downloaded_repos/fief-dev_fief/pyproject.toml", "downloaded_repos/fief-dev_fief/rollup.config.js", "downloaded_repos/fief-dev_fief/styles/additional-styles/flatpickr.scss", "downloaded_repos/fief-dev_fief/styles/additional-styles/range-slider.scss", "downloaded_repos/fief-dev_fief/styles/additional-styles/theme.scss", "downloaded_repos/fief-dev_fief/styles/additional-styles/toggle-switch.scss", "downloaded_repos/fief-dev_fief/styles/additional-styles/utility-patterns.scss", "downloaded_repos/fief-dev_fief/styles/globals.scss"], "skipped": [{"path": "downloaded_repos/fief-dev_fief/.github/actions/test/action.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/fief/services/email_template/templates/forgot_password.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/api_keys/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/get/general.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/clients/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/edit.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/email_templates/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/header.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/get.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/oauth_providers/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/permissions/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/get.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/roles/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/sidebar.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/email.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/get/general.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/tenants/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/edit.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/themes/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/get.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/user_fields/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/account.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/get/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/users/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/get.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/logs/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/admin/webhooks/table.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/change.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/dashboard/email/verify.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/forgot_password.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/register.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/auth/verify_email.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/macros/branding.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/macros/datatable.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/fief/templates/macros/pagination.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fief-dev_fief/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/data.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/helpers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/jwks.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_app.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_clients.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_email_templates.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_oauth_providers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_permissions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_tenants.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_user_fields.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_api_webhooks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_dashboard.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_forms_register.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_locale.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_oauth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_register.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_reset.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_auth_well_known.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_api_keys.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_clients.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_email_templates.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_oauth_providers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_permissions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_tenants.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_themes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_user_fields.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_users.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_dashboard_webhooks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_apps_security_headers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_db_types.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_dependencies_login_hint.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_dependencies_user_field.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_models_tenant.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_models_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_models_user_field_value.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_repositories_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_schemas_generics.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_email_provider_base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_email_provider_smtp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_email_template.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_is_localhost.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_tenant_email_domain.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_user_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_webhooks_delivery.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_services_webhooks_trigger.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_tasks_cleanup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_tasks_email_verification.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_tasks_forgot_password.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_tasks_register.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_tasks_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_tasks_user_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/test_tasks_webhooks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fief-dev_fief/tests/types.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.899224042892456, "profiling_times": {"config_time": 6.419590711593628, "core_time": 6.1370508670806885, "ignores_time": 0.0024085044860839844, "total_time": 12.560746908187866}, "parsing_time": {"total_time": 2.066681146621704, "per_file_time": {"mean": 0.006339512719698483, "std_dev": 0.00036475306791857945}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 20.896486043930054, "per_file_time": {"mean": 0.016479878583541024, "std_dev": 0.0062811242498158745}, "very_slow_stats": {"time_ratio": 0.08757974317544688, "count_ratio": 0.0007886435331230284}, "very_slow_files": [{"fpath": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2023-08-28_initial_migration.py", "ftime": 1.830108880996704}]}, "matching_time": {"total_time": 7.689537525177002, "per_file_and_rule_time": {"mean": 0.00358988679980252, "std_dev": 0.0001517194981036684}, "very_slow_stats": {"time_ratio": 0.10253595267655369, "count_ratio": 0.0014005602240896359}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/fief-dev_fief/fief/forms.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.12848901748657227}, {"fpath": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2023-08-28_initial_migration.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.3000640869140625}, {"fpath": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2023-08-28_initial_migration.py", "rule_id": "python.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.35990095138549805}]}, "tainting_time": {"total_time": 3.928563356399536, "per_def_and_rule_time": {"mean": 0.0008620942190914051, "std_dev": 1.587113217080677e-05}, "very_slow_stats": {"time_ratio": 0.09778536168125082, "count_ratio": 0.0006583278472679394}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2023-08-28_initial_migration.py", "fline": 1323, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.07505607604980469}, {"fpath": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2023-08-28_initial_migration.py", "fline": 22, "rule_id": "python.sqlalchemy.security.audit.avoid-sqlalchemy-text.avoid-sqlalchemy-text", "time": 0.08780503273010254}, {"fpath": "downloaded_repos/fief-dev_fief/fief/alembic/versions/2023-08-28_initial_migration.py", "fline": 22, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.22129487991333008}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090525056}, "engine_requested": "OSS", "skipped_rules": []}