{"version": "1.130.0", "results": [], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/silverstripe_silverstripe-elemental/lang/pl.yml:6:\n (approximate error location; error nearby after) error calling parser: did not find expected key character 0 position 0 returned: 0", "path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/pl.yml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/silverstripe_silverstripe-elemental/lang/sl.yml:32:\n (approximate error location; error nearby after) error calling parser: did not find expected key character 0 position 0 returned: 0", "path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/sl.yml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/silverstripe_silverstripe-elemental/lang/cs.yml:8:\n (approximate error location; error nearby after) error calling parser: did not find expected key character 0 position 0 returned: 0", "path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/cs.yml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/silverstripe_silverstripe-elemental/lang/hr.yml:19:\n (approximate error location; error nearby after) error calling parser: did not find expected key character 0 position 0 returned: 0", "path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/hr.yml"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/deploy-userhelp-docs.yml", "start": {"line": 15, "col": 69, "offset": 246}, "end": {"line": 15, "col": 72, "offset": 249}}]], "message": "Syntax error at line downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/deploy-userhelp-docs.yml:15:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/deploy-userhelp-docs.yml", "spans": [{"file": "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/deploy-userhelp-docs.yml", "start": {"line": 15, "col": 69, "offset": 246}, "end": {"line": 15, "col": 72, "offset": 249}}]}], "paths": {"scanned": ["downloaded_repos/silverstripe_silverstripe-elemental/.doclintrc", "downloaded_repos/silverstripe_silverstripe-elemental/.editorconfig", "downloaded_repos/silverstripe_silverstripe-elemental/.eslintrc.js", "downloaded_repos/silverstripe_silverstripe-elemental/.gitattributes", "downloaded_repos/silverstripe_silverstripe-elemental/.github/ISSUE_TEMPLATE/1_bug_report.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/ISSUE_TEMPLATE/2_feature_request.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/ISSUE_TEMPLATE/3_blank_issue.md", "downloaded_repos/silverstripe_silverstripe-elemental/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/add-prs-to-project.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/ci.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/deploy-userhelp-docs.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/dispatch-ci.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/keepalive.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/merge-up.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/tag-patch-release.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/update-js.yml", "downloaded_repos/silverstripe_silverstripe-elemental/.gitignore", "downloaded_repos/silverstripe_silverstripe-elemental/.idea/workspace.xml", "downloaded_repos/silverstripe_silverstripe-elemental/.nvmrc", "downloaded_repos/silverstripe_silverstripe-elemental/.stylelintrc.js", "downloaded_repos/silverstripe_silverstripe-elemental/.tx/config", "downloaded_repos/silverstripe_silverstripe-elemental/LICENSE", "downloaded_repos/silverstripe_silverstripe-elemental/README.md", "downloaded_repos/silverstripe_silverstripe-elemental/_config/cache.yml", "downloaded_repos/silverstripe_silverstripe-elemental/_config/config.yml", "downloaded_repos/silverstripe_silverstripe-elemental/babel.config.json", "downloaded_repos/silverstripe_silverstripe-elemental/behat.yml", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/cs.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/de.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/en.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/fi.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/fr.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/it.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/nl.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/pl.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/sl.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/cs.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/de.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/en.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/fi.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/fr.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/it.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/nl.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/pl.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/sl.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/src/sv.json", "downloaded_repos/silverstripe_silverstripe-elemental/client/lang/sv.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/boot/index.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/boot/registerComponents.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/boot/registerTransforms.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/bundles/bundle.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/AbstractAction.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/ArchiveAction.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/DuplicateAction.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/PublishAction.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/SaveAction.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/UnpublishAction.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/ActionMenu.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/AddElementPopover.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/AddNewButton.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/AddNewButton.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Content.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/DragPositionIndicator.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/DragPositionIndicator.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Element.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Element.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/ElementActions.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/ElementEditor.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/ElementEditor.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/ElementList.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/ElementList.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Header.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Header.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/HoverBar.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/HoverBar.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/InlineEditForm.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/InlineEditForm.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Summary.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Summary.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Toolbar.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Toolbar.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/HistoricElementView/HistoricElementView.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/TextCheckboxGroupField/TextCheckboxGroupField.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/TextCheckboxGroupField/TextCheckboxGroupField.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/legacy/ElementEditor/entwine.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/lib/prefixClassNames.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/state/editor/elementConfig.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/state/editor/loadElementFormStateName.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/state/editor/loadElementSchemaValue.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/state/history/revertToBlockVersionRequest.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/styles/admin.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/styles/bundle.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/styles/history.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/styles/reports.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/styles/textcheckboxgroup.scss", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/types/elementType.js", "downloaded_repos/silverstripe_silverstripe-elemental/client/src/types/elementTypeType.js", "downloaded_repos/silverstripe_silverstripe-elemental/code-of-conduct.md", "downloaded_repos/silverstripe_silverstripe-elemental/composer.json", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/01_basic_setup.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/02_advanced_setup.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/03_searching-blocks.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/04_defining-you-own-elements.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/05_examples/00_elementalarea_with_dataobject.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/09_content_migration.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/_images/content-block-overview.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/index.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/01_edit_content.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/02_history.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/03_reports.md", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/adding_blocks_between_blocks.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/adding_content_block.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/banner_example.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/block_version.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/content-block-overview.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/content_block_collapsed.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/content_block_compare.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/content_block_states.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/content_block_types.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/inline_editing.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/more_options.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/reorder_blocks.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/report_in_use.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/report_types.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/revert_button.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/_images/tooltip.png", "downloaded_repos/silverstripe_silverstripe-elemental/docs/en/userguide/index.md", "downloaded_repos/silverstripe_silverstripe-elemental/lang/_manifest_exclude", "downloaded_repos/silverstripe_silverstripe-elemental/lang/cs.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/de.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/en.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/fi.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/fr.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/hr.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/it.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/nl.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/pl.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/sl.yml", "downloaded_repos/silverstripe_silverstripe-elemental/lang/sv.yml", "downloaded_repos/silverstripe_silverstripe-elemental/package.json", "downloaded_repos/silverstripe_silverstripe-elemental/phpcs.xml.dist", "downloaded_repos/silverstripe_silverstripe-elemental/phpstan.neon.dist", "downloaded_repos/silverstripe_silverstripe-elemental/phpunit.xml.dist", "downloaded_repos/silverstripe_silverstripe-elemental/src/Controllers/ElementController.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Controllers/ElementalAreaController.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/ElementalAreaUsedOnTableExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/ElementalAreasExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/ElementalContentControllerExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/ElementalPageExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/GridFieldAddNewMultiClassHandlerExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/GridFieldDetailFormItemRequestExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/TopPageElementExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/TopPageFluentElementExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Extensions/TopPageSiteTreeExtension.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Forms/EditFormFactory.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Forms/ElementalAreaConfig.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Forms/ElementalAreaField.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Forms/TextCheckboxGroupField.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Models/BaseElement.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Models/ElementContent.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Models/ElementalArea.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/ORM/Search/ElementalSiteTreeSearchContext.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Reports/ElementTypeReport.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Reports/ElementsInUseReport.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Services/ElementTabProvider.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Services/ElementTypeRegistry.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Services/ReorderElements.php", "downloaded_repos/silverstripe_silverstripe-elemental/src/Tasks/MigrateContentToElement.php", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Controllers/ElementalAdmin_PreviewPanel.ss", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Forms/ElementalAreaField_holder.ss", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Forms/TextCheckboxGroupField.ss", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Layout/ElementHolder.ss", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Models/BaseElement/PreviewIcon.ss", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Models/BaseElement_EditorPreview.ss", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Models/ElementContent.ss", "downloaded_repos/silverstripe_silverstripe-elemental/templates/DNADesign/Elemental/Models/ElementalArea.ss", "downloaded_repos/silverstripe_silverstripe-elemental/webpack.config.js", "downloaded_repos/silverstripe_silverstripe-elemental/yarn.lock"], "skipped": [{"path": "downloaded_repos/silverstripe_silverstripe-elemental/.github/workflows/deploy-userhelp-docs.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/dist/js/bundle.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/dist/styles/bundle.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/tests/AbstractAction-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/tests/ArchiveAction-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/tests/DuplicateAction-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/tests/PublishAction-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/tests/SaveAction-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementActions/tests/UnpublishAction-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/AddElementPopover-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/Content-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/Element-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/ElementActions-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/ElementEditor-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/ElementList-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/Header-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/HoverBar-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/tests/Summary-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/HistoricElementView/tests/HistoricElementView-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/lib/tests/prefixClassNames-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/cs.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/hr.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/pl.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/lang/sl.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/BaseElementTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/Context/FeatureContext.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/Context/FixtureContext.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/Extension/UniqueHtmlEditorConfigExtension.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/add-block-element-to-data-object.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/add-block-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/add-link-to-anchor.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/archive-block-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/broken-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/change-tracking.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/content-blocks-in-use-report.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/edit-block-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/element-editor.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/elemental-reports.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/empty-element-list.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/file-upload.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/inline-block-validation.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/multiple-wysiwyg-configs.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/non-inline-block-validation.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/non-page-save-validation.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/page-save-validation.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/preview-a-block.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/publish-block-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/restore-archived-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/restore-archived-page.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/searchable-dropdown-fields.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/unpublish-block-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/features/versioned-status-block-element.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/files/file1.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Behat/files/file2.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Blocks/TestElementContent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Blocks/TestElementalArea.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Controllers/ElementalAreaControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Controllers/ElementalAreaControllerTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementControllerTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementalAreaDataObjectTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementalAreaTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementalAreaTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementalEditorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementalEditorTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementalPageExtensionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ElementalPageExtensionTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Extensions/ElementalAreasExtensionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Extensions/TopPageElementExtensionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Forms/EditFormFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Forms/EditFormFactoryTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Forms/ElementalAreaFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Forms/TextCheckboxGroupFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ORM/Search/ElementalSiteTreeSearchContextTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/ORM/Search/ElementalSiteTreeSearchContextTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Reports/ElementsInUseReportTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Reports/ElementsInUseReportTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Services/ElementTabProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Services/ReorderElementsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Services/ReorderElementsTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestContentForSearchIndexExtension.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestDataObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestDataObjectWithCMSEditLink.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestElement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestElementContentExtension.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestElementController.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestElementDataObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestMultiHtmlFieldsElement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestPage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestPageController.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestPreviewableDataObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestPreviewableDataObjectWithLink.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestReplacePageContentExtension.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestUnusedElement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/TestVersionedDataObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/ValidationFailedExtension.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/templates/DNADesign/Elemental/Tests/Src/TestElement.ss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Src/templates/DNADesign/Elemental/Tests/Src/TestPage.ss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Tasks/MigrateContentToElementTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/Tasks/MigrateContentToElementTest.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/TopPage/TestBlockPage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/TopPage/TestChildPage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/TopPage/TestContent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/TopPage/TestList.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/TopPage/TopPageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/silverstripe_silverstripe-elemental/tests/TopPage/TopPageTest.yml", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6964380741119385, "profiling_times": {"config_time": 5.8625993728637695, "core_time": 3.3015966415405273, "ignores_time": 0.001623392105102539, "total_time": 9.166699409484863}, "parsing_time": {"total_time": 1.5686044692993164, "per_file_time": {"mean": 0.014939090183803018, "std_dev": 0.0008632677699780616}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 6.386553049087524, "per_file_time": {"mean": 0.013675702460572868, "std_dev": 0.0024361511142117183}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.9249701499938965, "per_file_and_rule_time": {"mean": 0.002267338221429795, "std_dev": 8.498441302912941e-05}, "very_slow_stats": {"time_ratio": 0.1840169137382012, "count_ratio": 0.0035335689045936395}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Element.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.11607503890991211}, {"fpath": "downloaded_repos/silverstripe_silverstripe-elemental/client/src/components/ElementEditor/Element.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.11773490905761719}, {"fpath": "downloaded_repos/silverstripe_silverstripe-elemental/src/Models/BaseElement.php", "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.12041711807250977}]}, "tainting_time": {"total_time": 0.35164427757263184, "per_def_and_rule_time": {"mean": 0.001465184489885966, "std_dev": 1.1123728903043155e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}