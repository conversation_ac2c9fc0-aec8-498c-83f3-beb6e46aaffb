{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 38, "offset": 1099}, "end": {"line": 47, "col": 57, "offset": 1118}}, {"path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 24, "offset": 1099}, "end": {"line": 48, "col": 43, "offset": 1118}}]], "message": "Syntax error at line downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml:47:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 38, "offset": 1099}, "end": {"line": 47, "col": 57, "offset": 1118}}, {"file": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 24, "offset": 1099}, "end": {"line": 48, "col": 43, "offset": 1118}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 53, "offset": 1286}, "end": {"line": 52, "col": 69, "offset": 1302}}, {"path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 97, "offset": 1286}, "end": {"line": 52, "col": 115, "offset": 1304}}, {"path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 19, "offset": 1286}, "end": {"line": 53, "col": 22, "offset": 1289}}]], "message": "Syntax error at line downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml:52:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 53, "offset": 1286}, "end": {"line": 52, "col": 69, "offset": 1302}}, {"file": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 97, "offset": 1286}, "end": {"line": 52, "col": 115, "offset": 1304}}, {"file": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 19, "offset": 1286}, "end": {"line": 53, "col": 22, "offset": 1289}}]}], "paths": {"scanned": ["downloaded_repos/rappasoft_laravel-authentication-log/.editorconfig", "downloaded_repos/rappasoft_laravel-authentication-log/.gitattributes", "downloaded_repos/rappasoft_laravel-authentication-log/.github/CONTRIBUTING.md", "downloaded_repos/rappasoft_laravel-authentication-log/.github/FUNDING.yml", "downloaded_repos/rappasoft_laravel-authentication-log/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/rappasoft_laravel-authentication-log/.github/SECURITY.md", "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/php-cs-fixer.yml", "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "downloaded_repos/rappasoft_laravel-authentication-log/.gitignore", "downloaded_repos/rappasoft_laravel-authentication-log/.php_cs.dist.php", "downloaded_repos/rappasoft_laravel-authentication-log/CHANGELOG.md", "downloaded_repos/rappasoft_laravel-authentication-log/LICENSE.md", "downloaded_repos/rappasoft_laravel-authentication-log/README.md", "downloaded_repos/rappasoft_laravel-authentication-log/composer.json", "downloaded_repos/rappasoft_laravel-authentication-log/config/authentication-log.php", "downloaded_repos/rappasoft_laravel-authentication-log/database/factories/AuthenticationLogFactory.php", "downloaded_repos/rappasoft_laravel-authentication-log/database/migrations/create_authentication_log_table.php.stub", "downloaded_repos/rappasoft_laravel-authentication-log/docs/_index.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/introduction.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/known-issues.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/start/_index.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/start/configuration.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/start/installation.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/usage/_index.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/usage/displaying-log.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/usage/getting-logs.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/usage/notifications.md", "downloaded_repos/rappasoft_laravel-authentication-log/docs/usage/purging-old-logs.md", "downloaded_repos/rappasoft_laravel-authentication-log/phpunit.xml.dist", "downloaded_repos/rappasoft_laravel-authentication-log/resources/lang/en.json", "downloaded_repos/rappasoft_laravel-authentication-log/resources/lang/fr.json", "downloaded_repos/rappasoft_laravel-authentication-log/resources/lang/pt_BR.json", "downloaded_repos/rappasoft_laravel-authentication-log/resources/views/.gitkeep", "downloaded_repos/rappasoft_laravel-authentication-log/resources/views/emails/failed.blade.php", "downloaded_repos/rappasoft_laravel-authentication-log/resources/views/emails/new.blade.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Commands/PurgeAuthenticationLogCommand.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/LaravelAuthenticationLogServiceProvider.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Listeners/FailedLoginListener.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Listeners/LoginListener.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Listeners/LogoutListener.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Listeners/OtherDeviceLogoutListener.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Models/AuthenticationLog.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Notifications/FailedLogin.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Notifications/NewDevice.php", "downloaded_repos/rappasoft_laravel-authentication-log/src/Traits/AuthenticationLoggable.php"], "skipped": [{"path": "downloaded_repos/rappasoft_laravel-authentication-log/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rappasoft_laravel-authentication-log/tests/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rappasoft_laravel-authentication-log/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rappasoft_laravel-authentication-log/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6397180557250977, "profiling_times": {"config_time": 7.415130138397217, "core_time": 2.291940450668335, "ignores_time": 0.0019600391387939453, "total_time": 9.709939241409302}, "parsing_time": {"total_time": 0.28255343437194824, "per_file_time": {"mean": 0.012284931929215141, "std_dev": 7.135291265244726e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.6501479148864746, "per_file_time": {"mean": 0.005753521370676767, "std_dev": 0.00011501093696091287}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.12056112289428711, "per_file_and_rule_time": {"mean": 0.0008146021817181561, "std_dev": 3.412990176920709e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}