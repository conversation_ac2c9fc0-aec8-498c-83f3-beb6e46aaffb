{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/KABBOUCHI_nova-impersonate/.editorconfig", "downloaded_repos/KABBOUCHI_nova-impersonate/.gitignore", "downloaded_repos/KABBOUCHI_nova-impersonate/.nvmrc", "downloaded_repos/KABBOUCHI_nova-impersonate/LICENSE.md", "downloaded_repos/KABBOUCHI_nova-impersonate/README.md", "downloaded_repos/KABBOUCHI_nova-impersonate/composer.json", "downloaded_repos/KABBOUCHI_nova-impersonate/config/nova-impersonate.php", "downloaded_repos/KABBOUCHI_nova-impersonate/docs/screenshot1.png", "downloaded_repos/KABBOUCHI_nova-impersonate/docs/screenshot2.png", "downloaded_repos/KABBOUCHI_nova-impersonate/docs/screenshot3.png", "downloaded_repos/KABBOUCHI_nova-impersonate/mix-manifest.json", "downloaded_repos/KABBOUCHI_nova-impersonate/nova.mix.js", "downloaded_repos/KABBOUCHI_nova-impersonate/package.json", "downloaded_repos/KABBOUCHI_nova-impersonate/resources/js/components/Detail/ImpersonateField.vue", "downloaded_repos/KABBOUCHI_nova-impersonate/resources/js/components/Index/ImpersonateField.vue", "downloaded_repos/KABBOUCHI_nova-impersonate/resources/js/components/Shared/ImpersonateModal.vue", "downloaded_repos/KABBOUCHI_nova-impersonate/resources/js/field.js", "downloaded_repos/KABBOUCHI_nova-impersonate/resources/js/mixins/HandlesFieldValue.js", "downloaded_repos/KABBOUCHI_nova-impersonate/resources/sass/field.scss", "downloaded_repos/KABBOUCHI_nova-impersonate/resources/views/reverse.blade.php", "downloaded_repos/KABBOUCHI_nova-impersonate/routes/api.php", "downloaded_repos/KABBOUCHI_nova-impersonate/src/Contracts/Impersonate.php", "downloaded_repos/KABBOUCHI_nova-impersonate/src/Http/Controllers/ImpersonateController.php", "downloaded_repos/KABBOUCHI_nova-impersonate/src/Http/Middleware/Impersonate.php", "downloaded_repos/KABBOUCHI_nova-impersonate/src/Impersonate.php", "downloaded_repos/KABBOUCHI_nova-impersonate/src/ToolServiceProvider.php", "downloaded_repos/KABBOUCHI_nova-impersonate/vue.config.js", "downloaded_repos/KABBOUCHI_nova-impersonate/webpack.mix.js", "downloaded_repos/KABBOUCHI_nova-impersonate/yarn.lock"], "skipped": [{"path": "downloaded_repos/KABBOUCHI_nova-impersonate/dist/css/field.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/KABBOUCHI_nova-impersonate/dist/js/field.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/KABBOUCHI_nova-impersonate/dist/js/tool.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/KABBOUCHI_nova-impersonate/dist/mix-manifest.json", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8217370510101318, "profiling_times": {"config_time": 6.286924362182617, "core_time": 2.570127010345459, "ignores_time": 0.1323227882385254, "total_time": 8.990567445755005}, "parsing_time": {"total_time": 0.25377607345581055, "per_file_time": {"mean": 0.01586100459098816, "std_dev": 4.1355009527954685e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.8126578330993652, "per_file_time": {"mean": 0.010981862609450884, "std_dev": 0.00039175736494675203}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.09191775321960449, "per_file_and_rule_time": {"mean": 0.0026262215205601286, "std_dev": 8.115785072101174e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0007779598236083984, "per_def_and_rule_time": {"mean": 0.00012965997060139972, "std_dev": 3.0277238099390366e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}