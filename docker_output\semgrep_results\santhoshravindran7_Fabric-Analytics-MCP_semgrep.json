{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-start-claude.cjs", "start": {"line": 16, "col": 21, "offset": 409}, "end": {"line": 16, "col": 76, "offset": 464}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-start-claude.cjs", "start": {"line": 16, "col": 27, "offset": 415}, "end": {"line": 16, "col": 34, "offset": 422}, "extra": {"message": "Detected calls to child_process from a function argument `command`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-test.cjs", "start": {"line": 65, "col": 21, "offset": 2046}, "end": {"line": 69, "col": 7, "offset": 2150}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-test.cjs", "start": {"line": 65, "col": 27, "offset": 2052}, "end": {"line": 65, "col": 34, "offset": 2059}, "extra": {"message": "Detected calls to child_process from a function argument `command`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-test.cjs", "start": {"line": 103, "col": 27, "offset": 2795}, "end": {"line": 106, "col": 7, "offset": 2894}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-test.cjs", "start": {"line": 144, "col": 27, "offset": 3910}, "end": {"line": 147, "col": 7, "offset": 4009}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-test.cjs", "start": {"line": 163, "col": 23, "offset": 4446}, "end": {"line": 163, "col": 27, "offset": 4450}, "extra": {"message": "Checks for any usage of http servers instead of https servers. Encourages the usage of https protocol instead of http, which does not have TLS and is therefore unencrypted. Using http can lead to man-in-the-middle attacks in which the attacker is able to read sensitive information.", "metadata": {"likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": ["A02:2021 - Cryptographic Failures", "A03:2017 - Sensitive Data Exposure"], "references": ["https://nodejs.org/api/http.html#http_class_http_agent", "https://groups.google.com/g/rubyonrails-security/c/NCCsca7TEtY"], "subcategory": ["audit"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "shortlink": "https://sg.run/x1zL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.http-request.http-request", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-test.cjs", "start": {"line": 163, "col": 23, "offset": 4446}, "end": {"line": 166, "col": 9, "offset": 4600}, "extra": {"message": "Checks for requests sent to http:// URLs. This is dangerous as the server is attempting to connect to a website that does not encrypt traffic with TLS. Instead, only send requests to https:// URLs.", "metadata": {"likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": "A03:2017 - Sensitive Data Exposure", "references": ["https://nodejs.org/api/http.html#http_http_request_options_callback"], "subcategory": ["vuln"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.http-request.http-request", "shortlink": "https://sg.run/N4Qy"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/run-all-tests.cjs", "start": {"line": 126, "col": 21, "offset": 3560}, "end": {"line": 129, "col": 9, "offset": 3665}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-before-push.js", "start": {"line": 44, "col": 37, "offset": 1005}, "end": {"line": 44, "col": 44, "offset": 1012}, "extra": {"message": "Detected calls to child_process from a function argument `command`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-before-push.js", "start": {"line": 207, "col": 44, "offset": 7080}, "end": {"line": 207, "col": 47, "offset": 7083}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-before-push.js", "start": {"line": 207, "col": 49, "offset": 7085}, "end": {"line": 207, "col": 53, "offset": 7089}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-copilot-integration.cjs", "start": {"line": 17, "col": 25, "offset": 495}, "end": {"line": 20, "col": 5, "offset": 588}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-direct-tools.cjs", "start": {"line": 28, "col": 23, "offset": 679}, "end": {"line": 31, "col": 9, "offset": 803}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-direct-tools.cjs", "start": {"line": 72, "col": 29, "offset": 2146}, "end": {"line": 76, "col": 9, "offset": 2279}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-direct-tools.cjs", "start": {"line": 127, "col": 27, "offset": 3853}, "end": {"line": 131, "col": 7, "offset": 3978}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-direct-tools.cjs", "start": {"line": 188, "col": 23, "offset": 5553}, "end": {"line": 188, "col": 27, "offset": 5557}, "extra": {"message": "Checks for any usage of http servers instead of https servers. Encourages the usage of https protocol instead of http, which does not have TLS and is therefore unencrypted. Using http can lead to man-in-the-middle attacks in which the attacker is able to read sensitive information.", "metadata": {"likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": ["A02:2021 - Cryptographic Failures", "A03:2017 - Sensitive Data Exposure"], "references": ["https://nodejs.org/api/http.html#http_class_http_agent", "https://groups.google.com/g/rubyonrails-security/c/NCCsca7TEtY"], "subcategory": ["audit"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "shortlink": "https://sg.run/x1zL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-local-tools.cjs", "start": {"line": 108, "col": 26, "offset": 3045}, "end": {"line": 112, "col": 7, "offset": 3170}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-mcp-manual.cjs", "start": {"line": 16, "col": 25, "offset": 323}, "end": {"line": 19, "col": 5, "offset": 416}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-mcp-tools.js", "start": {"line": 7, "col": 29, "offset": 204}, "end": {"line": 7, "col": 2261, "offset": 2436}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-server-startup.cjs", "start": {"line": 12, "col": 23, "offset": 212}, "end": {"line": 15, "col": 3, "offset": 299}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-all-tools.cjs", "start": {"line": 103, "col": 23, "offset": 2541}, "end": {"line": 106, "col": 9, "offset": 2647}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-all-tools.cjs", "start": {"line": 139, "col": 29, "offset": 3560}, "end": {"line": 143, "col": 9, "offset": 3693}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-all-tools.js", "start": {"line": 103, "col": 23, "offset": 2541}, "end": {"line": 106, "col": 9, "offset": 2647}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-all-tools.js", "start": {"line": 139, "col": 29, "offset": 3560}, "end": {"line": 143, "col": 9, "offset": 3693}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-with-copilot.cjs", "start": {"line": 152, "col": 23, "offset": 4669}, "end": {"line": 155, "col": 9, "offset": 4793}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-with-copilot.cjs", "start": {"line": 199, "col": 28, "offset": 6095}, "end": {"line": 203, "col": 9, "offset": 6228}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-with-copilot.cjs", "start": {"line": 296, "col": 23, "offset": 8925}, "end": {"line": 296, "col": 27, "offset": 8929}, "extra": {"message": "Checks for any usage of http servers instead of https servers. Encourages the usage of https protocol instead of http, which does not have TLS and is therefore unencrypted. Using http can lead to man-in-the-middle attacks in which the attacker is able to read sensitive information.", "metadata": {"likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": ["A02:2021 - Cryptographic Failures", "A03:2017 - Sensitive Data Exposure"], "references": ["https://nodejs.org/api/http.html#http_class_http_agent", "https://groups.google.com/g/rubyonrails-security/c/NCCsca7TEtY"], "subcategory": ["audit"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "shortlink": "https://sg.run/x1zL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-child-process.detect-child-process", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate.js", "start": {"line": 45, "col": 37, "offset": 1163}, "end": {"line": 45, "col": 44, "offset": 1170}, "extra": {"message": "Detected calls to child_process from a function argument `command`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed. ", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html#do-not-use-dangerous-functions"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-child-process.js", "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-child-process.detect-child-process", "shortlink": "https://sg.run/l2lo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate.js", "start": {"line": 234, "col": 48, "offset": 8049}, "end": {"line": 234, "col": 51, "offset": 8052}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate.js", "start": {"line": 234, "col": 53, "offset": 8054}, "end": {"line": 234, "col": 57, "offset": 8058}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 135, "col": 12, "offset": 3489}, "end": {"line": 135, "col": 38, "offset": 3515}}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 135, "col": 56, "offset": 3489}, "end": {"line": 135, "col": 78, "offset": 3511}}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 135, "col": 96, "offset": 3489}, "end": {"line": 135, "col": 118, "offset": 3511}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml:135:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ secrets.ACR_LOGIN_SERVER` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 135, "col": 12, "offset": 3489}, "end": {"line": 135, "col": 38, "offset": 3515}}, {"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 135, "col": 56, "offset": 3489}, "end": {"line": 135, "col": 78, "offset": 3511}}, {"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 135, "col": 96, "offset": 3489}, "end": {"line": 135, "col": 118, "offset": 3511}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 194, "col": 85, "offset": 5629}, "end": {"line": 194, "col": 88, "offset": 5632}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml:194:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 194, "col": 85, "offset": 5629}, "end": {"line": 194, "col": 88, "offset": 5632}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 218, "col": 10, "offset": 6335}, "end": {"line": 218, "col": 13, "offset": 6338}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml:218:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 218, "col": 10, "offset": 6335}, "end": {"line": 218, "col": 13, "offset": 6338}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 228, "col": 14, "offset": 6714}, "end": {"line": 228, "col": 54, "offset": 6754}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml:228:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ needs.build-and-push.outputs.image-tag` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 228, "col": 14, "offset": 6714}, "end": {"line": 228, "col": 54, "offset": 6754}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 284, "col": 10, "offset": 8785}, "end": {"line": 284, "col": 13, "offset": 8788}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml:284:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 284, "col": 10, "offset": 8785}, "end": {"line": 284, "col": 13, "offset": 8788}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 290, "col": 14, "offset": 9022}, "end": {"line": 290, "col": 54, "offset": 9062}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml:290:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ needs.build-and-push.outputs.image-tag` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 290, "col": 14, "offset": 9022}, "end": {"line": 290, "col": 54, "offset": 9062}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 363, "col": 16, "offset": 11503}, "end": {"line": 363, "col": 19, "offset": 11506}}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 369, "col": 16, "offset": 11503}, "end": {"line": 369, "col": 19, "offset": 11506}}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 370, "col": 17, "offset": 11503}, "end": {"line": 370, "col": 20, "offset": 11506}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml:363:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 363, "col": 16, "offset": 11503}, "end": {"line": 363, "col": 19, "offset": 11506}}, {"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 369, "col": 16, "offset": 11503}, "end": {"line": 369, "col": 19, "offset": 11506}}, {"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "start": {"line": 370, "col": 17, "offset": 11503}, "end": {"line": 370, "col": 20, "offset": 11506}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/validate-deployment.sh", "start": {"line": 210, "col": 52, "offset": 0}, "end": {"line": 210, "col": 61, "offset": 9}}]], "message": "Syntax error at line downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/validate-deployment.sh:210:\n `/ 1000000` was unexpected", "path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/validate-deployment.sh", "spans": [{"file": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/validate-deployment.sh", "start": {"line": 210, "col": 52, "offset": 0}, "end": {"line": 210, "col": 61, "offset": 9}}]}], "paths": {"scanned": ["downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/copilot-instructions.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/test.yml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.gitignore", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.vscode/launch.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.vscode/mcp.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.vscode/tasks.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/AKS_DEPLOYMENT.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/AUTHENTICATION_SETUP.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/CONTRIBUTING.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/Dockerfile", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/EXAMPLES.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/GITHUB_COPILOT_VALIDATION.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/LICENSE", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/PRODUCTION_READY.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/README.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/SECURITY.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/SETUP_COMPLETE.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/TESTING_GUIDE.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/TROUBLESHOOTING.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/auth_client.py", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/babel.config.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/claude-desktop-config.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/cleanup_for_release.py", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/commitlint.config.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/config.template.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/count-tools.mjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/docker-compose.yml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/docs/AZURE_CLI_AUTH.md", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/enhanced_auth_test.py", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/eslint.config.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/eslint.config.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/final-check.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/generate-claude-config.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/github-copilot-capabilities.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/jest.config.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/configmap.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/deployment.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/hpa.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/ingress.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/namespace.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/rbac.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/secret.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/k8s/service.yaml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/livy_api_test.ipynb", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/mcp_spark_monitoring_demo.py", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/monitoring/grafana/dashboards/dashboard.yml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/monitoring/grafana/datasources/prometheus.yml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/monitoring/prometheus.yml", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/package-lock.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/package.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-start-claude.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-test.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/quick-validate.mjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/requirements.txt", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/sample-notebook.ipynb", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/build-and-push.sh", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/deploy-to-aks.sh", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/run-all-tests.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/setup-azure-resources.sh", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-all-functionality.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-azure-cli-auth.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-azure-cli-auth.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-crud-operations.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-livy-integration.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-mcp-azure-cli.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-notebook-management.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/test-spark-monitoring.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/validate-deployment.sh", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/setup-claude-test.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/setup-e2e.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/spark_monitoring_test.py", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/auth-client.ts", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/fabric-client.ts", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index-clean.ts", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index-consolidated.ts", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index-refactored.ts", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/simulation-service.ts", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-before-push.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-before-push.ps1", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-cli-auth.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-copilot-integration.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-direct-tools.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-end-to-end.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-final-count.mjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-local-tools.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-mcp-manual.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-mcp-server.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-mcp-tools.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-mcp-tools.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-mcp.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-server-startup.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test-tool-count.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/test_mcp_server.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tsconfig.json", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-all-tools.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-all-tools.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-azure-cli.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-claude-test.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-complete-setup.mjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-config.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-with-copilot.cjs", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate.js", "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate_claude_setup.py"], "skipped": [{"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/.github/workflows/deploy.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/scripts/validate-deployment.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/basic.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/basic.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/basic.test.ts.disabled", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/config.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/config.test.ts.disabled", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/fabric-client-simple.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/fabric-client.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/fabric-client.test.ts.backup", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/fabric-client.test.ts.disabled", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/integration.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/integration.test.ts.disabled", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/server.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/server.test.ts.disabled", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/tests/setup.ts", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7682979106903076, "profiling_times": {"config_time": 6.862121820449829, "core_time": 7.721758604049683, "ignores_time": 0.0017437934875488281, "total_time": 14.586661338806152}, "parsing_time": {"total_time": 4.536123275756836, "per_file_time": {"mean": 0.050967677255694815, "std_dev": 0.013445180913159983}, "very_slow_stats": {"time_ratio": 0.2296244904309288, "count_ratio": 0.011235955056179775}, "very_slow_files": [{"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "ftime": 1.041604995727539}]}, "scanning_time": {"total_time": 32.493114709854126, "per_file_time": {"mean": 0.10795054720881776, "std_dev": 0.1380014832956365}, "very_slow_stats": {"time_ratio": 0.2517103792633845, "count_ratio": 0.009966777408637873}, "very_slow_files": [{"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-with-copilot.cjs", "ftime": 1.5279510021209717}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/spark_monitoring_test.py", "ftime": 1.808096170425415}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "ftime": 4.842807054519653}]}, "matching_time": {"total_time": 16.006104469299316, "per_file_and_rule_time": {"mean": 0.014834202473864053, "std_dev": 0.0028401528791396707}, "very_slow_stats": {"time_ratio": 0.3757056509332881, "count_ratio": 0.022242817423540315}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index-clean.ts", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.1673269271850586}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/spark_monitoring_test.py", "rule_id": "python.django.security.injection.raw-html-format.raw-html-format", "time": 0.17792391777038574}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index-refactored.ts", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.18407201766967773}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.20827198028564453}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index-refactored.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.2807772159576416}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.29419684410095215}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/enhanced_auth_test.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.3871419429779053}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.559128999710083}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/spark_monitoring_test.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.9470059871673584}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.0079591274261475}]}, "tainting_time": {"total_time": 6.47005033493042, "per_def_and_rule_time": {"mean": 0.0020817407770046398, "std_dev": 6.761379514642379e-05}, "very_slow_stats": {"time_ratio": 0.12063539862649857, "count_ratio": 0.0019305019305019305}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "fline": 1, "rule_id": "javascript.lang.security.detect-child-process.detect-child-process", "time": 0.05383706092834473}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.058320045471191406}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/cleanup_for_release.py", "fline": 11, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.0887610912322998}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.ts", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.09810686111450195}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/src/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.1271820068359375}, {"fpath": "downloaded_repos/santhoshravindran7_Fabric-Analytics-MCP/validate-with-copilot.cjs", "fline": 410, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.3543100357055664}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}