{"version": "1.130.0", "results": [{"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_configs.html", "start": {"line": 2, "col": 1, "offset": 1}, "end": {"line": 17, "col": 8, "offset": 396}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_plugins.html", "start": {"line": 118, "col": 5, "offset": 5265}, "end": {"line": 138, "col": 12, "offset": 6257}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_rules.html", "start": {"line": 18, "col": 1, "offset": 282}, "end": {"line": 30, "col": 8, "offset": 606}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 483, "col": 9, "offset": 20501}, "end": {"line": 483, "col": 94, "offset": 20586}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/index.html", "start": {"line": 51, "col": 9, "offset": 1239}, "end": {"line": 51, "col": 76, "offset": 1306}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/index.html", "start": {"line": 51, "col": 9, "offset": 1239}, "end": {"line": 51, "col": 76, "offset": 1306}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/index.html", "start": {"line": 51, "col": 9, "offset": 1239}, "end": {"line": 51, "col": 76, "offset": 1306}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "start": {"line": 168, "col": 16, "offset": 6288}, "end": {"line": 168, "col": 41, "offset": 6313}, "extra": {"message": "RegExp() called with a `configTypes` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/index.html", "start": {"line": 51, "col": 9, "offset": 1239}, "end": {"line": 51, "col": 76, "offset": 1306}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 1401, "col": 30, "offset": 47259}, "end": {"line": 1401, "col": 79, "offset": 47308}, "extra": {"message": "RegExp() called with a `decimalPoint` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 2649, "col": 9, "offset": 81456}, "end": {"line": 2649, "col": 28, "offset": 81475}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 2685, "col": 7, "offset": 82583}, "end": {"line": 2685, "col": 26, "offset": 82602}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 2795, "col": 6, "offset": 85766}, "end": {"line": 2795, "col": 25, "offset": 85785}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 2910, "col": 4, "offset": 88849}, "end": {"line": 2910, "col": 72, "offset": 88917}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 3126, "col": 6, "offset": 94588}, "end": {"line": 3126, "col": 70, "offset": 94652}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 4471, "col": 12, "offset": 132715}, "end": {"line": 4471, "col": 33, "offset": 132736}, "extra": {"message": "`word.replace` method will only replace the first occurrence when used with a string argument ('\"'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 4477, "col": 10, "offset": 132813}, "end": {"line": 4477, "col": 58, "offset": 132861}, "extra": {"message": "RegExp() called with a `search` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 4536, "col": 7, "offset": 134643}, "end": {"line": 4536, "col": 41, "offset": 134677}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 5380, "col": 4, "offset": 157811}, "end": {"line": 5380, "col": 117, "offset": 157924}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "start": {"line": 5387, "col": 5, "offset": 158058}, "end": {"line": 5387, "col": 118, "offset": 158171}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/admin/index.php", "start": {"line": 41, "col": 40, "offset": 873}, "end": {"line": 41, "col": 132, "offset": 965}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "start": {"line": 1, "col": 942, "offset": 941}, "end": {"line": 1, "col": 1055, "offset": 1054}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "start": {"line": 1, "col": 1284, "offset": 1283}, "end": {"line": 1, "col": 1349, "offset": 1348}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "start": {"line": 1, "col": 1352, "offset": 1351}, "end": {"line": 1, "col": 1420, "offset": 1419}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "start": {"line": 1, "col": 1423, "offset": 1422}, "end": {"line": 1, "col": 1489, "offset": 1488}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "start": {"line": 1, "col": 942, "offset": 941}, "end": {"line": 1, "col": 1055, "offset": 1054}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "start": {"line": 1, "col": 1284, "offset": 1283}, "end": {"line": 1, "col": 1349, "offset": 1348}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "start": {"line": 1, "col": 1352, "offset": 1351}, "end": {"line": 1, "col": 1420, "offset": 1419}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "start": {"line": 1, "col": 1423, "offset": 1422}, "end": {"line": 1, "col": 1489, "offset": 1488}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/images/index.html", "start": {"line": 51, "col": 9, "offset": 1239}, "end": {"line": 51, "col": 76, "offset": 1306}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/kleeja-official_kleeja/includes/FetchFile.php", "start": {"line": 105, "col": 9, "offset": 2087}, "end": {"line": 105, "col": 56, "offset": 2134}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php", "start": {"line": 80, "col": 29, "offset": 2485}, "end": {"line": 80, "col": 78, "offset": 2534}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php", "start": {"line": 157, "col": 46, "offset": 4653}, "end": {"line": 157, "col": 107, "offset": 4714}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php", "start": {"line": 257, "col": 49, "offset": 7577}, "end": {"line": 257, "col": 110, "offset": 7638}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php", "start": {"line": 366, "col": 54, "offset": 11835}, "end": {"line": 366, "col": 104, "offset": 11885}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/d_img_ctrl.php", "start": {"line": 67, "col": 29, "offset": 1624}, "end": {"line": 67, "col": 78, "offset": 1673}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/g_users.php", "start": {"line": 1084, "col": 18, "offset": 34741}, "end": {"line": 1084, "col": 79, "offset": 34802}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/j_plugins.php", "start": {"line": 281, "col": 14, "offset": 10602}, "end": {"line": 281, "col": 56, "offset": 10644}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/k_ban.php", "start": {"line": 59, "col": 13, "offset": 1381}, "end": {"line": 59, "col": 37, "offset": 1405}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/m_styles.php", "start": {"line": 299, "col": 10, "offset": 10245}, "end": {"line": 299, "col": 51, "offset": 10286}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/p_check_update.php", "start": {"line": 19, "col": 20, "offset": 239}, "end": {"line": 19, "col": 55, "offset": 274}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/p_check_update.php", "start": {"line": 271, "col": 45, "offset": 8361}, "end": {"line": 271, "col": 99, "offset": 8415}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/adm/start.php", "start": {"line": 87, "col": 7, "offset": 4255}, "end": {"line": 87, "col": 42, "offset": 4290}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/kleeja-official_kleeja/includes/common.php", "start": {"line": 286, "col": 27, "offset": 7342}, "end": {"line": 286, "col": 79, "offset": 7394}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/functions.php", "start": {"line": 369, "col": 21, "offset": 10153}, "end": {"line": 369, "col": 49, "offset": 10181}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/functions.php", "start": {"line": 376, "col": 20, "offset": 10304}, "end": {"line": 376, "col": 37, "offset": 10321}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/includes/functions_display.php", "start": {"line": 432, "col": 9, "offset": 16701}, "end": {"line": 432, "col": 31, "offset": 16723}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/style.php", "start": {"line": 368, "col": 13, "offset": 12584}, "end": {"line": 368, "col": 61, "offset": 12632}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/includes/up_methods/defaultUploader.php", "start": {"line": 386, "col": 59, "offset": 11430}, "end": {"line": 386, "col": 139, "offset": 11510}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/includes/usr.php", "start": {"line": 102, "col": 25, "offset": 2852}, "end": {"line": 102, "col": 53, "offset": 2880}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/usr.php", "start": {"line": 282, "col": 25, "offset": 9835}, "end": {"line": 285, "col": 86, "offset": 10356}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/includes/usr.php", "start": {"line": 426, "col": 18, "offset": 15906}, "end": {"line": 426, "col": 95, "offset": 15983}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/kleeja-official_kleeja/includes/usr.php", "start": {"line": 436, "col": 49, "offset": 16358}, "end": {"line": 436, "col": 84, "offset": 16393}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/kleeja-official_kleeja/install/includes/functions_install.php", "start": {"line": 80, "col": 9, "offset": 1654}, "end": {"line": 80, "col": 39, "offset": 1684}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/check.html", "start": {"line": 69, "col": 9, "offset": 3228}, "end": {"line": 72, "col": 16, "offset": 3675}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/check.html", "start": {"line": 74, "col": 9, "offset": 3698}, "end": {"line": 77, "col": 16, "offset": 4144}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/check_all.html", "start": {"line": 55, "col": 9, "offset": 2337}, "end": {"line": 58, "col": 16, "offset": 2769}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/check_all.html", "start": {"line": 60, "col": 9, "offset": 2792}, "end": {"line": 63, "col": 16, "offset": 3248}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/configs.html", "start": {"line": 63, "col": 9, "offset": 2929}, "end": {"line": 68, "col": 16, "offset": 3409}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/configs.html", "start": {"line": 77, "col": 9, "offset": 3673}, "end": {"line": 80, "col": 16, "offset": 4116}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/header.html", "start": {"line": 24, "col": 17, "offset": 1329}, "end": {"line": 33, "col": 24, "offset": 2323}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "start": {"line": 1, "col": 942, "offset": 941}, "end": {"line": 1, "col": 1055, "offset": 1054}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "start": {"line": 1, "col": 1284, "offset": 1283}, "end": {"line": 1, "col": 1349, "offset": 1348}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "start": {"line": 1, "col": 1352, "offset": 1351}, "end": {"line": 1, "col": 1420, "offset": 1419}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "start": {"line": 1, "col": 1423, "offset": 1422}, "end": {"line": 1, "col": 1489, "offset": 1488}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/official.html", "start": {"line": 2, "col": 5, "offset": 119}, "end": {"line": 10, "col": 12, "offset": 778}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/sqls_done.html", "start": {"line": 12, "col": 5, "offset": 426}, "end": {"line": 14, "col": 12, "offset": 628}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/update_list.html", "start": {"line": 7, "col": 5, "offset": 312}, "end": {"line": 16, "col": 12, "offset": 1012}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/kleeja-official_kleeja/install/style/what_is_kleeja.html", "start": {"line": 10, "col": 5, "offset": 410}, "end": {"line": 13, "col": 12, "offset": 840}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "start": {"line": 1, "col": 942, "offset": 941}, "end": {"line": 1, "col": 1055, "offset": 1054}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "start": {"line": 1, "col": 1284, "offset": 1283}, "end": {"line": 1, "col": 1349, "offset": 1348}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "start": {"line": 1, "col": 1352, "offset": 1351}, "end": {"line": 1, "col": 1420, "offset": 1419}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "start": {"line": 1, "col": 1423, "offset": 1422}, "end": {"line": 1, "col": 1489, "offset": 1488}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "start": {"line": 1, "col": 942, "offset": 941}, "end": {"line": 1, "col": 1055, "offset": 1054}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "start": {"line": 1, "col": 1284, "offset": 1283}, "end": {"line": 1, "col": 1349, "offset": 1348}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "start": {"line": 1, "col": 1352, "offset": 1351}, "end": {"line": 1, "col": 1420, "offset": 1419}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "start": {"line": 1, "col": 1423, "offset": 1422}, "end": {"line": 1, "col": 1489, "offset": 1488}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "start": {"line": 1, "col": 942, "offset": 941}, "end": {"line": 1, "col": 1055, "offset": 1054}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "start": {"line": 1, "col": 1284, "offset": 1283}, "end": {"line": 1, "col": 1349, "offset": 1348}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "start": {"line": 1, "col": 1352, "offset": 1351}, "end": {"line": 1, "col": 1420, "offset": 1419}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "start": {"line": 1, "col": 1423, "offset": 1422}, "end": {"line": 1, "col": 1489, "offset": 1488}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/kleeja-official_kleeja/ucp.php", "start": {"line": 673, "col": 21, "offset": 27664}, "end": {"line": 673, "col": 53, "offset": 27696}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/kleeja-official_kleeja/uploads/thumbs/index.html", "start": {"line": 51, "col": 9, "offset": 1239}, "end": {"line": 51, "col": 76, "offset": 1306}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "message": "Timeout when running javascript.express.security.cors-misconfiguration.cors-misconfiguration on downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js:\n ", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js:\n ", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "message": "Timeout when running javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration on downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js:\n ", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_footer.html", "start": {"line": 238, "col": 1, "offset": 0}, "end": {"line": 239, "col": 8, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_footer.html:238:\n `</body>\n</html>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_footer.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_footer.html", "start": {"line": 238, "col": 1, "offset": 0}, "end": {"line": 239, "col": 8, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_header.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 125, "col": 34, "offset": 5456}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_header.html:2:\n `<html lang=\"{lang.LANG_SMALL_NAME}\">\n\n<head>\n\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n\n    <title>{lang.KLEEJA_CP} - {config.sitename}</title>\n\n    <link rel=\"shortcut icon\" href=\"{STYLE_PATH_ADMIN}images/favicon.ico\" />\n    <link rel=\"icon\" type=\"image/png\" href=\"{STYLE_PATH_ADMIN}images/favicon.png\" />\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"{STYLE_PATH_ADMIN}css/bootstrap.min.css\" rel=\"stylesheet\">\n\n    <!-- Custom fonts for this template -->\n    <link href=\"{STYLE_PATH_ADMIN}css/font-awesome.min.css\" rel=\"stylesheet\" type=\"text/css\">\n\n    <!-- Plugin CSS -->\n    <link href=\"{STYLE_PATH_ADMIN}css/dataTables.bootstrap4.css\" rel=\"stylesheet\">\n\n    <!-- Custom styles for this template -->\n    <link href=\"{STYLE_PATH_ADMIN}css/sb-admin.css\" rel=\"stylesheet\">\n    <link href=\"{STYLE_PATH_ADMIN}css/custom.css\" rel=\"stylesheet\">\n\n    <IF NAME=\"lang.DIR==rtl\">\n        <link rel=\"stylesheet\" type=\"text/css\"  href=\"{STYLE_PATH_ADMIN}css/bootstrap.rtl.min.css\" />\n        <link rel=\"stylesheet\" type=\"text/css\" href=\"{STYLE_PATH_ADMIN}css/sb-admin-rtl.css\" />\n\n\n        <style type=\"text/css\">\n            body{font-family: \"Tahoma\",Arial,sans-serif;}\n            body{\n                direction: rtl; /* placing it here fixes the issue with jquery white page bug */\n            }\n        </style>\n    </IF>\n\n\n    <script type=\"text/javascript\">\n        var STYLE_PATH_ADMIN = '{STYLE_PATH_ADMIN}';\n        var go_to = '{go_to}';\n    </script>\n<IF ISSET=\"extra_admin_header_code\">\n{extra_admin_header_code}\n</IF>\n</head>\n\n<body class=\"fixed-nav sticky-footer bg-{admin_theme_color}\" id=\"page-top\">\n\n\n\n<!-- Navigation -->\n<nav class=\"navbar navbar-expand-lg navbar-{admin_theme_color} bg-{admin_theme_color} fixed-top\" id=\"mainNav\">\n\n    <a class=\"navbar-brand\" href=\"./\"><img src=\"{STYLE_PATH_ADMIN}/images/kleeja.png\" alt=\"Kleeja\" style=\"margin:2px 8px;vertical-align:middle;width:24px\"/> Kleeja ... (truncated 3425 more characters)", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_header.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_header.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 125, "col": 34, "offset": 5456}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 123, "offset": 0}, "end": {"line": 62, "col": 124, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 126, "offset": 0}, "end": {"line": 62, "col": 127, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 142, "offset": 0}, "end": {"line": 62, "col": 144, "offset": 2}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 168, "offset": 0}, "end": {"line": 62, "col": 176, "offset": 8}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 63, "col": 124, "offset": 0}, "end": {"line": 63, "col": 125, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 63, "col": 127, "offset": 0}, "end": {"line": 63, "col": 128, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 63, "col": 144, "offset": 0}, "end": {"line": 63, "col": 145, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 64, "col": 29, "offset": 0}, "end": {"line": 72, "col": 6, "offset": 169}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 103, "col": 1, "offset": 0}, "end": {"line": 103, "col": 23, "offset": 22}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 218, "col": 219, "offset": 0}, "end": {"line": 218, "col": 220, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 239, "col": 218, "offset": 0}, "end": {"line": 239, "col": 219, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 485, "col": 1, "offset": 0}, "end": {"line": 485, "col": 6, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html:62:\n `=` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 123, "offset": 0}, "end": {"line": 62, "col": 124, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 126, "offset": 0}, "end": {"line": 62, "col": 127, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 142, "offset": 0}, "end": {"line": 62, "col": 144, "offset": 2}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 62, "col": 168, "offset": 0}, "end": {"line": 62, "col": 176, "offset": 8}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 63, "col": 124, "offset": 0}, "end": {"line": 63, "col": 125, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 63, "col": 127, "offset": 0}, "end": {"line": 63, "col": 128, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 63, "col": 144, "offset": 0}, "end": {"line": 63, "col": 145, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 64, "col": 29, "offset": 0}, "end": {"line": 72, "col": 6, "offset": 169}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 103, "col": 1, "offset": 0}, "end": {"line": 103, "col": 23, "offset": 22}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 218, "col": 219, "offset": 0}, "end": {"line": 218, "col": 220, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 239, "col": 218, "offset": 0}, "end": {"line": 239, "col": 219, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "start": {"line": 485, "col": 1, "offset": 0}, "end": {"line": 485, "col": 6, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 358, "col": 84, "offset": 0}, "end": {"line": 358, "col": 93, "offset": 9}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 423, "col": 80, "offset": 0}, "end": {"line": 423, "col": 91, "offset": 11}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 424, "col": 86, "offset": 0}, "end": {"line": 424, "col": 96, "offset": 10}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 435, "col": 88, "offset": 0}, "end": {"line": 435, "col": 97, "offset": 9}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 446, "col": 76, "offset": 0}, "end": {"line": 446, "col": 87, "offset": 11}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 447, "col": 82, "offset": 0}, "end": {"line": 447, "col": 92, "offset": 10}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 62, "offset": 0}, "end": {"line": 497, "col": 63, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 66, "offset": 0}, "end": {"line": 497, "col": 67, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 82, "offset": 0}, "end": {"line": 497, "col": 84, "offset": 2}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 110, "offset": 0}, "end": {"line": 497, "col": 118, "offset": 8}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 498, "col": 63, "offset": 0}, "end": {"line": 498, "col": 64, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 498, "col": 67, "offset": 0}, "end": {"line": 498, "col": 68, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 498, "col": 84, "offset": 0}, "end": {"line": 498, "col": 85, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 500, "col": 152, "offset": 0}, "end": {"line": 506, "col": 10, "offset": 79}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 513, "col": 1, "offset": 0}, "end": {"line": 514, "col": 8, "offset": 22}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 775, "col": 180, "offset": 0}, "end": {"line": 776, "col": 27, "offset": 28}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 781, "col": 180, "offset": 0}, "end": {"line": 782, "col": 26, "offset": 27}}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 806, "col": 1, "offset": 0}, "end": {"line": 806, "col": 6, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html:358:\n `>{{name}}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 358, "col": 84, "offset": 0}, "end": {"line": 358, "col": 93, "offset": 9}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 423, "col": 80, "offset": 0}, "end": {"line": 423, "col": 91, "offset": 11}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 424, "col": 86, "offset": 0}, "end": {"line": 424, "col": 96, "offset": 10}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 435, "col": 88, "offset": 0}, "end": {"line": 435, "col": 97, "offset": 9}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 446, "col": 76, "offset": 0}, "end": {"line": 446, "col": 87, "offset": 11}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 447, "col": 82, "offset": 0}, "end": {"line": 447, "col": 92, "offset": 10}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 62, "offset": 0}, "end": {"line": 497, "col": 63, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 66, "offset": 0}, "end": {"line": 497, "col": 67, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 82, "offset": 0}, "end": {"line": 497, "col": 84, "offset": 2}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 497, "col": 110, "offset": 0}, "end": {"line": 497, "col": 118, "offset": 8}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 498, "col": 63, "offset": 0}, "end": {"line": 498, "col": 64, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 498, "col": 67, "offset": 0}, "end": {"line": 498, "col": 68, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 498, "col": 84, "offset": 0}, "end": {"line": 498, "col": 85, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 500, "col": 152, "offset": 0}, "end": {"line": 506, "col": 10, "offset": 79}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 513, "col": 1, "offset": 0}, "end": {"line": 514, "col": 8, "offset": 22}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 775, "col": 180, "offset": 0}, "end": {"line": 776, "col": 27, "offset": 28}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 781, "col": 180, "offset": 0}, "end": {"line": 782, "col": 26, "offset": 27}}, {"file": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "start": {"line": 806, "col": 1, "offset": 0}, "end": {"line": 806, "col": 6, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html:1:\n `>>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html:1:\n `>>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/check.html", "start": {"line": 37, "col": 131, "offset": 0}, "end": {"line": 37, "col": 132, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/check.html:37:\n `>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/check.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/check.html", "start": {"line": 37, "col": 131, "offset": 0}, "end": {"line": 37, "col": 132, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/check_all.html", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 87, "offset": 37}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/check_all.html:17:\n `&& $GLOBALS['dbtype'] == 'sqlite'):}}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/check_all.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/check_all.html", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 87, "offset": 37}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 53, "col": 58, "offset": 0}, "end": {"line": 53, "col": 65, "offset": 7}}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 54, "col": 69, "offset": 0}, "end": {"line": 54, "col": 70, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 54, "col": 145, "offset": 0}, "end": {"line": 54, "col": 166, "offset": 21}}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 87, "col": 45, "offset": 0}, "end": {"line": 87, "col": 51, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/data.html:53:\n `>$t):}}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 53, "col": 58, "offset": 0}, "end": {"line": 53, "col": 65, "offset": 7}}, {"file": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 54, "col": 69, "offset": 0}, "end": {"line": 54, "col": 70, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 54, "col": 145, "offset": 0}, "end": {"line": 54, "col": 166, "offset": 21}}, {"file": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "start": {"line": 87, "col": 45, "offset": 0}, "end": {"line": 87, "col": 51, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/header.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 41, "col": 34, "offset": 2499}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/header.html:2:\n `<html lang=\"{{echo getlang()}}\" dir=\"{{echo $lang['DIR']}}\">\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n<meta name=\"robots\" content=\"noindex, follow\" />\n<title>{{echo $lang['INST_INSTALL_WIZARD']}}</title>\n<link rel=\"shortcut icon\" href=\"../images/favicon.ico\" />\n<link rel=\"icon\" type=\"image/gif\" href=\"../images/favicon.gif\" />\n<style type=\"text/css\">{{echo gettpl('style.css')}}</style>\n<!--[if IE]><style type=\"text/css\">.divline,button.btn span{margin-top:0;}.wrapper {background-position: center -5px;}</style><![endif]-->\n<script type=\"text/javascript\">\nvar PATH_SPACER = 'style/images/spacer.gif';\n</script>\n</head>\n<body>\n    <div id=\"Header\">\n        <div class=\"header\">\n            <ul class=\"headerIcon\">\n                <li><a onclick=\"window.open(this.href,'_blank');return false;\" href=\"https://kleeja.net\"><img src=\"style/images/kleeja.png\" title=\"kleeja.net\" alt=\"\" /></a></li>\n                <li><a href=\"./\"><img title=\"Start over\" src=\"style/images/install_h.png\" alt=\"\" /></a></li>\n            </ul>\n            <div class=\"BoxLang\">\n            {{if(((ig('step') && g('step') != 'language') && (strpos('index.php', $_SERVER['PHP_SELF'])=== false && ig('step'))) OR (ig('step') && g('step') == 'license' || g('step') == 'action_file') ):}}\n                <form action=\"?step={{echo g('step')}}&amp;change_lang=1\" method=\"post\">\n                    <select name=\"lang\" id=\"lang\" class=\"Lang\" onchange=\"submit()\">\n                    {{if($dh = opendir(PATH . 'lang')):}}\n                        {{while (($file = readdir($dh)) !== false): if(strpos($file, '.') === false && $file != '..' && $file != '.'):}}\n                        {{$current_icon=file_exists('style/images/'.$file.'_16.png') ? 'style/images/'.$file.'_16.png' : (file_exists('../lang/'.$file.'/icon_16.png') ? '../lang/'.$file.'/icon_16.png' : 'style/images/zz_flag.png');}}\n                        <option value=\"{{echo $file}}\" title=\"{{echo $current_icon}}\" {{echo ig('lang') && $file==g('lang') || (!ig('lang') && $file... (truncated 468 more characters)", "path": "downloaded_repos/kleeja-official_kleeja/install/style/header.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/header.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 41, "col": 34, "offset": 2499}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/images/index.html:1:\n `>>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/lang.html", "start": {"line": 6, "col": 120, "offset": 0}, "end": {"line": 6, "col": 156, "offset": 36}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/lang.html:6:\n `&& $file != '..' && $file != '.'):}}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/lang.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/lang.html", "start": {"line": 6, "col": 120, "offset": 0}, "end": {"line": 6, "col": 156, "offset": 36}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/license.html", "start": {"line": 5, "col": 20, "offset": 0}, "end": {"line": 5, "col": 43, "offset": 23}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/license.html:5:\n `<?php echo date('Y');?>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/license.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/license.html", "start": {"line": 5, "col": 20, "offset": 0}, "end": {"line": 5, "col": 43, "offset": 23}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/update_end.html", "start": {"line": 7, "col": 91, "offset": 0}, "end": {"line": 7, "col": 98, "offset": 7}}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/update_end.html", "start": {"line": 19, "col": 91, "offset": 0}, "end": {"line": 19, "col": 98, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/update_end.html:7:\n `>$m):}}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/update_end.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/update_end.html", "start": {"line": 7, "col": 91, "offset": 0}, "end": {"line": 7, "col": 98, "offset": 7}}, {"file": "downloaded_repos/kleeja-official_kleeja/install/style/update_end.html", "start": {"line": 19, "col": 91, "offset": 0}, "end": {"line": 19, "col": 98, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/install/style/update_list.html", "start": {"line": 9, "col": 49, "offset": 0}, "end": {"line": 9, "col": 56, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/install/style/update_list.html:9:\n `>$v):}}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/install/style/update_list.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/install/style/update_list.html", "start": {"line": 9, "col": 49, "offset": 0}, "end": {"line": 9, "col": 56, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html:1:\n `>>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/fileuser.html", "start": {"line": 81, "col": 1, "offset": 0}, "end": {"line": 81, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/bootstrap/fileuser.html:81:\n `{page_nums}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/fileuser.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/fileuser.html", "start": {"line": 81, "col": 1, "offset": 0}, "end": {"line": 81, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 37, "col": 18, "offset": 48}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "start": {"line": 44, "col": 1, "offset": 0}, "end": {"line": 44, "col": 12, "offset": 11}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "start": {"line": 138, "col": 1, "offset": 0}, "end": {"line": 139, "col": 8, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html:34:\n `</div><!-- end container -->\n\n\n{googleanalytics}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 37, "col": 18, "offset": 48}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "start": {"line": 44, "col": 1, "offset": 0}, "end": {"line": 44, "col": 12, "offset": 11}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "start": {"line": 138, "col": 1, "offset": 0}, "end": {"line": 139, "col": 8, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/header.html", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 99, "col": 10, "offset": 3630}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/bootstrap/header.html:3:\n `<html lang=\"{lang.LANG_SMALL_NAME}\" dir=\"{dir}\">\n<head>\n    <meta charset=\"{charset}\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"author\" content=\"Kleeja.net\">\n\n    <title>{title}(title? - :){config.sitename}</title>\n\n    <link rel=\"shortcut icon\" href=\"images/favicon.ico\" />\n    <link rel=\"icon\" type=\"image/gif\" href=\"images/favicon.gif\" />\n    <link rel=\"apple-touch-icon\" href=\"images/apple-touch-icon.png\" />\n    <link rel=\"apple-touch-startup-image\" href=\"images/iPhone.png\" />\n\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"{STYLE_PATH}css/bootstrap.min.css\" rel=\"stylesheet\">\n    <link href=\"{STYLE_PATH}css/stylesheet.css\" rel=\"stylesheet\">\n\n    <IF NAME=\"lang.DIR==rtl\">\n        <style type=\"text/css\">\n            body{font-family: \"Tahoma\",Arial,sans-serif;}\n            body{\n                direction: rtl; /* placing it here fixes the issue with jquery white page bug */\n            }\n        </style>\n        <link rel=\"stylesheet\" type=\"text/css\" media=\"all\" href=\"{STYLE_PATH}css/bootstrap.rtl.min.css\" />\n    </IF>\n\n\n    <script type=\"text/javascript\">\n        <!--\n        var LANG_PAST_URL_HERE = \"{lang.PAST_URL_HERE}\";\n        var LANG_MORE_F_FILES = \"{lang.MORE_F_FILES}\";\n        var STYLE_PATH  = \"{STYLE_PATH}\";\n        -->\n    </script>\n\n    <!-- Extra code -->\n    {EXTRA_CODE_META}\n</head>\n<body>\n\n    <!-- header -->\n    <nav class=\"navbar navbar-expand-lg navbar-dark bg-primary navbar-inverse\">\n        <button class=\"navbar-toggler navbar-toggler-right\" type=\"button\" data-toggle=\"collapse\" data-target=\"#TopMenuNavbar\" aria-controls=\"TopMenuNavbar\" aria-expanded=\"false\" aria-label=\"Toggle navigation\">\n            <span class=\"navbar-toggler-icon\"></span>\n        </button>\n\n        <a class=\"navbar-brand\" href=\"{config.siteurl}\">\n            <img src=\"{STYLE_PATH}images/logo.png\" width=\"30\" height=\"30\" class=\"d-inline-block align-top\" alt=\"{username}\">\n            {config.sitename}\n        </a>\n\n        <div class=\"collapse navbar-collap... (truncated 1599 more characters)", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/header.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/header.html", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 99, "col": 10, "offset": 3630}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html", "start": {"line": 56, "col": 116, "offset": 0}, "end": {"line": 56, "col": 127, "offset": 11}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html", "start": {"line": 57, "col": 116, "offset": 0}, "end": {"line": 57, "col": 126, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html:56:\n `>{lang.YES}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html", "start": {"line": 56, "col": 116, "offset": 0}, "end": {"line": 56, "col": 127, "offset": 11}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html", "start": {"line": 57, "col": 116, "offset": 0}, "end": {"line": 57, "col": 126, "offset": 10}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html:1:\n `>>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 66, "col": 66, "offset": 0}, "end": {"line": 66, "col": 67, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 66, "col": 95, "offset": 0}, "end": {"line": 66, "col": 96, "offset": 1}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 94, "col": 9, "offset": 0}, "end": {"line": 94, "col": 15, "offset": 6}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 97, "col": 9, "offset": 0}, "end": {"line": 97, "col": 14, "offset": 5}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 103, "col": 9, "offset": 0}, "end": {"line": 103, "col": 20, "offset": 11}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 131, "col": 1, "offset": 0}, "end": {"line": 131, "col": 7, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html:66:\n `\"` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 66, "col": 66, "offset": 0}, "end": {"line": 66, "col": 67, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 66, "col": 95, "offset": 0}, "end": {"line": 66, "col": 96, "offset": 1}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 94, "col": 9, "offset": 0}, "end": {"line": 94, "col": 15, "offset": 6}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 97, "col": 9, "offset": 0}, "end": {"line": 97, "col": 14, "offset": 5}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 103, "col": 9, "offset": 0}, "end": {"line": 103, "col": 20, "offset": 11}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "start": {"line": 131, "col": 1, "offset": 0}, "end": {"line": 131, "col": 7, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 4, "col": 5, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 15}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 4, "col": 114, "offset": 0}, "end": {"line": 4, "col": 116, "offset": 2}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 7, "col": 11, "offset": 6}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 7, "offset": 6}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 49, "col": 1, "offset": 0}, "end": {"line": 49, "col": 12, "offset": 11}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 139, "col": 1, "offset": 0}, "end": {"line": 140, "col": 8, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/default/footer.html:4:\n `(extras.footer?` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 4, "col": 5, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 15}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 4, "col": 114, "offset": 0}, "end": {"line": 4, "col": 116, "offset": 2}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 7, "col": 11, "offset": 6}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 7, "offset": 6}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 49, "col": 1, "offset": 0}, "end": {"line": 49, "col": 12, "offset": 11}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "start": {"line": 139, "col": 1, "offset": 0}, "end": {"line": 140, "col": 8, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/default/header.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 85, "col": 11, "offset": 3196}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/default/header.html:2:\n `<html xmlns=\"http://www.w3.org/1999/xhtml\" dir=\"{dir}\">\n<!-- Powered by klee<PERSON> || URL www.kleeja.net -->\n<head>\n    <title>{title}(title? - :){config.sitename}</title>\n    <meta http-equiv=\"Content-Type\" content=\"text/html;charset={charset}\" />\n    <meta http-equiv=\"Content-Language\" content=\"{lang.LANG_SMALL_NAME}\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"copyrights\" content=\"Powered by Klee<PERSON> || kleeja.net\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <!-- metatags.info/all_meta_tags -->\n    <link rel=\"shortcut icon\" href=\"images/favicon.ico\" />\n    <link rel=\"icon\" type=\"image/gif\" href=\"images/favicon.gif\" />\n    <link rel=\"apple-touch-icon\" href=\"images/apple-touch-icon.png\" />\n    <link rel=\"apple-touch-startup-image\" href=\"images/iPhone.png\" />\n    <link rel=\"stylesheet\" type=\"text/css\" media=\"all\" href=\"{STYLE_PATH}css/stylesheet.css\" />\n\n    <link rel=\"stylesheet\" type=\"text/css\" media=\"all\" href=\"{STYLE_PATH}css/color.css\" />\n    <IF NAME=\"lang.DIR==ltr\"><link rel=\"stylesheet\" type=\"text/css\" media=\"all\" href=\"{STYLE_PATH}css/ltr.css\" /></IF>\n\n\n\n    <script type=\"text/javascript\">\n    <!--\n    var number_of_uploads={config.filesnum};\n    var LANG_PAST_URL_HERE = \"{lang.PAST_URL_HERE}\";\n    var LANG_MORE_F_FILES = \"{lang.MORE_F_FILES}\";\n    var STYLE_PATH  = \"{STYLE_PATH}\";\n    -->\n    </script>\n\n\n    <!-- Extra code -->\n    {EXTRA_CODE_META}\n</head>\n<body<IF NAME=\"go_current == login\"> onload=\"document.login_form.lname.focus();\"</IF>>\n<!-- begin Main -->\n<div id=\"main\">\n\n    <!--begin Header-->\n    <div id=\"header\" class=\"clearfix\">\n        <h1><a title=\"{config.sitename}\" href=\"{config.siteurl}\" class=\"logo\">{config.sitename}</a></h1>\n    </div>\n    <!-- @end-Header -->\n\n    <div class=\"clr\"></div>\n\n    <!-- begin Top Navigation -->\n    <div class=\"top_nav\">\n        <ul class=\"menu\">\n        <LOOP NAME=\"top_menu\"><IF LOOP=\"show\">\n        <li><a href=\"{{url}}\" (go_current=={{name}}?class=\"current\":)>{{title}}</a></li>\n        </IF... (truncated 1165 more characters)", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/header.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/default/header.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 85, "col": 11, "offset": 3196}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html:1:\n `>>` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "start": {"line": 1, "col": 1280, "offset": 0}, "end": {"line": 1, "col": 1282, "offset": 2}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/kleeja-official_kleeja/styles/default/profile.html", "start": {"line": 39, "col": 108, "offset": 0}, "end": {"line": 39, "col": 119, "offset": 11}}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/profile.html", "start": {"line": 40, "col": 108, "offset": 0}, "end": {"line": 40, "col": 118, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/kleeja-official_kleeja/styles/default/profile.html:39:\n `>{lang.YES}` was unexpected", "path": "downloaded_repos/kleeja-official_kleeja/styles/default/profile.html", "spans": [{"file": "downloaded_repos/kleeja-official_kleeja/styles/default/profile.html", "start": {"line": 39, "col": 108, "offset": 0}, "end": {"line": 39, "col": 119, "offset": 11}}, {"file": "downloaded_repos/kleeja-official_kleeja/styles/default/profile.html", "start": {"line": 40, "col": 108, "offset": 0}, "end": {"line": 40, "col": 118, "offset": 10}}]}], "paths": {"scanned": ["downloaded_repos/kleeja-official_kleeja/.editorconfig", "downloaded_repos/kleeja-official_kleeja/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/kleeja-official_kleeja/.gitignore", "downloaded_repos/kleeja-official_kleeja/.php_cs", "downloaded_repos/kleeja-official_kleeja/CHANGELOG.md", "downloaded_repos/kleeja-official_kleeja/LICENSE.md", "downloaded_repos/kleeja-official_kleeja/README.md", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_ban.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_calls.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_check_update.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_configs.html", "downloaded_repos/kleeja-official_kleeja/admin/Masma<PERSON>/admin_err.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_extra.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_files.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_footer.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_header.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_img.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_info.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_login.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_plugins.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_repair.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_reports.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_rules.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_search.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_styles.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/bootstrap-grid.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/bootstrap-grid.min.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/bootstrap-reboot.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/bootstrap-reboot.min.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/bootstrap.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/bootstrap.min.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/bootstrap.rtl.min.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/custom.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/dataTables.bootstrap4.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/font-awesome.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/font-awesome.min.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/index.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/sb-admin-rtl.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/sb-admin.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/sb-admin.min.css", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/FontAwesome.otf", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/fontawesome-webfont.eot", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/fontawesome-webfont.svg", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/fontawesome-webfont.ttf", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/fontawesome-webfont.woff", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/fontawesome-webfont.woff2", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/index.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/ajax-loader-small.gif", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/favicon.ico", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/favicon.png", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/image-not-found.png", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/index.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/kleeja.png", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/placeholder.jpg", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/plugin.png", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/switch.gif", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/index.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/dataTables.bootstrap4.js", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/index.html", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jqBarGraph.js", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.waitforimages.js", "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/popper.js", "downloaded_repos/kleeja-official_kleeja/admin/index.php", "downloaded_repos/kleeja-official_kleeja/cache/.htaccess", "downloaded_repos/kleeja-official_kleeja/cache/index.html", "downloaded_repos/kleeja-official_kleeja/do.php", "downloaded_repos/kleeja-official_kleeja/download.php", "downloaded_repos/kleeja-official_kleeja/go.php", "downloaded_repos/kleeja-official_kleeja/htaccess.txt", "downloaded_repos/kleeja-official_kleeja/images/apple-touch-icon.png", "downloaded_repos/kleeja-official_kleeja/images/banned_user.jpg", "downloaded_repos/kleeja-official_kleeja/images/banned_user.psd", "downloaded_repos/kleeja-official_kleeja/images/favicon.gif", "downloaded_repos/kleeja-official_kleeja/images/favicon.ico", "downloaded_repos/kleeja-official_kleeja/images/favicon.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/1.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/10.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/11.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/2.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/3.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/4.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/5.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/6.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/7.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/8.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/9.png", "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "downloaded_repos/kleeja-official_kleeja/images/filetypes/3gp.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/7z.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/aep.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ai.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/aiff.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ani.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/asa.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/asax.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ascx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/asf.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ashx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/asmx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/asp.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/aspx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/au.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/avi.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/bmp.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/bz2.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/c.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/cab.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/cat.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/cc.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/cgi.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/chm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/config.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/cpp.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/cs.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/csproj.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/css.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/csv.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/cur.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/deb.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/def.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/divx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/dll.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/dng.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/doc.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/doc_rm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/docx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/dot.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/dothtml.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/dotm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/dotx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/dtd.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/em.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/eps.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/erf.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/exe.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/file.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/fla.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/flv.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/fon.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/gif.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/gmmp.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/gz.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/h.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/hlp.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/htc.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/htm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/html.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/i.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ico.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/iff.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/inc.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "downloaded_repos/kleeja-official_kleeja/images/filetypes/inf.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ini.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/iso.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/jar.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/jiff.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/jpe.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/jpeg.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/jpg.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/js.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/lib.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/log.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/m4v.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mad.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mag.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/master.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mda.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mdb.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mde.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mdn.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mht.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mid.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mov.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mp2.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mp3.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mp4.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mpa.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mpeg.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/mpg.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/msg.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/msi.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/nef.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ocx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/odh.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ogg.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ogm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pcd.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pdf.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pdfxml.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pfm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pgm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pic.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pl.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/png.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ppm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pps.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ppsx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ppt.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pptx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ps.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/psd.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/pst.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/py.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/ram.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/rar.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/rb.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/reg.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/rm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/rtf.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/shh.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/sln.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/sql.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/swf.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/tar.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/taz.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/tbz.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/tbz2.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/tgz.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/tif.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/tiff.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/txt.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/unknown.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/vb.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/vbs.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/wav.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/wm.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/wma.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/wmv.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xaml.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xls.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xlsx.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xlt.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xml.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xpi.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xps.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xsd.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xsl.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xslt.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/xst.png", "downloaded_repos/kleeja-official_kleeja/images/filetypes/zip.png", "downloaded_repos/kleeja-official_kleeja/images/iPhone.png", "downloaded_repos/kleeja-official_kleeja/images/index.html", "downloaded_repos/kleeja-official_kleeja/images/not_exists.jpg", "downloaded_repos/kleeja-official_kleeja/images/not_exists.psd", "downloaded_repos/kleeja-official_kleeja/images/site_closed.jpg", "downloaded_repos/kleeja-official_kleeja/images/spacer.gif", "downloaded_repos/kleeja-official_kleeja/images/watermark.gif", "downloaded_repos/kleeja-official_kleeja/includes/CP1256.MAP", "downloaded_repos/kleeja-official_kleeja/includes/FetchFile.php", "downloaded_repos/kleeja-official_kleeja/includes/KleejaUploader.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/a_configs.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/b_lgoutcp.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/d_img_ctrl.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/e_calls.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/f_reports.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/g_users.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/h_search.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/index.html", "downloaded_repos/kleeja-official_kleeja/includes/adm/j_plugins.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/k_ban.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/l_rules.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/m_styles.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/n_extra.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/p_check_update.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/r_repair.php", "downloaded_repos/kleeja-official_kleeja/includes/adm/start.php", "downloaded_repos/kleeja-official_kleeja/includes/arial.gdf", "downloaded_repos/kleeja-official_kleeja/includes/arial.ttf", "downloaded_repos/kleeja-official_kleeja/includes/cache.php", "downloaded_repos/kleeja-official_kleeja/includes/captcha.php", "downloaded_repos/kleeja-official_kleeja/includes/common.php", "downloaded_repos/kleeja-official_kleeja/includes/functions.php", "downloaded_repos/kleeja-official_kleeja/includes/functions_adm.php", "downloaded_repos/kleeja-official_kleeja/includes/functions_alternative.php", "downloaded_repos/kleeja-official_kleeja/includes/functions_display.php", "downloaded_repos/kleeja-official_kleeja/includes/index.html", "downloaded_repos/kleeja-official_kleeja/includes/mysqli.php", "downloaded_repos/kleeja-official_kleeja/includes/pager.php", "downloaded_repos/kleeja-official_kleeja/includes/phpass.php", "downloaded_repos/kleeja-official_kleeja/includes/plugins.php", "downloaded_repos/kleeja-official_kleeja/includes/sqlite.php", "downloaded_repos/kleeja-official_kleeja/includes/style.php", "downloaded_repos/kleeja-official_kleeja/includes/up_helpers/BMP.php", "downloaded_repos/kleeja-official_kleeja/includes/up_helpers/index.html", "downloaded_repos/kleeja-official_kleeja/includes/up_helpers/others.php", "downloaded_repos/kleeja-official_kleeja/includes/up_helpers/thumbs.php", "downloaded_repos/kleeja-official_kleeja/includes/up_helpers/watermark.php", "downloaded_repos/kleeja-official_kleeja/includes/up_methods/defaultUploader.php", "downloaded_repos/kleeja-official_kleeja/includes/up_methods/index.html", "downloaded_repos/kleeja-official_kleeja/includes/usr.php", "downloaded_repos/kleeja-official_kleeja/includes/version.php", "downloaded_repos/kleeja-official_kleeja/index.php", "downloaded_repos/kleeja-official_kleeja/install/includes/default_values.php", "downloaded_repos/kleeja-official_kleeja/install/includes/functions_install.php", "downloaded_repos/kleeja-official_kleeja/install/includes/index.html", "downloaded_repos/kleeja-official_kleeja/install/includes/install_sqls.php", "downloaded_repos/kleeja-official_kleeja/install/includes/update_schema.php", "downloaded_repos/kleeja-official_kleeja/install/index.php", "downloaded_repos/kleeja-official_kleeja/install/install.php", "downloaded_repos/kleeja-official_kleeja/install/quick.php", "downloaded_repos/kleeja-official_kleeja/install/style/check.html", "downloaded_repos/kleeja-official_kleeja/install/style/check_all.html", "downloaded_repos/kleeja-official_kleeja/install/style/choose.html", "downloaded_repos/kleeja-official_kleeja/install/style/configs.html", "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "downloaded_repos/kleeja-official_kleeja/install/style/end.html", "downloaded_repos/kleeja-official_kleeja/install/style/footer.html", "downloaded_repos/kleeja-official_kleeja/install/style/header.html", "downloaded_repos/kleeja-official_kleeja/install/style/ie6/blank.gif", "downloaded_repos/kleeja-official_kleeja/install/style/ie6/iepngfix.htc", "downloaded_repos/kleeja-official_kleeja/install/style/images/4.PNG", "downloaded_repos/kleeja-official_kleeja/install/style/images/Installer.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/bg.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/bg_box_body.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/bg_box_bottom.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/bg_box_top.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/bug.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/button.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/check_conf.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/checkbox1.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/dashed_line.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/dd_arrow.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/dev_mode.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/divline.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/end.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/error.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/good.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/help.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/hr.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "downloaded_repos/kleeja-official_kleeja/install/style/images/info2.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/inst_notes.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/install_h.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/k_info.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/kleeja.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/klj_info.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/logo.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/logo2.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/nav_.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/ok.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/ok_Plugin.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/plugin.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/s-home.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/s-user.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/spacer.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/stop.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/surprised.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/up_c.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/update.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/webdev.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/world_country.png", "downloaded_repos/kleeja-official_kleeja/install/style/images/xhover.gif", "downloaded_repos/kleeja-official_kleeja/install/style/images/zz_flag.png", "downloaded_repos/kleeja-official_kleeja/install/style/index.html", "downloaded_repos/kleeja-official_kleeja/install/style/lang.html", "downloaded_repos/kleeja-official_kleeja/install/style/license.html", "downloaded_repos/kleeja-official_kleeja/install/style/official.html", "downloaded_repos/kleeja-official_kleeja/install/style/sqls_done.html", "downloaded_repos/kleeja-official_kleeja/install/style/style.css", "downloaded_repos/kleeja-official_kleeja/install/style/update_end.html", "downloaded_repos/kleeja-official_kleeja/install/style/update_list.html", "downloaded_repos/kleeja-official_kleeja/install/style/what_is_kleeja.html", "downloaded_repos/kleeja-official_kleeja/install/update.php", "downloaded_repos/kleeja-official_kleeja/lang/ar/acp.php", "downloaded_repos/kleeja-official_kleeja/lang/ar/common.php", "downloaded_repos/kleeja-official_kleeja/lang/ar/icon.png", "downloaded_repos/kleeja-official_kleeja/lang/ar/icon_16.png", "downloaded_repos/kleeja-official_kleeja/lang/ar/index.html", "downloaded_repos/kleeja-official_kleeja/lang/ar/install.php", "downloaded_repos/kleeja-official_kleeja/lang/en/acp.php", "downloaded_repos/kleeja-official_kleeja/lang/en/common.php", "downloaded_repos/kleeja-official_kleeja/lang/en/icon.png", "downloaded_repos/kleeja-official_kleeja/lang/en/icon_16.png", "downloaded_repos/kleeja-official_kleeja/lang/en/index.html", "downloaded_repos/kleeja-official_kleeja/lang/en/install.php", "downloaded_repos/kleeja-official_kleeja/lang/fa/acp.php", "downloaded_repos/kleeja-official_kleeja/lang/fa/common.php", "downloaded_repos/kleeja-official_kleeja/lang/fa/icon.png", "downloaded_repos/kleeja-official_kleeja/lang/fa/icon_16.png", "downloaded_repos/kleeja-official_kleeja/lang/fa/index.html", "downloaded_repos/kleeja-official_kleeja/lang/fa/install.php", "downloaded_repos/kleeja-official_kleeja/lang/index.html", "downloaded_repos/kleeja-official_kleeja/plugins/index.html", "downloaded_repos/kleeja-official_kleeja/serve.php", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/call.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/bootstrap.min.css", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/bootstrap.rtl.min.css", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/stylesheet.css", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/download.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/err.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/fileuser.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/get_pass.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/guide.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/header.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/download.png", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/download2.png", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/folder.png", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/loading.gif", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/logo.png", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/rainbow.gif", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/spin.gif", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/spin_lg.gif", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/images/user.png", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/index.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/index_body.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/info.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/info.txt", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/login.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/register.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/report.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/rules.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/screenshot.png", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/stats.html", "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/up_boxes.html", "downloaded_repos/kleeja-official_kleeja/styles/default/call.html", "downloaded_repos/kleeja-official_kleeja/styles/default/css/color.css", "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "downloaded_repos/kleeja-official_kleeja/styles/default/css/ltr.css", "downloaded_repos/kleeja-official_kleeja/styles/default/css/stylesheet.css", "downloaded_repos/kleeja-official_kleeja/styles/default/download.html", "downloaded_repos/kleeja-official_kleeja/styles/default/err.html", "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "downloaded_repos/kleeja-official_kleeja/styles/default/get_pass.html", "downloaded_repos/kleeja-official_kleeja/styles/default/guide.html", "downloaded_repos/kleeja-official_kleeja/styles/default/header.html", "downloaded_repos/kleeja-official_kleeja/styles/default/images/Smile.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/admin_cp.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_animated.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_body.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_box.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_box_1.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_dots.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_footer.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_main.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_main_mobile.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_onlineall.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_onlinevisitor.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_right_menu.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_tabbernav.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_tabbertab.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_tile.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_tile_tab.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/bg_user_box.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/boxfileuser.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/call.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/clear.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/css.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/current-bg.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/download.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/err.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/error.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/file_rebort.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/filecp.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/fileuser.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/forget_pass.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/get_password.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/go_up.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/header.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/headline_bg.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/hover.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/img_container.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/imguser.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "downloaded_repos/kleeja-official_kleeja/styles/default/images/info.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/info_f.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/line.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/list_stat.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/loading.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/login.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/login.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/logo.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/logout.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/menu-bg.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/menu-right-top.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/menu_right_bottom.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/pagination.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/password.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/profile.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/profile2.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/register.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/register.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/saf3_code01.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/saf3_code_ltr.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/sidebar.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/spacer.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/spin.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/stats.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/table_file_cp.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/title_login.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/user_avater.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/username.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/warning_nofile.png", "downloaded_repos/kleeja-official_kleeja/styles/default/images/xhtml.gif", "downloaded_repos/kleeja-official_kleeja/styles/default/images/zl.png", "downloaded_repos/kleeja-official_kleeja/styles/default/index.html", "downloaded_repos/kleeja-official_kleeja/styles/default/index_body.html", "downloaded_repos/kleeja-official_kleeja/styles/default/info.html", "downloaded_repos/kleeja-official_kleeja/styles/default/info.txt", "downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js", "downloaded_repos/kleeja-official_kleeja/styles/default/login.html", "downloaded_repos/kleeja-official_kleeja/styles/default/profile.html", "downloaded_repos/kleeja-official_kleeja/styles/default/register.html", "downloaded_repos/kleeja-official_kleeja/styles/default/report.html", "downloaded_repos/kleeja-official_kleeja/styles/default/rules.html", "downloaded_repos/kleeja-official_kleeja/styles/default/screenshot.png", "downloaded_repos/kleeja-official_kleeja/styles/default/stats.html", "downloaded_repos/kleeja-official_kleeja/styles/default/up_boxes.html", "downloaded_repos/kleeja-official_kleeja/styles/index.html", "downloaded_repos/kleeja-official_kleeja/ucp.php", "downloaded_repos/kleeja-official_kleeja/uploads/.htaccess", "downloaded_repos/kleeja-official_kleeja/uploads/php.ini", "downloaded_repos/kleeja-official_kleeja/uploads/thumbs/.htaccess", "downloaded_repos/kleeja-official_kleeja/uploads/thumbs/index.html", "downloaded_repos/kleeja-official_kleeja/uploads/thumbs/php.ini", "downloaded_repos/kleeja-official_kleeja/web.config"], "skipped": [{"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_footer.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_header.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_users.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/popper.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/tether.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/images/pluginIcon.psd", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/check.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/check_all.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/data.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/header.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/images/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/lang.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/license.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/update_end.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/install/style/update_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/fileuser.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/footer.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/header.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/js/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/bootstrap/profile.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/fileuser.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/footer.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/header.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/kleeja-official_kleeja/styles/default/profile.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.614905834197998, "profiling_times": {"config_time": 5.701270818710327, "core_time": 23.712459325790405, "ignores_time": 0.0039179325103759766, "total_time": 29.41892981529236}, "parsing_time": {"total_time": 6.8956568241119385, "per_file_time": {"mean": 0.048221376392391185, "std_dev": 0.05712709700046592}, "very_slow_stats": {"time_ratio": 0.63727779784867, "count_ratio": 0.02097902097902098}, "very_slow_files": [{"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/popper.js", "ftime": 0.384645938873291}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "ftime": 1.6968379020690918}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "ftime": 2.312965154647827}]}, "scanning_time": {"total_time": 59.771408796310425, "per_file_time": {"mean": 0.04915411907591312, "std_dev": 0.40009185685192183}, "very_slow_stats": {"time_ratio": 0.68432089835503, "count_ratio": 0.005756578947368421}, "very_slow_files": [{"fpath": "downloaded_repos/kleeja-official_kleeja/ucp.php", "ftime": 2.024916887283325}, {"fpath": "downloaded_repos/kleeja-official_kleeja/includes/functions.php", "ftime": 2.1579740047454834}, {"fpath": "downloaded_repos/kleeja-official_kleeja/includes/adm/g_users.php", "ftime": 2.531285047531128}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/popper.js", "ftime": 2.673762083053589}, {"fpath": "downloaded_repos/kleeja-official_kleeja/styles/default/jquery.js", "ftime": 5.080215930938721}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "ftime": 6.823009014129639}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "ftime": 19.611661195755005}]}, "matching_time": {"total_time": 24.796955108642578, "per_file_and_rule_time": {"mean": 0.05437928751895303, "std_dev": 0.03595165767765165}, "very_slow_stats": {"time_ratio": 0.7371929945404672, "count_ratio": 0.10307017543859649}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/kleeja-official_kleeja/ucp.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.44503307342529297}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.4712381362915039}, {"fpath": "downloaded_repos/kleeja-official_kleeja/includes/adm/g_users.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.47385501861572266}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.5365021228790283}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.6119611263275146}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/popper.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.7139501571655273}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.9643850326538086}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.098538875579834}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 2.155390977859497}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 2.5847020149230957}]}, "tainting_time": {"total_time": 12.918250322341919, "per_def_and_rule_time": {"mean": 0.0035257233412505245, "std_dev": 0.0005507496184428103}, "very_slow_stats": {"time_ratio": 0.5149854837495454, "count_ratio": 0.01037117903930131}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.23984909057617188}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.2582249641418457}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2771949768066406}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.2777550220489502}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.340893030166626}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.42557311058044434}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.4662652015686035}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.48604702949523926}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.5587549209594727}, {"fpath": "downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.5720388889312744}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}