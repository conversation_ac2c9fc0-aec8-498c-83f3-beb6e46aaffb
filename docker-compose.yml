version: '3.8'

services:
  vulnerability-scanner:
    build: .
    container_name: unified-vuln-scanner
    environment:
      - GITHUB_TOKEN=${GITHUB_TOKEN}
    volumes:
      - ./output:/app/output
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker-in-Docker Semgrep
    command: ["--max-repos", "50", "--pages", "3", "--concurrent", "2"]
    networks:
      - scanner-network

  # Optional: Semgrep service for distributed scanning
  semgrep-worker:
    image: returntocorp/semgrep:latest
    container_name: semgrep-worker
    volumes:
      - ./downloaded_repos:/src:ro
      - ./semgrep_results:/output
    networks:
      - scanner-network
    profiles:
      - distributed

networks:
  scanner-network:
    driver: bridge

volumes:
  output:
  semgrep_results:
  downloaded_repos:
