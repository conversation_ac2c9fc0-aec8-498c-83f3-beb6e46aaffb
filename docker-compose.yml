version: '3.8'

services:
  vulnerability-scanner:
    build: .
    container_name: unified-vuln-scanner-container
    environment:
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - SEMGREP_IN_DOCKER=1
    volumes:
      - ./docker_output:/app/output
    command: ["--max-repos", "50", "--pages", "3", "--concurrent", "1"]
    networks:
      - scanner-network
    restart: "no"

  # Quick test scanner with fewer repos
  vulnerability-scanner-test:
    build: .
    container_name: unified-vuln-scanner-test
    environment:
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - SEMGREP_IN_DOCKER=1
    volumes:
      - ./docker_output:/app/output
    command: ["--max-repos", "20", "--pages", "2", "--concurrent", "1"]
    networks:
      - scanner-network
    restart: "no"
    profiles:
      - test

networks:
  scanner-network:
    driver: bridge

volumes:
  docker_output:
