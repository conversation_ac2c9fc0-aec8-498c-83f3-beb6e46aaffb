{"version": "1.130.0", "results": [{"check_id": "bash.lang.security.ifs-tampering.ifs-tampering", "path": "downloaded_repos/pantheon-systems_wp-saml-auth/bin/validate-fixture-version.sh", "start": {"line": 3, "col": 1, "offset": 30}, "end": {"line": 3, "col": 12, "offset": 41}, "extra": {"message": "The special variable IFS affects how splitting takes place when expanding unquoted variables. Don't set it globally. Prefer a dedicated utility such as 'cut' or 'awk' if you need to split input data. If you must use 'read', set IFS locally using e.g. 'IFS=\",\" read -a my_array'.", "metadata": {"cwe": ["CWE-20: Improper Input Validation"], "category": "security", "technology": ["bash"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/bash.lang.security.ifs-tampering.ifs-tampering", "shortlink": "https://sg.run/Q9pq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/phpcbf.yml", "start": {"line": 40, "col": 15, "offset": 1286}, "end": {"line": 40, "col": 18, "offset": 1289}}]], "message": "Syntax error at line downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/phpcbf.yml:40:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/phpcbf.yml", "spans": [{"file": "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/phpcbf.yml", "start": {"line": 40, "col": 15, "offset": 1286}, "end": {"line": 40, "col": 18, "offset": 1289}}]}], "paths": {"scanned": ["downloaded_repos/pantheon-systems_wp-saml-auth/.circleci/config.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.editorconfig", "downloaded_repos/pantheon-systems_wp-saml-auth/.gitattributes", "downloaded_repos/pantheon-systems_wp-saml-auth/.github/dependabot.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/build-tag-release.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/composer-diff.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/lint-test.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/phpcbf.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/validate-plugin-tested-up-to-version.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/wordpress-plugin-deploy.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/.gitignore", "downloaded_repos/pantheon-systems_wp-saml-auth/.wordpress-org/banner-1544x500.png", "downloaded_repos/pantheon-systems_wp-saml-auth/.wordpress-org/banner-772x250.png", "downloaded_repos/pantheon-systems_wp-saml-auth/.wordpress-org/icon-128x128.png", "downloaded_repos/pantheon-systems_wp-saml-auth/.wordpress-org/icon-256x256.png", "downloaded_repos/pantheon-systems_wp-saml-auth/CODEOWNERS", "downloaded_repos/pantheon-systems_wp-saml-auth/CONTRIBUTING.md", "downloaded_repos/pantheon-systems_wp-saml-auth/README.md", "downloaded_repos/pantheon-systems_wp-saml-auth/behat.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/1-adminnotice.feature", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/authsources.php.additions", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/behat-prepare-simplesaml1.18.0.sh", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/config.php.additions", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/functions.simplesaml1.18.0.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/pantheon.php74.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/saml20-idp-hosted.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/1.18/saml20-sp-remote.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/behat-cleanup.sh", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/behat-prepare.sh", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/behat-test.sh", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/1-adminnotice.feature", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/authsources.base.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/config-prepare.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/config.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/functions.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/saml20-idp-hosted.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/saml20-sp-remote.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/shib13-idp-hosted.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/shib13-sp-remote.php", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/fixtures/style.css", "downloaded_repos/pantheon-systems_wp-saml-auth/bin/validate-fixture-version.sh", "downloaded_repos/pantheon-systems_wp-saml-auth/catalog-info.yml", "downloaded_repos/pantheon-systems_wp-saml-auth/composer.json", "downloaded_repos/pantheon-systems_wp-saml-auth/composer.lock", "downloaded_repos/pantheon-systems_wp-saml-auth/inc/class-wp-saml-auth-cli.php", "downloaded_repos/pantheon-systems_wp-saml-auth/inc/class-wp-saml-auth-options.php", "downloaded_repos/pantheon-systems_wp-saml-auth/inc/class-wp-saml-auth-settings.php", "downloaded_repos/pantheon-systems_wp-saml-auth/inc/class-wp-saml-auth.php", "downloaded_repos/pantheon-systems_wp-saml-auth/languages/wp-saml-auth-fr_FR.mo", "downloaded_repos/pantheon-systems_wp-saml-auth/languages/wp-saml-auth-fr_FR.po", "downloaded_repos/pantheon-systems_wp-saml-auth/languages/wp-saml-auth.pot", "downloaded_repos/pantheon-systems_wp-saml-auth/phpcs.xml.dist", "downloaded_repos/pantheon-systems_wp-saml-auth/phpstan.neon.dist", "downloaded_repos/pantheon-systems_wp-saml-auth/phpunit.xml.dist", "downloaded_repos/pantheon-systems_wp-saml-auth/readme.txt", "downloaded_repos/pantheon-systems_wp-saml-auth/wp-saml-auth.php"], "skipped": [{"path": "downloaded_repos/pantheon-systems_wp-saml-auth/.github/workflows/phpcbf.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/behat/0-login.feature", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/behat/bootstrap/AdminLogIn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/phpunit/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/phpunit/class-simplesaml-auth-simple.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/phpunit/class-wp-saml-auth-test-cli.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/phpunit/test-authentication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/phpunit/test-scaffold-config.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pantheon-systems_wp-saml-auth/tests/phpunit/test-version-check.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6504030227661133, "profiling_times": {"config_time": 5.9377601146698, "core_time": 2.6126177310943604, "ignores_time": 0.0017926692962646484, "total_time": 8.553609848022461}, "parsing_time": {"total_time": 0.5251784324645996, "per_file_time": {"mean": 0.015914497953472714, "std_dev": 0.00035639080028965724}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.195050001144409, "per_file_time": {"mean": 0.015138275869961445, "std_dev": 0.0016027026271731417}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.5492277145385742, "per_file_and_rule_time": {"mean": 0.0014843992284826334, "std_dev": 3.8021668084661385e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.11221909523010254, "per_def_and_rule_time": {"mean": 0.000748127301534017, "std_dev": 2.686642212890749e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}