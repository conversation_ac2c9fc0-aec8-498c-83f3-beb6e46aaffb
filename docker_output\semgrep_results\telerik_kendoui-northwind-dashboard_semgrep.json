{"version": "1.130.0", "results": [{"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 9, "col": 9, "offset": 311}, "end": {"line": 9, "col": 92, "offset": 394}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 10, "col": 9, "offset": 430}, "end": {"line": 10, "col": 136, "offset": 557}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 14, "col": 9, "offset": 990}, "end": {"line": 14, "col": 57, "offset": 1038}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 25, "col": 17, "offset": 1661}, "end": {"line": 25, "col": 99, "offset": 1743}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 28, "col": 17, "offset": 1803}, "end": {"line": 28, "col": 98, "offset": 1884}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 31, "col": 17, "offset": 1944}, "end": {"line": 31, "col": 82, "offset": 2009}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 34, "col": 17, "offset": 2069}, "end": {"line": 34, "col": 90, "offset": 2142}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 37, "col": 17, "offset": 2202}, "end": {"line": 37, "col": 92, "offset": 2277}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 40, "col": 17, "offset": 2337}, "end": {"line": 40, "col": 90, "offset": 2410}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 43, "col": 17, "offset": 2470}, "end": {"line": 43, "col": 88, "offset": 2541}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 48, "col": 17, "offset": 2653}, "end": {"line": 48, "col": 97, "offset": 2733}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 51, "col": 17, "offset": 2793}, "end": {"line": 51, "col": 98, "offset": 2874}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 54, "col": 17, "offset": 2934}, "end": {"line": 54, "col": 96, "offset": 3013}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 57, "col": 17, "offset": 3073}, "end": {"line": 57, "col": 80, "offset": 3136}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 60, "col": 17, "offset": 3196}, "end": {"line": 60, "col": 94, "offset": 3273}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 65, "col": 17, "offset": 3385}, "end": {"line": 65, "col": 90, "offset": 3458}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 68, "col": 17, "offset": 3518}, "end": {"line": 68, "col": 92, "offset": 3593}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/index.html", "start": {"line": 5, "col": 9, "offset": 119}, "end": {"line": 5, "col": 109, "offset": 219}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/index.html", "start": {"line": 6, "col": 9, "offset": 228}, "end": {"line": 6, "col": 114, "offset": 333}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/index.html", "start": {"line": 8, "col": 9, "offset": 414}, "end": {"line": 8, "col": 76, "offset": 481}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/index.html", "start": {"line": 12, "col": 9, "offset": 749}, "end": {"line": 12, "col": 93, "offset": 833}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/index.html", "start": {"line": 13, "col": 9, "offset": 842}, "end": {"line": 13, "col": 115, "offset": 948}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/index.html", "start": {"line": 14, "col": 9, "offset": 957}, "end": {"line": 14, "col": 101, "offset": 1049}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 1278, "col": 3, "offset": 34387}, "end": {"line": 1281, "col": 45, "offset": 34603}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 1651, "col": 17, "offset": 45296}, "end": {"line": 1652, "col": 32, "offset": 45378}, "extra": {"message": "RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 1729, "col": 17, "offset": 47340}, "end": {"line": 1729, "col": 39, "offset": 47362}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 2180, "col": 12, "offset": 59368}, "end": {"line": 2180, "col": 34, "offset": 59390}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 2195, "col": 13, "offset": 59802}, "end": {"line": 2195, "col": 35, "offset": 59824}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 2203, "col": 13, "offset": 59993}, "end": {"line": 2203, "col": 35, "offset": 60015}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 2208, "col": 8, "offset": 60192}, "end": {"line": 2208, "col": 34, "offset": 60218}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 2730, "col": 10, "offset": 75091}, "end": {"line": 2730, "col": 32, "offset": 75113}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 3058, "col": 10, "offset": 83112}, "end": {"line": 3058, "col": 30, "offset": 83132}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 4770, "col": 5, "offset": 126795}, "end": {"line": 4770, "col": 74, "offset": 126864}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "start": {"line": 6171, "col": 8, "offset": 166114}, "end": {"line": 6171, "col": 31, "offset": 166137}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 1278, "col": 3, "offset": 34799}, "end": {"line": 1281, "col": 45, "offset": 35015}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 1651, "col": 17, "offset": 45708}, "end": {"line": 1652, "col": 32, "offset": 45790}, "extra": {"message": "RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 1729, "col": 17, "offset": 47752}, "end": {"line": 1729, "col": 39, "offset": 47774}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 2180, "col": 12, "offset": 59780}, "end": {"line": 2180, "col": 34, "offset": 59802}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 2195, "col": 13, "offset": 60214}, "end": {"line": 2195, "col": 35, "offset": 60236}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 2203, "col": 13, "offset": 60405}, "end": {"line": 2203, "col": 35, "offset": 60427}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 2208, "col": 8, "offset": 60604}, "end": {"line": 2208, "col": 34, "offset": 60630}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 2730, "col": 10, "offset": 75503}, "end": {"line": 2730, "col": 32, "offset": 75525}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 3058, "col": 10, "offset": 83524}, "end": {"line": 3058, "col": 30, "offset": 83544}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 4770, "col": 5, "offset": 127207}, "end": {"line": 4770, "col": 74, "offset": 127276}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "start": {"line": 6171, "col": 8, "offset": 166526}, "end": {"line": 6171, "col": 31, "offset": 166549}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate-vsdoc.js", "start": {"line": 249, "col": 27, "offset": 8167}, "end": {"line": 249, "col": 61, "offset": 8201}, "extra": {"message": "RegExp() called with a `i` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate-vsdoc.js", "start": {"line": 611, "col": 59, "offset": 20075}, "end": {"line": 612, "col": 52, "offset": 20183}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate-vsdoc.js", "start": {"line": 1190, "col": 49, "offset": 40660}, "end": {"line": 1190, "col": 85, "offset": 40696}, "extra": {"message": "RegExp() called with a `param` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "start": {"line": 272, "col": 28, "offset": 7681}, "end": {"line": 272, "col": 64, "offset": 7717}, "extra": {"message": "RegExp() called with a `i` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "start": {"line": 819, "col": 20, "offset": 24019}, "end": {"line": 819, "col": 122, "offset": 24121}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "start": {"line": 1002, "col": 38, "offset": 30019}, "end": {"line": 1002, "col": 97, "offset": 30078}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "start": {"line": 1002, "col": 38, "offset": 30019}, "end": {"line": 1002, "col": 97, "offset": 30078}, "extra": {"message": "RegExp() called with a `element` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "start": {"line": 1002, "col": 38, "offset": 30019}, "end": {"line": 1002, "col": 97, "offset": 30078}, "extra": {"message": "RegExp() called with a `message` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "start": {"line": 1515, "col": 10, "offset": 46638}, "end": {"line": 1515, "col": 44, "offset": 46672}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "start": {"line": 1515, "col": 10, "offset": 46638}, "end": {"line": 1515, "col": 44, "offset": 46672}, "extra": {"message": "RegExp() called with a `element` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "csharp.dotnet.security.net-webconfig-debug.net-webconfig-debug", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/web.config", "start": {"line": 32, "col": 5, "offset": 1447}, "end": {"line": 34, "col": 109, "offset": 1625}, "extra": {"message": "ASP.NET applications built with `debug` set to true in production may leak debug information to attackers. Debug mode also affects performance and reliability. Set `debug` to `false` or remove it from `<compilation ... />`", "metadata": {"likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "category": "security", "cwe": ["CWE-11: ASP.NET Misconfiguration: Creating Debug Binary"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://web.archive.org/web/20190919105353/https://blogs.msdn.microsoft.com/prashant_upadhyay/2011/07/14/why-debugfalse-in-asp-net-applications-in-production-environment/", "https://msdn.microsoft.com/en-us/library/e8z01xdh.aspx"], "subcategory": ["audit"], "technology": [".net"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Active Debug Code"], "source": "https://semgrep.dev/r/csharp.dotnet.security.net-webconfig-debug.net-webconfig-debug", "shortlink": "https://sg.run/yPWx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 6, "col": 5, "offset": 108}, "end": {"line": 6, "col": 107, "offset": 210}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 7, "col": 5, "offset": 215}, "end": {"line": 7, "col": 106, "offset": 316}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 8, "col": 5, "offset": 321}, "end": {"line": 8, "col": 72, "offset": 388}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 9, "col": 5, "offset": 393}, "end": {"line": 9, "col": 89, "offset": 477}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 10, "col": 5, "offset": 482}, "end": {"line": 10, "col": 95, "offset": 572}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 11, "col": 5, "offset": 577}, "end": {"line": 11, "col": 111, "offset": 683}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 68, "col": 29, "offset": 4068}, "end": {"line": 68, "col": 77, "offset": 4116}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "start": {"line": 6, "col": 5, "offset": 108}, "end": {"line": 6, "col": 105, "offset": 208}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "start": {"line": 7, "col": 5, "offset": 213}, "end": {"line": 7, "col": 106, "offset": 314}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "start": {"line": 8, "col": 5, "offset": 319}, "end": {"line": 8, "col": 72, "offset": 386}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "start": {"line": 9, "col": 5, "offset": 391}, "end": {"line": 9, "col": 89, "offset": 475}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "start": {"line": 10, "col": 5, "offset": 480}, "end": {"line": 10, "col": 95, "offset": 570}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "start": {"line": 11, "col": 5, "offset": 575}, "end": {"line": 11, "col": 111, "offset": 681}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "start": {"line": 12, "col": 5, "offset": 686}, "end": {"line": 12, "col": 97, "offset": 778}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 7, "col": 5, "offset": 137}, "end": {"line": 7, "col": 107, "offset": 239}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 8, "col": 5, "offset": 244}, "end": {"line": 8, "col": 106, "offset": 345}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 9, "col": 5, "offset": 351}, "end": {"line": 9, "col": 72, "offset": 418}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 10, "col": 5, "offset": 423}, "end": {"line": 10, "col": 89, "offset": 507}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 11, "col": 5, "offset": 512}, "end": {"line": 11, "col": 95, "offset": 602}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 12, "col": 5, "offset": 607}, "end": {"line": 12, "col": 111, "offset": 713}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/team-efficiency.html", "start": {"line": 6, "col": 5, "offset": 108}, "end": {"line": 6, "col": 107, "offset": 210}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/team-efficiency.html", "start": {"line": 7, "col": 5, "offset": 215}, "end": {"line": 7, "col": 106, "offset": 316}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/team-efficiency.html", "start": {"line": 8, "col": 5, "offset": 321}, "end": {"line": 8, "col": 72, "offset": 388}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/team-efficiency.html", "start": {"line": 9, "col": 5, "offset": 393}, "end": {"line": 9, "col": 89, "offset": 477}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/team-efficiency.html", "start": {"line": 10, "col": 5, "offset": 482}, "end": {"line": 10, "col": 95, "offset": 572}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/team-efficiency.html", "start": {"line": 11, "col": 5, "offset": 577}, "end": {"line": 11, "col": 111, "offset": 683}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 74, "col": 13, "offset": 0}, "end": {"line": 74, "col": 19, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html:74:\n `</div>` was unexpected", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "spans": [{"file": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "start": {"line": 74, "col": 13, "offset": 0}, "end": {"line": 74, "col": 19, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/_references.js", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 4, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/_references.js:1:\n `��/` was unexpected", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/_references.js", "spans": [{"file": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/_references.js", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 4, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 74, "col": 67, "offset": 0}, "end": {"line": 74, "col": 88, "offset": 21}}]], "message": "Syntax error at line downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html:74:\n `& HTML5 UI Components` was unexpected", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "spans": [{"file": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "start": {"line": 74, "col": 67, "offset": 0}, "end": {"line": 74, "col": 88, "offset": 21}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 8, "col": 106, "offset": 0}, "end": {"line": 8, "col": 107, "offset": 1}}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 62, "col": 72, "offset": 0}, "end": {"line": 62, "col": 80, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html:8:\n `>` was unexpected", "path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "spans": [{"file": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 8, "col": 106, "offset": 0}, "end": {"line": 8, "col": 107, "offset": 1}}, {"file": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "start": {"line": 62, "col": 72, "offset": 0}, "end": {"line": 62, "col": 80, "offset": 8}}]}], "paths": {"scanned": ["downloaded_repos/telerik_kendoui-northwind-dashboard/.github/workflows/check-repos-updates.sh", "downloaded_repos/telerik_kendoui-northwind-dashboard/.github/workflows/check-repos-updates.yml", "downloaded_repos/telerik_kendoui-northwind-dashboard/.gitignore", "downloaded_repos/telerik_kendoui-northwind-dashboard/CODEOWNERS", "downloaded_repos/telerik_kendoui-northwind-dashboard/README.md", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/1-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/1.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/2-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/2.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/3-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/3.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/4-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/4.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/5-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/5.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/6-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/6.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/7-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/7.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/8-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/8.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/9-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Employees/9.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Fonts/icomoon.eot", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Fonts/icomoon.svg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Fonts/icomoon.ttf", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Fonts/icomoon.woff", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/1.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/10.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/11.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/12.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/13.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/14.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/15.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/16.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/17.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/18.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/19.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/2.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/20.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/21.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/22.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/23.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/24.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/25.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/26.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/27.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/28.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/29.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/3.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/30.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/31.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/32.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/33.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/34.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/35.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/36.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/37.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/38.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/39.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/4.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/40.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/41.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/42.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/43.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/44.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/45.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/46.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/47.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/48.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/49.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/5.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/50.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/51.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/52.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/53.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/54.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/55.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/56.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/57.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/58.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/59.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/6.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/60.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/61.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/62.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/63.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/64.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/65.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/66.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/67.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/68.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/69.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/7.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/70.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/71.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/72.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/73.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/74.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/75.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/76.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/77.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/78.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/79.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/8.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/80.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Products/9.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/Site.css", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/countries-sales.geo.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/country-customers.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/customers.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/dataviz/map/countries-users.geo.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/employee-and-team-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/employee-average-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/employee-quarter-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/employee-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/employees-list.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/favicon.ico", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/order-details.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/order-information.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/orders.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/product-details.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/product-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/top-selling-products.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/README.md", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/app.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/bower.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/products-orders/products-orders.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/products-orders/products-orders.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/regional-sales/regional-sales.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/regional-sales/regional-sales.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/team-efficiency/team-efficiency.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/team-efficiency/team-efficiency.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/index.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/web.config", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/App_Data/northwind.db", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/App_Start/DatabaseConfig.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Configuration/SQLiteConfiguration.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/1-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/1.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/2-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/2.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/3-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/3.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/4-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/4.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/5-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/5.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/6-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/6.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/7-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/7.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/8-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/8.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/9-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Employees/9.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Fonts/icomoon.eot", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Fonts/icomoon.svg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Fonts/icomoon.ttf", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Fonts/icomoon.woff", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/1.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/10.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/11.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/12.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/13.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/14.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/15.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/16.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/17.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/18.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/19.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/2.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/20.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/21.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/22.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/23.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/24.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/25.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/26.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/27.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/28.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/29.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/3.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/30.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/31.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/32.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/33.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/34.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/35.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/36.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/37.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/38.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/39.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/4.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/40.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/41.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/42.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/43.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/44.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/45.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/46.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/47.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/48.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/49.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/5.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/50.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/51.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/52.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/53.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/54.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/55.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/56.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/57.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/58.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/59.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/6.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/60.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/61.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/62.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/63.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/64.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/65.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/66.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/67.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/68.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/69.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/7.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/70.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/71.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/72.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/73.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/74.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/75.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/76.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/77.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/78.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/79.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/8.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/80.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Products/9.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/Site.css", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/countries-sales.geo.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/dataviz/map/countries-users.geo.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/favicon.ico", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Controllers/CustomersController.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Controllers/HomeController.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Controllers/OrderDetailsController.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Controllers/OrdersController.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Controllers/ProductsController.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Controllers/RegionalSalesController.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Controllers/TeamController.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Global.asax", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Global.asax.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Images/accent.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Images/bullet.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Images/heroAccent.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Alphabetical_list_of_product.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Category.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CategoryViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Category_Sales_for_1997.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CountryCustomers_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CountryMarketShare_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CountryOrders_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CountryRevenue_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CountryTopProducts_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Current_Product_List.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CustOrderHist_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CustOrdersDetail_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CustOrdersOrders_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Customer.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CustomerCustomerDemo.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CustomerDemographic.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/CustomerViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Employee.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/EmployeeTerritory.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/EmployeeViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Employee_Sales_by_Country_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Invoice.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/MonthlySalesByEmployee_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Northwind.Context.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Northwind.Context.tt", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Northwind.Designer.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Northwind.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Northwind.edmx", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Northwind.edmx.diagram", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Northwind.tt", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Order.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/OrderDetail.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/OrderDetailViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/OrderViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Order_Detail.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Order_Details_Extended.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Order_Subtotal.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Orders_Qry.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Product.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/ProductViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Product_Sales_for_1997.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/ProductsSalesByMonth_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Products_Above_Average_Price.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Products_by_Category.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Region.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/SaleViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/SalesAmounts_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/SalesByCategory_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Sales_Totals_by_Amount.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Sales_by_Category.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Sales_by_Year_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Shipper.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/ShipperViewModel.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Summary_of_Sales_by_Quarter.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Summary_of_Sales_by_Year.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Supplier.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Ten_Most_Expensive_Products_Result.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Models/Territory.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Properties/AssemblyInfo.cs", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/_references.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.intellisense.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.min.map", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.min.map", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate-vsdoc.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Home/About.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Home/EditorTemplates/ShipCountry.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Home/ProductsAndOrders.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Home/RegionalSalesStatus.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Home/TeamEfficiency.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Currency.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/CustomGridForeignKey.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Date.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/DateTime.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Email.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/EmailAddress.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Float.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/GridForeignKey.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Integer.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Number.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Password.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/String.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Time.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/EditorTemplates/Url.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/Shared/_Layout.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/_ViewStart.cshtml", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Views/web.config", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Web.Debug.config", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Web.Release.config", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/kendoui-northwind-dashboard.csproj", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/kendoui-northwind-dashboard.csproj.user", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/packages.config", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/web.config", "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard.sln", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/CountryCustomers.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/CountryCustomersTotal.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/CountryMarketShare.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/CountryOrders.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/CountryRevenue.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/CountryTopProducts.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/MonthlySalesByEmployee.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/ProductsSalesByMonth.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/db/SalesAmounts.sql", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/1-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/1.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/2-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/2.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/3-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/3.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/4-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/4.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/5-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/5.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/6-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/6.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/7-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/7.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/8-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/8.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/9-t.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Employees/9.png", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Fonts/icomoon.eot", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Fonts/icomoon.svg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Fonts/icomoon.ttf", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Fonts/icomoon.woff", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/1.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/10.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/11.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/12.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/13.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/14.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/15.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/16.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/17.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/18.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/19.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/2.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/20.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/21.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/22.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/23.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/24.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/25.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/26.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/27.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/28.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/29.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/3.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/30.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/31.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/32.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/33.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/34.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/35.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/36.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/37.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/38.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/39.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/4.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/40.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/41.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/42.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/43.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/44.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/45.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/46.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/47.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/48.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/49.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/5.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/50.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/51.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/52.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/53.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/54.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/55.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/56.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/57.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/58.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/59.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/6.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/60.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/61.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/62.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/63.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/64.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/65.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/66.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/67.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/68.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/69.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/7.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/70.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/71.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/72.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/73.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/74.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/75.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/76.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/77.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/78.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/79.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/8.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/80.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Products/9.jpg", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/Site.css", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/countries-sales.geo.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/country-customers.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/customers.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/dataviz/map/countries-users.geo.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/employee-and-team-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/employee-average-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/employee-quarter-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/employee-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/employees-list.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/favicon.ico", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/order-details.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/order-information.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/orders.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/product-details.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/product-sales.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/top-selling-products.json", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/index.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/team-efficiency.html", "downloaded_repos/telerik_kendoui-northwind-dashboard/html/web.config"], "skipped": [{"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/dataviz/map/js/chroma.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/components/about/about.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/App_Data/NORTHWND.MDF", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/App_Data/NORTHWND_log.ldf", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/dataviz/map/js/chroma.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/_references.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/chroma.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/dataviz/map/js/chroma.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/about.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/products-orders.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6068851947784424, "profiling_times": {"config_time": 6.424161434173584, "core_time": 29.179227352142334, "ignores_time": 0.002646923065185547, "total_time": 35.60688924789429}, "parsing_time": {"total_time": 17.780240297317505, "per_file_time": {"mean": 0.33547623202485866, "std_dev": 0.2476718430518513}, "very_slow_stats": {"time_ratio": 0.8936235598247488, "count_ratio": 0.37735849056603776}, "very_slow_files": [{"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/dataviz/map/countries-users.geo.json", "ftime": 0.6476330757141113}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/dataviz/map/countries-users.geo.json", "ftime": 0.6620559692382812}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/order-information.json", "ftime": 0.6914839744567871}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/countries-sales.geo.json", "ftime": 0.7385199069976807}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.intellisense.js", "ftime": 0.9067139625549316}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/order-information.json", "ftime": 0.9148550033569336}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/countries-sales.geo.json", "ftime": 0.96875}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/countries-sales.geo.json", "ftime": 1.0171611309051514}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "ftime": 2.1598989963531494}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "ftime": 2.4983608722686768}]}, "scanning_time": {"total_time": 78.9375216960907, "per_file_time": {"mean": 0.07215495584651808, "std_dev": 0.981591356605453}, "very_slow_stats": {"time_ratio": 0.716767395738878, "count_ratio": 0.007312614259597806}, "very_slow_files": [{"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.intellisense.js", "ftime": 1.5147240161895752}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/html/Content/countries-sales.geo.json", "ftime": 1.5687370300292969}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Content/countries-sales.geo.json", "ftime": 1.5928020477294922}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/angular/Content/order-information.json", "ftime": 1.6845028400421143}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate-vsdoc.js", "ftime": 1.9456770420074463}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery.validate.js", "ftime": 2.935637950897217}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "ftime": 19.810395002365112}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "ftime": 25.527365922927856}]}, "matching_time": {"total_time": 27.182594537734985, "per_file_and_rule_time": {"mean": 0.05961095293362936, "std_dev": 0.028596633260128564}, "very_slow_stats": {"time_ratio": 0.8333457165257028, "count_ratio": 0.15789473684210525}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.4726450443267822}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5186231136322021}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.5296781063079834}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.7797589302062988}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.8594450950622559}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 0.9066469669342041}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.089637041091919}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.1803810596466064}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.6200311183929443}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.7358570098876953}]}, "tainting_time": {"total_time": 21.628618478775024, "per_def_and_rule_time": {"mean": 0.0049357869645766834, "std_dev": 0.0021256595890569714}, "very_slow_stats": {"time_ratio": 0.7616759068126602, "count_ratio": 0.01437699680511182}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.42642998695373535}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.483335018157959}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.5370829105377197}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.5750010013580322}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.6405870914459229}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.6790680885314941}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.7120938301086426}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.7384979724884033}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.slim.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.2883028984069824}, {"fpath": "downloaded_repos/telerik_kendoui-northwind-dashboard/aspnet-mvc/kendoui-northwind-dashboard/Scripts/jquery-3.7.1.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.776832103729248}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1091430400}, "engine_requested": "OSS", "skipped_rules": []}