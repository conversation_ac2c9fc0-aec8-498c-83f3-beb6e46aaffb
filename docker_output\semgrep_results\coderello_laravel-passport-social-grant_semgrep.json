{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/coderello_laravel-passport-social-grant/.github/workflows/tests.yml", "start": {"line": 39, "col": 57, "offset": 818}, "end": {"line": 39, "col": 73, "offset": 834}}]], "message": "Syntax error at line downloaded_repos/coderello_laravel-passport-social-grant/.github/workflows/tests.yml:39:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/coderello_laravel-passport-social-grant/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/coderello_laravel-passport-social-grant/.github/workflows/tests.yml", "start": {"line": 39, "col": 57, "offset": 818}, "end": {"line": 39, "col": 73, "offset": 834}}]}], "paths": {"scanned": ["downloaded_repos/coderello_laravel-passport-social-grant/.editorconfig", "downloaded_repos/coderello_laravel-passport-social-grant/.gitattributes", "downloaded_repos/coderello_laravel-passport-social-grant/.github/CONTRIBUTING.md", "downloaded_repos/coderello_laravel-passport-social-grant/.github/workflows/tests.yml", "downloaded_repos/coderello_laravel-passport-social-grant/.gitignore", "downloaded_repos/coderello_laravel-passport-social-grant/.styleci.yml", "downloaded_repos/coderello_laravel-passport-social-grant/CHANGELOG.md", "downloaded_repos/coderello_laravel-passport-social-grant/LICENSE.md", "downloaded_repos/coderello_laravel-passport-social-grant/README.md", "downloaded_repos/coderello_laravel-passport-social-grant/coderello.yml", "downloaded_repos/coderello_laravel-passport-social-grant/composer.json", "downloaded_repos/coderello_laravel-passport-social-grant/phpunit.xml.dist", "downloaded_repos/coderello_laravel-passport-social-grant/src/Grants/SocialGrant.php", "downloaded_repos/coderello_laravel-passport-social-grant/src/Providers/SocialGrantServiceProvider.php", "downloaded_repos/coderello_laravel-passport-social-grant/src/Resolvers/SocialUserResolverInterface.php"], "skipped": [{"path": "downloaded_repos/coderello_laravel-passport-social-grant/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/AbstractTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/SocialGrantTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/Stubs/AccessTokenEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/Stubs/ClientEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/Stubs/RefreshTokenEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/Stubs/ResponseType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/Stubs/ScopeEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/Stubs/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/coderello_laravel-passport-social-grant/tests/Stubs/private.key", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6546599864959717, "profiling_times": {"config_time": 6.229406356811523, "core_time": 2.2104532718658447, "ignores_time": 0.0020780563354492188, "total_time": 8.442963600158691}, "parsing_time": {"total_time": 0.06601786613464355, "per_file_time": {"mean": 0.009431123733520506, "std_dev": 8.844689205034227e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.15202093124389648, "per_file_time": {"mean": 0.0041086738174026085, "std_dev": 4.720906847796404e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.03168797492980957, "per_file_and_rule_time": {"mean": 0.0003407309132237588, "std_dev": 9.030105879287799e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}