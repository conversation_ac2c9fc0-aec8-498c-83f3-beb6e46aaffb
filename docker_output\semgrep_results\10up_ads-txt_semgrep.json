{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml", "start": {"line": 62, "col": 48, "offset": 1579}, "end": {"line": 62, "col": 51, "offset": 1582}}, {"path": "downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml", "start": {"line": 62, "col": 87, "offset": 1579}, "end": {"line": 62, "col": 90, "offset": 1582}}]], "message": "Syntax error at line downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml:62:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml", "spans": [{"file": "downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml", "start": {"line": 62, "col": 48, "offset": 1579}, "end": {"line": 62, "col": 51, "offset": 1582}}, {"file": "downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml", "start": {"line": 62, "col": 87, "offset": 1579}, "end": {"line": 62, "col": 90, "offset": 1582}}]}], "paths": {"scanned": ["downloaded_repos/10up_ads-txt/.gitattributes", "downloaded_repos/10up_ads-txt/.github/CODEOWNERS", "downloaded_repos/10up_ads-txt/.github/workflows/build-release-zip.yml", "downloaded_repos/10up_ads-txt/.github/workflows/close-stale-issues.yml", "downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml", "downloaded_repos/10up_ads-txt/.github/workflows/dependency-review.yml", "downloaded_repos/10up_ads-txt/.github/workflows/lint-php.yml", "downloaded_repos/10up_ads-txt/.github/workflows/php8-compatibility.yml", "downloaded_repos/10up_ads-txt/.github/workflows/phpunit.yml", "downloaded_repos/10up_ads-txt/.github/workflows/push-asset-readme-update.yml", "downloaded_repos/10up_ads-txt/.github/workflows/push-deploy.yml", "downloaded_repos/10up_ads-txt/.github/workflows/repo-automator.yml", "downloaded_repos/10up_ads-txt/.github/workflows/wordpress-version-checker.yml", "downloaded_repos/10up_ads-txt/.gitignore", "downloaded_repos/10up_ads-txt/.nvmrc", "downloaded_repos/10up_ads-txt/.phpcompat.xml.dist", "downloaded_repos/10up_ads-txt/.phpcs.xml.dist", "downloaded_repos/10up_ads-txt/.wordpress-org/banner-1544x500.png", "downloaded_repos/10up_ads-txt/.wordpress-org/banner-772x250.png", "downloaded_repos/10up_ads-txt/.wordpress-org/blueprints/blueprint.json", "downloaded_repos/10up_ads-txt/.wordpress-org/icon-128x128.png", "downloaded_repos/10up_ads-txt/.wordpress-org/icon-256x256.png", "downloaded_repos/10up_ads-txt/.wordpress-org/icon.svg", "downloaded_repos/10up_ads-txt/.wordpress-org/screenshot-1.png", "downloaded_repos/10up_ads-txt/.wordpress-org/screenshot-2.png", "downloaded_repos/10up_ads-txt/.wordpress-org/screenshot-3.png", "downloaded_repos/10up_ads-txt/.wordpress-version-checker.json", "downloaded_repos/10up_ads-txt/.wp-env.json", "downloaded_repos/10up_ads-txt/CHANGELOG.md", "downloaded_repos/10up_ads-txt/CODE_OF_CONDUCT.md", "downloaded_repos/10up_ads-txt/CONTRIBUTING.md", "downloaded_repos/10up_ads-txt/CREDITS.md", "downloaded_repos/10up_ads-txt/LICENSE.md", "downloaded_repos/10up_ads-txt/README.md", "downloaded_repos/10up_ads-txt/ads-txt.php", "downloaded_repos/10up_ads-txt/composer.json", "downloaded_repos/10up_ads-txt/composer.lock", "downloaded_repos/10up_ads-txt/css/admin.css", "downloaded_repos/10up_ads-txt/inc/admin.php", "downloaded_repos/10up_ads-txt/inc/helpers.php", "downloaded_repos/10up_ads-txt/inc/post-type.php", "downloaded_repos/10up_ads-txt/inc/save.php", "downloaded_repos/10up_ads-txt/js/admin.js", "downloaded_repos/10up_ads-txt/package-lock.json", "downloaded_repos/10up_ads-txt/package.json", "downloaded_repos/10up_ads-txt/phpunit.xml.dist", "downloaded_repos/10up_ads-txt/readme.txt"], "skipped": [{"path": "downloaded_repos/10up_ads-txt/.github/workflows/cypress.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/10up_ads-txt/tests/bin/initialize.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/bin/set-wp-config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/bin/wp-cli.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/config.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/fixtures/example.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/integration/admin.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/integration/ads-txt.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/integration/app-ads-txt.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/plugins/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/support/commands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/cypress/support/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/unit/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/unit/test-admin.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/unit/test-post-type.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_ads-txt/tests/unit/test-save.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6791160106658936, "profiling_times": {"config_time": 7.1154890060424805, "core_time": 2.5953307151794434, "ignores_time": 0.0017616748809814453, "total_time": 9.713690280914307}, "parsing_time": {"total_time": 0.4712331295013428, "per_file_time": {"mean": 0.020488396934840992, "std_dev": 0.0006256688441118832}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.7294979095458984, "per_file_time": {"mean": 0.014782033414922215, "std_dev": 0.0013666915532033282}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.5124781131744385, "per_file_and_rule_time": {"mean": 0.001396398128540704, "std_dev": 2.157719947806256e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.07185244560241699, "per_def_and_rule_time": {"mean": 0.0004887921469552177, "std_dev": 6.049397939796417e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}