{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/Sanjeet990_Astroluma/Dockerfile", "start": {"line": 42, "col": 1, "offset": 929}, "end": {"line": 42, "col": 29, "offset": 957}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"npm\", \"run\", \"server\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/Sanjeet990_Astroluma/docker-compose.yml", "start": {"line": 22, "col": 3, "offset": 604}, "end": {"line": 22, "col": 10, "offset": 611}, "extra": {"message": "Service 'mongodb' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/Sanjeet990_Astroluma/docker-compose.yml", "start": {"line": 22, "col": 3, "offset": 604}, "end": {"line": 22, "col": 10, "offset": 611}, "extra": {"message": "Service 'mongodb' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/accounts.js", "start": {"line": 33, "col": 65, "offset": 907}, "end": {"line": 33, "col": 97, "offset": 939}, "extra": {"message": "RegExp() called with a `req` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 20, "col": 35, "offset": 645}, "end": {"line": 20, "col": 46, "offset": 656}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 21, "col": 36, "offset": 710}, "end": {"line": 21, "col": 47, "offset": 721}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 76, "col": 32, "offset": 2419}, "end": {"line": 80, "col": 11, "offset": 2707}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 77, "col": 31, "offset": 2486}, "end": {"line": 77, "col": 40, "offset": 2495}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 107, "col": 70, "offset": 3594}, "end": {"line": 107, "col": 84, "offset": 3608}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 157, "col": 13, "offset": 4996}, "end": {"line": 157, "col": 64, "offset": 5047}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 199, "col": 76, "offset": 6153}, "end": {"line": 199, "col": 95, "offset": 6172}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 199, "col": 76, "offset": 6153}, "end": {"line": 199, "col": 95, "offset": 6172}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 200, "col": 68, "offset": 6244}, "end": {"line": 200, "col": 84, "offset": 6260}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 200, "col": 68, "offset": 6244}, "end": {"line": 200, "col": 84, "offset": 6260}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 274, "col": 48, "offset": 8308}, "end": {"line": 274, "col": 53, "offset": 8313}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 275, "col": 47, "offset": 8379}, "end": {"line": 275, "col": 52, "offset": 8384}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 306, "col": 76, "offset": 9454}, "end": {"line": 306, "col": 95, "offset": 9473}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 306, "col": 76, "offset": 9454}, "end": {"line": 306, "col": 95, "offset": 9473}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 307, "col": 68, "offset": 9545}, "end": {"line": 307, "col": 84, "offset": 9561}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 307, "col": 68, "offset": 9545}, "end": {"line": 307, "col": 84, "offset": 9561}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 308, "col": 64, "offset": 9627}, "end": {"line": 308, "col": 80, "offset": 9643}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 308, "col": 64, "offset": 9627}, "end": {"line": 308, "col": 80, "offset": 9643}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 410, "col": 46, "offset": 12795}, "end": {"line": 410, "col": 73, "offset": 12822}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 410, "col": 46, "offset": 12795}, "end": {"line": 410, "col": 73, "offset": 12822}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 443, "col": 46, "offset": 13763}, "end": {"line": 443, "col": 73, "offset": 13790}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 443, "col": 46, "offset": 13763}, "end": {"line": 443, "col": 73, "offset": 13790}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 449, "col": 40, "offset": 13932}, "end": {"line": 449, "col": 47, "offset": 13939}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 449, "col": 40, "offset": 13932}, "end": {"line": 449, "col": 47, "offset": 13939}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 457, "col": 39, "offset": 14237}, "end": {"line": 457, "col": 46, "offset": 14244}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 457, "col": 39, "offset": 14237}, "end": {"line": 457, "col": 46, "offset": 14244}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 457, "col": 58, "offset": 14256}, "end": {"line": 457, "col": 65, "offset": 14263}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 457, "col": 58, "offset": 14256}, "end": {"line": 457, "col": 65, "offset": 14263}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 464, "col": 22, "offset": 14424}, "end": {"line": 464, "col": 33, "offset": 14435}, "extra": {"message": "The application processes user-input, this is passed to res.sendFile which can allow an attacker to arbitrarily read files on the system through path traversal. It is recommended to perform input validation in addition to canonicalizing the path. This allows you to validate the path against the intended directory it should be accessing.", "metadata": {"references": ["https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html"], "technology": ["express"], "category": "security", "cwe": ["CWE-73: External Control of File Name or Path"], "owasp": ["A04:2021 - Insecure Design"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "shortlink": "https://sg.run/7DJk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.bypass-tls-verification.bypass-tls-verification", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 521, "col": 37, "offset": 15807}, "end": {"line": 523, "col": 10, "offset": 15856}, "extra": {"message": "Checks for setting the environment variable NODE_TLS_REJECT_UNAUTHORIZED to 0, which disables TLS verification. This should only be used for debugging purposes. Setting the option rejectUnauthorized to false bypasses verification against the list of trusted CAs, which also leads to insecure transport. These options lead to vulnerability to MTM attacks, and should not be used.", "metadata": {"likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": "A03:2017 - Sensitive Data Exposure", "references": ["https://nodejs.org/api/https.html#https_https_request_options_callback", "https://stackoverflow.com/questions/20433287/node-js-request-cert-has-expired#answer-29397100"], "subcategory": ["vuln"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.bypass-tls-verification.bypass-tls-verification", "shortlink": "https://sg.run/9oxr"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 543, "col": 49, "offset": 16357}, "end": {"line": 543, "col": 84, "offset": 16392}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 543, "col": 49, "offset": 16357}, "end": {"line": 543, "col": 84, "offset": 16392}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 592, "col": 60, "offset": 18074}, "end": {"line": 592, "col": 101, "offset": 18115}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 592, "col": 60, "offset": 18074}, "end": {"line": 592, "col": 101, "offset": 18115}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.require-request.require-request", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 602, "col": 36, "offset": 18503}, "end": {"line": 602, "col": 48, "offset": 18515}, "extra": {"message": "If an attacker controls the x in require(x) then they can cause code to load that was not intended to run on the server.", "metadata": {"interfile": true, "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "source-rule-url": "https://nodesecroadmap.fyi/chapter-1/threat-UIR.html", "category": "security", "technology": ["express"], "references": ["https://github.com/google/node-sec-roadmap/blob/master/chapter-2/dynamism.md#dynamism-when-you-need-it"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/javascript.express.security.require-request.require-request", "shortlink": "https://sg.run/jRbl"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-vm-injection.express-vm-injection", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 619, "col": 28, "offset": 18986}, "end": {"line": 619, "col": 53, "offset": 19011}, "extra": {"message": "Make sure that unverified user data can not reach `vm`.", "metadata": {"owasp": ["A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Injection_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.express.security.express-vm-injection.express-vm-injection", "shortlink": "https://sg.run/jkqJ"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 665, "col": 49, "offset": 20538}, "end": {"line": 665, "col": 104, "offset": 20593}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 665, "col": 49, "offset": 20538}, "end": {"line": 665, "col": 104, "offset": 20593}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 686, "col": 73, "offset": 21460}, "end": {"line": 686, "col": 142, "offset": 21529}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 686, "col": 73, "offset": 21460}, "end": {"line": 686, "col": 142, "offset": 21529}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 690, "col": 74, "offset": 21677}, "end": {"line": 690, "col": 145, "offset": 21748}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 690, "col": 74, "offset": 21677}, "end": {"line": 690, "col": 145, "offset": 21748}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 741, "col": 60, "offset": 23364}, "end": {"line": 741, "col": 121, "offset": 23425}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 741, "col": 60, "offset": 23364}, "end": {"line": 741, "col": 121, "offset": 23425}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.require-request.require-request", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 753, "col": 36, "offset": 23918}, "end": {"line": 753, "col": 48, "offset": 23930}, "extra": {"message": "If an attacker controls the x in require(x) then they can cause code to load that was not intended to run on the server.", "metadata": {"interfile": true, "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "source-rule-url": "https://nodesecroadmap.fyi/chapter-1/threat-UIR.html", "category": "security", "technology": ["express"], "references": ["https://github.com/google/node-sec-roadmap/blob/master/chapter-2/dynamism.md#dynamism-when-you-need-it"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/javascript.express.security.require-request.require-request", "shortlink": "https://sg.run/jRbl"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-vm-injection.express-vm-injection", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "start": {"line": 770, "col": 28, "offset": 24419}, "end": {"line": 770, "col": 53, "offset": 24444}, "extra": {"message": "Make sure that unverified user data can not reach `vm`.", "metadata": {"owasp": ["A03:2021 - Injection"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Injection_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.express.security.express-vm-injection.express-vm-injection", "shortlink": "https://sg.run/jkqJ"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/auth.js", "start": {"line": 18, "col": 57, "offset": 473}, "end": {"line": 18, "col": 89, "offset": 505}, "extra": {"message": "RegExp() called with a `req` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/listing.js", "start": {"line": 399, "col": 49, "offset": 11611}, "end": {"line": 399, "col": 68, "offset": 11630}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/listing.js", "start": {"line": 399, "col": 49, "offset": 11611}, "end": {"line": 399, "col": 68, "offset": 11630}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/networkdevice.js", "start": {"line": 37, "col": 27, "offset": 1179}, "end": {"line": 37, "col": 61, "offset": 1213}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "path": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/networkdevice.js", "start": {"line": 189, "col": 13, "offset": 5374}, "end": {"line": 198, "col": 15, "offset": 5645}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "references": ["https://en.wikipedia.org/wiki/Mass_assignment_vulnerability", "https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html"], "category": "security", "technology": ["express"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.express.security.express-data-exfiltration.express-data-exfiltration", "shortlink": "https://sg.run/pkpL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/install-app-dependencies.js", "start": {"line": 54, "col": 50, "offset": 1648}, "end": {"line": 54, "col": 54, "offset": 1652}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/install-app-dependencies.js", "start": {"line": 55, "col": 62, "offset": 1716}, "end": {"line": 55, "col": 66, "offset": 1720}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/install-app-dependencies.js", "start": {"line": 56, "col": 47, "offset": 1769}, "end": {"line": 56, "col": 62, "offset": 1784}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Sanjeet990_Astroluma/server/middlewares/auth.js", "start": {"line": 27, "col": 53, "offset": 776}, "end": {"line": 27, "col": 93, "offset": 816}, "extra": {"message": "RegExp() called with a `req` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/middlewares/uploadToLocalMiddleware.js", "start": {"line": 14, "col": 47, "offset": 376}, "end": {"line": 14, "col": 64, "offset": 393}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/Sanjeet990_Astroluma/server/server.js", "start": {"line": 10, "col": 7, "offset": 233}, "end": {"line": 10, "col": 22, "offset": 248}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "path": "downloaded_repos/Sanjeet990_Astroluma/server/server.js", "start": {"line": 14, "col": 16, "offset": 330}, "end": {"line": 14, "col": 20, "offset": 334}, "extra": {"message": "Checks for any usage of http servers instead of https servers. Encourages the usage of https protocol instead of http, which does not have TLS and is therefore unencrypted. Using http can lead to man-in-the-middle attacks in which the attacker is able to read sensitive information.", "metadata": {"likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": ["A02:2021 - Cryptographic Failures", "A03:2017 - Sensitive Data Exposure"], "references": ["https://nodejs.org/api/http.html#http_class_http_agent", "https://groups.google.com/g/rubyonrails-security/c/NCCsca7TEtY"], "subcategory": ["audit"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "shortlink": "https://sg.run/x1zL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/server.js", "start": {"line": 92, "col": 52, "offset": 2293}, "end": {"line": 92, "col": 59, "offset": 2300}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Sanjeet990_Astroluma/server/server.js", "start": {"line": 92, "col": 52, "offset": 2293}, "end": {"line": 92, "col": 59, "offset": 2300}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/Sanjeet990_Astroluma/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/Sanjeet990_Astroluma/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/Sanjeet990_Astroluma/.gitignore", "downloaded_repos/Sanjeet990_Astroluma/Dockerfile", "downloaded_repos/Sanjeet990_Astroluma/LICENSE", "downloaded_repos/Sanjeet990_Astroluma/README.md", "downloaded_repos/Sanjeet990_Astroluma/SECURITY.md", "downloaded_repos/Sanjeet990_Astroluma/client/.depcheckrc", "downloaded_repos/Sanjeet990_Astroluma/client/.eslintrc.cjs", "downloaded_repos/Sanjeet990_Astroluma/client/.gitignore", "downloaded_repos/Sanjeet990_Astroluma/client/README.md", "downloaded_repos/Sanjeet990_Astroluma/client/index.html", "downloaded_repos/Sanjeet990_Astroluma/client/package.json", "downloaded_repos/Sanjeet990_Astroluma/client/postcss.config.cjs", "downloaded_repos/Sanjeet990_Astroluma/client/public/.htaccess", "downloaded_repos/Sanjeet990_Astroluma/client/public/apps.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/astroluma.svg", "downloaded_repos/Sanjeet990_Astroluma/client/public/authenticator.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/avatar.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/cctv.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/code.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/computer.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/default.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/folder.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/footage.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/home.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/index.html", "downloaded_repos/Sanjeet990_Astroluma/client/public/link.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/live.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/logo192.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/logo512.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/network-error.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/nopreview.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/not-found.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/otp.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/robots.txt", "downloaded_repos/Sanjeet990_Astroluma/client/public/snippet.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/todo.png", "downloaded_repos/Sanjeet990_Astroluma/client/public/vite.svg", "downloaded_repos/Sanjeet990_Astroluma/client/src/App.css", "downloaded_repos/Sanjeet990_Astroluma/client/src/App.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/assets/react.svg", "downloaded_repos/Sanjeet990_Astroluma/client/src/atoms.js", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Accounts/AccountList.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Accounts/EditUser.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Accounts/UserProfile.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Auth/Login.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/AuthenticatorListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/AuthenticatorSidebar.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/EditAuthenticator.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/OtpComponent.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/SingleTotpItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Breadcrumb/Breadcrumb.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/BuyMeACoffee.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Camera/RTSPPlayer.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Camera/SingleFeed.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/ClickOutside.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Header/DropdownUser.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Header/WeatherWidget.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Header/WelcomeHeader.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Header/index.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/IconPacks/AddIconPack.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/IconPacks/SingleIconPackItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/IconPacks/index.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Icons/CustomIconPack.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Icons/MyIconsSection.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Integration/AppConfigurator.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Integration/InstallApps.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Integration/InstalledApps.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Integration/SingleHostedApp.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Integration/SingleInstalledApp.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Layout/ContentLoader.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Layout/Layout.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Layout/Loader.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Layout/PrivateRoute.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/CategoryListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/EditFolder.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/EditLink.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/EditStream.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/Listings.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/SingleListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/SingleListingInFront.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/StreamListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/ClientError.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/DatabaseError.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/ImageView.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/NetworkError.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/NoListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/NotFound.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/SelectList.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/ServerError.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Misc/WelcomeUser.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/AdditionalIntegrationConfigurationModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/BrandingRemovalModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/ConfirmPacketSendModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/DeleteCodeItemModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/DeletePageModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/DeleteSnippetItemModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/DeleteTodoModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/DeleteUserModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/ImageSelectorModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/LocationSelectorModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/ManagePublishPageModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/NewSnippetCodeItemModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/NewSnippetItemModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/NewTodoItemModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/RemoveInstalledIntegration.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/UpdateAvatarModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Modals/UpdatePasswordModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Networkdevice/DeviceStatus.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Networkdevice/EditDevice.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Networkdevice/SingleDeviceItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Networkdevice/SingleDeviceItemFront.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Networkdevice/WakeFrontListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Networkdevice/WakeListings.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceBack.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceButton.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceCheckbox.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceClose.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceDrag.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceForm.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceInput.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceLink.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceLoader.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceModal.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NicePreferenceHeader.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceTab.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceTip.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/NiceViews/NiceUploader.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Page/EditPage.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Page/Jodit.css", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Page/PageList.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Page/SinglePageItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Page/ViewPage.css", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Page/ViewPage.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Settings/GeneralSettings.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Settings/Settings.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Settings/SingleSettingsItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Settings/ThemeSettings.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Settings/WeatherSettings.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Sidebar/SidebarButtonItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Sidebar/SidebarLinkGroup.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Sidebar/SidebarLinkItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Sidebar/index.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Snippet/EditSnippetList.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Snippet/SingleCodeItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Snippet/SingleSnippetHeaderItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Snippet/SingleSnippetItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Snippet/SnippetListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Theme/SingleThemeItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Theme/ThemeList.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Todo/EditTodo.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Todo/SingleTodoItem.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Todo/TodoListing.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/events.js", "downloaded_repos/Sanjeet990_Astroluma/client/src/hooks/useCurrentRoute.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/hooks/useDetectProtocol.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/hooks/useDynamicFilter.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/hooks/useSecurityCheck.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/index.css", "downloaded_repos/Sanjeet990_Astroluma/client/src/main.jsx", "downloaded_repos/Sanjeet990_Astroluma/client/src/utils/ApiService.js", "downloaded_repos/Sanjeet990_Astroluma/client/src/utils/Constants.js", "downloaded_repos/Sanjeet990_Astroluma/client/src/utils/Helper.js", "downloaded_repos/Sanjeet990_Astroluma/client/src/utils/LanguageList.js", "downloaded_repos/Sanjeet990_Astroluma/client/src/utils/SystemThemes.js", "downloaded_repos/Sanjeet990_Astroluma/client/src/utils/ToastUtils.js", "downloaded_repos/Sanjeet990_Astroluma/client/tailwind.config.js", "downloaded_repos/Sanjeet990_Astroluma/client/vite.config.js", "downloaded_repos/Sanjeet990_Astroluma/docker-compose.yml", "downloaded_repos/Sanjeet990_Astroluma/output.css", "downloaded_repos/Sanjeet990_Astroluma/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.github/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.github/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.github/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.github/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.github/templates/m-response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.github/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.heimdall/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.heimdall/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.heimdall/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.heimdall/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.heimdall/templates/m-response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.heimdall/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.html/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.html/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.html/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.html/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.html/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.portainer/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.portainer/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.portainer/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.portainer/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.portainer/templates/m-response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.portainer/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxmox/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxmox/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxmox/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxmox/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxmox/templates/m-response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxmox/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxyman/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxyman/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxyman/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxyman/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxyman/templates/m-response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.proxyman/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.truenas.scale/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.truenas.scale/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.truenas.scale/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.truenas.scale/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.truenas.scale/templates/m-response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.truenas.scale/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.youtube/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.youtube/manifest.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.youtube/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.youtube/public/logo.png", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.youtube/templates/m-response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/apps/com.youtube/templates/response.tpl", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/accounts.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/auth.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/home.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/iconpack.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/image.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/listing.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/manage.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/networkdevice.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/page.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/snippet.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/todo.js", "downloaded_repos/Sanjeet990_Astroluma/server/controllers/totp.js", "downloaded_repos/Sanjeet990_Astroluma/server/install-app-dependencies.js", "downloaded_repos/Sanjeet990_Astroluma/server/middlewares/auth.js", "downloaded_repos/Sanjeet990_Astroluma/server/middlewares/uploadToLocalMiddleware.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrate-mongo-config.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrations/20250108055650-setup_astroluma.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrations/20250108055715-encrypt_totp_secrets.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrations/20250108064334-migrate_old_icons.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrations/20250111194505-migrate_apps_integration.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrations/20250111203153-add_app_integrations.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrations/20250118163157-add_avatar_in_existing_users.js", "downloaded_repos/Sanjeet990_Astroluma/server/migrations/20250119183234-add_site_logo_debranding_info.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/App.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/Authenticator.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/Icon.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/IconPack.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/Listing.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/NetworkDevice.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/Page.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/Snippet.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/Todo.js", "downloaded_repos/Sanjeet990_Astroluma/server/models/User.js", "downloaded_repos/Sanjeet990_Astroluma/server/package.json", "downloaded_repos/Sanjeet990_Astroluma/server/public/images/integration.png", "downloaded_repos/Sanjeet990_Astroluma/server/routes/accounts.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/app.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/auth.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/home.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/iconpack.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/image.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/listing.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/manage.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/networkdevice.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/page.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/snippet.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/todo.js", "downloaded_repos/Sanjeet990_Astroluma/server/routes/totp.js", "downloaded_repos/Sanjeet990_Astroluma/server/server.js", "downloaded_repos/Sanjeet990_Astroluma/server/start-server.js", "downloaded_repos/Sanjeet990_Astroluma/server/utils/allowedModules.js", "downloaded_repos/Sanjeet990_Astroluma/server/utils/apiutils.js", "downloaded_repos/Sanjeet990_Astroluma/server/websocket.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.8047950267791748, "profiling_times": {"config_time": 6.548473358154297, "core_time": 7.63247013092041, "ignores_time": 0.001775979995727539, "total_time": 14.183403015136719}, "parsing_time": {"total_time": 7.303226947784424, "per_file_time": {"mean": 0.03245878643459744, "std_dev": 0.002485584360218414}, "very_slow_stats": {"time_ratio": 0.06633027720007675, "count_ratio": 0.0044444444444444444}, "very_slow_files": [{"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/tailwind.config.js", "ftime": 0.48442506790161133}]}, "scanning_time": {"total_time": 57.28464341163635, "per_file_time": {"mean": 0.07429914839382154, "std_dev": 0.07776664749789977}, "very_slow_stats": {"time_ratio": 0.28031666346393974, "count_ratio": 0.009079118028534372}, "very_slow_files": [{"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/tailwind.config.js", "ftime": 1.5225369930267334}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/EditAuthenticator.jsx", "ftime": 1.6915550231933594}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/EditLink.jsx", "ftime": 1.7376911640167236}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/SingleListingInFront.jsx", "ftime": 2.133342981338501}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/Listings.jsx", "ftime": 2.4319469928741455}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/listing.js", "ftime": 2.571903944015503}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "ftime": 3.968863010406494}]}, "matching_time": {"total_time": 27.970683813095093, "per_file_and_rule_time": {"mean": 0.02577943208580194, "std_dev": 0.0029401695641726854}, "very_slow_stats": {"time_ratio": 0.5010741061241553, "count_ratio": 0.06912442396313365}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/networkdevice.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.2585899829864502}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Todo/TodoListing.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.***************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/EditAuthenticator.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.****************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Accounts/AccountList.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/atoms.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Accounts/EditUser.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.*****************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/SingleListingInFront.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.****************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/EditLink.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Listing/Listings.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.759329080581665}]}, "tainting_time": {"total_time": 9.359772682189941, "per_def_and_rule_time": {"mean": 0.01941861552321564, "std_dev": 0.0006169033168830644}, "very_slow_stats": {"time_ratio": 0.3542359757308446, "count_ratio": 0.08091286307053942}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "fline": 1, "rule_id": "javascript.lang.security.audit.dangerous-spawn-shell.dangerous-spawn-shell", "time": 0.10317397117614746}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/listing.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.10771298408508301}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/listing.js", "fline": 1, "rule_id": "javascript.lang.security.detect-child-process.detect-child-process", "time": 0.1106867790222168}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.11430597305297852}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "fline": 1, "rule_id": "javascript.express.security.express-vm-injection.express-vm-injection", "time": 0.11786484718322754}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/client/src/components/Authenticator/EditAuthenticator.jsx", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.11847615242004395}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.12259888648986816}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/listing.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.12277388572692871}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "fline": 1, "rule_id": "javascript.lang.security.detect-child-process.detect-child-process", "time": 0.19717001914978027}, {"fpath": "downloaded_repos/Sanjeet990_Astroluma/server/controllers/app.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.20504999160766602}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}