{"name": "fireshare", "version": "1.2.25", "private": true, "dependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@fontsource/inter": "^4.5.11", "@mui/icons-material": "^5.8.0", "@mui/material": "^5.8.1", "axios": "^0.27.2", "lodash": "^4.17.21", "react": "^18.1.0", "react-copy-to-clipboard": "^5.1.0", "react-device-detect": "^2.2.2", "react-dom": "^18.1.0", "react-helmet": "^6.1.0", "react-is-visible": "^1.2.0", "react-player": "^2.10.1", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-select": "^5.3.2", "typeface-roboto": "^1.1.13"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}