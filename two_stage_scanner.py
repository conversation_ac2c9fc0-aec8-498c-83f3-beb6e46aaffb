#!/usr/bin/env python3

"""
Two-Stage Vulnerability Scanner
===============================

Stage 1: Fast data-only analysis to identify promising repositories
Stage 2: Targeted Semgrep scanning on selected repositories
"""

import os
import sys
import subprocess
import time
import argparse

def run_stage_1(max_repos=50, top_repos=20, pages=3):
    """Stage 1: Data-only analysis to generate CSV of promising repositories"""
    print("🎯 STAGE 1: DATA-ONLY ANALYSIS")
    print("=" * 50)
    print("Fast repository analysis without Semgrep scanning")
    print(f"Analyzing {max_repos} repositories, keeping top {top_repos}")
    print()
    
    try:
        cmd = [
            sys.executable, "unified_vulnerability_scanner.py",
            "--skip-semgrep",
            "--max-repos", str(max_repos),
            "--top-repos", str(top_repos),
            "--pages", str(pages),
            "--concurrent", "1"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        start_time = time.time()
        
        result = subprocess.run(cmd, check=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ Stage 1 completed in {duration:.1f} seconds")
        
        # Check if CSV was generated
        csv_file = "unified_vulnerability_results.csv"
        if os.path.exists(csv_file):
            with open(csv_file, 'r', encoding='utf-8', errors='replace') as f:
                lines = f.readlines()
                repo_count = len(lines) - 1  # Subtract header
                print(f"📄 Generated CSV with {repo_count} repositories")
                
                # Show top 5 repositories
                if repo_count > 0:
                    print(f"\n🏆 TOP 5 REPOSITORIES FOR SEMGREP SCANNING:")
                    for i, line in enumerate(lines[1:6], 1):
                        try:
                            parts = line.strip().split(',')
                            if len(parts) >= 2:
                                rank = parts[0]
                                repo_name = parts[1]
                                final_score = parts[15] if len(parts) > 15 else "N/A"
                                print(f"  {rank}. {repo_name} - Score: {final_score}")
                        except:
                            pass
                
                return csv_file
        else:
            print("❌ No CSV file generated")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Stage 1 failed: {e}")
        return None
    except KeyboardInterrupt:
        print(f"⚠️ Stage 1 interrupted")
        return None

def run_stage_2(csv_file):
    """Stage 2: Targeted Semgrep scanning on repositories from CSV"""
    print(f"\n🔍 STAGE 2: TARGETED SEMGREP SCANNING")
    print("=" * 50)
    print(f"Running Semgrep analysis on repositories from: {csv_file}")
    print()
    
    try:
        cmd = [
            sys.executable, "unified_vulnerability_scanner.py",
            "--semgrep-only",
            "--input-csv", csv_file,
            "--concurrent", "1"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        start_time = time.time()
        
        result = subprocess.run(cmd, check=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ Stage 2 completed in {duration:.1f} seconds")
        
        # Check final results
        final_csv = "unified_vulnerability_results.csv"
        if os.path.exists(final_csv):
            with open(final_csv, 'r', encoding='utf-8', errors='replace') as f:
                lines = f.readlines()
                repo_count = len(lines) - 1
                print(f"📄 Final results with Semgrep data: {repo_count} repositories")
                
                # Show repositories with vulnerabilities
                if repo_count > 0:
                    print(f"\n🚨 REPOSITORIES WITH SEMGREP FINDINGS:")
                    vuln_count = 0
                    for i, line in enumerate(lines[1:], 1):
                        try:
                            parts = line.strip().split(',')
                            if len(parts) >= 13:
                                repo_name = parts[1]
                                semgrep_vulns = parts[12]
                                if semgrep_vulns and semgrep_vulns.isdigit() and int(semgrep_vulns) > 0:
                                    vuln_count += 1
                                    final_score = parts[15] if len(parts) > 15 else "N/A"
                                    print(f"  {i}. {repo_name} - {semgrep_vulns} vulnerabilities (Score: {final_score})")
                        except:
                            pass
                    
                    if vuln_count == 0:
                        print("  No repositories with Semgrep findings")
                    else:
                        print(f"\n📊 Total repositories with vulnerabilities: {vuln_count}")
                
                return final_csv
        else:
            print("❌ No final CSV file generated")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Stage 2 failed: {e}")
        return None
    except KeyboardInterrupt:
        print(f"⚠️ Stage 2 interrupted")
        return None

def main():
    parser = argparse.ArgumentParser(description="Two-Stage Vulnerability Scanner")
    parser.add_argument("--max-repos", type=int, default=50, help="Maximum repositories to analyze in stage 1")
    parser.add_argument("--top-repos", type=int, default=20, help="Top repositories to keep for stage 2")
    parser.add_argument("--pages", type=int, default=3, help="Search pages per query in stage 1")
    parser.add_argument("--stage1-only", action="store_true", help="Only run stage 1 (data analysis)")
    parser.add_argument("--stage2-only", type=str, help="Only run stage 2 with specified CSV file")
    
    args = parser.parse_args()
    
    print("🎯 TWO-STAGE VULNERABILITY SCANNER")
    print("=" * 60)
    print("Efficient vulnerability discovery through staged analysis")
    print()
    
    # Check GitHub token
    if not os.getenv("GITHUB_TOKEN"):
        print("❌ Please set GITHUB_TOKEN environment variable")
        print("   export GITHUB_TOKEN='your_token_here'")
        sys.exit(1)
    
    print("✅ GitHub token is set")
    print()
    
    total_start_time = time.time()
    
    if args.stage2_only:
        # Run only stage 2 with provided CSV
        if not os.path.exists(args.stage2_only):
            print(f"❌ CSV file not found: {args.stage2_only}")
            sys.exit(1)
        
        final_csv = run_stage_2(args.stage2_only)
        
    elif args.stage1_only:
        # Run only stage 1
        csv_file = run_stage_1(args.max_repos, args.top_repos, args.pages)
        if csv_file:
            print(f"\n✅ Stage 1 complete. Use this CSV for stage 2:")
            print(f"   python two_stage_scanner.py --stage2-only {csv_file}")
        
    else:
        # Run both stages
        print("🚀 Running complete two-stage analysis")
        print()
        
        # Stage 1: Data-only analysis
        csv_file = run_stage_1(args.max_repos, args.top_repos, args.pages)
        
        if not csv_file:
            print("❌ Stage 1 failed, cannot proceed to stage 2")
            sys.exit(1)
        
        # Rename stage 1 results
        stage1_csv = f"stage1_data_only_results.csv"
        os.rename(csv_file, stage1_csv)
        print(f"📁 Stage 1 results saved as: {stage1_csv}")
        
        # Ask user if they want to proceed to stage 2
        print(f"\n🤔 Proceed to Stage 2 (Semgrep scanning)?")
        response = input("This will clone repositories and run Semgrep analysis (y/n): ").lower().strip()
        
        if response != 'y':
            print("⏭️ Skipping Stage 2. You can run it later with:")
            print(f"   python two_stage_scanner.py --stage2-only {stage1_csv}")
            sys.exit(0)
        
        # Stage 2: Targeted Semgrep scanning
        final_csv = run_stage_2(stage1_csv)
        
        if final_csv:
            # Rename final results
            final_results = f"stage2_final_results.csv"
            os.rename(final_csv, final_results)
            print(f"📁 Final results saved as: {final_results}")
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    print(f"\n🎯 TWO-STAGE ANALYSIS COMPLETE")
    print(f"Total time: {total_duration:.1f} seconds")
    print()
    print("📊 Benefits of two-stage approach:")
    print("✅ Fast initial screening with data-only analysis")
    print("✅ Targeted Semgrep scanning on promising repositories")
    print("✅ Efficient resource usage and time management")
    print("✅ Flexibility to review stage 1 results before stage 2")

if __name__ == "__main__":
    main()
