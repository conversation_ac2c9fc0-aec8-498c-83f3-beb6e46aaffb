{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/sud0Ru_NauthNRPC/README.md", "downloaded_repos/sud0Ru_NauthNRPC/constants.py", "downloaded_repos/sud0Ru_NauthNRPC/nauth.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.973296880722046, "profiling_times": {"config_time": 7.866865634918213, "core_time": 2.8330721855163574, "ignores_time": 0.002508878707885742, "total_time": 10.70375394821167}, "parsing_time": {"total_time": 0.031170129776000977, "per_file_time": {"mean": 0.015585064888000488, "std_dev": 4.6007588153429424e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.34876227378845215, "per_file_time": {"mean": 0.04359528422355652, "std_dev": 0.008372588280031401}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.14044475555419922, "per_file_and_rule_time": {"mean": 0.0073918292396946954, "std_dev": 0.00013015307362746427}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.08266544342041016, "per_def_and_rule_time": {"mean": 0.0008522210661897954, "std_dev": 1.1924435835489631e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}