{"version": "1.130.0", "results": [{"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/fecgov_fec-cms/fec/data/jinja2.py", "start": {"line": 9, "col": 11, "offset": 333}, "end": {"line": 9, "col": 40, "offset": 362}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/fecgov_fec-cms/fec/data/views.py", "start": {"line": 100, "col": 5, "offset": 2615}, "end": {"line": 111, "col": 10, "offset": 3046}, "extra": {"message": "Data from request (query) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/merge_shape_files.py", "start": {"line": 25, "col": 18, "offset": 641}, "end": {"line": 25, "col": 54, "offset": 677}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/merge_shape_files.py", "start": {"line": 64, "col": 64, "offset": 1820}, "end": {"line": 64, "col": 68, "offset": 1824}, "extra": {"message": "Found 'subprocess' function 'run' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal-search-ao.js", "start": {"line": 404, "col": 3, "offset": 16051}, "end": {"line": 404, "col": 55, "offset": 16103}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal-search-ao.js", "start": {"line": 616, "col": 3, "offset": 23538}, "end": {"line": 616, "col": 111, "offset": 23646}, "extra": {"message": "User controlled data in a HTML string may result in XSS", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/xss/"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.raw-html-concat.raw-html-concat", "shortlink": "https://sg.run/4xAx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal-search-ao.js", "start": {"line": 618, "col": 3, "offset": 23680}, "end": {"line": 618, "col": 108, "offset": 23785}, "extra": {"message": "User controlled data in a HTML string may result in XSS", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/xss/"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.raw-html-concat.raw-html-concat", "shortlink": "https://sg.run/4xAx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal-search-ao.js", "start": {"line": 620, "col": 3, "offset": 23822}, "end": {"line": 620, "col": 99, "offset": 23918}, "extra": {"message": "User controlled data in a HTML string may result in XSS", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/xss/"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.raw-html-concat.raw-html-concat", "shortlink": "https://sg.run/4xAx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/party-money-bars.js", "start": {"line": 58, "col": 3, "offset": 2044}, "end": {"line": 58, "col": 55, "offset": 2096}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/party-money-bars.js", "start": {"line": 62, "col": 5, "offset": 2261}, "end": {"line": 62, "col": 49, "offset": 2305}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/contact-form.js", "start": {"line": 220, "col": 11, "offset": 6660}, "end": {"line": 220, "col": 80, "offset": 6729}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/contact-form.js", "start": {"line": 228, "col": 13, "offset": 7024}, "end": {"line": 230, "col": 65, "offset": 7141}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/house-senate-overview-across-time.js", "start": {"line": 147, "col": 7, "offset": 5662}, "end": {"line": 147, "col": 46, "offset": 5701}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/house-senate-overview-across-time.js", "start": {"line": 243, "col": 7, "offset": 9411}, "end": {"line": 243, "col": 37, "offset": 9441}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "start": {"line": 203, "col": 5, "offset": 7564}, "end": {"line": 203, "col": 51, "offset": 7610}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "start": {"line": 206, "col": 5, "offset": 7717}, "end": {"line": 206, "col": 39, "offset": 7751}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "start": {"line": 322, "col": 57, "offset": 12343}, "end": {"line": 322, "col": 65, "offset": 12351}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "start": {"line": 349, "col": 9, "offset": 13271}, "end": {"line": 349, "col": 53, "offset": 13315}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "start": {"line": 472, "col": 7, "offset": 17973}, "end": {"line": 472, "col": 78, "offset": 18044}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "start": {"line": 544, "col": 7, "offset": 20223}, "end": {"line": 544, "col": 59, "offset": 20275}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "start": {"line": 572, "col": 7, "offset": 21083}, "end": {"line": 572, "col": 60, "offset": 21136}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/statistical-summary.js", "start": {"line": 319, "col": 3, "offset": 11356}, "end": {"line": 319, "col": 103, "offset": 11456}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "start": {"line": 18, "col": 7, "offset": 593}, "end": {"line": 18, "col": 30, "offset": 616}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "start": {"line": 24, "col": 7, "offset": 754}, "end": {"line": 24, "col": 24, "offset": 771}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "start": {"line": 27, "col": 7, "offset": 834}, "end": {"line": 27, "col": 56, "offset": 883}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "start": {"line": 30, "col": 7, "offset": 952}, "end": {"line": 30, "col": 35, "offset": 980}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "start": {"line": 33, "col": 7, "offset": 1052}, "end": {"line": 33, "col": 38, "offset": 1083}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "start": {"line": 36, "col": 7, "offset": 1179}, "end": {"line": 36, "col": 41, "offset": 1213}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "start": {"line": 39, "col": 7, "offset": 1299}, "end": {"line": 39, "col": 47, "offset": 1339}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/calendar/events.hbs", "start": {"line": 40, "col": 21, "offset": 1907}, "end": {"line": 40, "col": 43, "offset": 1929}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/candidates.hbs", "start": {"line": 87, "col": 7, "offset": 2615}, "end": {"line": 87, "col": 30, "offset": 2638}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/candidates.hbs", "start": {"line": 90, "col": 7, "offset": 2697}, "end": {"line": 90, "col": 35, "offset": 2725}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/candidates.hbs", "start": {"line": 93, "col": 7, "offset": 2790}, "end": {"line": 93, "col": 45, "offset": 2828}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/candidates.hbs", "start": {"line": 96, "col": 7, "offset": 2897}, "end": {"line": 96, "col": 45, "offset": 2935}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "start": {"line": 48, "col": 7, "offset": 1333}, "end": {"line": 48, "col": 57, "offset": 1383}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "start": {"line": 54, "col": 9, "offset": 1533}, "end": {"line": 54, "col": 63, "offset": 1587}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "start": {"line": 57, "col": 7, "offset": 1660}, "end": {"line": 57, "col": 44, "offset": 1697}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "start": {"line": 60, "col": 7, "offset": 1766}, "end": {"line": 60, "col": 36, "offset": 1795}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "start": {"line": 63, "col": 7, "offset": 1867}, "end": {"line": 63, "col": 57, "offset": 1917}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "start": {"line": 66, "col": 7, "offset": 1974}, "end": {"line": 66, "col": 24, "offset": 1991}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "start": {"line": 69, "col": 7, "offset": 2048}, "end": {"line": 69, "col": 24, "offset": 2065}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/disbursements.hbs", "start": {"line": 24, "col": 7, "offset": 704}, "end": {"line": 24, "col": 41, "offset": 738}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/disbursements.hbs", "start": {"line": 27, "col": 7, "offset": 801}, "end": {"line": 27, "col": 55, "offset": 849}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/disbursements.hbs", "start": {"line": 30, "col": 7, "offset": 906}, "end": {"line": 30, "col": 26, "offset": 925}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/disbursements.hbs", "start": {"line": 33, "col": 7, "offset": 982}, "end": {"line": 33, "col": 50, "offset": 1025}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/independent-expenditures.hbs", "start": {"line": 5, "col": 7, "offset": 125}, "end": {"line": 5, "col": 30, "offset": 148}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/loans.hbs", "start": {"line": 37, "col": 7, "offset": 989}, "end": {"line": 37, "col": 42, "offset": 1024}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/loans.hbs", "start": {"line": 40, "col": 7, "offset": 1096}, "end": {"line": 40, "col": 37, "offset": 1126}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/loans.hbs", "start": {"line": 43, "col": 7, "offset": 1215}, "end": {"line": 43, "col": 34, "offset": 1242}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/national-party-account-disbursements.hbs", "start": {"line": 27, "col": 7, "offset": 769}, "end": {"line": 27, "col": 55, "offset": 817}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/national-party-account-receipts.hbs", "start": {"line": 28, "col": 7, "offset": 1082}, "end": {"line": 28, "col": 47, "offset": 1122}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/national-party-account-receipts.hbs", "start": {"line": 37, "col": 7, "offset": 1280}, "end": {"line": 37, "col": 49, "offset": 1322}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/national-party-account-receipts.hbs", "start": {"line": 40, "col": 7, "offset": 1380}, "end": {"line": 40, "col": 63, "offset": 1436}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/party-coordinated-expenditures.hbs", "start": {"line": 56, "col": 7, "offset": 1602}, "end": {"line": 56, "col": 37, "offset": 1632}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/party-coordinated-expenditures.hbs", "start": {"line": 59, "col": 7, "offset": 1691}, "end": {"line": 59, "col": 29, "offset": 1713}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/receipts.hbs", "start": {"line": 36, "col": 7, "offset": 1097}, "end": {"line": 36, "col": 47, "offset": 1137}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/receipts.hbs", "start": {"line": 45, "col": 7, "offset": 1295}, "end": {"line": 45, "col": 49, "offset": 1337}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/receipts.hbs", "start": {"line": 48, "col": 7, "offset": 1395}, "end": {"line": 48, "col": 63, "offset": 1451}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/receipts.hbs", "start": {"line": 51, "col": 7, "offset": 1508}, "end": {"line": 51, "col": 26, "offset": 1527}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 10, "col": 5, "offset": 286}, "end": {"line": 10, "col": 40, "offset": 321}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 12, "col": 5, "offset": 340}, "end": {"line": 12, "col": 28, "offset": 363}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 52, "col": 7, "offset": 1535}, "end": {"line": 52, "col": 43, "offset": 1571}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 55, "col": 7, "offset": 1641}, "end": {"line": 55, "col": 59, "offset": 1693}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 58, "col": 7, "offset": 1772}, "end": {"line": 58, "col": 62, "offset": 1827}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 61, "col": 7, "offset": 1908}, "end": {"line": 61, "col": 64, "offset": 1965}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 64, "col": 7, "offset": 2034}, "end": {"line": 64, "col": 68, "offset": 2095}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 67, "col": 7, "offset": 2167}, "end": {"line": 67, "col": 55, "offset": 2215}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 71, "col": 9, "offset": 2326}, "end": {"line": 71, "col": 48, "offset": 2365}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 75, "col": 9, "offset": 2461}, "end": {"line": 75, "col": 55, "offset": 2507}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 80, "col": 9, "offset": 2638}, "end": {"line": 80, "col": 44, "offset": 2673}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 91, "col": 7, "offset": 2888}, "end": {"line": 91, "col": 48, "offset": 2929}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 95, "col": 9, "offset": 3058}, "end": {"line": 95, "col": 57, "offset": 3106}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 99, "col": 9, "offset": 3211}, "end": {"line": 99, "col": 64, "offset": 3266}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 103, "col": 7, "offset": 3360}, "end": {"line": 103, "col": 68, "offset": 3421}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 113, "col": 7, "offset": 3629}, "end": {"line": 113, "col": 51, "offset": 3673}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 116, "col": 7, "offset": 3738}, "end": {"line": 116, "col": 45, "offset": 3776}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 126, "col": 7, "offset": 3978}, "end": {"line": 126, "col": 45, "offset": 4016}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 129, "col": 7, "offset": 4085}, "end": {"line": 129, "col": 45, "offset": 4123}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 139, "col": 7, "offset": 4322}, "end": {"line": 139, "col": 49, "offset": 4364}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "start": {"line": 142, "col": 7, "offset": 4430}, "end": {"line": 142, "col": 55, "offset": 4478}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/ie-only.hbs", "start": {"line": 10, "col": 5, "offset": 286}, "end": {"line": 10, "col": 40, "offset": 321}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/ie-only.hbs", "start": {"line": 12, "col": 5, "offset": 340}, "end": {"line": 12, "col": 28, "offset": 363}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/ie-only.hbs", "start": {"line": 49, "col": 7, "offset": 1458}, "end": {"line": 49, "col": 55, "offset": 1506}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/ie-only.hbs", "start": {"line": 52, "col": 7, "offset": 1582}, "end": {"line": 52, "col": 54, "offset": 1629}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 10, "col": 5, "offset": 286}, "end": {"line": 10, "col": 40, "offset": 321}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 12, "col": 5, "offset": 340}, "end": {"line": 12, "col": 28, "offset": 363}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 49, "col": 7, "offset": 1453}, "end": {"line": 49, "col": 43, "offset": 1489}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 52, "col": 7, "offset": 1554}, "end": {"line": 52, "col": 48, "offset": 1595}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 55, "col": 7, "offset": 1665}, "end": {"line": 55, "col": 59, "offset": 1717}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 58, "col": 7, "offset": 1796}, "end": {"line": 58, "col": 62, "offset": 1851}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 61, "col": 7, "offset": 1932}, "end": {"line": 61, "col": 64, "offset": 1989}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 64, "col": 7, "offset": 2057}, "end": {"line": 64, "col": 51, "offset": 2101}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 67, "col": 7, "offset": 2166}, "end": {"line": 67, "col": 45, "offset": 2204}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 70, "col": 7, "offset": 2267}, "end": {"line": 70, "col": 46, "offset": 2306}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 73, "col": 7, "offset": 2378}, "end": {"line": 73, "col": 55, "offset": 2426}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 76, "col": 7, "offset": 2495}, "end": {"line": 76, "col": 45, "offset": 2533}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 79, "col": 7, "offset": 2634}, "end": {"line": 79, "col": 72, "offset": 2699}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "start": {"line": 82, "col": 7, "offset": 2781}, "end": {"line": 82, "col": 53, "offset": 2827}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals-box.js", "start": {"line": 86, "col": 3, "offset": 3819}, "end": {"line": 90, "col": 65, "offset": 4101}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals-box.js", "start": {"line": 140, "col": 5, "offset": 5905}, "end": {"line": 140, "col": 52, "offset": 5952}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals-box.js", "start": {"line": 141, "col": 5, "offset": 5957}, "end": {"line": 141, "col": 67, "offset": 6019}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals-box.js", "start": {"line": 432, "col": 5, "offset": 16376}, "end": {"line": 435, "col": 7, "offset": 16497}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals-box.js", "start": {"line": 470, "col": 5, "offset": 17816}, "end": {"line": 470, "col": 63, "offset": 17874}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals-box.js", "start": {"line": 689, "col": 3, "offset": 27421}, "end": {"line": 689, "col": 37, "offset": 27455}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/contributions-by-state-box.js", "start": {"line": 653, "col": 3, "offset": 23291}, "end": {"line": 653, "col": 49, "offset": 23337}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/contributions-by-state-box.js", "start": {"line": 726, "col": 5, "offset": 25623}, "end": {"line": 726, "col": 45, "offset": 25663}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/contributions-by-state-box.js", "start": {"line": 835, "col": 3, "offset": 29543}, "end": {"line": 835, "col": 137, "offset": 29677}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/contributions-by-state-box.js", "start": {"line": 875, "col": 5, "offset": 31168}, "end": {"line": 875, "col": 56, "offset": 31219}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "start": {"line": 787, "col": 3, "offset": 28385}, "end": {"line": 787, "col": 38, "offset": 28420}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "start": {"line": 790, "col": 5, "offset": 28545}, "end": {"line": 790, "col": 48, "offset": 28588}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "start": {"line": 855, "col": 7, "offset": 31096}, "end": {"line": 855, "col": 43, "offset": 31132}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "start": {"line": 984, "col": 5, "offset": 35748}, "end": {"line": 984, "col": 40, "offset": 35783}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "start": {"line": 1028, "col": 3, "offset": 37369}, "end": {"line": 1028, "col": 42, "offset": 37408}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "start": {"line": 1029, "col": 3, "offset": 37411}, "end": {"line": 1029, "col": 44, "offset": 37452}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500-status.html", "start": {"line": 79, "col": 5, "offset": 3233}, "end": {"line": 79, "col": 123, "offset": 3351}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "start": {"line": 182, "col": 5, "offset": 7816}, "end": {"line": 182, "col": 123, "offset": 7934}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "start": {"line": 169, "col": 5, "offset": 7434}, "end": {"line": 169, "col": 123, "offset": 7552}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preconnects.html", "start": {"line": 22, "col": 1, "offset": 971}, "end": {"line": 22, "col": 61, "offset": 1031}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 19, "col": 5, "offset": 1178}, "end": {"line": 37, "col": 14, "offset": 2791}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preconnects.html:1:\n Failure: not a program", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preconnects.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n Failure: not a program", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n Failure: not a program", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n Failure: not a program", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n Failure: not a program", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n Failure: not a program", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fecgov_fec-cms/fec/search/templates/search/search.html:1:\n Failure: not a program", "path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/search.html"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 94}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html:1:\n `{% extends \"base.html\" %}\n{% block title %}404 error | FEC {% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 94}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500-status.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 60}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/500-status.html:1:\n `{% load static compress wagtailuserbar %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500-status.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500-status.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 60}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 98}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html:1:\n `{% extends \"base.html\" %}\n\n{% block title %}Server error | FEC {% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 98}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 111}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "start": {"line": 125, "col": 78, "offset": 0}, "end": {"line": 125, "col": 105, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html:1:\n `{#- extended by most fec/home/<USER>/*.html -#}\n{% load static compress wagtailuserbar %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 111}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "start": {"line": 125, "col": 78, "offset": 0}, "end": {"line": 125, "col": 105, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 60}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "start": {"line": 123, "col": 78, "offset": 0}, "end": {"line": 123, "col": 105, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html:1:\n `{% load static compress wagtailuserbar %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 60}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "start": {"line": 123, "col": 78, "offset": 0}, "end": {"line": 123, "col": 105, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-groups.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 30, "offset": 29}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-groups.html:1:\n `{% load wagtailimages_tags %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-groups.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-groups.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 30, "offset": 29}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 12, "col": 14, "offset": 470}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html", "start": {"line": 43, "col": 5, "offset": 0}, "end": {"line": 44, "col": 16, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html:1:\n `{% load wagtailimages_tags %}\n{% load author_groups %}\n\n{% comment %}\n    Check to see if the first author.name is an author group instead of\n    an individual. If so, render the author_groups.py templatetag, else render the template below with chosen authors.\n{% endcomment %}\n\n  {% with self.authors.all|first as first_author %}\n   {% if first_author.author.name in settings.CONSTANTS.author_groups.keys %}\n    {% author_group first_author.author.name %}\n   {% else %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 12, "col": 14, "offset": 470}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html", "start": {"line": 43, "col": 5, "offset": 0}, "end": {"line": 44, "col": 16, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/breadcrumbs.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 2, "col": 191, "offset": 190}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/breadcrumbs.html:2:\n `{% if \"/legal-resources/\" in request.path or \"/campaign-finance-data/\" in request.path or \"/introduction-campaign-finance/\" in request.path or \"/office-inspector-general/\" in request.path %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/breadcrumbs.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/breadcrumbs.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 2, "col": 191, "offset": 190}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 67, "offset": 190}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "start": {"line": 8, "col": 7, "offset": 0}, "end": {"line": 11, "col": 49, "offset": 86}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "start": {"line": 18, "col": 5, "offset": 0}, "end": {"line": 19, "col": 12, "offset": 23}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html:1:\n `{% if settings  != undefined %}\n    {% if settings.FEC_CMS_ENVIRONMENT != 'PRODUCTION' %}   \n      <div class=\"banner-dev\">\n        {# Uses settings.FEC_CMS_ENVIRONMENT for HTML templates #}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 67, "offset": 190}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "start": {"line": 8, "col": 7, "offset": 0}, "end": {"line": 11, "col": 49, "offset": 86}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "start": {"line": 18, "col": 5, "offset": 0}, "end": {"line": 19, "col": 12, "offset": 23}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 16, "col": 72, "offset": 0}, "end": {"line": 16, "col": 117, "offset": 45}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 17, "col": 56, "offset": 0}, "end": {"line": 17, "col": 121, "offset": 65}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 22, "col": 33, "offset": 0}, "end": {"line": 22, "col": 40, "offset": 7}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 28, "col": 64, "offset": 0}, "end": {"line": 28, "col": 130, "offset": 66}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html:1:\n `{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 16, "col": 72, "offset": 0}, "end": {"line": 16, "col": 117, "offset": 45}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 17, "col": 56, "offset": 0}, "end": {"line": 17, "col": 121, "offset": 65}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 22, "col": 33, "offset": 0}, "end": {"line": 22, "col": 40, "offset": 7}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "start": {"line": 28, "col": 64, "offset": 0}, "end": {"line": 28, "col": 130, "offset": 66}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preloads.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 17, "offset": 318}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preloads.html:1:\n `{% spaceless %}\n{% comment %}\n\nPreloading fonts / letting the browser know we're going to request them later in the page\n\nAll fonts are listed here but we're only preloading those we're using on the homepage at this time,\nwith the goal of simplifying preloading another font as needed (as of 2021-03)\n\n{% endcomment %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preloads.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preloads.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 17, "offset": 318}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 36}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags.html:1:\n `{% load static %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 36}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 48}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html", "start": {"line": 43, "col": 35, "offset": 0}, "end": {"line": 45, "col": 129, "offset": 186}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html:1:\n `{% load wagtailimages_tags %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 48}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html", "start": {"line": 43, "col": 35, "offset": 0}, "end": {"line": 45, "col": 129, "offset": 186}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/adr_search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/adr_search.html:1:\n `{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/adr_search.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/adr_search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/af_search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/af_search.html:1:\n `{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/af_search.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/af_search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 28, "offset": 56}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "start": {"line": 5, "col": 1, "offset": 0}, "end": {"line": 5, "col": 11, "offset": 10}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 7, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html:1:\n `{% load wagtailcore_tags %}\n\n{% if self.internal_page %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 28, "offset": 56}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "start": {"line": 5, "col": 1, "offset": 0}, "end": {"line": 5, "col": 11, "offset": 10}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 7, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/document-list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 46}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/document-list.html:1:\n `{% load wagtailcore_tags %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/document-list.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/document-list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 46}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-forms.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 57}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-forms.html:1:\n `{% load wagtailcore_tags %}\n{% load wagtailimages_tags %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-forms.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-forms.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 57}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/mur_search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/mur_search.html:1:\n `{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/mur_search.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/mur_search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/page-links.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 28, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/page-links.html:1:\n `{% load wagtailcore_tags %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/page-links.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/page-links.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 28, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/related-media.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 75}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/related-media.html:1:\n `{% load wagtailcore_tags %}\n{% load wagtailimages_tags %}\n{% load static %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/related-media.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/related-media.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 75}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/reporting-example-cards.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 75}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/reporting-example-cards.html:1:\n `{% load wagtailcore_tags %}\n{% load wagtailimages_tags %}\n{% load static %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/reporting-example-cards.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/reporting-example-cards.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 75}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-aside.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 57}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-aside.html:1:\n `{% load wagtailcore_tags %}\n{% load wagtailimages_tags %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-aside.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-aside.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 57}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-documents.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 57}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-documents.html:1:\n `{% load wagtailcore_tags %}\n{% load wagtailimages_tags %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-documents.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-documents.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 30, "offset": 57}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 24, "offset": 71}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 13, "offset": 12}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html:1:\n `{% load wagtailcore_tags %}\n{% load filters %}\n\n{% for block in self %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 24, "offset": 71}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 13, "offset": 12}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 46}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document.html:1:\n `{% load wagtailcore_tags %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 46}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 56, "col": 1, "offset": 0}, "end": {"line": 56, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load updates %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 56, "col": 1, "offset": 0}, "end": {"line": 56, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 12, "col": 20, "offset": 330}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 22, "col": 3, "offset": 0}, "end": {"line": 22, "col": 44, "offset": 41}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 67, "col": 53, "offset": 0}, "end": {"line": 67, "col": 86, "offset": 33}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 140, "col": 65, "offset": 0}, "end": {"line": 140, "col": 96, "offset": 31}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 150, "col": 65, "offset": 0}, "end": {"line": 150, "col": 95, "offset": 30}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 160, "col": 65, "offset": 0}, "end": {"line": 160, "col": 96, "offset": 31}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 169, "col": 65, "offset": 0}, "end": {"line": 169, "col": 97, "offset": 32}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 178, "col": 65, "offset": 0}, "end": {"line": 178, "col": 98, "offset": 33}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 198, "col": 1, "offset": 0}, "end": {"line": 200, "col": 21, "offset": 36}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 202, "col": 1, "offset": 0}, "end": {"line": 202, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load home_page %}\n{% load filters %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% comment %}\nThis template is for previewing the alert-for-emergency-use-only banner on Wagtail\n{% endcomment %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 12, "col": 20, "offset": 330}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 22, "col": 3, "offset": 0}, "end": {"line": 22, "col": 44, "offset": 41}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 67, "col": 53, "offset": 0}, "end": {"line": 67, "col": 86, "offset": 33}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 140, "col": 65, "offset": 0}, "end": {"line": 140, "col": 96, "offset": 31}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 150, "col": 65, "offset": 0}, "end": {"line": 150, "col": 95, "offset": 30}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 160, "col": 65, "offset": 0}, "end": {"line": 160, "col": 96, "offset": 31}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 169, "col": 65, "offset": 0}, "end": {"line": 169, "col": 97, "offset": 32}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 178, "col": 65, "offset": 0}, "end": {"line": 178, "col": 98, "offset": 33}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 198, "col": 1, "offset": 0}, "end": {"line": 200, "col": 21, "offset": 36}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 202, "col": 1, "offset": 0}, "end": {"line": 202, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 16, "offset": 210}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 137, "col": 1, "offset": 0}, "end": {"line": 142, "col": 15, "offset": 171}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n{% load compress %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block css %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 16, "offset": 210}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 137, "col": 1, "offset": 0}, "end": {"line": 142, "col": 15, "offset": 171}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 91}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html", "start": {"line": 194, "col": 1, "offset": 0}, "end": {"line": 194, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 91}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html", "start": {"line": 194, "col": 1, "offset": 0}, "end": {"line": 194, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 20, "offset": 245}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html", "start": {"line": 164, "col": 1, "offset": 0}, "end": {"line": 166, "col": 15, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load filters %}\n{% load static %}\n{% load updates %}\n{% load tips_for_treasurers %}\n\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 20, "offset": 245}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html", "start": {"line": 164, "col": 1, "offset": 0}, "end": {"line": 166, "col": 15, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 48, "offset": 284}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 13, "col": 7, "offset": 16}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 59, "col": 59, "offset": 0}, "end": {"line": 59, "col": 60, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 121, "col": 59, "offset": 0}, "end": {"line": 121, "col": 60, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 183, "col": 59, "offset": 0}, "end": {"line": 183, "col": 60, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 232, "col": 1, "offset": 0}, "end": {"line": 232, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n\n{% block content %}\n\n{% include 'partials/breadcrumbs.html' with page=self links=self.get_ancestors style='secondary' %}\n<div class=\"container\">\n  <header class=\"heading--main u-padding--top\">` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 48, "offset": 284}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 13, "col": 7, "offset": 16}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 59, "col": 59, "offset": 0}, "end": {"line": 59, "col": 60, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 121, "col": 59, "offset": 0}, "end": {"line": 121, "col": 60, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 183, "col": 59, "offset": 0}, "end": {"line": 183, "col": 60, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 232, "col": 1, "offset": 0}, "end": {"line": 232, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 35, "offset": 298}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 13, "col": 5, "offset": 0}, "end": {"line": 13, "col": 14, "offset": 9}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 51, "col": 37, "offset": 0}, "end": {"line": 51, "col": 67, "offset": 30}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 62, "col": 37, "offset": 0}, "end": {"line": 62, "col": 67, "offset": 30}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 73, "col": 37, "offset": 0}, "end": {"line": 73, "col": 69, "offset": 32}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 84, "col": 37, "offset": 0}, "end": {"line": 84, "col": 71, "offset": 34}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 104, "col": 3, "offset": 0}, "end": {"line": 108, "col": 15, "offset": 34}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load wagtailimages_tags %}\n{% load static %}\n\n{% block content %}\n{% include 'partials/breadcrumbs.html' with page=self links=ancestors style='secondary' %}\n\n<article class=\"main\">\n  <div class=\"container\">\n    <header class=\"heading--main\">` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 35, "offset": 298}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 13, "col": 5, "offset": 0}, "end": {"line": 13, "col": 14, "offset": 9}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 51, "col": 37, "offset": 0}, "end": {"line": 51, "col": 67, "offset": 30}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 62, "col": 37, "offset": 0}, "end": {"line": 62, "col": 67, "offset": 30}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 73, "col": 37, "offset": 0}, "end": {"line": 73, "col": 69, "offset": 32}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 84, "col": 37, "offset": 0}, "end": {"line": 84, "col": 71, "offset": 34}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 104, "col": 3, "offset": 0}, "end": {"line": 108, "col": 15, "offset": 34}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 35, "offset": 274}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 13, "col": 5, "offset": 0}, "end": {"line": 13, "col": 14, "offset": 9}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 83, "col": 3, "offset": 0}, "end": {"line": 86, "col": 15, "offset": 33}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n\n{% block content %}\n\n{% include 'partials/breadcrumbs.html' with page=self links=self.ancestors style='secondary' %}\n\n<article class=\"main\">\n  <div class=\"container\">\n    <header class=\"heading--main\">` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 35, "offset": 274}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 13, "col": 5, "offset": 0}, "end": {"line": 13, "col": 14, "offset": 9}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 83, "col": 3, "offset": 0}, "end": {"line": 86, "col": 15, "offset": 33}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 34, "offset": 290}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 12, "col": 4, "offset": 0}, "end": {"line": 12, "col": 13, "offset": 9}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n{% block content %}\n\n{% include 'partials/breadcrumbs.html' with page=self links=self.ancestors style='secondary' %}\n<article class=\"main\">\n  <div class=\"container\">\n   <header class=\"heading--main\">` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 34, "offset": 290}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 12, "col": 4, "offset": 0}, "end": {"line": 12, "col": 13, "offset": 9}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 264}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 18, "col": 3, "offset": 0}, "end": {"line": 18, "col": 38, "offset": 35}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 100, "col": 1, "offset": 0}, "end": {"line": 104, "col": 38, "offset": 75}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 136, "col": 1, "offset": 0}, "end": {"line": 136, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"home_base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n{% load top_entities elections_lookup home_page commissioners %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 264}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 18, "col": 3, "offset": 0}, "end": {"line": 18, "col": 38, "offset": 35}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 100, "col": 1, "offset": 0}, "end": {"line": 104, "col": 38, "offset": 75}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 136, "col": 1, "offset": 0}, "end": {"line": 136, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 12, "col": 20, "offset": 324}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 22, "col": 3, "offset": 0}, "end": {"line": 22, "col": 44, "offset": 41}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 67, "col": 37, "offset": 0}, "end": {"line": 67, "col": 42, "offset": 5}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 67, "col": 53, "offset": 0}, "end": {"line": 67, "col": 86, "offset": 33}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 140, "col": 65, "offset": 0}, "end": {"line": 140, "col": 96, "offset": 31}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 150, "col": 65, "offset": 0}, "end": {"line": 150, "col": 95, "offset": 30}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 160, "col": 65, "offset": 0}, "end": {"line": 160, "col": 96, "offset": 31}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 169, "col": 65, "offset": 0}, "end": {"line": 169, "col": 97, "offset": 32}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 178, "col": 65, "offset": 0}, "end": {"line": 178, "col": 98, "offset": 33}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 198, "col": 1, "offset": 0}, "end": {"line": 200, "col": 21, "offset": 36}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 202, "col": 1, "offset": 0}, "end": {"line": 202, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load home_page %}\n{% load filters %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% comment %}\nThis template is for previewing the home page banner announcement on Wagtail\n{% endcomment %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 12, "col": 20, "offset": 324}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 22, "col": 3, "offset": 0}, "end": {"line": 22, "col": 44, "offset": 41}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 67, "col": 37, "offset": 0}, "end": {"line": 67, "col": 42, "offset": 5}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 67, "col": 53, "offset": 0}, "end": {"line": 67, "col": 86, "offset": 33}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 140, "col": 65, "offset": 0}, "end": {"line": 140, "col": 96, "offset": 31}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 150, "col": 65, "offset": 0}, "end": {"line": 150, "col": 95, "offset": 30}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 160, "col": 65, "offset": 0}, "end": {"line": 160, "col": 96, "offset": 31}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 169, "col": 65, "offset": 0}, "end": {"line": 169, "col": 97, "offset": 32}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 178, "col": 65, "offset": 0}, "end": {"line": 178, "col": 98, "offset": 33}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 198, "col": 1, "offset": 0}, "end": {"line": 200, "col": 21, "offset": 36}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 202, "col": 1, "offset": 0}, "end": {"line": 202, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 18, "offset": 119}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 42, "col": 20, "offset": 35}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 48, "col": 42, "offset": 0}, "end": {"line": 48, "col": 92, "offset": 50}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 49, "col": 45, "offset": 0}, "end": {"line": 49, "col": 98, "offset": 53}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 50, "col": 51, "offset": 0}, "end": {"line": 50, "col": 110, "offset": 59}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 51, "col": 45, "offset": 0}, "end": {"line": 51, "col": 98, "offset": 53}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 97, "col": 1, "offset": 0}, "end": {"line": 103, "col": 17, "offset": 176}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 113, "col": 3, "offset": 0}, "end": {"line": 113, "col": 13, "offset": 10}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 127, "col": 3, "offset": 0}, "end": {"line": 128, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"home/feed_base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n\n{% block intro %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 18, "offset": 119}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 42, "col": 20, "offset": 35}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 48, "col": 42, "offset": 0}, "end": {"line": 48, "col": 92, "offset": 50}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 49, "col": 45, "offset": 0}, "end": {"line": 49, "col": 98, "offset": 53}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 50, "col": 51, "offset": 0}, "end": {"line": 50, "col": 110, "offset": 59}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 51, "col": 45, "offset": 0}, "end": {"line": 51, "col": 98, "offset": 53}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 97, "col": 1, "offset": 0}, "end": {"line": 103, "col": 17, "offset": 176}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 113, "col": 3, "offset": 0}, "end": {"line": 113, "col": 13, "offset": 10}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 127, "col": 3, "offset": 0}, "end": {"line": 128, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 16, "col": 42, "offset": 450}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "start": {"line": 185, "col": 31, "offset": 0}, "end": {"line": 185, "col": 67, "offset": 36}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "start": {"line": 195, "col": 1, "offset": 0}, "end": {"line": 198, "col": 15, "offset": 106}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n{% load legal_news %}\n\n{% block title %}Legal resources{% endblock %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block extra_js %}\n  {#- init and global chunks are loaded by base.html -#}\n  {% tags_for_js_chunks 'legal.js' '' %}\n{% endblock extra_js %}\n\n{% block content %}\n{% include \"partials/search-hero.html\" %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 16, "col": 42, "offset": 450}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "start": {"line": 185, "col": 31, "offset": 0}, "end": {"line": 185, "col": 67, "offset": 36}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "start": {"line": 195, "col": 1, "offset": 0}, "end": {"line": 198, "col": 15, "offset": 106}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 213}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 124, "col": 88, "offset": 0}, "end": {"line": 124, "col": 121, "offset": 33}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 131, "col": 88, "offset": 0}, "end": {"line": 131, "col": 121, "offset": 33}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 138, "col": 92, "offset": 0}, "end": {"line": 138, "col": 128, "offset": 36}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 155, "col": 93, "offset": 0}, "end": {"line": 155, "col": 138, "offset": 45}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 156, "col": 58, "offset": 0}, "end": {"line": 156, "col": 123, "offset": 65}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 165, "col": 41, "offset": 0}, "end": {"line": 167, "col": 132, "offset": 196}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 206, "col": 1, "offset": 0}, "end": {"line": 207, "col": 15, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n{% load updates %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 213}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 124, "col": 88, "offset": 0}, "end": {"line": 124, "col": 121, "offset": 33}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 131, "col": 88, "offset": 0}, "end": {"line": 131, "col": 121, "offset": 33}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 138, "col": 92, "offset": 0}, "end": {"line": 138, "col": 128, "offset": 36}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 155, "col": 93, "offset": 0}, "end": {"line": 155, "col": 138, "offset": 45}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 156, "col": 58, "offset": 0}, "end": {"line": 156, "col": 123, "offset": 65}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 165, "col": 41, "offset": 0}, "end": {"line": 167, "col": 132, "offset": 196}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 206, "col": 1, "offset": 0}, "end": {"line": 207, "col": 15, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 250}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 17, "col": 3, "offset": 0}, "end": {"line": 17, "col": 74, "offset": 71}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 25, "col": 3, "offset": 0}, "end": {"line": 25, "col": 14, "offset": 11}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 65, "col": 3, "offset": 0}, "end": {"line": 65, "col": 48, "offset": 45}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 85, "col": 3, "offset": 0}, "end": {"line": 86, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load oig_most_recent %}\n{% block title %}{{ self.title }}{% endblock %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 250}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 17, "col": 3, "offset": 0}, "end": {"line": 17, "col": 74, "offset": 71}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 25, "col": 3, "offset": 0}, "end": {"line": 25, "col": 14, "offset": 11}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 65, "col": 3, "offset": 0}, "end": {"line": 65, "col": 48, "offset": 45}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 85, "col": 3, "offset": 0}, "end": {"line": 86, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 141, "col": 1, "offset": 0}, "end": {"line": 141, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load updates %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 141, "col": 1, "offset": 0}, "end": {"line": 141, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 21, "col": 15, "offset": 686}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 24, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 70}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}\n\n{% block content %}\n\n{% include 'partials/breadcrumbs.html' with page=self links=self.get_ancestors style='secondary' %}\n\n<article class=\"main\">\n  <div class=\"container\" id=\"sections\">\n    <h1 class=\"heading--main\">{{ self.title }}</h1>\n    <div class=\"content__section\">\n      <div class=\"rich-text\">{{ self.intro }}</div>\n    </div>\n    <div class=\"grid grid--3-wide\">\n      {% for blurb in self.document_feeds %}\n      <a class=\"grid__item card--major\" href=\"{{ blurb.value.page.url }}\">\n        <div class=\"card--major__title\">\n          <h2>{{ blurb.value.page.title }}</h2>\n        </div>` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 21, "col": 15, "offset": 686}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "start": {"line": 24, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 70}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 175}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html", "start": {"line": 62, "col": 1, "offset": 0}, "end": {"line": 64, "col": 15, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 175}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html", "start": {"line": 62, "col": 1, "offset": 0}, "end": {"line": 64, "col": 15, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html", "start": {"line": 69, "col": 1, "offset": 0}, "end": {"line": 71, "col": 15, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load filters %}\n{% load static %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 194}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html", "start": {"line": 69, "col": 1, "offset": 0}, "end": {"line": 71, "col": 15, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 213}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "start": {"line": 49, "col": 1, "offset": 0}, "end": {"line": 49, "col": 54, "offset": 53}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "start": {"line": 89, "col": 1, "offset": 0}, "end": {"line": 93, "col": 15, "offset": 69}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags wagtailimages_tags %}\n{% load filters %}\n{% load static %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 213}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "start": {"line": 49, "col": 1, "offset": 0}, "end": {"line": 49, "col": 54, "offset": 53}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "start": {"line": 89, "col": 1, "offset": 0}, "end": {"line": 93, "col": 15, "offset": 69}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 175}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html", "start": {"line": 61, "col": 1, "offset": 0}, "end": {"line": 63, "col": 15, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags %}\n{% load static %}\n{% block body_class %}template-{{ self.get_verbose_name | slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 175}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html", "start": {"line": 61, "col": 1, "offset": 0}, "end": {"line": 63, "col": 15, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/audit-search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 64}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/audit-search.html:1:\n `{% load wagtailcore_tags %}\n{% load static %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/audit-search.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/audit-search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 64}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 18, "offset": 47}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html", "start": {"line": 10, "col": 57, "offset": 0}, "end": {"line": 10, "col": 89, "offset": 32}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html:1:\n `{% load wagtailimages_tags %}\n{% load static %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 18, "offset": 47}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html", "start": {"line": 10, "col": 57, "offset": 0}, "end": {"line": 10, "col": 89, "offset": 32}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/document.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 48}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/document.html:1:\n `{% load wagtailimages_tags %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/document.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/document.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 48}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 29, "offset": 56}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 35}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 25, "col": 31, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 38, "col": 1, "offset": 0}, "end": {"line": 38, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html:1:\n `{% load wagtailcore_tags %}\n{% if draft_alert_banners %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 29, "offset": 56}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 35}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 25, "col": 31, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "start": {"line": 38, "col": 1, "offset": 0}, "end": {"line": 38, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 23, "offset": 50}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 20, "col": 17, "offset": 29}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 25, "col": 31, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 38, "col": 1, "offset": 0}, "end": {"line": 38, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html:1:\n `{% load wagtailcore_tags %}\n{% if alert_banners %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 23, "offset": 50}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 20, "col": 17, "offset": 29}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 25, "col": 31, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "start": {"line": 38, "col": 1, "offset": 0}, "end": {"line": 38, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-news.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-news.html:1:\n `{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-news.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-news.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 15, "offset": 42}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 11, "offset": 10}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html:1:\n `{% load wagtailcore_tags %}\n{% if error %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 15, "offset": 42}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 11, "offset": 10}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/legal-news.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/legal-news.html:1:\n `{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/legal-news.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/legal-news.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/oig-most-recent.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 55, "offset": 73}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/oig-most-recent.html:1:\n `{% load filters %}\n{# List to display the most recent OIG report items #}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/oig-most-recent.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/oig-most-recent.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 55, "offset": 73}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 118}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 47, "col": 45, "offset": 0}, "end": {"line": 47, "col": 46, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 48, "col": 45, "offset": 0}, "end": {"line": 48, "col": 46, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 49, "col": 45, "offset": 0}, "end": {"line": 49, "col": 46, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 64, "col": 41, "offset": 0}, "end": {"line": 64, "col": 42, "offset": 1}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 81, "col": 1, "offset": 0}, "end": {"line": 81, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html:1:\n `{# The template for the homepage feature #}\n{% if FEATURES.barcharts %}\n{% load wagtailcore_tags %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 118}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 47, "col": 45, "offset": 0}, "end": {"line": 47, "col": 46, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 48, "col": 45, "offset": 0}, "end": {"line": 48, "col": 46, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 49, "col": 45, "offset": 0}, "end": {"line": 49, "col": 46, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 64, "col": 41, "offset": 0}, "end": {"line": 64, "col": 42, "offset": 1}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "start": {"line": 81, "col": 1, "offset": 0}, "end": {"line": 81, "col": 12, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/section-nav.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 46}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/section-nav.html:1:\n `{% load wagtailcore_tags %}\n{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/section-nav.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/section-nav.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 19, "offset": 46}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/tips-for-treasurers.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/tips-for-treasurers.html:1:\n `{% load filters %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/tips-for-treasurers.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/tips-for-treasurers.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 18, "offset": 73}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 35}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 29, "col": 17, "offset": 32}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 32, "col": 32, "offset": 0}, "end": {"line": 32, "col": 39, "offset": 7}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 60, "col": 73, "offset": 0}, "end": {"line": 60, "col": 79, "offset": 6}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 81, "col": 1, "offset": 0}, "end": {"line": 83, "col": 23, "offset": 38}}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 91, "col": 1, "offset": 0}, "end": {"line": 91, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html:1:\n `{% extends \"home/feed_base.html\" %}\n{% load filters %}\n\n{% block intro %}` was unexpected", "path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "spans": [{"file": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 18, "offset": 73}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 35}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 29, "col": 17, "offset": 32}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 32, "col": 32, "offset": 0}, "end": {"line": 32, "col": 39, "offset": 7}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 60, "col": 73, "offset": 0}, "end": {"line": 60, "col": 79, "offset": 6}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 81, "col": 1, "offset": 0}, "end": {"line": 83, "col": 23, "offset": 38}}, {"file": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "start": {"line": 91, "col": 1, "offset": 0}, "end": {"line": 91, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/fecgov_fec-cms/.cfignore", "downloaded_repos/fecgov_fec-cms/.circleci/config.yml", "downloaded_repos/fecgov_fec-cms/.coveragerc", "downloaded_repos/fecgov_fec-cms/.cspell/custom-dictionary-workspace.txt", "downloaded_repos/fecgov_fec-cms/.eslintignore", "downloaded_repos/fecgov_fec-cms/.eslintrc", "downloaded_repos/fecgov_fec-cms/.flake8", "downloaded_repos/fecgov_fec-cms/.github/ISSUE_TEMPLATE/bug-report.md", "downloaded_repos/fecgov_fec-cms/.github/ISSUE_TEMPLATE/feature-request.md", "downloaded_repos/fecgov_fec-cms/.github/ISSUE_TEMPLATE/task-issue.md", "downloaded_repos/fecgov_fec-cms/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/fecgov_fec-cms/.gitignore", "downloaded_repos/fecgov_fec-cms/.hound.yml", "downloaded_repos/fecgov_fec-cms/.jshintignore", "downloaded_repos/fecgov_fec-cms/.jshintrc", "downloaded_repos/fecgov_fec-cms/.pre-commit-config.yaml", "downloaded_repos/fecgov_fec-cms/.prettierignore", "downloaded_repos/fecgov_fec-cms/.prettierrc", "downloaded_repos/fecgov_fec-cms/.puppeteerrc.cjs", "downloaded_repos/fecgov_fec-cms/.stylelintrc.json", "downloaded_repos/fecgov_fec-cms/CONTRIBUTING.md", "downloaded_repos/fecgov_fec-cms/LICENSE.md", "downloaded_repos/fecgov_fec-cms/Procfile", "downloaded_repos/fecgov_fec-cms/README.md", "downloaded_repos/fecgov_fec-cms/babel.config.json", "downloaded_repos/fecgov_fec-cms/bin/cf_env_setup.sh", "downloaded_repos/fecgov_fec-cms/bin/run.sh", "downloaded_repos/fecgov_fec-cms/code-gov-config.json", "downloaded_repos/fecgov_fec-cms/conftest.py", "downloaded_repos/fecgov_fec-cms/fec/.coveragerc", "downloaded_repos/fecgov_fec-cms/fec/data/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/data/admin.py", "downloaded_repos/fecgov_fec-cms/fec/data/api_caller.py", "downloaded_repos/fecgov_fec-cms/fec/data/apps.py", "downloaded_repos/fecgov_fec-cms/fec/data/constants.py", "downloaded_repos/fecgov_fec-cms/fec/data/ecfr_caller.py", "downloaded_repos/fecgov_fec-cms/fec/data/jinja2.py", "downloaded_repos/fecgov_fec-cms/fec/data/legal_test_data.py", "downloaded_repos/fecgov_fec-cms/fec/data/templates/browse-data.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/candidates-single.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/committees-single.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/datatable.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/election-lookup.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/elections.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/house-senate-overview.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/landing.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/layouts/main.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/layouts/sidebar-page.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/layouts/widgets.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/breadcrumbs.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/bythenumbers.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/chart-committee-overviews.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/cycle-select.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/datatables.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/disclaimer.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/document.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/entity-pages.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/checkbox.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/committee-types.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/contributor-states.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/date.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/districts.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/dropdown-json.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/election-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/filing-frequency.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/ie-reports.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/office-sought.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/parties.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/range.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/report-type.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/states.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/support-oppose.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/text.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/typeahead-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/version-status.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/filters/years.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/missing-transferred.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/missing.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/null.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/page-header.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/reaction-box.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/reporting-info.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/search.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/tables.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/macros/widgets.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/allocated-federal-nonfederal-disbursements-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/audit-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/bulk-data.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/candidates.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/committees.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/external.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/filings-reports.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/historical.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/loans-debts.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/raising.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/browse-data/spending.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidate/about-candidate.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidate/filings-tab.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidate/financial-summary.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidate/other-spending-tab.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidate/raising.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidate/spending.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidates-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/candidates-office-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee/about-committee.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee/filings.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee/financial-summary.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee/raising.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee/spending.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee-totals-house-senate.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee-totals-ie-only.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee-totals-pac-party.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committee-totals-presidential.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/committees-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/communication-costs-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/datatable-modal.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/debts-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/disbursements-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/electioneering-communications-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/elections/about-this-election-tab.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/elections/election-data-and-compliance-tab.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/elections/election-profile-cards.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/elections/other-spending-tab.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/elections/sidebar-nav.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filings-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters/active-candidates.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters/audit-committee-types.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters/efiling.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters/form-type.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters/primary-general.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters/report-type.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters/unique-receipts.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/filters.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/google-tag-manager-noscript.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/google-tag-manager-script.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/hero.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/house-senate-overview/section-contributions-across-time.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/house-senate-overview/section-summary.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/house-senate-overview/section-totals-for-all-elections.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/independent-expenditures-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/individual-contributions-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/loading-tab.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/loans-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/meta-tags-preconnects.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/meta-tags.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/national-party-account-disbursements-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/national-party-account-receipts-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/operating-expenditures-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/pac-party-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/party-coordinated-expenditures-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/receipts-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/reports-filter.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/warnings.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/partials/widgets/contributions-by-state.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/pres-finance-map.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/raising-bythenumbers.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/search-results.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/spending-bythenumbers.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/widgets/aggregate-totals.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/widgets/contributions-by-state.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templates/widgets/pres-finance-map.jinja", "downloaded_repos/fecgov_fec-cms/fec/data/templatetags/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/data/templatetags/filters.py", "downloaded_repos/fecgov_fec-cms/fec/data/urls.py", "downloaded_repos/fecgov_fec-cms/fec/data/utils.py", "downloaded_repos/fecgov_fec-cms/fec/data/views.py", "downloaded_repos/fecgov_fec-cms/fec/data/views_datatables.py", "downloaded_repos/fecgov_fec-cms/fec/extend_admin/wagtailadmin/base.html", "downloaded_repos/fecgov_fec-cms/fec/extend_admin/wagtailadmin/widgets/chooser.html", "downloaded_repos/fecgov_fec-cms/fec/fec/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/fec/constants.py", "downloaded_repos/fecgov_fec-cms/fec/fec/context.py", "downloaded_repos/fecgov_fec-cms/fec/fec/draftail/anchor.py", "downloaded_repos/fecgov_fec-cms/fec/fec/draftail/glossary.py", "downloaded_repos/fecgov_fec-cms/fec/fec/draftail/sansserif.py", "downloaded_repos/fecgov_fec-cms/fec/fec/forms.py", "downloaded_repos/fecgov_fec-cms/fec/fec/middleware.py", "downloaded_repos/fecgov_fec-cms/fec/fec/settings/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/fec/settings/base.py", "downloaded_repos/fecgov_fec-cms/fec/fec/settings/dev.py", "downloaded_repos/fecgov_fec-cms/fec/fec/settings/env.py", "downloaded_repos/fecgov_fec-cms/fec/fec/settings/production.py", "downloaded_repos/fecgov_fec-cms/fec/fec/static/css/customize-editor.css", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bold.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bold.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bold.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bold.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bolditalic.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bolditalic.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bolditalic.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-bolditalic.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-italic.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-italic.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-italic.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-italic.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-regular.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-regular.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-regular.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/fec-currencymono-regular.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bold.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bold.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bold.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bold.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bolditalic.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bolditalic.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bolditalic.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-bolditalic.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-italic.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-italic.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-italic.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-italic.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-regular.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-regular.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-regular.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/gandhiserif-regular.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-bold.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-bold.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-bold.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-bold.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-regular.eot", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-regular.ttf", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-regular.woff", "downloaded_repos/fecgov_fec-cms/fec/fec/static/fonts/karla-regular.woff2", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/icon-template.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/all-files.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-down-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-down-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-down.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-left-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-left-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-left.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-right-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-right-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-right.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-target.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-up-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-up-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/arrow-up.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/bang-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/bang.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/bluesky-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/book.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/bulk.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/cal-add.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/calculator.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/calendar.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/candidate.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/cfr.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/check-circle-outline.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/check-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/check.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/checklist-2.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/checklist.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/clipboard-checklist.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/clock-reverse.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/committee-audit.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/committee.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/compass.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/dash.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/data-flag.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/decree.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/direction-sign.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/disbursement.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/document.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/download.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/election.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/example-document.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/exclamation-bubble.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/facebook-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/fax.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/file-box.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/filter.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/financial-document.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/folder-with-person.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/folder.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/github-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/graph-horizontal-ordered.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/graph-horizontal-unordered.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/graph.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/grid.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/hand-envelope.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/hand-money.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/individual-contributions.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/info-circle-outline.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/info-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/list.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/magnifying-glass-arrow.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/magnifying-glass.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/mallet.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/map-pin.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/map-us-states.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/menu.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/minus-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/moving-envelope-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/moving-envelope.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/notebook.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/org-chart.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/papers.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/person-location.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/piggy-bank.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/play-button.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/plus-circle 2.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/plus-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/plus.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/point-down-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/pop-up.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/question-bubble.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/question-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/scale.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/share.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/shield-scales.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/shield.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/signal.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/sort-down.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/sort-up.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/sort.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/speaker.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/stamp-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/star.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/subsection-doc.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/table.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/telephone-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/timer.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/training.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/twitter-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/two-candidates.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/updates.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/usc.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/x-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/x-twitter-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/x.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/input/youtube-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/all-files.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-down-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-down-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-down.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-left-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-left-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-left.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-right-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-right-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-right.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-target.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-up-border.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-up-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/arrow-up.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/bang-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/bang.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/bluesky-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/book.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/bulk.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/cal-add.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/calculator.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/calendar.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/candidate.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/cfr.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/check-circle-outline.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/check-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/check.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/checklist-2.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/checklist.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/clipboard-checklist.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/clock-reverse.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/committee-audit.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/committee.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/compass.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/dash.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/data-flag.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/decree.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/direction-sign.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/disbursement.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/document.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/download.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/election.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/example-document.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/exclamation-bubble.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/facebook-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/fax.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/file-box.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/filter.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/financial-document.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/folder-with-person.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/folder.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/github-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/graph-horizontal-ordered.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/graph-horizontal-unordered.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/graph.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/grid.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/hand-envelope.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/hand-money.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/individual-contributions.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/info-circle-outline.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/info-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/list.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/magnifying-glass-arrow.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/magnifying-glass.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/mallet.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/map-pin.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/map-us-states.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/menu.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/minus-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/moving-envelope-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/moving-envelope.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/notebook.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/org-chart.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/papers.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/person-location.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/piggy-bank.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/play-button.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/plus-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/plus.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/point-down-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/pop-up.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/question-bubble.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/question-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/scale.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/share.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/shield-scales.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/shield.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/signal.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/sort-down.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/sort-up.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/sort.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/speaker.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/stamp-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/star.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/subsection-doc.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/table.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/telephone-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/timer.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/training.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/twitter-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/two-candidates.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/updates.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/usc.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/x-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/x-twitter-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/x.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/icons/output/youtube-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/Homepage-hero-image-flag.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/TV-<PERSON>-Wall-Only-850.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/angle-arrow-down-primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/angle-arrow-down-primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/angle-arrow-up-primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/angle-arrow-up-primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/example-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/data/favicon-120x120.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/data/favicon-152x152.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/data/favicon-167x167.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/data/favicon-180x180.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/data/favicon-192x192.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/favicon-16x16.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/favicon-32x32.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/favicon-full.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/favicon-small.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/favicon.ico", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon-120x120.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon-152x152.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon-167x167.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon-180x180.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon-192x192.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon-200x200.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon-48x48.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/general/favicon.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/help/favicon-120x120.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/help/favicon-152x152.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/help/favicon-167x167.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/help/favicon-180x180.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/help/favicon-192x192.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/legal/favicon-120x120.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/legal/favicon-152x152.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/legal/favicon-167x167.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/legal/favicon-180x180.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/favicon/legal/favicon-192x192.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--candidate-support.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--compare-candidates.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--contributions-narrow.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--contributions.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--data.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--dates.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--filing.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--forms.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--legal.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--reporting.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/feature--training.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/fec-office.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/financial-document-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/headshot--goodman.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/headshot--hunter.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/headshot--no-photo.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/headshot--petersen.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/headshot--walther.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/headshot--weintraub.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/hero-about.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/hero-data.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/hero-home.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/hero-legal.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/hero-oig.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/hero-press.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/hero-registration.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-bluesky--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-bluesky--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-calendar--secondary-contrast.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-calendar-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--calendar.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--calendar.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--data--gray-lightest.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--data.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--data.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--legal--gray-lightest.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--legal.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--legal.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--regreport--gray-lightest.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--regreport.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-complex--regreport.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-document--secondary-contrast.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-document-circle.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-elections--neutral.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-elections--primary-contrast.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-elections--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-elections--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-email--inverse.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-email--inverse.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-email--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-email--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-github--inverse.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-github--inverse.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-github--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-github--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-graph--primary-contrast.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-mail--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-mail--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-map--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-overviews--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-phone--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-phone--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-table--neutral.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-table--primary-contrast.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-table--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-twitter--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-twitter--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-x-twitter--primary.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/i-x-twitter--primary.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/icon-dot-gov.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/icon-https.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/loading--primary.gif", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/loading-ellipsis-gray.gif", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/loading-ellipsis.gif", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/loading.gif", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/logo-oig-color.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/map-chart-icon.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/map-election-search-default.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/map-us-states-primary-contrast.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/new-tv-bkgd-wide.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/print-logo.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/reaction-confusing.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/reaction-informative.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/reaction-not-interested.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/seal--cropped.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/seal--cropped.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/seal--inverse.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/seal--inverse.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/seal.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/seal.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-data.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-help.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-legal.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-logo.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-microphone.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-microphones.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-pen.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/social/fec-seal.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--ao-brochure.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--ao-statistics.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--audio.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--candidates.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--captions.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--citizens-guide-brochure.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--enforcement-process.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--feca.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--filing-a-complaint.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--nonconnected.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--orgs.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--parties.jpg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--pdf-guide.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/thumbnail--video.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/tooltip-point.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/us_flag_small.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/wordmark.png", "downloaded_repos/fecgov_fec-cms/fec/fec/static/img/wordmark.svg", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/create_districts_topo.sh", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/districts.json", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/merge_shape_files.py", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/notes.md", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/state.json", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/stateDistricts.json", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/terms.json", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/us-states-10m.json", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data-init.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/draftail/Anchor.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/draftail/App.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/draftail/Glossary.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/draftail/SansSerif.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/draftail/components/Modal.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/draftail/utils.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/global.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/init.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal-search-ao.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/accessibility.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/analytics.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/audit-category-sub-category.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/audit_tags.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/calc-admin-fines-logic.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/calc-admin-fines-modal.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/calc-admin-fines.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/calendar-helpers.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/calendar-list-view.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/calendar-tooltip.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/calendar.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/column-helpers.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/columns.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/container-queries.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/cycle-select.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/data-map.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/decoders.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/download.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/dropdowns.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/election-form.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/election-lookup.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/election-map.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/election-search.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/election-summary.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/election-utils.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/events.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/feedback.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filings.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/checkbox-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/date-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/election-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/filter-base.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/filter-control.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/filter-panel.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/filter-set.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/filter-tags.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/filter-typeahead.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/multi-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/range-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/select-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/text-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/toggle-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/typeahead-filter.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters/validate-date-filters.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/filters-event.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/fips.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/form-nav.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/helpers.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/home-events.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/keyword-modal.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/line-chart-committees.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/line-chart.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/listeners.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/load-recaptcha.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/maps-event.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/maps.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/other-spending-totals.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/party-money-bars.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/performance.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/search.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/site-nav.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/skip-nav.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/statistical-summary-archive.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/table-columns.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/table-panels.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/table-switcher.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/tables.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/toc.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/toggle.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/top-entities.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/top-list.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/typeahead.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/urls.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/bythenumbers.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/calendar-page.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/candidate-single.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/committee-single.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/contact-form.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/data-browse-data.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/data-landing.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-allocated-federal-nonfederal-disbursements.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-audit.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-candidates-office.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-candidates.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-committees.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-communication-costs.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-debts.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-disbursements.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-electioneering-communications.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-filings.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-independent-expenditures.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-individual-contributions.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-loans.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-national-party-account-disbursements.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-national-party-account-receipts.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-operating-expenditures.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-pac-party.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-party-coordinated-expenditures.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-receipts.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/datatable-reports.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/election-lookup.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/elections.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/home.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/house-senate-overview-across-time.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/house-senate-overview-summary.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/house-senate-overview-totals.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/legal.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reaction-box.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/statistical-summary-archive.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/statistical-summary.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/allocated-federal-nonfederal-disbursements.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/audit_tags.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/calendar/details.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/calendar/download.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/calendar/events.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/calendar/listToggles.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/calendar/subscribe.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/candidateStateMap.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/candidates.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/committees.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/communication-costs.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/comparison.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/coverageEndDate.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/debts.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/disbursements.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/districts.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/download/container.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/download/item.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/election-cycles.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/electionCycle.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/electionCycles.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/electionNoResults.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/electionOffices.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/electionResult.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/electionZipWarning.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/electioneering-communications.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/feedback.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/homepage/events-and-deadlines.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/independent-expenditures.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/loans.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/national-party-account-disbursements.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/national-party-account-receipts.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/pac-party.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/party-coordinated-expenditures.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/receipts.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/candidate.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/ie-only.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/pac.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/reports/reportType.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/tables/exportWidget.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/tables/noData.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/tables/title.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/top-entity-row.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/templates/upcomingPresidential.hbs", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals-box.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/aggregate-totals.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/contributions-by-state-box.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/contributions-by-state.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/widget-vars.js", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/_fonts.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/_functions.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/_global-common.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/_global.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/_grid.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/_icon-variables.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/_variables.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/base.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/calendar.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/common.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_accordions.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_agendas.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_articles.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_breadcrumbs.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_breakdowns.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_buttons.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_calc-admin-fines.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_calendar.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_callouts.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_candidate-page.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_cards.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_charts.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_committee-snapshot.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_contact-form.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_contact-items.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_cycle-select.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_data-container.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_data-landing-callouts.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_datatable-panel.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_datatables.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_date-grid.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_documents.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_downloads.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_dropdowns.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_entity-header.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_examples.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_fec-offices.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_fec-org-chart.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_feedback.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_figures.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_filters.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_footer.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_form-styles.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_glossary.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_headings.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_hero.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_icon-headings.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_icons.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_legal-search.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_list-styles.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_maps.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_mega-menu.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_messages.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_modals.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_nav.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_options.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_overlay.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_overviews.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_page-headers.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_pagination.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_posts.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_reaction-boxes.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_responsive-object.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_results-info.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_richtext.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_search-bar.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_search-controls.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_search-results.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_side-nav.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_sidebar.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_site-header.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_table-styles.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_tags.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_toggles.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_tooltips.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/components/_type-styles.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/data-landing.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/datatables.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elections.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elements/_elements.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elements/_forms.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elements/_images.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elements/_links.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elements/_lists.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elements/_tables.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/elements/_typography.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/entity.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/home.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/layout/_grid.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/layout/_layout.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/layout/_slabs.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/legal-common.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/legal.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/mixins/_icon-mixins.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/mixins/_type-mixins.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/mixins/_utilities.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/widgets/aggregate-totals.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/widgets/contributions-by-state.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/widgets/house-senate-overview.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/widgets/party-money-bars.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/widgets/pres-finance-map.scss", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500-status.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/code.json", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/data.json", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/long_page.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-groups.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/body-blocks.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/breadcrumbs.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/disclaimer.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/footer-navigation.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/glossary.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/google-tag-manager-noscript.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/google-tag-manager-script.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preconnects.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preloads.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/navigation/nav-about.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/navigation/nav-data.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/navigation/nav-help.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/navigation/nav-legal.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/navigation/navigation.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/search-hero.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/usa-banner.html", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/robots.txt", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/robots_prod.txt", "downloaded_repos/fecgov_fec-cms/fec/fec/templates/uaa_client/login_error.html", "downloaded_repos/fecgov_fec-cms/fec/fec/urls.py", "downloaded_repos/fecgov_fec-cms/fec/fec/utils.py", "downloaded_repos/fecgov_fec-cms/fec/fec/wagtail_hooks.py", "downloaded_repos/fecgov_fec-cms/fec/fec/wsgi.py", "downloaded_repos/fecgov_fec-cms/fec/gulpfile.js", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0001_initial.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0002_create_homepage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0003_auto_20150819_0342.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0004_checklistpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0005_auto_20150819_0517.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0006_auto_20150819_0546.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0007_auto_20150901_0442.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0008_auto_20150917_1906.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0009_contactpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0010_calendarpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0011_ssfchecklistpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0012_partychecklistpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0013_auto_20160427_2133.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0014_nonconnectedchecklistpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0015_custompage_content.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0016_auto_20160714_2359.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0017_auto_20160823_1504.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0018_record_digest_press_release.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0019_auto_20160907_2152.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0019_auto_20160908_2113.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0020_auto_20160909_0139.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0021_merge.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0022_auto_20160921_2230.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0022_auto_20160923_0004.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0023_remove_presslandingpage_feed_intro.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0024_presslandingpage_feed_intro.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0025_auto_20161006_1415.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0025_merge.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0026_auto_20161004_1620.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0027_auto_20161004_1620.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0028_merge.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0029_auto_20161107_0304.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0030_pressreleasepage_formatted_title.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0031_auto_20161201_2123.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0032_auto_20161201_2124.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0033_auto_20161201_2126.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0034_auto_20161221_1920.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0034_pressreleasepage_homepage_pin.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0035_pressreleasepage_homepage_hide.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0035_recordpage_monthly_issue.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0036_auto_20161216_0202.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0036_auto_20161221_2007.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0037_auto_20161216_0222.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0037_recordpage_monthly_issue_url.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0038_aboutlandingpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0038_merge_20161223_1820.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0038_resourcepage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0039_aboutlandingpage_hero.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0039_legalresourceslanding.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0040_aboutlandingpage_mission_intro.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0040_auto_20161228_2356.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0041_auto_20161227_1524.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0041_enforcementpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0042_auto_20161227_1526.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0042_auto_20161229_2001.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0043_auto_20161227_1538.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0043_merge_20161230_0122.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0044_aboutlandingpage_option_blocks.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0044_auto_20170105_0006.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0045_merge_20170105_0036.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0046_auto_20170105_0038.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0047_auto_20170105_0044.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0048_auto_20170105_0049.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0049_auto_20170105_0052.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0050_auto_20170105_0055.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0051_commissionerpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0052_auto_20170111_2205.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0053_serviceslandingpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0054_auto_20170118_2104.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0055_auto_20170123_2232.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0055_tipsfortreasurerspage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0056_merge_20170127_1735.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0057_auto_20170202_0237.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0058_auto_20170210_2136.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0058_documentfeedpage_documentpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0059_auto_20170209_0151.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0060_auto_20170209_2030.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0061_merge_20170210_2230.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0062_auto_20170214_2207.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0063_auto_20170214_2207.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0064_auto_20170215_2153.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0065_auto_20170221_0345.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0066_remove_documentpage_file_name.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0067_agendapage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0067_documentpage_year_only.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0068_merge_20170310_0002.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0069_auto_20170316_0006.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0069_auto_20170318_0005.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0070_merge_20170322_1940.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0071_agendapage_imported_html.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0072_auto_20170324_0544.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0073_auto_20170329_2112.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0073_auto_20170330_1840.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0073_auto_20170330_1915.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0074_auto_20170330_2222.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0074_merge_20170330_2316.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0075_auto_20170330_2354.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0075_merge_20170331_0608.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0076_merge_20170331_1633.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0077_auto_20170331_1711.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0078_auto_20170404_2137.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0079_auto_20170414_0021.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0079_auto_20170420_0238.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0080_auto_20170420_1702.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0081_auto_20170501_2216.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0081_genericupdate.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0082_merge_20170504_2212.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0082_merge_20170505_0025.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0083_auto_20170504_2215.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0083_auto_20170506_0059.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0084_merge_20170509_0021.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0085_auto_20170510_1928.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0086_auto_20170519_1726.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0087_meetingpage_homepage_hide.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0088_auto_20170802_2329.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0088_folder.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0089_auto_20170810_2215.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0090_auto_20170811_1840.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0091_merge_20170811_2035.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0091_merge_20170814_0055.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0092_auto_20170814_0109.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0093_merge_20170814_2229.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0094_auto_20170829_1640.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0095_auto_20170831_2013.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0095_meetingpage_sunshine_act_doc_upld.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0096_merge_20170907_2031.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0097_auto_20170925_1952.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0097_auto_20170929_2329.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0098_auto_20170930_0054.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0099_merge_20171109_1546.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0100_auto_20180126_1444.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0101_auto_20180531_1615.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0102_auto_20180718_0132.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0103_auto_20180726_1409.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0104_auto_20181202_1431.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0105_auto_20191003_0006.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0106_auto_20191101_1759.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0107_auto_20191213_1031.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0108_fullwidthpage_citations.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0109_auto_20200219_1316.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0110_auto_20200930_2200.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0111_oiglandingpage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0112_auto_20210112_1100.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0113_auto_20210316_1226.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0114_auto_20210430_1346.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0115_auto_20210506_2326.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0116_auto_20210818_1041.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0117_auto_20210907_1026.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0118_auto_20211007_1004.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0119_auto_20211013_1228.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0120_auto_20211201_1130.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0121_auto_20211206_1255.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0122_auto_20220302_2114.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0122_auto_20220303_1357.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0123_merge_20220308_1116.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0124_auto_20220706_0036.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0125_officepage.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0126_auto_20230123_1608.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0127_auto_20230123_1927.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0128_merge_0125_officepage_0126_auto_20230123_1927.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0129_reportingdatestable.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0130_alter_reportingdatestable_reporting_dates_table.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0131_auto_20230905_2038.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0132_auto_20231130_1233.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0133_alter_recordpagetag_tag.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0134_embedsnippet_banner_icon_alter_resourcepage_intro.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0135_alter_resourcepage_sections.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0136_alter_embedsnippet_banner_icon_and_more.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0137_commissionerpage_picture_download.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0138_alter_reportingdatestable_reporting_dates_table.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0139_commissionerpage_commissioner_bluesky_and_more.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0140_alter_embedsnippet_banner_icon_and_more.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/adr_search.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/af_search.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/audit.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/careers.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/commissioners.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/contact-info.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/custom_table.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/disabled-page-links.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/document-list.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/embed-info-message.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/embed-table.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-forms.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-image.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-paragraph.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/mur_search.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/page-links.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/related-media.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/reporting-dates-table-block.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/reporting-example-cards.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-aside.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-documents.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/audit-search.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/current-commissioners.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/document-feed.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/document.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/elections-lookup.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-news.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/legal-keyword-modal.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/legal-news.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/oig-most-recent.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/press-feed.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/section-nav.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/tips-for-treasurers.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/purgecss-homepage/banners.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/purgecss-homepage/commissioners.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/purgecss-homepage/full.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/purgecss-homepage/hero.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/purgecss-homepage/navs.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/purgecss-homepage/readme.txt", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/purgecss-homepage/toggled.html", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/audit_search.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/author_groups.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/commissioners.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/document_feed.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/elections_lookup.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/filters.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home_page.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/legal_news.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/oig_most_recent.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/open_jobs.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/tips_for_treasurers.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/top_entities.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/updates.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/link_reroute.py", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "downloaded_repos/fecgov_fec-cms/fec/legal/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/legal/admin.py", "downloaded_repos/fecgov_fec-cms/fec/legal/apps.py", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/layouts/legal-doc-landing.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/layouts/legal-doc-search-results.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-admin_fine.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-adr.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-advisory-opinion.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-advisory-opinions-landing.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-archived-mur.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-current-mur.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-search-results-adrs.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-search-results-advisory_opinions.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-search-results-afs.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-search-results-murs.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-search-results-regulations.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-search-results-statutes.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-search-results.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/legal-statutes-landing.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/macros/legal.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-disclaimer.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-keyword-modal.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-pagination.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-regulations-pagination.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-search-results-adrs.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-search-results-advisory-opinion.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-search-results-afs.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-search-results-mur.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-search-results-regulation.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/templates/partials/legal-search-results-statute.jinja", "downloaded_repos/fecgov_fec-cms/fec/legal/tests.py", "downloaded_repos/fecgov_fec-cms/fec/legal/urls.py", "downloaded_repos/fecgov_fec-cms/fec/legal/views.py", "downloaded_repos/fecgov_fec-cms/fec/manage.py", "downloaded_repos/fecgov_fec-cms/fec/search/__init__.py", "downloaded_repos/fecgov_fec-cms/fec/search/management/data/sitemap_html.xml", "downloaded_repos/fecgov_fec-cms/fec/search/management/data/sitemap_pdf.xml", "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/search.html", "downloaded_repos/fecgov_fec-cms/fec/search/views.py", "downloaded_repos/fecgov_fec-cms/fec/webpack.config.cjs", "downloaded_repos/fecgov_fec-cms/karma.conf.js", "downloaded_repos/fecgov_fec-cms/locust_helpers/helpers.py", "downloaded_repos/fecgov_fec-cms/locust_helpers/urls.py", "downloaded_repos/fecgov_fec-cms/locustfile.py", "downloaded_repos/fecgov_fec-cms/manifest_dev.yml", "downloaded_repos/fecgov_fec-cms/manifest_feature.yml", "downloaded_repos/fecgov_fec-cms/manifest_prod.yml", "downloaded_repos/fecgov_fec-cms/manifest_stage.yml", "downloaded_repos/fecgov_fec-cms/package-lock.json", "downloaded_repos/fecgov_fec-cms/package.json", "downloaded_repos/fecgov_fec-cms/pytest.ini", "downloaded_repos/fecgov_fec-cms/requirements-dev.txt", "downloaded_repos/fecgov_fec-cms/requirements.txt", "downloaded_repos/fecgov_fec-cms/runtime.txt", "downloaded_repos/fecgov_fec-cms/tasks.py"], "skipped": [{"path": "downloaded_repos/fecgov_fec-cms/fec/data/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/data/tests/test_candidate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/data/tests/test_committee.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/data/tests/test_datatables.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/data/tests/test_legal_search.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/data/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/vendor/beautify-html.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/vendor/jquery.htmlClean.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/vendor/tablist.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/_bourbon-deprecated-upcoming.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/_bourbon.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_border-color.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_border-radius.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_border-style.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_border-width.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_buttons.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_clearfix.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_ellipsis.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_font-stacks.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_hide-text.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_margin.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_padding.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_position.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_prefixer.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_retina-image.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_size.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_text-inputs.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_timing-functions.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_triangle.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/addons/_word-wrap.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_animation.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_appearance.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_backface-visibility.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_background-image.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_background.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_border-image.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_calc.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_columns.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_filter.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_flex-box.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_font-face.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_font-feature-settings.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_hidpi-media-query.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_hyphens.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_image-rendering.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_keyframes.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_linear-gradient.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_perspective.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_placeholder.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_radial-gradient.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_selection.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_text-decoration.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_transform.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_transition.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/css3/_user-select.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_assign-inputs.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_contains-falsy.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_contains.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_is-length.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_is-light.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_is-number.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_is-size.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_modular-scale.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_px-to-em.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_px-to-rem.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_shade.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_strip-units.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_tint.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_transition-property-name.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/functions/_unpack.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_convert-units.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_directional-values.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_font-source-declaration.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_gradient-positions-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_linear-angle-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_linear-gradient-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_linear-positions-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_linear-side-corner-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_radial-arg-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_radial-gradient-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_radial-positions-parser.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_render-gradients.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_shape-size-stripper.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/helpers/_str-to-num.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/settings/_asset-pipeline.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/settings/_prefixer.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/bourbon/settings/_px-to-em.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/_neat-helpers.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/_neat.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/functions/_new-breakpoint.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/functions/_private.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_box-sizing.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_direction-context.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_display-context.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_fill-parent.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_media.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_omega.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_outer-container.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_pad.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_private.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_row.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_shift.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_span-columns.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_to-deprecate.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/grid/_visual-grid.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/settings/_disable-warnings.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/settings/_grid.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/static/scss/vendor/neat/settings/_visual-grid.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/404.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500-status.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/500.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/home_base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-groups.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/author-list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/breadcrumbs.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/env-banner.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meeting.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preconnects.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags-preloads.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/meta-tags.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/templates/partials/update.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/.eslintrc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/fixtures/context.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/fixtures/election-offices.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/fixtures/house-results.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/analyst-lookup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/analytics.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/babel.config.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/calc-admin-fines.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/calendar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/checkbox-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/column-helpers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/contact-form.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/cycle-select.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/date-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/download.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/draftail/anchor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/draftail/glossary.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/draftail/sansserif.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/draftail/setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/dropdowns.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/election-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/election-search.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/election-utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/feedback.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/filter-base.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/filter-panel.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/filter-set.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/filter-tags.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/helpers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/line-chart.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/listeners.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/multi-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/radform-validate.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/select-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/site-nav.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/skip-nav.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/statistical-summary-archive.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/table-switcher.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/tables.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/tablist.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/text-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/toc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/toggle-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/top-entities-breakdown.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/typeahead-filter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/js/urls.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/test_api_key.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/test_form.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/test_robots.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/fec/tests/test_static_files.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/adr_search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/af_search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/button.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/document-list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/example-forms.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/mur_search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/page-links.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/related-media.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/reporting-example-cards.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-aside.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/section-documents.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document-list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/blocks/simple-document.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/guides.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/services_landing_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/legal_resources_landing.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/digest_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/press_release_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/record_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/home/<USER>/tips_for_treasurers.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/audit-search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/commissioner.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/document.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/draft-home-page-banner-announcement.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-banner-announcement.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/home-page-news.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/jobs.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/legal-news.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/oig-most-recent.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/raising-spending.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/section-nav.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/partials/tips-for-treasurers.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/test_filters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/test_pages.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/test_usajobs.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/policy_guidance_search_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/templates/search/search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fecgov_fec-cms/fec/search/tests/test_views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8483998775482178, "profiling_times": {"config_time": 6.268617630004883, "core_time": 14.644923686981201, "ignores_time": 0.0028705596923828125, "total_time": 20.91768789291382}, "parsing_time": {"total_time": 16.567947387695312, "per_file_time": {"mean": 0.03555353516672814, "std_dev": 0.0256331054008491}, "very_slow_stats": {"time_ratio": 0.3931945123029443, "count_ratio": 0.017167381974248927}, "very_slow_files": [{"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/helpers.js", "ftime": 0.3027019500732422}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal-search-ao.js", "ftime": 0.3179779052734375}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/candidate-single.js", "ftime": 0.3228340148925781}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/data-map.js", "ftime": 0.378065824508667}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/columns.js", "ftime": 0.4539921283721924}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/pages/reporting-dates-tables.js", "ftime": 0.5665171146392822}, {"fpath": "downloaded_repos/fecgov_fec-cms/package-lock.json", "ftime": 1.156987190246582}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/districts.json", "ftime": 3.015349864959717}]}, "scanning_time": {"total_time": 120.0439727306366, "per_file_time": {"mean": 0.04022921338158067, "std_dev": 0.07882046620001228}, "very_slow_stats": {"time_ratio": 0.41837269948158884, "count_ratio": 0.005026809651474531}, "very_slow_files": [{"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "ftime": 2.351875066757202}, {"fpath": "downloaded_repos/fecgov_fec-cms/package-lock.json", "ftime": 2.728393077850342}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/legal-search-ao.js", "ftime": 2.868884801864624}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/contributions-by-state-box.js", "ftime": 2.9505090713500977}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "ftime": 4.036661863327026}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "ftime": 4.271775007247925}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/legal/views.py", "ftime": 4.386393070220947}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/data/districts.json", "ftime": 4.688112020492554}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/tables.js", "ftime": 6.129901170730591}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/data/views.py", "ftime": 6.284764051437378}]}, "matching_time": {"total_time": 57.79092788696289, "per_file_and_rule_time": {"mean": 0.02318127873524385, "std_dev": 0.00908935362908994}, "very_slow_stats": {"time_ratio": 0.6365249418381872, "count_ratio": 0.04853590052146009}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/fecgov_fec-cms/fec/data/views.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.6229691505432129}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.6606550216674805}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/widgets/pres-finance-map-box.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.7287430763244629}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/fec/static/js/modules/tables.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 0.7485990524291992}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/data/api_caller.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.8102707862854004}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.8963491916656494}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/data/views.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.9912910461425781}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/legal/views.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 1.1841719150543213}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/legal/views.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 1.600269079208374}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/data/views.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 2.616455078125}]}, "tainting_time": {"total_time": 18.24432873725891, "per_def_and_rule_time": {"mean": 0.0038120202125488754, "std_dev": 0.00024169877052911866}, "very_slow_stats": {"time_ratio": 0.3273991080843782, "count_ratio": 0.01211867948182198}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0073_auto_20170330_1840.py", "fline": 12, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.14652109146118164}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/open_jobs.py", "fline": 20, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.16335296630859375}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0104_auto_20181202_1431.py", "fline": 15, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.23809313774108887}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0105_auto_20191003_0006.py", "fline": 16, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.24865317344665527}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0107_auto_20191213_1031.py", "fline": 12, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.269428014755249}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0132_auto_20231130_1233.py", "fline": 13, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.30057191848754883}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0127_auto_20230123_1927.py", "fline": 10, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.3083357810974121}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/data/constants.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.323030948638916}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0112_auto_20210112_1100.py", "fline": 13, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.33212804794311523}, {"fpath": "downloaded_repos/fecgov_fec-cms/fec/home/<USER>/0131_auto_20230905_2038.py", "fline": 13, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.3893258571624756}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1120466304}, "engine_requested": "OSS", "skipped_rules": []}