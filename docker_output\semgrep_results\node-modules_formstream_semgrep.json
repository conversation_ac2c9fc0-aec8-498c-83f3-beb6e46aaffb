{"version": "1.130.0", "results": [{"check_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "path": "downloaded_repos/node-modules_formstream/example/upload.js", "start": {"line": 17, "col": 11, "offset": 419}, "end": {"line": 17, "col": 15, "offset": 423}, "extra": {"message": "Checks for any usage of http servers instead of https servers. Encourages the usage of https protocol instead of http, which does not have TLS and is therefore unencrypted. Using http can lead to man-in-the-middle attacks in which the attacker is able to read sensitive information.", "metadata": {"likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": ["A02:2021 - Cryptographic Failures", "A03:2017 - Sensitive Data Exposure"], "references": ["https://nodejs.org/api/http.html#http_class_http_agent", "https://groups.google.com/g/rubyonrails-security/c/NCCsca7TEtY"], "subcategory": ["audit"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "shortlink": "https://sg.run/x1zL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/node-modules_formstream/.github/workflows/ci.yml", "downloaded_repos/node-modules_formstream/.github/workflows/release.yml", "downloaded_repos/node-modules_formstream/.gitignore", "downloaded_repos/node-modules_formstream/.jshintignore", "downloaded_repos/node-modules_formstream/.jshintrc", "downloaded_repos/node-modules_formstream/CHANGELOG.md", "downloaded_repos/node-modules_formstream/LICENSE", "downloaded_repos/node-modules_formstream/README.md", "downloaded_repos/node-modules_formstream/example/upload.js", "downloaded_repos/node-modules_formstream/lib/formstream.js", "downloaded_repos/node-modules_formstream/package.json", "downloaded_repos/node-modules_formstream/types/formstream.d.ts", "downloaded_repos/node-modules_formstream/types/formstream.test-d.ts"], "skipped": [{"path": "downloaded_repos/node-modules_formstream/test/fixtures/foo.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/node-modules_formstream/test/fixtures/formstream.tgz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/node-modules_formstream/test/fixtures/logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/node-modules_formstream/test/fixtures/server.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/node-modules_formstream/test/fixtures/test.emf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/node-modules_formstream/test/formstream.test.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7728939056396484, "profiling_times": {"config_time": 7.857982397079468, "core_time": 2.6127817630767822, "ignores_time": 0.0018739700317382812, "total_time": 10.47332239151001}, "parsing_time": {"total_time": 0.1273047924041748, "per_file_time": {"mean": 0.018186398914882114, "std_dev": 0.00019790362394740774}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.510260820388794, "per_file_time": {"mean": 0.015462449102690722, "std_dev": 0.0019486521789798504}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.18922710418701172, "per_file_and_rule_time": {"mean": 0.0017521028165464049, "std_dev": 2.163507161216442e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.05841827392578125, "per_def_and_rule_time": {"mean": 0.0009736378987630209, "std_dev": 3.2211997939965742e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}