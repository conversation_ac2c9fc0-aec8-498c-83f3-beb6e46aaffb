{"version": "1.130.0", "results": [{"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/springload_madewithwagtail/core/frontend/svg/template/preview.hbs", "start": {"line": 14, "col": 4, "offset": 411}, "end": {"line": 14, "col": 20, "offset": 427}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/springload_madewithwagtail/core/frontend/svg/template/preview.hbs", "start": {"line": 15, "col": 4, "offset": 431}, "end": {"line": 15, "col": 19, "offset": 446}, "extra": {"message": "Detected an explicit unescape in a Mustache template, using triple braces '{{{...}}}' or ampersand '&'. If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. If you must do this, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://github.com/janl/mustache.js/#variables", "https://ractive.js.org/v0.x/0.7/mustaches#variables"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.mustache.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/Lwx9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "start": {"line": 63, "col": 1, "offset": 2531}, "end": {"line": 63, "col": 133, "offset": 2663}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/springload_madewithwagtail/docker-compose.yml", "start": {"line": 37, "col": 5, "offset": 1026}, "end": {"line": 37, "col": 13, "offset": 1034}, "extra": {"message": "Service 'database' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/springload_madewithwagtail/docker-compose.yml", "start": {"line": 37, "col": 5, "offset": 1026}, "end": {"line": 37, "col": 13, "offset": 1034}, "extra": {"message": "Service 'database' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 21, "col": 1, "offset": 392}, "end": {"line": 21, "col": 54, "offset": 445}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"yarn\", \"start\", \"--prefix\", \"/madewithwagtail\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 74, "col": 1, "offset": 2059}, "end": {"line": 74, "col": 136, "offset": 2194}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"/sbin/ssm-parent\", \"run\", \"-e\", \"-p\", \"/$PROJECT/common/\", \"-p\", \"/$PROJECT/$ENVIRONMENT/\", \"-r\",  \"--\", \"su-exec\", \"www\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 75, "col": 1, "offset": 2195}, "end": {"line": 75, "col": 108, "offset": 2302}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/usr/local/bin/gunicorn\", \"--config\", \"/madewithwagtail/docker/gunicorn.py\", \"madewithwagtail.wsgi\" ]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 90, "col": 1, "offset": 2729}, "end": {"line": 90, "col": 62, "offset": 2790}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"/usr/local/bin/wait-for\", \"database:5432\", \"--\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 91, "col": 1, "offset": 2791}, "end": {"line": 91, "col": 120, "offset": 2910}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/usr/local/bin/gunicorn\", \"--config\", \"/madewithwagtail/docker/gunicorn.py\", \"--reload\", \"madewithwagtail.wsgi\" ]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 98, "col": 1, "offset": 3093}, "end": {"line": 98, "col": 44, "offset": 3136}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/bin/sh\", \"/madewithwagtail/test.sh\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/aws.py", "start": {"line": 12, "col": 13, "offset": 252}, "end": {"line": 12, "col": 65, "offset": 304}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "path": "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/restframework.py", "start": {"line": 3, "col": 1, "offset": 18}, "end": {"line": 3, "col": 15, "offset": 32}, "extra": {"message": "Django REST framework configuration is missing default rate- limiting options. This could inadvertently allow resource starvation or Denial of Service (DoS) attacks. Add 'DEFAULT_THROTTLE_CLASSES' and 'DEFAULT_THROTTLE_RATES' to add rate-limiting to your application.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-770: Allocation of Resources Without Limits or Throttling"], "references": ["https://www.django-rest-framework.org/api-guide/throttling/#setting-the-throttling-policy"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "shortlink": "https://sg.run/vzBY"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/includes/macros.html:1:\n Failure: not a program", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/macros.html"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 44, "col": 61, "offset": 916}, "end": {"line": 44, "col": 64, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 44, "col": 101, "offset": 916}, "end": {"line": 44, "col": 104, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 47, "col": 20, "offset": 916}, "end": {"line": 47, "col": 23, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 47, "col": 60, "offset": 916}, "end": {"line": 47, "col": 63, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 48, "col": 20, "offset": 916}, "end": {"line": 48, "col": 23, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 48, "col": 60, "offset": 916}, "end": {"line": 48, "col": 63, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 50, "col": 20, "offset": 916}, "end": {"line": 50, "col": 23, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 50, "col": 60, "offset": 916}, "end": {"line": 50, "col": 63, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 51, "col": 20, "offset": 916}, "end": {"line": 51, "col": 23, "offset": 919}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 51, "col": 60, "offset": 916}, "end": {"line": 51, "col": 63, "offset": 919}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml:44:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 44, "col": 61, "offset": 916}, "end": {"line": 44, "col": 64, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 44, "col": 101, "offset": 916}, "end": {"line": 44, "col": 104, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 47, "col": 20, "offset": 916}, "end": {"line": 47, "col": 23, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 47, "col": 60, "offset": 916}, "end": {"line": 47, "col": 63, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 48, "col": 20, "offset": 916}, "end": {"line": 48, "col": 23, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 48, "col": 60, "offset": 916}, "end": {"line": 48, "col": 63, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 50, "col": 20, "offset": 916}, "end": {"line": 50, "col": 23, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 50, "col": 60, "offset": 916}, "end": {"line": 50, "col": 63, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 51, "col": 20, "offset": 916}, "end": {"line": 51, "col": 23, "offset": 919}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 51, "col": 60, "offset": 916}, "end": {"line": 51, "col": 63, "offset": 919}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 99, "col": 17, "offset": 3745}, "end": {"line": 99, "col": 20, "offset": 3748}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 106, "col": 63, "offset": 3745}, "end": {"line": 106, "col": 66, "offset": 3748}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 111, "col": 5, "offset": 3745}, "end": {"line": 111, "col": 8, "offset": 3748}}, {"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 111, "col": 33, "offset": 3745}, "end": {"line": 111, "col": 36, "offset": 3748}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml:99:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 99, "col": 17, "offset": 3745}, "end": {"line": 99, "col": 20, "offset": 3748}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 106, "col": 63, "offset": 3745}, "end": {"line": 106, "col": 66, "offset": 3748}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 111, "col": 5, "offset": 3745}, "end": {"line": 111, "col": 8, "offset": 3748}}, {"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 111, "col": 33, "offset": 3745}, "end": {"line": 111, "col": 36, "offset": 3748}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 123, "col": 33, "offset": 4728}, "end": {"line": 123, "col": 36, "offset": 4731}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml:123:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "start": {"line": 123, "col": 33, "offset": 4728}, "end": {"line": 123, "col": 36, "offset": 4731}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 79, "offset": 78}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 45, "col": 78, "offset": 0}, "end": {"line": 45, "col": 95, "offset": 17}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 46, "col": 87, "offset": 0}, "end": {"line": 46, "col": 118, "offset": 31}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 47, "col": 89, "offset": 0}, "end": {"line": 47, "col": 129, "offset": 40}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 48, "col": 89, "offset": 0}, "end": {"line": 48, "col": 127, "offset": 38}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/base.html:1:\n `{% load static wagtailcore_tags core_tags wagtailimages_tags wagtailuserbar %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 79, "offset": 78}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 45, "col": 78, "offset": 0}, "end": {"line": 45, "col": 95, "offset": 17}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 46, "col": 87, "offset": 0}, "end": {"line": 46, "col": 118, "offset": 31}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 47, "col": 89, "offset": 0}, "end": {"line": 47, "col": 129, "offset": 40}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "start": {"line": 48, "col": 89, "offset": 0}, "end": {"line": 48, "col": 127, "offset": 38}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 243}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "start": {"line": 58, "col": 1, "offset": 0}, "end": {"line": 62, "col": 23, "offset": 60}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "start": {"line": 303, "col": 1, "offset": 0}, "end": {"line": 305, "col": 15, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html:1:\n `{% extends \"base.html\" %}\n{% load macros wagtailcore_tags core_tags wagtailimages_tags static %}\n{% loadmacros \"core/includes/macros.html\" %}\n{% block body_class %}template-{{ self.get_verbose_name|slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 243}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "start": {"line": 58, "col": 1, "offset": 0}, "end": {"line": 62, "col": 23, "offset": 60}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "start": {"line": 303, "col": 1, "offset": 0}, "end": {"line": 305, "col": 15, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 217}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html", "start": {"line": 52, "col": 1, "offset": 0}, "end": {"line": 52, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html:1:\n `{% extends \"base.html\" %}\n{% load macros wagtailcore_tags core_tags %}\n{% loadmacros \"core/includes/macros.html\" %}\n{% block body_class %}template-{{ self.get_verbose_name|slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 217}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html", "start": {"line": 52, "col": 1, "offset": 0}, "end": {"line": 52, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/breadcrumbs.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 47, "offset": 46}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/includes/breadcrumbs.html:1:\n `{% load wagtailcore_tags wagtailimages_tags %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/breadcrumbs.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/breadcrumbs.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 47, "offset": 46}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 31, "offset": 88}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 25, "col": 19, "offset": 10}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 38, "col": 17, "offset": 29}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html:1:\n `{% load wagtailcore_tags %}\n\n    {% for link in links %}\n\n        {% if forloop.first %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 31, "offset": 88}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 25, "col": 19, "offset": 10}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 38, "col": 17, "offset": 29}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 38, "offset": 37}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/includes/menu.html:1:\n `{% load wagtailcore_tags core_tags %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/menu.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 38, "offset": 37}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 22, "offset": 130}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 32, "col": 130, "offset": 0}, "end": {"line": 32, "col": 135, "offset": 5}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 48, "col": 1, "offset": 0}, "end": {"line": 48, "col": 11, "offset": 10}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 52, "col": 1, "offset": 0}, "end": {"line": 54, "col": 36, "offset": 48}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html:1:\n `{% load macros wagtailcore_tags core_tags wagtailimages_tags%}\n{% loadmacros \"core/includes/macros.html\" %}\n\n{% if pages|length %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 22, "offset": 130}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 32, "col": 130, "offset": 0}, "end": {"line": 32, "col": 135, "offset": 5}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 48, "col": 1, "offset": 0}, "end": {"line": 48, "col": 11, "offset": 10}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "start": {"line": 52, "col": 1, "offset": 0}, "end": {"line": 54, "col": 36, "offset": 48}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 236}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html", "start": {"line": 50, "col": 1, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html:1:\n `{% extends \"base.html\" %}\n{% load macros wagtailcore_tags core_tags wagtailimages_tags%}\n{% loadmacros \"core/includes/macros.html\" %}\n\n{% block title %}Search{% if search_results %} Results{% endif %}{% endblock %}\n\n\n{% block content %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 236}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html", "start": {"line": 50, "col": 1, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 184}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html", "start": {"line": 73, "col": 5, "offset": 0}, "end": {"line": 75, "col": 15, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags wagtailimages_tags core_tags %}\n{% block body_class %}template-{{ self.get_verbose_name|slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 184}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html", "start": {"line": 73, "col": 5, "offset": 0}, "end": {"line": 75, "col": 15, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 185}}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html:1:\n `{% extends \"base.html\" %}\n{% load wagtailcore_tags wagtailimages_tags core_tags %}\n\n{% block body_class %}template-{{ self.get_verbose_name|slugify }}{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 185}}, {"file": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 56, "col": 22, "offset": 0}, "end": {"line": 56, "col": 28, "offset": 6}}, {"path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 57, "col": 22, "offset": 0}, "end": {"line": 57, "col": 28, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile:56:\n `--link` was unexpected", "path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "spans": [{"file": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 56, "col": 22, "offset": 0}, "end": {"line": 56, "col": 28, "offset": 6}}, {"file": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "start": {"line": 57, "col": 22, "offset": 0}, "end": {"line": 57, "col": 28, "offset": 6}}]}], "paths": {"scanned": ["downloaded_repos/springload_madewithwagtail/.circleci/circleci.env", "downloaded_repos/springload_madewithwagtail/.deepsource.toml", "downloaded_repos/springload_madewithwagtail/.dockerignore", "downloaded_repos/springload_madewithwagtail/.editorconfig", "downloaded_repos/springload_madewithwagtail/.eslintrc.json", "downloaded_repos/springload_madewithwagtail/.git-blame-ignore-revs", "downloaded_repos/springload_madewithwagtail/.githooks/deploy", "downloaded_repos/springload_madewithwagtail/.githooks/pre-commit", "downloaded_repos/springload_madewithwagtail/.githooks/pre-commit-js", "downloaded_repos/springload_madewithwagtail/.githooks/pre-commit-python", "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "downloaded_repos/springload_madewithwagtail/.github/workflows/codeql-analysis.yml", "downloaded_repos/springload_madewithwagtail/.github/workflows/static-analysis.yml", "downloaded_repos/springload_madewithwagtail/.gitignore", "downloaded_repos/springload_madewithwagtail/.nvmrc", "downloaded_repos/springload_madewithwagtail/.prettierrc", "downloaded_repos/springload_madewithwagtail/Justfile", "downloaded_repos/springload_madewithwagtail/LICENSE", "downloaded_repos/springload_madewithwagtail/Makefile", "downloaded_repos/springload_madewithwagtail/README.md", "downloaded_repos/springload_madewithwagtail/api/__init__.py", "downloaded_repos/springload_madewithwagtail/api/serializers.py", "downloaded_repos/springload_madewithwagtail/api/templates/__init__.py", "downloaded_repos/springload_madewithwagtail/api/templates/fixtures/data.json", "downloaded_repos/springload_madewithwagtail/api/urls.py", "downloaded_repos/springload_madewithwagtail/api/views.py", "downloaded_repos/springload_madewithwagtail/core/__init__.py", "downloaded_repos/springload_madewithwagtail/core/apps.py", "downloaded_repos/springload_madewithwagtail/core/context_processors.py", "downloaded_repos/springload_madewithwagtail/core/fixtures/__init__.py", "downloaded_repos/springload_madewithwagtail/core/fixtures/images/bcito.org.nz-1440x1200-1x.png", "downloaded_repos/springload_madewithwagtail/core/fixtures/images/springtail.png", "downloaded_repos/springload_madewithwagtail/core/fixtures/images/torchbox-logo.png", "downloaded_repos/springload_madewithwagtail/core/fixtures/images/www.rca.ac.uk-1440x1200-1x.png", "downloaded_repos/springload_madewithwagtail/core/fixtures/initial_data.json", "downloaded_repos/springload_madewithwagtail/core/forms.py", "downloaded_repos/springload_madewithwagtail/core/frontend/js/components/tagsToggle.js", "downloaded_repos/springload_madewithwagtail/core/frontend/js/wagtailsites.js", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/_breakpoints.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/_mixins.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/_typography.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_block.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_buttons.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_cards.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_company.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_content.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_footer.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_forms.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_grid.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_header.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_home.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_icons.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_map.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_media.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_nav.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_search.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_spacing.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/modules/_tags.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/sass/screen.scss", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/arrow-left.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/arrow-right.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/github.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/info.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/link.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/location.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/search.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/twitter.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/visit.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/colourise/wagtail-logo.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/made-with-wagtail-logo.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/springload-logo-dark.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/springload-logo.svg", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/template/default-css.hbs", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/template/preview.hbs", "downloaded_repos/springload_madewithwagtail/core/frontend/svg/wagtail-logo-og.svg", "downloaded_repos/springload_madewithwagtail/core/management/__init__.py", "downloaded_repos/springload_madewithwagtail/core/management/commands/__init__.py", "downloaded_repos/springload_madewithwagtail/core/management/commands/create_redirect.py", "downloaded_repos/springload_madewithwagtail/core/management/commands/load_initial_data.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0001_initial.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0002_auto_20150419_2004.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0003_wagtailsitepage_is_featured.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0004_auto_20160511_1652.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0005_auto_20161004_1431.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0006_wagtail_1_6_upgrade.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0007_auto_20170503_2308.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0008_wagtailcompanypage_sites_ordering.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0009_wagtail112upgrade.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0010_wagtailsitepage_screenshot.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0011_add_cooperation_field_to_site.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0012_auto_20191203_2153.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0013_auto_20211208_1153.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0014_submitformfield_clean_name.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0015_alter_wagtailcompanypage_coords.py", "downloaded_repos/springload_madewithwagtail/core/migrations/0016_auto_20231030_1543.py", "downloaded_repos/springload_madewithwagtail/core/migrations/__init__.py", "downloaded_repos/springload_madewithwagtail/core/models.py", "downloaded_repos/springload_madewithwagtail/core/panels.py", "downloaded_repos/springload_madewithwagtail/core/signals.py", "downloaded_repos/springload_madewithwagtail/core/snippets.py", "downloaded_repos/springload_madewithwagtail/core/static/images/apple-touch-icon-ipad-retina.png", "downloaded_repos/springload_madewithwagtail/core/static/images/apple-touch-icon-ipad.png", "downloaded_repos/springload_madewithwagtail/core/static/images/apple-touch-icon-iphone-retina.png", "downloaded_repos/springload_madewithwagtail/core/static/images/favicon.ico", "downloaded_repos/springload_madewithwagtail/core/static/images/markers/large.png", "downloaded_repos/springload_madewithwagtail/core/static/images/markers/medium.png", "downloaded_repos/springload_madewithwagtail/core/static/images/markers/small.png", "downloaded_repos/springload_madewithwagtail/core/static/images/markers/tiny.png", "downloaded_repos/springload_madewithwagtail/core/static/images/markers/xlarge.png", "downloaded_repos/springload_madewithwagtail/core/static/images/springtail.png", "downloaded_repos/springload_madewithwagtail/core/static/images/wagtail_logo.png", "downloaded_repos/springload_madewithwagtail/core/static/wagtailadmin/css/admin.css", "downloaded_repos/springload_madewithwagtail/core/static/wagtailadmin/scss/components/typography.scss", "downloaded_repos/springload_madewithwagtail/core/templates/403.html", "downloaded_repos/springload_madewithwagtail/core/templates/404.html", "downloaded_repos/springload_madewithwagtail/core/templates/500.html", "downloaded_repos/springload_madewithwagtail/core/templates/__init__.py", "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/breadcrumbs.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/icon.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/macros.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/menu.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/meta/open_graph.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/meta/open_graph_image.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/meta/twitter_card.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/search_box.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/svg.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/tag.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/submit_form_page.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/submit_form_page_landing.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html", "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html", "downloaded_repos/springload_madewithwagtail/core/templates/robots_dev.txt", "downloaded_repos/springload_madewithwagtail/core/templates/robots_prod.txt", "downloaded_repos/springload_madewithwagtail/core/templatetags/__init__.py", "downloaded_repos/springload_madewithwagtail/core/templatetags/core_tags.py", "downloaded_repos/springload_madewithwagtail/core/templatetags/macros.py", "downloaded_repos/springload_madewithwagtail/core/utilities.py", "downloaded_repos/springload_madewithwagtail/core/views.py", "downloaded_repos/springload_madewithwagtail/core/wagtail_hooks.py", "downloaded_repos/springload_madewithwagtail/deploy.sh", "downloaded_repos/springload_madewithwagtail/dev.env.example", "downloaded_repos/springload_madewithwagtail/docker/README.md", "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "downloaded_repos/springload_madewithwagtail/docker/constraints.txt", "downloaded_repos/springload_madewithwagtail/docker/database/01_continue_on_error.sh", "downloaded_repos/springload_madewithwagtail/docker/database/README.md", "downloaded_repos/springload_madewithwagtail/docker/database/run-after-init-script.sh", "downloaded_repos/springload_madewithwagtail/docker/dev.env", "downloaded_repos/springload_madewithwagtail/docker/gunicorn.py", "downloaded_repos/springload_madewithwagtail/docker/httpd/Dockerfile", "downloaded_repos/springload_madewithwagtail/docker/httpd/nginx.conf", "downloaded_repos/springload_madewithwagtail/docker/httpd/vhost.conf", "downloaded_repos/springload_madewithwagtail/docker/requirements.txt", "downloaded_repos/springload_madewithwagtail/docker-bake.hcl", "downloaded_repos/springload_madewithwagtail/docker-compose.yml", "downloaded_repos/springload_madewithwagtail/docs/more-wagtail-sites.md", "downloaded_repos/springload_madewithwagtail/gulpfile.js/config.js", "downloaded_repos/springload_madewithwagtail/gulpfile.js/css.js", "downloaded_repos/springload_madewithwagtail/gulpfile.js/index.js", "downloaded_repos/springload_madewithwagtail/gulpfile.js/js.js", "downloaded_repos/springload_madewithwagtail/gulpfile.js/svg.js", "downloaded_repos/springload_madewithwagtail/infra/README.md", "downloaded_repos/springload_madewithwagtail/infra/ecs-preview.toml", "downloaded_repos/springload_madewithwagtail/infra/ecs-production.toml", "downloaded_repos/springload_madewithwagtail/infra/ssm.ejson", "downloaded_repos/springload_madewithwagtail/madewithwagtail/__init__.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/s3utils.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/.gitignore", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/__init__.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/base.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/dev.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/aws.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/cache.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/celery.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/database.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/django.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/email.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/il8n.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/integrations/__init__.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/integrations/recaptcha.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/logging.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/paths.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/restframework.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/search/__init__.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/search/postgres.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/sentry.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/sessions.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/slack.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/templates.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/grains/wagtail.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/hosting.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/settings/test.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/urls.py", "downloaded_repos/springload_madewithwagtail/madewithwagtail/wsgi.py", "downloaded_repos/springload_madewithwagtail/manage.py", "downloaded_repos/springload_madewithwagtail/package.json", "downloaded_repos/springload_madewithwagtail/pyproject.toml", "downloaded_repos/springload_madewithwagtail/requirements/base.txt", "downloaded_repos/springload_madewithwagtail/requirements/constraints.txt", "downloaded_repos/springload_madewithwagtail/requirements/dev.txt", "downloaded_repos/springload_madewithwagtail/requirements/production.txt", "downloaded_repos/springload_madewithwagtail/requirements/test.txt", "downloaded_repos/springload_madewithwagtail/setup.cfg", "downloaded_repos/springload_madewithwagtail/svgo.config.js", "downloaded_repos/springload_madewithwagtail/test.sh", "downloaded_repos/springload_madewithwagtail/yarn.lock"], "skipped": [{"path": "downloaded_repos/springload_madewithwagtail/.github/workflows/build.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/static/js/vendor/elabel.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/static/js/vendor/markerclusterer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/static/wagtailadmin/js/vendor/jquery.htmlClean.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/static/wagtailadmin/js/vendor/rangy-selectionsaverestore.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/static/wagtailadmin/js/vendor/tag-it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/company_index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/home_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/breadcrumbs.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/footer_menu.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/macros.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/menu.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/includes/sites.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/search_results.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_company_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/templates/core/wagtail_site_page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/springload_madewithwagtail/core/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/tests/test_client.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/tests/test_page.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/tests/test_template_filters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/tests/test_template_tags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/core/tests/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/springload_madewithwagtail/docker/application/Dockerfile", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.1139791011810303, "profiling_times": {"config_time": 6.344736814498901, "core_time": 4.087246894836426, "ignores_time": 0.002122640609741211, "total_time": 10.4354567527771}, "parsing_time": {"total_time": 0.8248178958892822, "per_file_time": {"mean": 0.007430791854858398, "std_dev": 0.0001506272897807541}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 7.438871383666992, "per_file_time": {"mean": 0.013574582816910576, "std_dev": 0.002126120694814122}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.905198335647583, "per_file_and_rule_time": {"mean": 0.0032456530419890683, "std_dev": 0.00015472489170028183}, "very_slow_stats": {"time_ratio": 0.17858720975559822, "count_ratio": 0.0034071550255536627}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/springload_madewithwagtail/core/models.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.14208507537841797}, {"fpath": "downloaded_repos/springload_madewithwagtail/core/migrations/0001_initial.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.19815897941589355}]}, "tainting_time": {"total_time": 0.37389326095581055, "per_def_and_rule_time": {"mean": 0.0005972735798016143, "std_dev": 2.715326630752286e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}