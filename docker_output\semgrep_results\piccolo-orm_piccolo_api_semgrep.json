{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "path": "downloaded_repos/piccolo-orm_piccolo_api/e2e/pages.py", "start": {"line": 25, "col": 5, "offset": 541}, "end": {"line": 28, "col": 28, "offset": 723}, "extra": {"message": "Hardcoded password is used as a default argument to 'login'. This could be dangerous if a real password is not supplied.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["python"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "shortlink": "https://sg.run/Lw9r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "path": "downloaded_repos/piccolo-orm_piccolo_api/e2e/pages.py", "start": {"line": 45, "col": 5, "offset": 1215}, "end": {"line": 50, "col": 28, "offset": 1502}, "extra": {"message": "Hardcoded password is used as a default argument to 'login'. This could be dangerous if a real password is not supplied.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["python"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "shortlink": "https://sg.run/Lw9r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "path": "downloaded_repos/piccolo-orm_piccolo_api/e2e/pages.py", "start": {"line": 64, "col": 5, "offset": 1807}, "end": {"line": 66, "col": 28, "offset": 1923}, "extra": {"message": "Hardcoded password is used as a default argument to 'register'. This could be dangerous if a real password is not supplied.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["python"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/python.lang.security.audit.hardcoded-password-default-argument.hardcoded-password-default-argument", "shortlink": "https://sg.run/Lw9r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/app.py", "start": {"line": 23, "col": 15, "offset": 996}, "end": {"line": 28, "col": 2, "offset": 1129}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/app.py", "start": {"line": 35, "col": 37, "offset": 1299}, "end": {"line": 35, "col": 59, "offset": 1321}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/change_password/endpoints.py", "start": {"line": 244, "col": 19, "offset": 8209}, "end": {"line": 246, "col": 6, "offset": 8287}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/endpoints.py", "start": {"line": 497, "col": 21, "offset": 16479}, "end": {"line": 497, "col": 70, "offset": 16528}, "extra": {"message": "Detected user input used to manually construct a SQL string. This is usually bad practice because manual construction could accidentally result in a SQL injection. An attacker could use a SQL injection to steal or modify contents of the database. Instead, use a parameterized query which is available by default in most database engines. Alternatively, consider using the Django object-relational mappers (ORM) instead of raw SQL queries.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/security/#sql-injection-protection"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "impact": "LOW", "likelihood": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/python.django.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/PbZp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/fastapi/endpoints.py", "start": {"line": 287, "col": 13, "offset": 9047}, "end": {"line": 289, "col": 14, "offset": 9152}, "extra": {"message": "Annotations passed to `typing.get_type_hints` are evaluated in `globals` and `locals` namespaces. Make sure that no arbitrary value can be written as the annotation and passed to `typing.get_type_hints` function.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "category": "security", "references": ["https://docs.python.org/3/library/typing.html#typing.get_type_hints"], "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "shortlink": "https://sg.run/8R6J"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/fastapi/endpoints.py", "start": {"line": 348, "col": 13, "offset": 11131}, "end": {"line": 350, "col": 14, "offset": 11235}, "extra": {"message": "Annotations passed to `typing.get_type_hints` are evaluated in `globals` and `locals` namespaces. Make sure that no arbitrary value can be written as the annotation and passed to `typing.get_type_hints` function.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "category": "security", "references": ["https://docs.python.org/3/library/typing.html#typing.get_type_hints"], "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "shortlink": "https://sg.run/8R6J"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/fastapi/endpoints.py", "start": {"line": 372, "col": 13, "offset": 11933}, "end": {"line": 374, "col": 14, "offset": 12045}, "extra": {"message": "Annotations passed to `typing.get_type_hints` are evaluated in `globals` and `locals` namespaces. Make sure that no arbitrary value can be written as the annotation and passed to `typing.get_type_hints` function.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "category": "security", "references": ["https://docs.python.org/3/library/typing.html#typing.get_type_hints"], "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.dangerous-annotations-usage.dangerous-annotations-usage", "shortlink": "https://sg.run/8R6J"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/provider.py", "start": {"line": 77, "col": 23, "offset": 3043}, "end": {"line": 79, "col": 10, "offset": 3129}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/endpoints.py", "start": {"line": 22, "col": 15, "offset": 642}, "end": {"line": 24, "col": 2, "offset": 716}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/endpoints.py", "start": {"line": 54, "col": 21, "offset": 1411}, "end": {"line": 58, "col": 14, "offset": 1577}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/endpoints.py", "start": {"line": 69, "col": 21, "offset": 1829}, "end": {"line": 72, "col": 14, "offset": 1954}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/endpoints.py", "start": {"line": 145, "col": 37, "offset": 4871}, "end": {"line": 147, "col": 30, "offset": 4970}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/openapi/endpoints.py", "start": {"line": 16, "col": 15, "offset": 398}, "end": {"line": 21, "col": 2, "offset": 555}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/openapi/endpoints.py", "start": {"line": 69, "col": 20, "offset": 2167}, "end": {"line": 75, "col": 14, "offset": 2444}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/register/endpoints.py", "start": {"line": 258, "col": 19, "offset": 8588}, "end": {"line": 260, "col": 6, "offset": 8666}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/endpoints.py", "start": {"line": 519, "col": 19, "offset": 18710}, "end": {"line": 521, "col": 6, "offset": 18788}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/endpoints.py", "start": {"line": 574, "col": 19, "offset": 20708}, "end": {"line": 576, "col": 6, "offset": 20786}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/change_password.html", "start": {"line": 14, "col": 5, "offset": 273}, "end": {"line": 32, "col": 12, "offset": 1225}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_cancel.html", "start": {"line": 10, "col": 5, "offset": 170}, "end": {"line": 17, "col": 12, "offset": 463}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_setup.html", "start": {"line": 12, "col": 5, "offset": 194}, "end": {"line": 21, "col": 12, "offset": 573}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/session_logout.html", "start": {"line": 12, "col": 5, "offset": 188}, "end": {"line": 20, "col": 12, "offset": 446}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 102}}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html:1:\n `{% extends \"base.html\" %}\n\n{% block title %}MFA Authenticator Setup{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html", "spans": [{"file": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 102}}, {"file": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 91}}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html:1:\n `{% extends \"base.html\" %}\n\n{% block title %}MFA Disabled{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html", "spans": [{"file": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 91}}, {"file": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/piccolo-orm_piccolo_api/.flake8", "downloaded_repos/piccolo-orm_piccolo_api/.github/workflows/codeql.yml", "downloaded_repos/piccolo-orm_piccolo_api/.github/workflows/release.yaml", "downloaded_repos/piccolo-orm_piccolo_api/.github/workflows/tests.yaml", "downloaded_repos/piccolo-orm_piccolo_api/.gitignore", "downloaded_repos/piccolo-orm_piccolo_api/.readthedocs.yaml", "downloaded_repos/piccolo-orm_piccolo_api/CHANGES.rst", "downloaded_repos/piccolo-orm_piccolo_api/CONTRIBUTING.md", "downloaded_repos/piccolo-orm_piccolo_api/LICENSE", "downloaded_repos/piccolo-orm_piccolo_api/README.md", "downloaded_repos/piccolo-orm_piccolo_api/docs/Makefile", "downloaded_repos/piccolo-orm_piccolo_api/docs/logo_hero.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/make.bat", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/_static/.gitkeep", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/advanced_auth/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/api_reference/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/change_password/images/change_password.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/change_password/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/changes/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/conf.py", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/contributing/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/crud/hooks.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/crud/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/crud/piccolo_crud.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/crud/serializers.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/csp/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/csrf/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/csrf/introduction.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/csrf/prevention_measures.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/csrf/usage.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/encryption/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/encryption/introduction.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/encryption/providers.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/fastapi/images/fastapi_screenshot.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/fastapi/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/jwt/endpoints.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/jwt/example.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/jwt/examples/example.py", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/jwt/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/jwt/introduction.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/jwt/middleware.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/logo.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/mfa/endpoints.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/mfa/example.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/mfa/images/mfa_register_endpoint.jpg", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/mfa/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/mfa/introduction.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/mfa/providers.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/mfa/tables.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/openapi/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/piccolo_admin/images/piccolo_admin_screenshot.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/piccolo_admin/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/rate_limiting/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/register/endpoints.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/register/images/register_template.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/register/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/commands.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/endpoints.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/example.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/examples/example.py", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/images/login_template.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/images/logout_template.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/introduction.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/middleware.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/session_auth/tables.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/endpoints.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/example.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/examples/example.py", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/examples/excluded_paths_example/app.py", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/examples/excluded_paths_example/main.py", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/examples/excluded_paths_example/tables.py", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/images/authorize_button.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/images/authorize_modal.png", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/introduction.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/middleware.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/token_auth/tables.rst", "downloaded_repos/piccolo-orm_piccolo_api/docs/source/which_authentication/index.rst", "downloaded_repos/piccolo-orm_piccolo_api/e2e/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/e2e/conftest.py", "downloaded_repos/piccolo-orm_piccolo_api/e2e/pages.py", "downloaded_repos/piccolo-orm_piccolo_api/e2e/test_mfa.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/change_password_demo/README.md", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/change_password_demo/app.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/change_password_demo/main.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/change_password_demo/piccolo_conf.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/change_password_demo/requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/README.md", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/app.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/main.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/piccolo_conf.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/templates/home.html", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/register_demo/README.md", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/register_demo/app.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/register_demo/main.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/register_demo/piccolo_conf.py", "downloaded_repos/piccolo-orm_piccolo_api/example_projects/register_demo/requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/change_password/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/change_password/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/exceptions.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/hooks.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/serializers.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/validators.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/csp/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/csp/middleware.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/csrf/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/csrf/middleware.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/encryption/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/encryption/providers.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/fastapi/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/fastapi/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/jwt_auth/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/jwt_auth/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/jwt_auth/middleware.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/media/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/media/base.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/media/content_type.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/media/local.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/media/s3.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/README.md", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/piccolo_app.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/piccolo_migrations/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/piccolo_migrations/mfa_authenticator_2024_08_08t21_41_46_837552.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/provider.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/tables.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/authenticator/utils.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/provider.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/mfa/recovery_codes.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/openapi/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/openapi/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/openapi/templates/swagger_ui.html.jinja", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/py.typed", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/rate_limiting/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/rate_limiting/middleware.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/register/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/register/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/commands.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/middleware.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/piccolo_app.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/piccolo_migrations/2019-11-12T20-47-17.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/piccolo_migrations/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/session_auth/tables.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/auth/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/auth/captcha.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/auth/excluded_paths.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/auth/hooks.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/auth/junction.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/auth/styles.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/auth/user.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/middleware/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/shared/middleware/junction.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/base.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/change_password.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_cancel.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_setup.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/register.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/session_login.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/session_logout.html", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/token_auth/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/token_auth/endpoints.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/token_auth/middleware.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/token_auth/piccolo_app.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/token_auth/piccolo_migrations/2019-11-18T22-24-41.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/token_auth/piccolo_migrations/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/token_auth/tables.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/utils/__init__.py", "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/utils/types.py", "downloaded_repos/piccolo-orm_piccolo_api/pyproject.toml", "downloaded_repos/piccolo-orm_piccolo_api/requirements/README.md", "downloaded_repos/piccolo-orm_piccolo_api/requirements/dev-requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/doc-requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/e2e-requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/extras/authenticator.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/extras/cryptography.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/extras/pynacl.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/extras/s3.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/readthedocs-requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/requirements/test-requirements.txt", "downloaded_repos/piccolo-orm_piccolo_api/scripts/README.md", "downloaded_repos/piccolo-orm_piccolo_api/scripts/lint.sh", "downloaded_repos/piccolo-orm_piccolo_api/scripts/release.sh", "downloaded_repos/piccolo-orm_piccolo_api/scripts/run-docs.sh", "downloaded_repos/piccolo-orm_piccolo_api/scripts/run-e2e-test.sh", "downloaded_repos/piccolo-orm_piccolo_api/scripts/test-postgres.sh", "downloaded_repos/piccolo-orm_piccolo_api/scripts/test-sqlite.sh", "downloaded_repos/piccolo-orm_piccolo_api/setup.py"], "skipped": [{"path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_authenticator_setup.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/templates/mfa_disabled.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/change_password/test_change_password.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/crud/test_crud_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/crud/test_custom_pk.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/crud/test_hooks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/crud/test_validators.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/csp/test_csp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/csrf/test_csrf.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/fastapi/test_fastapi_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/jwt_auth/test_jwt_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/jwt_auth/test_jwt_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/media/test_base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/media/test_files/bulb.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/media/test_local.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/media/test_s3.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/mfa/authenticator/test_tables.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/mfa/test_mfa_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/mfa/test_recovery_codes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/openapi/test_openapi_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/postgres_conf.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/rate_limiting/test_rate_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/register/test_register.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/serve.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/session_auth/templates/complex_login_template/base.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/session_auth/templates/complex_login_template/login.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/session_auth/templates/simple_login_template/login.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/session_auth/test_session.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/shared/auth/test_captcha.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/shared/auth/test_styles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/shared/auth/test_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/sqlite_conf.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/token_auth/test_endpoints.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/token_auth/test_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/piccolo-orm_piccolo_api/tests/utils/test_types.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.011569023132324, "profiling_times": {"config_time": 6.481590509414673, "core_time": 5.27485203742981, "ignores_time": 0.002572298049926758, "total_time": 11.759857177734375}, "parsing_time": {"total_time": 1.13979172706604, "per_file_time": {"mean": 0.010855159305390858, "std_dev": 0.0005267989791243658}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 12.033472537994385, "per_file_time": {"mean": 0.023365966093192976, "std_dev": 0.012415242272725125}, "very_slow_stats": {"time_ratio": 0.14521185808174752, "count_ratio": 0.001941747572815534}, "very_slow_files": [{"fpath": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/endpoints.py", "ftime": 1.7474029064178467}]}, "matching_time": {"total_time": 5.588046550750732, "per_file_and_rule_time": {"mean": 0.006060788015998624, "std_dev": 0.0004176839926264465}, "very_slow_stats": {"time_ratio": 0.267777630533298, "count_ratio": 0.008676789587852495}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/endpoints.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.11085391044616699}, {"fpath": "downloaded_repos/piccolo-orm_piccolo_api/example_projects/change_password_demo/app.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.1619729995727539}, {"fpath": "downloaded_repos/piccolo-orm_piccolo_api/example_projects/mfa_demo/app.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.17679500579833984}, {"fpath": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/crud/endpoints.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.19191408157348633}, {"fpath": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/change_password/endpoints.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.1972799301147461}, {"fpath": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/register/endpoints.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.20462703704833984}, {"fpath": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/csrf/middleware.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.21802997589111328}, {"fpath": "downloaded_repos/piccolo-orm_piccolo_api/piccolo_api/jwt_auth/middleware.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.23488092422485352}]}, "tainting_time": {"total_time": 1.5759799480438232, "per_def_and_rule_time": {"mean": 0.0006115560527915495, "std_dev": 2.8617122256565342e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}