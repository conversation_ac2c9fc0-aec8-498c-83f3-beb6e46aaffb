{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/Dockerfile", "start": {"line": 21, "col": 1, "offset": 206}, "end": {"line": 21, "col": 141, "offset": 346}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD clientID=$clientID clientSecret=$clientSecret callBackURL=$callBackURL admin=$admin token=$token prefix=$prefix port=$port node index.js", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 9, "col": 7, "offset": 294}, "end": {"line": 9, "col": 22, "offset": 309}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-cookie-settings.express-cookie-session-default-name", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 26, "col": 5, "offset": 677}, "end": {"line": 30, "col": 7, "offset": 793}, "extra": {"message": "Don’t use the default session cookie name Using the default session cookie name can open your app to attacks. The security issue posed is similar to X-Powered-By: a potential attacker can use it to fingerprint the server and target attacks accordingly.", "metadata": {"cwe": ["CWE-522: Insufficiently Protected Credentials"], "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "source-rule-url": "https://expressjs.com/en/advanced/best-practice-security.html", "category": "security", "technology": ["express"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-cookie-settings.express-cookie-session-default-name", "shortlink": "https://sg.run/1Z5x"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-domain", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 26, "col": 5, "offset": 677}, "end": {"line": 30, "col": 7, "offset": 793}, "extra": {"message": "Default session middleware settings: `domain` not set. It indicates the domain of the cookie; use it to compare against the domain of the server in which the URL is being requested. If they match, then check the path attribute next.", "metadata": {"cwe": ["CWE-522: Insufficiently Protected Credentials"], "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "source-rule-url": "https://expressjs.com/en/advanced/best-practice-security.html", "category": "security", "technology": ["express"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-domain", "shortlink": "https://sg.run/rd41"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-expires", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 26, "col": 5, "offset": 677}, "end": {"line": 30, "col": 7, "offset": 793}, "extra": {"message": "Default session middleware settings: `expires` not set. Use it to set expiration date for persistent cookies.", "metadata": {"cwe": ["CWE-522: Insufficiently Protected Credentials"], "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "source-rule-url": "https://expressjs.com/en/advanced/best-practice-security.html", "category": "security", "technology": ["express"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-expires", "shortlink": "https://sg.run/N4eG"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-httponly", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 26, "col": 5, "offset": 677}, "end": {"line": 30, "col": 7, "offset": 793}, "extra": {"message": "Default session middleware settings: `httpOnly` not set. It ensures the cookie is sent only over HTTP(S), not client JavaScript, helping to protect against cross-site scripting attacks.", "metadata": {"cwe": ["CWE-522: Insufficiently Protected Credentials"], "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "source-rule-url": "https://expressjs.com/en/advanced/best-practice-security.html", "category": "security", "technology": ["express"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-httponly", "shortlink": "https://sg.run/ydBO"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-path", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 26, "col": 5, "offset": 677}, "end": {"line": 30, "col": 7, "offset": 793}, "extra": {"message": "Default session middleware settings: `path` not set. It indicates the path of the cookie; use it to compare against the request path. If this and domain match, then send the cookie in the request.", "metadata": {"cwe": ["CWE-522: Insufficiently Protected Credentials"], "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "source-rule-url": "https://expressjs.com/en/advanced/best-practice-security.html", "category": "security", "technology": ["express"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-path", "shortlink": "https://sg.run/b7pd"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-secure", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 26, "col": 5, "offset": 677}, "end": {"line": 30, "col": 7, "offset": 793}, "extra": {"message": "Default session middleware settings: `secure` not set. It ensures the browser only sends the cookie over HTTPS.", "metadata": {"cwe": ["CWE-522: Insufficiently Protected Credentials"], "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "source-rule-url": "https://expressjs.com/en/advanced/best-practice-security.html", "category": "security", "technology": ["express"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-cookie-settings.express-cookie-session-no-secure", "shortlink": "https://sg.run/9oKz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 27, "col": 7, "offset": 693}, "end": {"line": 27, "col": 49, "offset": 735}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-session-hardcoded-secret.express-session-hardcoded-secret", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "start": {"line": 27, "col": 7, "offset": 693}, "end": {"line": 27, "col": 49, "offset": 735}, "extra": {"message": "A hard-coded credential was detected. It is not recommended to store credentials in source-code, as this risks secrets being leaked and used by either an internal or external malicious adversary. It is recommended to use environment variables to securely provide credentials or retrieve credentials from a secure vault or HSM (Hardware Security Module).", "metadata": {"interfile": true, "cwe": ["CWE-798: Use of Hard-coded Credentials"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "category": "security", "technology": ["express", "secrets"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-session-hardcoded-secret.express-session-hardcoded-secret", "shortlink": "https://sg.run/LYvG"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/index.html", "start": {"line": 12, "col": 9, "offset": 201}, "end": {"line": 12, "col": 67, "offset": 259}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/installation/index.html", "start": {"line": 12, "col": 9, "offset": 201}, "end": {"line": 12, "col": 80, "offset": 272}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/installation/index.html", "start": {"line": 319, "col": 54, "offset": 9076}, "end": {"line": 319, "col": 111, "offset": 9133}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/plugins/index.html", "start": {"line": 12, "col": 9, "offset": 201}, "end": {"line": 12, "col": 75, "offset": 267}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/index.html", "start": {"line": 7, "col": 1, "offset": 169}, "end": {"line": 7, "col": 122, "offset": 290}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/index.html", "start": {"line": 8, "col": 1, "offset": 291}, "end": {"line": 8, "col": 101, "offset": 391}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/index.html", "start": {"line": 10, "col": 1, "offset": 436}, "end": {"line": 10, "col": 89, "offset": 524}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/index.html", "start": {"line": 11, "col": 1, "offset": 525}, "end": {"line": 11, "col": 98, "offset": 622}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/index.html", "start": {"line": 12, "col": 1, "offset": 623}, "end": {"line": 12, "col": 92, "offset": 714}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/index.html", "start": {"line": 196, "col": 1, "offset": 8922}, "end": {"line": 196, "col": 86, "offset": 9007}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/overrides/partials/footer.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 59, "offset": 58}}]], "message": "Syntax error at line downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/overrides/partials/footer.html:20:\n `{% import \"partials/language.html\" as lang with context %}` was unexpected", "path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/overrides/partials/footer.html", "spans": [{"file": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/overrides/partials/footer.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 59, "offset": 58}}]}], "paths": {"scanned": ["downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/.dockerignore", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/.gitattributes", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/.gitignore", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/.replit", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/Dockerfile", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/FUNDING.yml", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/LICENSE", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/README.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/TODO.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/content/dashprev.JPG", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/content/headerimage.png", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/README.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/docs/assets/images/example_dbp.jpeg", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/docs/assets/images/favicon.png", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/docs/index.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/docs/installation/basic.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/docs/plugins.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/docs/themes.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/docs/update.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/mkdocs.yml", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/overrides/partials/footer.html", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/overrides/partials/header.html", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/auth/auth.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/auth/passport.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/bot.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/ban.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/clear.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/coin.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/dog.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/index.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/kick.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/ping.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/roll.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/serverinfo.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/stats.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/commands/userinfo.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/config/config.default.json", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/config/settings.json", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/config/theme.json", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/config/version.json", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/events/message.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/events/ready.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/index.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/package-lock.json", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/package.json", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/stylesheets/plugins.css", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/login/script.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/login/style.css", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/routes/guilds.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/routes/home.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/routes/login.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/routes/plugins.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/routes/settings.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/routes/support.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/themes/default.css", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/views/error_pages/404.ejs", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/views/home/<USER>", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/views/home/<USER>", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/views/home/<USER>", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/views/home/<USER>", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/views/home/<USER>", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/views/login/login.ejs", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/README.md", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/images/favicon.png", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/bundle.76f349be.min.js.map", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/tinyseg.js", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/workers/search.b0710199.min.js.map", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/stylesheets/main.ca7ac06f.min.css", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/stylesheets/main.ca7ac06f.min.css.map", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/stylesheets/palette.f1a3b89f.min.css", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/stylesheets/palette.f1a3b89f.min.css.map", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/index.html", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/installation/index.html", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/plugins/index.html", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/sitemap.xml", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/sitemap.xml.gz", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/images/favicon.png", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/index.html", "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/main.css"], "skipped": [{"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/docs/overrides/partials/footer.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/vendor/popper-js/umd/popper.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/bundle.76f349be.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.ar.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.da.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.de.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.du.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.es.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.fi.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.fr.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.hu.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.it.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.ja.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.jp.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.multi.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.nl.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.no.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.pt.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.ro.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.ru.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.stemmer.support.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.sv.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.tr.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/lunr/min/lunr.vi.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/website/docs/assets/javascripts/workers/search.b0710199.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6187870502471924, "profiling_times": {"config_time": 6.044525623321533, "core_time": 5.1239705085754395, "ignores_time": 0.0017409324645996094, "total_time": 11.171190738677979}, "parsing_time": {"total_time": 1.4538447856903076, "per_file_time": {"mean": 0.03545962891927579, "std_dev": 0.004957378916459966}, "very_slow_stats": {"time_ratio": 0.2691270570512607, "count_ratio": 0.024390243902439025}, "very_slow_files": [{"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "ftime": 0.3912689685821533}]}, "scanning_time": {"total_time": 7.790658950805664, "per_file_time": {"mean": 0.03875949726768987, "std_dev": 0.03962311306801691}, "very_slow_stats": {"time_ratio": 0.34809341863691684, "count_ratio": 0.004975124378109453}, "very_slow_files": [{"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "ftime": 2.7118771076202393}]}, "matching_time": {"total_time": 2.745850086212158, "per_file_and_rule_time": {"mean": 0.012096255886397173, "std_dev": 0.0008648655899057756}, "very_slow_stats": {"time_ratio": 0.35065655517572825, "count_ratio": 0.02643171806167401}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.1127169132232666}, {"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.11530017852783203}, {"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.14237403869628906}, {"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.14383697509765625}, {"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.21127104759216309}, {"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2373511791229248}]}, "tainting_time": {"total_time": 0.7221248149871826, "per_def_and_rule_time": {"mean": 0.006877379190354119, "std_dev": 0.00017755527956262102}, "very_slow_stats": {"time_ratio": 0.1612114456795092, "count_ratio": 0.01904761904761905}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.05643486976623535}, {"fpath": "downloaded_repos/LachlanDev_Discord-BOT-Dashboard-V2/src/public/home/<USER>/javascript/theme.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.059979915618896484}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}