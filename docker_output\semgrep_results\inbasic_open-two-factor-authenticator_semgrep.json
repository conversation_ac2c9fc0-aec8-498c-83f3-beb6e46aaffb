{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/edit/index.js", "start": {"line": 9, "col": 3, "offset": 208}, "end": {"line": 16, "col": 10, "offset": 391}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/edit/index.js", "start": {"line": 22, "col": 1, "offset": 464}, "end": {"line": 24, "col": 8, "offset": 511}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/edit/index.js", "start": {"line": 26, "col": 1, "offset": 514}, "end": {"line": 35, "col": 3, "offset": 772}, "extra": {"message": "No validation of origin is done by the addEventListener API. It may be possible to exploit this flaw to perform Cross Origin attacks such as Cross-Site Scripting(XSS).", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "shortlink": "https://sg.run/gL9x"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/index.js", "start": {"line": 23, "col": 1, "offset": 551}, "end": {"line": 25, "col": 10, "offset": 636}, "extra": {"message": "No validation of origin is done by the addEventListener API. It may be possible to exploit this flaw to perform Cross Origin attacks such as Cross-Site Scripting(XSS).", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "shortlink": "https://sg.run/gL9x"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/index.js", "start": {"line": 43, "col": 46, "offset": 1472}, "end": {"line": 46, "col": 8, "offset": 1546}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/index.js", "start": {"line": 47, "col": 42, "offset": 1590}, "end": {"line": 50, "col": 8, "offset": 1659}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/index.js", "start": {"line": 51, "col": 42, "offset": 1703}, "end": {"line": 54, "col": 8, "offset": 1772}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/pin/index.js", "start": {"line": 19, "col": 3, "offset": 548}, "end": {"line": 22, "col": 10, "offset": 628}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/pin/index.js", "start": {"line": 30, "col": 3, "offset": 790}, "end": {"line": 32, "col": 10, "offset": 851}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.js", "start": {"line": 52, "col": 7, "offset": 1403}, "end": {"line": 56, "col": 14, "offset": 1535}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.js", "start": {"line": 87, "col": 5, "offset": 2370}, "end": {"line": 90, "col": 12, "offset": 2444}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.js", "start": {"line": 94, "col": 5, "offset": 2500}, "end": {"line": 97, "col": 12, "offset": 2642}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.js", "start": {"line": 100, "col": 5, "offset": 2681}, "end": {"line": 102, "col": 12, "offset": 2737}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.js", "start": {"line": 106, "col": 1, "offset": 2755}, "end": {"line": 132, "col": 10, "offset": 3755}, "extra": {"message": "No validation of origin is done by the addEventListener API. It may be possible to exploit this flaw to perform Cross Origin attacks such as Cross-Site Scripting(XSS).", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "shortlink": "https://sg.run/gL9x"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.js", "start": {"line": 133, "col": 1, "offset": 3757}, "end": {"line": 133, "col": 49, "offset": 3805}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/index.js", "start": {"line": 115, "col": 3, "offset": 3132}, "end": {"line": 115, "col": 98, "offset": 3227}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/firefox/firefox.js", "start": {"line": 322, "col": 18, "offset": 0}, "end": {"line": 322, "col": 41, "offset": 23}}]], "message": "Syntax error at line downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/firefox/firefox.js:322:\n `ex if ex.result === Cr.` was unexpected", "path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/firefox/firefox.js", "spans": [{"file": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/firefox/firefox.js", "start": {"line": 322, "col": 18, "offset": 0}, "end": {"line": 322, "col": 41, "offset": 23}}]}], "paths": {"scanned": ["downloaded_repos/inbasic_open-two-factor-authenticator/.github/FUNDING.yml", "downloaded_repos/inbasic_open-two-factor-authenticator/LICENSE", "downloaded_repos/inbasic_open-two-factor-authenticator/README.md", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/.gitignore", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/builds/packed/chrome.zip", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/builds/packed/firefox.xpi", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/builds/packed/icon.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/builds/packed/icon64.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/drawings/screenshot.xcf", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/drawings/small-tile.xcf", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/gulpfile.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/package.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/icons/128.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/icons/16.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/icons/256.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/icons/32.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/icons/48.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/icons/512.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/icons/64.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/chrome/chrome.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/firefox/firefox.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/qrcode-decoder.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/chrome/chrome.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/edit/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/edit/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/edit/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/firefox/firefox.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/fontello.woff", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/pin/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/pin/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/pin/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/RPKDmaFi75RJkvjWaDDb0nYhjbSpvc47ee6xR_80Hnw.woff2", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/VK-RlLrn4NFhRGqPkj6IwBkAz4rYn47Zy2rvigWQf6w.woff2", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/copy.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/LogMeIn.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/amazon.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/app_net.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/bitcoin.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/dashlane.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/dropbox.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/evernote.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/facebook.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/flickr.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/general.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/github.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/gmail.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/google.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/microsoft.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/nearlyfreespeech.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/new.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/outlook.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/twitter.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/yahoo.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/icons/zoho.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/popup/token/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/chrome/EventEmitter.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/chrome/background.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/chrome/chrome.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/common.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/config.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/firefox/firefox.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/secure.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/totp.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/manifest-app.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/manifest-extension.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/package.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/background.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/128.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/16.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/19.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/256.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/32.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/38.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/48.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/512.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/64.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/icons/icon.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/ReadMe", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/Sortable.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/close.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/copy.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/amazon.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/app_net.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/bitcoin.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/dashlane.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/dropbox.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/evernote.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/facebook.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/firefox.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/flickr.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/general.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/github.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/gmail.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/google.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/logmein.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/mega.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/microsoft.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/nearlyfreespeech.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/outlook.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/paypal.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/twitter.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/yahoo.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/icons/zoho.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/library/jsOTP.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/plus.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/refresh.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/secure.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/manifest.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/de/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/en/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/es/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/fr/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/it/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/ja/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/nl/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/pt_BR/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/pt_PT/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/ru/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/_locales/zh_CN/messages.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/128.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/16.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/19.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/256.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/32.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/38.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/48.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/512.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/64.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/icons/icon.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/ReadMe", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/Sortable.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/close.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/copy.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/amazon.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/app_net.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/bitcoin.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/dashlane.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/dropbox.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/evernote.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/facebook.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/firefox.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/flickr.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/general.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/github.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/gmail.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/google.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/logmein.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/mega.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/microsoft.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/nearlyfreespeech.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/outlook.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/paypal.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/twitter.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/yahoo.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/icons/zoho.png", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/index.css", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/index.html", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/index.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/library/jsOTP.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/plus.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/refresh.svg", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/secure.js", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/manifest.json", "downloaded_repos/inbasic_open-two-factor-authenticator/v3/worker.js"], "skipped": [{"path": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/lib/firefox/firefox.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.7015750408172607, "profiling_times": {"config_time": 6.599223375320435, "core_time": 7.647796392440796, "ignores_time": 0.0016598701477050781, "total_time": 14.249425888061523}, "parsing_time": {"total_time": 8.440951108932495, "per_file_time": {"mean": 0.14553363980918094, "std_dev": 0.13832322474160266}, "very_slow_stats": {"time_ratio": 0.8560972423529997, "count_ratio": 0.1206896551724138}, "very_slow_files": [{"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/library/jsOTP.js", "ftime": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/library/jsOTP.js", "ftime": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/Sortable.js", "ftime": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/Sortable.js", "ftime": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/qrcode-decoder.js", "ftime": 1.***************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "ftime": 1.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "ftime": 1.****************}]}, "scanning_time": {"total_time": 31.**************, "per_file_time": {"mean": 0.*****************, "std_dev": 0.****************}, "very_slow_stats": {"time_ratio": 0.****************, "count_ratio": 0.*****************}, "very_slow_files": [{"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/Sortable.js", "ftime": 3.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/Sortable.js", "ftime": 3.**************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/qrcode-decoder.js", "ftime": 3.***************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "ftime": 4.***************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "ftime": 4.***************}]}, "matching_time": {"total_time": 12.***************, "per_file_and_rule_time": {"mean": 0.036197934736752646, "std_dev": 0.006575871510822133}, "very_slow_stats": {"time_ratio": 0.****************, "count_ratio": 0.*****************}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/qrcode-decoder.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/Sortable.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.3066680431365967}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.33057713508605957}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/Sortable.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/qrcode-decoder.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.****************}]}, "tainting_time": {"total_time": 6.****************, "per_def_and_rule_time": {"mean": 0.004076672175229194, "std_dev": 0.0002773765067899034}, "very_slow_stats": {"time_ratio": 0.*****************, "count_ratio": 0.016075016744809108}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/Sortable.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/Sortable.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.10679793357849121}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.11408615112304688}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "fline": 1, "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.11804890632629395}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/popup/Sortable.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/popup/Sortable.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.1.0/src/data/new-account/qrcode-decoder.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v2/v0.2.0/data/plus/jsQR.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.*****************}, {"fpath": "downloaded_repos/inbasic_open-two-factor-authenticator/v3/data/plus/jsQR.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.*****************}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": **********}, "engine_requested": "OSS", "skipped_rules": []}