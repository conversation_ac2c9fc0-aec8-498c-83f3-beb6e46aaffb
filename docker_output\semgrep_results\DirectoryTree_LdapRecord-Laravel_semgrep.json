{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/LdapDatabaseManager.php", "start": {"line": 181, "col": 13, "offset": 5193}, "end": {"line": 181, "col": 32, "offset": 5212}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 59, "offset": 1469}, "end": {"line": 52, "col": 75, "offset": 1485}}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 103, "offset": 1469}, "end": {"line": 52, "col": 121, "offset": 1487}}]], "message": "Syntax error at line downloaded_repos/DirectoryTree_LdapRecord-<PERSON><PERSON>/.github/workflows/run-tests.yml:52:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 59, "offset": 1469}, "end": {"line": 52, "col": 75, "offset": 1485}}, {"file": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-tests.yml", "start": {"line": 52, "col": 103, "offset": 1469}, "end": {"line": 52, "col": 121, "offset": 1487}}]}], "paths": {"scanned": ["downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/FUNDING.yml", "downloaded_repos/DirectoryTree_LdapRecord-<PERSON><PERSON>/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/DirectoryTree_LdapRecord-<PERSON><PERSON>/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/DirectoryTree_LdapRecord-<PERSON>vel/.github/ISSUE_TEMPLATE/support-request.md", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-cs-fix.yml", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-tests.yml", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.gitignore", "downloaded_repos/DirectoryTree_LdapRecord-<PERSON><PERSON>/composer.json", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/config/ldap.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/database/migrations/2025_01_01_000000_add_ldap_columns_to_users_table.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/license.md", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/phpunit.xml", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/readme.md", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/resources/lang/en/errors.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/AuthenticatesWithLdap.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/BindFailureListener.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/CreatesUserProvider.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/DatabaseUserProvider.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/HasLdapUser.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/LdapAuthenticatable.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/ListensForLdapBindFailure.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/NoDatabaseUserProvider.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/Rule.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/Rules/OnlyImported.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/UserProvider.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Auth/Validator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/BrowseLdapServer.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/GetRootDse.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/ImportLdapUsers.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/MakeLdapModel.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/MakeLdapRule.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/MakeLdapScope.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/TestLdapConnection.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/stubs/model.stub", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/stubs/rule.stub", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Commands/stubs/scope.stub", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/DetectsSoftDeletes.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/BindFailed.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/Binding.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/Bound.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/Completed.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/CompletedWithWindows.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/DiscoveredWithCredentials.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/EloquentUserTrashed.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/Event.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/Rejected.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/RuleEvent.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/RuleFailed.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Auth/RulePassed.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Completed.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Deleted.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/DeletedMissing.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Event.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/ImportFailed.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Imported.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Importing.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Restored.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Saved.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Started.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Synchronized.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Import/Synchronizing.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/Loggable.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Events/LoggableEvent.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/EloquentHydrator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/EloquentUserHydrator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/Hydrators/AttributeHydrator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/Hydrators/DomainHydrator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/Hydrators/GuidHydrator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/Hydrators/Hydrator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/Hydrators/PasswordHydrator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/ImportException.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/Importer.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/LdapUserImporter.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/Synchronizer.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Import/UserSynchronizer.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/ImportableFromLdap.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/LdapAuthServiceProvider.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/LdapImportable.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/LdapRecord.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/LdapServiceProvider.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/LdapUserAuthenticator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/LdapUserRepository.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Middleware/UserDomainValidator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Middleware/WindowsAuthenticate.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/DirectoryEmulator.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/Emulated/ActiveDirectoryBuilder.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/Emulated/EmulatesModelQueries.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/Emulated/ModelBuilder.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/Emulated/OpenLdapBuilder.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/EmulatedBuilder.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/EmulatedConnectionFake.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/EmulatesQueries.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/LdapDatabaseManager.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/LdapObject.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/LdapObjectAttribute.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/LdapObjectAttributeValue.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/ResolvesEmulatedConnection.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/UnescapedValue.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/VirtualAttributeObserver.php", "downloaded_repos/DirectoryTree_LdapRecord-Laravel/src/Testing/VirtualAttributeValueObserver.php"], "skipped": [{"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Commands/GetRootDseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Commands/ImportLdapUsersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Commands/TestLdapConnectionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/CreatesTestUsers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/DatabaseTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/DatabaseUserProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedAuthenticationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedDatabaseAuthenticationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedImportTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedModelBindingTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedModelQueryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedQueryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedUserRepositoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatedWindowsAuthenticateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/Emulator/EmulatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/LdapUserSynchronizerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/ListenForLdapBindFailureTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/NoDatabaseUserProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/SanctumTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/SanctumTestUserModelStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/TestUserModelStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Feature/WindowsAuthMiddlewareTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/EloquentHydratorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/LdapDatabaseManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/LdapImporterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/LdapServiceProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/LdapSynchronizerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/LdapUserAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/LdapUserRepositoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/ListenForLdapBindFailureTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/ValidatorRuleOnlyImportedTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_LdapRecord-Laravel/tests/Unit/ValidatorTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.010612964630127, "profiling_times": {"config_time": 6.315556764602661, "core_time": 2.996856927871704, "ignores_time": 0.0022444725036621094, "total_time": 9.315486907958984}, "parsing_time": {"total_time": 1.09409761428833, "per_file_time": {"mean": 0.012156640158759223, "std_dev": 0.0005597065314700445}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.0415687561035156, "per_file_time": {"mean": 0.007039892262425912, "std_dev": 0.00038363553392873565}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.2559795379638672, "per_file_and_rule_time": {"mean": 0.0013402070050464252, "std_dev": 1.8822130714968428e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.01545858383178711, "per_def_and_rule_time": {"mean": 0.00019084671397268034, "std_dev": 1.1781369531594609e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}