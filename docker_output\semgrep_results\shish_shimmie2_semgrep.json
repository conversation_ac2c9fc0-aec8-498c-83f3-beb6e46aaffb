{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/shish_shimmie2/Dockerfile", "start": {"line": 81, "col": 1, "offset": 2918}, "end": {"line": 81, "col": 42, "offset": 2959}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"/app/.docker/entrypoint.sh\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/shish_shimmie2/Dockerfile", "start": {"line": 82, "col": 1, "offset": 2960}, "end": {"line": 82, "col": 36, "offset": 2995}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"php\", \"/app/.docker/run.php\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/shish_shimmie2/core/Util/CommandBuilder.php", "start": {"line": 37, "col": 9, "offset": 937}, "end": {"line": 37, "col": 35, "offset": 963}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/shish_shimmie2/core/Util/Filesystem.php", "start": {"line": 206, "col": 49, "offset": 6672}, "end": {"line": 206, "col": 66, "offset": 6689}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/shish_shimmie2/core/Util/Network.php", "start": {"line": 98, "col": 13, "offset": 3016}, "end": {"line": 98, "col": 60, "offset": 3063}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/shish_shimmie2/core/Util/Network.php", "start": {"line": 119, "col": 13, "offset": 3996}, "end": {"line": 119, "col": 85, "offset": 4068}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.assert-use.assert-use", "path": "downloaded_repos/shish_shimmie2/core/Util/util.php", "start": {"line": 525, "col": 12, "offset": 16608}, "end": {"line": 525, "col": 37, "offset": 16633}, "extra": {"message": "Calling assert with user input is equivalent to eval'ing.", "metadata": {"owasp": ["A03:2021 - Injection"], "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "references": ["https://www.php.net/manual/en/function.assert", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/AssertsSniff.php"], "category": "security", "technology": ["php"], "confidence": "HIGH", "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.assert-use.assert-use", "shortlink": "https://sg.run/3xXW"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/shish_shimmie2/ext/bbcode/script.js", "start": {"line": 16, "col": 13, "offset": 771}, "end": {"line": 18, "col": 76, "offset": 883}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/shish_shimmie2/ext/handle_pixel/test.php", "start": {"line": 56, "col": 13, "offset": 1593}, "end": {"line": 56, "col": 26, "offset": 1606}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/shish_shimmie2/ext/index/script.js", "start": {"line": 10, "col": 9, "offset": 440}, "end": {"line": 10, "col": 63, "offset": 494}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/shish_shimmie2/ext/tag_editcloud/script.js", "start": {"line": 29, "col": 5, "offset": 710}, "end": {"line": 30, "col": 69, "offset": 795}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/.github/workflows/release.yml", "start": {"line": 29, "col": 19, "offset": 679}, "end": {"line": 29, "col": 22, "offset": 682}}, {"path": "downloaded_repos/shish_shimmie2/.github/workflows/release.yml", "start": {"line": 30, "col": 17, "offset": 679}, "end": {"line": 30, "col": 20, "offset": 682}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/.github/workflows/release.yml:29:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/shish_shimmie2/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/shish_shimmie2/.github/workflows/release.yml", "start": {"line": 29, "col": 19, "offset": 679}, "end": {"line": 29, "col": 22, "offset": 682}}, {"file": "downloaded_repos/shish_shimmie2/.github/workflows/release.yml", "start": {"line": 30, "col": 17, "offset": 679}, "end": {"line": 30, "col": 20, "offset": 682}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 188, "col": 11, "offset": 5546}, "end": {"line": 188, "col": 39, "offset": 5574}}, {"path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 189, "col": 10, "offset": 5546}, "end": {"line": 189, "col": 23, "offset": 5559}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/.github/workflows/tests.yml:188:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ steps.build.outputs.digest` was unexpected", "path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 188, "col": 11, "offset": 5546}, "end": {"line": 188, "col": 39, "offset": 5574}}, {"file": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 189, "col": 10, "offset": 5546}, "end": {"line": 189, "col": 23, "offset": 5559}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 235, "col": 21, "offset": 7292}, "end": {"line": 235, "col": 43, "offset": 7314}}, {"path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 235, "col": 49, "offset": 7292}, "end": {"line": 235, "col": 68, "offset": 7311}}, {"path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 236, "col": 89, "offset": 7292}, "end": {"line": 236, "col": 92, "offset": 7295}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/.github/workflows/tests.yml:235:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ vars.DOCKER_USERNAME` was unexpected", "path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 235, "col": 21, "offset": 7292}, "end": {"line": 235, "col": 43, "offset": 7314}}, {"file": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 235, "col": 49, "offset": 7292}, "end": {"line": 235, "col": 68, "offset": 7311}}, {"file": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "start": {"line": 236, "col": 89, "offset": 7292}, "end": {"line": 236, "col": 92, "offset": 7295}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Config/ConfigMeta.php", "start": {"line": 8, "col": 7, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Config/ConfigMeta.php:8:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Config/ConfigMeta.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Config/ConfigMeta.php", "start": {"line": 8, "col": 7, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Events/EventBus.php", "start": {"line": 22, "col": 22, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Events/EventBus.php:22:\n `array` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Events/EventBus.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Events/EventBus.php", "start": {"line": 22, "col": 22, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 150, "col": 39, "offset": 0}, "end": {"line": 150, "col": 42, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 151, "col": 39, "offset": 0}, "end": {"line": 151, "col": 42, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 165, "col": 40, "offset": 0}, "end": {"line": 165, "col": 43, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 166, "col": 40, "offset": 0}, "end": {"line": 166, "col": 43, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php:150:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 150, "col": 39, "offset": 0}, "end": {"line": 150, "col": 42, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 151, "col": 39, "offset": 0}, "end": {"line": 151, "col": 42, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 165, "col": 40, "offset": 0}, "end": {"line": 165, "col": 43, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "start": {"line": 166, "col": 40, "offset": 0}, "end": {"line": 166, "col": 43, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php", "start": {"line": 14, "col": 16, "offset": 0}, "end": {"line": 14, "col": 22, "offset": 6}}, {"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php", "start": {"line": 16, "col": 16, "offset": 0}, "end": {"line": 16, "col": 19, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php:14:\n `string` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php", "start": {"line": 14, "col": 16, "offset": 0}, "end": {"line": 14, "col": 22, "offset": 6}}, {"file": "downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php", "start": {"line": 16, "col": 16, "offset": 0}, "end": {"line": 16, "col": 19, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 14, "col": 33, "offset": 166}}, {"path": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 110, "col": 5, "offset": 0}, "end": {"line": 110, "col": 18, "offset": 13}}, {"path": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 119, "col": 5, "offset": 0}, "end": {"line": 119, "col": 18, "offset": 13}}, {"path": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 126, "col": 1, "offset": 0}, "end": {"line": 126, "col": 2, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php:7:\n `enum MediaEngine: string\n{\n    case GD = \"gd\";\n    case IMAGICK = \"convert\";\n    case FFMPEG = \"ffmpeg\";\n    case STATIC = \"static\";\n\n    public const IMAGE_ENGINES =` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 14, "col": 33, "offset": 166}}, {"file": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 110, "col": 5, "offset": 0}, "end": {"line": 110, "col": 18, "offset": 13}}, {"file": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 119, "col": 5, "offset": 0}, "end": {"line": 119, "col": 18, "offset": 13}}, {"file": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "start": {"line": 126, "col": 1, "offset": 0}, "end": {"line": 126, "col": 2, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 14, "col": 39, "offset": 203}}, {"path": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 37, "col": 5, "offset": 0}, "end": {"line": 37, "col": 18, "offset": 13}}, {"path": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 43, "col": 5, "offset": 0}, "end": {"line": 43, "col": 18, "offset": 13}}, {"path": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 48, "col": 1, "offset": 0}, "end": {"line": 48, "col": 2, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php:7:\n `enum VideoContainer: string\n{\n    case WEBM = MimeType::WEBM;\n    case MP4 = MimeType::MP4_VIDEO;\n    case OGG = MimeType::OGG_VIDEO;\n    case MKV = MimeType::MKV;\n\n    public const VIDEO_CODEC_SUPPORT =` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 14, "col": 39, "offset": 203}}, {"file": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 37, "col": 5, "offset": 0}, "end": {"line": 37, "col": 18, "offset": 13}}, {"file": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 43, "col": 5, "offset": 0}, "end": {"line": 43, "col": 18, "offset": 13}}, {"file": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "start": {"line": 48, "col": 1, "offset": 0}, "end": {"line": 48, "col": 2, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Page/Cookie.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Page/Cookie.php:7:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Page/Cookie.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Page/Cookie.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 17, "col": 25, "offset": 0}, "end": {"line": 17, "col": 28, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 18, "col": 25, "offset": 0}, "end": {"line": 18, "col": 31, "offset": 6}}, {"path": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 20, "col": 16, "offset": 0}, "end": {"line": 20, "col": 24, "offset": 8}}, {"path": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 21, "col": 25, "offset": 0}, "end": {"line": 21, "col": 28, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Page/NavLink.php:17:\n `Url` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 17, "col": 25, "offset": 0}, "end": {"line": 17, "col": 28, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 18, "col": 25, "offset": 0}, "end": {"line": 18, "col": 31, "offset": 6}}, {"file": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 20, "col": 16, "offset": 0}, "end": {"line": 20, "col": 24, "offset": 8}}, {"file": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "start": {"line": 21, "col": 25, "offset": 0}, "end": {"line": 21, "col": 28, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Page/Page_Page.php", "start": {"line": 98, "col": 41, "offset": 0}, "end": {"line": 98, "col": 44, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Page/Page_Page.php:98:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Page/Page_Page.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Page/Page_Page.php", "start": {"line": 98, "col": 41, "offset": 0}, "end": {"line": 98, "col": 44, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Search/ImgCondition.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Search/ImgCondition.php:12:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Search/ImgCondition.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Search/ImgCondition.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php", "start": {"line": 14, "col": 25, "offset": 0}, "end": {"line": 14, "col": 30, "offset": 5}}, {"path": "downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php", "start": {"line": 15, "col": 25, "offset": 0}, "end": {"line": 15, "col": 30, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php:14:\n `array` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php", "start": {"line": 14, "col": 25, "offset": 0}, "end": {"line": 14, "col": 30, "offset": 5}}, {"file": "downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php", "start": {"line": 15, "col": 25, "offset": 0}, "end": {"line": 15, "col": 30, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Search/TagCondition.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Search/TagCondition.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Search/TagCondition.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Search/TagCondition.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/User/PermissionMeta.php", "start": {"line": 8, "col": 7, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/User/PermissionMeta.php:8:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/User/PermissionMeta.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/User/PermissionMeta.php", "start": {"line": 8, "col": 7, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 16, "col": 21, "offset": 0}, "end": {"line": 16, "col": 26, "offset": 5}}, {"path": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 23, "col": 16, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 15}}, {"path": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 31, "offset": 5}}, {"path": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 26, "col": 25, "offset": 0}, "end": {"line": 26, "col": 31, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/User/UserClass.php:16:\n `array` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 16, "col": 21, "offset": 0}, "end": {"line": 16, "col": 26, "offset": 5}}, {"file": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 23, "col": 16, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 15}}, {"file": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 31, "offset": 5}}, {"file": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "start": {"line": 26, "col": 25, "offset": 0}, "end": {"line": 26, "col": 31, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Util/Path.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Util/Path.php:7:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Util/Path.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Util/Path.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Util/Url.php", "start": {"line": 15, "col": 7, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Util/Url.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Util/Url.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Util/Url.php", "start": {"line": 15, "col": 7, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Util/polyfills.php", "start": {"line": 547, "col": 59, "offset": 0}, "end": {"line": 547, "col": 62, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Util/polyfills.php:547:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Util/polyfills.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Util/polyfills.php", "start": {"line": 547, "col": 59, "offset": 0}, "end": {"line": 547, "col": 62, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/core/Util/util.php", "start": {"line": 286, "col": 51, "offset": 0}, "end": {"line": 286, "col": 54, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/core/Util/util.php:286:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/core/Util/util.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/core/Util/util.php", "start": {"line": 286, "col": 51, "offset": 0}, "end": {"line": 286, "col": 54, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php", "start": {"line": 265, "col": 79, "offset": 0}, "end": {"line": 265, "col": 82, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php", "start": {"line": 277, "col": 47, "offset": 0}, "end": {"line": 277, "col": 50, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php:265:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php", "start": {"line": 265, "col": 79, "offset": 0}, "end": {"line": 265, "col": 82, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php", "start": {"line": 277, "col": 47, "offset": 0}, "end": {"line": 277, "col": 50, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}, {"path": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 101, "col": 44, "offset": 0}, "end": {"line": 101, "col": 47, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 278, "col": 50, "offset": 0}, "end": {"line": 278, "col": 53, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 281, "col": 59, "offset": 0}, "end": {"line": 281, "col": 62, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php:12:\n `readonly` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}, {"file": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 101, "col": 44, "offset": 0}, "end": {"line": 101, "col": 47, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 278, "col": 50, "offset": 0}, "end": {"line": 278, "col": 53, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "start": {"line": 281, "col": 59, "offset": 0}, "end": {"line": 281, "col": 62, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/graphql/main.php", "start": {"line": 21, "col": 16, "offset": 0}, "end": {"line": 21, "col": 22, "offset": 6}}, {"path": "downloaded_repos/shish_shimmie2/ext/graphql/main.php", "start": {"line": 23, "col": 16, "offset": 0}, "end": {"line": 23, "col": 22, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/graphql/main.php:21:\n `string` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/graphql/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/graphql/main.php", "start": {"line": 21, "col": 16, "offset": 0}, "end": {"line": 21, "col": 22, "offset": 6}}, {"file": "downloaded_repos/shish_shimmie2/ext/graphql/main.php", "start": {"line": 23, "col": 16, "offset": 0}, "end": {"line": 23, "col": 22, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/log_db/main.php", "start": {"line": 157, "col": 31, "offset": 0}, "end": {"line": 157, "col": 34, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/log_db/main.php:157:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/log_db/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/log_db/main.php", "start": {"line": 157, "col": 31, "offset": 0}, "end": {"line": 157, "col": 34, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "start": {"line": 31, "col": 16, "offset": 0}, "end": {"line": 31, "col": 22, "offset": 6}}, {"path": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "start": {"line": 33, "col": 16, "offset": 0}, "end": {"line": 33, "col": 22, "offset": 6}}, {"path": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "start": {"line": 35, "col": 16, "offset": 0}, "end": {"line": 35, "col": 20, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/pm/main.php:31:\n `string` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "start": {"line": 31, "col": 16, "offset": 0}, "end": {"line": 31, "col": 22, "offset": 6}}, {"file": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "start": {"line": 33, "col": 16, "offset": 0}, "end": {"line": 33, "col": 22, "offset": 6}}, {"file": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "start": {"line": 35, "col": 16, "offset": 0}, "end": {"line": 35, "col": 20, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 228, "col": 59, "offset": 0}, "end": {"line": 228, "col": 62, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 317, "col": 43, "offset": 0}, "end": {"line": 317, "col": 46, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 502, "col": 92, "offset": 0}, "end": {"line": 502, "col": 95, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 509, "col": 99, "offset": 0}, "end": {"line": 509, "col": 102, "offset": 3}}, {"path": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 628, "col": 33, "offset": 0}, "end": {"line": 628, "col": 36, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/pools/main.php:228:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 228, "col": 59, "offset": 0}, "end": {"line": 228, "col": 62, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 317, "col": 43, "offset": 0}, "end": {"line": 317, "col": 46, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 502, "col": 92, "offset": 0}, "end": {"line": 502, "col": 95, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 509, "col": 99, "offset": 0}, "end": {"line": 509, "col": 102, "offset": 3}}, {"file": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "start": {"line": 628, "col": 33, "offset": 0}, "end": {"line": 628, "col": 36, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/post_tags/main.php", "start": {"line": 296, "col": 61, "offset": 0}, "end": {"line": 296, "col": 64, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/post_tags/main.php:296:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/post_tags/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/post_tags/main.php", "start": {"line": 296, "col": 61, "offset": 0}, "end": {"line": 296, "col": 64, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/setup/theme.php", "start": {"line": 25, "col": 42, "offset": 0}, "end": {"line": 25, "col": 45, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/setup/theme.php:25:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/setup/theme.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/setup/theme.php", "start": {"line": 25, "col": 42, "offset": 0}, "end": {"line": 25, "col": 45, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/tag_list/theme.php", "start": {"line": 201, "col": 38, "offset": 0}, "end": {"line": 201, "col": 41, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/tag_list/theme.php:201:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/tag_list/theme.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/tag_list/theme.php", "start": {"line": 201, "col": 38, "offset": 0}, "end": {"line": 201, "col": 41, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/user/main.php", "start": {"line": 75, "col": 16, "offset": 0}, "end": {"line": 75, "col": 20, "offset": 4}}, {"path": "downloaded_repos/shish_shimmie2/ext/user/main.php", "start": {"line": 77, "col": 9, "offset": 0}, "end": {"line": 77, "col": 15, "offset": 6}}, {"path": "downloaded_repos/shish_shimmie2/ext/user/main.php", "start": {"line": 79, "col": 9, "offset": 0}, "end": {"line": 79, "col": 15, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/user/main.php:75:\n `User` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/user/main.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/user/main.php", "start": {"line": 75, "col": 16, "offset": 0}, "end": {"line": 75, "col": 20, "offset": 4}}, {"file": "downloaded_repos/shish_shimmie2/ext/user/main.php", "start": {"line": 77, "col": 9, "offset": 0}, "end": {"line": 77, "col": 15, "offset": 6}}, {"file": "downloaded_repos/shish_shimmie2/ext/user/main.php", "start": {"line": 79, "col": 9, "offset": 0}, "end": {"line": 79, "col": 15, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/shish_shimmie2/ext/user_config/theme.php", "start": {"line": 42, "col": 42, "offset": 0}, "end": {"line": 42, "col": 45, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/shish_shimmie2/ext/user_config/theme.php:42:\n `...` was unexpected", "path": "downloaded_repos/shish_shimmie2/ext/user_config/theme.php", "spans": [{"file": "downloaded_repos/shish_shimmie2/ext/user_config/theme.php", "start": {"line": 42, "col": 42, "offset": 0}, "end": {"line": 42, "col": 45, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/shish_shimmie2/.devcontainer/bash_history", "downloaded_repos/shish_shimmie2/.devcontainer/devcontainer.json", "downloaded_repos/shish_shimmie2/.devcontainer/manual.sh", "downloaded_repos/shish_shimmie2/.docker/entrypoint.sh", "downloaded_repos/shish_shimmie2/.docker/nginx-keyring.gpg", "downloaded_repos/shish_shimmie2/.docker/run.php", "downloaded_repos/shish_shimmie2/.dockerignore", "downloaded_repos/shish_shimmie2/.editorconfig", "downloaded_repos/shish_shimmie2/.gitattributes", "downloaded_repos/shish_shimmie2/.github/CONTRIBUTING.md", "downloaded_repos/shish_shimmie2/.github/FUNDING.yml", "downloaded_repos/shish_shimmie2/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/shish_shimmie2/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/shish_shimmie2/.github/dependabot.yml", "downloaded_repos/shish_shimmie2/.github/get-tags.py", "downloaded_repos/shish_shimmie2/.github/setup-db.sh", "downloaded_repos/shish_shimmie2/.github/workflows/main.yml", "downloaded_repos/shish_shimmie2/.github/workflows/release.yml", "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "downloaded_repos/shish_shimmie2/.gitignore", "downloaded_repos/shish_shimmie2/.htaccess", "downloaded_repos/shish_shimmie2/.php-cs-fixer.dist.php", "downloaded_repos/shish_shimmie2/Dockerfile", "downloaded_repos/shish_shimmie2/LICENSE.txt", "downloaded_repos/shish_shimmie2/README.md", "downloaded_repos/shish_shimmie2/composer.json", "downloaded_repos/shish_shimmie2/composer.lock", "downloaded_repos/shish_shimmie2/core/Cache/EventTracingCache.php", "downloaded_repos/shish_shimmie2/core/Cache/EventTracingCacheTest.php", "downloaded_repos/shish_shimmie2/core/Cache/README.md", "downloaded_repos/shish_shimmie2/core/Config/BaseConfigGroup.php", "downloaded_repos/shish_shimmie2/core/Config/BaseConfigGroupTest.php", "downloaded_repos/shish_shimmie2/core/Config/Config.php", "downloaded_repos/shish_shimmie2/core/Config/ConfigException.php", "downloaded_repos/shish_shimmie2/core/Config/ConfigGroup.php", "downloaded_repos/shish_shimmie2/core/Config/ConfigInput.php", "downloaded_repos/shish_shimmie2/core/Config/ConfigMeta.php", "downloaded_repos/shish_shimmie2/core/Config/ConfigTest.php", "downloaded_repos/shish_shimmie2/core/Config/ConfigType.php", "downloaded_repos/shish_shimmie2/core/Config/DatabaseConfig.php", "downloaded_repos/shish_shimmie2/core/Config/DatabaseUserConfig.php", "downloaded_repos/shish_shimmie2/core/Config/README.md", "downloaded_repos/shish_shimmie2/core/Config/SysConfig.php", "downloaded_repos/shish_shimmie2/core/Config/UserConfigGroup.php", "downloaded_repos/shish_shimmie2/core/Crud/AutoCompleteColumn.php", "downloaded_repos/shish_shimmie2/core/Crud/BBCodeColumn.php", "downloaded_repos/shish_shimmie2/core/Crud/ShortDateTimeColumn.php", "downloaded_repos/shish_shimmie2/core/Database/DBEngine.php", "downloaded_repos/shish_shimmie2/core/Database/Database.php", "downloaded_repos/shish_shimmie2/core/Database/DatabaseDriverID.php", "downloaded_repos/shish_shimmie2/core/Database/DatabaseException.php", "downloaded_repos/shish_shimmie2/core/Database/DatabaseExceptionTest.php", "downloaded_repos/shish_shimmie2/core/Database/DatabaseTest.php", "downloaded_repos/shish_shimmie2/core/Database/MySQL.php", "downloaded_repos/shish_shimmie2/core/Database/PostgreSQL.php", "downloaded_repos/shish_shimmie2/core/Database/README.md", "downloaded_repos/shish_shimmie2/core/Database/SQLTest.php", "downloaded_repos/shish_shimmie2/core/Database/SQLite.php", "downloaded_repos/shish_shimmie2/core/Events/BuildAvatarEvent.php", "downloaded_repos/shish_shimmie2/core/Events/BuildCaptchaEvent.php", "downloaded_repos/shish_shimmie2/core/Events/CheckCaptchaEvent.php", "downloaded_repos/shish_shimmie2/core/Events/CliGenEvent.php", "downloaded_repos/shish_shimmie2/core/Events/DatabaseUpgradeEvent.php", "downloaded_repos/shish_shimmie2/core/Events/DatabaseUpgradeEventTest.php", "downloaded_repos/shish_shimmie2/core/Events/Event.php", "downloaded_repos/shish_shimmie2/core/Events/EventBus.php", "downloaded_repos/shish_shimmie2/core/Events/EventBusTest.php", "downloaded_repos/shish_shimmie2/core/Events/InitExtEvent.php", "downloaded_repos/shish_shimmie2/core/Events/InitExtEventTest.php", "downloaded_repos/shish_shimmie2/core/Events/LogEvent.php", "downloaded_repos/shish_shimmie2/core/Events/PageRequestEvent.php", "downloaded_repos/shish_shimmie2/core/Events/PageRequestEventTest.php", "downloaded_repos/shish_shimmie2/core/Events/PartListBuildingEvent.php", "downloaded_repos/shish_shimmie2/core/Events/TextFormattingEvent.php", "downloaded_repos/shish_shimmie2/core/Events/TimeoutException.php", "downloaded_repos/shish_shimmie2/core/Exceptions/FetchException.php", "downloaded_repos/shish_shimmie2/core/Exceptions/HistoryNotFound.php", "downloaded_repos/shish_shimmie2/core/Exceptions/InstallerException.php", "downloaded_repos/shish_shimmie2/core/Exceptions/InstallerExceptionTest.php", "downloaded_repos/shish_shimmie2/core/Exceptions/InvalidInput.php", "downloaded_repos/shish_shimmie2/core/Exceptions/ObjectNotFound.php", "downloaded_repos/shish_shimmie2/core/Exceptions/PermissionDenied.php", "downloaded_repos/shish_shimmie2/core/Exceptions/PostNotFound.php", "downloaded_repos/shish_shimmie2/core/Exceptions/SCoreException.php", "downloaded_repos/shish_shimmie2/core/Exceptions/ServerError.php", "downloaded_repos/shish_shimmie2/core/Exceptions/UserError.php", "downloaded_repos/shish_shimmie2/core/Exceptions/UserNotFound.php", "downloaded_repos/shish_shimmie2/core/Extension/AvatarExtension.php", "downloaded_repos/shish_shimmie2/core/Extension/CaptchaExtension.php", "downloaded_repos/shish_shimmie2/core/Extension/Ctx.php", "downloaded_repos/shish_shimmie2/core/Extension/DataHandlerExtension.php", "downloaded_repos/shish_shimmie2/core/Extension/Enablable.php", "downloaded_repos/shish_shimmie2/core/Extension/EnablableTest.php", "downloaded_repos/shish_shimmie2/core/Extension/Extension.php", "downloaded_repos/shish_shimmie2/core/Extension/ExtensionCategory.php", "downloaded_repos/shish_shimmie2/core/Extension/ExtensionInfo.php", "downloaded_repos/shish_shimmie2/core/Extension/ExtensionNotFound.php", "downloaded_repos/shish_shimmie2/core/Extension/ExtensionVisibility.php", "downloaded_repos/shish_shimmie2/core/Extension/FormatterExtension.php", "downloaded_repos/shish_shimmie2/core/Extension/README.md", "downloaded_repos/shish_shimmie2/core/ImageBoard/Image.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ImageAdditionEvent.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ImageDeletionEvent.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ImagePropType.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ImageReplaceEvent.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ImageReplaceException.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ImageTest.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ParseLinkTemplateEvent.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/TagTest.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ThumbnailGenerationEvent.php", "downloaded_repos/shish_shimmie2/core/ImageBoard/ThumbnailUtil.php", "downloaded_repos/shish_shimmie2/core/Media/FileExtension.php", "downloaded_repos/shish_shimmie2/core/Media/InsufficientMemoryException.php", "downloaded_repos/shish_shimmie2/core/Media/MediaCheckPropertiesEvent.php", "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "downloaded_repos/shish_shimmie2/core/Media/MediaException.php", "downloaded_repos/shish_shimmie2/core/Media/MediaResizeEvent.php", "downloaded_repos/shish_shimmie2/core/Media/MimeMap.php", "downloaded_repos/shish_shimmie2/core/Media/MimeType.php", "downloaded_repos/shish_shimmie2/core/Media/README.md", "downloaded_repos/shish_shimmie2/core/Media/ResizeType.php", "downloaded_repos/shish_shimmie2/core/Media/VideoCodec.php", "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "downloaded_repos/shish_shimmie2/core/Page/Block.php", "downloaded_repos/shish_shimmie2/core/Page/BlockTest.php", "downloaded_repos/shish_shimmie2/core/Page/CommonElementsTheme.php", "downloaded_repos/shish_shimmie2/core/Page/Cookie.php", "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "downloaded_repos/shish_shimmie2/core/Page/NavLinkTest.php", "downloaded_repos/shish_shimmie2/core/Page/Page.php", "downloaded_repos/shish_shimmie2/core/Page/PageMode.php", "downloaded_repos/shish_shimmie2/core/Page/PageNavBuildingEvent.php", "downloaded_repos/shish_shimmie2/core/Page/PageSubNavBuildingEvent.php", "downloaded_repos/shish_shimmie2/core/Page/PageTest.php", "downloaded_repos/shish_shimmie2/core/Page/Page_Data.php", "downloaded_repos/shish_shimmie2/core/Page/Page_Error.php", "downloaded_repos/shish_shimmie2/core/Page/Page_File.php", "downloaded_repos/shish_shimmie2/core/Page/Page_Page.php", "downloaded_repos/shish_shimmie2/core/Page/Page_Redirect.php", "downloaded_repos/shish_shimmie2/core/Page/README.md", "downloaded_repos/shish_shimmie2/core/Page/Themelet.php", "downloaded_repos/shish_shimmie2/core/Page/WithFlash.php", "downloaded_repos/shish_shimmie2/core/Search/ImgCondition.php", "downloaded_repos/shish_shimmie2/core/Search/Querylet.php", "downloaded_repos/shish_shimmie2/core/Search/Search.php", "downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php", "downloaded_repos/shish_shimmie2/core/Search/SearchParametersTest.php", "downloaded_repos/shish_shimmie2/core/Search/SearchTerm.php", "downloaded_repos/shish_shimmie2/core/Search/SearchTest.php", "downloaded_repos/shish_shimmie2/core/Search/TagCondition.php", "downloaded_repos/shish_shimmie2/core/Testing/ConfigGetReturnTypeExtension.php", "downloaded_repos/shish_shimmie2/core/Testing/MyExampleConfig.php", "downloaded_repos/shish_shimmie2/core/Testing/MyExamplePermission.php", "downloaded_repos/shish_shimmie2/core/Testing/ShimmiePHPUnitTestCase.php", "downloaded_repos/shish_shimmie2/core/Testing/TestConfig.php", "downloaded_repos/shish_shimmie2/core/User/PermissionGroup.php", "downloaded_repos/shish_shimmie2/core/User/PermissionGroupTest.php", "downloaded_repos/shish_shimmie2/core/User/PermissionMeta.php", "downloaded_repos/shish_shimmie2/core/User/README.md", "downloaded_repos/shish_shimmie2/core/User/User.php", "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "downloaded_repos/shish_shimmie2/core/User/UserClassSource.php", "downloaded_repos/shish_shimmie2/core/User/UserClassTest.php", "downloaded_repos/shish_shimmie2/core/Util/Captcha.php", "downloaded_repos/shish_shimmie2/core/Util/CliApp.php", "downloaded_repos/shish_shimmie2/core/Util/CommandBuilder.php", "downloaded_repos/shish_shimmie2/core/Util/Filesystem.php", "downloaded_repos/shish_shimmie2/core/Util/FilesystemTest.php", "downloaded_repos/shish_shimmie2/core/Util/Installer.php", "downloaded_repos/shish_shimmie2/core/Util/LoadBalancer.php", "downloaded_repos/shish_shimmie2/core/Util/LoadBalancerTest.php", "downloaded_repos/shish_shimmie2/core/Util/Log.php", "downloaded_repos/shish_shimmie2/core/Util/LogLevel.php", "downloaded_repos/shish_shimmie2/core/Util/MicroHTMLTest.php", "downloaded_repos/shish_shimmie2/core/Util/Network.php", "downloaded_repos/shish_shimmie2/core/Util/NetworkTest.php", "downloaded_repos/shish_shimmie2/core/Util/Path.php", "downloaded_repos/shish_shimmie2/core/Util/PathTest.php", "downloaded_repos/shish_shimmie2/core/Util/PolyfillsTest.php", "downloaded_repos/shish_shimmie2/core/Util/QueryArray.php", "downloaded_repos/shish_shimmie2/core/Util/Url.php", "downloaded_repos/shish_shimmie2/core/Util/UrlTest.php", "downloaded_repos/shish_shimmie2/core/Util/UtilTest.php", "downloaded_repos/shish_shimmie2/core/Util/microhtml.php", "downloaded_repos/shish_shimmie2/core/Util/polyfills.php", "downloaded_repos/shish_shimmie2/core/Util/util.php", "downloaded_repos/shish_shimmie2/ext/admin/info.php", "downloaded_repos/shish_shimmie2/ext/admin/main.php", "downloaded_repos/shish_shimmie2/ext/admin/permissions.php", "downloaded_repos/shish_shimmie2/ext/admin/style.css", "downloaded_repos/shish_shimmie2/ext/admin/test.php", "downloaded_repos/shish_shimmie2/ext/admin/theme.php", "downloaded_repos/shish_shimmie2/ext/alias_editor/info.php", "downloaded_repos/shish_shimmie2/ext/alias_editor/main.php", "downloaded_repos/shish_shimmie2/ext/alias_editor/permissions.php", "downloaded_repos/shish_shimmie2/ext/alias_editor/test.php", "downloaded_repos/shish_shimmie2/ext/alias_editor/theme.php", "downloaded_repos/shish_shimmie2/ext/approval/info.php", "downloaded_repos/shish_shimmie2/ext/approval/main.php", "downloaded_repos/shish_shimmie2/ext/approval/permissions.php", "downloaded_repos/shish_shimmie2/ext/approval/test.php", "downloaded_repos/shish_shimmie2/ext/approval/theme.php", "downloaded_repos/shish_shimmie2/ext/artists/config.php", "downloaded_repos/shish_shimmie2/ext/artists/info.php", "downloaded_repos/shish_shimmie2/ext/artists/main.php", "downloaded_repos/shish_shimmie2/ext/artists/permissions.php", "downloaded_repos/shish_shimmie2/ext/artists/test.php", "downloaded_repos/shish_shimmie2/ext/artists/theme.php", "downloaded_repos/shish_shimmie2/ext/auto_tagger/config.php", "downloaded_repos/shish_shimmie2/ext/auto_tagger/info.php", "downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php", "downloaded_repos/shish_shimmie2/ext/auto_tagger/permissions.php", "downloaded_repos/shish_shimmie2/ext/auto_tagger/test.php", "downloaded_repos/shish_shimmie2/ext/auto_tagger/theme.php", "downloaded_repos/shish_shimmie2/ext/autocomplete/info.php", "downloaded_repos/shish_shimmie2/ext/autocomplete/main.php", "downloaded_repos/shish_shimmie2/ext/autocomplete/script.js", "downloaded_repos/shish_shimmie2/ext/autocomplete/style.css", "downloaded_repos/shish_shimmie2/ext/autocomplete/test.php", "downloaded_repos/shish_shimmie2/ext/avatar_gravatar/config.php", "downloaded_repos/shish_shimmie2/ext/avatar_gravatar/info.php", "downloaded_repos/shish_shimmie2/ext/avatar_gravatar/main.php", "downloaded_repos/shish_shimmie2/ext/avatar_post/config.php", "downloaded_repos/shish_shimmie2/ext/avatar_post/info.php", "downloaded_repos/shish_shimmie2/ext/avatar_post/main.php", "downloaded_repos/shish_shimmie2/ext/avatar_post/script.js", "downloaded_repos/shish_shimmie2/ext/avatar_post/style.css", "downloaded_repos/shish_shimmie2/ext/avatar_post/theme.php", "downloaded_repos/shish_shimmie2/ext/ban_words/config.php", "downloaded_repos/shish_shimmie2/ext/ban_words/info.php", "downloaded_repos/shish_shimmie2/ext/ban_words/main.php", "downloaded_repos/shish_shimmie2/ext/ban_words/test.php", "downloaded_repos/shish_shimmie2/ext/bbcode/info.php", "downloaded_repos/shish_shimmie2/ext/bbcode/main.php", "downloaded_repos/shish_shimmie2/ext/bbcode/script.js", "downloaded_repos/shish_shimmie2/ext/bbcode/style.css", "downloaded_repos/shish_shimmie2/ext/bbcode/test.php", "downloaded_repos/shish_shimmie2/ext/biography/config.php", "downloaded_repos/shish_shimmie2/ext/biography/info.php", "downloaded_repos/shish_shimmie2/ext/biography/main.php", "downloaded_repos/shish_shimmie2/ext/biography/test.php", "downloaded_repos/shish_shimmie2/ext/biography/theme.php", "downloaded_repos/shish_shimmie2/ext/blocks/info.php", "downloaded_repos/shish_shimmie2/ext/blocks/main.php", "downloaded_repos/shish_shimmie2/ext/blocks/permissions.php", "downloaded_repos/shish_shimmie2/ext/blocks/test.php", "downloaded_repos/shish_shimmie2/ext/blocks/theme.php", "downloaded_repos/shish_shimmie2/ext/blotter/config.php", "downloaded_repos/shish_shimmie2/ext/blotter/info.php", "downloaded_repos/shish_shimmie2/ext/blotter/main.php", "downloaded_repos/shish_shimmie2/ext/blotter/permissions.php", "downloaded_repos/shish_shimmie2/ext/blotter/script.js", "downloaded_repos/shish_shimmie2/ext/blotter/style.css", "downloaded_repos/shish_shimmie2/ext/blotter/test.php", "downloaded_repos/shish_shimmie2/ext/blotter/theme.php", "downloaded_repos/shish_shimmie2/ext/bone_quality/config.php", "downloaded_repos/shish_shimmie2/ext/bone_quality/images/boned.jpg", "downloaded_repos/shish_shimmie2/ext/bone_quality/info.php", "downloaded_repos/shish_shimmie2/ext/bone_quality/main.php", "downloaded_repos/shish_shimmie2/ext/bone_quality/style.css", "downloaded_repos/shish_shimmie2/ext/bone_quality/test.php", "downloaded_repos/shish_shimmie2/ext/bone_quality/theme.php", "downloaded_repos/shish_shimmie2/ext/browser_search/config.php", "downloaded_repos/shish_shimmie2/ext/browser_search/info.php", "downloaded_repos/shish_shimmie2/ext/browser_search/main.php", "downloaded_repos/shish_shimmie2/ext/browser_search/test.php", "downloaded_repos/shish_shimmie2/ext/bulk_actions/info.php", "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "downloaded_repos/shish_shimmie2/ext/bulk_actions/permissions.php", "downloaded_repos/shish_shimmie2/ext/bulk_actions/script.js", "downloaded_repos/shish_shimmie2/ext/bulk_actions/style.css", "downloaded_repos/shish_shimmie2/ext/bulk_actions/theme.php", "downloaded_repos/shish_shimmie2/ext/bulk_add/info.php", "downloaded_repos/shish_shimmie2/ext/bulk_add/main.php", "downloaded_repos/shish_shimmie2/ext/bulk_add/permissions.php", "downloaded_repos/shish_shimmie2/ext/bulk_add/test.php", "downloaded_repos/shish_shimmie2/ext/bulk_add/theme.php", "downloaded_repos/shish_shimmie2/ext/bulk_add_csv/info.php", "downloaded_repos/shish_shimmie2/ext/bulk_add_csv/main.php", "downloaded_repos/shish_shimmie2/ext/bulk_add_csv/theme.php", "downloaded_repos/shish_shimmie2/ext/bulk_download/config.php", "downloaded_repos/shish_shimmie2/ext/bulk_download/info.php", "downloaded_repos/shish_shimmie2/ext/bulk_download/main.php", "downloaded_repos/shish_shimmie2/ext/bulk_download/permissions.php", "downloaded_repos/shish_shimmie2/ext/bulk_import_export/events.php", "downloaded_repos/shish_shimmie2/ext/bulk_import_export/info.php", "downloaded_repos/shish_shimmie2/ext/bulk_import_export/main.php", "downloaded_repos/shish_shimmie2/ext/bulk_import_export/permissions.php", "downloaded_repos/shish_shimmie2/ext/bulk_parent_child/info.php", "downloaded_repos/shish_shimmie2/ext/bulk_parent_child/main.php", "downloaded_repos/shish_shimmie2/ext/bulk_parent_child/permissions.php", "downloaded_repos/shish_shimmie2/ext/comment/config.php", "downloaded_repos/shish_shimmie2/ext/comment/info.php", "downloaded_repos/shish_shimmie2/ext/comment/main.php", "downloaded_repos/shish_shimmie2/ext/comment/permissions.php", "downloaded_repos/shish_shimmie2/ext/comment/script.js", "downloaded_repos/shish_shimmie2/ext/comment/style.css", "downloaded_repos/shish_shimmie2/ext/comment/test.php", "downloaded_repos/shish_shimmie2/ext/comment/theme.php", "downloaded_repos/shish_shimmie2/ext/cron_uploader/config.php", "downloaded_repos/shish_shimmie2/ext/cron_uploader/info.php", "downloaded_repos/shish_shimmie2/ext/cron_uploader/main.php", "downloaded_repos/shish_shimmie2/ext/cron_uploader/permissions.php", "downloaded_repos/shish_shimmie2/ext/cron_uploader/style.css", "downloaded_repos/shish_shimmie2/ext/cron_uploader/theme.php", "downloaded_repos/shish_shimmie2/ext/custom_html_headers/config.php", "downloaded_repos/shish_shimmie2/ext/custom_html_headers/info.php", "downloaded_repos/shish_shimmie2/ext/custom_html_headers/main.php", "downloaded_repos/shish_shimmie2/ext/danbooru_api/info.php", "downloaded_repos/shish_shimmie2/ext/danbooru_api/main.php", "downloaded_repos/shish_shimmie2/ext/danbooru_api/test.php", "downloaded_repos/shish_shimmie2/ext/download/events.php", "downloaded_repos/shish_shimmie2/ext/download/info.php", "downloaded_repos/shish_shimmie2/ext/download/main.php", "downloaded_repos/shish_shimmie2/ext/download/test.php", "downloaded_repos/shish_shimmie2/ext/downtime/config.php", "downloaded_repos/shish_shimmie2/ext/downtime/info.php", "downloaded_repos/shish_shimmie2/ext/downtime/main.php", "downloaded_repos/shish_shimmie2/ext/downtime/permissions.php", "downloaded_repos/shish_shimmie2/ext/downtime/style.css", "downloaded_repos/shish_shimmie2/ext/downtime/test.php", "downloaded_repos/shish_shimmie2/ext/downtime/theme.php", "downloaded_repos/shish_shimmie2/ext/emoticons/default/arrow.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/biggrin.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/confused.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/cool.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/cry.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/eek.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/evil.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/exclaim.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/frown.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/idea.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/lol.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/mad.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/mrgreen.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/neutral.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/question.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/razz.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/redface.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/rolleyes.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/sad.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/smile.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/surprised.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/twisted.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/default/wink.gif", "downloaded_repos/shish_shimmie2/ext/emoticons/info.php", "downloaded_repos/shish_shimmie2/ext/emoticons/main.php", "downloaded_repos/shish_shimmie2/ext/emoticons/test.php", "downloaded_repos/shish_shimmie2/ext/emoticons_list/info.php", "downloaded_repos/shish_shimmie2/ext/emoticons_list/main.php", "downloaded_repos/shish_shimmie2/ext/emoticons_list/theme.php", "downloaded_repos/shish_shimmie2/ext/eokm/config.php", "downloaded_repos/shish_shimmie2/ext/eokm/info.php", "downloaded_repos/shish_shimmie2/ext/eokm/main.php", "downloaded_repos/shish_shimmie2/ext/eokm/test.php", "downloaded_repos/shish_shimmie2/ext/et/info.php", "downloaded_repos/shish_shimmie2/ext/et/main.php", "downloaded_repos/shish_shimmie2/ext/et/permissions.php", "downloaded_repos/shish_shimmie2/ext/et/test.php", "downloaded_repos/shish_shimmie2/ext/et/theme.php", "downloaded_repos/shish_shimmie2/ext/et_server/info.php", "downloaded_repos/shish_shimmie2/ext/et_server/main.php", "downloaded_repos/shish_shimmie2/ext/et_server/permissions.php", "downloaded_repos/shish_shimmie2/ext/et_server/style.css", "downloaded_repos/shish_shimmie2/ext/et_server/test.php", "downloaded_repos/shish_shimmie2/ext/ext_manager/baseline_open_in_new_black_18dp.png", "downloaded_repos/shish_shimmie2/ext/ext_manager/info.php", "downloaded_repos/shish_shimmie2/ext/ext_manager/main.php", "downloaded_repos/shish_shimmie2/ext/ext_manager/permissions.php", "downloaded_repos/shish_shimmie2/ext/ext_manager/test.php", "downloaded_repos/shish_shimmie2/ext/ext_manager/theme.php", "downloaded_repos/shish_shimmie2/ext/favorites/info.php", "downloaded_repos/shish_shimmie2/ext/favorites/main.php", "downloaded_repos/shish_shimmie2/ext/favorites/permissions.php", "downloaded_repos/shish_shimmie2/ext/favorites/test.php", "downloaded_repos/shish_shimmie2/ext/favorites/theme.php", "downloaded_repos/shish_shimmie2/ext/featured/config.php", "downloaded_repos/shish_shimmie2/ext/featured/info.php", "downloaded_repos/shish_shimmie2/ext/featured/main.php", "downloaded_repos/shish_shimmie2/ext/featured/permissions.php", "downloaded_repos/shish_shimmie2/ext/featured/test.php", "downloaded_repos/shish_shimmie2/ext/featured/theme.php", "downloaded_repos/shish_shimmie2/ext/filter/config.php", "downloaded_repos/shish_shimmie2/ext/filter/info.php", "downloaded_repos/shish_shimmie2/ext/filter/main.php", "downloaded_repos/shish_shimmie2/ext/filter/script.js", "downloaded_repos/shish_shimmie2/ext/filter/style.css", "downloaded_repos/shish_shimmie2/ext/filter/theme.php", "downloaded_repos/shish_shimmie2/ext/forum/config.php", "downloaded_repos/shish_shimmie2/ext/forum/info.php", "downloaded_repos/shish_shimmie2/ext/forum/main.php", "downloaded_repos/shish_shimmie2/ext/forum/permissions.php", "downloaded_repos/shish_shimmie2/ext/forum/style.css", "downloaded_repos/shish_shimmie2/ext/forum/test.php", "downloaded_repos/shish_shimmie2/ext/forum/theme.php", "downloaded_repos/shish_shimmie2/ext/four_oh_four/info.php", "downloaded_repos/shish_shimmie2/ext/four_oh_four/main.php", "downloaded_repos/shish_shimmie2/ext/four_oh_four/test.php", "downloaded_repos/shish_shimmie2/ext/google_analytics/config.php", "downloaded_repos/shish_shimmie2/ext/google_analytics/info.php", "downloaded_repos/shish_shimmie2/ext/google_analytics/main.php", "downloaded_repos/shish_shimmie2/ext/graphql/config.php", "downloaded_repos/shish_shimmie2/ext/graphql/info.php", "downloaded_repos/shish_shimmie2/ext/graphql/main.php", "downloaded_repos/shish_shimmie2/ext/graphql/test.php", "downloaded_repos/shish_shimmie2/ext/handle_archive/config.php", "downloaded_repos/shish_shimmie2/ext/handle_archive/info.php", "downloaded_repos/shish_shimmie2/ext/handle_archive/main.php", "downloaded_repos/shish_shimmie2/ext/handle_archive/test.php", "downloaded_repos/shish_shimmie2/ext/handle_cbz/comic.js", "downloaded_repos/shish_shimmie2/ext/handle_cbz/info.php", "downloaded_repos/shish_shimmie2/ext/handle_cbz/main.php", "downloaded_repos/shish_shimmie2/ext/handle_cbz/spinner.gif", "downloaded_repos/shish_shimmie2/ext/handle_cbz/style.css", "downloaded_repos/shish_shimmie2/ext/handle_cbz/theme.php", "downloaded_repos/shish_shimmie2/ext/handle_ico/info.php", "downloaded_repos/shish_shimmie2/ext/handle_ico/main.php", "downloaded_repos/shish_shimmie2/ext/handle_ico/test.php", "downloaded_repos/shish_shimmie2/ext/handle_ico/theme.php", "downloaded_repos/shish_shimmie2/ext/handle_mp3/info.php", "downloaded_repos/shish_shimmie2/ext/handle_mp3/main.php", "downloaded_repos/shish_shimmie2/ext/handle_mp3/script.js", "downloaded_repos/shish_shimmie2/ext/handle_mp3/style.css", "downloaded_repos/shish_shimmie2/ext/handle_mp3/theme.php", "downloaded_repos/shish_shimmie2/ext/handle_mp3/thumb.jpg", "downloaded_repos/shish_shimmie2/ext/handle_pixel/info.php", "downloaded_repos/shish_shimmie2/ext/handle_pixel/main.php", "downloaded_repos/shish_shimmie2/ext/handle_pixel/script.js", "downloaded_repos/shish_shimmie2/ext/handle_pixel/style.css", "downloaded_repos/shish_shimmie2/ext/handle_pixel/test.php", "downloaded_repos/shish_shimmie2/ext/handle_pixel/theme.php", "downloaded_repos/shish_shimmie2/ext/handle_svg/info.php", "downloaded_repos/shish_shimmie2/ext/handle_svg/main.php", "downloaded_repos/shish_shimmie2/ext/handle_svg/test.php", "downloaded_repos/shish_shimmie2/ext/handle_svg/theme.php", "downloaded_repos/shish_shimmie2/ext/handle_svg/thumb.jpg", "downloaded_repos/shish_shimmie2/ext/handle_video/config.php", "downloaded_repos/shish_shimmie2/ext/handle_video/info.php", "downloaded_repos/shish_shimmie2/ext/handle_video/main.php", "downloaded_repos/shish_shimmie2/ext/handle_video/theme.php", "downloaded_repos/shish_shimmie2/ext/handle_video/thumb.jpg", "downloaded_repos/shish_shimmie2/ext/help_pages/baseline_help_outline_black_18dp.png", "downloaded_repos/shish_shimmie2/ext/help_pages/info.php", "downloaded_repos/shish_shimmie2/ext/help_pages/main.php", "downloaded_repos/shish_shimmie2/ext/help_pages/style.css", "downloaded_repos/shish_shimmie2/ext/help_pages/test.php", "downloaded_repos/shish_shimmie2/ext/help_pages/theme.php", "downloaded_repos/shish_shimmie2/ext/holiday/config.php", "downloaded_repos/shish_shimmie2/ext/holiday/info.php", "downloaded_repos/shish_shimmie2/ext/holiday/main.php", "downloaded_repos/shish_shimmie2/ext/holiday/stylesheets/aprilfools.css", "downloaded_repos/shish_shimmie2/ext/home/<USER>", "downloaded_repos/shish_shimmie2/ext/home/<USER>", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/0.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/1.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/2.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/3.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/4.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/5.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/6.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/7.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/8.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/default/9.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/0.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/1.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/2.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/3.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/4.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/5.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/6.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/7.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/8.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>/newcounter/9.gif", "downloaded_repos/shish_shimmie2/ext/home/<USER>", "downloaded_repos/shish_shimmie2/ext/home/<USER>", "downloaded_repos/shish_shimmie2/ext/home/<USER>", "downloaded_repos/shish_shimmie2/ext/home/<USER>", "downloaded_repos/shish_shimmie2/ext/home/<USER>", "downloaded_repos/shish_shimmie2/ext/image/config.php", "downloaded_repos/shish_shimmie2/ext/image/info.php", "downloaded_repos/shish_shimmie2/ext/image/main.php", "downloaded_repos/shish_shimmie2/ext/image/permissions.php", "downloaded_repos/shish_shimmie2/ext/image/test.php", "downloaded_repos/shish_shimmie2/ext/image_hash_ban/info.php", "downloaded_repos/shish_shimmie2/ext/image_hash_ban/main.php", "downloaded_repos/shish_shimmie2/ext/image_hash_ban/permissions.php", "downloaded_repos/shish_shimmie2/ext/image_hash_ban/test.php", "downloaded_repos/shish_shimmie2/ext/image_view_counter/info.php", "downloaded_repos/shish_shimmie2/ext/image_view_counter/main.php", "downloaded_repos/shish_shimmie2/ext/image_view_counter/permissions.php", "downloaded_repos/shish_shimmie2/ext/image_view_counter/test.php", "downloaded_repos/shish_shimmie2/ext/image_view_counter/theme.php", "downloaded_repos/shish_shimmie2/ext/index/config.php", "downloaded_repos/shish_shimmie2/ext/index/events.php", "downloaded_repos/shish_shimmie2/ext/index/info.php", "downloaded_repos/shish_shimmie2/ext/index/main.php", "downloaded_repos/shish_shimmie2/ext/index/permissions.php", "downloaded_repos/shish_shimmie2/ext/index/script.js", "downloaded_repos/shish_shimmie2/ext/index/style.css", "downloaded_repos/shish_shimmie2/ext/index/test.php", "downloaded_repos/shish_shimmie2/ext/index/theme.php", "downloaded_repos/shish_shimmie2/ext/ipban/config.php", "downloaded_repos/shish_shimmie2/ext/ipban/info.php", "downloaded_repos/shish_shimmie2/ext/ipban/main.php", "downloaded_repos/shish_shimmie2/ext/ipban/permissions.php", "downloaded_repos/shish_shimmie2/ext/ipban/test.php", "downloaded_repos/shish_shimmie2/ext/ipban/theme.php", "downloaded_repos/shish_shimmie2/ext/link_image/config.php", "downloaded_repos/shish_shimmie2/ext/link_image/info.php", "downloaded_repos/shish_shimmie2/ext/link_image/main.php", "downloaded_repos/shish_shimmie2/ext/link_image/test.php", "downloaded_repos/shish_shimmie2/ext/link_image/theme.php", "downloaded_repos/shish_shimmie2/ext/link_scan/config.php", "downloaded_repos/shish_shimmie2/ext/link_scan/info.php", "downloaded_repos/shish_shimmie2/ext/link_scan/main.php", "downloaded_repos/shish_shimmie2/ext/link_scan/test.php", "downloaded_repos/shish_shimmie2/ext/livefeed/config.php", "downloaded_repos/shish_shimmie2/ext/livefeed/info.php", "downloaded_repos/shish_shimmie2/ext/livefeed/main.php", "downloaded_repos/shish_shimmie2/ext/log_console/config.php", "downloaded_repos/shish_shimmie2/ext/log_console/info.php", "downloaded_repos/shish_shimmie2/ext/log_console/main.php", "downloaded_repos/shish_shimmie2/ext/log_db/config.php", "downloaded_repos/shish_shimmie2/ext/log_db/info.php", "downloaded_repos/shish_shimmie2/ext/log_db/main.php", "downloaded_repos/shish_shimmie2/ext/log_db/permissions.php", "downloaded_repos/shish_shimmie2/ext/log_db/test.php", "downloaded_repos/shish_shimmie2/ext/log_logstash/config.php", "downloaded_repos/shish_shimmie2/ext/log_logstash/info.php", "downloaded_repos/shish_shimmie2/ext/log_logstash/main.php", "downloaded_repos/shish_shimmie2/ext/log_net/config.php", "downloaded_repos/shish_shimmie2/ext/log_net/info.php", "downloaded_repos/shish_shimmie2/ext/log_net/main.php", "downloaded_repos/shish_shimmie2/ext/media/config.php", "downloaded_repos/shish_shimmie2/ext/media/info.php", "downloaded_repos/shish_shimmie2/ext/media/main.php", "downloaded_repos/shish_shimmie2/ext/media/permissions.php", "downloaded_repos/shish_shimmie2/ext/media/theme.php", "downloaded_repos/shish_shimmie2/ext/mime/info.php", "downloaded_repos/shish_shimmie2/ext/mime/main.php", "downloaded_repos/shish_shimmie2/ext/mime/test.php", "downloaded_repos/shish_shimmie2/ext/mime/theme.php", "downloaded_repos/shish_shimmie2/ext/nav_timing/info.php", "downloaded_repos/shish_shimmie2/ext/nav_timing/init.js", "downloaded_repos/shish_shimmie2/ext/not_a_tag/info.php", "downloaded_repos/shish_shimmie2/ext/not_a_tag/main.php", "downloaded_repos/shish_shimmie2/ext/not_a_tag/test.php", "downloaded_repos/shish_shimmie2/ext/notes/config.php", "downloaded_repos/shish_shimmie2/ext/notes/info.php", "downloaded_repos/shish_shimmie2/ext/notes/main.php", "downloaded_repos/shish_shimmie2/ext/notes/permissions.php", "downloaded_repos/shish_shimmie2/ext/notes/script.js", "downloaded_repos/shish_shimmie2/ext/notes/style.css", "downloaded_repos/shish_shimmie2/ext/notes/test.php", "downloaded_repos/shish_shimmie2/ext/notes/theme.php", "downloaded_repos/shish_shimmie2/ext/numeric_score/info.php", "downloaded_repos/shish_shimmie2/ext/numeric_score/main.php", "downloaded_repos/shish_shimmie2/ext/numeric_score/permissions.php", "downloaded_repos/shish_shimmie2/ext/numeric_score/test.php", "downloaded_repos/shish_shimmie2/ext/numeric_score/theme.php", "downloaded_repos/shish_shimmie2/ext/ouroboros_api/info.php", "downloaded_repos/shish_shimmie2/ext/ouroboros_api/main.php", "downloaded_repos/shish_shimmie2/ext/perm_manager/info.php", "downloaded_repos/shish_shimmie2/ext/perm_manager/main.php", "downloaded_repos/shish_shimmie2/ext/perm_manager/permissions.php", "downloaded_repos/shish_shimmie2/ext/perm_manager/style.css", "downloaded_repos/shish_shimmie2/ext/perm_manager/test.php", "downloaded_repos/shish_shimmie2/ext/perm_manager/theme.php", "downloaded_repos/shish_shimmie2/ext/pm/info.php", "downloaded_repos/shish_shimmie2/ext/pm/main.php", "downloaded_repos/shish_shimmie2/ext/pm/permissions.php", "downloaded_repos/shish_shimmie2/ext/pm/test.php", "downloaded_repos/shish_shimmie2/ext/pm/theme.php", "downloaded_repos/shish_shimmie2/ext/pools/config.php", "downloaded_repos/shish_shimmie2/ext/pools/info.php", "downloaded_repos/shish_shimmie2/ext/pools/main.php", "downloaded_repos/shish_shimmie2/ext/pools/permissions.php", "downloaded_repos/shish_shimmie2/ext/pools/script.js", "downloaded_repos/shish_shimmie2/ext/pools/style.css", "downloaded_repos/shish_shimmie2/ext/pools/test.php", "downloaded_repos/shish_shimmie2/ext/pools/theme.php", "downloaded_repos/shish_shimmie2/ext/post_description/info.php", "downloaded_repos/shish_shimmie2/ext/post_description/main.php", "downloaded_repos/shish_shimmie2/ext/post_description/permissions.php", "downloaded_repos/shish_shimmie2/ext/post_description/test.php", "downloaded_repos/shish_shimmie2/ext/post_description/theme.php", "downloaded_repos/shish_shimmie2/ext/post_lock/info.php", "downloaded_repos/shish_shimmie2/ext/post_lock/main.php", "downloaded_repos/shish_shimmie2/ext/post_lock/permissions.php", "downloaded_repos/shish_shimmie2/ext/post_lock/test.php", "downloaded_repos/shish_shimmie2/ext/post_lock/theme.php", "downloaded_repos/shish_shimmie2/ext/post_owner/info.php", "downloaded_repos/shish_shimmie2/ext/post_owner/main.php", "downloaded_repos/shish_shimmie2/ext/post_owner/permissions.php", "downloaded_repos/shish_shimmie2/ext/post_owner/test.php", "downloaded_repos/shish_shimmie2/ext/post_owner/theme.php", "downloaded_repos/shish_shimmie2/ext/post_peek/info.php", "downloaded_repos/shish_shimmie2/ext/post_peek/main.php", "downloaded_repos/shish_shimmie2/ext/post_peek/script.js", "downloaded_repos/shish_shimmie2/ext/post_peek/theme.php", "downloaded_repos/shish_shimmie2/ext/post_source/info.php", "downloaded_repos/shish_shimmie2/ext/post_source/main.php", "downloaded_repos/shish_shimmie2/ext/post_source/permissions.php", "downloaded_repos/shish_shimmie2/ext/post_source/test.php", "downloaded_repos/shish_shimmie2/ext/post_source/theme.php", "downloaded_repos/shish_shimmie2/ext/post_tags/info.php", "downloaded_repos/shish_shimmie2/ext/post_tags/main.php", "downloaded_repos/shish_shimmie2/ext/post_tags/permissions.php", "downloaded_repos/shish_shimmie2/ext/post_tags/test.php", "downloaded_repos/shish_shimmie2/ext/post_tags/theme.php", "downloaded_repos/shish_shimmie2/ext/post_titles/config.php", "downloaded_repos/shish_shimmie2/ext/post_titles/events/post_title_set_event.php", "downloaded_repos/shish_shimmie2/ext/post_titles/info.php", "downloaded_repos/shish_shimmie2/ext/post_titles/main.php", "downloaded_repos/shish_shimmie2/ext/post_titles/permissions.php", "downloaded_repos/shish_shimmie2/ext/post_titles/theme.php", "downloaded_repos/shish_shimmie2/ext/private_image/config.php", "downloaded_repos/shish_shimmie2/ext/private_image/info.php", "downloaded_repos/shish_shimmie2/ext/private_image/main.php", "downloaded_repos/shish_shimmie2/ext/private_image/permissions.php", "downloaded_repos/shish_shimmie2/ext/private_image/theme.php", "downloaded_repos/shish_shimmie2/ext/random_image/config.php", "downloaded_repos/shish_shimmie2/ext/random_image/info.php", "downloaded_repos/shish_shimmie2/ext/random_image/main.php", "downloaded_repos/shish_shimmie2/ext/random_image/test.php", "downloaded_repos/shish_shimmie2/ext/random_image/theme.php", "downloaded_repos/shish_shimmie2/ext/random_list/config.php", "downloaded_repos/shish_shimmie2/ext/random_list/info.php", "downloaded_repos/shish_shimmie2/ext/random_list/main.php", "downloaded_repos/shish_shimmie2/ext/random_list/theme.php", "downloaded_repos/shish_shimmie2/ext/rating/config.php", "downloaded_repos/shish_shimmie2/ext/rating/info.php", "downloaded_repos/shish_shimmie2/ext/rating/main.php", "downloaded_repos/shish_shimmie2/ext/rating/permissions.php", "downloaded_repos/shish_shimmie2/ext/rating/test.php", "downloaded_repos/shish_shimmie2/ext/rating/theme.php", "downloaded_repos/shish_shimmie2/ext/ratings_blur/config.php", "downloaded_repos/shish_shimmie2/ext/ratings_blur/info.php", "downloaded_repos/shish_shimmie2/ext/ratings_blur/main.php", "downloaded_repos/shish_shimmie2/ext/ratings_blur/style.css", "downloaded_repos/shish_shimmie2/ext/ratings_blur/test.php", "downloaded_repos/shish_shimmie2/ext/recaptcha/config.php", "downloaded_repos/shish_shimmie2/ext/recaptcha/info.php", "downloaded_repos/shish_shimmie2/ext/recaptcha/main.php", "downloaded_repos/shish_shimmie2/ext/regen_thumb/info.php", "downloaded_repos/shish_shimmie2/ext/regen_thumb/main.php", "downloaded_repos/shish_shimmie2/ext/regen_thumb/test.php", "downloaded_repos/shish_shimmie2/ext/regen_thumb/theme.php", "downloaded_repos/shish_shimmie2/ext/relationships/info.php", "downloaded_repos/shish_shimmie2/ext/relationships/main.php", "downloaded_repos/shish_shimmie2/ext/relationships/permissions.php", "downloaded_repos/shish_shimmie2/ext/relationships/script.js", "downloaded_repos/shish_shimmie2/ext/relationships/style.css", "downloaded_repos/shish_shimmie2/ext/relationships/test.php", "downloaded_repos/shish_shimmie2/ext/relationships/theme.php", "downloaded_repos/shish_shimmie2/ext/replace_file/info.php", "downloaded_repos/shish_shimmie2/ext/replace_file/main.php", "downloaded_repos/shish_shimmie2/ext/replace_file/permissions.php", "downloaded_repos/shish_shimmie2/ext/replace_file/test.php", "downloaded_repos/shish_shimmie2/ext/replace_file/theme.php", "downloaded_repos/shish_shimmie2/ext/report_image/config.php", "downloaded_repos/shish_shimmie2/ext/report_image/info.php", "downloaded_repos/shish_shimmie2/ext/report_image/main.php", "downloaded_repos/shish_shimmie2/ext/report_image/permissions.php", "downloaded_repos/shish_shimmie2/ext/report_image/style.css", "downloaded_repos/shish_shimmie2/ext/report_image/test.php", "downloaded_repos/shish_shimmie2/ext/report_image/theme.php", "downloaded_repos/shish_shimmie2/ext/res_limit/config.php", "downloaded_repos/shish_shimmie2/ext/res_limit/info.php", "downloaded_repos/shish_shimmie2/ext/res_limit/main.php", "downloaded_repos/shish_shimmie2/ext/res_limit/test.php", "downloaded_repos/shish_shimmie2/ext/resize/config.php", "downloaded_repos/shish_shimmie2/ext/resize/info.php", "downloaded_repos/shish_shimmie2/ext/resize/main.php", "downloaded_repos/shish_shimmie2/ext/resize/script.js", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/config.php", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/icons/ascii2d.ico", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/icons/saucenao.ico", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/icons/tineye.ico", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/icons/trace.moe.ico", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/icons/yandex.ico", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/info.php", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/main.php", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/style.css", "downloaded_repos/shish_shimmie2/ext/reverse_search_links/theme.php", "downloaded_repos/shish_shimmie2/ext/robots_txt/config.php", "downloaded_repos/shish_shimmie2/ext/robots_txt/info.php", "downloaded_repos/shish_shimmie2/ext/robots_txt/main.php", "downloaded_repos/shish_shimmie2/ext/robots_txt/test.php", "downloaded_repos/shish_shimmie2/ext/rotate/info.php", "downloaded_repos/shish_shimmie2/ext/rotate/main.php", "downloaded_repos/shish_shimmie2/ext/rss_comments/info.php", "downloaded_repos/shish_shimmie2/ext/rss_comments/main.php", "downloaded_repos/shish_shimmie2/ext/rss_comments/test.php", "downloaded_repos/shish_shimmie2/ext/rss_images/config.php", "downloaded_repos/shish_shimmie2/ext/rss_images/info.php", "downloaded_repos/shish_shimmie2/ext/rss_images/main.php", "downloaded_repos/shish_shimmie2/ext/rss_images/test.php", "downloaded_repos/shish_shimmie2/ext/s3/S3.php", "downloaded_repos/shish_shimmie2/ext/s3/config.php", "downloaded_repos/shish_shimmie2/ext/s3/info.php", "downloaded_repos/shish_shimmie2/ext/s3/main.php", "downloaded_repos/shish_shimmie2/ext/setup/config.php", "downloaded_repos/shish_shimmie2/ext/setup/info.php", "downloaded_repos/shish_shimmie2/ext/setup/main.php", "downloaded_repos/shish_shimmie2/ext/setup/permissions.php", "downloaded_repos/shish_shimmie2/ext/setup/script.js", "downloaded_repos/shish_shimmie2/ext/setup/style.css", "downloaded_repos/shish_shimmie2/ext/setup/test.php", "downloaded_repos/shish_shimmie2/ext/setup/theme.php", "downloaded_repos/shish_shimmie2/ext/site_description/config.php", "downloaded_repos/shish_shimmie2/ext/site_description/info.php", "downloaded_repos/shish_shimmie2/ext/site_description/main.php", "downloaded_repos/shish_shimmie2/ext/site_description/test.php", "downloaded_repos/shish_shimmie2/ext/sitemap/info.php", "downloaded_repos/shish_shimmie2/ext/sitemap/main.php", "downloaded_repos/shish_shimmie2/ext/sitemap/test.php", "downloaded_repos/shish_shimmie2/ext/source_history/config.php", "downloaded_repos/shish_shimmie2/ext/source_history/info.php", "downloaded_repos/shish_shimmie2/ext/source_history/main.php", "downloaded_repos/shish_shimmie2/ext/source_history/theme.php", "downloaded_repos/shish_shimmie2/ext/static_files/info.php", "downloaded_repos/shish_shimmie2/ext/static_files/init.js", "downloaded_repos/shish_shimmie2/ext/static_files/installer.css", "downloaded_repos/shish_shimmie2/ext/static_files/main.php", "downloaded_repos/shish_shimmie2/ext/static_files/script.js", "downloaded_repos/shish_shimmie2/ext/static_files/static/README.txt", "downloaded_repos/shish_shimmie2/ext/static_files/static/apple-touch-icon.png", "downloaded_repos/shish_shimmie2/ext/static_files/static/favicon.ico", "downloaded_repos/shish_shimmie2/ext/static_files/static/favicon.png", "downloaded_repos/shish_shimmie2/ext/static_files/static/favicon.svg", "downloaded_repos/shish_shimmie2/ext/static_files/static/favicon_64.png", "downloaded_repos/shish_shimmie2/ext/static_files/static/grey.gif", "downloaded_repos/shish_shimmie2/ext/static_files/style.css", "downloaded_repos/shish_shimmie2/ext/static_files/test.php", "downloaded_repos/shish_shimmie2/ext/statistics/info.php", "downloaded_repos/shish_shimmie2/ext/statistics/main.php", "downloaded_repos/shish_shimmie2/ext/statistics/style.css", "downloaded_repos/shish_shimmie2/ext/statistics/test.php", "downloaded_repos/shish_shimmie2/ext/statistics/theme.php", "downloaded_repos/shish_shimmie2/ext/statsd/config.php", "downloaded_repos/shish_shimmie2/ext/statsd/info.php", "downloaded_repos/shish_shimmie2/ext/statsd/main.php", "downloaded_repos/shish_shimmie2/ext/system/info.php", "downloaded_repos/shish_shimmie2/ext/system/main.php", "downloaded_repos/shish_shimmie2/ext/system/test.php", "downloaded_repos/shish_shimmie2/ext/tag_categories/config.php", "downloaded_repos/shish_shimmie2/ext/tag_categories/info.php", "downloaded_repos/shish_shimmie2/ext/tag_categories/main.php", "downloaded_repos/shish_shimmie2/ext/tag_categories/permissions.php", "downloaded_repos/shish_shimmie2/ext/tag_categories/style.css", "downloaded_repos/shish_shimmie2/ext/tag_categories/test.php", "downloaded_repos/shish_shimmie2/ext/tag_categories/theme.php", "downloaded_repos/shish_shimmie2/ext/tag_editcloud/config.php", "downloaded_repos/shish_shimmie2/ext/tag_editcloud/info.php", "downloaded_repos/shish_shimmie2/ext/tag_editcloud/main.php", "downloaded_repos/shish_shimmie2/ext/tag_editcloud/script.js", "downloaded_repos/shish_shimmie2/ext/tag_editcloud/style.css", "downloaded_repos/shish_shimmie2/ext/tag_history/config.php", "downloaded_repos/shish_shimmie2/ext/tag_history/info.php", "downloaded_repos/shish_shimmie2/ext/tag_history/main.php", "downloaded_repos/shish_shimmie2/ext/tag_history/style.css", "downloaded_repos/shish_shimmie2/ext/tag_history/test.php", "downloaded_repos/shish_shimmie2/ext/tag_history/theme.php", "downloaded_repos/shish_shimmie2/ext/tag_list/config.php", "downloaded_repos/shish_shimmie2/ext/tag_list/info.php", "downloaded_repos/shish_shimmie2/ext/tag_list/main.php", "downloaded_repos/shish_shimmie2/ext/tag_list/test.php", "downloaded_repos/shish_shimmie2/ext/tag_list/theme.php", "downloaded_repos/shish_shimmie2/ext/tag_map/config.php", "downloaded_repos/shish_shimmie2/ext/tag_map/info.php", "downloaded_repos/shish_shimmie2/ext/tag_map/main.php", "downloaded_repos/shish_shimmie2/ext/tag_map/test.php", "downloaded_repos/shish_shimmie2/ext/tag_map/theme.php", "downloaded_repos/shish_shimmie2/ext/tag_tools/info.php", "downloaded_repos/shish_shimmie2/ext/tag_tools/main.php", "downloaded_repos/shish_shimmie2/ext/tag_tools/test.php", "downloaded_repos/shish_shimmie2/ext/tag_tools/theme.php", "downloaded_repos/shish_shimmie2/ext/tagger_xml/config.php", "downloaded_repos/shish_shimmie2/ext/tagger_xml/info.php", "downloaded_repos/shish_shimmie2/ext/tagger_xml/main.php", "downloaded_repos/shish_shimmie2/ext/terms/config.php", "downloaded_repos/shish_shimmie2/ext/terms/info.php", "downloaded_repos/shish_shimmie2/ext/terms/main.php", "downloaded_repos/shish_shimmie2/ext/terms/style.css", "downloaded_repos/shish_shimmie2/ext/terms/test.php", "downloaded_repos/shish_shimmie2/ext/terms/theme.php", "downloaded_repos/shish_shimmie2/ext/test_captcha/info.php", "downloaded_repos/shish_shimmie2/ext/test_captcha/main.php", "downloaded_repos/shish_shimmie2/ext/tips/images/coins.png", "downloaded_repos/shish_shimmie2/ext/tips/info.php", "downloaded_repos/shish_shimmie2/ext/tips/main.php", "downloaded_repos/shish_shimmie2/ext/tips/permissions.php", "downloaded_repos/shish_shimmie2/ext/tips/test.php", "downloaded_repos/shish_shimmie2/ext/tips/theme.php", "downloaded_repos/shish_shimmie2/ext/transcode/config.php", "downloaded_repos/shish_shimmie2/ext/transcode/info.php", "downloaded_repos/shish_shimmie2/ext/transcode/main.php", "downloaded_repos/shish_shimmie2/ext/transcode/script.js", "downloaded_repos/shish_shimmie2/ext/transcode/theme.php", "downloaded_repos/shish_shimmie2/ext/transcode_video/info.php", "downloaded_repos/shish_shimmie2/ext/transcode_video/main.php", "downloaded_repos/shish_shimmie2/ext/transcode_video/theme.php", "downloaded_repos/shish_shimmie2/ext/trash/info.php", "downloaded_repos/shish_shimmie2/ext/trash/main.php", "downloaded_repos/shish_shimmie2/ext/trash/permissions.php", "downloaded_repos/shish_shimmie2/ext/trash/theme.php", "downloaded_repos/shish_shimmie2/ext/upgrade/info.php", "downloaded_repos/shish_shimmie2/ext/upgrade/main.php", "downloaded_repos/shish_shimmie2/ext/upload/bookmarklet.js", "downloaded_repos/shish_shimmie2/ext/upload/config.php", "downloaded_repos/shish_shimmie2/ext/upload/events/upload_common_building_event.php", "downloaded_repos/shish_shimmie2/ext/upload/events/upload_header_building_event.php", "downloaded_repos/shish_shimmie2/ext/upload/events/upload_specific_building_event.php", "downloaded_repos/shish_shimmie2/ext/upload/info.php", "downloaded_repos/shish_shimmie2/ext/upload/main.php", "downloaded_repos/shish_shimmie2/ext/upload/permissions.php", "downloaded_repos/shish_shimmie2/ext/upload/script.js", "downloaded_repos/shish_shimmie2/ext/upload/style.css", "downloaded_repos/shish_shimmie2/ext/upload/test.php", "downloaded_repos/shish_shimmie2/ext/upload/theme.php", "downloaded_repos/shish_shimmie2/ext/user/config.php", "downloaded_repos/shish_shimmie2/ext/user/events.php", "downloaded_repos/shish_shimmie2/ext/user/info.php", "downloaded_repos/shish_shimmie2/ext/user/main.php", "downloaded_repos/shish_shimmie2/ext/user/permissions.php", "downloaded_repos/shish_shimmie2/ext/user/test.php", "downloaded_repos/shish_shimmie2/ext/user/theme.php", "downloaded_repos/shish_shimmie2/ext/user_class_file/info.php", "downloaded_repos/shish_shimmie2/ext/user_class_file/main.php", "downloaded_repos/shish_shimmie2/ext/user_config/config.php", "downloaded_repos/shish_shimmie2/ext/user_config/info.php", "downloaded_repos/shish_shimmie2/ext/user_config/main.php", "downloaded_repos/shish_shimmie2/ext/user_config/test.php", "downloaded_repos/shish_shimmie2/ext/user_config/theme.php", "downloaded_repos/shish_shimmie2/ext/varnish/config.php", "downloaded_repos/shish_shimmie2/ext/varnish/info.php", "downloaded_repos/shish_shimmie2/ext/varnish/main.php", "downloaded_repos/shish_shimmie2/ext/view/events/displaying_image_event.php", "downloaded_repos/shish_shimmie2/ext/view/events/image_admin_block_building_event.php", "downloaded_repos/shish_shimmie2/ext/view/events/image_info_box_building_event.php", "downloaded_repos/shish_shimmie2/ext/view/events/image_info_set_event.php", "downloaded_repos/shish_shimmie2/ext/view/info.php", "downloaded_repos/shish_shimmie2/ext/view/main.php", "downloaded_repos/shish_shimmie2/ext/view/script.js", "downloaded_repos/shish_shimmie2/ext/view/style.css", "downloaded_repos/shish_shimmie2/ext/view/test.php", "downloaded_repos/shish_shimmie2/ext/view/theme.php", "downloaded_repos/shish_shimmie2/ext/wiki/config.php", "downloaded_repos/shish_shimmie2/ext/wiki/info.php", "downloaded_repos/shish_shimmie2/ext/wiki/main.php", "downloaded_repos/shish_shimmie2/ext/wiki/permissions.php", "downloaded_repos/shish_shimmie2/ext/wiki/style.css", "downloaded_repos/shish_shimmie2/ext/wiki/test.php", "downloaded_repos/shish_shimmie2/ext/wiki/theme.php", "downloaded_repos/shish_shimmie2/ext/word_filter/config.php", "downloaded_repos/shish_shimmie2/ext/word_filter/info.php", "downloaded_repos/shish_shimmie2/ext/word_filter/main.php", "downloaded_repos/shish_shimmie2/ext/word_filter/test.php", "downloaded_repos/shish_shimmie2/index.php", "downloaded_repos/shish_shimmie2/phpstan.dist.neon", "downloaded_repos/shish_shimmie2/phpunit.dist.xml", "downloaded_repos/shish_shimmie2/themes/danbooru2/admin.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/comment.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/common_elements.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/ext_manager.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/index.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/page.class.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/style.css", "downloaded_repos/shish_shimmie2/themes/danbooru2/tag_map.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/upload.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/user.theme.php", "downloaded_repos/shish_shimmie2/themes/danbooru2/view.theme.php", "downloaded_repos/shish_shimmie2/themes/default/page.class.php", "downloaded_repos/shish_shimmie2/themes/default/style.css", "downloaded_repos/shish_shimmie2/themes/futaba/comment.theme.php", "downloaded_repos/shish_shimmie2/themes/futaba/common_elements.theme.php", "downloaded_repos/shish_shimmie2/themes/futaba/fade.png", "downloaded_repos/shish_shimmie2/themes/futaba/page.class.php", "downloaded_repos/shish_shimmie2/themes/futaba/style.css", "downloaded_repos/shish_shimmie2/themes/futaba/view.theme.php", "downloaded_repos/shish_shimmie2/themes/lite/comment.theme.php", "downloaded_repos/shish_shimmie2/themes/lite/common_elements.theme.php", "downloaded_repos/shish_shimmie2/themes/lite/page.class.php", "downloaded_repos/shish_shimmie2/themes/lite/setup.theme.php", "downloaded_repos/shish_shimmie2/themes/lite/style.css", "downloaded_repos/shish_shimmie2/themes/lite/user.theme.php", "downloaded_repos/shish_shimmie2/themes/lite/user_config.theme.php", "downloaded_repos/shish_shimmie2/themes/lite/view.theme.php", "downloaded_repos/shish_shimmie2/themes/warm/bg.png", "downloaded_repos/shish_shimmie2/themes/warm/page.class.php", "downloaded_repos/shish_shimmie2/themes/warm/style.css", "downloaded_repos/shish_shimmie2/themes/warm/upload.theme.php", "downloaded_repos/shish_shimmie2/themes/warm/user.theme.php"], "skipped": [{"path": "downloaded_repos/shish_shimmie2/.github/workflows/release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Config/ConfigMeta.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Events/EventBus.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/Tag.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/ImageBoard/TagUsage.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Media/MediaEngine.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Media/VideoContainer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Page/Cookie.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Page/NavLink.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Page/Page_Page.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Search/ImgCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Search/SearchParameters.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Search/TagCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/User/PermissionMeta.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/User/UserClass.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Util/Path.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Util/Url.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Util/polyfills.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/core/Util/util.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/auto_tagger/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/bulk_actions/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/graphql/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/handle_cbz/jszip-utils-ie.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/ext/handle_cbz/jszip-utils.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/ext/handle_cbz/jszip.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/ext/handle_mp3/lib/jsmediatags.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/ext/log_db/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/pm/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/pools/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/post_tags/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/setup/theme.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/tag_list/theme.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/user/main.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/ext/user_config/theme.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/shish_shimmie2/tests/alert.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/tests/bedroom_workshop.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/tests/favicon.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/tests/pbx_screenshot.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/tests/router.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/shish_shimmie2/tests/test.svg", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.772233009338379, "profiling_times": {"config_time": 6.376438617706299, "core_time": 6.915624141693115, "ignores_time": 0.0017359256744384766, "total_time": 13.295302391052246}, "parsing_time": {"total_time": 7.167011976242065, "per_file_time": {"mean": 0.009368643106198768, "std_dev": 0.0004794857198552693}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 30.70186710357666, "per_file_time": {"mean": 0.012006987525841482, "std_dev": 0.003935034771166158}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 12.060006380081177, "per_file_and_rule_time": {"mean": 0.007204304886547899, "std_dev": 0.0004082512342726076}, "very_slow_stats": {"time_ratio": 0.18818593863713962, "count_ratio": 0.008363201911589008}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/shish_shimmie2/ext/comment/main.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.11770892143249512}, {"fpath": "downloaded_repos/shish_shimmie2/ext/comment/main.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.12082695960998535}, {"fpath": "downloaded_repos/shish_shimmie2/core/Page/CommonElementsTheme.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.12310290336608887}, {"fpath": "downloaded_repos/shish_shimmie2/ext/cron_uploader/main.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.15848112106323242}, {"fpath": "downloaded_repos/shish_shimmie2/ext/upload/theme.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.17537283897399902}, {"fpath": "downloaded_repos/shish_shimmie2/ext/view/theme.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.17554688453674316}, {"fpath": "downloaded_repos/shish_shimmie2/ext/ouroboros_api/main.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.19432902336120605}, {"fpath": "downloaded_repos/shish_shimmie2/core/Util/polyfills.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.2088310718536377}, {"fpath": "downloaded_repos/shish_shimmie2/core/Util/util.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.23968195915222168}, {"fpath": "downloaded_repos/shish_shimmie2/ext/upload/bookmarklet.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.30661892890930176}]}, "tainting_time": {"total_time": 4.207117080688477, "per_def_and_rule_time": {"mean": 0.000994825509739531, "std_dev": 5.271044345520003e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1097954240}, "engine_requested": "OSS", "skipped_rules": []}