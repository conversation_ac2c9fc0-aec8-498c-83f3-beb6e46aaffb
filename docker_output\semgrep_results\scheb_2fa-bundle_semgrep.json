{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 31, "col": 17, "offset": 0}, "end": {"line": 31, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 32, "col": 17, "offset": 0}, "end": {"line": 32, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 34, "col": 17, "offset": 0}, "end": {"line": 34, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 35, "col": 26, "offset": 0}, "end": {"line": 35, "col": 30, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Controller/FormController.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 31, "col": 17, "offset": 0}, "end": {"line": 31, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 32, "col": 17, "offset": 0}, "end": {"line": 32, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 34, "col": 17, "offset": 0}, "end": {"line": 34, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "start": {"line": 35, "col": 26, "offset": 0}, "end": {"line": 35, "col": 30, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "start": {"line": 61, "col": 41, "offset": 0}, "end": {"line": 61, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php:61:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "start": {"line": 61, "col": 41, "offset": 0}, "end": {"line": 61, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersister.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersister.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersister.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersister.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 21, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 21, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Authentication/AuthenticationTrustResolver.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Authentication/AuthenticationTrustResolver.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Authentication/AuthenticationTrustResolver.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Authentication/AuthenticationTrustResolver.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 42, "col": 13, "offset": 0}, "end": {"line": 42, "col": 21, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 45, "col": 17, "offset": 0}, "end": {"line": 45, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 46, "col": 17, "offset": 0}, "end": {"line": 46, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 47, "col": 17, "offset": 0}, "end": {"line": 47, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 48, "col": 17, "offset": 0}, "end": {"line": 48, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 49, "col": 17, "offset": 0}, "end": {"line": 49, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 50, "col": 17, "offset": 0}, "end": {"line": 50, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php:42:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 42, "col": 13, "offset": 0}, "end": {"line": 42, "col": 21, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 45, "col": 17, "offset": 0}, "end": {"line": 45, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 46, "col": 17, "offset": 0}, "end": {"line": 46, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 47, "col": 17, "offset": 0}, "end": {"line": 47, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 48, "col": 17, "offset": 0}, "end": {"line": 48, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 49, "col": 17, "offset": 0}, "end": {"line": 49, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 50, "col": 17, "offset": 0}, "end": {"line": 50, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/AbstractCheckCodeListener.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 31, "offset": 5}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 3}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 31, "offset": 5}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 3}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php:27:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 33, "offset": 7}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 34, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php:19:\n `Request` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 33, "offset": 7}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 34, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextFactory.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 56, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextFactory.php:17:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextFactory.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 56, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php:18:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php:17:\n `iterable` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php:24:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 17, "col": 26, "offset": 0}, "end": {"line": 17, "col": 33, "offset": 7}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php:17:\n `Request` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 17, "col": 26, "offset": 0}, "end": {"line": 17, "col": 33, "offset": 7}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php:15:\n `object` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php:15:\n `object` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 55, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php:17:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 55, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 35, "col": 13, "offset": 0}, "end": {"line": 35, "col": 21, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 41, "col": 26, "offset": 0}, "end": {"line": 41, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 42, "col": 26, "offset": 0}, "end": {"line": 42, "col": 30, "offset": 4}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 43, "col": 26, "offset": 0}, "end": {"line": 43, "col": 30, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php:35:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 35, "col": 13, "offset": 0}, "end": {"line": 35, "col": 21, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 41, "col": 26, "offset": 0}, "end": {"line": 41, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 42, "col": 26, "offset": 0}, "end": {"line": 42, "col": 30, "offset": 4}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 43, "col": 26, "offset": 0}, "end": {"line": 43, "col": 30, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 58, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php:18:\n `iterable` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 58, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 31, "offset": 5}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php:21:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 31, "offset": 5}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallContext.php:18:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "spans": [{"file": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]}], "paths": {"scanned": ["downloaded_repos/scheb_2fa-bundle/.gitattributes", "downloaded_repos/scheb_2fa-bundle/.github/stale.yml", "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Compiler/MailerCompilerPass.php", "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Compiler/TwoFactorFirewallConfigCompilerPass.php", "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Compiler/TwoFactorProviderCompilerPass.php", "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Configuration.php", "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Factory/Security/TwoFactorServicesFactory.php", "downloaded_repos/scheb_2fa-bundle/DependencyInjection/SchebTwoFactorExtension.php", "downloaded_repos/scheb_2fa-bundle/LICENSE", "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersister.php", "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php", "downloaded_repos/scheb_2fa-bundle/Model/PersisterInterface.php", "downloaded_repos/scheb_2fa-bundle/Model/PreferredProviderInterface.php", "downloaded_repos/scheb_2fa-bundle/README.md", "downloaded_repos/scheb_2fa-bundle/Resources/config/backup_codes.php", "downloaded_repos/scheb_2fa-bundle/Resources/config/persistence.php", "downloaded_repos/scheb_2fa-bundle/Resources/config/security.php", "downloaded_repos/scheb_2fa-bundle/Resources/config/trusted_device.php", "downloaded_repos/scheb_2fa-bundle/Resources/config/two_factor.php", "downloaded_repos/scheb_2fa-bundle/Resources/config/two_factor_provider_email.php", "downloaded_repos/scheb_2fa-bundle/Resources/config/two_factor_provider_google.php", "downloaded_repos/scheb_2fa-bundle/Resources/config/two_factor_provider_totp.php", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.cs.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.de.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.en.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.es.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.fr.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.hr.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.hu.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.id.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.nl.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.pl.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.ro.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.ru.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.sk.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.sv.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.tr.yml", "downloaded_repos/scheb_2fa-bundle/Resources/translations/SchebTwoFactorBundle.uk.yml", "downloaded_repos/scheb_2fa-bundle/Resources/views/Authentication/form.html.twig", "downloaded_repos/scheb_2fa-bundle/SchebTwoFactorBundle.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/AuthenticationTrustResolver.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/Exception/InvalidTwoFactorCodeException.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/Exception/ReusedTwoFactorCodeException.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/Exception/TwoFactorProviderNotFoundException.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/Token/TwoFactorToken.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/Token/TwoFactorTokenFactory.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/Token/TwoFactorTokenFactoryInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/Authentication/Token/TwoFactorTokenInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "downloaded_repos/scheb_2fa-bundle/Security/Authorization/Voter/TwoFactorInProgressVoter.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/AuthenticationRequiredHandlerInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/SuppressRememberMeListener.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/ThrowExceptionOnTwoFactorCodeReuseListener.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Utils/JsonRequestUtils.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Utils/ParameterBagUtils.php", "downloaded_repos/scheb_2fa-bundle/Security/Http/Utils/RequestDataReader.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextFactory.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextFactoryInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/TwoFactorConditionInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Csrf/NullCsrfTokenManager.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationSuccessEventSuppressor.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvents.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/IpWhitelist/IpWhitelistProviderInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/Exception/TwoFactorProviderLogicException.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/Exception/UnexpectedTokenException.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/Exception/UnknownTwoFactorProviderException.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/PreparationRecorderInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorFormRendererInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderDecider.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderDeciderInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInterface.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "downloaded_repos/scheb_2fa-bundle/composer.json"], "skipped": [{"path": "downloaded_repos/scheb_2fa-bundle/Controller/FormController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersister.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Model/Persister/DoctrinePersisterFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Authentication/AuthenticationTrustResolver.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Authorization/TwoFactorAccessDecider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/ExceptionListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContext.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/AuthenticationContextFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa-bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.5918710231781006, "profiling_times": {"config_time": 5.946642637252808, "core_time": 2.4039552211761475, "ignores_time": 0.002093076705932617, "total_time": 8.353659391403198}, "parsing_time": {"total_time": 0.742039680480957, "per_file_time": {"mean": 0.007571833474295479, "std_dev": 0.00017638592348192812}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.3933818340301514, "per_file_time": {"mean": 0.004613847132550176, "std_dev": 0.00013033664818143875}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.17696547508239746, "per_file_and_rule_time": {"mean": 0.00029642458137755004, "std_dev": 1.377980375947304e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0011868476867675781, "per_def_and_rule_time": {"mean": 0.00023736953735351563, "std_dev": 1.4542012195306596e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}