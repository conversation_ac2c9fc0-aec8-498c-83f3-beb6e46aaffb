{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 3, "col": 1, "offset": 29}, "end": {"line": 3, "col": 23, "offset": 51}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 6, "col": 1, "offset": 82}, "end": {"line": 6, "col": 51, "offset": 132}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 7, "col": 1, "offset": 133}, "end": {"line": 7, "col": 46, "offset": 178}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 8, "col": 1, "offset": 179}, "end": {"line": 8, "col": 23, "offset": 201}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 14, "col": 1, "offset": 458}, "end": {"line": 14, "col": 16, "offset": 473}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 15, "col": 1, "offset": 474}, "end": {"line": 15, "col": 16, "offset": 489}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 18, "col": 1, "offset": 516}, "end": {"line": 18, "col": 67, "offset": 582}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 19, "col": 1, "offset": 583}, "end": {"line": 19, "col": 37, "offset": 619}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 20, "col": 1, "offset": 620}, "end": {"line": 20, "col": 49, "offset": 668}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 21, "col": 1, "offset": 669}, "end": {"line": 21, "col": 55, "offset": 723}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 24, "col": 1, "offset": 743}, "end": {"line": 24, "col": 70, "offset": 812}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "path": "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "start": {"line": 26, "col": 1, "offset": 862}, "end": {"line": 26, "col": 17, "offset": 878}, "extra": {"message": "Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities.", "metadata": {"category": "security", "technology": ["dockerfile"], "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration"], "references": ["https://cwe.mitre.org/data/definitions/250.html", "https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile", "shortlink": "https://sg.run/80Q7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoDatabaseController.php", "start": {"line": 364, "col": 21, "offset": 14192}, "end": {"line": 364, "col": 34, "offset": 14205}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/Redis/ConfigurationRedis.php", "start": {"line": 30, "col": 27, "offset": 811}, "end": {"line": 30, "col": 65, "offset": 849}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCheckbox.vue", "start": {"line": 22, "col": 5, "offset": 584}, "end": {"line": 22, "col": 39, "offset": 618}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCheckbox.vue", "start": {"line": 34, "col": 9, "offset": 923}, "end": {"line": 34, "col": 63, "offset": 977}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue", "start": {"line": 19, "col": 7, "offset": 456}, "end": {"line": 19, "col": 41, "offset": 490}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue", "start": {"line": 30, "col": 11, "offset": 809}, "end": {"line": 30, "col": 66, "offset": 864}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue", "start": {"line": 45, "col": 7, "offset": 1252}, "end": {"line": 45, "col": 41, "offset": 1286}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue", "start": {"line": 57, "col": 11, "offset": 1616}, "end": {"line": 57, "col": 66, "offset": 1671}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue", "start": {"line": 26, "col": 5, "offset": 713}, "end": {"line": 26, "col": 39, "offset": 747}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue", "start": {"line": 29, "col": 9, "offset": 844}, "end": {"line": 33, "col": 18, "offset": 980}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue", "start": {"line": 37, "col": 9, "offset": 1052}, "end": {"line": 37, "col": 64, "offset": 1107}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDate.vue", "start": {"line": 18, "col": 5, "offset": 547}, "end": {"line": 18, "col": 39, "offset": 581}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDate.vue", "start": {"line": 30, "col": 9, "offset": 879}, "end": {"line": 30, "col": 56, "offset": 926}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDatetime.vue", "start": {"line": 24, "col": 5, "offset": 688}, "end": {"line": 24, "col": 39, "offset": 722}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDatetime.vue", "start": {"line": 36, "col": 9, "offset": 1024}, "end": {"line": 36, "col": 60, "offset": 1075}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue", "start": {"line": 12, "col": 5, "offset": 301}, "end": {"line": 12, "col": 39, "offset": 335}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue", "start": {"line": 15, "col": 9, "offset": 432}, "end": {"line": 19, "col": 18, "offset": 562}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue", "start": {"line": 23, "col": 9, "offset": 634}, "end": {"line": 23, "col": 58, "offset": 683}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEmail.vue", "start": {"line": 15, "col": 5, "offset": 358}, "end": {"line": 15, "col": 39, "offset": 392}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEmail.vue", "start": {"line": 27, "col": 9, "offset": 691}, "end": {"line": 27, "col": 57, "offset": 739}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNotificationMessage.vue", "start": {"line": 43, "col": 11, "offset": 1135}, "end": {"line": 44, "col": 20, "offset": 1160}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNotificationMessage.vue", "start": {"line": 85, "col": 11, "offset": 2343}, "end": {"line": 85, "col": 24, "offset": 2356}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNumber.vue", "start": {"line": 13, "col": 5, "offset": 375}, "end": {"line": 13, "col": 39, "offset": 409}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNumber.vue", "start": {"line": 25, "col": 9, "offset": 709}, "end": {"line": 25, "col": 58, "offset": 758}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPassword.vue", "start": {"line": 10, "col": 5, "offset": 239}, "end": {"line": 10, "col": 39, "offset": 273}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPassword.vue", "start": {"line": 22, "col": 9, "offset": 575}, "end": {"line": 22, "col": 60, "offset": 626}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoRadio.vue", "start": {"line": 21, "col": 5, "offset": 522}, "end": {"line": 21, "col": 39, "offset": 556}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoRadio.vue", "start": {"line": 33, "col": 9, "offset": 855}, "end": {"line": 33, "col": 57, "offset": 903}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSearch.vue", "start": {"line": 12, "col": 5, "offset": 272}, "end": {"line": 12, "col": 39, "offset": 306}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSearch.vue", "start": {"line": 24, "col": 9, "offset": 606}, "end": {"line": 24, "col": 58, "offset": 655}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelect.vue", "start": {"line": 18, "col": 5, "offset": 534}, "end": {"line": 18, "col": 39, "offset": 568}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelect.vue", "start": {"line": 30, "col": 9, "offset": 868}, "end": {"line": 30, "col": 58, "offset": 917}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelectMultiple.vue", "start": {"line": 19, "col": 5, "offset": 460}, "end": {"line": 19, "col": 39, "offset": 494}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelectMultiple.vue", "start": {"line": 31, "col": 9, "offset": 803}, "end": {"line": 33, "col": 18, "offset": 881}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue", "start": {"line": 13, "col": 5, "offset": 327}, "end": {"line": 13, "col": 39, "offset": 361}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue", "start": {"line": 16, "col": 9, "offset": 458}, "end": {"line": 20, "col": 18, "offset": 591}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue", "start": {"line": 24, "col": 9, "offset": 666}, "end": {"line": 24, "col": 58, "offset": 715}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSwitch.vue", "start": {"line": 51, "col": 5, "offset": 1854}, "end": {"line": 51, "col": 39, "offset": 1888}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSwitch.vue", "start": {"line": 63, "col": 9, "offset": 2188}, "end": {"line": 63, "col": 58, "offset": 2237}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTags.vue", "start": {"line": 12, "col": 5, "offset": 309}, "end": {"line": 12, "col": 39, "offset": 343}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTags.vue", "start": {"line": 24, "col": 9, "offset": 641}, "end": {"line": 24, "col": 56, "offset": 688}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoText.vue", "start": {"line": 18, "col": 5, "offset": 516}, "end": {"line": 18, "col": 39, "offset": 550}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoText.vue", "start": {"line": 30, "col": 9, "offset": 848}, "end": {"line": 30, "col": 56, "offset": 895}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTextarea.vue", "start": {"line": 7, "col": 5, "offset": 248}, "end": {"line": 7, "col": 39, "offset": 282}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTextarea.vue", "start": {"line": 19, "col": 9, "offset": 584}, "end": {"line": 19, "col": 60, "offset": 635}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTime.vue", "start": {"line": 21, "col": 5, "offset": 617}, "end": {"line": 21, "col": 39, "offset": 651}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTime.vue", "start": {"line": 33, "col": 9, "offset": 949}, "end": {"line": 33, "col": 56, "offset": 996}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFile.vue", "start": {"line": 20, "col": 5, "offset": 512}, "end": {"line": 20, "col": 39, "offset": 546}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFile.vue", "start": {"line": 32, "col": 9, "offset": 851}, "end": {"line": 32, "col": 63, "offset": 905}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFileMultiple.vue", "start": {"line": 25, "col": 5, "offset": 560}, "end": {"line": 25, "col": 39, "offset": 594}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFileMultiple.vue", "start": {"line": 37, "col": 9, "offset": 908}, "end": {"line": 39, "col": 18, "offset": 991}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue", "start": {"line": 20, "col": 5, "offset": 516}, "end": {"line": 20, "col": 39, "offset": 550}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue", "start": {"line": 23, "col": 9, "offset": 643}, "end": {"line": 27, "col": 18, "offset": 779}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue", "start": {"line": 31, "col": 9, "offset": 848}, "end": {"line": 31, "col": 64, "offset": 903}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImageMultiple.vue", "start": {"line": 25, "col": 5, "offset": 564}, "end": {"line": 25, "col": 39, "offset": 598}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImageMultiple.vue", "start": {"line": 37, "col": 9, "offset": 909}, "end": {"line": 39, "col": 18, "offset": 993}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUrl.vue", "start": {"line": 12, "col": 5, "offset": 264}, "end": {"line": 12, "col": 39, "offset": 298}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUrl.vue", "start": {"line": 23, "col": 9, "offset": 586}, "end": {"line": 23, "col": 55, "offset": 632}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/forgot-password.vue", "start": {"line": 37, "col": 13, "offset": 1113}, "end": {"line": 39, "col": 22, "offset": 1192}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/forgot-password.vue", "start": {"line": 77, "col": 13, "offset": 2303}, "end": {"line": 79, "col": 22, "offset": 2382}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/login.vue", "start": {"line": 28, "col": 15, "offset": 883}, "end": {"line": 28, "col": 56, "offset": 924}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/login.vue", "start": {"line": 52, "col": 15, "offset": 1672}, "end": {"line": 52, "col": 56, "offset": 1713}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue", "start": {"line": 28, "col": 15, "offset": 899}, "end": {"line": 28, "col": 59, "offset": 943}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue", "start": {"line": 50, "col": 15, "offset": 1641}, "end": {"line": 52, "col": 24, "offset": 1717}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue", "start": {"line": 75, "col": 15, "offset": 2418}, "end": {"line": 75, "col": 59, "offset": 2462}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue", "start": {"line": 97, "col": 15, "offset": 3145}, "end": {"line": 97, "col": 59, "offset": 3189}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue", "start": {"line": 120, "col": 15, "offset": 3914}, "end": {"line": 122, "col": 24, "offset": 3990}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue", "start": {"line": 154, "col": 15, "offset": 4984}, "end": {"line": 154, "col": 59, "offset": 5028}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/secret-login.vue", "start": {"line": 28, "col": 15, "offset": 883}, "end": {"line": 28, "col": 56, "offset": 924}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/secret-login.vue", "start": {"line": 52, "col": 15, "offset": 1672}, "end": {"line": 52, "col": 56, "offset": 1713}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/verify.vue", "start": {"line": 36, "col": 13, "offset": 1040}, "end": {"line": 36, "col": 55, "offset": 1082}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse-bin.vue", "start": {"line": 146, "col": 27, "offset": 5475}, "end": {"line": 148, "col": 36, "offset": 5581}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse-bin.vue", "start": {"line": 445, "col": 29, "offset": 18685}, "end": {"line": 447, "col": 38, "offset": 18795}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse.vue", "start": {"line": 193, "col": 27, "offset": 7143}, "end": {"line": 195, "col": 36, "offset": 7249}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse.vue", "start": {"line": 482, "col": 29, "offset": 19903}, "end": {"line": 484, "col": 38, "offset": 20013}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/read.vue", "start": {"line": 71, "col": 25, "offset": 2726}, "end": {"line": 73, "col": 34, "offset": 2828}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpstan.yml", "start": {"line": 41, "col": 55, "offset": 829}, "end": {"line": 41, "col": 58, "offset": 832}}]], "message": "Syntax error at line downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpstan.yml:41:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpstan.yml", "spans": [{"file": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpstan.yml", "start": {"line": 41, "col": 55, "offset": 829}, "end": {"line": 41, "col": 58, "offset": 832}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpunit.yml", "start": {"line": 75, "col": 55, "offset": 1818}, "end": {"line": 75, "col": 58, "offset": 1821}}]], "message": "Syntax error at line downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpunit.yml:75:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpunit.yml", "spans": [{"file": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpunit.yml", "start": {"line": 75, "col": 55, "offset": 1818}, "end": {"line": 75, "col": 58, "offset": 1821}}]}], "paths": {"scanned": ["downloaded_repos/uasoft-indonesia_badaso/.eslintignore", "downloaded_repos/uasoft-indonesia_badaso/.eslintrc.js", "downloaded_repos/uasoft-indonesia_badaso/.github/FUNDING.yml", "downloaded_repos/uasoft-indonesia_badaso/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/uasoft-indonesia_badaso/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/uasoft-indonesia_badaso/.github/pr-labeler.yml", "downloaded_repos/uasoft-indonesia_badaso/.github/stale.yml", "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/eslint.yml", "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpstan.yml", "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpunit.yml", "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/pr-labeler.yml", "downloaded_repos/uasoft-indonesia_badaso/.gitignore", "downloaded_repos/uasoft-indonesia_badaso/.gitpod/AppServiceProvider.php", "downloaded_repos/uasoft-indonesia_badaso/.gitpod/app.php", "downloaded_repos/uasoft-indonesia_badaso/.gitpod/composer.json", "downloaded_repos/uasoft-indonesia_badaso/.gitpod/webpack.mix.js", "downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile", "downloaded_repos/uasoft-indonesia_badaso/.gitpod.yml", "downloaded_repos/uasoft-indonesia_badaso/.styleci.yml", "downloaded_repos/uasoft-indonesia_badaso/CONTRIBUTING.md", "downloaded_repos/uasoft-indonesia_badaso/LICENSE", "downloaded_repos/uasoft-indonesia_badaso/README.md", "downloaded_repos/uasoft-indonesia_badaso/SECURITY.md", "downloaded_repos/uasoft-indonesia_badaso/bin/bump-version.sh", "downloaded_repos/uasoft-indonesia_badaso/composer.json", "downloaded_repos/uasoft-indonesia_badaso/package.json", "downloaded_repos/uasoft-indonesia_badaso/phpstan-baseline.neon", "downloaded_repos/uasoft-indonesia_badaso/phpstan.neon.dist", "downloaded_repos/uasoft-indonesia_badaso/src/.DS_Store", "downloaded_repos/uasoft-indonesia_badaso/src/Badaso.php", "downloaded_repos/uasoft-indonesia_badaso/src/BadasoDeploymentOrchestrator.php", "downloaded_repos/uasoft-indonesia_badaso/src/Commands/AdminCommand.php", "downloaded_repos/uasoft-indonesia_badaso/src/Commands/BackupCommand.php", "downloaded_repos/uasoft-indonesia_badaso/src/Commands/BadasoFirebaseCommand.php", "downloaded_repos/uasoft-indonesia_badaso/src/Commands/BadasoSetup.php", "downloaded_repos/uasoft-indonesia_badaso/src/Commands/BadasoTestSetup.php", "downloaded_repos/uasoft-indonesia_badaso/src/Commands/GenerateSeederCommand.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/analytics.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/backup.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/badaso-hidden-tables.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/badaso-watch-tables.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/badaso.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/database.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/firebase.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/l5-swagger.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/lfm.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/log-viewer.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/octane.php", "downloaded_repos/uasoft-indonesia_badaso/src/Config/sanctum.php", "downloaded_repos/uasoft-indonesia_badaso/src/ContentManager/ContentGenerator.php", "downloaded_repos/uasoft-indonesia_badaso/src/ContentManager/ContentManager.php", "downloaded_repos/uasoft-indonesia_badaso/src/ContentManager/FileGenerator.php", "downloaded_repos/uasoft-indonesia_badaso/src/ContentManager/FileSystem.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoActivityLogController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoAuthController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoBaseController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoCRUDController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoConfigurationsController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoDashboardController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoDataController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoDatabaseController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoFCMController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoFileController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoFirebaseConfigController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoMaintenanceController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoMenuController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoNotificationsController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoPermissionController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoRoleController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoRolePermissionController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoTableController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoUserController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoUserRoleController.php", "downloaded_repos/uasoft-indonesia_badaso/src/Controllers/Controller.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/DatabaseUpdater.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Platforms/Mysql.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Platforms/Platform.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Platforms/Postgresql.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Platforms/Sqlite.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Schema/Column.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Schema/ForeignKey.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Schema/Identifier.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Schema/Index.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Schema/SchemaManager.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Schema/Table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Common/CharType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Common/DoubleType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Common/JsonType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Common/NumericType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Common/TextType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Common/VarCharType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/BinaryType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/BitType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/BlobType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/EnumType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/FloatType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/GeometryCollectionType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/GeometryType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/LineStringType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/LongBlobType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/LongTextType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/MediumBlobType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/MediumIntType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/MediumTextType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/MultiLineStringType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/MultiPointType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/MultiPolygonType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/PointType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/PolygonType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/SetType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/TimeStampType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/TinyBlobType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/TinyIntType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/TinyTextType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/VarBinaryType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Mysql/YearType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/BitType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/BitVaryingType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/ByteaType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/CharacterType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/CharacterVaryingType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/CidrType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/DoublePrecisionType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/GeometryType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/InetType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/IntervalType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/JsonbType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/MacAddrType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/MoneyType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/RealType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/SmallIntType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/TimeStampType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/TimeStampTzType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/TimeTzType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/TsQueryType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/TsVectorType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/TxidSnapshotType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/UuidType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Postgresql/XmlType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Sqlite/RealType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Database/Types/Type.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/CRUDDataAdded.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/CRUDDataChanged.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/CRUDDataDeleted.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/CRUDDataUpdated.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/ConfigurationUpdated.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/EntityAdded.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/EntityChanged.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/EntityDeleted.php", "downloaded_repos/uasoft-indonesia_badaso/src/Events/EntityUpdated.php", "downloaded_repos/uasoft-indonesia_badaso/src/Exceptions/OrchestratorHandlerNotFoundException.php", "downloaded_repos/uasoft-indonesia_badaso/src/Exceptions/SingleException.php", "downloaded_repos/uasoft-indonesia_badaso/src/Facades/Badaso.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/ApiDocs.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/ApiResponse.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/AuthenticatedUser.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/CallHelperTest.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/CaseConvert.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/CheckBase64.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/Config.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/DataTypeToComponent.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/Firebase/FCMNotification.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/Firebase/FirebasePublishFile.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/GetData.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/HandleFile.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/HiddenTableConfig.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/MigrationParser.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/Redis/ConfigurationRedis.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/TokenManagement.php", "downloaded_repos/uasoft-indonesia_badaso/src/Helpers/WatchTableConfig.php", "downloaded_repos/uasoft-indonesia_badaso/src/Images/.DS_Store", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/auth-bg.jpg", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/badaso.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/default-user.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/favicon.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/logo-144px.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/logo-192px.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/logo-512px.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/logo.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/logo.webp", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/maintenance.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/auth-bg.jpg", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/badaso.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/default-user.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/favicon.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/logo-144px.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/logo-192px.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/logo-512px.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/logo.png", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/logo.webp", "downloaded_repos/uasoft-indonesia_badaso/src/Images/badaso-images/thumbs/maintenance.png", "downloaded_repos/uasoft-indonesia_badaso/src/Interfaces/WidgetInterface.php", "downloaded_repos/uasoft-indonesia_badaso/src/Listeners/BadasoCRUDDataChanged.php", "downloaded_repos/uasoft-indonesia_badaso/src/Mail/ForgotPassword.php", "downloaded_repos/uasoft-indonesia_badaso/src/Mail/SendUserVerification.php", "downloaded_repos/uasoft-indonesia_badaso/src/Middleware/ApiRequest.php", "downloaded_repos/uasoft-indonesia_badaso/src/Middleware/BadasoAuthenticate.php", "downloaded_repos/uasoft-indonesia_badaso/src/Middleware/BadasoAuthenticateIframe.php", "downloaded_repos/uasoft-indonesia_badaso/src/Middleware/BadasoCheckPermissions.php", "downloaded_repos/uasoft-indonesia_badaso/src/Middleware/BadasoCheckPermissionsForCRUD.php", "downloaded_repos/uasoft-indonesia_badaso/src/Middleware/CheckForMaintenanceMode.php", "downloaded_repos/uasoft-indonesia_badaso/src/Middleware/GenerateForSwagger.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2019_12_14_000001_create_personal_access_tokens_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_12_000000_create_badaso_users_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_12_100000_create_badaso_password_resets_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_13_064800_create_data_type.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_18_014827_create_configurations.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_18_014939_create_roles.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_18_014950_create_permissions.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_18_015020_create_menus.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_18_015029_create_menu_items.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_18_015852_create_user_roles.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2020_11_18_020028_create_role_permissions.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_03_05_093223_create_activity_log_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_03_09_064445_create_user_verifications.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_03_12_073541_create_email_resets.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_04_26_014032_create_firebase_cloud_messages_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_04_28_004319_create_f_c_m_messages_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_09_29_094036_add_username_to_users_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_10_05_024140_rename_f_c_m_messages_table_to_notifications.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_10_21_054710_add_is_show_header_and_expand_in_menus_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2021_11_25_100130_add_is_expand_in_badaso_menu_items_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2022_03_08_090850_change_order_integer_data_type_in_menus_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2022_03_18_075818_add_event_column_to_activity_log_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2022_03_18_075819_add_batch_uuid_column_to_activity_log_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2022_11_15_081356_add_phone_number_to_badaso_users_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2023_02_02_073310_alter_table_badaso_permissions_tables.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2023_02_24_042847_alter_badaso_users_tables.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2023_09_25_021749_add_address_to_badaso_users_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Migrations/2023_09_25_091419_add_gender_to_badaso_users_table.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/Configuration.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/DataRow.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/DataType.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/EmailReset.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/FirebaseCloudMessages.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/Menu.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/MenuItem.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/Migration.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/Notification.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/PasswordReset.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/Permission.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/PersonalAccessToken.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/Role.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/RolePermission.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/User.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/UserRole.php", "downloaded_repos/uasoft-indonesia_badaso/src/Models/UserVerification.php", "downloaded_repos/uasoft-indonesia_badaso/src/OrchestratorHandlers/CRUDDataAddedHandler.php", "downloaded_repos/uasoft-indonesia_badaso/src/OrchestratorHandlers/CRUDDataDeletedHandler.php", "downloaded_repos/uasoft-indonesia_badaso/src/OrchestratorHandlers/CRUDDataUpdatedHandler.php", "downloaded_repos/uasoft-indonesia_badaso/src/Providers/BadasoServiceProvider.php", "downloaded_repos/uasoft-indonesia_badaso/src/Providers/DropboxServiceProvider.php", "downloaded_repos/uasoft-indonesia_badaso/src/Providers/GoogleDriveServiceProvider.php", "downloaded_repos/uasoft-indonesia_badaso/src/Providers/OrchestratorEventServiceProvider.php", "downloaded_repos/uasoft-indonesia_badaso/src/Routes/api.php", "downloaded_repos/uasoft-indonesia_badaso/src/Routes/web.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/CRUD/BadasoDeploymentOrchestratorSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/Configurations/BadasoSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/Configurations/ConfigurationsSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/Configurations/FixedMenuItemSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/Configurations/MenusSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/Configurations/PermissionsSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/Configurations/RolePermissionsSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/Configurations/RolesSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Seeder/ManualGenerate/BadasoManualGenerateSeeder.php", "downloaded_repos/uasoft-indonesia_badaso/src/Swagger/swagger_models/auth.php", "downloaded_repos/uasoft-indonesia_badaso/src/Swagger/swagger_models/settings/badaso.php", "downloaded_repos/uasoft-indonesia_badaso/src/Swagger/swagger_models/upload-file.php", "downloaded_repos/uasoft-indonesia_badaso/src/Swagger/swagger_models/user.php", "downloaded_repos/uasoft-indonesia_badaso/src/Traits/FileHandler.php", "downloaded_repos/uasoft-indonesia_badaso/src/Traits/Seedable.php", "downloaded_repos/uasoft-indonesia_badaso/src/Widgets/PermissionWidget.php", "downloaded_repos/uasoft-indonesia_badaso/src/Widgets/RoleWidget.php", "downloaded_repos/uasoft-indonesia_badaso/src/Widgets/UserWidget.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/.DS_Store", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/before-request.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/handle-error.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/index.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-activity-log.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-auth-user.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-auth.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-configuration.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-crud.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-dashboard.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-data.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-database.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-entity.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-fcm.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-file.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-maintenance.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-menu.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-permission.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-role.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-table.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso-user.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/modules/badaso.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/query-string.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/api/resource.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/app.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/apps/App.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/_variable.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/base/reset.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_button.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_container.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_custom.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_footer.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_layout.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_mixins.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_navbar.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_reboot.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_sidebar.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/layout/_vs-modified.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_action-card.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_alert.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_auth-card-header.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_breadcrumb-hover.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_breadcrumb-row.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_breadcrumb.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_checkbox.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_code-editor.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_collapse-item.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_collapse.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_color-picker.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_date.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_datetime.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_dropdown-item.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_dropdown.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_editor.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_email.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_hidden.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_license-blocker.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_loading-page.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_logo-display.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_maintenance.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_notification-message.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_number.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_pagination.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_password.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_radio.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_search.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_select-multiple.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_select.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_server-side-table.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_sidebar-group.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_slider.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_switch.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_table.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_tags.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_text.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_textarea.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_time.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_unauthorize.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_upload-file-multiple.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_upload-file.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_upload-image-multiple.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_upload-image.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/_url.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/module/index.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_activity-log.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_api-documentation.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_crud-generated.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_crud-management.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_database-management.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_file-manager.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_forgot-password.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_home.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_log-viewer.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_login.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_menu-management.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_page-not-found.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_profile.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_register.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_reset-password.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_site-management.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/_verify.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/page/index.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/state/_drag.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/state/_message.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/state/index.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/assets/scss/style.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoAlertBlock.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoAuthCardHeader.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoBreadcrumb.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoBreadcrumbHover.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoBreadcrumbRow.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCheckbox.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCollapse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCollapseItem.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDate.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDatetime.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDropdown.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDropdownItem.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEmail.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoHidden.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoLicenseBlocker.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoLoadingPage.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoLogoDisplay.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNavbar.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNotificationMessage.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNumber.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPagination.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPassword.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPrompt.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoRadio.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSearch.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelect.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelectMultiple.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoServerSideTable.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSidebarGroup.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSidebarItem.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSidebarMenu.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSidebarMenuCallback.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSwitch.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTable.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTags.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoText.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTextarea.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTh.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTime.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUnauthorize.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFile.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFileMultiple.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImageMultiple.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUrl.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoWidget.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/lang/index.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/lang/modules/en.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/lang/modules/id.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/layout/admin/Container.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/layout/admin/footer/Footer.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/layout/admin/header/Navbar.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/layout/admin/sidebar/SideBar.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/layout/auth/Container.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/layout/public/Container.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/activity-log/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/activity-log/read.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/api-documentation/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/forgot-password.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/login.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/reset-password.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/secret-login.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/verify.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/configuration/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/configuration/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse-bin.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/edit.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/read.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/sort.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-management/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-management/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-management/edit.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-management/read.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/data-pending-add/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/data-pending-edit/read.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/database-management/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/database-management/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/database-management/edit.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/error/PageNotFound.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/file-manager/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/home.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/image-manager/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/index.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/log-viewer/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/maintenance.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/menu-management/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/menu-management/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/menu-management/builder.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/menu-management/edit.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/menu-management/permissions.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/notification/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/permission-management/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/permission-management/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/permission-management/edit.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/permission-management/read.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/role-management/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/role-management/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/role-management/edit.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/role-management/permissions.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/role-management/read.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/user/profile.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/user-management/add.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/user-management/browse.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/user-management/edit.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/user-management/read.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/user-management/roles.vue", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/admin/admin-router.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/admin/configuration-router.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/admin/generated-router.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/auth/auth-router.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/excludeRouter.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/others/readme.md", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/public/public-router.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/router.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/store/modules/badaso.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/store/store.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/broadcast-messages.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/case-convert.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/check-connection.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/color.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/database-helper.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/firebase.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/helper.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/utils/indexed-db.js", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/.DS_Store", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/api/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/components/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/lang/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/pages/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/routers/admin/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/routers/auth/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/routers/others/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/routers/public/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/scss/custom.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/scss/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/stores/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/customization/utils/readme.MD", "downloaded_repos/uasoft-indonesia_badaso/src/resources/lang/en/api_response.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/lang/en/notification.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/lang/en/validation.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/lang/id/api_response.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/lang/id/notification.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/lang/id/validation.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/sass/_variables.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/sass/app.scss", "downloaded_repos/uasoft-indonesia_badaso/src/resources/views/admin-panel/index.blade.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/views/landing-page/index.blade.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/views/mail/email-verification.blade.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/views/mail/forgot-password.blade.php", "downloaded_repos/uasoft-indonesia_badaso/src/resources/views/maintenance.blade.php", "downloaded_repos/uasoft-indonesia_badaso/src/stubs/data_seed.stub", "downloaded_repos/uasoft-indonesia_badaso/src/stubs/delete_seed.stub", "downloaded_repos/uasoft-indonesia_badaso/src/stubs/migration.stub", "downloaded_repos/uasoft-indonesia_badaso/src/stubs/row_seed.stub", "downloaded_repos/uasoft-indonesia_badaso/src/stubs/seed.stub"], "skipped": [{"path": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpstan.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/.github/workflows/phpunit.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiConfigurationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiCrudManagementTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiMenuItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiMenuTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiPermissionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiRolePermissionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiRoleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/BadasoApiUserRoleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Feature/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/uasoft-indonesia_badaso/tests/Unit/ExampleTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7312369346618652, "profiling_times": {"config_time": 5.758577108383179, "core_time": 4.500900506973267, "ignores_time": 0.001758575439453125, "total_time": 10.261966228485107}, "parsing_time": {"total_time": 2.74117112159729, "per_file_time": {"mean": 0.009229532395950472, "std_dev": 0.0007469444079883909}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 9.709927797317505, "per_file_time": {"mean": 0.007031084574451484, "std_dev": 0.001256526780148235}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.480463981628418, "per_file_and_rule_time": {"mean": 0.0031923603367161107, "std_dev": 0.00011672030731361074}, "very_slow_stats": {"time_ratio": 0.10786605304590045, "count_ratio": 0.002574002574002574}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/lang/modules/en.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.11544394493103027}, {"fpath": "downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/router/admin/configuration-router.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.1521139144897461}]}, "tainting_time": {"total_time": 0.6111631393432617, "per_def_and_rule_time": {"mean": 0.0011234616532045257, "std_dev": 9.054548187344493e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1094278272}, "engine_requested": "OSS", "skipped_rules": []}