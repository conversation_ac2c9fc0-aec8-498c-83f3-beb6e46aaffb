{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 96, "offset": 91}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 25, "col": 71, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 27, "col": 5, "offset": 0}, "end": {"line": 28, "col": 96, "offset": 106}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 34, "col": 9, "offset": 0}, "end": {"line": 34, "col": 71, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 36, "col": 5, "offset": 0}, "end": {"line": 37, "col": 79, "offset": 89}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 96, "offset": 91}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 25, "col": 71, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 27, "col": 5, "offset": 0}, "end": {"line": 28, "col": 96, "offset": 106}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 34, "col": 9, "offset": 0}, "end": {"line": 34, "col": 71, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "start": {"line": 36, "col": 5, "offset": 0}, "end": {"line": 37, "col": 79, "offset": 89}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 36, "col": 120, "offset": 0}, "end": {"line": 36, "col": 164, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 45, "col": 90, "offset": 0}, "end": {"line": 45, "col": 142, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 36, "col": 120, "offset": 0}, "end": {"line": 36, "col": 164, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 45, "col": 90, "offset": 0}, "end": {"line": 45, "col": 142, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 3, "col": 67, "offset": 0}, "end": {"line": 3, "col": 126, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 22, "col": 5, "offset": 0}, "end": {"line": 22, "col": 33, "offset": 28}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 32, "col": 9, "offset": 0}, "end": {"line": 32, "col": 66, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 21, "offset": 20}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 3, "col": 67, "offset": 0}, "end": {"line": 3, "col": 126, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 22, "col": 5, "offset": 0}, "end": {"line": 22, "col": 33, "offset": 28}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 32, "col": 9, "offset": 0}, "end": {"line": 32, "col": 66, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 21, "offset": 20}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 45, "col": 120, "offset": 0}, "end": {"line": 45, "col": 164, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 53, "col": 90, "offset": 0}, "end": {"line": 53, "col": 142, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 45, "col": 120, "offset": 0}, "end": {"line": 45, "col": 164, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 53, "col": 90, "offset": 0}, "end": {"line": 53, "col": 142, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 21, "col": 9, "offset": 0}, "end": {"line": 21, "col": 62, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 99, "offset": 94}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 25, "col": 71, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 27, "col": 5, "offset": 0}, "end": {"line": 28, "col": 99, "offset": 109}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 34, "col": 9, "offset": 0}, "end": {"line": 34, "col": 71, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 36, "col": 5, "offset": 0}, "end": {"line": 37, "col": 79, "offset": 89}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 21, "col": 9, "offset": 0}, "end": {"line": 21, "col": 62, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 99, "offset": 94}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 25, "col": 71, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 27, "col": 5, "offset": 0}, "end": {"line": 28, "col": 99, "offset": 109}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 34, "col": 9, "offset": 0}, "end": {"line": 34, "col": 71, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "start": {"line": 36, "col": 5, "offset": 0}, "end": {"line": 37, "col": 79, "offset": 89}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 36, "col": 124, "offset": 0}, "end": {"line": 36, "col": 168, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 45, "col": 94, "offset": 0}, "end": {"line": 45, "col": 146, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 36, "col": 124, "offset": 0}, "end": {"line": 36, "col": 168, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 45, "col": 94, "offset": 0}, "end": {"line": 45, "col": 146, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 133, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 22, "col": 5, "offset": 0}, "end": {"line": 22, "col": 33, "offset": 28}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 32, "col": 9, "offset": 0}, "end": {"line": 32, "col": 66, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 21, "offset": 20}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 3, "col": 71, "offset": 0}, "end": {"line": 3, "col": 133, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 22, "col": 5, "offset": 0}, "end": {"line": 22, "col": 33, "offset": 28}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 32, "col": 9, "offset": 0}, "end": {"line": 32, "col": 66, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 21, "offset": 20}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 45, "col": 124, "offset": 0}, "end": {"line": 45, "col": 168, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 53, "col": 94, "offset": 0}, "end": {"line": 53, "col": 146, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 45, "col": 124, "offset": 0}, "end": {"line": 45, "col": 168, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 53, "col": 94, "offset": 0}, "end": {"line": 53, "col": 146, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 36, "col": 124, "offset": 0}, "end": {"line": 36, "col": 168, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 45, "col": 94, "offset": 0}, "end": {"line": 45, "col": 146, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 36, "col": 124, "offset": 0}, "end": {"line": 36, "col": 168, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 45, "col": 94, "offset": 0}, "end": {"line": 45, "col": 146, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 44, "col": 124, "offset": 0}, "end": {"line": 44, "col": 168, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 48, "col": 5, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 52, "col": 94, "offset": 0}, "end": {"line": 52, "col": 146, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 53, "col": 1, "offset": 0}, "end": {"line": 53, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 137, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 44, "col": 124, "offset": 0}, "end": {"line": 44, "col": 168, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 48, "col": 5, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 52, "col": 94, "offset": 0}, "end": {"line": 52, "col": 146, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "start": {"line": 53, "col": 1, "offset": 0}, "end": {"line": 53, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "start": {"line": 18, "col": 9, "offset": 0}, "end": {"line": 18, "col": 62, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "start": {"line": 20, "col": 5, "offset": 0}, "end": {"line": 20, "col": 79, "offset": 74}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "start": {"line": 18, "col": 9, "offset": 0}, "end": {"line": 18, "col": 62, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "start": {"line": 20, "col": 5, "offset": 0}, "end": {"line": 20, "col": 79, "offset": 74}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 36, "col": 122, "offset": 0}, "end": {"line": 36, "col": 166, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 45, "col": 92, "offset": 0}, "end": {"line": 45, "col": 144, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 36, "col": 122, "offset": 0}, "end": {"line": 36, "col": 166, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 45, "col": 92, "offset": 0}, "end": {"line": 45, "col": 144, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 45, "col": 122, "offset": 0}, "end": {"line": 45, "col": 166, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 53, "col": 92, "offset": 0}, "end": {"line": 53, "col": 144, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 45, "col": 122, "offset": 0}, "end": {"line": 45, "col": 166, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 53, "col": 92, "offset": 0}, "end": {"line": 53, "col": 144, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 36, "col": 122, "offset": 0}, "end": {"line": 36, "col": 166, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 45, "col": 92, "offset": 0}, "end": {"line": 45, "col": 144, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 36, "col": 122, "offset": 0}, "end": {"line": 36, "col": 166, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 45, "col": 92, "offset": 0}, "end": {"line": 45, "col": 144, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 3, "col": 69, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 111, "offset": 98}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 106, "offset": 89}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 20, "col": 13, "offset": 0}, "end": {"line": 20, "col": 22, "offset": 9}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 25, "col": 5, "offset": 0}, "end": {"line": 25, "col": 33, "offset": 28}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 35, "col": 9, "offset": 0}, "end": {"line": 35, "col": 66, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 21, "offset": 20}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 3, "col": 69, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 111, "offset": 98}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 106, "offset": 89}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 20, "col": 13, "offset": 0}, "end": {"line": 20, "col": 22, "offset": 9}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 25, "col": 5, "offset": 0}, "end": {"line": 25, "col": 33, "offset": 28}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 35, "col": 9, "offset": 0}, "end": {"line": 35, "col": 66, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 21, "offset": 20}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 7, "col": 11, "offset": 134}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 8, "col": 96, "offset": 0}, "end": {"line": 8, "col": 119, "offset": 23}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 9, "col": 9, "offset": 0}, "end": {"line": 9, "col": 19, "offset": 10}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 10, "col": 13, "offset": 0}, "end": {"line": 10, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 16, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 19, "col": 13, "offset": 0}, "end": {"line": 19, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 31, "col": 21, "offset": 0}, "end": {"line": 31, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 40, "col": 21, "offset": 0}, "end": {"line": 40, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 51, "col": 21, "offset": 0}, "end": {"line": 51, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 51, "col": 122, "offset": 0}, "end": {"line": 51, "col": 166, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 55, "col": 5, "offset": 0}, "end": {"line": 57, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 58, "col": 43, "offset": 0}, "end": {"line": 58, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 59, "col": 92, "offset": 0}, "end": {"line": 59, "col": 144, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 60, "col": 1, "offset": 0}, "end": {"line": 60, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 7, "col": 11, "offset": 134}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 8, "col": 96, "offset": 0}, "end": {"line": 8, "col": 119, "offset": 23}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 9, "col": 9, "offset": 0}, "end": {"line": 9, "col": 19, "offset": 10}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 10, "col": 13, "offset": 0}, "end": {"line": 10, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 16, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 19, "col": 13, "offset": 0}, "end": {"line": 19, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 31, "col": 21, "offset": 0}, "end": {"line": 31, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 40, "col": 21, "offset": 0}, "end": {"line": 40, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 51, "col": 21, "offset": 0}, "end": {"line": 51, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 51, "col": 122, "offset": 0}, "end": {"line": 51, "col": 166, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 55, "col": 5, "offset": 0}, "end": {"line": 57, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 58, "col": 43, "offset": 0}, "end": {"line": 58, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 59, "col": 92, "offset": 0}, "end": {"line": 59, "col": 144, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "start": {"line": 60, "col": 1, "offset": 0}, "end": {"line": 60, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 36, "col": 124, "offset": 0}, "end": {"line": 36, "col": 168, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 45, "col": 94, "offset": 0}, "end": {"line": 45, "col": 146, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 36, "col": 124, "offset": 0}, "end": {"line": 36, "col": 168, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 45, "col": 94, "offset": 0}, "end": {"line": 45, "col": 146, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 44, "col": 124, "offset": 0}, "end": {"line": 44, "col": 168, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 48, "col": 5, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 52, "col": 94, "offset": 0}, "end": {"line": 52, "col": 146, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 53, "col": 1, "offset": 0}, "end": {"line": 53, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 3, "col": 75, "offset": 0}, "end": {"line": 3, "col": 139, "offset": 64}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 44, "col": 124, "offset": 0}, "end": {"line": 44, "col": 168, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 48, "col": 5, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 52, "col": 94, "offset": 0}, "end": {"line": 52, "col": 146, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "start": {"line": 53, "col": 1, "offset": 0}, "end": {"line": 53, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 66, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 20, "col": 5, "offset": 0}, "end": {"line": 20, "col": 98, "offset": 93}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 22, "col": 9, "offset": 0}, "end": {"line": 22, "col": 71, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 24, "col": 5, "offset": 0}, "end": {"line": 25, "col": 96, "offset": 106}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 27, "col": 9, "offset": 0}, "end": {"line": 27, "col": 78, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 29, "col": 5, "offset": 0}, "end": {"line": 30, "col": 98, "offset": 108}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 36, "col": 71, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 38, "col": 5, "offset": 0}, "end": {"line": 39, "col": 96, "offset": 106}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 45, "col": 9, "offset": 0}, "end": {"line": 45, "col": 80, "offset": 71}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 47, "col": 5, "offset": 0}, "end": {"line": 48, "col": 96, "offset": 106}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 54, "col": 9, "offset": 0}, "end": {"line": 54, "col": 80, "offset": 71}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 56, "col": 5, "offset": 0}, "end": {"line": 57, "col": 79, "offset": 89}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 66, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 20, "col": 5, "offset": 0}, "end": {"line": 20, "col": 98, "offset": 93}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 22, "col": 9, "offset": 0}, "end": {"line": 22, "col": 71, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 24, "col": 5, "offset": 0}, "end": {"line": 25, "col": 96, "offset": 106}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 27, "col": 9, "offset": 0}, "end": {"line": 27, "col": 78, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 29, "col": 5, "offset": 0}, "end": {"line": 30, "col": 98, "offset": 108}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 36, "col": 71, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 38, "col": 5, "offset": 0}, "end": {"line": 39, "col": 96, "offset": 106}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 45, "col": 9, "offset": 0}, "end": {"line": 45, "col": 80, "offset": 71}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 47, "col": 5, "offset": 0}, "end": {"line": 48, "col": 96, "offset": 106}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 54, "col": 9, "offset": 0}, "end": {"line": 54, "col": 80, "offset": 71}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "start": {"line": 56, "col": 5, "offset": 0}, "end": {"line": 57, "col": 79, "offset": 89}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 36, "col": 122, "offset": 0}, "end": {"line": 36, "col": 166, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 45, "col": 92, "offset": 0}, "end": {"line": 45, "col": 144, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 36, "col": 122, "offset": 0}, "end": {"line": 36, "col": 166, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 45, "col": 92, "offset": 0}, "end": {"line": 45, "col": 144, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 3, "col": 69, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 96, "offset": 79}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 19, "col": 13, "offset": 0}, "end": {"line": 19, "col": 111, "offset": 98}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 106, "offset": 89}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 22, "offset": 9}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 33, "offset": 28}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 36, "col": 66, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 21, "offset": 20}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 3, "col": 69, "offset": 0}, "end": {"line": 3, "col": 130, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 4, "col": 9, "offset": 0}, "end": {"line": 4, "col": 35, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 41, "offset": 66}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 80, "offset": 67}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 15, "col": 17, "offset": 0}, "end": {"line": 15, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 76}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 17, "col": 17, "offset": 0}, "end": {"line": 17, "col": 86, "offset": 69}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 96, "offset": 79}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 19, "col": 13, "offset": 0}, "end": {"line": 19, "col": 111, "offset": 98}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 106, "offset": 89}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 22, "offset": 9}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 33, "offset": 28}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 36, "col": 66, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 21, "offset": 20}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 45, "col": 122, "offset": 0}, "end": {"line": 45, "col": 166, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 53, "col": 92, "offset": 0}, "end": {"line": 53, "col": 144, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 3, "col": 73, "offset": 0}, "end": {"line": 3, "col": 134, "offset": 61}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 45, "col": 122, "offset": 0}, "end": {"line": 45, "col": 166, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 53, "col": 92, "offset": 0}, "end": {"line": 53, "col": 144, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 36, "col": 125, "offset": 0}, "end": {"line": 36, "col": 169, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 45, "col": 95, "offset": 0}, "end": {"line": 45, "col": 147, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 36, "col": 125, "offset": 0}, "end": {"line": 36, "col": 169, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 45, "col": 95, "offset": 0}, "end": {"line": 45, "col": 147, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 45, "col": 125, "offset": 0}, "end": {"line": 45, "col": 169, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 53, "col": 95, "offset": 0}, "end": {"line": 53, "col": 147, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 3, "col": 76, "offset": 0}, "end": {"line": 3, "col": 141, "offset": 65}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 45, "col": 21, "offset": 0}, "end": {"line": 45, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 45, "col": 125, "offset": 0}, "end": {"line": 45, "col": 169, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 49, "col": 5, "offset": 0}, "end": {"line": 51, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 52, "col": 43, "offset": 0}, "end": {"line": 52, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 53, "col": 95, "offset": 0}, "end": {"line": 53, "col": 147, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm:3:\n `<?= e(trans('backend::lang.form.create')) ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 3, "col": 9, "offset": 0}, "end": {"line": 3, "col": 53, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 6, "col": 9, "offset": 0}, "end": {"line": 6, "col": 63, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 66, "offset": 53}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 23, "col": 79, "offset": 74}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 36, "col": 119, "offset": 0}, "end": {"line": 36, "col": 163, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 45, "col": 89, "offset": 0}, "end": {"line": 45, "col": 141, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 65, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 36, "col": 119, "offset": 0}, "end": {"line": 36, "col": 163, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 41, "col": 5, "offset": 0}, "end": {"line": 43, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 44, "col": 43, "offset": 0}, "end": {"line": 44, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 45, "col": 89, "offset": 0}, "end": {"line": 45, "col": 141, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 34, "offset": 59}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 42, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 15, "col": 43, "offset": 0}, "end": {"line": 15, "col": 70, "offset": 27}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "start": {"line": 20, "col": 9, "offset": 0}, "end": {"line": 20, "col": 61, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 8, "col": 30, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 61, "offset": 40}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 44, "col": 119, "offset": 0}, "end": {"line": 44, "col": 163, "offset": 44}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 48, "col": 5, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 36}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 77, "offset": 34}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 52, "col": 89, "offset": 0}, "end": {"line": 52, "col": 141, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 53, "col": 1, "offset": 0}, "end": {"line": 53, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm:1:\n `<?php Block::put('breadcrumb') ?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 3, "col": 70, "offset": 0}, "end": {"line": 3, "col": 127, "offset": 57}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 4, "col": 13, "offset": 0}, "end": {"line": 4, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 10, "col": 45, "offset": 105}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 13, "col": 13, "offset": 0}, "end": {"line": 13, "col": 39, "offset": 26}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 25, "col": 21, "offset": 0}, "end": {"line": 25, "col": 63, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 61, "offset": 40}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 44, "col": 119, "offset": 0}, "end": {"line": 44, "col": 163, "offset": 44}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 48, "col": 5, "offset": 0}, "end": {"line": 50, "col": 15, "offset": 36}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 77, "offset": 34}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 52, "col": 89, "offset": 0}, "end": {"line": 52, "col": 141, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "start": {"line": 53, "col": 1, "offset": 0}, "end": {"line": 53, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 3, "offset": 622}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 80, "offset": 71}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 15, "col": 13, "offset": 0}, "end": {"line": 15, "col": 75, "offset": 62}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 42}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 22, "col": 21, "offset": 0}, "end": {"line": 22, "col": 76, "offset": 55}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 25, "col": 13, "offset": 0}, "end": {"line": 26, "col": 53, "offset": 63}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 29, "col": 21, "offset": 0}, "end": {"line": 29, "col": 73, "offset": 52}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 32, "col": 13, "offset": 0}, "end": {"line": 33, "col": 56, "offset": 66}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 37, "col": 21, "offset": 0}, "end": {"line": 37, "col": 79, "offset": 58}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 40, "col": 13, "offset": 0}, "end": {"line": 41, "col": 53, "offset": 63}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 75, "offset": 54}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 47, "col": 13, "offset": 0}, "end": {"line": 48, "col": 56, "offset": 66}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 51, "col": 21, "offset": 0}, "end": {"line": 51, "col": 87, "offset": 66}}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 54, "col": 13, "offset": 0}, "end": {"line": 54, "col": 23, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm:1:\n `<?php\n$bHideOfferImportButton = \\Lovata\\Shopaholic\\Models\\Settings::getValue('hide_offer_import_from_csv');\n$bHideProductImportButton = \\Lovata\\Shopaholic\\Models\\Settings::getValue('hide_product_import_from_csv');\n$bHideBrandImportButton = \\Lovata\\Shopaholic\\Models\\Settings::getValue('hide_brand_import_from_csv');\n$bHideCategoryImportButton = \\Lovata\\Shopaholic\\Models\\Settings::getValue('hide_category_import_from_csv');\n$bHidePropertyImportButton = \\Lovata\\Shopaholic\\Models\\Settings::getValue('hide_property_import_from_csv') || !\\System\\Classes\\PluginManager::instance()->hasPlugin('Lovata.PropertiesShopaholic');\n?>` was unexpected", "path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "spans": [{"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 3, "offset": 622}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 11, "col": 9, "offset": 0}, "end": {"line": 11, "col": 80, "offset": 71}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 15, "col": 13, "offset": 0}, "end": {"line": 15, "col": 75, "offset": 62}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 42}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 22, "col": 21, "offset": 0}, "end": {"line": 22, "col": 76, "offset": 55}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 25, "col": 13, "offset": 0}, "end": {"line": 26, "col": 53, "offset": 63}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 29, "col": 21, "offset": 0}, "end": {"line": 29, "col": 73, "offset": 52}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 32, "col": 13, "offset": 0}, "end": {"line": 33, "col": 56, "offset": 66}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 37, "col": 21, "offset": 0}, "end": {"line": 37, "col": 79, "offset": 58}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 40, "col": 13, "offset": 0}, "end": {"line": 41, "col": 53, "offset": 63}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 75, "offset": 54}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 47, "col": 13, "offset": 0}, "end": {"line": 48, "col": 56, "offset": 66}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 51, "col": 21, "offset": 0}, "end": {"line": 51, "col": 87, "offset": 66}}, {"file": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "start": {"line": 54, "col": 13, "offset": 0}, "end": {"line": 54, "col": 23, "offset": 10}}]}], "paths": {"scanned": ["downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/.codeclimate.yml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/.editorconfig", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/.github/FUNDING.yml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/.gitignore", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/CONTRIBUTING.md", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/LICENSE.md", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/PHPMD_custom.xml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/Plugin.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/README.md", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/assets/images/icon.svg", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/assets/images/logo.png", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/assets/images/shopaholic-banner.png", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/collection/BrandCollection.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/collection/CategoryCollection.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/collection/CurrencyCollection.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/collection/OfferCollection.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/collection/ProductCollection.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/collection/PromoBlockCollection.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/collection/TaxCollection.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/console/CheckTableIntegrity.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/console/ImportFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/console/PreconfigureImportSettingsFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/ExtendMenuHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/brand/BrandModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/category/CategoryModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/currency/CurrencyModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/measure/MeasureModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/offer/ExtendOfferFieldsHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/offer/OfferModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/price/PriceModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/product/ProductModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/product/ProductRelationHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/promoblock/PromoBlockModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/promoblock/PromoBlockRelationHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/tax/ExtendTaxFieldsHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/tax/TaxModelHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/event/tax/TaxRelationHandler.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/AllCategoriesMenuType.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/CatalogMenuType.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/CategoryMenuType.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/CommonMenuType.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/CurrencyHelper.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/MeasureHelper.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/PriceTypeHelper.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/helper/TaxHelper.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportBrandModelFromCSV.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportBrandModelFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportCategoryModelFromCSV.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportCategoryModelFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportOfferModelFromCSV.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportOfferModelFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportOfferPriceFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportProductModelFromCSV.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ImportProductModelFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/import/ParseCategoryXMLNode.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/BrandItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/CategoryItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/CurrencyItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/MeasureItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/OfferItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/ProductItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/PromoBlockItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/item/TaxItem.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/BrandListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/CategoryListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/CurrencyListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/OfferListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/ProductListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/PromoBlockListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/TaxListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/brand/ActiveListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/brand/ListByCategoryStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/brand/ListBySiteStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/brand/SortingListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/category/ActiveListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/category/ListBySiteStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/category/TopLevelListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/currency/ActiveListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/currency/SortingListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/offer/ActiveListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/offer/ListBySiteStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/offer/SortingListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/product/ActiveListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/product/ListByBrandStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/product/ListByCategoryStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/product/ListByPromoBlockStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/product/ListBySiteStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/product/SortingListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/promoblock/ActiveListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/promoblock/HiddenListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/promoblock/NotHiddenListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/promoblock/SortingListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/tax/ActiveListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/classes/store/tax/SortingListStore.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/BrandData.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/BrandList.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/BrandPage.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/Breadcrumbs.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/CategoryData.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/CategoryList.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/CategoryPage.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/CurrencyList.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/ProductData.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/ProductList.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/ProductPage.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/PromoBlockData.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/PromoBlockList.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/components/PromoBlockPage.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/composer.json", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/Brands.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/Categories.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/Currencies.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/Measures.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/Offers.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/PriceTypes.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/Products.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/PromoBlocks.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/Taxes.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_config_filter.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/config_import_export.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/config_reorder.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_config_filter.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/config_import_export.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/config_relation.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/config_reorder.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_config_filter.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/config_reorder.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/config_import_export.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/config_relation.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_config_filter.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/config_reorder.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_config_filter.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_offer.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/config_import_export.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/config_relation.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_config_filter.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_product.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/config_relation.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/config_reorder.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_category.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_config_filter.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_country.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_product.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_state.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/config_form.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/config_list.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/config_relation.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/config_reorder.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/index.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/crowdin.yml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/ar/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/be/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/bg/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/ca/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/cs/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/da/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/de/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/el/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/en/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/es/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/et/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/fa/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/fi/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/fr/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/hu/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/id/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/it/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/ja/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/kk/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/ko/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/ky/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/lt/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/lv/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/nb/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/nl/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/pl/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/pt/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/ro/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/ru/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/sk/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/sv/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/tr/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/uk/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/vi/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/lang/zh/lang.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Brand.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Category.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Currency.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Measure.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Offer.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Price.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/PriceType.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Product.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/PromoBlock.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Settings.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/Tax.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/XmlImportSettings.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/brand/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/brand/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/category/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/category/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/currency/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/currency/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/measure/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/measure/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/offer/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/offer/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/offer/relation_columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/pricetype/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/pricetype/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/product/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/product/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/product/relation_columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/promoblock/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/promoblock/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/settings/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/tax/columns.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/tax/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/models/xmlimportsettings/fields.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/phpunit.xml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/plugin.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_additional_categories.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_brands.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_categories.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_currency.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_entity_site_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_measure.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_offer_site_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_offers.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_price_types.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_prices.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_product_site_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_products.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_promo_block.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_promo_block_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_tax_category_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_tax_country_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_tax_product_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_tax_state_relation.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/create_table_taxes.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/seeder_create_default_currency.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/seeder_price_format.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/seeder_transfer_offer_prices.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/update_table_offers_add_dimensions_field.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/update_table_offers_add_measure_field.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/update_table_offers_add_sorting_field.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/update_table_offers_change_quantity_field.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/update_table_offers_remove_price_field.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/update_table_users_add_currency_field.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/updates/version.yaml", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/views/import_from_xml_info.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/views/offer_price_list.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/ImportFromCSV.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/ImportFromXML.php", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromxml/partials/_widget.htm"], "skipped": [{"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/import.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/reorder.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/brands/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/import.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/reorder.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/categories/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/reorder.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/currencies/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/measures/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/import.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/offers/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/reorder.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/pricetypes/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/import.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/reorder.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/products/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/reorder.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/promoblocks/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/_list_toolbar.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/create.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/preview.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/reorder.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/controllers/taxes/update.htm", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/collection/BrandCollectionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/collection/CategoryCollectionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/collection/OfferCollectionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/collection/ProductCollectionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/item/BrandItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/item/CategoryItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/item/MeasureItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/item/OfferItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/item/ProductItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/models/BrandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/models/CategoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/models/MeasureTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/models/OfferTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/tests/unit/models/ProductTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/oc-shopaholic_oc-shopaholic-plugin/widgets/importfromcsv/partials/_widget.htm", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6381371021270752, "profiling_times": {"config_time": 5.700983762741089, "core_time": 3.0897116661071777, "ignores_time": 0.0019137859344482422, "total_time": 8.793843030929565}, "parsing_time": {"total_time": 0.9325759410858154, "per_file_time": {"mean": 0.003139986333622274, "std_dev": 2.027592109860979e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.0227174758911133, "per_file_time": {"mean": 0.003142117958306771, "std_dev": 3.418496000485892e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7226574420928955, "per_file_and_rule_time": {"mean": 0.00033565138973195316, "std_dev": 4.132358528135229e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}