{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/qoomon_otp-authenticator-webapp/Dockerfile", "start": {"line": 23, "col": 1, "offset": 242}, "end": {"line": 23, "col": 42, "offset": 283}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"http-server\", \"dist\", \"-p\", \"8080\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/qoomon_otp-authenticator-webapp/README.md", "start": {"line": 18, "col": 35, "offset": 779}, "end": {"line": 18, "col": 74, "offset": 818}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/qoomon_otp-authenticator-webapp/app/cookies.js", "start": {"line": 4, "col": 45, "offset": 85}, "end": {"line": 4, "col": 84, "offset": 124}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/qoomon_otp-authenticator-webapp/app/index.js", "start": {"line": 61, "col": 13, "offset": 1616}, "end": {"line": 61, "col": 76, "offset": 1679}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/qoomon_otp-authenticator-webapp/app/index.js", "start": {"line": 69, "col": 9, "offset": 1991}, "end": {"line": 69, "col": 56, "offset": 2038}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/qoomon_otp-authenticator-webapp/.github/dependabot.yml", "downloaded_repos/qoomon_otp-authenticator-webapp/.github/workflows/dependabot-auto-merge.yaml", "downloaded_repos/qoomon_otp-authenticator-webapp/.github/workflows/deploy.yml", "downloaded_repos/qoomon_otp-authenticator-webapp/.gitignore", "downloaded_repos/qoomon_otp-authenticator-webapp/CNAME", "downloaded_repos/qoomon_otp-authenticator-webapp/Dockerfile", "downloaded_repos/qoomon_otp-authenticator-webapp/README.md", "downloaded_repos/qoomon_otp-authenticator-webapp/app/cookies.js", "downloaded_repos/qoomon_otp-authenticator-webapp/app/favicon.ico", "downloaded_repos/qoomon_otp-authenticator-webapp/app/github-icon.png", "downloaded_repos/qoomon_otp-authenticator-webapp/app/index.css", "downloaded_repos/qoomon_otp-authenticator-webapp/app/index.html", "downloaded_repos/qoomon_otp-authenticator-webapp/app/index.js", "downloaded_repos/qoomon_otp-authenticator-webapp/app/index_dark.css", "downloaded_repos/qoomon_otp-authenticator-webapp/app/light-bulb.png", "downloaded_repos/qoomon_otp-authenticator-webapp/app/more.png", "downloaded_repos/qoomon_otp-authenticator-webapp/app/otp-authenticator.png", "downloaded_repos/qoomon_otp-authenticator-webapp/app/otpauthUrl.js", "downloaded_repos/qoomon_otp-authenticator-webapp/app/scan-image.png", "downloaded_repos/qoomon_otp-authenticator-webapp/app/scan-video.png", "downloaded_repos/qoomon_otp-authenticator-webapp/app/totp.js", "downloaded_repos/qoomon_otp-authenticator-webapp/package-lock.json", "downloaded_repos/qoomon_otp-authenticator-webapp/package.json", "downloaded_repos/qoomon_otp-authenticator-webapp/webpack.config.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7139410972595215, "profiling_times": {"config_time": 5.965019464492798, "core_time": 2.6539885997772217, "ignores_time": 0.001707315444946289, "total_time": 8.621428489685059}, "parsing_time": {"total_time": 0.38070178031921387, "per_file_time": {"mean": 0.031725148359934494, "std_dev": 0.0010891916265664116}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.2643296718597412, "per_file_time": {"mean": 0.021072161197662354, "std_dev": 0.0024056053390200324}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.3158276081085205, "per_file_and_rule_time": {"mean": 0.00248683155990961, "std_dev": 3.503369441107132e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.052992820739746094, "per_def_and_rule_time": {"mean": 0.0004140064120292664, "std_dev": 7.047287570727255e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}