{"version": "1.130.0", "results": [{"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/PHP.php", "start": {"line": 64, "col": 13, "offset": 1507}, "end": {"line": 64, "col": 31, "offset": 1525}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Controller/Logout.php", "start": {"line": 281, "col": 25, "offset": 9542}, "end": {"line": 281, "col": 42, "offset": 9559}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Controller/Logout.php", "start": {"line": 281, "col": 46, "offset": 9563}, "end": {"line": 281, "col": 60, "offset": 9577}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Storage/SQLPermanentStorage.php", "start": {"line": 176, "col": 25, "offset": 5342}, "end": {"line": 176, "col": 51, "offset": 5368}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Storage/SQLPermanentStorage.php", "start": {"line": 234, "col": 39, "offset": 7072}, "end": {"line": 234, "col": 75, "offset": 7108}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/ProcessingFilter.php", "start": {"line": 82, "col": 13, "offset": 2466}, "end": {"line": 82, "col": 45, "offset": 2498}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/State.php", "start": {"line": 305, "col": 18, "offset": 10042}, "end": {"line": 305, "col": 37, "offset": 10061}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Memcache.php", "start": {"line": 84, "col": 21, "offset": 2354}, "end": {"line": 84, "col": 49, "offset": 2382}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandler.php", "start": {"line": 410, "col": 17, "offset": 15445}, "end": {"line": 410, "col": 60, "offset": 15488}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandlerSerialize.php", "start": {"line": 176, "col": 18, "offset": 5000}, "end": {"line": 176, "col": 36, "offset": 5018}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/SessionHandlerPHP.php", "start": {"line": 266, "col": 24, "offset": 9107}, "end": {"line": 266, "col": 45, "offset": 9128}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Store/RedisStore.php", "start": {"line": 141, "col": 16, "offset": 4623}, "end": {"line": 141, "col": 36, "offset": 4643}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Store/SQLStore.php", "start": {"line": 386, "col": 18, "offset": 13464}, "end": {"line": 386, "col": 37, "offset": 13483}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/HTTP.php", "start": {"line": 976, "col": 16, "offset": 36242}, "end": {"line": 976, "col": 50, "offset": 36276}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/HTTP.php", "start": {"line": 1002, "col": 16, "offset": 37684}, "end": {"line": 1002, "col": 50, "offset": 37718}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/HTTP.php", "start": {"line": 1218, "col": 20, "offset": 45275}, "end": {"line": 1218, "col": 89, "offset": 45344}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/System.php", "start": {"line": 228, "col": 17, "offset": 6978}, "end": {"line": 228, "col": 33, "offset": 6994}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/System.php", "start": {"line": 239, "col": 13, "offset": 7384}, "end": {"line": 239, "col": 29, "offset": 7400}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "start": {"line": 33, "col": 37, "offset": 723}, "end": {"line": 33, "col": 56, "offset": 742}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml:33:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "start": {"line": 33, "col": 37, "offset": 723}, "end": {"line": 33, "col": 56, "offset": 742}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "start": {"line": 39, "col": 19, "offset": 915}, "end": {"line": 39, "col": 22, "offset": 918}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml:39:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "start": {"line": 39, "col": 19, "offset": 915}, "end": {"line": 39, "col": 22, "offset": 918}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "start": {"line": 86, "col": 39, "offset": 2428}, "end": {"line": 86, "col": 55, "offset": 2444}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml:86:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.version` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "start": {"line": 86, "col": 39, "offset": 2428}, "end": {"line": 86, "col": 55, "offset": 2444}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 62, "col": 37, "offset": 1610}, "end": {"line": 62, "col": 56, "offset": 1629}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:62:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 62, "col": 37, "offset": 1610}, "end": {"line": 62, "col": 56, "offset": 1629}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 65, "col": 37, "offset": 1729}, "end": {"line": 65, "col": 56, "offset": 1748}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:65:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 65, "col": 37, "offset": 1729}, "end": {"line": 65, "col": 56, "offset": 1748}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 130, "col": 37, "offset": 3986}, "end": {"line": 130, "col": 56, "offset": 4005}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:130:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 130, "col": 37, "offset": 3986}, "end": {"line": 130, "col": 56, "offset": 4005}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 133, "col": 37, "offset": 4105}, "end": {"line": 133, "col": 56, "offset": 4124}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:133:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 133, "col": 37, "offset": 4105}, "end": {"line": 133, "col": 56, "offset": 4124}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 178, "col": 37, "offset": 5731}, "end": {"line": 178, "col": 56, "offset": 5750}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:178:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 178, "col": 37, "offset": 5731}, "end": {"line": 178, "col": 56, "offset": 5750}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 224, "col": 15, "offset": 7356}, "end": {"line": 224, "col": 18, "offset": 7359}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:224:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 224, "col": 15, "offset": 7356}, "end": {"line": 224, "col": 18, "offset": 7359}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 232, "col": 15, "offset": 7586}, "end": {"line": 232, "col": 18, "offset": 7589}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:232:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 232, "col": 15, "offset": 7586}, "end": {"line": 232, "col": 18, "offset": 7589}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 239, "col": 15, "offset": 7778}, "end": {"line": 239, "col": 18, "offset": 7781}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:239:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 239, "col": 15, "offset": 7778}, "end": {"line": 239, "col": 18, "offset": 7781}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 263, "col": 37, "offset": 8673}, "end": {"line": 263, "col": 56, "offset": 8692}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml:263:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "start": {"line": 263, "col": 37, "offset": 8673}, "end": {"line": 263, "col": 56, "offset": 8692}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 20, "col": 18, "offset": 0}, "end": {"line": 21, "col": 48, "offset": 78}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 22, "col": 18, "offset": 0}, "end": {"line": 23, "col": 50, "offset": 92}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 24, "col": 18, "offset": 0}, "end": {"line": 25, "col": 42, "offset": 70}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 26, "col": 18, "offset": 0}, "end": {"line": 27, "col": 52, "offset": 90}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 28, "col": 18, "offset": 0}, "end": {"line": 29, "col": 52, "offset": 106}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 30, "col": 18, "offset": 0}, "end": {"line": 31, "col": 58, "offset": 88}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 32, "col": 18, "offset": 0}, "end": {"line": 33, "col": 54, "offset": 92}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 34, "col": 18, "offset": 0}, "end": {"line": 35, "col": 62, "offset": 90}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 36, "col": 18, "offset": 0}, "end": {"line": 37, "col": 46, "offset": 90}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 38, "col": 18, "offset": 0}, "end": {"line": 39, "col": 54, "offset": 78}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 40, "col": 18, "offset": 0}, "end": {"line": 41, "col": 46, "offset": 72}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 42, "col": 18, "offset": 0}, "end": {"line": 43, "col": 42, "offset": 82}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 44, "col": 18, "offset": 0}, "end": {"line": 45, "col": 54, "offset": 94}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 46, "col": 18, "offset": 0}, "end": {"line": 47, "col": 68, "offset": 112}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 48, "col": 18, "offset": 0}, "end": {"line": 49, "col": 62, "offset": 120}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 50, "col": 18, "offset": 0}, "end": {"line": 51, "col": 66, "offset": 96}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 52, "col": 18, "offset": 0}, "end": {"line": 53, "col": 52, "offset": 86}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 54, "col": 18, "offset": 0}, "end": {"line": 55, "col": 44, "offset": 82}}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 56, "col": 18, "offset": 0}, "end": {"line": 56, "col": 50, "offset": 32}}]], "message": "Syntax error at line downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php:20:\n `const ACSPARAMS = 'ACSPARAMS';\n    final public const ARSPARAMS = 'ARSPARAMS';` was unexpected", "path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "spans": [{"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 20, "col": 18, "offset": 0}, "end": {"line": 21, "col": 48, "offset": 78}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 22, "col": 18, "offset": 0}, "end": {"line": 23, "col": 50, "offset": 92}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 24, "col": 18, "offset": 0}, "end": {"line": 25, "col": 42, "offset": 70}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 26, "col": 18, "offset": 0}, "end": {"line": 27, "col": 52, "offset": 90}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 28, "col": 18, "offset": 0}, "end": {"line": 29, "col": 52, "offset": 106}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 30, "col": 18, "offset": 0}, "end": {"line": 31, "col": 58, "offset": 88}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 32, "col": 18, "offset": 0}, "end": {"line": 33, "col": 54, "offset": 92}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 34, "col": 18, "offset": 0}, "end": {"line": 35, "col": 62, "offset": 90}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 36, "col": 18, "offset": 0}, "end": {"line": 37, "col": 46, "offset": 90}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 38, "col": 18, "offset": 0}, "end": {"line": 39, "col": 54, "offset": 78}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 40, "col": 18, "offset": 0}, "end": {"line": 41, "col": 46, "offset": 72}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 42, "col": 18, "offset": 0}, "end": {"line": 43, "col": 42, "offset": 82}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 44, "col": 18, "offset": 0}, "end": {"line": 45, "col": 54, "offset": 94}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 46, "col": 18, "offset": 0}, "end": {"line": 47, "col": 68, "offset": 112}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 48, "col": 18, "offset": 0}, "end": {"line": 49, "col": 62, "offset": 120}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 50, "col": 18, "offset": 0}, "end": {"line": 51, "col": 66, "offset": 96}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 52, "col": 18, "offset": 0}, "end": {"line": 53, "col": 52, "offset": 86}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 54, "col": 18, "offset": 0}, "end": {"line": 55, "col": 44, "offset": 82}}, {"file": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "start": {"line": 56, "col": 18, "offset": 0}, "end": {"line": 56, "col": 50, "offset": 32}}]}], "paths": {"scanned": ["downloaded_repos/simplesamlphp_simplesamlphp/.editorconfig", "downloaded_repos/simplesamlphp_simplesamlphp/.gitattributes", "downloaded_repos/simplesamlphp_simplesamlphp/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/simplesamlphp_simplesamlphp/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/simplesamlphp_simplesamlphp/.github/dependabot.yml", "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/autolock-conversations.yml", "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/documentation.yml", "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/translations.yml", "downloaded_repos/simplesamlphp_simplesamlphp/.gitignore", "downloaded_repos/simplesamlphp_simplesamlphp/.markdownlint.yml", "downloaded_repos/simplesamlphp_simplesamlphp/.markdownlintignore", "downloaded_repos/simplesamlphp_simplesamlphp/.php_cs.dist", "downloaded_repos/simplesamlphp_simplesamlphp/CONTRIBUTING.md", "downloaded_repos/simplesamlphp_simplesamlphp/COPYING", "downloaded_repos/simplesamlphp_simplesamlphp/LICENSE", "downloaded_repos/simplesamlphp_simplesamlphp/README.md", "downloaded_repos/simplesamlphp_simplesamlphp/SECURITY.md", "downloaded_repos/simplesamlphp_simplesamlphp/TESTING.md", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/addurnprefix.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/deprecatedSchacNS.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/entra2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/facebook2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/feide-oid.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/linkedin2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/name2claim.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/name2entra.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/name2oid.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/name2urn.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/newSchacNS.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/oid-feide.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/oid2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/oid2urn.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/openid2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/removeurnprefix.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/test.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/twitter2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/urn2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/urn2oid.php", "downloaded_repos/simplesamlphp_simplesamlphp/attributemap/windowslive2name.php", "downloaded_repos/simplesamlphp_simplesamlphp/bin/console", "downloaded_repos/simplesamlphp_simplesamlphp/bin/convertTranslations.php", "downloaded_repos/simplesamlphp_simplesamlphp/bin/importPdoMetadata.php", "downloaded_repos/simplesamlphp_simplesamlphp/bin/initMDSPdo.php", "downloaded_repos/simplesamlphp_simplesamlphp/bin/ldapattrschemaparser.pl", "downloaded_repos/simplesamlphp_simplesamlphp/bin/memcacheSync.php", "downloaded_repos/simplesamlphp_simplesamlphp/bin/pwgen.php", "downloaded_repos/simplesamlphp_simplesamlphp/bin/translateAttributes.php", "downloaded_repos/simplesamlphp_simplesamlphp/bin/translations", "downloaded_repos/simplesamlphp_simplesamlphp/cert/.gitkeep", "downloaded_repos/simplesamlphp_simplesamlphp/codecov.yml", "downloaded_repos/simplesamlphp_simplesamlphp/composer.json", "downloaded_repos/simplesamlphp_simplesamlphp/composer.lock", "downloaded_repos/simplesamlphp_simplesamlphp/config/acl.php.dist", "downloaded_repos/simplesamlphp_simplesamlphp/config/authsources.php.dist", "downloaded_repos/simplesamlphp_simplesamlphp/config/config.php.dist", "downloaded_repos/simplesamlphp_simplesamlphp/docs/README", "downloaded_repos/simplesamlphp_simplesamlphp/docs/conf.py", "downloaded_repos/simplesamlphp_simplesamlphp/docs/index.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/resources/simplesamlphp-googleapps/googleapps-cert.png", "downloaded_repos/simplesamlphp_simplesamlphp/docs/resources/simplesamlphp-googleapps/googleapps-menu.png", "downloaded_repos/simplesamlphp_simplesamlphp/docs/resources/simplesamlphp-googleapps/googleapps-sso.png", "downloaded_repos/simplesamlphp_simplesamlphp/docs/resources/simplesamlphp-googleapps/googleapps-ssoconfig.png", "downloaded_repos/simplesamlphp_simplesamlphp/docs/resources/simplesamlphp-install/screenshot-installationpage.png", "downloaded_repos/simplesamlphp_simplesamlphp/docs/resources/simplesamlphp-sp/screenshot-example.png", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-advancedfeatures.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-artifact-idp.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-artifact-sp.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-authproc.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-authsource.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-changelog-1.x.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-changelog.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-customauth.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-database.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-developer-information.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-ecp-idp.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-errorhandling.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-googleapps.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-hok-idp.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-hok-sp.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-idp-more.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-idp.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-install-repo.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-install.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-maintenance.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-metadata-endpoints.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-metadata-extensions-attributes.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-metadata-extensions-idpdisc.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-metadata-extensions-rpi.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-metadata-extensions-ui.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-metadata-pdostoragehandler.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-modules.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-nostate.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-reference-idp-hosted.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-reference-idp-remote.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-reference-sp-remote.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-scoping.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-sp-api.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-sp.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-theming.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-translation.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.10.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.11.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.12.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.13.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.14.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.15.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.16.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.17.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.18.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.19.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.5.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.6.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.7.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.8.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-1.9.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-2.0.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-2.1.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-2.2.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-2.3.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes-2.4.md", "downloaded_repos/simplesamlphp_simplesamlphp/docs/simplesamlphp-upgrade-notes.md", "downloaded_repos/simplesamlphp_simplesamlphp/extra/simplesamlphp.spec", "downloaded_repos/simplesamlphp_simplesamlphp/locales/af/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/af/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ar/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ar/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ca/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ca/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/cs/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/cs/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/da/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/da/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/de/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/de/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/el/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/el/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/en/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/en/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/es/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/es/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/es/LC_MESSAGES/ssp.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/et/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/et/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/eu/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/eu/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/fa/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/fi/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/fi/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/fr/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/fr/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/he/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/he/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/hr/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/hr/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/hu/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/hu/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/id/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/id/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/it/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/it/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ja/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ja/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/lb/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/lb/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/lt/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/lt/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/lv/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/lv/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nb/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nb/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nb/LC_MESSAGES/ssp.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nb/LC_MESSAGES/test.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nl/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nl/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nn/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nn/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nn/LC_MESSAGES/ssp.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/nn/LC_MESSAGES/test.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/no/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/no/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/no/LC_MESSAGES/ssp.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/no/LC_MESSAGES/test.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/pl/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/pl/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/pt/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/pt/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/pt_BR/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/pt_BR/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ro/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ro/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ru/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ru/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/se/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/se/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sk/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sk/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sl/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sl/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sma/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sr/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sr/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/st/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/st/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sv/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/sv/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/th/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/th/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/tr/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/tr/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/ur/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/vi/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/vi/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/xh/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/xh/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/zh/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/zh/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/zh_TW/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/zh_TW/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/zu/LC_MESSAGES/attributes.po", "downloaded_repos/simplesamlphp_simplesamlphp/locales/zu/LC_MESSAGES/messages.po", "downloaded_repos/simplesamlphp_simplesamlphp/metadata/saml20-idp-hosted.php.dist", "downloaded_repos/simplesamlphp_simplesamlphp/metadata/saml20-idp-remote.php.dist", "downloaded_repos/simplesamlphp_simplesamlphp/metadata/saml20-sp-remote.php.dist", "downloaded_repos/simplesamlphp_simplesamlphp/modules/.gitignore", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/af/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/ar/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/ca/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/cs/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/da/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/de/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/el/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/en/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/en_LS/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/es/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/et/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/eu/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/fa/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/fi/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/fr/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/he/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/hr/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/hu/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/id/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/it/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/ja/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/lb/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/lt/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/lv/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/nb/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/nl/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/nn/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/no/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/pl/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/pt/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/pt_BR/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/ro/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/ru/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/se/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/sk/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/sl/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/sma/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/sr/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/sv/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/th/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/tr/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/ur/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/vi/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/xh/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/zh/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/zh_TW/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/locales/zu/LC_MESSAGES/admin.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/public/assets/css/admin.css", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/public/assets/js/metadata-converter.js", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/src/Controller/Config.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/src/Controller/Federation.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/src/Controller/Menu.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/src/Controller/Sandbox.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/src/Controller/Test.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/authsource_list.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/config.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/diagnostics.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/federation.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/includes/menu.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/logout.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/metadata_converter.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/show_metadata.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/admin/templates/status.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_attributeadd.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_attributealter.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_attributecopy.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_attributelimit.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_attributemap.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_attributevaluemap.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_cardinality.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_cardinalitysingle.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_generategroups.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_languageadaptor.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_php.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_scopeattribute.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_scopefromattribute.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_targetedid.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authproc_warnshortssointerval.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/docs/authsource_selector.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/af/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/ar/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/cs/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/da/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/de/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/el/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/en/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/en_LS/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/es/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/et/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/eu/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/fi/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/fr/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/he/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/hr/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/hu/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/id/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/it/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/ja/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/lb/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/lt/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/lv/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/nb/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/nl/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/nn/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/pl/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/pt/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/pt_BR/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/ro/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/ru/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/sk/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/sl/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/sr/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/sv/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/th/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/tr/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/vi/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/xh/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/zh/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/zh_TW/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/locales/zu/LC_MESSAGES/core.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/public/assets/js/loginuserpass.js", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/AttributeAdd.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/AttributeAlter.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/AttributeCopy.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/AttributeLimit.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/AttributeMap.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/AttributeValueMap.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/Cardinality.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/CardinalitySingle.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/ExtendIdPSession.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/GenerateGroups.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/LanguageAdaptor.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/PHP.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/ScopeAttribute.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/ScopeFromAttribute.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/TargetedID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Process/WarnShortSSOInterval.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Source/AbstractSourceSelector.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Source/AdminPassword.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Source/RequestedAuthnContextSelector.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/Source/SourceIPSelector.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/UserPassBase.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Auth/UserPassOrgBase.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Controller/ErrorReport.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Controller/Exception.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Controller/Login.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Controller/Logout.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Controller/Redirection.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Stats/Output/File.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Stats/Output/Log.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/src/Storage/SQLPermanentStorage.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/base.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/cardinality_error.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/error.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/errorreport.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/loginuserpass.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/logout-iframe-wrapper.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/logout-iframe.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/method_not_allowed.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/no_cookie.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/no_metadata.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/no_state.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/short_sso_interval.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/core/templates/welcome.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/bin/cron.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/config/module_cron.php.dist", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/docs/cron.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/hooks/hook_configpage.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/hooks/hook_cron.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/af/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/ar/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/cs/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/da/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/de/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/el/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/en/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/es/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/et/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/eu/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/fr/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/he/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/hr/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/hu/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/id/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/it/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/ja/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/lt/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/lv/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/nb/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/nl/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/nn/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/pt/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/pt_BR/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/ro/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/ru/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/sl/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/sr/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/sv/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/th/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/vi/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/zh/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/locales/zh_TW/LC_MESSAGES/cron.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/public/assets/css/cron.css", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/src/Controller/Cron.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/src/Cron.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/templates/croninfo-result.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/cron/templates/croninfo.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/docs/debugsp.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/af/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/ar/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/ca/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/cs/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/da/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/de/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/el/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/en/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/en_LS/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/es/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/et/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/eu/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/fa/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/fi/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/fr/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/he/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/hr/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/hu/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/id/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/it/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/ja/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/lb/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/lt/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/lv/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/nb/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/nl/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/nn/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/no/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/pl/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/pt/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/pt_BR/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/ro/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/ru/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/se/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/sk/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/sl/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/sma/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/sr/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/sv/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/th/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/tr/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/ur/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/vi/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/xh/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/zh/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/zh_TW/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/locales/zu/LC_MESSAGES/debugsp.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/public/assets/css/splogintest.css", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/src/Controller/Test.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/templates/authsource_list.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/templates/logout.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/debugsp/templates/status.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/exampleauth/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/modules/exampleauth/src/Auth/Process/RedirectTest.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/exampleauth/src/Auth/Source/External.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/exampleauth/src/Auth/Source/StaticSource.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/exampleauth/src/Auth/Source/UserPass.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/exampleauth/src/Controller/ExampleAuth.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/exampleauth/templates/authenticate.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/docs/multiauth.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/af/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/ar/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/cs/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/da/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/de/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/el/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/en/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/en_LS/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/es/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/et/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/eu/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/fi/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/fr/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/he/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/hr/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/hu/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/id/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/it/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/ja/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/lt/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/lv/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/nb/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/nl/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/nn/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/pl/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/pt/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/ro/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/ru/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/sl/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/sr/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/sv/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/th/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/vi/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/xh/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/zh/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/zh_TW/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/locales/zu/LC_MESSAGES/multiauth.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/src/Auth/Source/MultiAuth.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/src/Controller/DiscoController.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/multiauth/templates/selectsource.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/authproc_authncontextclassref.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/authproc_expectedauthncontextclassref.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/authproc_pairwiseid.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/authproc_scopedissuer.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/authproc_subjectid.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/filterscopes.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/keyrollover.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/nameid.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/nameidattribute.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/docs/sp.md", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/hooks/hook_sanitycheck.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/af/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/ar/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/cs/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/da/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/de/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/el/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/en/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/en_LS/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/es/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/et/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/eu/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/fr/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/hr/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/hu/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/id/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/it/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/lt/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/nl/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/nn/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/ro/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/ru/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/sk/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/sr/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/sv/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/th/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/vi/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/xh/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/zh/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/zh_TW/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/locales/zu/LC_MESSAGES/saml.po", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/AttributeNameID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/AuthnContextClassRef.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/ExpectedAuthnContextClassRef.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/FilterScopes.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/NameIDAttribute.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/PairwiseID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/PersistentNameID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/PersistentNameID2TargetedID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/SQLPersistentNameID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/ScopedIssuer.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/SubjectID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Process/TransientNameID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Auth/Source/SP.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/BaseNameIDGenerator.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Controller/Disco.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Controller/Exception.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Controller/Metadata.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Controller/Proxy.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Controller/ServiceProvider.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Controller/SingleLogout.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Controller/WebBrowserSingleSignOn.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Error/NoAuthnContext.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Error/NoAvailableIDP.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Error/NoPassive.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Error/NoSupportedIDP.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Error/ProxyCountExceeded.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Error.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/IdP/SAML2.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/IdP/SQLNameID.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Message.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/SP/LogoutStore.php", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/templates/proxy/invalid_session.twig", "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/templates/sp/wrong_authncontextclassref.twig", "downloaded_repos/simplesamlphp_simplesamlphp/phpcs.xml", "downloaded_repos/simplesamlphp_simplesamlphp/phpunit.xml", "downloaded_repos/simplesamlphp_simplesamlphp/psalm-dev.xml", "downloaded_repos/simplesamlphp_simplesamlphp/psalm.xml", "downloaded_repos/simplesamlphp_simplesamlphp/public/_include.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/admin/index.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/assets/.gitkeep", "downloaded_repos/simplesamlphp_simplesamlphp/public/index.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/module.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/saml2/idp/ArtifactResolutionService.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/saml2/idp/SSOService.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/saml2/idp/SingleLogoutService.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/saml2/idp/initSLO.php", "downloaded_repos/simplesamlphp_simplesamlphp/public/saml2/idp/metadata.php", "downloaded_repos/simplesamlphp_simplesamlphp/routing/routes/routes.yml", "downloaded_repos/simplesamlphp_simplesamlphp/routing/services/console.yml", "downloaded_repos/simplesamlphp_simplesamlphp/routing/services/routing.yml", "downloaded_repos/simplesamlphp_simplesamlphp/routing/services/services.yml", "downloaded_repos/simplesamlphp_simplesamlphp/routing/services/simplesamlphp.yml", "downloaded_repos/simplesamlphp_simplesamlphp/routing/services/web.yml", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/AuthenticationFactory.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/ProcessingChain.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/ProcessingFilter.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/Simple.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/Source.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/SourceFactory.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Auth/State.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Command/RouterDebugCommand.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Command/SspCacheClearCommand.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Command/UnusedTranslatableStringsCommand.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Command/UpdateTranslatableStringsCommand.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Compat/Logger.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Compat/SspContainer.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Configuration.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Console/Application.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Database.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/AuthSource.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/BadRequest.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/BadUserInput.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/CannotSetCookie.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ConfigurationError.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/CriticalConfigurationError.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/Error.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/Exception.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ExceptionHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/InvalidCredential.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/MetadataNotFound.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/MethodNotAllowed.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/NoState.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/NotFound.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/UnserializableException.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/User.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/UserAborted.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/UserNotFound.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/HTTP/RunnableResponse.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/IdP/IFrameLogoutHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/IdP/LogoutHandlerInterface.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/IdP/TraditionalLogoutHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/IdP.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Kernel.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Locale/Language.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Locale/Localization.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Locale/Translate.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Locale/TwigTranslator.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Logger/ErrorLogLoggingHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Logger/FileLoggingHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Logger/LoggingHandlerInterface.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Logger/StandardErrorLoggingHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Logger/SyslogLoggingHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Logger.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Memcache.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandlerDirectory.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandlerFlatFile.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandlerPdo.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandlerSerialize.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageHandlerXML.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/MetaDataStorageSource.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/SAMLBuilder.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/SAMLParser.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/Signer.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/Sources/MDQ.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Module.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Session.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/SessionHandler.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/SessionHandlerCookie.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/SessionHandlerPHP.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/SessionHandlerStore.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Stats/Output.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Stats.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Store/MemcacheStore.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Store/RedisStore.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Store/SQLStore.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Store/StoreFactory.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Store/StoreInterface.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Arrays.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Attributes.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Auth.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/ClearableState.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Config/Metadata.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Config.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Crypto.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/EMail.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/HTTP.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Random.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/System.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Time.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/Translate.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/XML.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XHTML/IdPDisco.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XHTML/Template.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XHTML/TemplateControllerInterface.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XHTML/TemplateLoader.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XML/Errors.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XML/Parser.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XML/Signer.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/XML/Validator.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/_autoload.php", "downloaded_repos/simplesamlphp_simplesamlphp/src/_autoload_modules.php", "downloaded_repos/simplesamlphp_simplesamlphp/templates/IFrameLogoutHandler.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/_footer.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/_header.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/_table.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/auth_status.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/base.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/error.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/includes/expander.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/mailhtml.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/mailtxt.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/post.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/sandbox.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/selectidp-dropdown.twig", "downloaded_repos/simplesamlphp_simplesamlphp/templates/selectidp-links.twig", "downloaded_repos/simplesamlphp_simplesamlphp/tools/composer-require-checker.json", "downloaded_repos/simplesamlphp_simplesamlphp/tools/linters/.stylelintrc.json", "downloaded_repos/simplesamlphp_simplesamlphp/tools/linters/.yaml-lint.yml", "downloaded_repos/simplesamlphp_simplesamlphp/tools/linters/eslint.config.js"], "skipped": [{"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/build/full.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/build/slim.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/build-release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/.github/workflows/php.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Error/ErrorCodes.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/SigningTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/Utils/ExitTestException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/Utils/SpTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/Utils/TestAuthSource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/Utils/TestAuthSourceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/_autoload_modules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/config/defined_config.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/config/returned_config.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/admin/src/Controller/ConfigTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/admin/src/Controller/FederationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/admin/src/Controller/SandboxTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/admin/src/Controller/TestTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/AttributeAddTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/AttributeAlterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/AttributeCopyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/AttributeLimitTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/AttributeMapTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/AttributeValueMapTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/CardinalitySingleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/CardinalityTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/PHPTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/ScopeAttributeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/ScopeFromAttributeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Process/TargetedIDTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Source/RequestedAuthnContextSelectorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/Source/SourceIPSelectorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/UserPassBaseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Auth/UserPassOrgBaseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Controller/ErrorReportTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Controller/ExceptionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Controller/LoginTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Controller/LogoutTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/core/src/Storage/SQLPermanentStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/cron/src/Controller/CronTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/exampleauth/src/Controller/ExampleAuthTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/multiauth/src/Auth/Source/MultiAuthTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/multiauth/src/Controller/DiscoControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Auth/Process/AttributeNameIDTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Auth/Process/FilterScopesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Auth/Process/NameIDAttributeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Auth/Process/PairwiseIDTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Auth/Process/ScopedIssuerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Auth/Process/SubjectIDTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Auth/Source/SPTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Controller/DiscoTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Controller/MetadataTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Controller/ProxyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/Controller/ServiceProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/IdP/SAML2Test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/saml/src/IdP/SQLNameIDTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/unittest/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/unittest/hooks/hook_invalid.php.orig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/unittest/hooks/hook_valid.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/modules/unittest/hooks/invalid_hook_prefix.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/routers/configLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Auth/ProcessingChainTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Auth/ProcessingFilterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Auth/SimpleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Auth/SourceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Auth/StateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/ConfigurationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/DatabaseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Error/ErrorCodesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Error/ErrorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Locale/LanguageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Locale/LocalizationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Locale/TranslateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/LoggerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/MetaDataStorageHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/MetaDataStorageSourceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/SAMLBuilderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/SAMLParserTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source1/saml20-idp-hosted.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source1/saml20-idp-remote.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source1/saml20-sp-remote.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source2/saml20-sp-remote/entityB.serialized", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source2/saml20-sp-remote/entityInBoth.serialized", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source2/saml20-sp-remote/expiredInSrc1InSrc2.serialized", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source3/saml20-idp-hosted.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Metadata/test-metadata/source4/saml20-idp-hosted.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/ModuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/SessionHandlerPHPTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/SessionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Store/RedisStoreTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Store/SQLStoreTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Store/StoreFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/ArraysTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/AttributesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/Config/MetadataTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/ConfigTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/CryptoTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/EMailTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/HTTPTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/RandomTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/SystemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/TimeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/Utils/XMLTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/XHTML/TemplateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/XHTML/TemplateTranslationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/XML/ErrorsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/XML/ParserTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/XML/SignerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/src/SimpleSAML/XML/ValidatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplesamlphp_simplesamlphp/tests/www/IndexTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.7183730602264404, "profiling_times": {"config_time": 6.467613935470581, "core_time": 14.0010244846344, "ignores_time": 0.0025687217712402344, "total_time": 20.4840886592865}, "parsing_time": {"total_time": 4.614546537399292, "per_file_time": {"mean": 0.018607042489513278, "std_dev": 0.0024343576102506194}, "very_slow_stats": {"time_ratio": 0.17529936097025178, "count_ratio": 0.008064516129032258}, "very_slow_files": [{"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/HTTP.php", "ftime": 0.3041069507598877}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/IdP/SAML2.php", "ftime": 0.5048201084136963}]}, "scanning_time": {"total_time": 25.750328540802002, "per_file_time": {"mean": 0.014680917070012544, "std_dev": 0.00813450069734607}, "very_slow_stats": {"time_ratio": 0.14499038588188926, "count_ratio": 0.0011402508551881414}, "very_slow_files": [{"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Message.php", "ftime": 1.5949840545654297}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/IdP/SAML2.php", "ftime": 2.138566017150879}]}, "matching_time": {"total_time": 8.425785779953003, "per_file_and_rule_time": {"mean": 0.007140496423688984, "std_dev": 0.001057476579934493}, "very_slow_stats": {"time_ratio": 0.4554558340424151, "count_ratio": 0.015254237288135594}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Metadata/SAMLParser.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.15714693069458008}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Session.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.15937089920043945}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/IdP/SAML2.php", "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.160660982131958}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Module.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.1946561336517334}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/IdP/SAML2.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.2047100067138672}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Message.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.2678220272064209}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/Message.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.28383398056030273}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Session.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.36383795738220215}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/modules/saml/src/IdP/SAML2.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.3799870014190674}, {"fpath": "downloaded_repos/simplesamlphp_simplesamlphp/src/SimpleSAML/Utils/HTTP.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.6524739265441895}]}, "tainting_time": {"total_time": 2.290874481201172, "per_def_and_rule_time": {"mean": 0.0011664330352348124, "std_dev": 5.203444774676705e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1096777408}, "engine_requested": "OSS", "skipped_rules": []}