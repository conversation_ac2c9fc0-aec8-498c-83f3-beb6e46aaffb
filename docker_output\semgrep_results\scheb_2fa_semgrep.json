{"version": "1.130.0", "results": [{"check_id": "yaml.github-actions.security.run-shell-injection.run-shell-injection", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 55, "col": 13, "offset": 1817}, "end": {"line": 55, "col": 142, "offset": 1946}, "extra": {"message": "Using variable interpolation `${{...}}` with `github` context data in a `run:` step could allow an attacker to inject their own code into the runner. This would allow them to steal secrets and code. `github` context data can have arbitrary user input and should be treated as untrusted. Instead, use an intermediate environment variable with `env:` to store the data and use the environment variable in the `run:` script. Be sure to use double-quotes the environment variable, like this: \"$ENVVAR\".", "metadata": {"category": "security", "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.github.com/en/actions/learn-github-actions/security-hardening-for-github-actions#understanding-the-risk-of-script-injections", "https://securitylab.github.com/research/github-actions-untrusted-input/"], "technology": ["github-actions"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/yaml.github-actions.security.run-shell-injection.run-shell-injection", "shortlink": "https://sg.run/pkzk"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.github-actions.security.run-shell-injection.run-shell-injection", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 67, "col": 13, "offset": 2423}, "end": {"line": 67, "col": 132, "offset": 2542}, "extra": {"message": "Using variable interpolation `${{...}}` with `github` context data in a `run:` step could allow an attacker to inject their own code into the runner. This would allow them to steal secrets and code. `github` context data can have arbitrary user input and should be treated as untrusted. Instead, use an intermediate environment variable with `env:` to store the data and use the environment variable in the `run:` script. Be sure to use double-quotes the environment variable, like this: \"$ENVVAR\".", "metadata": {"category": "security", "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.github.com/en/actions/learn-github-actions/security-hardening-for-github-actions#understanding-the-risk-of-script-injections", "https://securitylab.github.com/research/github-actions-untrusted-input/"], "technology": ["github-actions"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/yaml.github-actions.security.run-shell-injection.run-shell-injection", "shortlink": "https://sg.run/pkzk"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.github-actions.security.run-shell-injection.run-shell-injection", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 75, "col": 13, "offset": 2750}, "end": {"line": 75, "col": 119, "offset": 2856}, "extra": {"message": "Using variable interpolation `${{...}}` with `github` context data in a `run:` step could allow an attacker to inject their own code into the runner. This would allow them to steal secrets and code. `github` context data can have arbitrary user input and should be treated as untrusted. Instead, use an intermediate environment variable with `env:` to store the data and use the environment variable in the `run:` script. Be sure to use double-quotes the environment variable, like this: \"$ENVVAR\".", "metadata": {"category": "security", "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.github.com/en/actions/learn-github-actions/security-hardening-for-github-actions#understanding-the-risk-of-script-injections", "https://securitylab.github.com/research/github-actions-untrusted-input/"], "technology": ["github-actions"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/yaml.github-actions.security.run-shell-injection.run-shell-injection", "shortlink": "https://sg.run/pkzk"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.github-actions.security.run-shell-injection.run-shell-injection", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 87, "col": 13, "offset": 3319}, "end": {"line": 87, "col": 80, "offset": 3386}, "extra": {"message": "Using variable interpolation `${{...}}` with `github` context data in a `run:` step could allow an attacker to inject their own code into the runner. This would allow them to steal secrets and code. `github` context data can have arbitrary user input and should be treated as untrusted. Instead, use an intermediate environment variable with `env:` to store the data and use the environment variable in the `run:` script. Be sure to use double-quotes the environment variable, like this: \"$ENVVAR\".", "metadata": {"category": "security", "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.github.com/en/actions/learn-github-actions/security-hardening-for-github-actions#understanding-the-risk-of-script-injections", "https://securitylab.github.com/research/github-actions-untrusted-input/"], "technology": ["github-actions"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/yaml.github-actions.security.run-shell-injection.run-shell-injection", "shortlink": "https://sg.run/pkzk"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 55, "col": 108, "offset": 1822}, "end": {"line": 55, "col": 111, "offset": 1825}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml:55:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "spans": [{"file": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 55, "col": 108, "offset": 1822}, "end": {"line": 55, "col": 111, "offset": 1825}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 67, "col": 98, "offset": 2428}, "end": {"line": 67, "col": 101, "offset": 2431}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml:67:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "spans": [{"file": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 67, "col": 98, "offset": 2428}, "end": {"line": 67, "col": 101, "offset": 2431}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 75, "col": 85, "offset": 2755}, "end": {"line": 75, "col": 88, "offset": 2758}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml:75:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "spans": [{"file": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 75, "col": 85, "offset": 2755}, "end": {"line": 75, "col": 88, "offset": 2758}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 87, "col": 46, "offset": 3324}, "end": {"line": 87, "col": 49, "offset": 3327}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml:87:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "spans": [{"file": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "start": {"line": 87, "col": 46, "offset": 3324}, "end": {"line": 87, "col": 49, "offset": 3327}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/app/src/Controller/AssetsController.php", "start": {"line": 16, "col": 50, "offset": 0}, "end": {"line": 16, "col": 56, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/app/src/Controller/AssetsController.php:16:\n `<PERSON><PERSON>` was unexpected", "path": "downloaded_repos/scheb_2fa/app/src/Controller/AssetsController.php", "spans": [{"file": "downloaded_repos/scheb_2fa/app/src/Controller/AssetsController.php", "start": {"line": 16, "col": 50, "offset": 0}, "end": {"line": 16, "col": 56, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/BackupCodeManager.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/BackupCodeManager.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/BackupCodeManager.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/BackupCodeManager.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 31, "col": 17, "offset": 0}, "end": {"line": 31, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 32, "col": 17, "offset": 0}, "end": {"line": 32, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 34, "col": 17, "offset": 0}, "end": {"line": 34, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 35, "col": 26, "offset": 0}, "end": {"line": 35, "col": 30, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 31, "col": 17, "offset": 0}, "end": {"line": 31, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 32, "col": 17, "offset": 0}, "end": {"line": 32, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 34, "col": 17, "offset": 0}, "end": {"line": 34, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "start": {"line": 35, "col": 26, "offset": 0}, "end": {"line": 35, "col": 30, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "start": {"line": 61, "col": 41, "offset": 0}, "end": {"line": 61, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php:61:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "start": {"line": 61, "col": 41, "offset": 0}, "end": {"line": 61, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersister.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersister.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersister.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersister.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 21, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 18, "col": 13, "offset": 0}, "end": {"line": 18, "col": 21, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/AuthenticationTrustResolver.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/AuthenticationTrustResolver.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/AuthenticationTrustResolver.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/AuthenticationTrustResolver.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 42, "col": 13, "offset": 0}, "end": {"line": 42, "col": 21, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 45, "col": 17, "offset": 0}, "end": {"line": 45, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 46, "col": 17, "offset": 0}, "end": {"line": 46, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 47, "col": 17, "offset": 0}, "end": {"line": 47, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 48, "col": 17, "offset": 0}, "end": {"line": 48, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 49, "col": 17, "offset": 0}, "end": {"line": 49, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 50, "col": 17, "offset": 0}, "end": {"line": 50, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php:42:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 42, "col": 13, "offset": 0}, "end": {"line": 42, "col": 21, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 45, "col": 17, "offset": 0}, "end": {"line": 45, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 46, "col": 17, "offset": 0}, "end": {"line": 46, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 47, "col": 17, "offset": 0}, "end": {"line": 47, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 48, "col": 17, "offset": 0}, "end": {"line": 48, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 49, "col": 17, "offset": 0}, "end": {"line": 49, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "start": {"line": 50, "col": 17, "offset": 0}, "end": {"line": 50, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/AbstractCheckCodeListener.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 31, "offset": 5}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 3}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php:24:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 31, "offset": 5}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 3}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php:27:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 33, "offset": 7}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 34, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php:19:\n `Request` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 33, "offset": 7}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 34, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextFactory.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 56, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextFactory.php:17:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextFactory.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 56, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php:18:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "start": {"line": 16, "col": 41, "offset": 0}, "end": {"line": 16, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php:17:\n `iterable` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php:24:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 17, "col": 26, "offset": 0}, "end": {"line": 17, "col": 33, "offset": 7}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php:17:\n `Request` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 17, "col": 26, "offset": 0}, "end": {"line": 17, "col": 33, "offset": 7}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php:15:\n `object` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php:15:\n `object` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 15, "col": 26, "offset": 0}, "end": {"line": 15, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "start": {"line": 16, "col": 26, "offset": 0}, "end": {"line": 16, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 55, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php:17:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "start": {"line": 17, "col": 50, "offset": 0}, "end": {"line": 17, "col": 55, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 31, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "start": {"line": 20, "col": 41, "offset": 0}, "end": {"line": 20, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 35, "col": 13, "offset": 0}, "end": {"line": 35, "col": 21, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 41, "col": 26, "offset": 0}, "end": {"line": 41, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 42, "col": 26, "offset": 0}, "end": {"line": 42, "col": 30, "offset": 4}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 43, "col": 26, "offset": 0}, "end": {"line": 43, "col": 30, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php:35:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 35, "col": 13, "offset": 0}, "end": {"line": 35, "col": 21, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 41, "col": 26, "offset": 0}, "end": {"line": 41, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 42, "col": 26, "offset": 0}, "end": {"line": 42, "col": 30, "offset": 4}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "start": {"line": 43, "col": 26, "offset": 0}, "end": {"line": 43, "col": 30, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 58, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php:18:\n `iterable` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 58, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 31, "offset": 5}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php:21:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 31, "offset": 5}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallContext.php:18:\n `array` was unexpected", "path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "start": {"line": 18, "col": 50, "offset": 0}, "end": {"line": 18, "col": 55, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/email/Mailer/SymfonyAuthCodeMailer.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/email/Mailer/SymfonyAuthCodeMailer.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/email/Mailer/SymfonyAuthCodeMailer.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/email/Mailer/SymfonyAuthCodeMailer.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "start": {"line": 20, "col": 26, "offset": 0}, "end": {"line": 20, "col": 29, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "start": {"line": 19, "col": 17, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "start": {"line": 20, "col": 26, "offset": 0}, "end": {"line": 20, "col": 29, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 29, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 29, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 29, "offset": 3}}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php:21:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 21, "col": 26, "offset": 0}, "end": {"line": 21, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 23, "col": 26, "offset": 0}, "end": {"line": 23, "col": 29, "offset": 3}}, {"file": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "start": {"line": 24, "col": 17, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 28, "col": 26, "offset": 0}, "end": {"line": 28, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 29, "col": 26, "offset": 0}, "end": {"line": 29, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 30, "col": 26, "offset": 0}, "end": {"line": 30, "col": 29, "offset": 3}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 31, "col": 26, "offset": 0}, "end": {"line": 31, "col": 29, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php:28:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 28, "col": 26, "offset": 0}, "end": {"line": 28, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 29, "col": 26, "offset": 0}, "end": {"line": 29, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 30, "col": 26, "offset": 0}, "end": {"line": 30, "col": 29, "offset": 3}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "start": {"line": 31, "col": 26, "offset": 0}, "end": {"line": 31, "col": 29, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 29, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "start": {"line": 23, "col": 17, "offset": 0}, "end": {"line": 23, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 29, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php", "start": {"line": 20, "col": 17, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php", "start": {"line": 21, "col": 17, "offset": 0}, "end": {"line": 21, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 31, "offset": 5}}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php:24:\n `string` was unexpected", "path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 25, "col": 26, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 31, "offset": 5}}, {"file": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/Http/EventListener/TrustedDeviceListener.php", "start": {"line": 19, "col": 41, "offset": 0}, "end": {"line": 19, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/Http/EventListener/TrustedDeviceListener.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/Http/EventListener/TrustedDeviceListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/Http/EventListener/TrustedDeviceListener.php", "start": {"line": 19, "col": 41, "offset": 0}, "end": {"line": 19, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php", "start": {"line": 17, "col": 26, "offset": 0}, "end": {"line": 17, "col": 30, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php:16:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php", "start": {"line": 16, "col": 17, "offset": 0}, "end": {"line": 16, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php", "start": {"line": 17, "col": 26, "offset": 0}, "end": {"line": 17, "col": 30, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php", "start": {"line": 28, "col": 22, "offset": 0}, "end": {"line": 28, "col": 27, "offset": 5}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php", "start": {"line": 30, "col": 41, "offset": 0}, "end": {"line": 30, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php:28:\n `Clock` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php", "start": {"line": 28, "col": 22, "offset": 0}, "end": {"line": 28, "col": 27, "offset": 5}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php", "start": {"line": 30, "col": 41, "offset": 0}, "end": {"line": 30, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 3}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 28, "col": 26, "offset": 0}, "end": {"line": 28, "col": 30, "offset": 4}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 29, "col": 26, "offset": 0}, "end": {"line": 29, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 30, "col": 26, "offset": 0}, "end": {"line": 30, "col": 32, "offset": 6}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 31, "col": 26, "offset": 0}, "end": {"line": 31, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php:25:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 25, "col": 17, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 3}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 28, "col": 26, "offset": 0}, "end": {"line": 28, "col": 30, "offset": 4}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 29, "col": 26, "offset": 0}, "end": {"line": 29, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 30, "col": 26, "offset": 0}, "end": {"line": 30, "col": 32, "offset": 6}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "start": {"line": 31, "col": 26, "offset": 0}, "end": {"line": 31, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceManager.php", "start": {"line": 18, "col": 41, "offset": 0}, "end": {"line": 18, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceManager.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceManager.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceManager.php", "start": {"line": 18, "col": 41, "offset": 0}, "end": {"line": 18, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceToken.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceToken.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceToken.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceToken.php", "start": {"line": 15, "col": 41, "offset": 0}, "end": {"line": 15, "col": 49, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 29, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php", "start": {"line": 18, "col": 17, "offset": 0}, "end": {"line": 18, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php", "start": {"line": 19, "col": 26, "offset": 0}, "end": {"line": 19, "col": 29, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "start": {"line": 30, "col": 26, "offset": 0}, "end": {"line": 30, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "spans": [{"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "start": {"line": 28, "col": 17, "offset": 0}, "end": {"line": 28, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"file": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "start": {"line": 30, "col": 26, "offset": 0}, "end": {"line": 30, "col": 32, "offset": 6}}]}], "paths": {"scanned": ["downloaded_repos/scheb_2fa/.codecov.yml", "downloaded_repos/scheb_2fa/.editorconfig", "downloaded_repos/scheb_2fa/.gitattributes", "downloaded_repos/scheb_2fa/.github/ISSUE_TEMPLATE/bug-report.md", "downloaded_repos/scheb_2fa/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/scheb_2fa/.github/ISSUE_TEMPLATE/feature-request.md", "downloaded_repos/scheb_2fa/.github/ISSUE_TEMPLATE/support-request.md", "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "downloaded_repos/scheb_2fa/.github/pull_request_template.md", "downloaded_repos/scheb_2fa/.github/stale.yml", "downloaded_repos/scheb_2fa/.github/workflows/app.yaml", "downloaded_repos/scheb_2fa/.github/workflows/ci.yaml", "downloaded_repos/scheb_2fa/.github/workflows/split.yaml", "downloaded_repos/scheb_2fa/.gitignore", "downloaded_repos/scheb_2fa/.gitsplit.yml", "downloaded_repos/scheb_2fa/.php-cs-fixer.dist.php", "downloaded_repos/scheb_2fa/.symfony.bundle.yaml", "downloaded_repos/scheb_2fa/CONTRIBUTING.md", "downloaded_repos/scheb_2fa/LICENSE", "downloaded_repos/scheb_2fa/README.md", "downloaded_repos/scheb_2fa/SECURITY.md", "downloaded_repos/scheb_2fa/UPGRADE.md", "downloaded_repos/scheb_2fa/app/.env", "downloaded_repos/scheb_2fa/app/Dockerfile", "downloaded_repos/scheb_2fa/app/README.md", "downloaded_repos/scheb_2fa/app/bin/console", "downloaded_repos/scheb_2fa/app/composer.json", "downloaded_repos/scheb_2fa/app/config/bootstrap.php", "downloaded_repos/scheb_2fa/app/config/bundles.php", "downloaded_repos/scheb_2fa/app/config/packages/dev/mailer.yaml", "downloaded_repos/scheb_2fa/app/config/packages/dev/monolog.yaml", "downloaded_repos/scheb_2fa/app/config/packages/dev/routing.yaml", "downloaded_repos/scheb_2fa/app/config/packages/dev/web_profiler.yaml", "downloaded_repos/scheb_2fa/app/config/packages/doctrine.yaml", "downloaded_repos/scheb_2fa/app/config/packages/framework.yaml", "downloaded_repos/scheb_2fa/app/config/packages/mailer.yaml", "downloaded_repos/scheb_2fa/app/config/packages/prod/mailer.yaml", "downloaded_repos/scheb_2fa/app/config/packages/prod/monolog.yaml", "downloaded_repos/scheb_2fa/app/config/packages/routing.yaml", "downloaded_repos/scheb_2fa/app/config/packages/scheb_2fa.yaml", "downloaded_repos/scheb_2fa/app/config/packages/security.php", "downloaded_repos/scheb_2fa/app/config/packages/translation.yaml", "downloaded_repos/scheb_2fa/app/config/packages/twig.yaml", "downloaded_repos/scheb_2fa/app/config/preload.php", "downloaded_repos/scheb_2fa/app/config/routes/attributes.yaml", "downloaded_repos/scheb_2fa/app/config/routes/dev/web_profiler.yaml", "downloaded_repos/scheb_2fa/app/config/routes/scheb_2fa.yaml", "downloaded_repos/scheb_2fa/app/config/routes.yaml", "downloaded_repos/scheb_2fa/app/config/services.yaml", "downloaded_repos/scheb_2fa/app/data/data.db", "downloaded_repos/scheb_2fa/app/docker-compose.yaml", "downloaded_repos/scheb_2fa/app/nginx.conf", "downloaded_repos/scheb_2fa/app/phpunit.xml.dist", "downloaded_repos/scheb_2fa/app/public/assets/style.css", "downloaded_repos/scheb_2fa/app/public/index.php", "downloaded_repos/scheb_2fa/app/src/Controller/AssetsController.php", "downloaded_repos/scheb_2fa/app/src/Controller/EsiController.php", "downloaded_repos/scheb_2fa/app/src/Controller/HomeController.php", "downloaded_repos/scheb_2fa/app/src/Controller/MembersController.php", "downloaded_repos/scheb_2fa/app/src/Controller/QrCodeController.php", "downloaded_repos/scheb_2fa/app/src/Controller/SecurityController.php", "downloaded_repos/scheb_2fa/app/src/Entity/User.php", "downloaded_repos/scheb_2fa/app/src/Kernel.php", "downloaded_repos/scheb_2fa/app/templates/_navigation.html.twig", "downloaded_repos/scheb_2fa/app/templates/base.html.twig", "downloaded_repos/scheb_2fa/app/templates/home.html.twig", "downloaded_repos/scheb_2fa/app/templates/members/index.html.twig", "downloaded_repos/scheb_2fa/app/templates/security/2fa.html.twig", "downloaded_repos/scheb_2fa/app/templates/security/login.html.twig", "downloaded_repos/scheb_2fa/app/var/.gitkeep", "downloaded_repos/scheb_2fa/bin/check.sh", "downloaded_repos/scheb_2fa/bin/gitsplit.sh", "downloaded_repos/scheb_2fa/composer.json", "downloaded_repos/scheb_2fa/doc/2fa-logo.svg", "downloaded_repos/scheb_2fa/doc/api.rst", "downloaded_repos/scheb_2fa/doc/authentication-process.svg", "downloaded_repos/scheb_2fa/doc/backup_codes.rst", "downloaded_repos/scheb_2fa/doc/brute_force_protection.rst", "downloaded_repos/scheb_2fa/doc/configuration.rst", "downloaded_repos/scheb_2fa/doc/csrf_protection.rst", "downloaded_repos/scheb_2fa/doc/custom_conditions.rst", "downloaded_repos/scheb_2fa/doc/events.rst", "downloaded_repos/scheb_2fa/doc/firewall_template.rst", "downloaded_repos/scheb_2fa/doc/index.rst", "downloaded_repos/scheb_2fa/doc/installation.rst", "downloaded_repos/scheb_2fa/doc/multi_authentication.rst", "downloaded_repos/scheb_2fa/doc/persister.rst", "downloaded_repos/scheb_2fa/doc/providers/custom.rst", "downloaded_repos/scheb_2fa/doc/providers/email.rst", "downloaded_repos/scheb_2fa/doc/providers/google.rst", "downloaded_repos/scheb_2fa/doc/providers/totp.rst", "downloaded_repos/scheb_2fa/doc/troubleshooting.rst", "downloaded_repos/scheb_2fa/doc/trusted_device.rst", "downloaded_repos/scheb_2fa/phpcs.xml.dist", "downloaded_repos/scheb_2fa/phpunit.xml.dist", "downloaded_repos/scheb_2fa/psalm.xml", "downloaded_repos/scheb_2fa/src/backup-code/.gitattributes", "downloaded_repos/scheb_2fa/src/backup-code/.github/stale.yml", "downloaded_repos/scheb_2fa/src/backup-code/LICENSE", "downloaded_repos/scheb_2fa/src/backup-code/Model/BackupCodeInterface.php", "downloaded_repos/scheb_2fa/src/backup-code/README.md", "downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php", "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/BackupCodeManager.php", "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/BackupCodeManagerInterface.php", "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/NullBackupCodeManager.php", "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Event/BackupCodeEvents.php", "downloaded_repos/scheb_2fa/src/backup-code/composer.json", "downloaded_repos/scheb_2fa/src/bundle/.gitattributes", "downloaded_repos/scheb_2fa/src/bundle/.github/stale.yml", "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Compiler/MailerCompilerPass.php", "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Compiler/TwoFactorFirewallConfigCompilerPass.php", "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Compiler/TwoFactorProviderCompilerPass.php", "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Configuration.php", "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Factory/Security/TwoFactorServicesFactory.php", "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/SchebTwoFactorExtension.php", "downloaded_repos/scheb_2fa/src/bundle/LICENSE", "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersister.php", "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php", "downloaded_repos/scheb_2fa/src/bundle/Model/PersisterInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Model/PreferredProviderInterface.php", "downloaded_repos/scheb_2fa/src/bundle/README.md", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/backup_codes.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/persistence.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/security.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/trusted_device.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/two_factor.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/two_factor_provider_email.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/two_factor_provider_google.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/config/two_factor_provider_totp.php", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.cs.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.de.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.en.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.es.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.fr.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.hr.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.hu.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.id.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.nl.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.pl.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.ro.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.ru.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.sk.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.sv.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.tr.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/translations/SchebTwoFactorBundle.uk.yml", "downloaded_repos/scheb_2fa/src/bundle/Resources/views/Authentication/form.html.twig", "downloaded_repos/scheb_2fa/src/bundle/SchebTwoFactorBundle.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/AuthenticationTrustResolver.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/Exception/InvalidTwoFactorCodeException.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/Exception/ReusedTwoFactorCodeException.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/Exception/TwoFactorProviderNotFoundException.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/Token/TwoFactorToken.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/Token/TwoFactorTokenFactory.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/Token/TwoFactorTokenFactoryInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/Token/TwoFactorTokenInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/Voter/TwoFactorInProgressVoter.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/AuthenticationRequiredHandlerInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/SuppressRememberMeListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/ThrowExceptionOnTwoFactorCodeReuseListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Utils/JsonRequestUtils.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Utils/ParameterBagUtils.php", "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Utils/RequestDataReader.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextFactory.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextFactoryInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/TwoFactorConditionInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Csrf/NullCsrfTokenManager.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationSuccessEventSuppressor.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvents.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/IpWhitelist/IpWhitelistProviderInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/Exception/TwoFactorProviderLogicException.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/Exception/UnexpectedTokenException.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/Exception/UnknownTwoFactorProviderException.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/PreparationRecorderInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorFormRendererInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderDecider.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderDeciderInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInterface.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "downloaded_repos/scheb_2fa/src/bundle/composer.json", "downloaded_repos/scheb_2fa/src/email/.gitattributes", "downloaded_repos/scheb_2fa/src/email/.github/stale.yml", "downloaded_repos/scheb_2fa/src/email/LICENSE", "downloaded_repos/scheb_2fa/src/email/Mailer/AuthCodeMailerInterface.php", "downloaded_repos/scheb_2fa/src/email/Mailer/SymfonyAuthCodeMailer.php", "downloaded_repos/scheb_2fa/src/email/Model/Email/TwoFactorInterface.php", "downloaded_repos/scheb_2fa/src/email/README.md", "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Event/EmailCodeEvents.php", "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGeneratorInterface.php", "downloaded_repos/scheb_2fa/src/email/composer.json", "downloaded_repos/scheb_2fa/src/google-authenticator/.gitattributes", "downloaded_repos/scheb_2fa/src/google-authenticator/.github/stale.yml", "downloaded_repos/scheb_2fa/src/google-authenticator/LICENSE", "downloaded_repos/scheb_2fa/src/google-authenticator/Model/Google/TwoFactorInterface.php", "downloaded_repos/scheb_2fa/src/google-authenticator/README.md", "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Event/GoogleAuthenticatorCodeEvents.php", "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorInterface.php", "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php", "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "downloaded_repos/scheb_2fa/src/google-authenticator/composer.json", "downloaded_repos/scheb_2fa/src/totp/.gitattributes", "downloaded_repos/scheb_2fa/src/totp/.github/stale.yml", "downloaded_repos/scheb_2fa/src/totp/LICENSE", "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfigurationInterface.php", "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TwoFactorInterface.php", "downloaded_repos/scheb_2fa/src/totp/README.md", "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Event/TotpCodeEvents.php", "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorInterface.php", "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php", "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "downloaded_repos/scheb_2fa/src/totp/composer.json", "downloaded_repos/scheb_2fa/src/trusted-device/.gitattributes", "downloaded_repos/scheb_2fa/src/trusted-device/.github/stale.yml", "downloaded_repos/scheb_2fa/src/trusted-device/LICENSE", "downloaded_repos/scheb_2fa/src/trusted-device/Model/TrustedDeviceInterface.php", "downloaded_repos/scheb_2fa/src/trusted-device/README.md", "downloaded_repos/scheb_2fa/src/trusted-device/Security/Http/Authenticator/Passport/Badge/TrustedDeviceBadge.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/Http/EventListener/TrustedDeviceListener.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/NullTrustedDeviceManager.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceManager.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceManagerInterface.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceToken.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php", "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "downloaded_repos/scheb_2fa/src/trusted-device/composer.json"], "skipped": [{"path": "downloaded_repos/scheb_2fa/.github/actions/setup-build/action.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/app/config/packages/test/framework.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/config/packages/test/mailer.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/config/packages/test/monolog.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/config/packages/test/security.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/config/packages/test/web_profiler.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/src/Controller/AssetsController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/app/tests/CsrfProtetctionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/tests/RememberMeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/tests/TrustedDeviceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/tests/TwoFactorAccessControlTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/tests/TwoFactorAuthenticationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/app/tests/TwoFactorProvidersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/doc/authentication-process.ai", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/scheb_2fa/src/backup-code/Security/Http/EventListener/CheckBackupCodeListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/backup-code/Security/TwoFactor/Backup/BackupCodeManager.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Controller/FormController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/DependencyInjection/Factory/Security/TwoFactorFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersister.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Model/Persister/DoctrinePersisterFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authentication/AuthenticationTrustResolver.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Authorization/TwoFactorAccessDecider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationFailureHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationRequiredHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authentication/DefaultAuthenticationSuccessHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentials.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Authenticator/TwoFactorAuthenticator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/AbstractCheckCodeListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/EventListener/CheckTwoFactorCodeReuseListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/ExceptionListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/Http/Firewall/TwoFactorAccessListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContext.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/AuthenticationContextFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/AuthenticatedTokenCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/IpWhitelistCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Condition/TwoFactorConditionRegistry.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/AuthenticationTokenListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorAuthenticationEvent.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeEvent.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorCodeReusedEvent.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Event/TwoFactorFormListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProvider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/DefaultTwoFactorFormRenderer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TokenPreparationRecorder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderInitiator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderPreparationListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/Provider/TwoFactorProviderRegistry.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallConfig.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/bundle/Security/TwoFactor/TwoFactorFirewallContext.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/email/Mailer/SymfonyAuthCodeMailer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/EmailTwoFactorProvider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/email/Security/TwoFactor/Provider/Email/Generator/CodeGenerator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProvider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/google-authenticator/Security/TwoFactor/Provider/Google/GoogleTotpFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/totp/Model/Totp/TotpConfiguration.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProvider.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/totp/Security/TwoFactor/Provider/Totp/TotpFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/Http/EventListener/TrustedDeviceListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Condition/TrustedDeviceCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/JwtTokenEncoder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedCookieResponseListener.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceManager.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceToken.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/src/trusted-device/Security/TwoFactor/Trusted/TrustedDeviceTokenStorage.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/scheb_2fa/tests/ComposerJsonTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Controller/FormControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/DependencyInjection/Compiler/MailerCompilerPassTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/DependencyInjection/Compiler/TwoFactorFirewallConfigCompilerPassTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/DependencyInjection/Compiler/TwoFactorProviderCompilerPassTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/DependencyInjection/Factory/Security/TestableFactoryConfiguration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/DependencyInjection/Factory/Security/TwoFactorFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/DependencyInjection/Factory/Security/TwoFactorServicesFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/DependencyInjection/SchebTwoFactorExtensionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/EventDispatcherTestHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Mailer/SymfonyAuthCodeMailerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Model/Persister/DoctrinePersisterFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Model/Persister/DoctrinePersisterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Model/Totp/TotpConfigurationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/SchebTwoFactorBundleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Authentication/AuthenticationTrustResolverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Authentication/Token/TwoFactorTokenFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Authentication/Token/TwoFactorTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Authorization/TwoFactorAccessDeciderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Authorization/Voter/TwoFactorInProgressVoterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Authentication/DefaultAuthenticationFailureHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Authentication/DefaultAuthenticationRequiredHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Authentication/DefaultAuthenticationSuccessHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Authenticator/Passport/Credentials/TwoFactorCodeCredentialsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Authenticator/TwoFactorAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/EventListener/AbstractCheckCodeListenerTestSetup.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/EventListener/CheckBackupCodeListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/EventListener/CheckTwoFactorCodeListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/EventListener/CheckTwoFactorCodeReuseListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/EventListener/SuppressRememberMeListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/EventListener/ThrowExceptionOnTwoFactorReuseListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/EventListener/TrustedDeviceListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Firewall/ExceptionListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Firewall/TwoFactorAccessListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Utils/JsonRequestUtilsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Utils/ParameterBagUtilsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/Http/Utils/RequestDataReaderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/AuthenticationContextFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/AuthenticationContextTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Backup/BackupCodeManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Condition/AbstractAuthenticationContextTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Condition/AuthenticatedTokenConditionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Condition/IpWhitelistConditionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Condition/TrustedDeviceConditionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Condition/TwoFactorConditionRegistryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Event/AuthenticationSuccessEventSuppressorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Event/AuthenticationTokenListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Event/TwoFactorFormListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/IpWhitelist/DefaultIpWhitelistProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/DefaultTwoFactorFormRendererTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Email/EmailTwoFactorProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Email/Generator/CodeGeneratorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Email/Generator/TestableCodeGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Email/UserWithTwoFactorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Google/GoogleAuthenticatorTwoFactorProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Google/GoogleTotpFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Google/UserWithTwoFactorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/TokenPreparationRecorderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Totp/TotpAuthenticatorTwoFactorProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Totp/TotpFactoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/Totp/UserWithTwoFactorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/TwoFactorProviderDeciderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/TwoFactorProviderInitiatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/TwoFactorProviderPreparationListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/TwoFactorProviderRegistryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Provider/UserWithPreferredProviderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/JwtTokenEncoderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/TestableTrustedCookieResponseListener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/TestableTrustedDeviceTokenEncoder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/TrustedCookieResponseListenerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/TrustedDeviceManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/TrustedDeviceTokenEncoderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/TrustedDeviceTokenStorageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/TrustedDeviceTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/Trusted/UserInterfaceWithTrustedDeviceInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/TwoFactorFirewallConfigTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/Security/TwoFactor/TwoFactorFirewallContextTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/scheb_2fa/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.020401954650879, "profiling_times": {"config_time": 6.078862905502319, "core_time": 3.7880656719207764, "ignores_time": 0.002227783203125, "total_time": 9.870431661605835}, "parsing_time": {"total_time": 1.1850433349609375, "per_file_time": {"mean": 0.006077145307491987, "std_dev": 0.0001716035485825751}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.1232991218566895, "per_file_time": {"mean": 0.004337915447023174, "std_dev": 0.00014129203485575058}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.4694483280181885, "per_file_and_rule_time": {"mean": 0.00028314133173594014, "std_dev": 1.2954848573525086e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.01095438003540039, "per_def_and_rule_time": {"mean": 0.00024343066745334207, "std_dev": 1.549741821208168e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}