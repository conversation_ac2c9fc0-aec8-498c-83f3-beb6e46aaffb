{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Storage/FileStorage.php", "start": {"line": 35, "col": 22, "offset": 693}, "end": {"line": 35, "col": 35, "offset": 706}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/database/factories/MoonshineUserFactory.php", "start": {"line": 27, "col": 28, "offset": 812}, "end": {"line": 27, "col": 88, "offset": 872}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/ActionButton.js", "start": {"line": 29, "col": 7, "offset": 900}, "end": {"line": 31, "col": 18, "offset": 1016}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/BelongsToMany.js", "start": {"line": 34, "col": 7, "offset": 873}, "end": {"line": 34, "col": 65, "offset": 931}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "start": {"line": 44, "col": 15, "offset": 1501}, "end": {"line": 44, "col": 39, "offset": 1525}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "start": {"line": 176, "col": 9, "offset": 4925}, "end": {"line": 176, "col": 73, "offset": 4989}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "start": {"line": 296, "col": 7, "offset": 8155}, "end": {"line": 296, "col": 44, "offset": 8192}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Fragment.js", "start": {"line": 67, "col": 9, "offset": 1859}, "end": {"line": 67, "col": 33, "offset": 1883}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/TableBuilder.js", "start": {"line": 217, "col": 9, "offset": 5553}, "end": {"line": 217, "col": 37, "offset": 5581}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Sets.js", "start": {"line": 94, "col": 7, "offset": 2639}, "end": {"line": 94, "col": 35, "offset": 2667}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Sets.js", "start": {"line": 96, "col": 7, "offset": 2675}, "end": {"line": 96, "col": 66, "offset": 2734}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/AsyncLoadContent.js", "start": {"line": 12, "col": 5, "offset": 296}, "end": {"line": 12, "col": 52, "offset": 343}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DOMUpdater.js", "start": {"line": 71, "col": 5, "offset": 1923}, "end": {"line": 71, "col": 29, "offset": 1947}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DOMUpdater.js", "start": {"line": 73, "col": 5, "offset": 2001}, "end": {"line": 73, "col": 29, "offset": 2025}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/Iterable.js", "start": {"line": 82, "col": 11, "offset": 2520}, "end": {"line": 82, "col": 40, "offset": 2549}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/ShowWhen.js", "start": {"line": 243, "col": 17, "offset": 6387}, "end": {"line": 243, "col": 65, "offset": 6435}, "extra": {"message": "`inputName.replace('slide[', '').replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml", "start": {"line": 42, "col": 54, "offset": 831}, "end": {"line": 42, "col": 57, "offset": 834}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml", "start": {"line": 42, "col": 79, "offset": 831}, "end": {"line": 42, "col": 82, "offset": 834}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml:42:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml", "start": {"line": 42, "col": 54, "offset": 831}, "end": {"line": 42, "col": 57, "offset": 834}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml", "start": {"line": 42, "col": 79, "offset": 831}, "end": {"line": 42, "col": 82, "offset": 834}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 29, "col": 35, "offset": 726}, "end": {"line": 29, "col": 63, "offset": 754}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 29, "col": 99, "offset": 726}, "end": {"line": 29, "col": 120, "offset": 747}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 30, "col": 31, "offset": 726}, "end": {"line": 30, "col": 34, "offset": 729}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 30, "col": 75, "offset": 726}, "end": {"line": 30, "col": 78, "offset": 729}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml:29:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ secrets.SUPER_ACCESS_TOKEN` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 29, "col": 35, "offset": 726}, "end": {"line": 29, "col": 63, "offset": 754}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 29, "col": 99, "offset": 726}, "end": {"line": 29, "col": 120, "offset": 747}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 30, "col": 31, "offset": 726}, "end": {"line": 30, "col": 34, "offset": 729}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "start": {"line": 30, "col": 75, "offset": 726}, "end": {"line": 30, "col": 78, "offset": 729}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 33, "col": 57, "offset": 807}, "end": {"line": 33, "col": 60, "offset": 810}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 34, "col": 21, "offset": 807}, "end": {"line": 34, "col": 49, "offset": 835}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 34, "col": 85, "offset": 807}, "end": {"line": 34, "col": 106, "offset": 828}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 36, "col": 21, "offset": 807}, "end": {"line": 36, "col": 24, "offset": 810}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 37, "col": 31, "offset": 807}, "end": {"line": 37, "col": 34, "offset": 810}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml:33:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 33, "col": 57, "offset": 807}, "end": {"line": 33, "col": 60, "offset": 810}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 34, "col": 21, "offset": 807}, "end": {"line": 34, "col": 49, "offset": 835}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 34, "col": 85, "offset": 807}, "end": {"line": 34, "col": 106, "offset": 828}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 36, "col": 21, "offset": 807}, "end": {"line": 36, "col": 24, "offset": 810}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "start": {"line": 37, "col": 31, "offset": 807}, "end": {"line": 37, "col": 34, "offset": 810}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml", "start": {"line": 42, "col": 54, "offset": 835}, "end": {"line": 42, "col": 57, "offset": 838}}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml", "start": {"line": 42, "col": 79, "offset": 835}, "end": {"line": 42, "col": 82, "offset": 838}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml:42:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml", "start": {"line": 42, "col": 54, "offset": 835}, "end": {"line": 42, "col": 57, "offset": 838}}, {"file": "downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml", "start": {"line": 42, "col": 79, "offset": 835}, "end": {"line": 42, "col": 82, "offset": 838}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/AssetManager.php", "start": {"line": 37, "col": 17, "offset": 0}, "end": {"line": 37, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/AssetManager.php:37:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/AssetManager.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/AssetManager.php", "start": {"line": 37, "col": 17, "offset": 0}, "end": {"line": 37, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineCss.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineCss.php:22:\n `string` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineCss.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineCss.php", "start": {"line": 22, "col": 26, "offset": 0}, "end": {"line": 22, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineJs.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineJs.php:24:\n `string` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineJs.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineJs.php", "start": {"line": 24, "col": 26, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Raw.php", "start": {"line": 13, "col": 7, "offset": 0}, "end": {"line": 13, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Raw.php:13:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Raw.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Raw.php", "start": {"line": 13, "col": 7, "offset": 0}, "end": {"line": 13, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/AbstractRequest.php", "start": {"line": 18, "col": 19, "offset": 0}, "end": {"line": 18, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Core/src/AbstractRequest.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/AbstractRequest.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/AbstractRequest.php", "start": {"line": 18, "col": 19, "offset": 0}, "end": {"line": 18, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Attributes/Layout.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Core/src/Attributes/Layout.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Attributes/Layout.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Attributes/Layout.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 34, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 34, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 28, "col": 26, "offset": 0}, "end": {"line": 28, "col": 29, "offset": 3}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 31, "col": 26, "offset": 0}, "end": {"line": 31, "col": 29, "offset": 3}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 32, "col": 26, "offset": 0}, "end": {"line": 32, "col": 30, "offset": 4}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 34, "col": 17, "offset": 0}, "end": {"line": 34, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php:26:\n `iterable` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 26, "col": 26, "offset": 0}, "end": {"line": 26, "col": 34, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 27, "col": 26, "offset": 0}, "end": {"line": 27, "col": 34, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 28, "col": 26, "offset": 0}, "end": {"line": 28, "col": 29, "offset": 3}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 29, "col": 17, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 30, "col": 17, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 31, "col": 26, "offset": 0}, "end": {"line": 31, "col": 29, "offset": 3}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 32, "col": 26, "offset": 0}, "end": {"line": 32, "col": 30, "offset": 4}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 33, "col": 17, "offset": 0}, "end": {"line": 33, "col": 25, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "start": {"line": 34, "col": 17, "offset": 0}, "end": {"line": 34, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorCaster.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorCaster.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorCaster.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorCaster.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorLink.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorLink.php:9:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorLink.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorLink.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataCaster.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataCaster.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataCaster.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataCaster.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataWrapper.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataWrapper.php:9:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataWrapper.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataWrapper.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakePageCommand.php", "start": {"line": 115, "col": 17, "offset": 0}, "end": {"line": 115, "col": 27, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakePageCommand.php:115:\n `namespace:` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakePageCommand.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakePageCommand.php", "start": {"line": 115, "col": 17, "offset": 0}, "end": {"line": 115, "col": 27, "offset": 10}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php", "start": {"line": 112, "col": 13, "offset": 0}, "end": {"line": 112, "col": 23, "offset": 10}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php", "start": {"line": 118, "col": 13, "offset": 0}, "end": {"line": 118, "col": 23, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php:112:\n `namespace:` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php", "start": {"line": 112, "col": 13, "offset": 0}, "end": {"line": 112, "col": 23, "offset": 10}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php", "start": {"line": 118, "col": 13, "offset": 0}, "end": {"line": 118, "col": 23, "offset": 10}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/PublishCommand.php", "start": {"line": 126, "col": 58, "offset": 0}, "end": {"line": 126, "col": 68, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/PublishCommand.php:126:\n `namespace:` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/PublishCommand.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/PublishCommand.php", "start": {"line": 126, "col": 58, "offset": 0}, "end": {"line": 126, "col": 68, "offset": 10}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Notifications.php", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 21, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Notifications.php:21:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Notifications.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Notifications.php", "start": {"line": 21, "col": 13, "offset": 0}, "end": {"line": 21, "col": 21, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Profile.php", "start": {"line": 27, "col": 13, "offset": 0}, "end": {"line": 27, "col": 21, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Profile.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Profile.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Profile.php", "start": {"line": 27, "col": 13, "offset": 0}, "end": {"line": 27, "col": 21, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Search.php", "start": {"line": 29, "col": 26, "offset": 0}, "end": {"line": 29, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Search.php:29:\n `string` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Search.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Search.php", "start": {"line": 29, "col": 26, "offset": 0}, "end": {"line": 29, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Paginator.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Paginator.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Paginator.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Paginator.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShineConfigurator.php", "start": {"line": 26, "col": 13, "offset": 0}, "end": {"line": 26, "col": 21, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShineConfigurator.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShineConfigurator.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShineConfigurator.php", "start": {"line": 26, "col": 13, "offset": 0}, "end": {"line": 26, "col": 21, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/Translator.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/Translator.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/Translator.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/Translator.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/BelongsToMany.php", "start": {"line": 242, "col": 13, "offset": 0}, "end": {"line": 242, "col": 20, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/BelongsToMany.php:242:\n `parent:` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/BelongsToMany.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/BelongsToMany.php", "start": {"line": 242, "col": 13, "offset": 0}, "end": {"line": 242, "col": 20, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/RelationRepeater.php", "start": {"line": 232, "col": 45, "offset": 0}, "end": {"line": 232, "col": 52, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/RelationRepeater.php:232:\n `parent:` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/RelationRepeater.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/RelationRepeater.php", "start": {"line": 232, "col": 45, "offset": 0}, "end": {"line": 232, "col": 52, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Forms/FiltersForm.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Forms/FiltersForm.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Forms/FiltersForm.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Forms/FiltersForm.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/MoonShineController.php", "start": {"line": 110, "col": 15, "offset": 0}, "end": {"line": 110, "col": 38, "offset": 23}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/MoonShineController.php:110:\n `new ($cast->getClass())` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/MoonShineController.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/MoonShineController.php", "start": {"line": 110, "col": 15, "offset": 0}, "end": {"line": 110, "col": 38, "offset": 23}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/ChangeLocale.php", "start": {"line": 13, "col": 18, "offset": 0}, "end": {"line": 13, "col": 38, "offset": 20}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/ChangeLocale.php:13:\n `const KEY = '_lang';` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/ChangeLocale.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/ChangeLocale.php", "start": {"line": 13, "col": 18, "offset": 0}, "end": {"line": 13, "col": 38, "offset": 20}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Models/MoonshineUserRole.php", "start": {"line": 18, "col": 24, "offset": 0}, "end": {"line": 20, "col": 14, "offset": 35}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Models/MoonshineUserRole.php:18:\n `DEFAULT_ROLE_ID = 1;\n\n    protected` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Models/MoonshineUserRole.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Models/MoonshineUserRole.php", "start": {"line": 18, "col": 24, "offset": 0}, "end": {"line": 20, "col": 14, "offset": 35}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineEndpoints.php", "start": {"line": 22, "col": 7, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineEndpoints.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineEndpoints.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineEndpoints.php", "start": {"line": 22, "col": 7, "offset": 0}, "end": {"line": 22, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationButton.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationButton.php:12:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationButton.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationButton.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationItem.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationItem.php:12:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationItem.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationItem.php", "start": {"line": 12, "col": 7, "offset": 0}, "end": {"line": 12, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationMemoryItem.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationMemoryItem.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationMemoryItem.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationMemoryItem.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Storage/LaravelStorage.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Storage/LaravelStorage.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Storage/LaravelStorage.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Storage/LaravelStorage.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/CacheAttributes.php", "start": {"line": 20, "col": 7, "offset": 0}, "end": {"line": 20, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/CacheAttributes.php:20:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/CacheAttributes.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/CacheAttributes.php", "start": {"line": 20, "col": 7, "offset": 0}, "end": {"line": 20, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/MenuAutoloader.php", "start": {"line": 49, "col": 7, "offset": 0}, "end": {"line": 49, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/MenuAutoloader.php:49:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/MenuAutoloader.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/MenuAutoloader.php", "start": {"line": 49, "col": 7, "offset": 0}, "end": {"line": 49, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/StubsPath.php", "start": {"line": 20, "col": 26, "offset": 0}, "end": {"line": 20, "col": 32, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/StubsPath.php:20:\n `string` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/StubsPath.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/StubsPath.php", "start": {"line": 20, "col": 26, "offset": 0}, "end": {"line": 20, "col": 32, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php", "start": {"line": 19, "col": 7, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php", "start": {"line": 41, "col": 20, "offset": 0}, "end": {"line": 41, "col": 45, "offset": 25}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php:19:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php", "start": {"line": 19, "col": 7, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php", "start": {"line": 41, "col": 20, "offset": 0}, "end": {"line": 41, "col": 45, "offset": 25}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelDataWrapper.php", "start": {"line": 15, "col": 7, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelDataWrapper.php:15:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelDataWrapper.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelDataWrapper.php", "start": {"line": 15, "col": 7, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/PaginatorCaster.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/PaginatorCaster.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/PaginatorCaster.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/PaginatorCaster.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/CanSee.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/CanSee.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/CanSee.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/CanSee.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Group.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Group.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Group.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Group.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Order.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Order.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Order.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Order.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/SkipMenu.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/SkipMenu.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/SkipMenu.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/SkipMenu.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuCondition.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuCondition.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuCondition.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuCondition.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/AlpineJs.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/AlpineJs.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/AlpineJs.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/AlpineJs.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/AsyncCallback.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/AsyncCallback.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/AsyncCallback.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/AsyncCallback.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItem.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItem.php:11:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItem.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItem.php", "start": {"line": 11, "col": 7, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItemExtra.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItemExtra.php:9:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItemExtra.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItemExtra.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Option.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Option.php:9:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Option.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Option.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionGroup.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionGroup.php:9:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionGroup.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionGroup.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionImage.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionImage.php:10:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionImage.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionImage.php", "start": {"line": 10, "col": 7, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionProperty.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionProperty.php:9:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionProperty.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionProperty.php", "start": {"line": 9, "col": 7, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Options.php", "start": {"line": 14, "col": 7, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Options.php:14:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Options.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Options.php", "start": {"line": 14, "col": 7, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/ListOf.php", "start": {"line": 16, "col": 50, "offset": 0}, "end": {"line": 16, "col": 56, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/ListOf.php:16:\n `string` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/ListOf.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/ListOf.php", "start": {"line": 16, "col": 50, "offset": 0}, "end": {"line": 16, "col": 56, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/UriKey.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/UriKey.php:7:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/UriKey.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/UriKey.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/VO/FieldEmptyValue.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/Support/src/VO/FieldEmptyValue.php:7:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/VO/FieldEmptyValue.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/Support/src/VO/FieldEmptyValue.php", "start": {"line": 7, "col": 7, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 30, "col": 19, "offset": 0}, "end": {"line": 30, "col": 27, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 31, "col": 19, "offset": 0}, "end": {"line": 31, "col": 27, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 27, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 34, "col": 19, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 8}}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 35, "col": 19, "offset": 0}, "end": {"line": 35, "col": 27, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 30, "col": 19, "offset": 0}, "end": {"line": 30, "col": 27, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 31, "col": 19, "offset": 0}, "end": {"line": 31, "col": 27, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 32, "col": 19, "offset": 0}, "end": {"line": 32, "col": 27, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 27, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 34, "col": 19, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 8}}, {"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "start": {"line": 35, "col": 19, "offset": 0}, "end": {"line": 35, "col": 27, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Menu.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Menu.php:22:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Menu.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Menu.php", "start": {"line": 22, "col": 17, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Json.php", "start": {"line": 315, "col": 35, "offset": 0}, "end": {"line": 315, "col": 42, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Json.php:315:\n `parent:` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Json.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Json.php", "start": {"line": 315, "col": 35, "offset": 0}, "end": {"line": 315, "col": 42, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Sets/UpdateOnPreviewPopover.php", "start": {"line": 18, "col": 7, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/moonshine-software_moonshine/src/UI/src/Sets/UpdateOnPreviewPopover.php:18:\n `readonly` was unexpected", "path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Sets/UpdateOnPreviewPopover.php", "spans": [{"file": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Sets/UpdateOnPreviewPopover.php", "start": {"line": 18, "col": 7, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 8}}]}], "paths": {"scanned": ["downloaded_repos/moonshine-software_moonshine/.editorconfig", "downloaded_repos/moonshine-software_moonshine/.gitattributes", "downloaded_repos/moonshine-software_moonshine/.github/FUNDING.yml", "downloaded_repos/moonshine-software_moonshine/.github/ISSUE_TEMPLATE/Bug_report.yml", "downloaded_repos/moonshine-software_moonshine/.github/ISSUE_TEMPLATE/Issue.yml", "downloaded_repos/moonshine-software_moonshine/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/moonshine-software_moonshine/.github/pull_request_template.md", "downloaded_repos/moonshine-software_moonshine/.github/workflows/analyse.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/js-tests.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/lint.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/node.js.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/rector.yaml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/style-prettier.yml", "downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml", "downloaded_repos/moonshine-software_moonshine/.gitignore", "downloaded_repos/moonshine-software_moonshine/.idea/icon.svg", "downloaded_repos/moonshine-software_moonshine/.meta-storm.xml", "downloaded_repos/moonshine-software_moonshine/.php-cs-fixer.dist.php", "downloaded_repos/moonshine-software_moonshine/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/Makefile", "downloaded_repos/moonshine-software_moonshine/README.md", "downloaded_repos/moonshine-software_moonshine/_ide_helpers.php", "downloaded_repos/moonshine-software_moonshine/art/avatar.jpg", "downloaded_repos/moonshine-software_moonshine/art/bg.jpg", "downloaded_repos/moonshine-software_moonshine/art/bg_full.jpg", "downloaded_repos/moonshine-software_moonshine/art/line-1.jpg", "downloaded_repos/moonshine-software_moonshine/art/line-2.jpg", "downloaded_repos/moonshine-software_moonshine/art/new.jpg", "downloaded_repos/moonshine-software_moonshine/art/sticker.webp", "downloaded_repos/moonshine-software_moonshine/composer.json", "downloaded_repos/moonshine-software_moonshine/monorepo-builder.php", "downloaded_repos/moonshine-software_moonshine/package-lock.json", "downloaded_repos/moonshine-software_moonshine/package.json", "downloaded_repos/moonshine-software_moonshine/phpstan.neon.dist", "downloaded_repos/moonshine-software_moonshine/phpunit-example.xml.dist", "downloaded_repos/moonshine-software_moonshine/rector.php", "downloaded_repos/moonshine-software_moonshine/repair-monorepo.sh", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/README.md", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/composer.json", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/AssetElements.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/AssetManager.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Contracts/HasLinkContact.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Contracts/HasVersionContact.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Css.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineCss.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineJs.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Js.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Raw.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Traits/HasLink.php", "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Traits/WithVersion.php", "downloaded_repos/moonshine-software_moonshine/src/ColorManager/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/ColorManager/README.md", "downloaded_repos/moonshine-software_moonshine/src/ColorManager/composer.json", "downloaded_repos/moonshine-software_moonshine/src/ColorManager/src/ColorManager.php", "downloaded_repos/moonshine-software_moonshine/src/ColorManager/src/ColorMutator.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/Contracts/README.md", "downloaded_repos/moonshine-software_moonshine/src/Contracts/composer.json", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/AssetManager/AssetElementContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/AssetManager/AssetElementsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/AssetManager/AssetManagerContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/AssetManager/AssetResolverContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/ColorManager/ColorManagerContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudPageContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudResourceContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudResourceWithFieldsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudResourceWithModalsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudResourceWithPagesContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudResourceWithQueryParamsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudResourceWithResponseModifiersContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/CrudResourceWithSearchContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/AppliesRegisterContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/CacheAttributesContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/ConfiguratorContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/CoreContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/EndpointsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/FieldsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/OptimizerCollectionContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/RequestContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/RouterContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/StorageContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/TranslatorContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/DependencyInjection/ViewRendererContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasAssetsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasCanSeeContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasComponentsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasCoreContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasResourceContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasStructureContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasUriKeyContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/HasViewRendererContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/PageContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/PagesContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/Paginator/PaginatorCasterContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/Paginator/PaginatorContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/Paginator/PaginatorLinkContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/Paginator/PaginatorLinksContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/ResourceContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/ResourcesContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/StatefulContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/TypeCasts/DataCasterContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/Core/TypeCasts/DataWrapperContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/MenuManager/MenuAutoloaderContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/MenuManager/MenuElementContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/MenuManager/MenuElementsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/MenuManager/MenuFillerContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/MenuManager/MenuManagerContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/ActionButtonContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/ApplyContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/Collection/ActionButtonsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/Collection/ComponentsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/Collection/TableCellsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/Collection/TableRowsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/ComponentAttributesBagContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/ComponentContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/FieldContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/FieldWithComponentContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/FormBuilderContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/FormContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/FormElementContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasAsyncContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasBadgeContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasCasterContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasComponentAttributesContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasFieldsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasHintContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasIconContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasLabelContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasLinkContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasModalContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasOffCanvasContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasQuickFormElementAttributesContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasReactivityContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/HasShowWhenContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/LayoutContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/NowOnContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/SortableFieldContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/TableBuilderContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/TableCellContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/TableRowContract.php", "downloaded_repos/moonshine-software_moonshine/src/Contracts/src/UI/WithoutExtractionContract.php", "downloaded_repos/moonshine-software_moonshine/src/Core/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/Core/README.md", "downloaded_repos/moonshine-software_moonshine/src/Core/composer.json", "downloaded_repos/moonshine-software_moonshine/src/Core/src/AbstractRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/AbstractRouter.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Attributes/Layout.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Collections/BaseCollection.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Collections/Components.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Collections/OptimizerCollection.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Core.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Exceptions/EndpointException.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Exceptions/MoonShineException.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Exceptions/PageException.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Exceptions/ResourceException.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Pages/Page.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Pages/Pages.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorCaster.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorLink.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorLinks.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Resources/Resource.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Resources/Resources.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Storage/FileStorage.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Traits/HasResource.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Traits/InteractsWithRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Traits/NowOn.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Traits/WithAssets.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Traits/WithCore.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Traits/WithUriKey.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/Traits/WithViewRenderer.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataCaster.php", "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataWrapper.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/Laravel/README.md", "downloaded_repos/moonshine-software_moonshine/src/Laravel/composer.json", "downloaded_repos/moonshine-software_moonshine/src/Laravel/config/moonshine.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/database/factories/MoonshineUserFactory.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/database/factories/MoonshineUserRoleFactory.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/database/migrations/2020_10_04_115514_create_moonshine_roles_table.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/database/migrations/2020_10_05_173148_create_moonshine_tables.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/lang/en/auth.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/lang/en/pagination.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/lang/en/ui.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/lang/en/validation.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/routes/moonshine.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Fields/FileModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/FieldsWithoutFilters.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/BelongsToManyModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/BelongsToModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/CheckboxModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/DateModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/DateRangeModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/JsonModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/MorphToModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/RangeModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/SelectModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Applies/Filters/TextModelApply.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/BelongsToOrManyButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/CreateButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/DeleteButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/DetailButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/EditButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/FiltersButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/HasManyButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Buttons/MassDeleteButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Collections/Fields.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/InstallCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeApplyCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeComponentCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeControllerCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeFieldCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeHandlerCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeLayoutCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakePageCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakePolicyCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeTypeCastCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeUserCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MoonShineCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/OptimizeClearCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/OptimizeCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/PublishCommand.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Fragment.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Locales.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Notifications.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Profile.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Search.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Paginator.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Fields/HasAsyncSearchContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Fields/HasModalModeContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Fields/HasOutsideSwitcherContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Fields/HasPivotContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Fields/HasRelatedValuesContact.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Fields/HasTabModeContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Notifications/MoonShineNotificationContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Notifications/NotificationButtonContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Notifications/NotificationItemContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Resource/HasHandlersContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Resource/HasQueryTagsContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/Resource/WithQueryBuilderContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Contracts/WithResponseModifierContract.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/AssetResolver.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShine.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShineConfigurator.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShineRouter.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/Request.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/Translator.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/ViewRenderer.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Enums/Ability.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Enums/Action.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Exceptions/CrudResourceException.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Exceptions/FileFieldException.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Exceptions/FilterException.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Exceptions/ModelRelationFieldException.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Exceptions/MoonShineNotFoundException.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/BelongsTo.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/BelongsToMany.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/HasMany.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/HasManyThrough.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/HasOne.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/HasOneThrough.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/ModelRelationField.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/MorphMany.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/MorphOne.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/MorphTo.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/MorphToMany.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/RelationRepeater.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Slug.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Forms/FiltersForm.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Forms/LoginForm.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Handlers/Handler.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Handlers/Handlers.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/AsyncSearchController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/AuthenticateController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/ComponentController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/CrudController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/HandlerController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/HasManyController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/HomeController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/MethodController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/MoonShineController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/NotificationController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/PageController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/ProfileController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/ReactiveController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/UpdateFieldController.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/Authenticate.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/ChangeLocale.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/SecurityHeadersMiddleware.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/LoginFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/MoonShineFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/ProfileFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Relations/RelationModelColumnUpdateRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Relations/RelationModelFieldRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/CreateFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/DeleteFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/EditFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/MassDeleteFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/StoreFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/UpdateColumnFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/UpdateFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/ViewAnyFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Requests/Resources/ViewFormRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Responses/MoonShineJsonResponse.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Layouts/AppLayout.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Layouts/BaseLayout.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Layouts/BlankLayout.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Layouts/CompactLayout.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Layouts/LoginLayout.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Models/MoonshineUser.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Models/MoonshineUserRole.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineAuth.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineEndpoints.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineUI.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/DatabaseNotification.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/MoonShineMemoryNotification.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/MoonShineNotification.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationButton.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationItem.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationMemoryItem.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/Crud/CrudPage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/Crud/DetailPage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/Crud/FormPage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/Crud/IndexPage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/Dashboard.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/ErrorPage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/LoginPage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/Page.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/ProfilePage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Pages/QuickPage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Providers/MoonShineServiceProvider.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/QueryTags/QueryTag.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Resources/CrudResource.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Resources/ModelResource.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Resources/MoonShineUserResource.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Resources/MoonShineUserRoleResource.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Storage/LaravelStorage.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/CacheAttributes.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/DBOperators.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/MenuAutoloader.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/StubsPath.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Controller/InteractsWithAuth.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Controller/InteractsWithUI.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Fields/BelongsToOrManyCreatable.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Fields/HasHorizontalMode.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Fields/HasModalModeConcern.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Fields/HasTreeMode.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Fields/WithAsyncSearch.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Fields/WithRelatedLink.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Fields/WithRelatedValues.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Request/HasPageRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Request/HasResourceRequest.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceActions.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceCrudRouter.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceEvents.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceModelQuery.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceQuery.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceValidation.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceWithAuthorization.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceWithButtons.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceWithFields.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceWithPageComponents.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceWithParent.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/Resource/ResourceWithTableModifiers.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Traits/WithComponentsPusher.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelDataWrapper.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/PaginatorCaster.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/helpers.php", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Apply.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Component.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Controller.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/CrudPage.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Field.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Handler.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Layout.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/ModelResourceDefault.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/ModelResourceWithPages.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/MoonShineServiceProvider.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Page.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Policy.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/Resource.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/TypeCast.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/assets/css.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/assets/postcss.config.preset.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/assets/tailwind.config.preset.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/pest.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/test.stub", "downloaded_repos/moonshine-software_moonshine/src/Laravel/stubs/view.stub", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/README.md", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/composer.json", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/CanSee.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Group.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Order.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/SkipMenu.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuCondition.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuDivider.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuElement.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuElements.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuGroup.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuItem.php", "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuManager.php", "downloaded_repos/moonshine-software_moonshine/src/Support/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/Support/README.md", "downloaded_repos/moonshine-software_moonshine/src/Support/composer.json", "downloaded_repos/moonshine-software_moonshine/src/Support/src/AlpineJs.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Attributes/Icon.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Attributes/SearchUsingFullText.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Components/MoonShineComponentAttributeBag.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/AsyncCallback.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItem.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItemExtra.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Option.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionGroup.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionImage.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionProperty.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Options.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/ClickAction.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/Color.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/FlashType.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/FormMethod.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/HttpMethod.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/JsEvent.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/Layer.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/ObjectFit.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/PageType.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/SortDirection.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/TextWrap.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Enums/ToastType.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/EventParams.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/ListOf.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Memoize/Backtrace.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Memoize/MemoizeRepository.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/ToastEventParams.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Traits/Makeable.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Traits/WithComponentAttributes.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/Traits/WithQueue.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/UriKey.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/VO/FieldEmptyValue.php", "downloaded_repos/moonshine-software_moonshine/src/Support/src/helpers.php", "downloaded_repos/moonshine-software_moonshine/src/UI/.env.example", "downloaded_repos/moonshine-software_moonshine/src/UI/.gitattributes", "downloaded_repos/moonshine-software_moonshine/src/UI/.gitignore", "downloaded_repos/moonshine-software_moonshine/src/UI/.prettierrc", "downloaded_repos/moonshine-software_moonshine/src/UI/LICENSE.md", "downloaded_repos/moonshine-software_moonshine/src/UI/README.md", "downloaded_repos/moonshine-software_moonshine/src/UI/babel.config.js", "downloaded_repos/moonshine-software_moonshine/src/UI/composer.json", "downloaded_repos/moonshine-software_moonshine/src/UI/jest.config.js", "downloaded_repos/moonshine-software_moonshine/src/UI/package-lock.json", "downloaded_repos/moonshine-software_moonshine/src/UI/package.json", "downloaded_repos/moonshine-software_moonshine/src/UI/postcss.config.cjs", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/base/animation.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/base/common.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/base/layout.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/accordions.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/alerts.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/badges.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/box.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/breadcrumbs.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/buttons.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/cards.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/carousel.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/colors.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/dropdowns.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/dropzone.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/forms.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/languages.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/menu.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/modals.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/navigation.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/notifications.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/offcanvas.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/pagination.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/popovers.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/profile.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/progressbars.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/search.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/social.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/spinners.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/tables.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/tabs.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/theme-switcher.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/components/toasts.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/main.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/minimalistic.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/pages/authentication.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/css/pages/dashboard.css", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/fonts/Gilroy-Black.woff2", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/fonts/Gilroy-Bold.woff2", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/fonts/Gilroy-Medium.woff2", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/fonts/Gilroy-Regular.woff2", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/fonts/Gilroy-SemiBold.woff2", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/ActionButton.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/BelongsToMany.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/CardsBuilder.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Carousel.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Collapse.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Dropdown.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Fragment.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Global.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Modal.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/NavTooltip.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/OffCanvas.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Popover.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/QueryTag.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Range.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Select.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Sortable.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/TableBuilder.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Tabs.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Toast.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Tooltip.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/DTOs/ComponentRequestData.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Core.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Sets.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/AsyncLoadContent.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DOMUpdater.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/Debounce.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DispatchEvents.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/Forms.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/HtmlMode.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/Iterable.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/SelectorsParams.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/ShowWhen.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/TypedDataset.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/UI.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/URLs.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/.gitignore", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/DTOs/ComponentRequestData.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Request/Core.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Request/Sets.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/AsyncLoadContent.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/Debounce.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/DispatchEvents.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/Forms.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/Iterable.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/SelectorsParams.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/ShowWhen.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/TypedDataset.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Support/URLs.test.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/__mocks__/follow-redirects.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/setup.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/app.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/bootstrap.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/layout.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/moonshine-build.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/moonshine.js", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/action-button.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/action-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/alert.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/badge.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/boolean.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/breadcrumbs.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/card.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/cards.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/carousel.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/collapse.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/color.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/components.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/dropdown.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/field-container.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/fields-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/file.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/files.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/flexible-render.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/all-errors.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/builder.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/button.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/fieldset.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/file-item.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/file.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/hint.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/index.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input-error.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input-extensions/copy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input-extensions/ext.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input-extensions/eye.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input-extensions/lock.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input-extensions/up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input-extensions.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/input.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/label.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/select.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/slide-range.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/switcher.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/textarea.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/form/wrapper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/fragment.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/heading.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/icon.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/iterable-wrapper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/assets.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/block.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/body.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/box.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/burger.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/column.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/content.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/div.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/divider.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/favicon.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/flash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/flex.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/footer.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/grid.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/head.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/header.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/html.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/index.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/line-break.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/locales.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/logo.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/mobile-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/notifications.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/profile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/search.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/sidebar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/theme-switcher.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/top-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/layout/wrapper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/link-button.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/link-native.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/loader.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/menu/divider.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/menu/group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/menu/index.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/menu/item-link.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/menu/item.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/metrics/value.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/metrics/wrapped/value.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/modal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/off-canvas.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/pagination.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/popover.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/progress-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/rating.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/spinner.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/table/builder.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/table/index.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/table/row.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/table/td.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/table/th.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/tabs.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/thumbnails.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/title.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/toast.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/tooltip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/components/url.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/errors/404.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/checkbox.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/color.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/fieldset.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/file.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/hidden-ids.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/hidden.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/image.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/input.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/json.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/preview.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/range.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/relationships/belongs-to-many.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/relationships/belongs-to.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/relationships/has-many.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/relationships/has-one.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/relationships/morph-to.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/select.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/slide.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/switch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/fields/textarea.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/academic-cap.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/adjustments-horizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/adjustments-vertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/archive-box-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/archive-box-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/archive-box.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-down-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-down-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-down-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-down-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-down-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-left-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-left-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-left-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-left-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-long-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-long-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-long-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-long-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-path-rounded-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-path.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-right-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-right-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-right-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-right-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-small-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-small-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-small-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-small-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-top-right-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-trending-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-trending-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-up-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-up-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-up-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-up-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-up-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-uturn-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-uturn-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-uturn-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrow-uturn-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrows-pointing-in.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrows-pointing-out.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrows-right-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/arrows-up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/at-symbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/backspace.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/backward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/banknotes.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-3-bottom-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-3-bottom-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-3-center-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-3.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-4.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bars-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/battery-0.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/battery-100.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/battery-50.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/beaker.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bell-alert.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bell-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bell-snooze.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bell.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bolt-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bolt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/book-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bookmark-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bookmark-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bookmark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/briefcase.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/bug-ant.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/building-library.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/building-office-2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/building-office.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/building-storefront.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/academicap.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/adjustments-horizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/adjustments-vertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/archive-box-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/archive-box-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/archive-box.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-down-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-down-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-down-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-down-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-down-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-left-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-left-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-left-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-long-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-long-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-long-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-long-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-path-rounded-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-path.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-right-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-right-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-right-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-top-right-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-trending-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-trending-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-up-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-up-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-up-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-up-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-up-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-uturn-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-uturn-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-uturn-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrow-uturn-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrows-pointing-in.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrows-pointing-out.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrows-right-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/arrows-up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/at-symbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/backspace.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/backward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/banknotes.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-3-bottom-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-3-bottom-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-3-center-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-3.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-4.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bars-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/battery-0.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/battery-100.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/battery-50.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/beaker.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bell-alert.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bell-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bell-snooze.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bell.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bolt-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bolt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/book-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bookmark-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bookmark-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bookmark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/briefcase.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/bug-ant.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/building-library.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/building-office-2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/building-office.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/building-storefront.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cake.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/calculator.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/calendar-days.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/calendar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/camera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chart-bar-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chart-pie.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chat-bubble-bottom-center-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chat-bubble-bottom-center.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chat-bubble-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chat-bubble-left-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chat-bubble-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chat-bubble-oval-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chat-bubble-oval-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/check-badge.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/check-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-double-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-double-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-double-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-double-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/chevron-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/circle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/clipboard-document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/clipboard-document-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/clipboard-document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/clipboard.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/clock.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cloud-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cloud-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cloud.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/code-bracket-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/code-bracket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cog-6-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cog-8-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cog.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/command-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/computer-desktop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cpu-chip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/credit-card.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cube-transparent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cube.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/currency-bangladeshi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/currency-dollar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/currency-euro.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/currency-pound.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/currency-rupee.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/currency-yen.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cursor-arrow-rays.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/cursor-arrow-ripple.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/device-phone-mobile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/device-tablet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-duplicate.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/ellipsis-horizontal-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/ellipsis-horizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/ellipsis-vertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/envelope-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/envelope.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/exclamation-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/exclamation-triangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/eye-dropper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/eye-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/eye.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/face-frown.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/face-smile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/film.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/finger-print.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/fire.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/flag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/folder-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/folder-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/folder-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/folder-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/folder.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/forward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/funnel.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/gif.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/gift-top.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/gift.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/globe-alt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/globe-americas.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/globe-asia-australia.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/globe-europe-africa.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/hand-raised.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/hand-thumb-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/hand-thumb-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/hashtag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/heart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/home-modern.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/home.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/identification.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/inbox-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/inbox-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/inbox.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/information-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/key.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/language.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/lifebuoy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/light-bulb.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/link.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/list-bullet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/lock-closed.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/lock-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/magnifying-glass-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/magnifying-glass-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/magnifying-glass-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/map-pin.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/map.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/megaphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/microphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/minus-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/moon.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/musical-note.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/newspaper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/no-symbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/paint-brush.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/paper-airplane.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/paper-clip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/pause-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/pencil-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/pencil.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/phone-arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/phone-arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/phone-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/phone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/photo.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/play-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/play-pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/play.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/plus-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/power.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/presentation-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/presentation-chart-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/printer.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/puzzle-piece.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/qr-code.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/question-mark-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/queue-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/radio.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/receipt-percent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/receipt-refund.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/rectangle-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/rectangle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/rocket-launch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/rss.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/scale.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/scissors.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/server-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/server.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/share.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/shield-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/shield-exclamation.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/shopping-bag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/shopping-cart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/signal-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/signal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/sparkles.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/speaker-wave.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/speaker-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/square-2-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/square-3-stack-3d.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/squares-2x2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/squares-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/star.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/stop-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/stop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/sun.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/swatch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/table-cells.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/tag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/ticket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/trash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/trophy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/truck.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/tv.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/user-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/user-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/user-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/user-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/user.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/users.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/variable.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/video-camera-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/video-camera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/view-columns.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/viewfinder-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/wallet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/wifi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/window.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/wrench-screwdriver.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/wrench.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/x-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/c/x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cake.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/calculator.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/calendar-days.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/calendar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/camera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chart-bar-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chart-pie.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chat-bubble-bottom-center-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chat-bubble-bottom-center.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chat-bubble-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chat-bubble-left-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chat-bubble-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chat-bubble-oval-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chat-bubble-oval-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/check-badge.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/check-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-double-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-double-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-double-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-double-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/chevron-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/circle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/clipboard-document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/clipboard-document-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/clipboard-document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/clipboard.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/clock.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cloud-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cloud-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cloud.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/code-bracket-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/code-bracket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cog-6-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cog-8-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cog.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/command-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/computer-desktop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cpu-chip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/credit-card.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cube-transparent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cube.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/currency-bangladeshi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/currency-dollar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/currency-euro.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/currency-pound.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/currency-rupee.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/currency-yen.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cursor-arrow-rays.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/cursor-arrow-ripple.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/device-phone-mobile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/device-tablet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-duplicate.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/ellipsis-horizontal-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/ellipsis-horizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/ellipsis-vertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/envelope-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/envelope.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/exclamation-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/exclamation-triangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/eye-dropper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/eye-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/eye.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/face-frown.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/face-smile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/film.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/finger-print.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/fire.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/flag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/folder-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/folder-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/folder-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/folder-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/folder.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/forward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/funnel.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/gif.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/gift-top.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/gift.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/globe-alt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/globe-americas.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/globe-asia-australia.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/globe-europe-africa.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/hand-raised.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/hand-thumb-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/hand-thumb-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/hashtag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/heart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/home-modern.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/home.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/identification.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/inbox-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/inbox-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/inbox.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/information-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/key.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/language.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/lifebuoy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/light-bulb.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/link.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/list-bullet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/lock-closed.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/lock-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/academic-cap.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/adjustments-horizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/adjustments-vertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/archive-box-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/archive-box-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/archive-box.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-down-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-down-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-down-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-down-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-down-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-left-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-left-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-left-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-left-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-long-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-long-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-long-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-long-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-path-rounded-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-path.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-right-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-right-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-right-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-right-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-small-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-small-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-small-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-small-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-top-right-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-trending-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-trending-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-up-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-up-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-up-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-up-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-up-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-uturn-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-uturn-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-uturn-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrow-uturn-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrows-pointing-in.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrows-pointing-out.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrows-right-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/arrows-up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/at-symbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/backspace.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/backward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/banknotes.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-3-bottoleft.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-3-bottoright.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-3-center-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-3.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-4.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bars-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/battery-0.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/battery-100.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/battery-50.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/beaker.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bell-alert.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bell-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bell-snooze.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bell.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bolt-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bolt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/book-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bookmark-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bookmark-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bookmark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/briefcase.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/bug-ant.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/building-library.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/building-office-2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/building-office.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/building-storefront.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cake.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/calculator.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/calendar-days.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/calendar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/camera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chart-bar-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chart-pie.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chat-bubble-bottocenter-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chat-bubble-bottocenter.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chat-bubble-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chat-bubble-left-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chat-bubble-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chat-bubble-oval-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chat-bubble-oval-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/check-badge.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/check-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-double-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-double-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-double-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-double-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/chevron-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/circle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/clipboard-document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/clipboard-document-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/clipboard-document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/clipboard.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/clock.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cloud-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cloud-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cloud.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/code-bracket-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/code-bracket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cog-6-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cog-8-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cog.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/command-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/computer-desktop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cpu-chip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/credit-card.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cube-transparent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cube.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/currency-bangladeshi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/currency-dollar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/currency-euro.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/currency-pound.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/currency-rupee.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/currency-yen.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cursor-arrow-rays.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/cursor-arrow-ripple.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/device-phone-mobile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/device-tablet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-duplicate.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/ellipsis-horizontal-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/ellipsis-horizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/ellipsis-vertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/envelope-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/envelope.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/exclamation-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/exclamation-triangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/eye-dropper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/eye-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/eye.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/face-frown.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/face-smile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/film.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/finger-print.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/fire.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/flag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/folder-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/folder-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/folder-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/folder-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/folder.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/forward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/funnel.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/gif.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/gift-top.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/gift.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/globe-alt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/globe-americas.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/globe-asia-australia.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/globe-europe-africa.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/hand-raised.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/hand-thumb-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/hand-thumb-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/hashtag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/heart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/home-modern.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/home.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/identification.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/inbox-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/inbox-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/inbox.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/information-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/key.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/language.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/lifebuoy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/light-bulb.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/link.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/list-bullet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/lock-closed.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/lock-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/magnifying-glass-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/magnifying-glass-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/magnifying-glass-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/map-pin.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/map.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/megaphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/microphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/minus-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/minus-small.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/moon.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/musical-note.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/newspaper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/no-symbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/paint-brush.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/paper-airplane.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/paper-clip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/pause-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/pencil-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/pencil.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/phone-arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/phone-arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/phone-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/phone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/photo.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/play-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/play-pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/play.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/plus-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/plus-small.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/power.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/presentation-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/presentation-chart-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/printer.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/puzzle-piece.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/qr-code.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/question-mark-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/queue-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/radio.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/receipt-percent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/receipt-refund.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/rectangle-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/rectangle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/rocket-launch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/rss.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/scale.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/scissors.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/server-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/server.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/share.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/shield-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/shield-exclamation.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/shopping-bag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/shopping-cart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/signal-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/signal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/sparkles.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/speaker-wave.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/speaker-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/square-2-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/square-3-stack-3d.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/squares-2x2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/squares-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/star.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/stop-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/stop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/sun.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/swatch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/table-cells.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/tag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/ticket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/trash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/trophy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/truck.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/tv.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/user-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/user-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/user-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/user-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/user.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/users.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/variable.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/video-camera-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/video-camera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/view-columns.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/viewfinder-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/wallet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/wifi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/window.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/wrench-screwdriver.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/wrench.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/x-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/m/x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/magnifying-glass-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/magnifying-glass-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/magnifying-glass-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/map-pin.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/map.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/megaphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/microphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/minus-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/minus-small.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/moon.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/musical-note.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/newspaper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/nsymbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/paint-brush.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/paper-airplane.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/paper-clip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/pause-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/pencil-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/pencil.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/phone-arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/phone-arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/phone-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/phone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/photo.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/play-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/play-pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/play.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/plus-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/plus-small.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/power.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/presentation-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/presentation-chart-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/printer.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/puzzle-piece.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/qr-code.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/question-mark-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/queue-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/radio.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/receipt-percent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/receipt-refund.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/rectangle-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/rectangle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/rocket-launch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/rss.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/academic-cap.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/adjustmenthorizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/adjustmentvertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/archive-box-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/archive-box-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/archive-box.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-down-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-down-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-down-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-down-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-down-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-left-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-left-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-left-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-left-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-long-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-long-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-long-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-long-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-path-rounded-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-path.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-right-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-right-end-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-right-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-right-start-on-rectangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-small-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-small-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-small-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-small-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-top-right-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-trending-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-trending-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-up-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-up-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-up-on-square-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-up-on-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-up-tray.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-uturn-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-uturn-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-uturn-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrow-uturn-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrowpointing-in.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrowpointing-out.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrowright-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/arrowup-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/at-symbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/backspace.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/backward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/banknotes.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bar2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bar3-bottom-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bar3-bottom-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bar3-center-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bar3.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bar4.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bararrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bararrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/battery-0.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/battery-100.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/battery-50.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/beaker.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bell-alert.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bell-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bell-snooze.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bell.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bolt-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bolt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/book-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bookmark-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bookmark-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bookmark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/briefcase.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/bug-ant.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/building-library.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/building-office-2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/building-office.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/building-storefront.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cake.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/calculator.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/calendar-days.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/calendar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/camera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chart-bar-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chart-pie.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chat-bubble-bottom-center-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chat-bubble-bottom-center.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chat-bubble-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chat-bubble-left-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chat-bubble-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chat-bubble-oval-left-ellipsis.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chat-bubble-oval-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/check-badge.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/check-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-double-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-double-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-double-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-double-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-up-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/chevron-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/circle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/clipboard-document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/clipboard-document-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/clipboard-document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/clipboard.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/clock.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cloud-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cloud-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cloud.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/code-bracket-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/code-bracket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cog-6-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cog-8-tooth.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cog.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/command-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/computer-desktop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cpu-chip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/credit-card.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cube-transparent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cube.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/currency-bangladeshi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/currency-dollar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/currency-euro.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/currency-pound.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/currency-rupee.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/currency-yen.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cursor-arrow-rays.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/cursor-arrow-ripple.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/device-phone-mobile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/device-tablet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-arrow-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-duplicate.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document-text.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/document.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/ellipsihorizontal-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/ellipsihorizontal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/ellipsivertical.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/envelope-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/envelope.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/exclamation-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/exclamation-triangle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/eye-dropper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/eye-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/eye.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/face-frown.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/face-smile.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/film.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/finger-print.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/fire.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/flag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/folder-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/folder-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/folder-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/folder-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/folder.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/forward.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/funnel.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/gif.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/gift-top.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/gift.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/globe-alt.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/globe-americas.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/globe-asia-australia.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/globe-europe-africa.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/hand-raised.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/hand-thumb-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/hand-thumb-up.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/hashtag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/heart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/home-modern.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/home.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/identification.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/inbox-arrow-down.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/inbox-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/inbox.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/information-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/key.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/language.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/lifebuoy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/light-bulb.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/link.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/list-bullet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/lock-closed.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/lock-open.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/magnifying-glascircle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/magnifying-glasminus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/magnifying-glasplus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/magnifying-glass.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/map-pin.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/map.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/megaphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/microphone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/minucircle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/minusmall.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/moon.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/musical-note.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/newspaper.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/no-symbol.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/paint-brush.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/paper-airplane.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/paper-clip.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/pause-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/pencil-square.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/pencil.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/phone-arrow-down-left.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/phone-arrow-up-right.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/phone-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/phone.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/photo.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/play-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/play-pause.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/play.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/plucircle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/plusmall.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/power.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/presentation-chart-bar.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/presentation-chart-line.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/printer.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/puzzle-piece.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/qr-code.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/question-mark-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/queue-list.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/radio.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/receipt-percent.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/receipt-refund.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/rectangle-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/rectangle-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/rocket-launch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/rss.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/scale.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/scissors.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/server-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/server.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/share.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/shield-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/shield-exclamation.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/shopping-bag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/shopping-cart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/signal-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/signal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/sparkles.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/speaker-wave.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/speaker-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/square-2-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/square-3-stack-3d.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/square2x2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/squareplus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/star.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/stop-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/stop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/sun.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/swatch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/table-cells.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/tag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/ticket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/trash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/trophy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/truck.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/tv.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/user-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/user-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/user-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/user-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/user.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/users.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/variable.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/video-camera-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/video-camera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/view-columns.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/viewfinder-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/wallet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/wifi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/window.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/wrench-screwdriver.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/wrench.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/x-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/s/x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/scale.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/scissors.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/server-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/server.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/share.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/shield-check.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/shield-exclamation.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/shopping-bag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/shopping-cart.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/signal-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/signal.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/sparkles.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/speaker-wave.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/speaker-x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/square-2-stack.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/square-3-stack-3d.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/squares-2x2.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/squares-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/star.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/stop-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/stop.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/sun.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/swatch.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/table-cells.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/tag.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/ticket.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/trash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/trophy.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/truck.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/tv.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/user-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/user-group.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/user-minus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/user-plus.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/user.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/users.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/variable.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/videcamera-slash.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/videcamera.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/view-columns.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/viewfinder-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/wallet.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/wifi.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/window.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/wrench-screwdriver.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/wrench.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/x-circle.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/icons/x-mark.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/page.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/shared/img-popup.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/resources/views/shared/toasts.blade.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Applies/AppliesRegister.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Collections/ActionButtons.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Collections/Fields.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Collections/TableCells.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Collections/TableRows.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/AbstractWithComponents.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/ActionButton.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/ActionGroup.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Alert.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Badge.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Boolean.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Breadcrumbs.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Card.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/CardsBuilder.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Carousel.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Collapse.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Color.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Components.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Dropdown.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/FieldsGroup.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Files.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/FlexibleRender.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/FormBuilder.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Heading.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Icon.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/IterableComponent.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Assets.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Body.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Box.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Burger.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Column.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Content.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Div.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Divider.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Favicon.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Flash.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Flex.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Footer.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Grid.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Head.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Header.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Html.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Layout.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/LineBreak.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Logo.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Menu.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Meta.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/MobileBar.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Sidebar.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/ThemeSwitcher.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/TopBar.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Wrapper.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Link.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Loader.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Metrics/Wrapped/Metric.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Metrics/Wrapped/ValueMetric.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Modal.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/MoonShineComponent.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/OffCanvas.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Popover.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/ProgressBar.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Rating.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Spinner.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Table/TableBuilder.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Table/TableRow.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Table/TableTd.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Table/TableTh.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Tabs/Tab.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Tabs.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Thumbnails.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Title.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Url.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/When.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/DefaultValueTypes/CanBeArray.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/DefaultValueTypes/CanBeBool.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/DefaultValueTypes/CanBeEnum.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/DefaultValueTypes/CanBeNumeric.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/DefaultValueTypes/CanBeObject.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/DefaultValueTypes/CanBeString.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/DefaultValueTypes/MustBeNull.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/FieldsWrapperContract.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/FileableContract.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/HasDefaultValueContract.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/HasUpdateOnPreviewContract.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/RangeFieldContract.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/RemovableContract.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Contracts/WrapperWithApplyContract.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Enums/HtmlMode.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Exceptions/ActionButtonException.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Exceptions/FieldException.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Exceptions/MoonShineComponentException.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Checkbox.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Color.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Date.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/DateRange.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Email.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Enum.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Field.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/FieldContainer.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Fieldset.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/File.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/FormElement.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Hidden.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/HiddenIds.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/ID.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Image.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Json.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Number.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Password.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/PasswordRepeat.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Phone.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Position.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Preview.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Range.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/RangeSlider.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Select.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/StackFields.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Switcher.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Template.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Text.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Textarea.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Url.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/InputExtensions/InputCopy.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/InputExtensions/InputExt.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/InputExtensions/InputExtension.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/InputExtensions/InputEye.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/InputExtensions/InputLock.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/InputExtensions/InputNumberUpDown.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Sets/UpdateOnPreviewPopover.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/ActionButton/InDropdownOrLine.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/ActionButton/WithModal.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/ActionButton/WithOffCanvas.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Components/HasDifferentHtmlTag.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Components/WithColumnSpan.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Components/WithComponents.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Components/WithHeadingGradation.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Components/WithSlotContent.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/Applies.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/BooleanTrait.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/CanBeMultiple.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/DateTrait.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/FileDeletable.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/FileTrait.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/HasPlaceholder.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/HasTabModeConcern.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/HasVerticalMode.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/NumberTrait.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/RangeTrait.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/Reactivity.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/Searchable.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/SelectTrait.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/ShowWhen.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/UpdateOnPreview.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithAdditionalFields.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithBadge.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithDefaultValue.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithEscapedValue.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithHint.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithInputExtensions.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithLink.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithMask.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithQuickFormElementAttributes.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Fields/WithSorts.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/HasAsync.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/HasCanSee.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/HasDataCast.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Removable.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/Table/TableStates.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/WithBadge.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/WithFields.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/WithIcon.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/WithLabel.php", "downloaded_repos/moonshine-software_moonshine/src/UI/src/Traits/WithStorage.php", "downloaded_repos/moonshine-software_moonshine/src/UI/tailwind.config.js", "downloaded_repos/moonshine-software_moonshine/src/UI/vite.config.js"], "skipped": [{"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/mutate.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-commits.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/split-releases.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/AssetManager.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineCss.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/InlineJs.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/AssetManager/src/Raw.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/AbstractRequest.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Attributes/Layout.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/Paginator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorCaster.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/Paginator/PaginatorLink.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataCaster.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Core/src/TypeCasts/MixedDataWrapper.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakePageCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/MakeResourceCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Commands/PublishCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Notifications.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Profile.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Layout/Search.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Components/Paginator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/MoonShineConfigurator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/DependencyInjection/Translator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/BelongsToMany.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Fields/Relationships/RelationRepeater.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Forms/FiltersForm.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Controllers/MoonShineController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Http/Middleware/ChangeLocale.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Models/MoonshineUserRole.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/MoonShineEndpoints.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationButton.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationItem.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Notifications/NotificationMemoryItem.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Storage/LaravelStorage.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/CacheAttributes.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/MenuAutoloader.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/Support/StubsPath.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelCaster.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/ModelDataWrapper.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Laravel/src/TypeCasts/PaginatorCaster.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/CanSee.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Group.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/Order.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/Attributes/SkipMenu.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/MenuManager/src/MenuCondition.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/AlpineJs.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/AsyncCallback.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItem.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/FileItemExtra.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Option.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionGroup.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionImage.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/OptionProperty.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/DTOs/Select/Options.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/ListOf.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/UriKey.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/Support/src/VO/FieldEmptyValue.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/android-chrome-192x192.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/android-chrome-512x512.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/apple-touch-icon.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/assets/app.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/assets/main.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/assets/minimalistic.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/avatar.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/browserconfig.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/favicon-16x16.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/favicon-32x32.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/favicon.ico", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/fonts/<PERSON><PERSON>-Black.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/fonts/Gilroy-Bold.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/fonts/Gilroy-Medium.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/fonts/Gilroy-Regular.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/fonts/Gilroy-SemiBold.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/logo-small.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/logo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/manifest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/mstile-150x150.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/robots.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/safari-pinned-tab.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/dist/site.webmanifest", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/AbstractLayout.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Components/Layout/Menu.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Fields/Json.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/src/UI/src/Sets/UpdateOnPreviewPopover.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Expectations.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/AppliesRegisterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/CacheAttributesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/ComponentCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/FieldCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/InstallCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/LayoutCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/MakeSimpleStubsCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/OptimizeClearCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/OptimizeCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/PageCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/PublishCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/ResourceCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Commands/UserCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/ComponentsEqualsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/AsyncSearchControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/ComponentControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/CrudControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/HasManyControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/HomeControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/MethodControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/ProfileControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/QuickPageControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/ReactiveControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Controllers/UpdateFieldControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/CheckboxFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/DateRangeFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/FileFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/FileMultipleFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/JsonFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/RangeFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/Relationships/BelongsToManyFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/Relationships/BelongsToTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/Relationships/HasManyFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/Relationships/HasOneFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/Relationships/MorphToTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/SelectFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/SwitcherFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/TempateFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Fields/TextFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/FormBuilderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/LocalizationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/MenuAutoloaderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/MoonShineRequestTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/MoonShineRouterTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/MoonShineTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Pages/IndexPageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Pages/PageFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Pages/PageWithRelationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Pages/ProfilePageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Resources/ResourceWithCustomPageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Resources/ResourceWithFieldsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Resources/ResourceWithFileHasManyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Resources/ResourceWithHasOneFileTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Resources/ResourceWithMultiCustomPageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/Resources/ResourceWithPoliciesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Feature/SDUITest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Enums/TestEnumColor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Factories/CategoryFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Factories/CommentFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Factories/CoverFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Factories/ItemFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Files/test.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Migrations/2023_05_22_094625_create_categories.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Migrations/2023_05_22_130906_create_items.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Migrations/2023_05_22_130910_create_images.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Migrations/2023_05_22_130914_create_files.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Migrations/2023_08_27_155352_create_comments.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Migrations/2023_08_28_073630_create_covers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/Category.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/Comment.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/Cover.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/FileModel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/ImageModel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/Item.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/Traits/MorphRelationTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/Traits/UserBelongsToTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Models/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/CategoryResource/CategoryPageDetail.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/CategoryResource/CategoryPageForm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/CategoryResource/CategoryPageIndex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/CoverResource/CoverPageDetail.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/CoverResource/CoverPageForm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/CoverResource/CoverPageIndex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/Custom/CustomPageDetail.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/Custom/CustomPageForm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/Custom/CustomPageIndex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/NoType/CustomNoTypeForm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Pages/NoType/CustomNoTypeIndex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Policies/CommentPolicy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Policies/ItemPolicy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/QuickPageController.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Requests/CrudRequestFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/AbstractTestingResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/Crud/AbstractTestingCrudResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/Crud/TestCommentCrudResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestCategoryResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestCommentResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestCoverResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestFileResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestFileResourceWithParent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestHasManyCommentResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestImageResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestItemResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/TestResourceBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/WithCustomPages/TestCategoryPageResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Resources/WithCustomPages/TestCoverPageResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/TestServiceProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Fixtures/Views/quick-page.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/AssetManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Components/TableBuilderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/CheckboxFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/ColorFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/DateFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/DateRangeFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/EmailFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/EnumFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/FieldsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/FieldsetFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/FileFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/HiddenFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/IDFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/ImageFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/JsonFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/NumberFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/PasswordFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/PasswordRepeatFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/PhoneFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/PositionFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/PreviewFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/RangeFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/RangeSliderFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/Relationships/BelongsToManyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/Relationships/BelongsToTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/Relationships/MorphToTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/SelectFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/ShowWhenTraitTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/SlugFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/SwitcherFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/TextFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/TextareaFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Fields/UrlFieldTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/ListOfTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/MenuManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/MoonShineAuthTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/MoonShineTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Pages/PageComponentsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Pages/PageTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/moonshine-software_moonshine/tests/Unit/Resources/ResourceRouterTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.676408052444458, "profiling_times": {"config_time": 5.755950927734375, "core_time": 9.840852975845337, "ignores_time": 0.0016529560089111328, "total_time": 15.599745273590088}, "parsing_time": {"total_time": 7.648832321166992, "per_file_time": {"mean": 0.003954928811358327, "std_dev": 0.0001429456575180362}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 28.682018756866455, "per_file_time": {"mean": 0.004778743544962758, "std_dev": 0.0019269421637842876}, "very_slow_stats": {"time_ratio": 0.17121556638057842, "count_ratio": 0.0004998333888703765}, "very_slow_files": [{"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/TableBuilder.js", "ftime": 1.5551848411560059}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Select.js", "ftime": 1.6214702129364014}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "ftime": 1.7341530323028564}]}, "matching_time": {"total_time": 5.891247987747192, "per_file_and_rule_time": {"mean": 0.0021337370473550154, "std_dev": 0.00011876039884415674}, "very_slow_stats": {"time_ratio": 0.2126741397148527, "count_ratio": 0.002897500905469033}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.10511088371276855}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/ActionButton.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.11900520324707031}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Request/Core.test.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.12126302719116211}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/TableBuilder.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.15269994735717773}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.16982197761535645}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/__tests__/Request/Core.test.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.17495298385620117}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Select.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.18237805366516113}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.22768402099609375}]}, "tainting_time": {"total_time": 2.6846225261688232, "per_def_and_rule_time": {"mean": 0.0022886807554721423, "std_dev": 4.51739366293154e-05}, "very_slow_stats": {"time_ratio": 0.11760990532696668, "count_ratio": 0.004262574595055414}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Select.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.051969051361083984}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.05232405662536621}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Select.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.05816197395324707}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Select.js", "fline": 1, "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.07171010971069336}, {"fpath": "downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Select.js", "fline": 1, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.0815730094909668}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1149784512}, "engine_requested": "OSS", "skipped_rules": []}