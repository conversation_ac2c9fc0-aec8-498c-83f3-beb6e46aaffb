{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/subs.js", "start": {"line": 97, "col": 59, "offset": 2321}, "end": {"line": 97, "col": 64, "offset": 2326}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/riemann_riemann-dash/.github/workflows/ci.yml", "downloaded_repos/riemann_riemann-dash/.gitignore", "downloaded_repos/riemann_riemann-dash/CHANGELOG.md", "downloaded_repos/riemann_riemann-dash/Gemfile", "downloaded_repos/riemann_riemann-dash/Gemfile.lock", "downloaded_repos/riemann_riemann-dash/LICENSE", "downloaded_repos/riemann_riemann-dash/README.markdown", "downloaded_repos/riemann_riemann-dash/Rakefile", "downloaded_repos/riemann_riemann-dash/SECURITY.md", "downloaded_repos/riemann_riemann-dash/bin/riemann-dash", "downloaded_repos/riemann_riemann-dash/example/config.rb", "downloaded_repos/riemann_riemann-dash/example/config.ru", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/app.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/browser_config/file.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/browser_config/s3.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/browser_config.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/config.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/controller/css.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/controller/index.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/clock.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/dash.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/eventPane.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/favicon.ico", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/format.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/keys.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/persistence.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/profile.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/sounds/beep.wav", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/sounds/geiger.wav", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/strings.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/subs.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/toolbar.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/util.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/view.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/dial.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/flot.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/gauge.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/geiger.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/grid.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/help.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/iframe.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/list.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/log.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/timeseries.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/title.js", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/x.png", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/rack/static.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/version.rb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/views/css.scss", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/views/index.erb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/views/layout.erb", "downloaded_repos/riemann_riemann-dash/lib/riemann/dash.rb", "downloaded_repos/riemann_riemann-dash/riemann-dash.gemspec", "downloaded_repos/riemann_riemann-dash/sh/c", "downloaded_repos/riemann_riemann-dash/sh/env.rb", "downloaded_repos/riemann_riemann-dash/sh/test"], "skipped": [{"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/PriorityQueue.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/backbone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.colorhelpers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.colorhelpers.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.canvas.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.canvas.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.categories.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.categories.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.crosshair.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.crosshair.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.errorbars.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.errorbars.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.fillbetween.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.fillbetween.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.image.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.image.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.navigate.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.navigate.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.pie.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.pie.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.resize.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.resize.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.selection.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.selection.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.stack.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.stack.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.symbol.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.symbol.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.threshold.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.threshold.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.time.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.time.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.tooltip.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/flot/jquery.flot.tooltip.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/gauge.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/jquery/jquery-1.9.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/jquery/jquery-1.9.1.min.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/jquery/jquery-ui-1.10.2.custom.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/jquery/jquery.quickfit.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/jquery/jquery.simplemodal.1.4.4.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/jquery.gauge.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/lodash.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/smoothie.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/toastr/toastr.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/vendor/toastr/toastr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/test/browser_config_test.rb", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/test/config_test.rb", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/test/fixtures/config/basic_config.rb", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/test/fixtures/config/ws_config.rb", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/test/fixtures/ws_config/dummy_config.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/test/fixtures/ws_config/pretty_printed_config.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/riemann_riemann-dash/test/test_helper.rb", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.67631196975708, "profiling_times": {"config_time": 6.126254558563232, "core_time": 3.141840934753418, "ignores_time": 0.0016891956329345703, "total_time": 9.27066445350647}, "parsing_time": {"total_time": 1.2777245044708252, "per_file_time": {"mean": 0.034533094715427703, "std_dev": 0.0017276082585336039}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 5.590496301651001, "per_file_time": {"mean": 0.037520109407053706, "std_dev": 0.012246946159323624}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.470979690551758, "per_file_and_rule_time": {"mean": 0.012543044114475928, "std_dev": 0.0006288822692419646}, "very_slow_stats": {"time_ratio": 0.22498851799105368, "count_ratio": 0.02030456852791878}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/timeseries.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.12654614448547363}, {"fpath": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/grid.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.13058996200561523}, {"fpath": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/views/flot.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.14499306678771973}, {"fpath": "downloaded_repos/riemann_riemann-dash/lib/riemann/dash/public/view.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.15381288528442383}]}, "tainting_time": {"total_time": 0.7861251831054688, "per_def_and_rule_time": {"mean": 0.003990483162971922, "std_dev": 4.7793394018969295e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}