{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/robinp7720_Oblecto/Dockerfile", "start": {"line": 10, "col": 1, "offset": 229}, "end": {"line": 10, "col": 44, "offset": 272}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [ \"node\", \"dist/bin/oblecto\", \"start\" ]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/Seedbox.js", "start": {"line": 54, "col": 29, "offset": 1934}, "end": {"line": 54, "col": 75, "offset": 1980}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxController.js", "start": {"line": 173, "col": 17, "offset": 5443}, "end": {"line": 173, "col": 83, "offset": 5509}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxController.js", "start": {"line": 174, "col": 17, "offset": 5528}, "end": {"line": 174, "col": 31, "offset": 5542}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxController.js", "start": {"line": 215, "col": 17, "offset": 6806}, "end": {"line": 215, "col": 49, "offset": 6838}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxController.js", "start": {"line": 216, "col": 17, "offset": 6856}, "end": {"line": 216, "col": 31, "offset": 6870}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/episodes.js", "start": {"line": 48, "col": 18, "offset": 1721}, "end": {"line": 48, "col": 25, "offset": 1728}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/episodes.js", "start": {"line": 57, "col": 22, "offset": 2105}, "end": {"line": 57, "col": 31, "offset": 2114}, "extra": {"message": "The application processes user-input, this is passed to res.sendFile which can allow an attacker to arbitrarily read files on the system through path traversal. It is recommended to perform input validation in addition to canonicalizing the path. This allows you to validate the path against the intended directory it should be accessing.", "metadata": {"references": ["https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html"], "technology": ["express"], "category": "security", "cwe": ["CWE-73: External Control of File Name or Path"], "owasp": ["A04:2021 - Insecure Design"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "shortlink": "https://sg.run/7DJk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/episodes.js", "start": {"line": 113, "col": 18, "offset": 4119}, "end": {"line": 113, "col": 31, "offset": 4132}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/episodes.js", "start": {"line": 144, "col": 18, "offset": 5126}, "end": {"line": 144, "col": 25, "offset": 5133}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "start": {"line": 51, "col": 18, "offset": 1690}, "end": {"line": 51, "col": 25, "offset": 1697}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "start": {"line": 90, "col": 18, "offset": 2981}, "end": {"line": 90, "col": 25, "offset": 2988}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "start": {"line": 98, "col": 22, "offset": 3248}, "end": {"line": 98, "col": 26, "offset": 3252}, "extra": {"message": "The application processes user-input, this is passed to res.sendFile which can allow an attacker to arbitrarily read files on the system through path traversal. It is recommended to perform input validation in addition to canonicalizing the path. This allows you to validate the path against the intended directory it should be accessing.", "metadata": {"references": ["https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html"], "technology": ["express"], "category": "security", "cwe": ["CWE-73: External Control of File Name or Path"], "owasp": ["A04:2021 - Insecure Design"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "shortlink": "https://sg.run/7DJk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "start": {"line": 149, "col": 22, "offset": 4979}, "end": {"line": 149, "col": 26, "offset": 4983}, "extra": {"message": "The application processes user-input, this is passed to res.sendFile which can allow an attacker to arbitrarily read files on the system through path traversal. It is recommended to perform input validation in addition to canonicalizing the path. This allows you to validate the path against the intended directory it should be accessing.", "metadata": {"references": ["https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html"], "technology": ["express"], "category": "security", "cwe": ["CWE-73: External Control of File Name or Path"], "owasp": ["A04:2021 - Insecure Design"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "shortlink": "https://sg.run/7DJk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "start": {"line": 208, "col": 18, "offset": 6879}, "end": {"line": 208, "col": 23, "offset": 6884}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "start": {"line": 241, "col": 18, "offset": 7926}, "end": {"line": 241, "col": 32, "offset": 7940}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/misc.js", "start": {"line": 19, "col": 18, "offset": 642}, "end": {"line": 19, "col": 52, "offset": 676}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/misc.js", "start": {"line": 29, "col": 18, "offset": 1007}, "end": {"line": 29, "col": 52, "offset": 1041}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/sources.js", "start": {"line": 14, "col": 18, "offset": 526}, "end": {"line": 14, "col": 67, "offset": 575}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/tvshows.js", "start": {"line": 37, "col": 18, "offset": 1303}, "end": {"line": 37, "col": 25, "offset": 1310}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/tvshows.js", "start": {"line": 45, "col": 18, "offset": 1551}, "end": {"line": 45, "col": 22, "offset": 1555}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/tvshows.js", "start": {"line": 66, "col": 18, "offset": 2198}, "end": {"line": 66, "col": 22, "offset": 2202}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/tvshows.js", "start": {"line": 74, "col": 22, "offset": 2466}, "end": {"line": 74, "col": 31, "offset": 2475}, "extra": {"message": "The application processes user-input, this is passed to res.sendFile which can allow an attacker to arbitrarily read files on the system through path traversal. It is recommended to perform input validation in addition to canonicalizing the path. This allows you to validate the path against the intended directory it should be accessing.", "metadata": {"references": ["https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html"], "technology": ["express"], "category": "security", "cwe": ["CWE-73: External Control of File Name or Path"], "owasp": ["A04:2021 - Insecure Design"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "shortlink": "https://sg.run/7DJk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/users.js", "start": {"line": 22, "col": 18, "offset": 672}, "end": {"line": 22, "col": 22, "offset": 676}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/users.js", "start": {"line": 32, "col": 18, "offset": 1017}, "end": {"line": 32, "col": 22, "offset": 1021}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/robinp7720_Oblecto/.babelrc", "downloaded_repos/robinp7720_Oblecto/.deepsource.toml", "downloaded_repos/robinp7720_Oblecto/.dockerignore", "downloaded_repos/robinp7720_Oblecto/.eslintrc.js", "downloaded_repos/robinp7720_Oblecto/.github/FUNDING.yml", "downloaded_repos/robinp7720_Oblecto/.github/workflows/docker-publish.yml", "downloaded_repos/robinp7720_Oblecto/.github/workflows/mocha.yml", "downloaded_repos/robinp7720_Oblecto/.github/workflows/node.js.yml", "downloaded_repos/robinp7720_Oblecto/.gitignore", "downloaded_repos/robinp7720_Oblecto/.gitmodules", "downloaded_repos/robinp7720_Oblecto/.jshintrc", "downloaded_repos/robinp7720_Oblecto/.mocharc.js", "downloaded_repos/robinp7720_Oblecto/Dockerfile", "downloaded_repos/robinp7720_Oblecto/LICENSE", "downloaded_repos/robinp7720_Oblecto/Oblecto.code-workspace", "downloaded_repos/robinp7720_Oblecto/README.md", "downloaded_repos/robinp7720_Oblecto/images/Favicon.png", "downloaded_repos/robinp7720_Oblecto/images/logomark BW.png", "downloaded_repos/robinp7720_Oblecto/images/logomark blackBG.png", "downloaded_repos/robinp7720_Oblecto/images/logomark.png", "downloaded_repos/robinp7720_Oblecto/images/logotype.png", "downloaded_repos/robinp7720_Oblecto/package-lock.json", "downloaded_repos/robinp7720_Oblecto/package.json", "downloaded_repos/robinp7720_Oblecto/res/config.json", "downloaded_repos/robinp7720_Oblecto/scripts/test.sh", "downloaded_repos/robinp7720_Oblecto/src/bin/oblecto.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/adduser.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/changepassword.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/deluser.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/helpers/argumentError.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/helpers/generateAssetDirectories.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/init/assets.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/init/database.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/init/general.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/init/index.js", "downloaded_repos/robinp7720_Oblecto/src/bin/scripts/removepassword.js", "downloaded_repos/robinp7720_Oblecto/src/config.ts", "downloaded_repos/robinp7720_Oblecto/src/core/graphical.js", "downloaded_repos/robinp7720_Oblecto/src/core/index.js", "downloaded_repos/robinp7720_Oblecto/src/index.js", "downloaded_repos/robinp7720_Oblecto/src/interfaces/config.ts", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/ArtworkScaler.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/ArtworkUtils.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/movies/AggregateMovieArtworkRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/movies/MovieArtworkCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/movies/MovieArtworkDownloader.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/movies/artworkRetrievers/FanarttvMovieArtworkRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/movies/artworkRetrievers/TmdbMovieArtworkRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/series/AggregateSeriesArtworkRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/series/SeriesArtworkCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/series/SeriesArtworkDownloader.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/series/artworkRetrievers/FanarttvSeriesArtworkRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/series/artworkRetrievers/TmdbSeriesArtworkRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/artwork/series/artworkRetrievers/TvdbSeriesArtworkRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/cleaners/FileCleaner.js", "downloaded_repos/robinp7720_Oblecto/src/lib/cleaners/MovieCleaner.js", "downloaded_repos/robinp7720_Oblecto/src/lib/cleaners/SeriesCleaner.js", "downloaded_repos/robinp7720_Oblecto/src/lib/common/AggregateIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/common/AggregateUpdateRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/downloader/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/errors.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/branding/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/displaypreferences/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/items/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/quickconnect/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/sessions/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/shows/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/system/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/system/info.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/system/ping.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/users/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/videos/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/web/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/helpers.js", "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/errors/DebugExtendableError.js", "downloaded_repos/robinp7720_Oblecto/src/lib/errors/ExtendableError.js", "downloaded_repos/robinp7720_Oblecto/src/lib/errors/FileExistsError.js", "downloaded_repos/robinp7720_Oblecto/src/lib/errors/IdentificationError.js", "downloaded_repos/robinp7720_Oblecto/src/lib/errors/InfoExtendableError.js", "downloaded_repos/robinp7720_Oblecto/src/lib/errors/VideoAnalysisError.js", "downloaded_repos/robinp7720_Oblecto/src/lib/errors/WarnExtendableError.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/client/FederationClient.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/client/FederationClientController.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/client/FederationDataClient.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/client/FederationMediaClient.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/server/FederationController.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/server/FederationDataServer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/server/FederationDataServerConnection.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/server/FederationMediaServer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/server/FederationMediaServerConnection.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/server/FederationServer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federation/server/FederationServerConnection.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federationindexer/FederationEpisodeIndexer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/federationindexer/FederationMovieIndexer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/MediaIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/files/FileIndexer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/movies/MovieCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/movies/MovieIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/movies/MovieIndexer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/movies/identifiers/TmdbMovieidentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/EpisodeIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/SeriesCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/SeriesIdentifer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/SeriesIndexer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/identifiers/TmdbEpisodeIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/identifiers/TmdbSeriesIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/identifiers/TvdbEpisodeIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/indexers/series/identifiers/TvdbSeriesIdentifier.js", "downloaded_repos/robinp7720_Oblecto/src/lib/oblecto/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/queue/index.js", "downloaded_repos/robinp7720_Oblecto/src/lib/realtime/RealtimeClient.js", "downloaded_repos/robinp7720_Oblecto/src/lib/realtime/RealtimeController.js", "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/Seedbox.js", "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxController.js", "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxImportDriver.js", "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxImportDrivers/SeedboxImportFTP.js", "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxImportDrivers/SeedboxImportFTPS.js", "downloaded_repos/robinp7720_Oblecto/src/lib/seedbox/SeedboxImportDrivers/SeedboxImportSSH.js", "downloaded_repos/robinp7720_Oblecto/src/lib/sets/tv/MovieSetCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/streamSessions/StreamSession.js", "downloaded_repos/robinp7720_Oblecto/src/lib/streamSessions/StreamSessionController.js", "downloaded_repos/robinp7720_Oblecto/src/lib/streamSessions/StreamSessionTypes/DirectHttpStreamSession.js", "downloaded_repos/robinp7720_Oblecto/src/lib/streamSessions/StreamSessionTypes/DirectStreamSession.js", "downloaded_repos/robinp7720_Oblecto/src/lib/streamSessions/StreamSessionTypes/HLSStreamer.js", "downloaded_repos/robinp7720_Oblecto/src/lib/streamSessions/StreamSessionTypes/RecodeFederationStreamSession.js", "downloaded_repos/robinp7720_Oblecto/src/lib/streamSessions/StreamSessionTypes/RecodeStreamSession.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/files/FileUpdateCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/files/FileUpdater.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/movies/MovieUpdateCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/movies/MovieUpdater.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/movies/informationRetrievers/TmdbMovieRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/series/SeriesUpdateCollector.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/series/SeriesUpdater.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/series/informationRetrievers/TmdbEpisodeRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/series/informationRetrievers/TmdbSeriesRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/series/informationRetrievers/TvdbEpisodeRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/lib/updaters/series/informationRetrievers/TvdbSeriesRetriever.js", "downloaded_repos/robinp7720_Oblecto/src/models/episode.js", "downloaded_repos/robinp7720_Oblecto/src/models/episodeFiles.js", "downloaded_repos/robinp7720_Oblecto/src/models/file.js", "downloaded_repos/robinp7720_Oblecto/src/models/movie.js", "downloaded_repos/robinp7720_Oblecto/src/models/movieFiles.js", "downloaded_repos/robinp7720_Oblecto/src/models/movieSet.js", "downloaded_repos/robinp7720_Oblecto/src/models/series.js", "downloaded_repos/robinp7720_Oblecto/src/models/seriesSet.js", "downloaded_repos/robinp7720_Oblecto/src/models/stream.js", "downloaded_repos/robinp7720_Oblecto/src/models/trackEpisode.js", "downloaded_repos/robinp7720_Oblecto/src/models/trackMovie.js", "downloaded_repos/robinp7720_Oblecto/src/models/user.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/errors.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/index.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/middleware/auth.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/auth.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/clients.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/episodes.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/files.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/index.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/sets.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/index.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/maintenance.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/misc.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/remoteImport.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/settings/sources.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/streaming.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/tvshows.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/users.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/web.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/axiosTimeout.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/database.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/ffmpeg.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/ffprobe.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/guessit.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/logger/index.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/promiseTimeout.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/utils.js", "downloaded_repos/robinp7720_Oblecto/src/submodules/zeroconf.js", "downloaded_repos/robinp7720_Oblecto/tsconfig.json", "downloaded_repos/robinp7720_Oblecto/webpack.config.js"], "skipped": [{"path": "downloaded_repos/robinp7720_Oblecto/tests/mocha/Downloader.spec..js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/robinp7720_Oblecto/tests/mocha/FanartTVMovieArtworkRetriever.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/robinp7720_Oblecto/tests/mocha/SeriesIndexer.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/robinp7720_Oblecto/tests/mocha/TmdbMovieArtworkRetriever.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/robinp7720_Oblecto/tests/queue.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/robinp7720_Oblecto/tests/startup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/robinp7720_Oblecto/tests/startupTui.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6912751197814941, "profiling_times": {"config_time": 6.0869951248168945, "core_time": 4.259519338607788, "ignores_time": 0.002642393112182617, "total_time": 10.349891901016235}, "parsing_time": {"total_time": 2.4698305130004883, "per_file_time": {"mean": 0.014701372101193377, "std_dev": 0.001157891246328886}, "very_slow_stats": {"time_ratio": 0.15413119906711123, "count_ratio": 0.005952380952380952}, "very_slow_files": [{"fpath": "downloaded_repos/robinp7720_Oblecto/package-lock.json", "ftime": 0.3806779384613037}]}, "scanning_time": {"total_time": 13.967411041259766, "per_file_time": {"mean": 0.026254532032443177, "std_dev": 0.013350382421895584}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 5.272124528884888, "per_file_and_rule_time": {"mean": 0.007353032815739034, "std_dev": 0.0004300076593569622}, "very_slow_stats": {"time_ratio": 0.2587766331614773, "count_ratio": 0.012552301255230125}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/episodes.js", "rule_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "time": 0.10090994834899902}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/users/index.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.1061699390411377}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/users/index.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.13427209854125977}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "rule_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "time": 0.1397390365600586}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/items/index.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.140211820602417}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/episodes.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.15151500701904297}, {"fpath": "downloaded_repos/robinp7720_Oblecto/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.15341496467590332}, {"fpath": "downloaded_repos/robinp7720_Oblecto/package-lock.json", "rule_id": "json.aws.security.public-s3-policy-statement.public-s3-policy-statement", "time": 0.17958283424377441}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.2584869861602783}]}, "tainting_time": {"total_time": 1.7542579174041748, "per_def_and_rule_time": {"mean": 0.004247597862964105, "std_dev": 0.00011238392744002164}, "very_slow_stats": {"time_ratio": 0.2066851764996247, "count_ratio": 0.012106537530266344}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/users/index.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.05130314826965332}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/users/index.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.06746888160705566}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/users/index.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.0702509880065918}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/submodules/REST/routes/movies.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "time": 0.07224893569946289}, {"fpath": "downloaded_repos/robinp7720_Oblecto/src/lib/embyEmulation/ServerAPI/routes/items/index.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10130715370178223}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}