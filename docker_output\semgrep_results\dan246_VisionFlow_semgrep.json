{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/dan246_VisionFlow/API_ENHANCED.md", "start": {"line": 2227, "col": 20, "offset": 57851}, "end": {"line": 2227, "col": 25, "offset": 57856}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/dan246_VisionFlow/API_ENHANCED.md", "start": {"line": 2537, "col": 24, "offset": 67573}, "end": {"line": 2537, "col": 29, "offset": 67578}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "path": "downloaded_repos/dan246_VisionFlow/camera_ctrler/app.py", "start": {"line": 422, "col": 5, "offset": 16377}, "end": {"line": 422, "col": 39, "offset": 16411}, "extra": {"message": "Running flask app with host 0.0.0.0 could expose the server publicly.", "metadata": {"cwe": ["CWE-668: Exposure of Resource to Wrong Sphere"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "shortlink": "https://sg.run/eLby"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 8, "offset": 30}, "extra": {"message": "Service 'redis' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 8, "offset": 30}, "extra": {"message": "Service 'redis' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 13, "col": 3, "offset": 216}, "end": {"line": 13, "col": 11, "offset": 224}, "extra": {"message": "Service 'worker_1' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 13, "col": 3, "offset": 216}, "end": {"line": 13, "col": 11, "offset": 224}, "extra": {"message": "Service 'worker_1' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 39, "col": 3, "offset": 785}, "end": {"line": 39, "col": 11, "offset": 793}, "extra": {"message": "Service 'worker_2' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 39, "col": 3, "offset": 785}, "end": {"line": 39, "col": 11, "offset": 793}, "extra": {"message": "Service 'worker_2' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 65, "col": 3, "offset": 1354}, "end": {"line": 65, "col": 11, "offset": 1362}, "extra": {"message": "Service 'worker_3' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "start": {"line": 65, "col": 3, "offset": 1354}, "end": {"line": 65, "col": 11, "offset": 1362}, "extra": {"message": "Service 'worker_3' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 8, "offset": 30}, "extra": {"message": "Service 'redis' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 8, "offset": 30}, "extra": {"message": "Service 'redis' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 13, "col": 3, "offset": 216}, "end": {"line": 13, "col": 11, "offset": 224}, "extra": {"message": "Service 'worker_1' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 13, "col": 3, "offset": 216}, "end": {"line": 13, "col": 11, "offset": 224}, "extra": {"message": "Service 'worker_1' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 30, "col": 3, "offset": 570}, "end": {"line": 30, "col": 11, "offset": 578}, "extra": {"message": "Service 'worker_2' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 30, "col": 3, "offset": 570}, "end": {"line": 30, "col": 11, "offset": 578}, "extra": {"message": "Service 'worker_2' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 47, "col": 3, "offset": 924}, "end": {"line": 47, "col": 11, "offset": 932}, "extra": {"message": "Service 'worker_3' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "start": {"line": 47, "col": 3, "offset": 924}, "end": {"line": 47, "col": 11, "offset": 932}, "extra": {"message": "Service 'worker_3' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.optimized.yaml", "start": {"line": 5, "col": 3, "offset": 52}, "end": {"line": 5, "col": 5, "offset": 54}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.optimized.yaml", "start": {"line": 5, "col": 3, "offset": 52}, "end": {"line": 5, "col": 5, "offset": 54}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.optimized.yaml", "start": {"line": 29, "col": 3, "offset": 766}, "end": {"line": 29, "col": 8, "offset": 771}, "extra": {"message": "Service 'redis' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.optimized.yaml", "start": {"line": 29, "col": 3, "offset": 766}, "end": {"line": 29, "col": 8, "offset": 771}, "extra": {"message": "Service 'redis' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "start": {"line": 6, "col": 3, "offset": 44}, "end": {"line": 6, "col": 5, "offset": 46}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "start": {"line": 6, "col": 3, "offset": 44}, "end": {"line": 6, "col": 5, "offset": 46}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "start": {"line": 42, "col": 3, "offset": 789}, "end": {"line": 42, "col": 14, "offset": 800}, "extra": {"message": "Service 'camera_ctrl' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "start": {"line": 42, "col": 3, "offset": 789}, "end": {"line": 42, "col": 14, "offset": 800}, "extra": {"message": "Service 'camera_ctrl' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "start": {"line": 67, "col": 3, "offset": 1459}, "end": {"line": 67, "col": 20, "offset": 1476}, "extra": {"message": "Service 'objectrecognition' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "start": {"line": 67, "col": 3, "offset": 1459}, "end": {"line": 67, "col": 20, "offset": 1476}, "extra": {"message": "Service 'objectrecognition' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.privileged-service.privileged-service", "path": "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "start": {"line": 89, "col": 17, "offset": 2157}, "end": {"line": 89, "col": 21, "offset": 2161}, "extra": {"message": "Service 'objectrecognition' is running in privileged mode. This grants the container the equivalent of root capabilities on the host machine. This can lead to container escapes, privilege escalation, and other security concerns. Remove the 'privileged' key to disable this capability.", "fix": "false", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A06:2017 - Security Misconfiguration", "A05:2021 - Security Misconfiguration"], "references": ["https://www.trendmicro.com/en_us/research/19/l/why-running-a-privileged-container-in-docker-is-a-bad-idea.html", "https://containerjournal.com/topics/container-security/why-running-a-privileged-container-is-not-a-good-idea/"], "category": "security", "technology": ["docker-compose"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.privileged-service.privileged-service", "shortlink": "https://sg.run/AlX0"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/dan246_VisionFlow/object_recognition/YOLOModel.py", "start": {"line": 36, "col": 13, "offset": 1213}, "end": {"line": 36, "col": 54, "offset": 1254}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "path": "downloaded_repos/dan246_VisionFlow/web/app.py", "start": {"line": 143, "col": 5, "offset": 5175}, "end": {"line": 147, "col": 6, "offset": 5327}, "extra": {"message": "Running flask app with host 0.0.0.0 could expose the server publicly.", "metadata": {"cwe": ["CWE-668: Exposure of Resource to Wrong Sphere"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "shortlink": "https://sg.run/eLby"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/draw_area.html", "start": {"line": 7, "col": 3, "offset": 146}, "end": {"line": 7, "col": 106, "offset": 249}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/draw_area.html", "start": {"line": 33, "col": 3, "offset": 1043}, "end": {"line": 33, "col": 75, "offset": 1115}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/index.html", "start": {"line": 8, "col": 3, "offset": 212}, "end": {"line": 8, "col": 106, "offset": 315}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/index.html", "start": {"line": 138, "col": 3, "offset": 5692}, "end": {"line": 138, "col": 75, "offset": 5764}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/index.html", "start": {"line": 139, "col": 3, "offset": 5767}, "end": {"line": 139, "col": 95, "offset": 5859}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/index.html", "start": {"line": 140, "col": 3, "offset": 5862}, "end": {"line": 140, "col": 97, "offset": 5956}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/register.html", "start": {"line": 8, "col": 3, "offset": 208}, "end": {"line": 8, "col": 106, "offset": 311}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/register.html", "start": {"line": 46, "col": 3, "offset": 1774}, "end": {"line": 46, "col": 75, "offset": 1846}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/register.html", "start": {"line": 47, "col": 3, "offset": 1849}, "end": {"line": 47, "col": 95, "offset": 1941}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/dan246_VisionFlow/web/templates/register.html", "start": {"line": 48, "col": 3, "offset": 1944}, "end": {"line": 48, "col": 97, "offset": 2038}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Internal matching error", "rule_id": "yaml.github-actions.security.curl-eval.curl-eval", "message": "Internal matching error when running yaml.github-actions.security.curl-eval.curl-eval on downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:\n An error occurred while invoking the Semgrep engine. Please help us fix this by creating an issue at https://github.com/semgrep/semgrep\n\nrule yaml.github-actions.security.curl-eval.curl-eval: metavariable-pattern failed when parsing $SHELL's content as Bash: echo \"KaQHLJGPWG`{G{VHYaGDPF|fHy\\GqRG|{HhLGerHVX...\"\ndocker pull python:3.11-slim || \\\ndocker pull python:3.11-alpine || \\\necho \"JngGRK: IEbHtVKaQHLJGPWG`{G{VHYaGDPQ}MGqHG]iG|{HhLHZCJZVIQG\"\n", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml"}, {"code": 2, "level": "warn", "type": "Internal matching error", "rule_id": "yaml.github-actions.security.curl-eval.curl-eval", "message": "Internal matching error when running yaml.github-actions.security.curl-eval.curl-eval on downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml:\n An error occurred while invoking the Semgrep engine. Please help us fix this by creating an issue at https://github.com/semgrep/semgrep\n\nrule yaml.github-actions.security.curl-eval.curl-eval: metavariable-pattern failed when parsing $SHELL's content as Bash: echo \"R`OJ VisionFlow CI/CD HkcH`fKA[KBOQ}B\"\necho \"D]F G`{H]mHkcH`f: KA[KBO\"\necho \"D]F Docker HhLG|{: KA[KBO\"\necho \"R`TF HZCKWT: $(date)\"\necho \"R`M! GIGHUp: ${{ github.ref_name }}\"\necho \"R`T^ HPQF{e: ${{ github.sha }}\"\n", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml"}, {"code": 2, "level": "warn", "type": "Internal matching error", "rule_id": "yaml.github-actions.security.curl-eval.curl-eval", "message": "Internal matching error when running yaml.github-actions.security.curl-eval.curl-eval on downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml:\n An error occurred while invoking the Semgrep engine. Please help us fix this by creating an issue at https://github.com/semgrep/semgrep\n\nrule yaml.github-actions.security.curl-eval.curl-eval: metavariable-pattern failed when parsing $SHELL's content as Bash: echo \"R`UN G`xJbMIiLG}PIc}GTBJtkHkcH`f...\"\n\n# GoJJd^HkcH`fGxfGFx\npip install flake8 bandit pycodestyle\n\n# HkcH`f Python IiLG}PIc}GTBJtk\nfor service in web object_recognition camera_ctrler redisv1; do\n  if [ -d \"$service\" ]; then\n    echo \"HkcH`f $service H]NGLZ...\"\n    \n    # Flake8 Jk_HtVHkcH`f\n    flake8 $service --max-line-length=88 --ignore=E203,W503 --format=json > $service-flake8.json || true\n    \n    # Bandit GoJGFiHkcH`f\n    bandit -r $service -f json -o $service-bandit.json || true\n  fi\ndone\n", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml"}, {"code": 2, "level": "warn", "type": "Internal matching error", "rule_id": "yaml.github-actions.security.curl-eval.curl-eval", "message": "Internal matching error when running yaml.github-actions.security.curl-eval.curl-eval on downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:\n An error occurred while invoking the Semgrep engine. Please help us fix this by creating an issue at https://github.com/semgrep/semgrep\n\nrule yaml.github-actions.security.curl-eval.curl-eval: metavariable-pattern failed when parsing $SHELL's content as Bash: if [ \"${{ needs.deploy-production.result }}\" == \"success\" ]; then\n  echo \"D]F VisionFlow GxsHIQGK`KDiI~sGIqIU`IUcISqGcD\"\nelif [ \"${{ needs.deploy-staging.result }}\" == \"success\" ]; then\n  echo \"D]F VisionFlow GxsHIQGK`KDiI~sGIqHymJjgISqGcD\"\nelse\n  echo \"D^M VisionFlow KDiI~sGerHVX\"\nfi\n# KAZJdbGPpF|fHx|GKaGpgK[\\I[EKA[I`fKCPJ}pQ}I<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ail InJQ}J\n", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml"}, {"code": 2, "level": "warn", "type": "Internal matching error", "rule_id": "yaml.github-actions.security.curl-eval.curl-eval", "message": "Internal matching error when running yaml.github-actions.security.curl-eval.curl-eval on downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:\n An error occurred while invoking the Semgrep engine. Please help us fix this by creating an issue at https://github.com/semgrep/semgrep\n\nrule yaml.github-actions.security.curl-eval.curl-eval: metavariable-pattern failed when parsing $SHELL's content as Bash: python -m pip install --upgrade pip\npip install flake8 pytest pytest-cov black isort safety bandit\n\n# IJzHoKJZVIQG object_recognition H]NGLZ\nif [ \"${{ matrix.service }}\" == \"object_recognition\" ]; then\n  echo \"Installing from object_recognition/requirements_new.txt\"\n  if [ -f object_recognition/requirements_new.txt ]; then\n    pip install -r object_recognition/requirements_new.txt\n  else\n    echo \"requirements_new.txt not found, installing basic dependencies\"\n    pip install flask ultralytics opencv-python-headless numpy torch redis\n  fi\nelse\n  # GFwF|WH]NGLZI[EF ^JtuGoJJd^\n  if [ -f ${{ matrix.service }}/requirements.txt ]; then\n    echo \"Installing from ${{ matrix.service }}/requirements.txt\"\n    pip install -r ${{ matrix.service }}/requirements.txt\n  fi\n  \n  if [ -f ${{ matrix.service }}/requirements_new.txt ]; then\n    echo \"Installing from ${{ matrix.service }}/requirements_new.txt\"\n    pip install -r ${{ matrix.service }}/requirements_new.txt\n  fi\nfi\n\n# GgCH_]HsSH]J requirements HkUHbIQ}MGoJJd^G`{H]mF ^Jtu\nif [ ! -f ${{ matrix.service }}/requirements.txt ] && [ ! -f ${{ matrix.service }}/requirements_new.txt ] && [ \"${{ matrix.service }}\" != \"object_recognition\" ]; then\n  echo \"No requirements file found for ${{ matrix.service }}, installing basic dependencies\"\n  pip install flask requests redis\nfi\n", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 47, "col": 24, "offset": 1304}, "end": {"line": 57, "col": 3, "offset": 1634}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml:47:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `\"\nimport yaml, os, glob\nyaml_files = glob.glob('**/*.yml', recursive=True) + glob.glob('**/*.yaml', recursive=True)\nfor f in yaml_files[:5]:  # GPkHkcH`fGJN5GALHkUHbI\n    try:\n        with open(f, 'r') as file:\n            yaml.safe_load(file)\n        print(f'D]F {f}')\n    except Exception as e:\n        print(f'D^M {f}: {e}')\n\"\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 47, "col": 24, "offset": 1304}, "end": {"line": 57, "col": 3, "offset": 1634}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 86, "col": 13, "offset": 2624}, "end": {"line": 93, "col": 4, "offset": 3066}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml:86:\n When parsing a snippet as Ba<PERSON> for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `if [ -f \"${{ matrix.service.path }}/${{ matrix.service.dockerfile }}\" ]; then\n  echo \"HhLG|{ ${{ matrix.service.name }} HYaGDP...\"\n  docker build -t ${{ matrix.service.name }}:test ${{ matrix.service.path }} -f ${{ matrix.service.path }}/${{ matrix.service.dockerfile }}\n  echo \"D]F ${{ matrix.service.name }} HYaGDPHhLG|{HIQGK`\"\nelse\n  echo \"D^M Dockerfile FyNGnYG]i: ${{ matrix.service.path }}/${{ matrix.service.dockerfile }}\"\n  exit 1\nfi\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 86, "col": 13, "offset": 2624}, "end": {"line": 93, "col": 4, "offset": 3066}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 98, "col": 1, "offset": 3169}, "end": {"line": 105, "col": 4, "offset": 3561}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml:98:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `docker run --rm -d --name test-${{ matrix.service.name }} ${{ matrix.service.name }}:test sleep 30 || true\nsleep 5\nif docker ps | grep -q test-${{ matrix.service.name }}; then\n  echo \"D]F ${{ matrix.service.name }} GozGZiGV`GLVHIQGK`\"\n  docker stop test-${{ matrix.service.name }} || true\nelse\n  echo \"D[aQyP ${{ matrix.service.name }} GozGZiGPpJD~GV`GLVGerHVXQ}MF~GKAZGPpJD~HYpHndGyyI[E\"\nfi\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 98, "col": 1, "offset": 3169}, "end": {"line": 105, "col": 4, "offset": 3561}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 135, "col": 18, "offset": 4404}, "end": {"line": 139, "col": 39, "offset": 4596}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml:135:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `\"D^M VisionFlow CI/CD HkcH`fGerHVXQ}B\"\necho \"JlLHkcH`fFyKJ!qHnfKj`I[EKMpJkeJiKHBp\"\necho \"R`TF HZCKWT: $(date)\"\necho \"R`M! GIGHUp: ${{ github.ref_name }}\"\necho \"R`T^ HPQF{e: ${{ github.sha }}\"\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "start": {"line": 135, "col": 18, "offset": 4404}, "end": {"line": 139, "col": 39, "offset": 4596}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 79, "col": 24, "offset": 2766}, "end": {"line": 79, "col": 27, "offset": 2769}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 81, "col": 36, "offset": 2766}, "end": {"line": 81, "col": 52, "offset": 2782}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:79:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 79, "col": 24, "offset": 2766}, "end": {"line": 79, "col": 27, "offset": 2769}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 81, "col": 36, "offset": 2766}, "end": {"line": 81, "col": 52, "offset": 2782}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 87, "col": 29, "offset": 3045}, "end": {"line": 87, "col": 32, "offset": 3048}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 89, "col": 36, "offset": 3045}, "end": {"line": 89, "col": 52, "offset": 3061}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:87:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 87, "col": 29, "offset": 3045}, "end": {"line": 87, "col": 32, "offset": 3048}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 89, "col": 36, "offset": 3045}, "end": {"line": 89, "col": 52, "offset": 3061}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 95, "col": 10, "offset": 3330}, "end": {"line": 95, "col": 13, "offset": 3333}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 96, "col": 10, "offset": 3330}, "end": {"line": 96, "col": 13, "offset": 3333}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 98, "col": 36, "offset": 3330}, "end": {"line": 98, "col": 52, "offset": 3346}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:95:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 95, "col": 10, "offset": 3330}, "end": {"line": 95, "col": 13, "offset": 3333}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 96, "col": 10, "offset": 3330}, "end": {"line": 96, "col": 13, "offset": 3333}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 98, "col": 36, "offset": 3330}, "end": {"line": 98, "col": 52, "offset": 3346}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 104, "col": 1, "offset": 3776}, "end": {"line": 112, "col": 4, "offset": 4105}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:104:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `if [ \"${{ matrix.service }}\" == \"object_recognition\" ]; then\n  if [ -f object_recognition/requirements_new.txt ]; then\n    safety check -r object_recognition/requirements_new.txt || true\n  fi\nelse\n  if [ -f ${{ matrix.service }}/requirements.txt ]; then\n    safety check -r ${{ matrix.service }}/requirements.txt || true\n  fi\nfi\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 104, "col": 1, "offset": 3776}, "end": {"line": 112, "col": 4, "offset": 4105}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 123, "col": 13, "offset": 4488}, "end": {"line": 134, "col": 4, "offset": 4952}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:123:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `if [ -d ${{ matrix.service }}/tests ]; then\n  cd ${{ matrix.service }}\n  pytest tests/ -v --cov=./ --cov-report=xml --cov-report=html\nelif [ -f ${{ matrix.service }}/test_*.py ]; then\n  cd ${{ matrix.service }}\n  pytest test_*.py -v --cov=./ --cov-report=xml --cov-report=html\nelse\n  echo \"No tests found for ${{ matrix.service }}, creating dummy test\"\n  cd ${{ matrix.service }}\n  echo \"def test_dummy(): assert True\" > test_dummy.py\n  pytest test_dummy.py -v\nfi\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 123, "col": 13, "offset": 4488}, "end": {"line": 134, "col": 4, "offset": 4952}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 14, "offset": 10530}, "end": {"line": 320, "col": 28, "offset": 10544}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 34, "offset": 10530}, "end": {"line": 320, "col": 50, "offset": 10546}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 60, "offset": 10530}, "end": {"line": 320, "col": 77, "offset": 10547}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 83, "offset": 10530}, "end": {"line": 320, "col": 95, "offset": 10542}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 14, "offset": 10530}, "end": {"line": 337, "col": 28, "offset": 10544}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 34, "offset": 10530}, "end": {"line": 337, "col": 50, "offset": 10546}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 68, "offset": 10530}, "end": {"line": 337, "col": 85, "offset": 10547}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 91, "offset": 10530}, "end": {"line": 337, "col": 103, "offset": 10542}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 14, "offset": 10530}, "end": {"line": 342, "col": 28, "offset": 10544}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 34, "offset": 10530}, "end": {"line": 342, "col": 50, "offset": 10546}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 75, "offset": 10530}, "end": {"line": 342, "col": 92, "offset": 10547}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 98, "offset": 10530}, "end": {"line": 342, "col": 110, "offset": 10542}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:320:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ env.REGISTRY` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 14, "offset": 10530}, "end": {"line": 320, "col": 28, "offset": 10544}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 34, "offset": 10530}, "end": {"line": 320, "col": 50, "offset": 10546}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 60, "offset": 10530}, "end": {"line": 320, "col": 77, "offset": 10547}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 320, "col": 83, "offset": 10530}, "end": {"line": 320, "col": 95, "offset": 10542}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 14, "offset": 10530}, "end": {"line": 337, "col": 28, "offset": 10544}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 34, "offset": 10530}, "end": {"line": 337, "col": 50, "offset": 10546}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 68, "offset": 10530}, "end": {"line": 337, "col": 85, "offset": 10547}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 337, "col": 91, "offset": 10530}, "end": {"line": 337, "col": 103, "offset": 10542}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 14, "offset": 10530}, "end": {"line": 342, "col": 28, "offset": 10544}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 34, "offset": 10530}, "end": {"line": 342, "col": 50, "offset": 10546}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 75, "offset": 10530}, "end": {"line": 342, "col": 92, "offset": 10547}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 342, "col": 98, "offset": 10530}, "end": {"line": 342, "col": 110, "offset": 10542}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 350, "col": 6, "offset": 11728}, "end": {"line": 351, "col": 32, "offset": 11783}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:350:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `\"InJG FH]NGLZGV`GLV...\"\nsleep 60  # InJG FH]NGLZGV`GLV\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 350, "col": 6, "offset": 11728}, "end": {"line": 351, "col": 32, "offset": 11783}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 449, "col": 18, "offset": 14937}, "end": {"line": 453, "col": 42, "offset": 15112}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:449:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `\"H{WGCZKDiI~sGIqIU`IUcISqGcD...\"\necho \"GIGHUp: ${{ github.ref }}\"\necho \"HPQF{e: ${{ github.sha }}\"\necho \"F~]JAF: ${{ github.actor }}\"\necho \"JlLIc{JkNHJAH]JHymJjgKD~GxsKA[KBO\"\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 449, "col": 18, "offset": 14937}, "end": {"line": 453, "col": 42, "offset": 15112}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 457, "col": 18, "offset": 15205}, "end": {"line": 458, "col": 49, "offset": 15280}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml:457:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `\"KDiI~sGIqIU`IUcISqGcD...\"\n# KAZJdbGPpF|fHx|GKaGpgK[\\I[EIU`IUcKDiI~sJFtH]m\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "start": {"line": 457, "col": 18, "offset": 15205}, "end": {"line": 458, "col": 49, "offset": 15280}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 140, "col": 40, "offset": 4143}, "end": {"line": 140, "col": 54, "offset": 4157}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 140, "col": 60, "offset": 4143}, "end": {"line": 140, "col": 76, "offset": 4159}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 140, "col": 86, "offset": 4143}, "end": {"line": 140, "col": 121, "offset": 4178}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 145, "col": 1, "offset": 4143}, "end": {"line": 145, "col": 16, "offset": 4158}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 150, "col": 25, "offset": 4143}, "end": {"line": 150, "col": 28, "offset": 4146}}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 150, "col": 68, "offset": 4143}, "end": {"line": 150, "col": 71, "offset": 4146}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml:140:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ env.REGISTRY` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 140, "col": 40, "offset": 4143}, "end": {"line": 140, "col": 54, "offset": 4157}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 140, "col": 60, "offset": 4143}, "end": {"line": 140, "col": 76, "offset": 4159}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 140, "col": 86, "offset": 4143}, "end": {"line": 140, "col": 121, "offset": 4178}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 145, "col": 1, "offset": 4143}, "end": {"line": 145, "col": 16, "offset": 4158}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 150, "col": 25, "offset": 4143}, "end": {"line": 150, "col": 28, "offset": 4146}}, {"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "start": {"line": 150, "col": 68, "offset": 4143}, "end": {"line": 150, "col": 71, "offset": 4146}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml", "start": {"line": 32, "col": 13, "offset": 726}, "end": {"line": 34, "col": 4, "offset": 899}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml:32:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `if [ -f ${{ matrix.service }}/requirements.txt ]; then\n  safety check -r ${{ matrix.service }}/requirements.txt --json > ${{ matrix.service }}-safety-report.json || true\nfi\n` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml", "start": {"line": 32, "col": 13, "offset": 726}, "end": {"line": 34, "col": 4, "offset": 899}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile", "start": {"line": 5, "col": 28, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 51}}, {"path": "downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile", "start": {"line": 14, "col": 5, "offset": 0}, "end": {"line": 56, "col": 25, "offset": 1055}}]], "message": "Syntax error at line downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile:5:\n `=1 \\\r\n    PYTHONUNBUFFERED=1 \\\r\n    DEBIAN_FRONTEND` was unexpected", "path": "downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile", "spans": [{"file": "downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile", "start": {"line": 5, "col": 28, "offset": 0}, "end": {"line": 7, "col": 20, "offset": 51}}, {"file": "downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile", "start": {"line": 14, "col": 5, "offset": 0}, "end": {"line": 56, "col": 25, "offset": 1055}}]}], "paths": {"scanned": ["downloaded_repos/dan246_VisionFlow/.env.dev", "downloaded_repos/dan246_VisionFlow/.env.example", "downloaded_repos/dan246_VisionFlow/.env.prod", "downloaded_repos/dan246_VisionFlow/.gitattributes", "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "downloaded_repos/dan246_VisionFlow/.github/workflows/docker-image.yml", "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml", "downloaded_repos/dan246_VisionFlow/.gitignore", "downloaded_repos/dan246_VisionFlow/.pre-commit-config.yaml", "downloaded_repos/dan246_VisionFlow/.vscode/launch.json", "downloaded_repos/dan246_VisionFlow/.yamllint.yml", "downloaded_repos/dan246_VisionFlow/API_Doc.md", "downloaded_repos/dan246_VisionFlow/API_ENHANCED.md", "downloaded_repos/dan246_VisionFlow/CI-CD-COMPLETION-SUMMARY.md", "downloaded_repos/dan246_VisionFlow/DEPLOYMENT.md", "downloaded_repos/dan246_VisionFlow/LICENSE", "downloaded_repos/dan246_VisionFlow/Makefile", "downloaded_repos/dan246_VisionFlow/PROJECT_SUMMARY.md", "downloaded_repos/dan246_VisionFlow/README.md", "downloaded_repos/dan246_VisionFlow/README_CI_CD.md", "downloaded_repos/dan246_VisionFlow/README_en.md", "downloaded_repos/dan246_VisionFlow/camera_ctrler/app.py", "downloaded_repos/dan246_VisionFlow/camera_ctrler/camera_manager.py", "downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile", "downloaded_repos/dan246_VisionFlow/camera_ctrler/config.py", "downloaded_repos/dan246_VisionFlow/camera_ctrler/data.py", "downloaded_repos/dan246_VisionFlow/camera_ctrler/env.py", "downloaded_repos/dan246_VisionFlow/camera_ctrler/no_single.jpg", "downloaded_repos/dan246_VisionFlow/camera_ctrler/redis_utils.py", "downloaded_repos/dan246_VisionFlow/camera_ctrler/requirements.txt", "downloaded_repos/dan246_VisionFlow/camera_ctrler/static/css/style.css", "downloaded_repos/dan246_VisionFlow/camera_ctrler/static/js/main.js", "downloaded_repos/dan246_VisionFlow/camera_ctrler/templates/snapshot_ui.html", "downloaded_repos/dan246_VisionFlow/camera_ctrler/time_stamped_images.py", "downloaded_repos/dan246_VisionFlow/docker-compose-redis-gpu.yaml", "downloaded_repos/dan246_VisionFlow/docker-compose-redis.yaml", "downloaded_repos/dan246_VisionFlow/docker-compose.optimized.yaml", "downloaded_repos/dan246_VisionFlow/docker-compose.yaml", "downloaded_repos/dan246_VisionFlow/docs/CI-CD-GUIDE.md", "downloaded_repos/dan246_VisionFlow/docs/GITHUB-SETUP.md", "downloaded_repos/dan246_VisionFlow/docs/TROUBLESHOOTING.md", "downloaded_repos/dan246_VisionFlow/object_recognition/ApiService.py", "downloaded_repos/dan246_VisionFlow/object_recognition/YOLOModel.py", "downloaded_repos/dan246_VisionFlow/object_recognition/app.py", "downloaded_repos/dan246_VisionFlow/object_recognition/appv2.py", "downloaded_repos/dan246_VisionFlow/object_recognition/config/config.py", "downloaded_repos/dan246_VisionFlow/object_recognition/image_storage.py", "downloaded_repos/dan246_VisionFlow/object_recognition/logging_config.py", "downloaded_repos/dan246_VisionFlow/object_recognition/model_config.py", "downloaded_repos/dan246_VisionFlow/object_recognition/objectrecognitionDockerfile", "downloaded_repos/dan246_VisionFlow/object_recognition/requirements.txt", "downloaded_repos/dan246_VisionFlow/object_recognition/requirements_fixed.txt", "downloaded_repos/dan246_VisionFlow/object_recognition/requirements_new.txt", "downloaded_repos/dan246_VisionFlow/object_recognition/send_line.py", "downloaded_repos/dan246_VisionFlow/object_recognition/send_mail.py", "downloaded_repos/dan246_VisionFlow/readme_image/camera_management.PNG", "downloaded_repos/dan246_VisionFlow/readme_image/register.PNG", "downloaded_repos/dan246_VisionFlow/readme_image/stream_interface.PNG", "downloaded_repos/dan246_VisionFlow/redisv1/Dockerfile.optimized", "downloaded_repos/dan246_VisionFlow/redisv1/addw1.py", "downloaded_repos/dan246_VisionFlow/redisv1/app.py", "downloaded_repos/dan246_VisionFlow/redisv1/app_GPU.py", "downloaded_repos/dan246_VisionFlow/redisv1/config.py", "downloaded_repos/dan246_VisionFlow/redisv1/requirements.txt", "downloaded_repos/dan246_VisionFlow/redisv1/rtsptestDockerfile", "downloaded_repos/dan246_VisionFlow/scripts/deploy.sh", "downloaded_repos/dan246_VisionFlow/scripts/docker-build-test.sh", "downloaded_repos/dan246_VisionFlow/scripts/monitor.sh", "downloaded_repos/dan246_VisionFlow/scripts/setup-dev.sh", "downloaded_repos/dan246_VisionFlow/shared/logging_config.py", "downloaded_repos/dan246_VisionFlow/sonar-project.properties", "downloaded_repos/dan246_VisionFlow/web/Dockerfile", "downloaded_repos/dan246_VisionFlow/web/Dockerfile.alpine", "downloaded_repos/dan246_VisionFlow/web/__init__.py", "downloaded_repos/dan246_VisionFlow/web/app.py", "downloaded_repos/dan246_VisionFlow/web/config.py", "downloaded_repos/dan246_VisionFlow/web/extensions.py", "downloaded_repos/dan246_VisionFlow/web/manage.py", "downloaded_repos/dan246_VisionFlow/web/migrations/README", "downloaded_repos/dan246_VisionFlow/web/migrations/alembic.ini", "downloaded_repos/dan246_VisionFlow/web/migrations/env.py", "downloaded_repos/dan246_VisionFlow/web/migrations/script.py.mako", "downloaded_repos/dan246_VisionFlow/web/migrations/versions/af0b14c2e267_camera關聯user_id.py", "downloaded_repos/dan246_VisionFlow/web/migrations/versions/b2afa59b7467_.py", "downloaded_repos/dan246_VisionFlow/web/migrations/versions/d9b7f6e1cdb6_.py", "downloaded_repos/dan246_VisionFlow/web/models/__init__.py", "downloaded_repos/dan246_VisionFlow/web/models/camera.py", "downloaded_repos/dan246_VisionFlow/web/models/email_recipient.py", "downloaded_repos/dan246_VisionFlow/web/models/line_token.py", "downloaded_repos/dan246_VisionFlow/web/models/notification.py", "downloaded_repos/dan246_VisionFlow/web/models/user.py", "downloaded_repos/dan246_VisionFlow/web/requirements.txt", "downloaded_repos/dan246_VisionFlow/web/routes/__init__.py", "downloaded_repos/dan246_VisionFlow/web/routes/auth_routes.py", "downloaded_repos/dan246_VisionFlow/web/routes/camera_routes.py", "downloaded_repos/dan246_VisionFlow/web/routes/email_recipient_routes.py", "downloaded_repos/dan246_VisionFlow/web/routes/health_routes.py", "downloaded_repos/dan246_VisionFlow/web/routes/line_token_routes.py", "downloaded_repos/dan246_VisionFlow/web/routes/notification_routes.py", "downloaded_repos/dan246_VisionFlow/web/services/__init__.py", "downloaded_repos/dan246_VisionFlow/web/services/camera_service.py", "downloaded_repos/dan246_VisionFlow/web/services/line_service.py", "downloaded_repos/dan246_VisionFlow/web/services/mail_service.py", "downloaded_repos/dan246_VisionFlow/web/static/css/styles.css", "downloaded_repos/dan246_VisionFlow/web/static/images/login_backend.jpg", "downloaded_repos/dan246_VisionFlow/web/static/images/no_camera.gif", "downloaded_repos/dan246_VisionFlow/web/static/js/app.js", "downloaded_repos/dan246_VisionFlow/web/static/js/draw_area.js", "downloaded_repos/dan246_VisionFlow/web/static/js/register.js", "downloaded_repos/dan246_VisionFlow/web/templates/draw_area.html", "downloaded_repos/dan246_VisionFlow/web/templates/index.html", "downloaded_repos/dan246_VisionFlow/web/templates/register.html", "downloaded_repos/dan246_VisionFlow/web/update_db.txt"], "skipped": [{"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd-simple.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/ci-cd.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dan246_VisionFlow/.github/workflows/security-scan.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dan246_VisionFlow/AImodels/object_recognition/model/yolo11n.pt", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/dan246_VisionFlow/AImodels/object_recognition/tmp/yolo11n.pt", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/dan246_VisionFlow/camera_ctrler/cameractrlDockerfile", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dan246_VisionFlow/object_recognition/simsun.ttc", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/dan246_VisionFlow/object_recognition/yolo11n.pt", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/dan246_VisionFlow/readme_image/detection_area.PNG", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/dan246_VisionFlow/readme_image/login.PNG", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/dan246_VisionFlow/web/static/images/logo.PNG", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 2.022123098373413, "profiling_times": {"config_time": 6.57268762588501, "core_time": 4.485782146453857, "ignores_time": 0.0022954940795898438, "total_time": 11.062165021896362}, "parsing_time": {"total_time": 1.5880529880523682, "per_file_time": {"mean": 0.02089543405332064, "std_dev": 0.001926659720456493}, "very_slow_stats": {"time_ratio": 0.2111660546509814, "count_ratio": 0.013157894736842105}, "very_slow_files": [{"fpath": "downloaded_repos/dan246_VisionFlow/web/static/js/app.js", "ftime": 0.3353428840637207}]}, "scanning_time": {"total_time": 12.681528329849243, "per_file_time": {"mean": 0.04144290303872299, "std_dev": 0.016427998168845305}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 4.106412410736084, "per_file_and_rule_time": {"mean": 0.004714595190282532, "std_dev": 0.000175673176205917}, "very_slow_stats": {"time_ratio": 0.1493322918131474, "count_ratio": 0.004592422502870264}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/dan246_VisionFlow/camera_ctrler/app.py", "rule_id": "python.flask.security.injection.raw-html-concat.raw-html-format", "time": 0.1318988800048828}, {"fpath": "downloaded_repos/dan246_VisionFlow/redisv1/app_GPU.py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.15268611907958984}, {"fpath": "downloaded_repos/dan246_VisionFlow/web/static/js/app.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.15406513214111328}, {"fpath": "downloaded_repos/dan246_VisionFlow/camera_ctrler/camera_manager.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.17456984519958496}]}, "tainting_time": {"total_time": 2.2263004779815674, "per_def_and_rule_time": {"mean": 0.001984225024939008, "std_dev": 9.61719356804616e-05}, "very_slow_stats": {"time_ratio": 0.27220642677312734, "count_ratio": 0.0035650623885918}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/dan246_VisionFlow/object_recognition/appv2.py", "fline": 311, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.11179995536804199}, {"fpath": "downloaded_repos/dan246_VisionFlow/redisv1/app.py", "fline": 56, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.11784219741821289}, {"fpath": "downloaded_repos/dan246_VisionFlow/camera_ctrler/app.py", "fline": 142, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.1704111099243164}, {"fpath": "downloaded_repos/dan246_VisionFlow/redisv1/app_GPU.py", "fline": 55, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20596003532409668}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}