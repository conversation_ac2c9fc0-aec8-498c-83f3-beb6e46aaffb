{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/PrismarineJS_prismarine-auth/src/MicrosoftAuthFlow.js", "start": {"line": 64, "col": 48, "offset": 2479}, "end": {"line": 64, "col": 57, "offset": 2488}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/PrismarineJS_prismarine-auth/src/MicrosoftAuthFlow.js", "start": {"line": 64, "col": 59, "offset": 2490}, "end": {"line": 64, "col": 93, "offset": 2524}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/PrismarineJS_prismarine-auth/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/PrismarineJS_prismarine-auth/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/PrismarineJS_prismarine-auth/.github/ISSUE_TEMPLATE/question.md", "downloaded_repos/PrismarineJS_prismarine-auth/.github/dependabot.yml", "downloaded_repos/PrismarineJS_prismarine-auth/.github/workflows/ci.yml", "downloaded_repos/PrismarineJS_prismarine-auth/.github/workflows/commands.yml", "downloaded_repos/PrismarineJS_prismarine-auth/.github/workflows/publish.yml", "downloaded_repos/PrismarineJS_prismarine-auth/.gitignore", "downloaded_repos/PrismarineJS_prismarine-auth/.npmrc", "downloaded_repos/PrismarineJS_prismarine-auth/HISTORY.md", "downloaded_repos/PrismarineJS_prismarine-auth/LICENSE", "downloaded_repos/PrismarineJS_prismarine-auth/README.md", "downloaded_repos/PrismarineJS_prismarine-auth/docs/API.md", "downloaded_repos/PrismarineJS_prismarine-auth/examples/bedrock/deviceCode.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/bedrock/password.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/mcpc/deviceCode.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/mcpc/password.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/playfab/deviceCode.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/services/deviceCode.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/xbox/azure.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/xbox/basic.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/xbox/simpleMsal.js", "downloaded_repos/PrismarineJS_prismarine-auth/examples/xbox/sisu.js", "downloaded_repos/PrismarineJS_prismarine-auth/index.d.ts", "downloaded_repos/PrismarineJS_prismarine-auth/index.js", "downloaded_repos/PrismarineJS_prismarine-auth/package.json", "downloaded_repos/PrismarineJS_prismarine-auth/src/MicrosoftAuthFlow.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/TokenManagers/LiveTokenManager.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/TokenManagers/MinecraftBedrockServicesManager.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/TokenManagers/MinecraftBedrockTokenManager.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/TokenManagers/MinecraftJavaTokenManager.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/TokenManagers/MsaTokenManager.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/TokenManagers/PlayfabTokenManager.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/TokenManagers/XboxTokenManager.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/common/Constants.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/common/Titles.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/common/Util.js", "downloaded_repos/PrismarineJS_prismarine-auth/src/common/cache/FileCache.js"], "skipped": [{"path": "downloaded_repos/PrismarineJS_prismarine-auth/test/FileCache.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_prismarine-auth/test/devicecode.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_prismarine-auth/test/manualCodeTest.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_prismarine-auth/test/password.test.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6618058681488037, "profiling_times": {"config_time": 5.647060871124268, "core_time": 2.6495683193206787, "ignores_time": 0.0016160011291503906, "total_time": 8.299056768417358}, "parsing_time": {"total_time": 0.7286834716796875, "per_file_time": {"mean": 0.025127016264816813, "std_dev": 0.0005443342104888646}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.7928531169891357, "per_file_time": {"mean": 0.026598601114182246, "std_dev": 0.003983536924492025}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.957355260848999, "per_file_and_rule_time": {"mean": 0.0036821356186499967, "std_dev": 7.867756822227231e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.21723270416259766, "per_def_and_rule_time": {"mean": 0.00042846687211557725, "std_dev": 3.798235520120467e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}