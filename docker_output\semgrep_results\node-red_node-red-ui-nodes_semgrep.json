{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 1391, "col": 18, "offset": 25733}, "end": {"line": 1391, "col": 115, "offset": 25830}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 1992, "col": 6, "offset": 38274}, "end": {"line": 1992, "col": 30, "offset": 38298}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 1997, "col": 4, "offset": 38320}, "end": {"line": 1997, "col": 25, "offset": 38341}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 2032, "col": 4, "offset": 39006}, "end": {"line": 2032, "col": 35, "offset": 39037}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 2075, "col": 5, "offset": 39630}, "end": {"line": 2075, "col": 36, "offset": 39661}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 6332, "col": 4, "offset": 125416}, "end": {"line": 6332, "col": 46, "offset": 125458}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 6368, "col": 5, "offset": 126128}, "end": {"line": 6368, "col": 34, "offset": 126157}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 6805, "col": 7, "offset": 134041}, "end": {"line": 6805, "col": 65, "offset": 134099}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 7599, "col": 4, "offset": 150448}, "end": {"line": 7599, "col": 41, "offset": 150485}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 10291, "col": 5, "offset": 202477}, "end": {"line": 10291, "col": 70, "offset": 202542}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 10305, "col": 5, "offset": 203024}, "end": {"line": 10305, "col": 68, "offset": 203087}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 10561, "col": 5, "offset": 209362}, "end": {"line": 10561, "col": 85, "offset": 209442}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 10579, "col": 4, "offset": 209879}, "end": {"line": 10579, "col": 82, "offset": 209957}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 11772, "col": 5, "offset": 239874}, "end": {"line": 11772, "col": 50, "offset": 239919}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 11802, "col": 6, "offset": 240909}, "end": {"line": 11802, "col": 40, "offset": 240943}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 11930, "col": 6, "offset": 244369}, "end": {"line": 11930, "col": 31, "offset": 244394}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 11975, "col": 4, "offset": 245610}, "end": {"line": 11975, "col": 38, "offset": 245644}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 12231, "col": 6, "offset": 252039}, "end": {"line": 12231, "col": 56, "offset": 252089}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 12242, "col": 5, "offset": 252364}, "end": {"line": 12242, "col": 57, "offset": 252416}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 12256, "col": 5, "offset": 252903}, "end": {"line": 12256, "col": 55, "offset": 252953}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 14171, "col": 8, "offset": 300074}, "end": {"line": 14171, "col": 65, "offset": 300131}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 14176, "col": 8, "offset": 300281}, "end": {"line": 14176, "col": 65, "offset": 300338}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 14529, "col": 7, "offset": 309083}, "end": {"line": 14529, "col": 33, "offset": 309109}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 16098, "col": 4, "offset": 351493}, "end": {"line": 16098, "col": 63, "offset": 351552}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 17471, "col": 4, "offset": 391720}, "end": {"line": 17471, "col": 50, "offset": 391766}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 18369, "col": 5, "offset": 414185}, "end": {"line": 18369, "col": 55, "offset": 414235}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 18468, "col": 6, "offset": 417423}, "end": {"line": 18468, "col": 33, "offset": 417450}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 18532, "col": 8, "offset": 419236}, "end": {"line": 18532, "col": 33, "offset": 419261}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20034, "col": 5, "offset": 459827}, "end": {"line": 20034, "col": 29, "offset": 459851}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20061, "col": 4, "offset": 460538}, "end": {"line": 20061, "col": 36, "offset": 460570}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20070, "col": 4, "offset": 460840}, "end": {"line": 20070, "col": 35, "offset": 460871}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20079, "col": 4, "offset": 461138}, "end": {"line": 20079, "col": 35, "offset": 461169}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20088, "col": 4, "offset": 461436}, "end": {"line": 20088, "col": 35, "offset": 461467}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20123, "col": 5, "offset": 462447}, "end": {"line": 20123, "col": 39, "offset": 462481}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20855, "col": 5, "offset": 481123}, "end": {"line": 20855, "col": 40, "offset": 481158}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 20871, "col": 5, "offset": 481597}, "end": {"line": 20871, "col": 40, "offset": 481632}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "start": {"line": 21668, "col": 3, "offset": 502769}, "end": {"line": 21668, "col": 33, "offset": 502799}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/locales/en-US/ui_vega.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 16, "col": 10, "offset": 1049}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/locales/ja/ui_vega.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 15, "col": 10, "offset": 982}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js:\n ", "path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js"}], "paths": {"scanned": ["downloaded_repos/node-red_node-red-ui-nodes/.github/ISSUE_TEMPLATE.md", "downloaded_repos/node-red_node-red-ui-nodes/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/node-red_node-red-ui-nodes/.jscsrc", "downloaded_repos/node-red_node-red-ui-nodes/.jshintrc", "downloaded_repos/node-red_node-red-ui-nodes/CONTRIBUTING.md", "downloaded_repos/node-red_node-red-ui-nodes/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/README.md", "downloaded_repos/node-red_node-red-ui-nodes/docs/api.md", "downloaded_repos/node-red_node-red-ui-nodes/images/readme.txt", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/examples/01 - Embed specified URL.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/examples/02 - Embed URL in message.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/examples/03 - Control using Web Messaging API.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/examples/04 - Scale embedded web page.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/figs/sample00.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/icons/icon.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/locales/en-US/ui_iframe.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/locales/en-US/ui_iframe.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/locales/ja/ui_iframe.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/locales/ja/ui_iframe.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/ui_iframe.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-iframe/ui_iframe.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-lineargauge/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-lineargauge/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-lineargauge/imgs/linearGauges.PNG", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-lineargauge/imgs/linearGaugesGIF.gif", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-lineargauge/linear_gauge.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-lineargauge/linear_gauge.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-lineargauge/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/examples/example.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/figs/sample00.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/icons/icon.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/locales/en-US/ui_list.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/locales/en-US/ui_list.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/locales/ja/ui_list.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/locales/ja/ui_list.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/ui_list.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-list/ui_list.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/examples/01 - record and play audio.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/examples/02 - recognize and play speech.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/lib/recorderWorker.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/locales/en-US/ui_microphone.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/locales/en-US/ui_microphone.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/locales/ja/ui_microphone.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/locales/ja/ui_microphone.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/ui_microphone.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-microphone/ui_microphone.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-mylittleuinode/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-mylittleuinode/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-mylittleuinode/mylittleuinode.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-mylittleuinode/mylittleuinode.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-mylittleuinode/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/examples/1 Simple table.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/examples/2 Richer table.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/examples/3 ui_control table.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/examples/4 sending commands.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/examples/5 copy paste.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/examples/6 ui_control interactive.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/examples/7 quotationtest.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/css/tabulator.min.css", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/css/tabulator.min.css.map", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/css/tabulator_midnight.min.css", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/locales/en-US/node.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/locales/en-US/node.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/node.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/node.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/screenshot.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/screenshot2.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/ui-table-custom.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/CHANGELOG.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega - Example Gallery.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega - Radial Tree Layout.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega - Word Cloud.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega-Lite - Example Gallery.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega-Lite - Isotype Bar Chart.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega-Lite - Line and Bart Chart.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega-Lite - Map.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/examples/Vega-Lite - Simple Bar Chart.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/figs/vega-example.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/icons/icon.png", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/locales/en-US/ui_vega.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/locales/en-US/ui_vega.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/locales/ja/ui_vega.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/locales/ja/ui_vega.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/ui_vega.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-vega/ui_vega.js", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-webcam/LICENSE", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-webcam/README.md", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-webcam/package.json", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-webcam/ui_webcam.html", "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-webcam/ui_webcam.js"], "skipped": [{"path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6837210655212402, "profiling_times": {"config_time": 6.1778318881988525, "core_time": 39.86625385284424, "ignores_time": 0.1156015396118164, "total_time": 46.16058135032654}, "parsing_time": {"total_time": 0.790569543838501, "per_file_time": {"mean": 0.01581139087677002, "std_dev": 0.0007607636077484357}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 41.30175733566284, "per_file_time": {"mean": 0.14856747243044185, "std_dev": 4.812559260286688}, "very_slow_stats": {"time_ratio": 0.8870134168993573, "count_ratio": 0.0035971223021582736}, "very_slow_files": [{"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "ftime": 36.635212898254395}]}, "matching_time": {"total_time": 12.795997381210327, "per_file_and_rule_time": {"mean": 0.05037794244570996, "std_dev": 0.04549252358417151}, "very_slow_stats": {"time_ratio": 0.8741993127931474, "count_ratio": 0.07874015748031496}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.4388449192047119}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.4797079563140869}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.5850179195404053}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.5971858501434326}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.9696788787841797}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 1.005707025527954}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.045875072479248}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.097208023071289}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 1.7947380542755127}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.8360729217529297}]}, "tainting_time": {"total_time": 17.805909633636475, "per_def_and_rule_time": {"mean": 0.010504961435773731, "std_dev": 0.017733767801702886}, "very_slow_stats": {"time_ratio": 0.894676056384044, "count_ratio": 0.010619469026548672}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.3295421600341797}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "time": 0.3346860408782959}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.34153008460998535}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.394711971282959}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.7965719699859619}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.89255690574646}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 2.2681150436401367}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 2.4384701251983643}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 2.5021770000457764}, {"fpath": "downloaded_repos/node-red_node-red-ui-nodes/node-red-node-ui-table/lib/js/tabulator.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 3.1962978839874268}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}