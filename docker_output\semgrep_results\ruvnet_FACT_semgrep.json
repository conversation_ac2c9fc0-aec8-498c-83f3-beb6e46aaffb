{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 5, "col": 3, "offset": 82}, "end": {"line": 5, "col": 11, "offset": 90}, "extra": {"message": "Service 'fact-app' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 5, "col": 3, "offset": 82}, "end": {"line": 5, "col": 11, "offset": 90}, "extra": {"message": "Service 'fact-app' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 60, "col": 3, "offset": 1441}, "end": {"line": 60, "col": 13, "offset": 1451}, "extra": {"message": "Service 'fact-redis' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 60, "col": 3, "offset": 1441}, "end": {"line": 60, "col": 13, "offset": 1451}, "extra": {"message": "Service 'fact-redis' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 91, "col": 3, "offset": 2137}, "end": {"line": 91, "col": 16, "offset": 2150}, "extra": {"message": "Service 'fact-postgres' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 91, "col": 3, "offset": 2137}, "end": {"line": 91, "col": 16, "offset": 2150}, "extra": {"message": "Service 'fact-postgres' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 124, "col": 3, "offset": 2982}, "end": {"line": 124, "col": 13, "offset": 2992}, "extra": {"message": "Service 'fact-nginx' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 124, "col": 3, "offset": 2982}, "end": {"line": 124, "col": 13, "offset": 2992}, "extra": {"message": "Service 'fact-nginx' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 153, "col": 3, "offset": 3573}, "end": {"line": 153, "col": 18, "offset": 3588}, "extra": {"message": "Service 'fact-monitoring' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 153, "col": 3, "offset": 3573}, "end": {"line": 153, "col": 18, "offset": 3588}, "extra": {"message": "Service 'fact-monitoring' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 177, "col": 3, "offset": 4231}, "end": {"line": 177, "col": 15, "offset": 4243}, "extra": {"message": "Service 'fact-grafana' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "start": {"line": 177, "col": 3, "offset": 4231}, "end": {"line": 177, "col": 15, "offset": 4243}, "extra": {"message": "Service 'fact-grafana' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 36, "col": 3, "offset": 833}, "end": {"line": 36, "col": 13, "offset": 843}, "extra": {"message": "Service 'fact-redis' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 36, "col": 3, "offset": 833}, "end": {"line": 36, "col": 13, "offset": 843}, "extra": {"message": "Service 'fact-redis' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 54, "col": 3, "offset": 1269}, "end": {"line": 54, "col": 16, "offset": 1282}, "extra": {"message": "Service 'fact-postgres' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 54, "col": 3, "offset": 1269}, "end": {"line": 54, "col": 16, "offset": 1282}, "extra": {"message": "Service 'fact-postgres' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 76, "col": 3, "offset": 1907}, "end": {"line": 76, "col": 18, "offset": 1922}, "extra": {"message": "Service 'fact-monitoring' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 76, "col": 3, "offset": 1907}, "end": {"line": 76, "col": 18, "offset": 1922}, "extra": {"message": "Service 'fact-monitoring' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 95, "col": 3, "offset": 2531}, "end": {"line": 95, "col": 15, "offset": 2543}, "extra": {"message": "Service 'fact-grafana' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "start": {"line": 95, "col": 3, "offset": 2531}, "end": {"line": 95, "col": 15, "offset": 2543}, "extra": {"message": "Service 'fact-grafana' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/ruvnet_FACT/deployment/kubernetes/secrets.yaml", "start": {"line": 17, "col": 13, "offset": 468}, "end": {"line": 17, "col": 58, "offset": 513}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/ruvnet_FACT/deployment/kubernetes/secrets.yaml", "start": {"line": 18, "col": 10, "offset": 553}, "end": {"line": 18, "col": 51, "offset": 594}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/ruvnet_FACT/deployment/kubernetes/secrets.yaml", "start": {"line": 21, "col": 16, "offset": 664}, "end": {"line": 21, "col": 63, "offset": 711}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/ruvnet_FACT/deployment/kubernetes/secrets.yaml", "start": {"line": 32, "col": 11, "offset": 1283}, "end": {"line": 32, "col": 51, "offset": 1323}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.exec-detected.exec-detected", "path": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/08_advanced_tools/advanced_tool_usage.py", "start": {"line": 420, "col": 13, "offset": 15634}, "end": {"line": 420, "col": 57, "offset": 15678}, "extra": {"message": "Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.exec-detected.exec-detected", "shortlink": "https://sg.run/ndRX"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/verify_setup.py", "start": {"line": 63, "col": 17, "offset": 1873}, "end": {"line": 63, "col": 53, "offset": 1909}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/verify_setup.py", "start": {"line": 113, "col": 17, "offset": 3792}, "end": {"line": 113, "col": 53, "offset": 3828}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/ruvnet_FACT/src/db/connection.py", "start": {"line": 370, "col": 17, "offset": 16372}, "end": {"line": 370, "col": 64, "offset": 16419}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/ruvnet_FACT/src/db/connection.py", "start": {"line": 370, "col": 17, "offset": 16372}, "end": {"line": 370, "col": 64, "offset": 16419}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/ruvnet_FACT/src/db/connection.py", "start": {"line": 520, "col": 36, "offset": 22101}, "end": {"line": 520, "col": 88, "offset": 22153}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/ruvnet_FACT/src/db/connection.py", "start": {"line": 520, "col": 36, "offset": 22101}, "end": {"line": 520, "col": 88, "offset": 22153}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "start": {"line": 281, "col": 30, "offset": 7540}, "end": {"line": 281, "col": 61, "offset": 7571}}]], "message": "Syntax error at line downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml:281:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ needs.build.outputs.image-tag` was unexpected", "path": "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "start": {"line": 281, "col": 30, "offset": 7540}, "end": {"line": 281, "col": 61, "offset": 7571}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "start": {"line": 328, "col": 30, "offset": 9175}, "end": {"line": 328, "col": 61, "offset": 9206}}]], "message": "Syntax error at line downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml:328:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ needs.build.outputs.image-tag` was unexpected", "path": "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "spans": [{"file": "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "start": {"line": 328, "col": 30, "offset": 9175}, "end": {"line": 328, "col": 61, "offset": 9206}}]}], "paths": {"scanned": ["downloaded_repos/ruvnet_FACT/.env.example", "downloaded_repos/ruvnet_FACT/.env.template", "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "downloaded_repos/ruvnet_FACT/.gitignore", "downloaded_repos/ruvnet_FACT/.roo/README.md", "downloaded_repos/ruvnet_FACT/.roo/mcp-list.txt", "downloaded_repos/ruvnet_FACT/.roo/mcp.json", "downloaded_repos/ruvnet_FACT/.roo/mcp.md", "downloaded_repos/ruvnet_FACT/.roo/rules/apply_diff_guidelines.md", "downloaded_repos/ruvnet_FACT/.roo/rules/file_operations_guidelines.md", "downloaded_repos/ruvnet_FACT/.roo/rules/insert_content.md", "downloaded_repos/ruvnet_FACT/.roo/rules/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules/search_replace.md", "downloaded_repos/ruvnet_FACT/.roo/rules/tool_guidelines_index.md", "downloaded_repos/ruvnet_FACT/.roo/rules-architect/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-ask/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-code/apply_diff_guidelines.md", "downloaded_repos/ruvnet_FACT/.roo/rules-code/code_editing.md", "downloaded_repos/ruvnet_FACT/.roo/rules-code/file_operations_guidelines.md", "downloaded_repos/ruvnet_FACT/.roo/rules-code/insert_content.md", "downloaded_repos/ruvnet_FACT/.roo/rules-code/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-code/search_replace.md", "downloaded_repos/ruvnet_FACT/.roo/rules-code/tool_guidelines_index.md", "downloaded_repos/ruvnet_FACT/.roo/rules-debug/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-devops/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-docs-writer/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-integration/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-mcp/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-post-deployment-monitoring-mode/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-refinement-optimization-mode/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-security-review/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-sparc/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-spec-pseudocode/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-supabase-admin/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-tdd/rules.md", "downloaded_repos/ruvnet_FACT/.roo/rules-tutorial/rules.md", "downloaded_repos/ruvnet_FACT/.roomodes", "downloaded_repos/ruvnet_FACT/LICENSE", "downloaded_repos/ruvnet_FACT/Makefile", "downloaded_repos/ruvnet_FACT/README.md", "downloaded_repos/ruvnet_FACT/architecture/architecture-overview.md", "downloaded_repos/ruvnet_FACT/architecture/architecture-review.md", "downloaded_repos/ruvnet_FACT/architecture/component-diagram.md", "downloaded_repos/ruvnet_FACT/architecture/component-gap-analysis.md", "downloaded_repos/ruvnet_FACT/architecture/data-flow.md", "downloaded_repos/ruvnet_FACT/architecture/executive-summary.md", "downloaded_repos/ruvnet_FACT/architecture/folder-structure.md", "downloaded_repos/ruvnet_FACT/architecture/implementation-guidelines.md", "downloaded_repos/ruvnet_FACT/architecture/implementation-priorities.md", "downloaded_repos/ruvnet_FACT/architecture/implementation-status-report.md", "downloaded_repos/ruvnet_FACT/architecture/integration-action-plan.md", "downloaded_repos/ruvnet_FACT/architecture/integration-assessment-report.md", "downloaded_repos/ruvnet_FACT/architecture/scalability-architecture-assessment.md", "downloaded_repos/ruvnet_FACT/architecture/security-architecture-assessment.md", "downloaded_repos/ruvnet_FACT/data/fact_demo.db", "downloaded_repos/ruvnet_FACT/db/test_fact.db", "downloaded_repos/ruvnet_FACT/db/test_final.db", "downloaded_repos/ruvnet_FACT/db/test_sql_fixes.db", "downloaded_repos/ruvnet_FACT/db/test_validation.db", "downloaded_repos/ruvnet_FACT/deployment/DEPLOYMENT_GUIDE.md", "downloaded_repos/ruvnet_FACT/deployment/docker/Dockerfile", "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.prod.yml", "downloaded_repos/ruvnet_FACT/deployment/docker/docker-compose.yml", "downloaded_repos/ruvnet_FACT/deployment/kubernetes/configmap.yaml", "downloaded_repos/ruvnet_FACT/deployment/kubernetes/namespace.yaml", "downloaded_repos/ruvnet_FACT/deployment/kubernetes/secrets.yaml", "downloaded_repos/ruvnet_FACT/deployment/monitoring/alerts/fact-alerts.yml", "downloaded_repos/ruvnet_FACT/deployment/monitoring/prometheus.yml", "downloaded_repos/ruvnet_FACT/docs/10_benchmarking_performance_guide.md", "downloaded_repos/ruvnet_FACT/docs/11_troubleshooting_configuration_guide.md", "downloaded_repos/ruvnet_FACT/docs/12_system_architecture_components.md", "downloaded_repos/ruvnet_FACT/docs/1_overview_project.md", "downloaded_repos/ruvnet_FACT/docs/2_installation_setup.md", "downloaded_repos/ruvnet_FACT/docs/3_core_concepts.md", "downloaded_repos/ruvnet_FACT/docs/4_user_guide.md", "downloaded_repos/ruvnet_FACT/docs/5_api_reference.md", "downloaded_repos/ruvnet_FACT/docs/6_tool_creation_guide.md", "downloaded_repos/ruvnet_FACT/docs/7_advanced_usage.md", "downloaded_repos/ruvnet_FACT/docs/8_troubleshooting_guide.md", "downloaded_repos/ruvnet_FACT/docs/9_complete_setup_guide.md", "downloaded_repos/ruvnet_FACT/docs/README.md", "downloaded_repos/ruvnet_FACT/docs/anthropic_api_debugging_resolution.md", "downloaded_repos/ruvnet_FACT/docs/api-specification.md", "downloaded_repos/ruvnet_FACT/docs/arcade-dev/architecture-overview.md", "downloaded_repos/ruvnet_FACT/docs/arcade-dev/implementation-strategy.md", "downloaded_repos/ruvnet_FACT/docs/arcade-dev/project-summary.md", "downloaded_repos/ruvnet_FACT/docs/architecture.md", "downloaded_repos/ruvnet_FACT/docs/benchmarking-guide.md", "downloaded_repos/ruvnet_FACT/docs/blog/blog-post-introducing-fact.md", "downloaded_repos/ruvnet_FACT/docs/blog/blog_post_fact_system.md", "downloaded_repos/ruvnet_FACT/docs/blog/complete_blog_post_fact_system.md", "downloaded_repos/ruvnet_FACT/docs/cache-implementation.md", "downloaded_repos/ruvnet_FACT/docs/cache_integration_implementation.md", "downloaded_repos/ruvnet_FACT/docs/cache_resilience_completion_report.md", "downloaded_repos/ruvnet_FACT/docs/cache_resilience_guide.md", "downloaded_repos/ruvnet_FACT/docs/cache_resilience_implementation.md", "downloaded_repos/ruvnet_FACT/docs/complete_integration_guide.md", "downloaded_repos/ruvnet_FACT/docs/configuration-requirements-analysis.md", "downloaded_repos/ruvnet_FACT/docs/configuration-requirements-specification.md", "downloaded_repos/ruvnet_FACT/docs/configuration-validation-pseudocode.md", "downloaded_repos/ruvnet_FACT/docs/debugging_report_database_fixes.md", "downloaded_repos/ruvnet_FACT/docs/domain-model.md", "downloaded_repos/ruvnet_FACT/docs/environment-configuration-guide.md", "downloaded_repos/ruvnet_FACT/docs/fact_algorithm_analysis.md", "downloaded_repos/ruvnet_FACT/docs/fact_algorithm_performance_analysis.md", "downloaded_repos/ruvnet_FACT/docs/fact_benchmark_analysis.md", "downloaded_repos/ruvnet_FACT/docs/implementation-roadmap.md", "downloaded_repos/ruvnet_FACT/docs/libraries/README.md", "downloaded_repos/ruvnet_FACT/docs/libraries/core-libraries.md", "downloaded_repos/ruvnet_FACT/docs/libraries/security-libraries.md", "downloaded_repos/ruvnet_FACT/docs/optimization-strategies.md", "downloaded_repos/ruvnet_FACT/docs/performance_optimization_guide.md", "downloaded_repos/ruvnet_FACT/docs/pseudocode-core.md", "downloaded_repos/ruvnet_FACT/docs/pseudocode-tools.md", "downloaded_repos/ruvnet_FACT/docs/requirements.md", "downloaded_repos/ruvnet_FACT/docs/response_padding_guide.md", "downloaded_repos/ruvnet_FACT/docs/security-assessment.md", "downloaded_repos/ruvnet_FACT/docs/security-guidelines.md", "downloaded_repos/ruvnet_FACT/docs/security-remediation-plan.md", "downloaded_repos/ruvnet_FACT/docs/setup_completion_report.md", "downloaded_repos/ruvnet_FACT/docs/testing-framework-implementation.md", "downloaded_repos/ruvnet_FACT/docs/testing-strategy.md", "downloaded_repos/ruvnet_FACT/docs/tool-execution-framework.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/.env.example", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/.gitignore", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/01_basic_integration/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/01_basic_integration/README.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/01_basic_integration/basic_arcade_client.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/02_tool_registration/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/02_tool_registration/register_fact_tools.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/03_intelligent_routing/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/03_intelligent_routing/hybrid_execution.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/04_error_handling/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/04_error_handling/resilient_execution.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/README.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/cached_arcade_client.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/cached_arcade_client_enhanced.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/cached_arcade_client_fixed.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/06_security/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/06_security/README.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/06_security/secure_tool_execution.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/07_cache_integration/cached_arcade_client.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/08_advanced_tools/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/08_advanced_tools/advanced_tool_usage.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/09_testing/arcade_integration_tests.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/10_deployment/production_deployment.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/12_monitoring/CHANGES.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/12_monitoring/README.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/12_monitoring/arcade_monitoring.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/README.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/config/global.yaml", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/interactive_demo.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/requirements.txt", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/run_all_examples.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/tutorial.md", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/utils/__init__.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/utils/import_helper.py", "downloaded_repos/ruvnet_FACT/examples/arcade-dev/verify_setup.py", "downloaded_repos/ruvnet_FACT/examples/tool_execution_demo.py", "downloaded_repos/ruvnet_FACT/fact-memory/README.md", "downloaded_repos/ruvnet_FACT/fact-memory/api-specs/mcp-compatibility.md", "downloaded_repos/ruvnet_FACT/fact-memory/architecture/system-design.md", "downloaded_repos/ruvnet_FACT/fact-memory/docs/implementation-plan.md", "downloaded_repos/ruvnet_FACT/fact-memory/docs/mcp-components.md", "downloaded_repos/ruvnet_FACT/fact-memory/docs/research-findings.md", "downloaded_repos/ruvnet_FACT/fact-memory/examples/README.md", "downloaded_repos/ruvnet_FACT/fact-memory/examples/basic_usage.py", "downloaded_repos/ruvnet_FACT/fact-memory/examples/mcp_client.py", "downloaded_repos/ruvnet_FACT/fact-memory/examples/performance_comparison.py", "downloaded_repos/ruvnet_FACT/fact-memory/src/IMPLEMENTATION_SUMMARY.md", "downloaded_repos/ruvnet_FACT/fact-memory/src/INTEGRATION_PATTERNS.md", "downloaded_repos/ruvnet_FACT/fact-memory/src/README.md", "downloaded_repos/ruvnet_FACT/fact-memory/src/VALIDATION_REPORT.md", "downloaded_repos/ruvnet_FACT/fact-memory/src/hello_mcp_server.py", "downloaded_repos/ruvnet_FACT/fact-memory/src/package.json", "downloaded_repos/ruvnet_FACT/fact-memory/src/requirements.txt", "downloaded_repos/ruvnet_FACT/fact-memory/src/test_client.py", "downloaded_repos/ruvnet_FACT/fact-memory/src/test_mcp_integration.py", "downloaded_repos/ruvnet_FACT/fact-memory/src/tsconfig.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153445/charts/performance_overview_20250523_153445.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153445/raw_data/raw_results_20250523_153445.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153445/reports/benchmark_report_20250523_153445.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153445/reports/benchmark_summary_20250523_153445.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153454/charts/latency_comparison_20250523_153454.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153454/charts/performance_overview_20250523_153454.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153454/raw_data/raw_results_20250523_153454.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153454/reports/benchmark_report_20250523_153454.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153454/reports/benchmark_summary_20250523_153454.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153708/charts/latency_comparison_20250523_153708.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153708/charts/performance_overview_20250523_153708.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153708/raw_data/raw_results_20250523_153708.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153708/reports/benchmark_report_20250523_153708.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250523_153708/reports/benchmark_summary_20250523_153708.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_163757/raw_data/raw_results_20250524_163810.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_163757/reports/benchmark_report_20250524_163810.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_163757/reports/benchmark_summary_20250524_163810.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164318/raw_data/raw_results_20250524_164330.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164318/reports/benchmark_report_20250524_164330.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164318/reports/benchmark_summary_20250524_164330.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164727/raw_data/raw_results_20250524_164823.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164727/reports/benchmark_report_20250524_164823.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164727/reports/benchmark_summary_20250524_164823.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164954/charts/cache_performance_20250524_165006.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164954/raw_data/raw_results_20250524_165006.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164954/reports/benchmark_report_20250524_165006.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_164954/reports/benchmark_summary_20250524_165006.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165136/charts/cache_performance_20250524_165142.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165136/raw_data/raw_results_20250524_165142.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165136/reports/benchmark_report_20250524_165142.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165136/reports/benchmark_summary_20250524_165142.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165219/charts/cache_performance_20250524_165223.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165219/raw_data/raw_results_20250524_165223.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165219/reports/benchmark_report_20250524_165223.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165219/reports/benchmark_summary_20250524_165223.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165253/charts/cache_performance_20250524_165254.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165253/raw_data/raw_results_20250524_165254.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165253/reports/benchmark_report_20250524_165254.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165253/reports/benchmark_summary_20250524_165254.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165319/charts/cache_performance_20250524_165323.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165319/raw_data/raw_results_20250524_165323.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165319/reports/benchmark_report_20250524_165323.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165319/reports/benchmark_summary_20250524_165323.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165357/charts/cache_performance_20250524_165400.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165357/raw_data/raw_results_20250524_165400.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165357/reports/benchmark_report_20250524_165400.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_165357/reports/benchmark_summary_20250524_165400.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_170709/charts/cache_performance_20250524_170945.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_170709/raw_data/raw_results_20250524_170945.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_170709/reports/benchmark_report_20250524_170945.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_170709/reports/benchmark_summary_20250524_170945.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_171850/charts/cache_performance_20250524_172122.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_171850/raw_data/raw_results_20250524_172122.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_171850/reports/benchmark_report_20250524_172122.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_171850/reports/benchmark_summary_20250524_172122.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172259/charts/cache_performance_20250524_172531.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172259/raw_data/raw_results_20250524_172531.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172259/reports/benchmark_report_20250524_172531.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172259/reports/benchmark_summary_20250524_172531.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172623/charts/cache_performance_20250524_172855.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172623/raw_data/raw_results_20250524_172855.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172623/reports/benchmark_report_20250524_172855.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_172623/reports/benchmark_summary_20250524_172855.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173107/charts/cache_performance_20250524_173343.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173107/raw_data/raw_results_20250524_173343.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173107/reports/benchmark_report_20250524_173343.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173107/reports/benchmark_summary_20250524_173343.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173436/charts/cache_performance_20250524_173714.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173436/raw_data/raw_results_20250524_173714.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173436/reports/benchmark_report_20250524_173714.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173436/reports/benchmark_summary_20250524_173714.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173803/charts/cache_performance_20250524_174039.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173803/raw_data/raw_results_20250524_174039.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173803/reports/benchmark_report_20250524_174039.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_173803/reports/benchmark_summary_20250524_174039.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174112/charts/cache_performance_20250524_174347.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174112/raw_data/raw_results_20250524_174347.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174112/reports/benchmark_report_20250524_174347.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174112/reports/benchmark_summary_20250524_174347.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174511/charts/cache_performance_20250524_174540.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174511/raw_data/raw_results_20250524_174540.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174511/reports/benchmark_report_20250524_174540.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174511/reports/benchmark_summary_20250524_174540.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174947/charts/cache_performance_20250524_175001.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174947/raw_data/raw_results_20250524_175001.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174947/reports/benchmark_report_20250524_175001.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_174947/reports/benchmark_summary_20250524_175001.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203034/charts/cache_performance_20250524_203102.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203034/raw_data/raw_results_20250524_203102.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203034/reports/benchmark_report_20250524_203102.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203034/reports/benchmark_summary_20250524_203102.txt", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203532/charts/cache_performance_20250524_203556.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203532/raw_data/raw_results_20250524_203556.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203532/reports/benchmark_report_20250524_203556.json", "downloaded_repos/ruvnet_FACT/logs/benchmark_20250524_203532/reports/benchmark_summary_20250524_203556.txt", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164007/reports/demo_benchmark_report_20250524_164007.json", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164007/reports/demo_benchmark_summary_20250524_164007.txt", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164049/reports/demo_benchmark_report_20250524_164049.json", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164049/reports/demo_benchmark_summary_20250524_164049.txt", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164102/reports/demo_benchmark_report_20250524_164102.json", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164102/reports/demo_benchmark_summary_20250524_164102.txt", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164128/reports/demo_benchmark_report_20250524_164128.json", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164128/reports/demo_benchmark_summary_20250524_164128.txt", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164209/reports/demo_benchmark_report_20250524_164209.json", "downloaded_repos/ruvnet_FACT/logs/demo_benchmark_20250524_164209/reports/demo_benchmark_summary_20250524_164209.txt", "downloaded_repos/ruvnet_FACT/logs/optimized_benchmark_20250524_183049/raw_data/benchmark_data_20250524_183049.json", "downloaded_repos/ruvnet_FACT/logs/optimized_benchmark_20250524_183049/reports/optimized_benchmark_report_20250524_183049.json", "downloaded_repos/ruvnet_FACT/logs/optimized_benchmark_20250524_183049/reports/optimized_benchmark_summary_20250524_183049.txt", "downloaded_repos/ruvnet_FACT/main.py", "downloaded_repos/ruvnet_FACT/plans/arcade-dev.md", "downloaded_repos/ruvnet_FACT/plans/critical-components-pseudocode.md", "downloaded_repos/ruvnet_FACT/plans/fact-system-comprehensive-implementation-plan.md", "downloaded_repos/ruvnet_FACT/plans/readme.txt", "downloaded_repos/ruvnet_FACT/plans/testing-strategy-and-validation.md", "downloaded_repos/ruvnet_FACT/pytest.ini", "downloaded_repos/ruvnet_FACT/requirements-security.txt", "downloaded_repos/ruvnet_FACT/requirements-test.txt", "downloaded_repos/ruvnet_FACT/requirements.txt", "downloaded_repos/ruvnet_FACT/scripts/BENCHMARK_RUNNER_GUIDE.md", "downloaded_repos/ruvnet_FACT/scripts/README.md", "downloaded_repos/ruvnet_FACT/scripts/README_BENCHMARKS.md", "downloaded_repos/ruvnet_FACT/scripts/benchmark", "downloaded_repos/ruvnet_FACT/scripts/demo_cache_resilience.py", "downloaded_repos/ruvnet_FACT/scripts/demo_lifecycle.py", "downloaded_repos/ruvnet_FACT/scripts/deploy.sh", "downloaded_repos/ruvnet_FACT/scripts/fix_imports.py", "downloaded_repos/ruvnet_FACT/scripts/fix_imports_comprehensive.py", "downloaded_repos/ruvnet_FACT/scripts/init_environment.py", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_161627/charts/performance_overview_20250524_161627.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_161627/raw_data/raw_results_20250524_161627.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_161627/reports/benchmark_report_20250524_161627.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_161627/reports/benchmark_summary_20250524_161627.txt", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_164238/raw_data/raw_results_20250524_164238.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_164238/reports/benchmark_report_20250524_164238.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_164238/reports/benchmark_summary_20250524_164238.txt", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_165406/charts/cache_performance_20250524_165406.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_165406/raw_data/raw_results_20250524_165406.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_165406/reports/benchmark_report_20250524_165406.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_165406/reports/benchmark_summary_20250524_165406.txt", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_170947/charts/cache_performance_20250524_170947.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_170947/raw_data/raw_results_20250524_170947.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_170947/reports/benchmark_report_20250524_170947.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_170947/reports/benchmark_summary_20250524_170947.txt", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175422/charts/cache_performance_20250524_175422.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175422/raw_data/raw_results_20250524_175422.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175422/reports/benchmark_report_20250524_175422.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175422/reports/benchmark_summary_20250524_175422.txt", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175805/charts/performance_overview_20250524_175805.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175805/raw_data/raw_results_20250524_175805.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175805/reports/benchmark_report_20250524_175805.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_175805/reports/benchmark_summary_20250524_175805.txt", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_181918/charts/cache_performance_20250524_181918.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_181918/raw_data/raw_results_20250524_181918.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_181918/reports/benchmark_report_20250524_181918.json", "downloaded_repos/ruvnet_FACT/scripts/logs/benchmark_20250524_181918/reports/benchmark_summary_20250524_181918.txt", "downloaded_repos/ruvnet_FACT/scripts/run_benchmarks.py", "downloaded_repos/ruvnet_FACT/scripts/run_benchmarks_demo.py", "downloaded_repos/ruvnet_FACT/scripts/run_benchmarks_standalone.py", "downloaded_repos/ruvnet_FACT/scripts/run_optimized_benchmarks.py", "downloaded_repos/ruvnet_FACT/scripts/setup.py", "downloaded_repos/ruvnet_FACT/scripts/setup_env.py", "downloaded_repos/ruvnet_FACT/scripts/test_benchmark_runner.py", "downloaded_repos/ruvnet_FACT/scripts/test_cache_fix.py", "downloaded_repos/ruvnet_FACT/scripts/test_cache_resilience.py", "downloaded_repos/ruvnet_FACT/scripts/test_fact_cache_integration.py", "downloaded_repos/ruvnet_FACT/scripts/test_response_padding.py", "downloaded_repos/ruvnet_FACT/scripts/validate_complete_system.py", "downloaded_repos/ruvnet_FACT/scripts/validate_env.py", "downloaded_repos/ruvnet_FACT/src/__init__.py", "downloaded_repos/ruvnet_FACT/src/arcade/__init__.py", "downloaded_repos/ruvnet_FACT/src/arcade/client.py", "downloaded_repos/ruvnet_FACT/src/arcade/errors.py", "downloaded_repos/ruvnet_FACT/src/arcade/gateway.py", "downloaded_repos/ruvnet_FACT/src/benchmarking/__init__.py", "downloaded_repos/ruvnet_FACT/src/benchmarking/comparisons.py", "downloaded_repos/ruvnet_FACT/src/benchmarking/framework.py", "downloaded_repos/ruvnet_FACT/src/benchmarking/monitoring.py", "downloaded_repos/ruvnet_FACT/src/benchmarking/profiler.py", "downloaded_repos/ruvnet_FACT/src/benchmarking/visualization.py", "downloaded_repos/ruvnet_FACT/src/cache/__init__.py", "downloaded_repos/ruvnet_FACT/src/cache/manager.py", "downloaded_repos/ruvnet_FACT/src/cache/metrics.py", "downloaded_repos/ruvnet_FACT/src/cache/strategy.py", "downloaded_repos/ruvnet_FACT/src/cache/validation.py", "downloaded_repos/ruvnet_FACT/src/cache/warming.py", "downloaded_repos/ruvnet_FACT/src/core/__init__.py", "downloaded_repos/ruvnet_FACT/src/core/agentic_flow.py", "downloaded_repos/ruvnet_FACT/src/core/cli.py", "downloaded_repos/ruvnet_FACT/src/core/config.py", "downloaded_repos/ruvnet_FACT/src/core/conversation.py", "downloaded_repos/ruvnet_FACT/src/core/driver.py", "downloaded_repos/ruvnet_FACT/src/core/errors.py", "downloaded_repos/ruvnet_FACT/src/db/__init__.py", "downloaded_repos/ruvnet_FACT/src/db/connection.py", "downloaded_repos/ruvnet_FACT/src/db/models.py", "downloaded_repos/ruvnet_FACT/src/monitoring/__init__.py", "downloaded_repos/ruvnet_FACT/src/monitoring/metrics.py", "downloaded_repos/ruvnet_FACT/src/monitoring/performance_optimizer.py", "downloaded_repos/ruvnet_FACT/src/security/__init__.py", "downloaded_repos/ruvnet_FACT/src/security/auth.py", "downloaded_repos/ruvnet_FACT/src/security/cache_encryption.py", "downloaded_repos/ruvnet_FACT/src/security/config.py", "downloaded_repos/ruvnet_FACT/src/security/error_handler.py", "downloaded_repos/ruvnet_FACT/src/security/input_sanitizer.py", "downloaded_repos/ruvnet_FACT/src/security/token_manager.py", "downloaded_repos/ruvnet_FACT/src/tools/__init__.py", "downloaded_repos/ruvnet_FACT/src/tools/connectors/__init__.py", "downloaded_repos/ruvnet_FACT/src/tools/connectors/file.py", "downloaded_repos/ruvnet_FACT/src/tools/connectors/http.py", "downloaded_repos/ruvnet_FACT/src/tools/connectors/sql.py", "downloaded_repos/ruvnet_FACT/src/tools/decorators.py", "downloaded_repos/ruvnet_FACT/src/tools/executor.py", "downloaded_repos/ruvnet_FACT/src/tools/validation.py"], "skipped": [{"path": "downloaded_repos/ruvnet_FACT/.github/workflows/ci-cd.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ruvnet_FACT/tests/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/TESTING_ASSESSMENT_REPORT.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/db/test_validation.db", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/debug_sql_validation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/integration/test_cache_resilience_e2e.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/integration/test_cache_resilience_e2e_sync.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/integration/test_complete_system.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/integration/test_system_integration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/performance/test_benchmarks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_all_fixes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_basic_functionality.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_execution_guide.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_fixes_summary.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_imports.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_nonetype_bug.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_query_error.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_revenue_trends.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_runner.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_simple.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_sql_fixes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/test_sql_fixes_validation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_cache_manager_integration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_cache_mechanism.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_cache_security.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_cache_validation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_database_operations.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_environment_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_missing_components_tdd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_tool_executor.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ruvnet_FACT/tests/unit/test_tool_framework.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.4646739959716797, "profiling_times": {"config_time": 7.168652296066284, "core_time": 9.402224063873291, "ignores_time": 0.002112150192260742, "total_time": 16.5739905834198}, "parsing_time": {"total_time": 3.4374918937683105, "per_file_time": {"mean": 0.017017286602813417, "std_dev": 0.0006752747693270279}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 71.05696964263916, "per_file_time": {"mean": 0.07192000975975628, "std_dev": 0.1217985951376518}, "very_slow_stats": {"time_ratio": 0.5906028097442078, "count_ratio": 0.018218623481781375}, "very_slow_files": [{"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/06_security/secure_tool_execution.py", "ftime": 1.9599308967590332}, {"fpath": "downloaded_repos/ruvnet_FACT/src/cache/manager.py", "ftime": 2.1551830768585205}, {"fpath": "downloaded_repos/ruvnet_FACT/src/benchmarking/monitoring.py", "ftime": 2.170469045639038}, {"fpath": "downloaded_repos/ruvnet_FACT/scripts/run_benchmarks_standalone.py", "ftime": 2.429229974746704}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/cached_arcade_client_enhanced.py", "ftime": 2.6078760623931885}, {"fpath": "downloaded_repos/ruvnet_FACT/src/core/driver.py", "ftime": 2.6348888874053955}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/07_cache_integration/cached_arcade_client.py", "ftime": 2.7892940044403076}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/10_deployment/production_deployment.py", "ftime": 3.776197910308838}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/cached_arcade_client.py", "ftime": 3.877584934234619}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/09_testing/arcade_integration_tests.py", "ftime": 4.081058979034424}]}, "matching_time": {"total_time": 46.74569487571716, "per_file_and_rule_time": {"mean": 0.02157161738611775, "std_dev": 0.010051305112216698}, "very_slow_stats": {"time_ratio": 0.5640385767716432, "count_ratio": 0.03461005999077065}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/ruvnet_FACT/src/tools/connectors/http.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.8699178695678711}, {"fpath": "downloaded_repos/ruvnet_FACT/scripts/run_benchmarks_demo.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.9215519428253174}, {"fpath": "downloaded_repos/ruvnet_FACT/scripts/validate_env.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.9645929336547852}, {"fpath": "downloaded_repos/ruvnet_FACT/fact-memory/examples/performance_comparison.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.197882890701294}, {"fpath": "downloaded_repos/ruvnet_FACT/fact-memory/examples/mcp_client.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.3344409465789795}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/09_testing/arcade_integration_tests.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.4289259910583496}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/cached_arcade_client_enhanced.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.4654779434204102}, {"fpath": "downloaded_repos/ruvnet_FACT/scripts/run_benchmarks_standalone.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.5553150177001953}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/05_cache_integration/cached_arcade_client.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.5709149837493896}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/07_cache_integration/cached_arcade_client.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.6123449802398682}]}, "tainting_time": {"total_time": 11.16594934463501, "per_def_and_rule_time": {"mean": 0.0015272807200977989, "std_dev": 3.820957901579461e-05}, "very_slow_stats": {"time_ratio": 0.14791416106450214, "count_ratio": 0.002051702913418137}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/ruvnet_FACT/fact-memory/examples/basic_usage.py", "fline": 14, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.06624293327331543}, {"fpath": "downloaded_repos/ruvnet_FACT/scripts/demo_cache_resilience.py", "fline": 222, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.07756686210632324}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/02_tool_registration/register_fact_tools.py", "fline": 584, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.08753514289855957}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/09_testing/arcade_integration_tests.py", "fline": 642, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.09133791923522949}, {"fpath": "downloaded_repos/ruvnet_FACT/src/core/driver.py", "fline": 115, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.*****************}, {"fpath": "downloaded_repos/ruvnet_FACT/scripts/run_optimized_benchmarks.py", "fline": 57, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.****************}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/04_error_handling/resilient_execution.py", "fline": 490, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.*****************}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/03_intelligent_routing/hybrid_execution.py", "fline": 733, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.***************}, {"fpath": "downloaded_repos/ruvnet_FACT/examples/arcade-dev/01_basic_integration/basic_arcade_client.py", "fline": 360, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.****************}, {"fpath": "downloaded_repos/ruvnet_FACT/scripts/fix_imports_comprehensive.py", "fline": 14, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.****************}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": **********}, "engine_requested": "OSS", "skipped_rules": []}