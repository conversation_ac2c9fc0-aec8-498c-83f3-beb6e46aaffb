{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 37, "col": 38, "offset": 886}, "end": {"line": 37, "col": 57, "offset": 905}}, {"path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 24, "offset": 886}, "end": {"line": 38, "col": 43, "offset": 905}}]], "message": "Syntax error at line downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml:37:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 37, "col": 38, "offset": 886}, "end": {"line": 37, "col": 57, "offset": 905}}, {"file": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 24, "offset": 886}, "end": {"line": 38, "col": 43, "offset": 905}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 53, "offset": 1073}, "end": {"line": 42, "col": 69, "offset": 1089}}, {"path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 97, "offset": 1073}, "end": {"line": 42, "col": 115, "offset": 1091}}, {"path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 19, "offset": 1073}, "end": {"line": 43, "col": 22, "offset": 1076}}]], "message": "Syntax error at line downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml:42:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 53, "offset": 1073}, "end": {"line": 42, "col": 69, "offset": 1089}}, {"file": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 97, "offset": 1073}, "end": {"line": 42, "col": 115, "offset": 1091}}, {"file": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 19, "offset": 1073}, "end": {"line": 43, "col": 22, "offset": 1076}}]}], "paths": {"scanned": ["downloaded_repos/lara-zeus_sky/.editorconfig", "downloaded_repos/lara-zeus_sky/.gitattributes", "downloaded_repos/lara-zeus_sky/.github/CONTRIBUTING.md", "downloaded_repos/lara-zeus_sky/.github/FUNDING.yml", "downloaded_repos/lara-zeus_sky/.github/ISSUE_TEMPLATE/bug.yml", "downloaded_repos/lara-zeus_sky/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/lara-zeus_sky/.github/SECURITY.md", "downloaded_repos/lara-zeus_sky/.github/dependabot.yml", "downloaded_repos/lara-zeus_sky/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/lara-zeus_sky/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/lara-zeus_sky/.github/workflows/phpstan.yml", "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "downloaded_repos/lara-zeus_sky/.github/workflows/update-changelog.yml", "downloaded_repos/lara-zeus_sky/.gitignore", "downloaded_repos/lara-zeus_sky/.prettierrc", "downloaded_repos/lara-zeus_sky/CHANGELOG.md", "downloaded_repos/lara-zeus_sky/CODE_OF_CONDUCT.md", "downloaded_repos/lara-zeus_sky/LICENSE.md", "downloaded_repos/lara-zeus_sky/README.md", "downloaded_repos/lara-zeus_sky/composer.json", "downloaded_repos/lara-zeus_sky/composer.lock", "downloaded_repos/lara-zeus_sky/config/zeus-sky.php", "downloaded_repos/lara-zeus_sky/database/factories/LibraryFactory.php", "downloaded_repos/lara-zeus_sky/database/factories/PostFactory.php", "downloaded_repos/lara-zeus_sky/database/migrations/create_faqs_table.php.stub", "downloaded_repos/lara-zeus_sky/database/migrations/create_library_table.php.stub", "downloaded_repos/lara-zeus_sky/database/migrations/create_navigations_table.php.stub", "downloaded_repos/lara-zeus_sky/database/migrations/create_posts_table.php.stub", "downloaded_repos/lara-zeus_sky/database/migrations/modify_posts_columns.php.stub", "downloaded_repos/lara-zeus_sky/database/seeders/SkySeeder.php", "downloaded_repos/lara-zeus_sky/docs/_index.md", "downloaded_repos/lara-zeus_sky/docs/advanced/_index.md", "downloaded_repos/lara-zeus_sky/docs/advanced/addons.md", "downloaded_repos/lara-zeus_sky/docs/advanced/customization.md", "downloaded_repos/lara-zeus_sky/docs/advanced/models.md", "downloaded_repos/lara-zeus_sky/docs/filament.md", "downloaded_repos/lara-zeus_sky/docs/getting-started/_index.md", "downloaded_repos/lara-zeus_sky/docs/getting-started/assets.md", "downloaded_repos/lara-zeus_sky/docs/getting-started/changelog.md", "downloaded_repos/lara-zeus_sky/docs/getting-started/configuration.md", "downloaded_repos/lara-zeus_sky/docs/getting-started/editor.md", "downloaded_repos/lara-zeus_sky/docs/getting-started/installation.md", "downloaded_repos/lara-zeus_sky/docs/getting-started/upgrade.md", "downloaded_repos/lara-zeus_sky/docs/introduction.md", "downloaded_repos/lara-zeus_sky/phpstan-baseline.neon", "downloaded_repos/lara-zeus_sky/phpstan.neon.dist", "downloaded_repos/lara-zeus_sky/phpunit.xml.dist", "downloaded_repos/lara-zeus_sky/pint.json", "downloaded_repos/lara-zeus_sky/resources/lang/ar/filament-navigation.php", "downloaded_repos/lara-zeus_sky/resources/lang/ar.json", "downloaded_repos/lara-zeus_sky/resources/lang/ckb.json", "downloaded_repos/lara-zeus_sky/resources/lang/en/filament-navigation.php", "downloaded_repos/lara-zeus_sky/resources/lang/en.json", "downloaded_repos/lara-zeus_sky/resources/lang/fr.json", "downloaded_repos/lara-zeus_sky/resources/lang/tr.json", "downloaded_repos/lara-zeus_sky/resources/views/components/nav-item.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/filament/card-divider.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/filament/columns/page-title.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/filament/columns/post-title.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/filament/columns/status-desc.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/filament/hidden-action.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/filament/navigation-builder.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/faq.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-item.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-tag.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-types/file-url.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-types/file.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-types/image-url.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-types/image.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-types/video-url.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library-types/video.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/addons/library.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/category.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/home.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/page.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/category.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/children-pages.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/empty.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/password-form.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/post.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/related.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/sidebar/authors.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/sidebar/categories.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/sidebar/pages.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/sidebar/recent.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/sidebar/search.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/sidebar.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/sticky.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/partial/tag.blade.php", "downloaded_repos/lara-zeus_sky/resources/views/themes/zeus/sky/post.blade.php", "downloaded_repos/lara-zeus_sky/routes/web.php", "downloaded_repos/lara-zeus_sky/src/Classes/BoltParser.php", "downloaded_repos/lara-zeus_sky/src/Classes/ContentEditor.php", "downloaded_repos/lara-zeus_sky/src/Classes/RenderNavItem.php", "downloaded_repos/lara-zeus_sky/src/Concerns/CanManipulateFiles.php", "downloaded_repos/lara-zeus_sky/src/Configuration.php", "downloaded_repos/lara-zeus_sky/src/Console/InstallCommand.php", "downloaded_repos/lara-zeus_sky/src/Console/PublishCommand.php", "downloaded_repos/lara-zeus_sky/src/Console/ZeusEditorCommand.php", "downloaded_repos/lara-zeus_sky/src/Console/migrateCommand.php", "downloaded_repos/lara-zeus_sky/src/Editors/MarkdownEditor.php", "downloaded_repos/lara-zeus_sky/src/Editors/RichEditor.php", "downloaded_repos/lara-zeus_sky/src/Editors/TinyEditor.php", "downloaded_repos/lara-zeus_sky/src/Editors/TipTapEditor.php", "downloaded_repos/lara-zeus_sky/src/Filament/Fields/NavigationSelect.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/FaqResource/Pages/CreateFaq.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/FaqResource/Pages/EditFaq.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/FaqResource/Pages/ListFaqs.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/FaqResource.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/LibraryResource/Pages/CreateLibrary.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/LibraryResource/Pages/EditLibrary.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/LibraryResource/Pages/ListLibrary.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/LibraryResource.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/NavigationResource/Pages/Concerns/HandlesNavigationBuilder.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/NavigationResource/Pages/CreateNavigation.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/NavigationResource/Pages/EditNavigation.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/NavigationResource/Pages/ListNavigations.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/NavigationResource.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PageResource/Pages/CreatePage.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PageResource/Pages/EditPage.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PageResource/Pages/ListPage.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PageResource.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PostResource/Pages/CreatePost.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PostResource/Pages/EditPost.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PostResource/Pages/ListPosts.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/PostResource.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/SkyResource.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/TagResource/Pages/CreateTag.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/TagResource/Pages/EditTag.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/TagResource/Pages/ListTags.php", "downloaded_repos/lara-zeus_sky/src/Filament/Resources/TagResource.php", "downloaded_repos/lara-zeus_sky/src/Livewire/Faq.php", "downloaded_repos/lara-zeus_sky/src/Livewire/Library.php", "downloaded_repos/lara-zeus_sky/src/Livewire/LibraryItem.php", "downloaded_repos/lara-zeus_sky/src/Livewire/LibraryTag.php", "downloaded_repos/lara-zeus_sky/src/Livewire/Page.php", "downloaded_repos/lara-zeus_sky/src/Livewire/Post.php", "downloaded_repos/lara-zeus_sky/src/Livewire/Posts.php", "downloaded_repos/lara-zeus_sky/src/Livewire/SearchHelpers.php", "downloaded_repos/lara-zeus_sky/src/Livewire/Tags.php", "downloaded_repos/lara-zeus_sky/src/Models/Faq.php", "downloaded_repos/lara-zeus_sky/src/Models/Library.php", "downloaded_repos/lara-zeus_sky/src/Models/Navigation.php", "downloaded_repos/lara-zeus_sky/src/Models/Post.php", "downloaded_repos/lara-zeus_sky/src/Models/PostScope.php", "downloaded_repos/lara-zeus_sky/src/Models/PostStatus.php", "downloaded_repos/lara-zeus_sky/src/Models/Tag.php", "downloaded_repos/lara-zeus_sky/src/Rules/UniqueTranslationRule.php", "downloaded_repos/lara-zeus_sky/src/SkyPlugin.php", "downloaded_repos/lara-zeus_sky/src/SkyServiceProvider.php", "downloaded_repos/lara-zeus_sky/stubs/ZeusEditor.stub"], "skipped": [{"path": "downloaded_repos/lara-zeus_sky/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/lara-zeus_sky/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/lara-zeus_sky/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6822400093078613, "profiling_times": {"config_time": 6.076894283294678, "core_time": 2.8667097091674805, "ignores_time": 0.0018162727355957031, "total_time": 8.946455240249634}, "parsing_time": {"total_time": 0.7080726623535156, "per_file_time": {"mean": 0.0061040746754613415, "std_dev": 0.00015568711161761758}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.8515689373016357, "per_file_time": {"mean": 0.004429590759094821, "std_dev": 0.00018114184720933253}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.1906726360321045, "per_file_and_rule_time": {"mean": 0.0005195439673899306, "std_dev": 1.8020043357025804e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}