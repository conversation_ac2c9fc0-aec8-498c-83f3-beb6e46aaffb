#!/usr/bin/env python3

"""
Docker Container Vulnerability Scanner Runner
============================================

Builds and runs the unified vulnerability scanner in a Docker container
with all projects scanned inside the container using Semgrep.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

def print_banner():
    print("🐳" + "=" * 60 + "🐳")
    print("    DOCKER CONTAINER VULNERABILITY SCANNER")
    print("    All-in-One Container with Semgrep Integration")
    print("🐳" + "=" * 60 + "🐳")
    print()

def check_prerequisites():
    """Check if Docker and GitHub token are available"""
    print("🔍 Checking prerequisites...")
    
    # Check Docker
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Docker not installed")
            return False, "Docker not available"
        print(f"✅ Docker: {result.stdout.strip()}")
    except FileNotFoundError:
        print("❌ Docker not found in PATH")
        return False, "Docker not found"
    
    # Check Docker daemon
    try:
        result = subprocess.run(["docker", "info"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Docker daemon not running")
            return False, "Docker daemon not running"
        print("✅ Docker daemon is running")
    except:
        print("❌ Cannot connect to Docker daemon")
        return False, "Docker daemon connection failed"
    
    # Check GitHub token
    github_token = os.getenv("GITHUB_TOKEN")
    if not github_token or github_token == "your_github_token_here":
        print("❌ GITHUB_TOKEN not set")
        return False, "GitHub token not set"
    
    print(f"✅ GitHub Token: {github_token[:8]}...")
    print()
    return True, "All prerequisites met"

def build_docker_image(force_rebuild=False):
    """Build the Docker image"""
    print("🔨 Building Docker image...")
    
    # Check if image exists
    if not force_rebuild:
        try:
            result = subprocess.run(
                ["docker", "images", "-q", "unified-vuln-scanner"],
                capture_output=True, text=True
            )
            if result.stdout.strip():
                print("✅ Docker image already exists (use --rebuild to force rebuild)")
                return True
        except:
            pass
    
    try:
        # Build the image
        cmd = [
            "docker", "build", 
            "-t", "unified-vuln-scanner",
            "--progress", "plain",
            "."
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True)
        print("✅ Docker image built successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build Docker image: {e}")
        return False

def create_output_directory():
    """Create and prepare output directory"""
    output_dir = Path("./docker_output")
    output_dir.mkdir(exist_ok=True)
    
    # Create subdirectories
    (output_dir / "semgrep_results").mkdir(exist_ok=True)
    (output_dir / "logs").mkdir(exist_ok=True)
    
    print(f"📁 Output directory: {output_dir.absolute()}")
    return output_dir

def run_container_scanner(args, output_dir):
    """Run the vulnerability scanner in Docker container"""
    print("🚀 Running vulnerability scanner in Docker container...")
    print("This will:")
    print("  1. Search GitHub for vulnerable repositories")
    print("  2. Clone repositories inside the container")
    print("  3. Run Semgrep analysis on each repository")
    print("  4. Keep only top 10 highest scoring repositories")
    print("  5. Export detailed results with full Semgrep findings")
    print()
    
    # Prepare Docker command
    docker_cmd = [
        "docker", "run", "--rm",
        "-e", f"GITHUB_TOKEN={os.getenv('GITHUB_TOKEN')}",
        "-v", f"{output_dir.absolute()}:/app/output",
        "--name", "vuln-scanner-container"
    ]

    # Mount CSV file if provided
    if args.input_csv:
        if not os.path.exists(args.input_csv):
            print(f"❌ Input CSV file not found: {args.input_csv}")
            return False
        csv_abs_path = os.path.abspath(args.input_csv)
        csv_filename = os.path.basename(args.input_csv)
        docker_cmd.extend(["-v", f"{csv_abs_path}:/app/output/{csv_filename}:ro"])
        print(f"📄 Mounting CSV file: {args.input_csv}")

    docker_cmd.append("unified-vuln-scanner")
    
    # Add scanner arguments
    scanner_args = []
    if args.max_repos:
        scanner_args.extend(["--max-repos", str(args.max_repos)])
    if args.top_repos:
        scanner_args.extend(["--top-repos", str(args.top_repos)])
    if args.pages:
        scanner_args.extend(["--pages", str(args.pages)])
    if args.min_stars:
        scanner_args.extend(["--min-stars", str(args.min_stars)])
    if args.max_stars:
        scanner_args.extend(["--max-stars", str(args.max_stars)])
    if args.concurrent:
        scanner_args.extend(["--concurrent", str(args.concurrent)])
    if args.timeout:
        scanner_args.extend(["--timeout", str(args.timeout)])
    if args.skip_semgrep:
        scanner_args.append("--skip-semgrep")
    if args.input_csv:
        scanner_args.extend(["--input-csv", f"/app/output/{os.path.basename(args.input_csv)}"])
    if args.semgrep_only:
        scanner_args.append("--semgrep-only")
    
    docker_cmd.extend(scanner_args)
    
    print(f"🐳 Docker command: {' '.join(docker_cmd[:8])}... [additional args]")
    print()
    
    try:
        start_time = time.time()
        
        # Run the container
        result = subprocess.run(docker_cmd, check=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ Container scan completed successfully in {duration:.1f} seconds")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Container scan failed with return code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠️ Container scan interrupted by user")
        # Try to stop the container
        try:
            subprocess.run(["docker", "stop", "vuln-scanner-container"], check=False)
            print("🛑 Container stopped")
        except:
            pass
        return False

def show_results(output_dir):
    """Display scan results"""
    print("\n📊 SCAN RESULTS")
    print("=" * 40)
    
    # Check main results file
    results_file = output_dir / "unified_vulnerability_results.csv"
    if results_file.exists():
        try:
            with open(results_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if len(lines) > 1:
                    print(f"📄 Top repositories: {len(lines) - 1}")
                    
                    # Show top 5 results
                    print(f"\n🏆 TOP 5 RESULTS:")
                    for i, line in enumerate(lines[1:6], 1):
                        parts = line.strip().split(',')
                        if len(parts) >= 12:
                            rank = parts[0]
                            repo_name = parts[1]
                            final_score = parts[11]
                            semgrep_vulns = parts[8]
                            print(f"  {rank}. {repo_name} - Score: {final_score} ({semgrep_vulns} vulns)")
                else:
                    print("❌ Results file is empty")
        except Exception as e:
            print(f"❌ Error reading results: {e}")
    else:
        print("❌ No results file found")
    
    # Check Semgrep results
    semgrep_dir = output_dir / "semgrep_results"
    if semgrep_dir.exists():
        semgrep_files = list(semgrep_dir.glob("*.json"))
        print(f"🔍 Semgrep analysis files: {len(semgrep_files)}")
    
    # Check logs
    logs_dir = output_dir / "logs"
    if logs_dir.exists():
        log_files = list(logs_dir.glob("*.log"))
        if log_files:
            print(f"📝 Log files: {len(log_files)}")
    
    # Show all output files
    print(f"\n📁 All output files:")
    try:
        for file in output_dir.rglob("*"):
            if file.is_file():
                size = file.stat().st_size
                rel_path = file.relative_to(output_dir)
                print(f"  {rel_path} ({size:,} bytes)")
    except Exception as e:
        print(f"Error listing files: {e}")

def main():
    parser = argparse.ArgumentParser(description="Docker Container Vulnerability Scanner with Comprehensive Analysis")
    parser.add_argument("--max-repos", type=int, default=50, help="Maximum repositories to analyze")
    parser.add_argument("--top-repos", type=int, default=10, help="Number of top repositories to keep in CSV")
    parser.add_argument("--pages", type=int, default=3, help="Search pages per query")
    parser.add_argument("--min-stars", type=int, default=50, help="Minimum star count")
    parser.add_argument("--max-stars", type=int, default=2000, help="Maximum star count")
    parser.add_argument("--concurrent", type=int, default=1, help="Max concurrent scans")
    parser.add_argument("--timeout", type=int, default=300, help="Semgrep timeout in seconds")
    parser.add_argument("--skip-semgrep", action="store_true", help="Skip Semgrep scanning, only pull repository data")
    parser.add_argument("--input-csv", type=str, help="Input CSV file with repositories to scan")
    parser.add_argument("--semgrep-only", action="store_true", help="Only run Semgrep on repositories from input CSV")
    parser.add_argument("--rebuild", action="store_true", help="Force rebuild Docker image")
    parser.add_argument("--build-only", action="store_true", help="Only build the image")
    parser.add_argument("--show-results", action="store_true", help="Show existing results")
    
    args = parser.parse_args()
    
    print_banner()
    
    # Show results only
    if args.show_results:
        output_dir = Path("./docker_output")
        if output_dir.exists():
            show_results(output_dir)
        else:
            print("❌ No output directory found")
        return
    
    # Check prerequisites
    prereq_ok, prereq_msg = check_prerequisites()
    if not prereq_ok:
        print(f"❌ {prereq_msg}")
        if "token" in prereq_msg.lower():
            print("Set GitHub token with: export GITHUB_TOKEN='your_token_here'")
        sys.exit(1)
    
    # Create output directory
    output_dir = create_output_directory()
    
    # Build Docker image
    if not build_docker_image(args.rebuild):
        print("❌ Failed to build Docker image")
        sys.exit(1)
    
    if args.build_only:
        print("✅ Build complete")
        return
    
    # Run container scanner
    success = run_container_scanner(args, output_dir)
    
    if success:
        show_results(output_dir)
        print(f"\n🎯 SCAN COMPLETE!")
        print(f"📁 Results saved to: {output_dir.absolute()}")
        print(f"📄 Main results: {output_dir}/unified_vulnerability_results.csv")
        print(f"🔍 Semgrep details: {output_dir}/semgrep_results/")
        print(f"📝 Logs: {output_dir}/logs/")
    else:
        print("❌ Container scan failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
