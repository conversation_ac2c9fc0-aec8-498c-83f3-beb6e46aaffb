{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/index.html", "start": {"line": 9, "col": 3, "offset": 494}, "end": {"line": 9, "col": 72, "offset": 563}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/index.html", "start": {"line": 22, "col": 3, "offset": 835}, "end": {"line": 22, "col": 65, "offset": 897}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_electron/renderer/index.js", "start": {"line": 8, "col": 3, "offset": 151}, "end": {"line": 8, "col": 34, "offset": 182}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_http_proxy/client_http_proxy.js", "start": {"line": 15, "col": 17, "offset": 446}, "end": {"line": 15, "col": 21, "offset": 450}, "extra": {"message": "Checks for any usage of http servers instead of https servers. Encourages the usage of https protocol instead of http, which does not have TLS and is therefore unencrypted. Using http can lead to man-in-the-middle attacks in which the attacker is able to read sensitive information.", "metadata": {"likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": ["A02:2021 - Cryptographic Failures", "A03:2017 - Sensitive Data Exposure"], "references": ["https://nodejs.org/api/http.html#http_class_http_agent", "https://groups.google.com/g/rubyonrails-security/c/NCCsca7TEtY"], "subcategory": ["audit"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "shortlink": "https://sg.run/x1zL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server/server.js", "start": {"line": 23, "col": 15, "offset": 674}, "end": {"line": 23, "col": 45, "offset": 704}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server/server.js", "start": {"line": 27, "col": 17, "offset": 833}, "end": {"line": 27, "col": 50, "offset": 866}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/PrismarineJS_node-minecraft-protocol/.github/FUNDING.yml", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.github/dependabot.yml", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.github/workflows/ci.yml", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.github/workflows/commands.yml", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.github/workflows/npm-publish.yml", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.gitignore", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.gitpod.yml", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.npmignore", "downloaded_repos/PrismarineJS_node-minecraft-protocol/.npmrc", "downloaded_repos/PrismarineJS_node-minecraft-protocol/LICENSE", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/.nojekyll", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/API.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/FAQ.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/HISTORY.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/README.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/_sidebar.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/chat.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/docs/index.html", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_auto/client_auto.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_auto/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_channel/client_channel.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_channel/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_chat/client_chat.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_chat/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_custom_auth/client_custom_auth.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_custom_auth/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_custom_channel/client_custom_channel.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_custom_channel/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_custom_packets/client_custom_packets.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_custom_packets/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_echo/client_echo.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_echo/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_electron/README.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_electron/Window.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_electron/main.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_electron/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_electron/renderer/index.html", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_electron/renderer/index.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_http_proxy/client_http_proxy.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_http_proxy/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_microsoft_auth/client_microsoft_auth.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_microsoft_auth/client_msal_auth.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_microsoft_auth/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_realms/client_realms.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_realms/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_socks_proxy/README.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_socks_proxy/client_socks_proxy.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/client_socks_proxy/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/compiler_parse_buffer/index.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/compiler_parse_buffer/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/ipc/ipc_server.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/ipc/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/proxy/README.md", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/proxy/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/proxy/proxy.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server/server.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_channel/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_channel/server_channel.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_custom_channel/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_custom_channel/server_custom_channel.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_helloworld/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_helloworld/server_helloworld.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_ping/ping.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_world/mc.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/examples/server_world/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/package.json", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/browser.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/autoVersion.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/chat.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/compress.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/encrypt.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/keepalive.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/microsoftAuth.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/mojangAuth.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/play.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/pluginChannels.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/setProtocol.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/tcp_dns.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client/versionChecking.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/client.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/createClient.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/createServer.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/datatypes/compiler-minecraft.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/datatypes/minecraft.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/datatypes/uuid.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/index.d.ts", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/index.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/ping.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server/chat.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server/constants.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server/handshake.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server/keepalive.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server/login.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server/ping.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/states.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/transforms/binaryStream.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/transforms/compression.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/transforms/encryption.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/transforms/framing.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/transforms/serializer.js", "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/version.js"], "skipped": [{"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/benchmark.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/clientTest.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/common/clientHelpers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/common/util.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/cyclePacketTest.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/docTest.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/packetTest.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/PrismarineJS_node-minecraft-protocol/test/serverTest.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.0595309734344482, "profiling_times": {"config_time": 6.678882122039795, "core_time": 3.6396541595458984, "ignores_time": 0.0019443035125732422, "total_time": 10.321768522262573}, "parsing_time": {"total_time": 1.5526833534240723, "per_file_time": {"mean": 0.01764412901618264, "std_dev": 0.0008543880231336701}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 6.471631765365601, "per_file_time": {"mean": 0.021644253395871574, "std_dev": 0.005057578020044134}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.5151076316833496, "per_file_and_rule_time": {"mean": 0.005153909081318339, "std_dev": 0.00016063357214232365}, "very_slow_stats": {"time_ratio": 0.04287530549924231, "count_ratio": 0.0020491803278688526}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/PrismarineJS_node-minecraft-protocol/src/server/login.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.10783600807189941}]}, "tainting_time": {"total_time": 0.7545979022979736, "per_def_and_rule_time": {"mean": 0.0013523259897813148, "std_dev": 1.0517301646783596e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}