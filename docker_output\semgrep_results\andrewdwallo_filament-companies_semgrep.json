{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/andrewdwallo_filament-companies/.editorconfig", "downloaded_repos/andrewdwallo_filament-companies/.gitattributes", "downloaded_repos/andrewdwallo_filament-companies/.github/dependabot.yml", "downloaded_repos/andrewdwallo_filament-companies/.github/workflows/code-style.yml", "downloaded_repos/andrewdwallo_filament-companies/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/andrewdwallo_filament-companies/.github/workflows/update-changelog.yml", "downloaded_repos/andrewdwallo_filament-companies/.gitignore", "downloaded_repos/andrewdwallo_filament-companies/.styleci.yml", "downloaded_repos/andrewdwallo_filament-companies/LICENSE", "downloaded_repos/andrewdwallo_filament-companies/README.md", "downloaded_repos/andrewdwallo_filament-companies/UPGRADE.md", "downloaded_repos/andrewdwallo_filament-companies/composer.json", "downloaded_repos/andrewdwallo_filament-companies/database/factories/CompanyFactory.php", "downloaded_repos/andrewdwallo_filament-companies/database/factories/UserFactory.php", "downloaded_repos/andrewdwallo_filament-companies/database/migrations/0001_01_01_000000_create_users_table.php", "downloaded_repos/andrewdwallo_filament-companies/database/migrations/2020_05_21_100000_create_companies_table.php", "downloaded_repos/andrewdwallo_filament-companies/database/migrations/2020_05_21_200000_create_company_user_table.php", "downloaded_repos/andrewdwallo_filament-companies/database/migrations/2020_05_21_300000_create_company_invitations_table.php", "downloaded_repos/andrewdwallo_filament-companies/database/migrations/2020_12_22_000000_create_connected_accounts_table.php", "downloaded_repos/andrewdwallo_filament-companies/database/seeders/DatabaseSeeder.php", "downloaded_repos/andrewdwallo_filament-companies/lang/ar/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/cs/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/de/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/en/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/es/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/fr/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/id/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/ja/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/ko/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/nl/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/pt_BR/default.php", "downloaded_repos/andrewdwallo_filament-companies/lang/pt_PT/default.php", "downloaded_repos/andrewdwallo_filament-companies/phpunit.xml.dist", "downloaded_repos/andrewdwallo_filament-companies/pint.json", "downloaded_repos/andrewdwallo_filament-companies/resources/views/auth/login.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/auth/policy.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/auth/register.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/auth/terms.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/companies/company-employee-manager.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/companies/delete-company-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/companies/update-company-name-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/connected-account.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/grid-section.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/input-error.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/input.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/section-border.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/bitbucket.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/facebook.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/github.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/gitlab.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/google.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/linkedin.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/slack.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite-icons/twitter.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/components/socialite.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/filament/pages/companies/company_settings.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/filament/pages/companies/create_company.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/filament/pages/user/personal-access-tokens.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/filament/pages/user/profile.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/mail/company-invitation.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/profile/connected-accounts-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/profile/delete-user-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/profile/logout-other-browser-sessions-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/profile/set-password-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/profile/update-password-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/resources/views/profile/update-profile-information-form.blade.php", "downloaded_repos/andrewdwallo_filament-companies/src/Actions/GenerateRedirectForProvider.php", "downloaded_repos/andrewdwallo_filament-companies/src/Actions/UpdateCompanyEmployeeRole.php", "downloaded_repos/andrewdwallo_filament-companies/src/Actions/ValidateCompanyDeletion.php", "downloaded_repos/andrewdwallo_filament-companies/src/Company.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasAddedProfileComponents.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasAutoAcceptInvitations.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasBaseActionBindings.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasBaseModels.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasBaseProfileComponents.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasBaseProfileFeatures.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasCompanyFeatures.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasModals.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasNotifications.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasPanels.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasPermissions.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasRoutes.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Base/HasTermsAndPrivacyPolicy.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/ManagesProfileComponents.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Socialite/CanEnableSocialite.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Socialite/HasConnectedAccountModel.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Socialite/HasProviderFeatures.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Socialite/HasProviders.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Socialite/HasSocialiteActionBindings.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Socialite/HasSocialiteComponents.php", "downloaded_repos/andrewdwallo_filament-companies/src/Concerns/Socialite/HasSocialiteProfileFeatures.php", "downloaded_repos/andrewdwallo_filament-companies/src/ConnectedAccount.php", "downloaded_repos/andrewdwallo_filament-companies/src/Console/InstallCommand.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/AddsCompanyEmployees.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/CreatesCompanies.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/CreatesConnectedAccounts.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/CreatesNewUsers.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/CreatesUserFromProvider.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/Credentials.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/DeletesCompanies.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/DeletesUsers.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/GeneratesProviderRedirect.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/HandlesInvalidState.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/InvitesCompanyEmployees.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/RemovesCompanyEmployees.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/ResolvesSocialiteUsers.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/SetsUserPasswords.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/UpdatesCompanyNames.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/UpdatesConnectedAccounts.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/UpdatesUserPasswords.php", "downloaded_repos/andrewdwallo_filament-companies/src/Contracts/UpdatesUserProfileInformation.php", "downloaded_repos/andrewdwallo_filament-companies/src/Credentials.php", "downloaded_repos/andrewdwallo_filament-companies/src/Employeeship.php", "downloaded_repos/andrewdwallo_filament-companies/src/Enums/Feature.php", "downloaded_repos/andrewdwallo_filament-companies/src/Enums/Provider.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/AddingCompany.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/AddingCompanyEmployee.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/CompanyCreated.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/CompanyDeleted.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/CompanyEmployeeAdded.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/CompanyEmployeeRemoved.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/CompanyEmployeeUpdated.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/CompanyEvent.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/CompanyUpdated.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/ConnectedAccountCreated.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/ConnectedAccountDeleted.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/ConnectedAccountEvent.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/ConnectedAccountUpdated.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/InvitingCompanyEmployee.php", "downloaded_repos/andrewdwallo_filament-companies/src/Events/RemovingCompanyEmployee.php", "downloaded_repos/andrewdwallo_filament-companies/src/FilamentCompanies.php", "downloaded_repos/andrewdwallo_filament-companies/src/FilamentCompaniesServiceProvider.php", "downloaded_repos/andrewdwallo_filament-companies/src/HasCompanies.php", "downloaded_repos/andrewdwallo_filament-companies/src/HasConnectedAccounts.php", "downloaded_repos/andrewdwallo_filament-companies/src/HasProfilePhoto.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Controllers/CompanyInvitationController.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Controllers/OAuthController.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/CompanyEmployeeManager.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/ConnectedAccountsForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/CreateCompanyForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/DeleteCompanyForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/DeleteUserForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/LogoutOtherBrowserSessionsForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/SetPasswordForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/UpdateCompanyNameForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/UpdatePasswordForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Livewire/UpdateProfileInformationForm.php", "downloaded_repos/andrewdwallo_filament-companies/src/Http/Responses/Auth/FilamentCompaniesRegistrationResponse.php", "downloaded_repos/andrewdwallo_filament-companies/src/Listeners/SwitchCurrentCompany.php", "downloaded_repos/andrewdwallo_filament-companies/src/Mail/CompanyInvitation.php", "downloaded_repos/andrewdwallo_filament-companies/src/OwnerRole.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/Auth/Login.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/Auth/PrivacyPolicy.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/Auth/Register.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/Auth/Terms.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/Company/CompanySettings.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/Company/CreateCompany.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/User/PersonalAccessTokens.php", "downloaded_repos/andrewdwallo_filament-companies/src/Pages/User/Profile.php", "downloaded_repos/andrewdwallo_filament-companies/src/RedirectsActions.php", "downloaded_repos/andrewdwallo_filament-companies/src/Role.php", "downloaded_repos/andrewdwallo_filament-companies/src/Rules/Role.php", "downloaded_repos/andrewdwallo_filament-companies/src/SetsProfilePhotoFromUrl.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/AddCompanyEmployee.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/CreateCompany.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/CreateConnectedAccount.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/CreateNewUser.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/CreateUserFromProvider.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/DeleteCompany.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/DeleteUser.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/DeleteUserWithSocialite.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/HandleInvalidState.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/InviteCompanyEmployee.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/RemoveCompanyEmployee.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/ResolveSocialiteUser.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/SetUserPassword.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/UpdateCompanyName.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/UpdateConnectedAccount.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/UpdateUserPassword.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Actions/FilamentCompanies/UpdateUserProfileInformation.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Models/Company.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Models/CompanyInvitation.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Models/ConnectedAccount.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Models/Employeeship.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Models/User.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Models/UserWithSocialite.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Policies/CompanyPolicy.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Policies/ConnectedAccountPolicy.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Providers/FilamentCompaniesServiceProvider.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/app/Providers/FilamentCompaniesWithSocialiteServiceProvider.php", "downloaded_repos/andrewdwallo_filament-companies/stubs/resources/markdown/policy.md", "downloaded_repos/andrewdwallo_filament-companies/stubs/resources/markdown/terms.md"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.****************, "profiling_times": {"config_time": 5.***************, "core_time": 3.****************, "ignores_time": 0.0022134780883789062, "total_time": 9.***************}, "parsing_time": {"total_time": 0.****************, "per_file_time": {"mean": 0.0049969798228779805, "std_dev": 8.859801940695087e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.***************, "per_file_time": {"mean": 0.004521892184302928, "std_dev": 0.00010279182673833423}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.24207496643066406, "per_file_and_rule_time": {"mean": 0.0006472592685311872, "std_dev": 2.995146185075456e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.02302861213684082, "per_def_and_rule_time": {"mean": 0.0002530616718334156, "std_dev": 2.930153999625718e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}