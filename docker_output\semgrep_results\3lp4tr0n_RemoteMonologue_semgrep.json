{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/3lp4tr0n_RemoteMonologue/RemoteMonologue.py", "start": {"line": 1112, "col": 13, "offset": 45862}, "end": {"line": 1112, "col": 142, "offset": 45991}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret 'loaded smbclient auth file: domain=%s, username=%s, password=%s' % (repr(domain), repr(username), repr(password)) being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/3lp4tr0n_RemoteMonologue/README.md", "downloaded_repos/3lp4tr0n_RemoteMonologue/RemoteMonologue.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.8999929428100586, "profiling_times": {"config_time": 6.6176228523254395, "core_time": 4.002164840698242, "ignores_time": 0.003079652786254883, "total_time": 10.623682498931885}, "parsing_time": {"total_time": 0.037728071212768555, "per_file_time": {"mean": 0.037728071212768555, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.4614479541778564, "per_file_time": {"mean": 0.29228959083557127, "std_dev": 0.30995936556065773}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.0526914596557617, "per_file_and_rule_time": {"mean": 0.02567540145501858, "std_dev": 0.0011857582190277559}, "very_slow_stats": {"time_ratio": 0.4390878280745081, "count_ratio": 0.0975609756097561}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/3lp4tr0n_RemoteMonologue/RemoteMonologue.py", "rule_id": "python.django.security.injection.command.command-injection-os-system.command-injection-os-system", "time": 0.10196709632873535}, {"fpath": "downloaded_repos/3lp4tr0n_RemoteMonologue/RemoteMonologue.py", "rule_id": "python.django.security.injection.sql.sql-injection-using-db-cursor-execute.sql-injection-db-cursor-execute", "time": 0.10724997520446777}, {"fpath": "downloaded_repos/3lp4tr0n_RemoteMonologue/RemoteMonologue.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.11303997039794922}, {"fpath": "downloaded_repos/3lp4tr0n_RemoteMonologue/RemoteMonologue.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.1399669647216797}]}, "tainting_time": {"total_time": 0.21734023094177246, "per_def_and_rule_time": {"mean": 0.0012709955025834647, "std_dev": 1.1045220073204335e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}