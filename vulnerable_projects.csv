Name,URL,Stars,Issues,Updated,Risk,Description
aleron75/mageres,https://github.com/aleron75/mageres,997,4,2025-07-26T10:33:33Z,0,A list of useful Magento technical resources
woocommerce/storefront,https://github.com/woocommerce/storefront,995,108,2025-06-20T08:28:39Z,0,Official theme for WooCommerce
xb2016/kratos-pjax,https://github.com/xb2016/kratos-pjax,993,3,2025-05-12T07:33:28Z,0,🍨 A lovely WordPress theme [DEPRECATED]
spatie/calendar-links,https://github.com/spatie/calendar-links,975,1,2025-07-01T21:38:03Z,0,"Generate add to calendar links for Google, iCal and other calendar systems"
totumonline/totum-mit,https://github.com/totumonline/totum-mit,971,0,2025-07-24T14:51:44Z,0,"Small-code database for non-programmers. Universal UI, simple-code logic, automatic actions, access rules, logging, API and more. Quickly create a complex internal apps using the database as an interface."
fightbulc/moment.php,https://github.com/fightbulc/moment.php,970,21,2025-07-27T08:47:21Z,0,"Parse, validate, manipulate, and display dates in PHP w/ i18n support. Inspired by moment.js"
nette/schema,https://github.com/nette/schema,975,9,2025-06-19T17:51:01Z,0,📐 Validating data structures against a given Schema.
openspout/openspout,https://github.com/openspout/openspout,965,60,2025-07-27T02:42:11Z,0," Read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way "
tedious/Stash,https://github.com/tedious/Stash,964,34,2025-05-27T13:42:02Z,0,The place to keep your cache.
spatie/temporary-directory,https://github.com/spatie/temporary-directory,956,1,2025-06-16T13:53:19Z,0,A simple class to work with a temporary directory
Combodo/iTop,https://github.com/Combodo/iTop,954,34,2025-07-24T08:59:52Z,0,"A simple, web based IT Service Management tool "
spatie/pdf-to-text,https://github.com/spatie/pdf-to-text,953,1,2025-06-13T15:24:04Z,0,Extract text from a pdf
tschoffelen/php-pkpass,https://github.com/tschoffelen/php-pkpass,948,1,2025-06-16T16:11:00Z,0,💳 PHP class for creating passes for Wallet on iOS.
TasmoAdmin/TasmoAdmin,https://github.com/TasmoAdmin/TasmoAdmin,941,62,2025-07-26T20:55:16Z,1,TasmoAdmin is an administrative platform for devices flashed with Tasmota
MyIntervals/emogrifier,https://github.com/MyIntervals/emogrifier,935,79,2025-07-27T19:25:58Z,0,Converts CSS styles into inline style attributes in your HTML code.
Vonage/vonage-php-sdk-core,https://github.com/Vonage/vonage-php-sdk-core,931,1,2025-07-14T14:06:12Z,0,"Vonage REST API client for PHP. API support for SMS, Voice, Text-to-Speech, Numbers, Verify (2FA) and more."
nextcloud/news,https://github.com/nextcloud/news,930,29,2025-07-26T01:15:14Z,0,📰 RSS/Atom feed reader
solcloud/Counter-Strike,https://github.com/solcloud/Counter-Strike,929,2,2025-05-12T14:27:14Z,0,Multiplayer FPS game - Counter-Strike: Football 🏉
Incenteev/ParameterHandler,https://github.com/Incenteev/ParameterHandler,929,32,2025-07-11T22:39:45Z,0,Composer script handling your ignored parameter file
php/web-php,https://github.com/php/web-php,921,31,2025-07-27T13:05:28Z,0,The www.php.net site
paragonie/sodium_compat,https://github.com/paragonie/sodium_compat,919,0,2025-07-19T01:34:23Z,0,Pure PHP polyfill for ext/sodium
AdvancedCustomFields/acf,https://github.com/AdvancedCustomFields/acf,909,490,2025-07-22T14:02:53Z,0,Advanced Custom Fields 
mongodb/mongo-php-driver,https://github.com/mongodb/mongo-php-driver,906,15,2025-07-24T11:10:08Z,0,The Official MongoDB PHP driver
nette/di,https://github.com/nette/di,904,31,2025-06-19T17:14:43Z,0,"💎 Flexible, compiled and full-featured Dependency Injection Container with perfectly usable autowiring and support for all new PHP 8 features."
OpenMage/magento-lts,https://github.com/OpenMage/magento-lts,902,193,2025-07-22T07:48:45Z,0,"Official OpenMage LTS codebase | Migrate easily from Magento Community Edition in minutes! Download the source code for free or contribute to OpenMage LTS | Security vulnerability patches, bug fixes, performance improvements and more."
rappasoft/laravel-authentication-log,https://github.com/rappasoft/laravel-authentication-log,901,20,2025-05-24T03:13:59Z,1,Log user authentication details and send new device notifications.
pkp/ojs,https://github.com/pkp/ojs,894,246,2025-07-25T16:51:48Z,0,Open Journal Systems is open source software to manage scholarly journals.
fusionpbx/fusionpbx,https://github.com/fusionpbx/fusionpbx,884,393,2025-07-25T21:32:08Z,0,Official FusionPBX - A full-featured domain based multi-tenant PBX and voice switch for FreeSwitch.
inspector-apm/neuron-ai,https://github.com/inspector-apm/neuron-ai,883,6,2025-07-26T10:48:43Z,0,"The PHP Agent Development Kit to build customizable, production-ready Agentic applications. Connect components (models, vector DBs, file converters) to workflows or agents that can interact with your data. With advanced retrieval methods, it's best suited for building RAG, question answering, semantic search or business processes automations."
nette/robot-loader,https://github.com/nette/robot-loader,883,5,2025-06-19T18:40:01Z,0,🍀 RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application. 
awesomemotive/easy-digital-downloads,https://github.com/awesomemotive/easy-digital-downloads,880,626,2025-07-15T21:14:32Z,0,Sell digital downloads through WordPress
gmajian/sandphoto,https://github.com/gmajian/sandphoto,879,3,2025-07-15T07:57:58Z,0,Layout ID/VISA photos in a single 6-inch photo 证件照片排版在线生成器 - 在一张6寸的照片上排版多张证件照
libretime/libretime,https://github.com/libretime/libretime,876,165,2025-07-22T16:58:29Z,0,LibreTime: Radio Broadcast & Automation Platform
chamilo/chamilo-lms,https://github.com/chamilo/chamilo-lms,872,560,2025-07-25T23:02:55Z,0,Chamilo is a learning management system focused on ease of use and accessibility
ezSQL/ezsql,https://github.com/ezSQL/ezsql,870,2,2025-04-30T21:55:58Z,0,PHP class to make interacting with a database ridiculusly easy
thephpleague/container,https://github.com/thephpleague/container,856,1,2025-06-12T19:27:23Z,0,Small but powerful dependency injection container
cakephp/debug_kit,https://github.com/cakephp/debug_kit,848,3,2025-07-22T01:18:42Z,0,Debug Toolbar for CakePHP applications.
aschmelyun/subvert,https://github.com/aschmelyun/subvert,846,24,2025-05-26T04:56:54Z,0,"Generate subtitles, summaries, and chapters from videos in seconds"
andrii-kryvoviaz/slink,https://github.com/andrii-kryvoviaz/slink,840,15,2025-07-18T18:33:27Z,0,Self-hosted image sharing service.
textpattern/textpattern,https://github.com/textpattern/textpattern,833,80,2025-07-16T21:34:48Z,0,"A flexible, elegant, fast and easy-to-use content management system written in PHP."
karlomikus/bar-assistant,https://github.com/karlomikus/bar-assistant,830,17,2025-07-26T18:23:58Z,0,Bar assistant is a all-in-one solution for managing your home bar
Icinga/icingaweb2,https://github.com/Icinga/icingaweb2,829,326,2025-07-25T13:38:38Z,0,A lightweight and extensible web interface to keep an eye on your environment. Analyse problems and act on them.
arabcoders/watchstate,https://github.com/arabcoders/watchstate,828,0,2025-07-24T08:15:10Z,0,"Self-hosted service to sync your plex, jellyfin and emby play state. without relying on 3rd-party external services."
joanhey/AdapterMan,https://github.com/joanhey/AdapterMan,816,32,2025-05-06T09:51:31Z,0,"Run almost any PHP app faster and asynchronously with Workerman, without touch 1 line of code in your fw or app. Also use it as Serverless."
playsms/playsms,https://github.com/playsms/playsms,809,66,2025-06-20T15:00:34Z,0,playSMS is a web interface for SMS gateways and bulk SMS services
ClassicPress/ClassicPress,https://github.com/ClassicPress/ClassicPress,807,39,2025-07-27T10:05:16Z,0,The CMS for Creators. Stable. Lightweight. Instantly Familiar. Forked from WordPress.
spatie/enum,https://github.com/spatie/enum,806,0,2025-07-15T00:36:12Z,0,Strongly typed enums in PHP supporting autocompletion and refactoring
phpList/phplist3,https://github.com/phpList/phplist3,802,83,2025-07-14T06:20:06Z,0,"Fully functional Open Source email marketing manager for creating, sending, integrating, and analysing email campaigns and newsletters."
lirantal/daloradius,https://github.com/lirantal/daloradius,783,86,2025-07-23T07:25:46Z,0,"daloRADIUS is an advanced RADIUS web management application for managing hotspots and general-purpose ISP deployments. It features user management, graphical reporting, accounting, a billing engine, and integrates with OpenStreetMap for geolocation. The system is based on FreeRADIUS with which it shares access to the backend database."
Tatoeba/tatoeba2,https://github.com/Tatoeba/tatoeba2,781,463,2025-07-24T15:16:31Z,0,Tatoeba is a platform whose purpose is to create a collaborative and open dataset of sentences and their translations.
WordPress/two-factor,https://github.com/WordPress/two-factor,779,72,2025-07-21T23:08:47Z,1,Two-Factor Authentication for WordPress.
reactphp/http,https://github.com/reactphp/http,772,22,2025-05-29T21:28:11Z,0,"Event-driven, streaming HTTP client and server implementation for ReactPHP."
KnpLabs/knp-components,https://github.com/KnpLabs/knp-components,768,8,2025-06-27T05:36:13Z,0,"Various component pack, includes paginator"
lcobucci/clock,https://github.com/lcobucci/clock,755,1,2025-07-27T02:44:22Z,0,Yet another clock abstraction
itflow-org/itflow,https://github.com/itflow-org/itflow,754,17,2025-07-25T18:21:59Z,1,"All in One PSA for MSPs, which Unifies client, contact, vendor, asset, license, domain, ssl certificate, password, documentation, file, network and location management with ticketing and billing capabilities, with a client portal on the side."
thirtybees/thirtybees,https://github.com/thirtybees/thirtybees,753,344,2025-06-16T06:09:31Z,0,thirty bees - e-commerce that works for you
ivan-sincek/penetration-testing-cheat-sheet,https://github.com/ivan-sincek/penetration-testing-cheat-sheet,736,0,2025-06-17T12:34:02Z,0,Work in progress...
ad-aures/castopod,https://github.com/ad-aures/castopod,735,0,2025-05-20T12:23:09Z,0,Castopod is an open-source hosting platform made for podcasters who want engage and interact with their audience. Synchronized read-only mirror of https://code.castopod.org/adaures/castopod 
ChurchCRM/CRM,https://github.com/ChurchCRM/CRM,726,8,2025-07-23T09:36:15Z,0,ChurchCRM is an OpenSource Church CRM & Management Software.
tirrenotechnologies/tirreno,https://github.com/tirrenotechnologies/tirreno,723,3,2025-07-25T20:05:14Z,0,"tirreno - Open Source Cybersecurity Analytics. Monitor, analyze, and protect your digital services from cyberfraud, account threats, and abuse. Get started — free."
pan-unit42/iocs,https://github.com/pan-unit42/iocs,717,6,2025-07-23T18:40:52Z,0,Indicators from Unit 42 Public Reports
poweradmin/poweradmin,https://github.com/poweradmin/poweradmin,717,61,2025-07-24T05:41:55Z,0,A web-based control panel for PowerDNS
spatie/macroable,https://github.com/spatie/macroable,712,1,2025-06-16T06:56:46Z,0,A trait to dynamically add methods to a class
ushahidi/platform,https://github.com/ushahidi/platform,711,10,2025-05-06T01:07:12Z,0,Ushahidi Platform API version 3+
pyrohost/pyrodactyl,https://github.com/pyrohost/pyrodactyl,711,34,2025-07-22T12:42:43Z,0,"Pyrodactyl is the Pterodactyl-based game server panel that's faster, smaller, safer, and more accessible than Pelican."
HaschekSolutions/opentrashmail,https://github.com/HaschekSolutions/opentrashmail,707,11,2025-07-24T07:15:01Z,0,Open Source standalone trashmail solution that ships its own mail server
guanguans/music-dl,https://github.com/guanguans/music-dl,707,0,2025-06-19T14:10:28Z,0,Music Searcher and Downloader. - 音乐搜索下载器。
Submitty/Submitty,https://github.com/Submitty/Submitty,704,584,2025-07-27T16:29:25Z,0,"Homework Submission, Automated Grading, and TA grading system."
WonderCMS/wondercms,https://github.com/WonderCMS/wondercms,701,3,2025-07-12T17:42:45Z,0,"Fast and small flat file CMS (5 files). Built with PHP, JSON database."
openml/OpenML,https://github.com/openml/OpenML,700,374,2025-07-20T14:28:00Z,0,Open Machine Learning
voku/anti-xss,https://github.com/voku/anti-xss,696,32,2025-06-12T23:08:31Z,0,㊙️ AntiXSS | Protection against Cross-site scripting (XSS) via PHP
SolidInvoice/SolidInvoice,https://github.com/SolidInvoice/SolidInvoice,695,81,2025-07-21T16:41:41Z,0,Simple and elegant invoicing solution.
jamesmills/laravel-timezone,https://github.com/jamesmills/laravel-timezone,693,33,2025-05-28T05:47:25Z,0,Enable user Timezones in your application.
algolia/algoliasearch-client-php,https://github.com/algolia/algoliasearch-client-php,687,18,2025-07-23T09:55:53Z,0,⚡️ A fully-featured and blazing-fast PHP API client to interact with Algolia.
php-censor/php-censor,https://github.com/php-censor/php-censor,684,23,2025-05-23T03:40:43Z,0,PHP Censor is an open source self-hosted continuous integration server for PHP projects.
CodeWithKyrian/transformers-php,https://github.com/CodeWithKyrian/transformers-php,681,15,2025-07-21T13:00:38Z,0,Transformers PHP is a toolkit for PHP developers to add machine learning magic to their projects easily. 
php-youtubers/directory,https://github.com/php-youtubers/directory,678,4,2025-07-21T03:17:29Z,0,Curated a list of PHP YouTubers. 📋
nette/bootstrap,https://github.com/nette/bootstrap,677,1,2025-06-19T16:52:59Z,0,🅱 The simple way to configure and bootstrap your Nette application.
wp-erp/wp-erp,https://github.com/wp-erp/wp-erp,675,52,2025-07-25T12:37:25Z,0,An open-source ERP (Enterprise Resource Planning) solution for WordPress
10up/distributor,https://github.com/10up/distributor,671,238,2025-07-17T21:23:21Z,0,Share content between your websites.
maxmind/MaxMind-DB-Reader-php,https://github.com/maxmind/MaxMind-DB-Reader-php,670,5,2025-07-24T16:36:31Z,0,PHP Reader for the MaxMind DB Database Format
tempestphp/highlight,https://github.com/tempestphp/highlight,668,7,2025-07-18T12:40:01Z,0,"🎨 Fast, extensible, server-side code highlighting for web and terminal"
wp-graphql/wp-graphql-woocommerce,https://github.com/wp-graphql/wp-graphql-woocommerce,665,88,2025-05-01T18:46:55Z,0,Add WooCommerce support and functionality to your WPGraphQL server
billabear/billabear,https://github.com/billabear/billabear,663,1,2025-07-24T18:55:08Z,0,Subscription Management and Billing System
phpstan/phpstan-strict-rules,https://github.com/phpstan/phpstan-strict-rules,650,40,2025-07-21T12:19:59Z,0,Extra strict and opinionated rules for PHPStan
10up/classifai,https://github.com/10up/classifai,649,118,2025-07-27T20:25:31Z,0,Supercharge WordPress Content Workflows and Engagement with Artificial Intelligence.
psr7-sessions/storageless,https://github.com/psr7-sessions/storageless,649,3,2025-07-27T06:35:57Z,0,:mailbox_with_mail: storage-less PSR-7 session support
aimeos/ai-admin-jqadm,https://github.com/aimeos/ai-admin-jqadm,648,2,2025-07-09T07:05:28Z,1,Aimeos e-commerce Vue.js+Bootstrap based admin interface
aporat/store-receipt-validator,https://github.com/aporat/store-receipt-validator,641,2,2025-05-06T01:04:46Z,0,"PHP receipt validator for Apple iTunes, Google Play and Amazon App Store"
brufdev/many-notes,https://github.com/brufdev/many-notes,640,10,2025-07-27T14:24:28Z,0,Markdown note-taking web application designed for simplicity
NamelessMC/Nameless,https://github.com/NamelessMC/Nameless,640,134,2025-07-27T09:00:13Z,0,"NamelessMC is a free, easy to use & powerful website software for your Minecraft server, which includes a large range of features."
ONLYOFFICE/onlyoffice-nextcloud,https://github.com/ONLYOFFICE/onlyoffice-nextcloud,637,256,2025-07-25T14:15:53Z,0,"The app which enables the users to edit office documents from Nextcloud using ONLYOFFICE Document Server, allows multiple users to collaborate in real time and to save back those changes to Nextcloud"
leiweibau/Pi.Alert,https://github.com/leiweibau/Pi.Alert,637,4,2025-07-25T19:20:27Z,0,"Scan the devices connected to your WIFI / LAN and alert you the connection of unknown devices. It also warns if a ""always connected"" device disconnects. In addition, it is possible to check web services for availability. For this purpose HTTP status codes and the response time of the service are evaluated."
leenooks/phpLDAPadmin,https://github.com/leenooks/phpLDAPadmin,636,5,2025-07-25T13:12:23Z,1,phpLDAPadmin - Web based LDAP administration tool
WebDevStudios/custom-post-type-ui,https://github.com/WebDevStudios/custom-post-type-ui,636,43,2025-07-25T20:54:40Z,1,Admin UI settings for creating custom post types and taxonomies in WordPress
ackintosh/ganesha,https://github.com/ackintosh/ganesha,634,15,2025-06-08T22:32:03Z,0,:elephant: A Circuit Breaker pattern implementation for PHP applications.
ksubileau/color-thief-php,https://github.com/ksubileau/color-thief-php,634,7,2025-07-17T15:51:44Z,0,"Grabs the dominant color or a representative color palette from an image. Uses PHP and GD, Imagick or Gmagick."
nunomaduro/pokio,https://github.com/nunomaduro/pokio,633,4,2025-06-09T12:01:47Z,0,Pokio is a dead simple Asynchronous API for PHP that just works.
nextcloud/recognize,https://github.com/nextcloud/recognize,620,113,2025-07-27T14:01:16Z,0,"👁 👂 Smart media tagging for Nextcloud: recognizes faces, objects, landscapes, music genres"
Chassis/Chassis,https://github.com/Chassis/Chassis,620,33,2025-07-15T21:29:59Z,0,"📦  Chassis is a virtual server for your WordPress site, built using Vagrant."
xtrime-ru/TelegramApiServer,https://github.com/xtrime-ru/TelegramApiServer,616,17,2025-07-13T19:57:30Z,0,"Fast, simple, async php telegram api server: MadelineProto + Amp HTTP Server"
jprochazka/adsb-receiver,https://github.com/jprochazka/adsb-receiver,612,10,2025-05-29T20:32:16Z,0,Create your own ADS-B receiver.
siteorigin/siteorigin-panels,https://github.com/siteorigin/siteorigin-panels,611,17,2025-07-23T23:10:26Z,0,"SiteOrigin Page Builder is a powerful content creation interface, instantly recognizable, astonishingly different. SiteOrigin Page Builder makes it easy to create responsive column-based content using the widgets you know and love. Your content will accurately adapt to all mobile devices, ensuring your site is mobile-ready."
swoole/phpy,https://github.com/swoole/phpy,610,4,2025-06-16T06:44:39Z,0,Connecting the Python and PHP ecosystems together
wpengine/frost,https://github.com/wpengine/frost,609,5,2025-07-01T13:49:42Z,0,The ultimate WordPress block theme for website designers and developers.
noob-hackers/spamx,https://github.com/noob-hackers/spamx,607,3,2025-05-26T04:05:10Z,0,All In 1 Spam Tool For Termux Users Subscribe Us (Noob Hackers) some shit heads are trying to abuse this script so don't worry about them ...let them hallucinate ...but you are free to use this script
aimeos/ai-client-jsonapi,https://github.com/aimeos/ai-client-jsonapi,605,1,2025-07-09T07:05:33Z,0,Aimeos frontend JSON API
aws-actions/amazon-ecr-login,https://github.com/aws-actions/amazon-ecr-login,1000,41,2025-07-22T23:53:24Z,0,Logs into Amazon ECR with the local Docker client.
lucahammer/tweetXer,https://github.com/lucahammer/tweetXer,998,6,2025-05-29T16:13:18Z,0,Delete all your Tweets for free
sc-forks/solidity-coverage,https://github.com/sc-forks/solidity-coverage,998,39,2025-05-07T21:41:30Z,0,Code coverage for Solidity smart-contracts 
dart-lang/site-www,https://github.com/dart-lang/site-www,997,142,2025-07-25T15:09:47Z,0,The source for the Dart website.
lookbook-hq/lookbook,https://github.com/lookbook-hq/lookbook,995,29,2025-07-26T15:12:04Z,0,A UI development environment for Ruby on Rails apps ✨
brightdata/brightdata-mcp,https://github.com/brightdata/brightdata-mcp,994,6,2025-07-22T07:18:35Z,0,A powerful Model Context Protocol (MCP) server that provides an all-in-one solution for public web access.
c-frame/aframe-extras,https://github.com/c-frame/aframe-extras,994,24,2025-07-05T09:35:42Z,0,Add-ons and helpers for A-Frame VR.
repository-settings/app,https://github.com/repository-settings/app,991,80,2025-07-25T01:13:20Z,0,Pull Requests for GitHub repository settings
newrelic/node-newrelic,https://github.com/newrelic/node-newrelic,989,75,2025-07-25T21:00:03Z,0,"New Relic Node.js agent code base. Developers are welcome to create pull requests here, please see our contributing guidelines. For New Relic technical support, please go to http://support.newrelic.com."
LispCookbook/cl-cookbook,https://github.com/LispCookbook/cl-cookbook,989,70,2025-07-24T15:49:04Z,0,The Common Lisp Cookbook
draios/sysdig-inspect,https://github.com/draios/sysdig-inspect,988,69,2025-05-14T16:46:43Z,0,Sysdig Inspect - A powerful opensource interface for container troubleshooting and security investigation
AmauriC/tarteaucitron.js,https://github.com/AmauriC/tarteaucitron.js,987,3,2025-07-24T17:26:39Z,0,Get a compliant and accessible cookie banner
metarhia/impress,https://github.com/metarhia/impress,985,32,2025-07-15T13:31:48Z,0,Enterprise application server for Node.js and Metarhia private cloud ⚡
DoctorMcKay/node-steam-user,https://github.com/DoctorMcKay/node-steam-user,982,53,2025-05-30T23:24:35Z,0,Allows interaction with the Steam network via the Steam client protocol
jeffpar/pcjs,https://github.com/jeffpar/pcjs,982,33,2025-07-27T17:17:33Z,0,The original IBM PC and other machine emulations in JavaScript
evansiroky/timezone-boundary-builder,https://github.com/evansiroky/timezone-boundary-builder,982,5,2025-06-13T14:09:50Z,0,A tool to extract data from Open Street Map (OSM) to build the boundaries of the world's timezones.
CircuitVerse/CircuitVerse,https://github.com/CircuitVerse/CircuitVerse,981,986,2025-07-27T17:37:08Z,0,CircuitVerse Primary Code Base
v8/v8.dev,https://github.com/v8/v8.dev,978,24,2025-06-26T10:00:14Z,0,"The source code of v8.dev, the official website of the V8 project."
indexeddbshim/IndexedDBShim,https://github.com/indexeddbshim/IndexedDBShim,974,17,2025-06-19T09:02:54Z,0,A polyfill for IndexedDB using WebSql
eslint-community/eslint-plugin-promise,https://github.com/eslint-community/eslint-plugin-promise,974,43,2025-07-25T19:53:33Z,0,Enforce best practices for JavaScript promises
asciimath/asciimathml,https://github.com/asciimath/asciimathml,973,53,2025-05-22T16:34:56Z,0,A new home for asciimathml
gulpjs/vinyl-fs,https://github.com/gulpjs/vinyl-fs,971,7,2025-06-01T21:41:10Z,0,Vinyl adapter for the file system.
emberjs/ember-inspector,https://github.com/emberjs/ember-inspector,970,78,2025-07-22T09:01:44Z,0, Adds an Ember tab to the browser's Developer Tools that allows you to inspect Ember objects in your application.
SteamDatabase/SteamTracking,https://github.com/SteamDatabase/SteamTracking,968,1,2025-07-25T23:06:00Z,0,"🕵 Tracking things, so you don't have to"
sonnyp/Tangram,https://github.com/sonnyp/Tangram,967,59,2025-07-24T22:11:35Z,0,Browser for your pinned tabs
webpack/enhanced-resolve,https://github.com/webpack/enhanced-resolve,966,29,2025-07-22T12:27:54Z,0,Offers an async require.resolve function. It's highly configurable.
imgix/imgix.js,https://github.com/imgix/imgix.js,965,7,2025-07-25T18:47:21Z,0,"Responsive images in the browser, simplified"
pallets-eco/flask-debugtoolbar,https://github.com/pallets-eco/flask-debugtoolbar,965,39,2025-07-07T19:28:00Z,0,A toolbar overlay for debugging Flask applications
glowing-bear/glowing-bear,https://github.com/glowing-bear/glowing-bear,961,152,2025-07-15T09:33:22Z,0,A web client for WeeChat
dyatko/arkit,https://github.com/dyatko/arkit,961,31,2025-07-27T13:03:44Z,0,JavaScript architecture diagrams and dependency graphs
source-academy/sicp,https://github.com/source-academy/sicp,961,30,2025-07-05T03:59:35Z,0,"XML sources of SICP and SICP JS, and support for generating Interactive SICP JS, PDF, e-book and comparison editions"
LavaMoat/LavaMoat,https://github.com/LavaMoat/LavaMoat,961,188,2025-07-27T14:44:05Z,0,tools for sandboxing your dependency graph
christianvoigt/argdown,https://github.com/christianvoigt/argdown,961,153,2025-07-24T16:34:00Z,0,a simple syntax for complex argumentation
ryansolid/dom-expressions,https://github.com/ryansolid/dom-expressions,961,63,2025-07-10T17:59:16Z,0,A Fine-Grained Runtime for Performant DOM Rendering
webpack-contrib/less-loader,https://github.com/webpack-contrib/less-loader,960,3,2025-06-20T13:48:53Z,0,Compiles Less to CSS
christian-fei/my-yt,https://github.com/christian-fei/my-yt,958,1,2025-07-05T08:24:50Z,0,"A clean and minimal youtube frontend, without all the ads and whistles"
xiaopanglian/icefox,https://github.com/xiaopanglian/icefox,956,14,2025-07-23T02:20:23Z,0,Typecho微信朋友圈主题 icefox
projectfluent/fluent.js,https://github.com/projectfluent/fluent.js,955,95,2025-07-18T13:13:48Z,0,JavaScript implementation of Project Fluent
salesforce/design-system-react,https://github.com/salesforce/design-system-react,954,152,2025-06-24T19:50:58Z,0,Salesforce Lightning Design System for React
transitive-bullshit/ffmpeg-concat,https://github.com/transitive-bullshit/ffmpeg-concat,949,51,2025-06-03T06:18:46Z,0,Concats a list of videos together using ffmpeg with sexy OpenGL transitions.
offen/offen,https://github.com/offen/offen,949,23,2025-07-17T21:41:49Z,0,Offen Fair Web Analytics
uber/h3-js,https://github.com/uber/h3-js,945,9,2025-07-18T02:47:37Z,0,"h3-js provides a JavaScript version of H3, a hexagon-based geospatial indexing system."
chrisgrieser/shimmering-obsidian,https://github.com/chrisgrieser/shimmering-obsidian,944,1,2025-07-21T08:37:59Z,0,Alfred workflow with dozens of features for controlling your Obsidian vault.
sudheerj/ECMAScript-features,https://github.com/sudheerj/ECMAScript-features,943,0,2025-05-30T10:39:50Z,0,ECMAScript features cheatsheet
codeforequity-at/botium-speech-processing,https://github.com/codeforequity-at/botium-speech-processing,943,2,2025-07-24T03:52:55Z,0,Botium Speech Processing
microsoft/uf2,https://github.com/microsoft/uf2,941,20,2025-07-10T20:18:58Z,0,UF2 file format specification
emn178/js-sha256,https://github.com/emn178/js-sha256,941,7,2025-05-29T00:51:04Z,0,A simple SHA-256 / SHA-224 hash function for JavaScript supports UTF-8 encoding.
pistazie/cdk-dia,https://github.com/pistazie/cdk-dia,939,24,2025-06-22T11:54:23Z,0,Automated diagrams of CDK provisioned infrastructure
le-doux/bitsy,https://github.com/le-doux/bitsy,939,1,2025-07-17T21:22:26Z,0,"a little engine for little games, worlds, and stories"
layer5io/layer5,https://github.com/layer5io/layer5,938,136,2025-07-27T12:14:08Z,0,"Layer5, expect more from your infrastructure"
swiftyapp/swifty,https://github.com/swiftyapp/swifty,937,91,2025-05-09T14:07:13Z,1,🔑   Free Offline-first Password Manager 
asyncapi/generator,https://github.com/asyncapi/generator,937,36,2025-07-22T14:10:32Z,0,"Use your AsyncAPI definition to generate literally anything. Markdown documentation, Node.js code, HTML documentation, anything!"
newsdev/ai2html,https://github.com/newsdev/ai2html,935,66,2025-06-11T21:53:06Z,0,A script for Adobe Illustrator that converts your Illustrator artwork into an html page.
marcrobledo/RomPatcher.js,https://github.com/marcrobledo/RomPatcher.js,935,19,2025-04-30T09:39:29Z,0,An IPS/UPS/APS/BPS/RUP/PPF/xdelta ROM patcher made in HTML5.
paed01/bpmn-engine,https://github.com/paed01/bpmn-engine,933,1,2025-05-18T09:56:39Z,0,BPMN 2.0 execution engine. Open source javascript workflow engine.
MuYunyun/reactSPA,https://github.com/MuYunyun/reactSPA,932,6,2025-07-27T02:23:13Z,0,combination of react teconology stack
gdtool/cloudflare-workers-blog,https://github.com/gdtool/cloudflare-workers-blog,932,13,2025-06-09T07:58:40Z,0,A Blog Powered By Cloudflare Workers and KV 
midudev/tailwind-animations,https://github.com/midudev/tailwind-animations,931,2,2025-06-20T08:42:05Z,0,Easy peasy animations for your Tailwind project
traccar/traccar-web,https://github.com/traccar/traccar-web,931,112,2025-07-25T22:10:03Z,0,Traccar GPS Tracking System
nttgin/BGPalerter,https://github.com/nttgin/BGPalerter,928,3,2025-07-21T10:16:19Z,0,"BGP and RPKI monitoring tool. Pre-configured for real-time detection of visibility loss, RPKI invalid announcements, hijacks, ROA misconfiguration, and more."
morgan3d/quadplay,https://github.com/morgan3d/quadplay,927,0,2025-07-27T18:47:28Z,0,The quadplay✜ fantasy console
apify/proxy-chain,https://github.com/apify/proxy-chain,927,22,2025-06-02T20:21:50Z,1,"Node.js implementation of a proxy server (think Squid) with support for SSL, authentication and upstream proxy chaining."
wingkwong/leetcode-the-hard-way,https://github.com/wingkwong/leetcode-the-hard-way,927,25,2025-07-16T08:45:59Z,0,LeetCode The Hard Way - From Absolute Beginner to Quitter. Join Discord: https://discord.com/invite/Nqm4jJcyBf
duereg/js-algorithms,https://github.com/duereg/js-algorithms,926,2,2025-05-22T23:37:16Z,0,Javascript versions of classic software development algorithms
tektoncd/dashboard,https://github.com/tektoncd/dashboard,926,29,2025-07-22T09:38:18Z,0,A dashboard for Tekton!
qor/admin,https://github.com/qor/admin,924,117,2025-06-30T09:35:41Z,1,"Qor Admin - Instantly create a beautiful, cross platform, configurable Admin Interface and API for managing your data in minutes."
Dictionarry-Hub/profilarr,https://github.com/Dictionarry-Hub/profilarr,924,40,2025-06-22T05:05:03Z,0,Configuration Management Platform for Radarr/Sonarr
saimn/sigal,https://github.com/saimn/sigal,922,97,2025-07-20T10:36:31Z,0,yet another simple static gallery generator
OpenUserJS/OpenUserJS.org,https://github.com/OpenUserJS/OpenUserJS.org,919,80,2025-06-27T17:05:43Z,0,The home of FOSS user scripts.
igorshubovych/markdownlint-cli,https://github.com/igorshubovych/markdownlint-cli,919,22,2025-07-21T19:45:04Z,0,MarkdownLint Command Line Interface
prerender/prerender-node,https://github.com/prerender/prerender-node,918,40,2025-07-22T15:05:03Z,0,Express middleware for prerendering javascript-rendered pages on the fly for SEO
compdemocracy/polis,https://github.com/compdemocracy/polis,916,322,2025-07-27T19:05:11Z,0,:milky_way: Open Source AI for large scale open ended feedback
echo094/decode-js,https://github.com/echo094/decode-js,916,4,2025-06-27T15:48:53Z,0,JS混淆代码的AST分析工具 AST analysis tool for obfuscated JS code
saharan/OimoPhysics,https://github.com/saharan/OimoPhysics,915,15,2025-05-18T14:53:08Z,0,A cross-platform 3D physics engine
hapi-swagger/hapi-swagger,https://github.com/hapi-swagger/hapi-swagger,913,94,2025-06-02T18:31:18Z,0,A Swagger interface for hapi
mafintosh/pump,https://github.com/mafintosh/pump,913,19,2025-06-16T08:11:27Z,0,pipe streams together and close all of them if one of them closes
maxi1134/Home-Assistant-Config,https://github.com/maxi1134/Home-Assistant-Config,910,10,2025-07-16T22:52:52Z,0,This is my Smart-home Installation repository
endojs/endo,https://github.com/endojs/endo,910,499,2025-07-27T01:39:05Z,0,"Endo is a distributed secure JavaScript sandbox, based on SES"
barretlee/online-markdown,https://github.com/barretlee/online-markdown,909,42,2025-06-27T05:12:53Z,0,A online markdown converter specially for Wechat Public formatting.
alchemyplatform/create-web3-dapp,https://github.com/alchemyplatform/create-web3-dapp,906,11,2025-06-02T16:22:23Z,0,The complete toolbox to create web3 applications. 
gildas-lormeau/single-file-cli,https://github.com/gildas-lormeau/single-file-cli,906,49,2025-06-02T14:46:43Z,0,CLI tool for saving a faithful copy of a complete web page in a single HTML file (based on SingleFile)
secureCodeBox/secureCodeBox,https://github.com/secureCodeBox/secureCodeBox,904,87,2025-07-24T11:59:49Z,0,secureCodeBox (SCB) - continuous secure delivery out of the box 
HemmeligOrg/Hemmelig.app,https://github.com/HemmeligOrg/Hemmelig.app,903,33,2025-07-22T09:00:27Z,0,"Keep your sensitive information out of chat logs, emails, and more with encrypted secrets."
nextcloud/mail,https://github.com/nextcloud/mail,903,835,2025-07-27T17:18:54Z,0,💌 Mail app for Nextcloud
ChaokunHong/MetaScreener,https://github.com/ChaokunHong/MetaScreener,903,3,2025-05-26T14:16:07Z,0,AI-powered tool for efficient abstract and PDF screening in systematic reviews.
antfu/broz,https://github.com/antfu/broz,900,7,2025-07-03T05:28:04Z,0,"A simple, frameless browser for screenshots"
Robbendebiene/Gesturefy,https://github.com/Robbendebiene/Gesturefy,899,47,2025-07-27T15:00:07Z,0,"Navigate, operate, and browse faster with mouse gestures! A customizable Firefox mouse gesture add-on with a variety of different commands."
wxik/react-native-rich-editor,https://github.com/wxik/react-native-rich-editor,897,176,2025-07-22T09:13:34Z,0,"Lightweight React Native (JavaScript, H5) rich text editor"
ossf/wg-best-practices-os-developers,https://github.com/ossf/wg-best-practices-os-developers,894,74,2025-07-24T10:08:36Z,0,The Best Practices for OSS Developers working group is dedicated to raising awareness and education of secure code best practices for open source developers.
btw-so/btw,https://github.com/btw-so/btw,893,18,2025-07-17T04:19:19Z,0,Open source Medium alternative- set up your personal blog in minutes.
koajs/router,https://github.com/koajs/router,893,17,2025-07-01T18:57:28Z,0,Router middleware for Koa. Maintained by @forwardemail and @ladjs.
sindresorhus/p-retry,https://github.com/sindresorhus/p-retry,892,2,2025-05-06T14:50:36Z,0,Retry a promise-returning or async function
ruby/setup-ruby,https://github.com/ruby/setup-ruby,891,17,2025-07-26T08:59:11Z,0,An action to download a prebuilt Ruby and add it to the PATH in 5 seconds
nodemailer/smtp-server,https://github.com/nodemailer/smtp-server,890,38,2025-06-27T10:28:18Z,0,Create custom SMTP servers on the fly
atlassian/react-sweet-state,https://github.com/atlassian/react-sweet-state,889,25,2025-07-22T07:16:31Z,0,Shared state management solution for React
bmuschko/ckad-crash-course,https://github.com/bmuschko/ckad-crash-course,889,0,2025-07-16T14:41:26Z,0,In-depth and hands-on practice for acing the exam.
mattiasw/ExifReader,https://github.com/mattiasw/ExifReader,889,9,2025-07-24T16:55:42Z,0,A JavaScript Exif info parser.
webpack/analyse,https://github.com/webpack/analyse,887,45,2025-06-02T16:28:31Z,0,analyse web app for webpack stats
fluid-player/fluid-player,https://github.com/fluid-player/fluid-player,887,17,2025-07-25T09:49:01Z,0,Fluid Player - an open source VAST compliant HTML5 video player
fleetbase/fleetbase,https://github.com/fleetbase/fleetbase,884,11,2025-07-25T08:13:40Z,0,Modular logistics and supply chain operating system (LSOS)
tngoman/Store-POS,https://github.com/tngoman/Store-POS,883,44,2025-07-01T14:38:53Z,0,Point of Sale Desktop App built with Electron
typefully/minimal-twitter,https://github.com/typefully/minimal-twitter,882,28,2025-04-30T15:27:11Z,0,Minimal Theme for Twitter - Refine and Declutter Your Twitter Web Experience
HaschekSolutions/pictshare,https://github.com/HaschekSolutions/pictshare,881,14,2025-07-12T09:40:31Z,1,":camera: PictShare is an open source image, mp4, pastebin hosting service with a simple resizing and upload API that you can host yourself. :rice_scene:"
GNOME/gnome-shell,https://github.com/GNOME/gnome-shell,880,1,2025-07-27T20:20:28Z,0,Read-only mirror of https://gitlab.gnome.org/GNOME/gnome-shell
theophilusx/ssh2-sftp-client,https://github.com/theophilusx/ssh2-sftp-client,880,2,2025-06-23T05:02:21Z,0,a client for SSH2 SFTP 
proginosko/LeechBlockNG,https://github.com/proginosko/LeechBlockNG,879,60,2025-06-04T23:24:30Z,0,LeechBlock NG (Next Generation) for Firefox is a simple productivity tool designed to block those time-wasting sites that can suck the life out of your working day. All you need to do is specify which sites to block and when to block them.
massgravel/massgrave.dev,https://github.com/massgravel/massgrave.dev,876,0,2025-07-26T16:15:53Z,0,Documentation For Microsoft Activation Scripts (MAS)
JetBrains/mcp-jetbrains,https://github.com/JetBrains/mcp-jetbrains,876,31,2025-07-01T10:06:50Z,0,"A model context protocol server to work with JetBrains IDEs: IntelliJ, PyCharm, WebStorm, etc. Also, works with Android Studio"
dreampuf/GraphvizOnline,https://github.com/dreampuf/GraphvizOnline,876,6,2025-06-20T07:35:53Z,0,Let's Graphviz it online
expo/google-fonts,https://github.com/expo/google-fonts,873,11,2025-07-26T17:38:14Z,0,Use any of the 1000+ fonts (and their variants) from fonts.google.com in your Expo app.
tomayac/SVGcode,https://github.com/tomayac/SVGcode,873,7,2025-07-04T13:07:48Z,0,Convert color bitmap images to color SVG vector images.
FileNation/FileNation,https://github.com/FileNation/FileNation,872,28,2025-07-24T02:52:25Z,0,The simplest way to send your files around the world using IPFS. ✏️ 🗃
marwin1991/profile-technology-icons,https://github.com/marwin1991/profile-technology-icons,871,7,2025-07-24T09:27:20Z,0,📋 😄 Add icons to your GitHub profile using this generator  😄 📋
vatesfr/xen-orchestra,https://github.com/vatesfr/xen-orchestra,871,400,2025-07-25T15:51:24Z,0,The global orchestration solution to manage and backup XCP-ng and XenServer.
leviarista/github-profile-header-generator,https://github.com/leviarista/github-profile-header-generator,870,6,2025-07-17T16:45:41Z,0,A header image generator for your Github profile Readme
alfateam/orange-orm,https://github.com/alfateam/orange-orm,869,6,2025-07-27T18:16:49Z,0,The ultimate ORM for Node and Typescript
nemtsov/json-mask,https://github.com/nemtsov/json-mask,869,4,2025-06-05T18:14:09Z,0,"Tiny language and engine for selecting specific parts of a JS object, hiding the rest."
raphaelameaume/fragment,https://github.com/raphaelameaume/fragment,869,4,2025-06-30T09:41:38Z,0,[alpha] A web development environment for creative coding
ChromeDevTools/debugger-protocol-viewer,https://github.com/ChromeDevTools/debugger-protocol-viewer,867,29,2025-07-17T21:19:41Z,0,"DevTools Protocol API docs—its domains, methods, and events"
SuperMap/iClient-JavaScript,https://github.com/SuperMap/iClient-JavaScript,866,14,2025-07-25T05:41:01Z,0,"Modern GIS Web Client for JavaScript, based on Leaflet\OpenLayers\MapboxGL-JS\Classic(iClient8C), enhanced with ECharts\D3\MapV etc. Contributed by SuperMap & community."
UXPin/adele,https://github.com/UXPin/adele,866,25,2025-06-10T09:51:43Z,0,Adele - Design Systems Repository
nexus-js/ui,https://github.com/nexus-js/ui,866,44,2025-07-03T15:53:19Z,0,NexusUI: Web Audio Interfaces
slaylines/canvas-engines-comparison,https://github.com/slaylines/canvas-engines-comparison,866,17,2025-07-22T08:41:18Z,0,Performance comparison of different canvas rendering engines.
falling-sky/source,https://github.com/falling-sky/source,864,1,2025-07-20T15:14:33Z,0,"Main source for falling-sky.  Mostly  HTML, JavaScript."
code-dot-org/code-dot-org,https://github.com/code-dot-org/code-dot-org,863,363,2025-07-27T16:32:00Z,0,The code powering code.org and studio.code.org
steveseguin/social_stream,https://github.com/steveseguin/social_stream,860,162,2025-07-27T12:30:32Z,0,Consolidate your live social messaging streams and much more
qntm/base2048,https://github.com/qntm/base2048,857,0,2025-07-22T13:46:53Z,0,Binary encoding optimised for Twitter
agusmakmun/django-markdown-editor,https://github.com/agusmakmun/django-markdown-editor,857,37,2025-07-26T16:16:26Z,0,"🙌 Awesome Django Markdown Editor, supported for Bootstrap & Semantic-UI"
Yuheng0101/X,https://github.com/Yuheng0101/X,856,1,2025-07-03T09:29:49Z,0,"Some scripts about Quantumult X, Surge, Loon, Shadowrocket and NodeJS."
postcss/postcss-cli,https://github.com/postcss/postcss-cli,856,15,2025-06-23T14:28:50Z,0,CLI for postcss
gramps-project/gramps-web,https://github.com/gramps-project/gramps-web,854,68,2025-07-26T19:49:38Z,0,Open Source Online Genealogy System
domoritz/leaflet-locatecontrol,https://github.com/domoritz/leaflet-locatecontrol,853,27,2025-07-18T06:36:32Z,0,A leaflet control to geolocate the user.
leibnizli/hummingbird,https://github.com/leibnizli/hummingbird,853,11,2025-07-14T05:07:29Z,0,"A compression (jpg/png/webp/svg/gif/css/js/html/mp4/mov) App, it can convert different format pictures, support for macOS and Windows."
IanVS/eslint-nibble,https://github.com/IanVS/eslint-nibble,849,14,2025-07-18T23:18:20Z,0,"Ease into ESLint, by fixing one rule at a time"
goatandsheep/react-native-dotenv,https://github.com/goatandsheep/react-native-dotenv,848,9,2025-07-25T10:58:51Z,0,Load react native environment variables using import statements for multiple env files.
vvo/tzdb,https://github.com/vvo/tzdb,847,41,2025-07-25T17:29:14Z,0,"🕰 Simplified, grouped and always up to date list of time zones, with major cities"
AgregoreWeb/agregore-browser,https://github.com/AgregoreWeb/agregore-browser,846,85,2025-07-19T06:25:25Z,0,A minimal browser for the distributed web (Desktop version)
cozuya/secret-hitler,https://github.com/cozuya/secret-hitler,845,83,2025-06-11T01:12:10Z,0,A web adaptation of the social deduction board game Secret Hitler.  https://secrethitler.io
radiantearth/stac-spec,https://github.com/radiantearth/stac-spec,844,37,2025-07-01T13:48:27Z,0,SpatioTemporal Asset Catalog specification - making geospatial assets openly searchable and crawlable
dokieli/dokieli,https://github.com/dokieli/dokieli,844,182,2025-07-11T18:19:08Z,0,":bulb: dokieli is a clientside editor for decentralised article publishing, annotations and social interactions"
jobisoft/TbSync,https://github.com/jobisoft/TbSync,844,52,2025-07-23T15:26:56Z,0,"[Thunderbird Add-On] Central user interface to manage cloud accounts and to synchronize their contact, task and calendar information with Thunderbird"
nickfox/GpsTracker,https://github.com/nickfox/GpsTracker,843,63,2025-05-14T11:34:32Z,0,GPS Cell Phone Tracker is an open-source project that tracks android and ios cell phones and displays routes on a map.
wkylin/pro-react-admin,https://github.com/wkylin/pro-react-admin,841,3,2025-07-27T11:11:02Z,0,融合了 React 19、Webpack 5、React Router 7、Antd 5、Typescript 5、Tailwindcss 4 及 Fetch Api 的企业级中台基础模板，为大规模系统提供技术底座，助力企业数字化转型...
DavidWells/markdown-magic,https://github.com/DavidWells/markdown-magic,841,10,2025-06-26T23:29:22Z,0,"💫  Automatically format markdown files via comment blocks using source code, external data & custom transforms."
microrealestate/microrealestate,https://github.com/microrealestate/microrealestate,838,17,2025-06-08T09:43:17Z,0,This is an Open Source Real estate management system that helps landlords to manage their rentals and properties
creold/illustrator-scripts,https://github.com/creold/illustrator-scripts,836,1,2025-07-14T09:05:04Z,0,Some powerfull JSX scripts for extending Adobe Illustrator
strathausen/dracula,https://github.com/strathausen/dracula,836,18,2025-07-17T07:27:27Z,0,JavaScript layout and representation of connected graphs.
DumbWareio/DumbAssets,https://github.com/DumbWareio/DumbAssets,835,39,2025-07-25T20:03:49Z,0,A Stupid Simple Asset Tracker
zed-industries/zed-fonts,https://github.com/zed-industries/zed-fonts,834,9,2025-06-23T21:11:03Z,0,"The Zed Mono and Sans typefaces, custom built from Iosevka"
henrygd/bigpicture,https://github.com/henrygd/bigpicture,834,10,2025-06-13T21:24:53Z,0,"Lightweight JavaScript image / video viewer. Supports Youtube, Vimeo, etc."
dracula/visual-studio-code,https://github.com/dracula/visual-studio-code,833,12,2025-06-27T18:40:32Z,0,🧛🏻‍♂️ Dark theme for Visual Studio Code
rive-app/rive-wasm,https://github.com/rive-app/rive-wasm,831,47,2025-07-23T22:33:39Z,0,Wasm/JS runtime for Rive
wozulong/open-wegram-bot,https://github.com/wozulong/open-wegram-bot,831,8,2025-05-15T10:06:43Z,0,【零费用】一个让人呼吸顺畅的 Telegram 双向私聊机器人 🤖 / [Zero Cost] A Smooth-Operating Two-Way Private Messaging Telegram Bot 🤖 
templui/templui,https://github.com/templui/templui,828,4,2025-07-27T10:02:53Z,0,The UI Kit for templ
microsoft/generative-ai-with-javascript,https://github.com/microsoft/generative-ai-with-javascript,828,40,2025-07-22T08:16:25Z,0,Join a time-traveling adventure where you meet history’s legends while learning Generative AI technologies! ✨
coreui/coreui,https://github.com/coreui/coreui,827,13,2025-07-22T10:36:35Z,0,Open Source UI Kit built on top of Bootstrap 5 and plain JavaScript without any additional libraries like jQuery
sealninja/react-grid-system,https://github.com/sealninja/react-grid-system,827,41,2025-07-26T16:12:16Z,0, A powerful Bootstrap-like responsive grid system for React.
spohlenz/tinymce-rails,https://github.com/spohlenz/tinymce-rails,827,74,2025-07-21T12:24:22Z,0,Integration of TinyMCE with the Rails asset pipeline
BaekjoonHub/BaekjoonHub,https://github.com/BaekjoonHub/BaekjoonHub,827,14,2025-07-13T04:14:56Z,0,백준 자동 푸시 익스텐션(Auto Git Push for BOJ)
sdesalas/trifleJS,https://github.com/sdesalas/trifleJS,824,19,2025-07-05T13:59:16Z,0,Headless automation for Internet Explorer
ShaneIsrael/fireshare,https://github.com/ShaneIsrael/fireshare,824,12,2025-07-25T19:37:00Z,0,Self host your media and share with unique links
previm/previm,https://github.com/previm/previm,821,8,2025-06-04T15:52:22Z,0,"Realtime preview by Vim. (Markdown, reStructuredText, textile)"
FrankerFaceZ/FrankerFaceZ,https://github.com/FrankerFaceZ/FrankerFaceZ,820,520,2025-07-21T17:20:08Z,0,The Twitch Enhancement Suite. Get custom emotes and tons of new features you'll never want to go without.
dauxio/daux.io,https://github.com/dauxio/daux.io,817,15,2025-07-23T01:32:09Z,0,Daux.io is an documentation generator that uses a simple folder structure and Markdown files to create custom documentation on the fly. It helps you create great looking documentation in a developer friendly way.
spencermountain/wtf_wikipedia,https://github.com/spencermountain/wtf_wikipedia,817,52,2025-07-13T17:59:15Z,0,a pretty-committed wikipedia markup parser
circleci/circleci-docs,https://github.com/circleci/circleci-docs,814,27,2025-07-25T03:31:22Z,0,Documentation for CircleCI.
tech-shrimp/gemini-playground,https://github.com/tech-shrimp/gemini-playground,814,25,2025-07-02T06:08:37Z,0,"Deploy a Gemini multimodal chat website in 10 seconds, Severless!         只需准备一个Gemini API Key，10秒即可部署一个Gemini多模态对话的网站。"
pillarjs/send,https://github.com/pillarjs/send,810,22,2025-07-23T12:39:30Z,0,Streaming static file server with Range and conditional-GET support
