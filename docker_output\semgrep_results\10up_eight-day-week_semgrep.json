{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml", "start": {"line": 65, "col": 48, "offset": 1724}, "end": {"line": 65, "col": 51, "offset": 1727}}, {"path": "downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml", "start": {"line": 65, "col": 87, "offset": 1724}, "end": {"line": 65, "col": 90, "offset": 1727}}]], "message": "Syntax error at line downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml:65:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml", "spans": [{"file": "downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml", "start": {"line": 65, "col": 48, "offset": 1724}, "end": {"line": 65, "col": 51, "offset": 1727}}, {"file": "downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml", "start": {"line": 65, "col": 87, "offset": 1724}, "end": {"line": 65, "col": 90, "offset": 1727}}]}], "paths": {"scanned": ["downloaded_repos/10up_eight-day-week/.bowerrc", "downloaded_repos/10up_eight-day-week/.gitattributes", "downloaded_repos/10up_eight-day-week/.github/CODEOWNERS", "downloaded_repos/10up_eight-day-week/.github/workflows/build-release-zip.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/close-stale-issues.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/dependency-review.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/php-standards-scrutinizer.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/push-asset-readme-update.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/push-deploy.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/repo-automator.yml", "downloaded_repos/10up_eight-day-week/.github/workflows/wordpress-version-checker.yml", "downloaded_repos/10up_eight-day-week/.gitignore", "downloaded_repos/10up_eight-day-week/.jshintrc", "downloaded_repos/10up_eight-day-week/.phpcompat.xml", "downloaded_repos/10up_eight-day-week/.phpcs.xml", "downloaded_repos/10up_eight-day-week/.wordpress-org/banner-1544x500-rtl.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/banner-1544x500.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/banner-772x250-rtl.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/banner-772x250.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/blueprints/blueprint.json", "downloaded_repos/10up_eight-day-week/.wordpress-org/blueprints/demo-data.xml", "downloaded_repos/10up_eight-day-week/.wordpress-org/icon-128x128.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/icon-256x256.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/icon-256x256.svg", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-1.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-2.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-3.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-4.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-5.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-6.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-7.png", "downloaded_repos/10up_eight-day-week/.wordpress-org/screenshot-8.png", "downloaded_repos/10up_eight-day-week/.wordpress-version-checker.json", "downloaded_repos/10up_eight-day-week/.wp-env.json", "downloaded_repos/10up_eight-day-week/CHANGELOG.md", "downloaded_repos/10up_eight-day-week/CODE_OF_CONDUCT.md", "downloaded_repos/10up_eight-day-week/CONTRIBUTING.md", "downloaded_repos/10up_eight-day-week/CREDITS.md", "downloaded_repos/10up_eight-day-week/Gruntfile.js", "downloaded_repos/10up_eight-day-week/LICENSE.md", "downloaded_repos/10up_eight-day-week/README.md", "downloaded_repos/10up_eight-day-week/assets/css/sass/_article-export.scss", "downloaded_repos/10up_eight-day-week/assets/css/sass/style.scss", "downloaded_repos/10up_eight-day-week/assets/css/style.css", "downloaded_repos/10up_eight-day-week/assets/css/style.min.css", "downloaded_repos/10up_eight-day-week/assets/js/scripts.js", "downloaded_repos/10up_eight-day-week/assets/js/src/scripts.js", "downloaded_repos/10up_eight-day-week/composer.json", "downloaded_repos/10up_eight-day-week/composer.lock", "downloaded_repos/10up_eight-day-week/eight-day-week.php", "downloaded_repos/10up_eight-day-week/includes/functions/admin-menu-page.php", "downloaded_repos/10up_eight-day-week/includes/functions/articles.php", "downloaded_repos/10up_eight-day-week/includes/functions/core.php", "downloaded_repos/10up_eight-day-week/includes/functions/plugins/article-byline.php", "downloaded_repos/10up_eight-day-week/includes/functions/plugins/article-count.php", "downloaded_repos/10up_eight-day-week/includes/functions/plugins/article-export.php", "downloaded_repos/10up_eight-day-week/includes/functions/plugins/article-status.php", "downloaded_repos/10up_eight-day-week/includes/functions/plugins/issue-publication.php", "downloaded_repos/10up_eight-day-week/includes/functions/plugins/issue-status.php", "downloaded_repos/10up_eight-day-week/includes/functions/print-issue-columns.php", "downloaded_repos/10up_eight-day-week/includes/functions/print-issue.php", "downloaded_repos/10up_eight-day-week/includes/functions/sections.php", "downloaded_repos/10up_eight-day-week/includes/functions/taxonomies.php", "downloaded_repos/10up_eight-day-week/includes/functions/user-roles.php", "downloaded_repos/10up_eight-day-week/includes/lib/zip.lib.php", "downloaded_repos/10up_eight-day-week/package-lock.json", "downloaded_repos/10up_eight-day-week/package.json", "downloaded_repos/10up_eight-day-week/plugins.php", "downloaded_repos/10up_eight-day-week/readme.txt", "downloaded_repos/10up_eight-day-week/vip-compat.php"], "skipped": [{"path": "downloaded_repos/10up_eight-day-week/.github/workflows/cypress.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/10up_eight-day-week/assets/js/scripts.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/bin/initialize.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/bin/set-wp-config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/bin/wp-cli.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/e2e/admin.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/e2e/article-status.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/e2e/issue-status.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/e2e/print.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/e2e/publications.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/e2e/user-custom-role.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/fixtures/example.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/support/commands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/support/e2e.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/support/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/10up_eight-day-week/tests/cypress/tsconfig.json", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7801620960235596, "profiling_times": {"config_time": 6.987405300140381, "core_time": 3.6286139488220215, "ignores_time": 0.002555370330810547, "total_time": 10.619478225708008}, "parsing_time": {"total_time": 1.7085258960723877, "per_file_time": {"mean": 0.04745905266867743, "std_dev": 0.010445139963469689}, "very_slow_stats": {"time_ratio": 0.6462343343505799, "count_ratio": 0.08333333333333333}, "very_slow_files": [{"fpath": "downloaded_repos/10up_eight-day-week/assets/js/scripts.js", "ftime": 0.30379605293273926}, {"fpath": "downloaded_repos/10up_eight-day-week/assets/js/src/scripts.js", "ftime": 0.30602598190307617}, {"fpath": "downloaded_repos/10up_eight-day-week/package-lock.json", "ftime": 0.49428606033325195}]}, "scanning_time": {"total_time": 7.616645336151123, "per_file_time": {"mean": 0.042790142337927654, "std_dev": 0.02264840618226413}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.015937566757202, "per_file_and_rule_time": {"mean": 0.008021110549886174, "std_dev": 0.000506417296181137}, "very_slow_stats": {"time_ratio": 0.24223813275810235, "count_ratio": 0.013297872340425532}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/10up_eight-day-week/includes/functions/plugins/article-export.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.1234731674194336}, {"fpath": "downloaded_repos/10up_eight-day-week/assets/js/src/scripts.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.13901519775390625}, {"fpath": "downloaded_repos/10up_eight-day-week/assets/js/scripts.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.14467597007751465}, {"fpath": "downloaded_repos/10up_eight-day-week/assets/js/src/scripts.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1586148738861084}, {"fpath": "downloaded_repos/10up_eight-day-week/assets/js/scripts.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1647958755493164}]}, "tainting_time": {"total_time": 0.9868850708007812, "per_def_and_rule_time": {"mean": 0.0007591423621544476, "std_dev": 8.97256508782792e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}