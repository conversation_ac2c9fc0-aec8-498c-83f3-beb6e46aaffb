{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/.gitignore", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/CONTRIBUTING.md", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/LICENSE", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/README.md", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/dataform.json", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/def/bid_strategy_roas.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/def/dim_ads.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/def/dim_customer.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/def/fact_ad_conversion_daily.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/def/fact_ad_performance_daily.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/product_v1/ad_conversions.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/product_v1/ad_performance.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/product_v1/ad_performance_conversions.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/update/update_dim_ads.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/update/update_dim_customer", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/update/update_fact_ad_conversion_daily.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/update/update_fact_ad_performance_daily.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/update/update_fact_campaign_strategies_daily.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/views/ad.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/views/ad_conversion_stats.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/views/ad_stats.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/views/adgroup.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/views/campaign.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/views/campaign_strategies.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/ads_domain/google_ads/views/customer.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/backfill/collected_traffic_source_backfill.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/browser.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/collected_traffic_source.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/device_type.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/event.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/ga4_events.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/ga4_pseudo_users.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/location.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/normalized_device_type.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/pseudo_user_privacy_info.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/pseudo_users.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/session.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/base/traffic_source.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/aggregated_vbb.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/collected_traffic_source.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/device.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/event.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/event_page.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/event_weekly_metrics.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/get_weekly_percent_change.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/latest_event_per_user_last_24_hours.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/latest_event_per_user_last_72_hours.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/latest_event_per_user_last_event_day.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/location.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/normalized_device.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/page_session.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/page_session_daily_metrics.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/page_views.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/pageview_weekly_metrics.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/session.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/session_date.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/session_device_daily_metrics.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/session_location_daily_metrics.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/session_stats.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/session_weekly_metrics.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/traffic_source.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/product_v1/unique_page_views.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/source/ga4_events_export.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/definitions/digital_analytics_domain/source/ga4_pseudo_users_export.sqlx", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/includes/constants.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/includes/docs.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/includes/functions.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/includes/ga4.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/includes/sql.js", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/mde_repo_sync.sh", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/package-lock.json", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/package.json", "downloaded_repos/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform/renovate.json"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.901177167892456, "profiling_times": {"config_time": 7.452815771102905, "core_time": 2.821244239807129, "ignores_time": 0.001844644546508789, "total_time": 10.277398586273193}, "parsing_time": {"total_time": 0.30484938621520996, "per_file_time": {"mean": 0.021774956158229282, "std_dev": 6.79757534805842e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.0817971229553223, "per_file_time": {"mean": 0.0067612320184707655, "std_dev": 0.00024883612972877763}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.16882610321044922, "per_file_and_rule_time": {"mean": 0.0024118014744349886, "std_dev": 9.42363548602418e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.011718988418579102, "per_def_and_rule_time": {"mean": 0.0009014606475830079, "std_dev": 1.1194537836882562e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}