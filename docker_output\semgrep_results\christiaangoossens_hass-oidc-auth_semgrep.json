{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/oidc_client.py", "start": {"line": 215, "col": 17, "offset": 7243}, "end": {"line": 221, "col": 18, "offset": 7513}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Error: Token could not be obtained (%s, %s), \"\n                    + \"did you forget the client_secret? Server returned: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/oidc_client.py", "start": {"line": 223, "col": 17, "offset": 7548}, "end": {"line": 223, "col": 76, "offset": 7607}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Unexpected error exchanging token: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/oidc_client.py", "start": {"line": 261, "col": 17, "offset": 9168}, "end": {"line": 265, "col": 18, "offset": 9364}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"ID Token received signed with the wrong algorithm: %s, expected %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/oidc_client.py", "start": {"line": 533, "col": 13, "offset": 20937}, "end": {"line": 533, "col": 86, "offset": 21010}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Failed to complete token flow, returning None. (%s)\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/provider.py", "start": {"line": 278, "col": 19, "offset": 9695}, "end": {"line": 278, "col": 79, "offset": 9755}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/loader.py", "start": {"line": 57, "col": 15, "offset": 1906}, "end": {"line": 57, "col": 75, "offset": 1966}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/base.html", "start": {"line": 9, "col": 5, "offset": 256}, "end": {"line": 9, "col": 56, "offset": 307}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/finish.html", "start": {"line": 10, "col": 9, "offset": 289}, "end": {"line": 16, "col": 16, "offset": 694}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 128}}, {"path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/christiaangoossens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html:1:\n `{% extends \"base.html\" %}\n{% block title %}Oops!{% endblock %}\n{% block head %}\n{{ super() }}\n{% endblock %}\n{% block content %}` was unexpected", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html", "spans": [{"file": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 128}}, {"file": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 133}}, {"path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/christiaangoossens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html:1:\n `{% extends \"base.html\" %}\n{% block title %}OIDC Login{% endblock %}\n{% block head %}\n{{ super() }}\n{% endblock %}\n{% block content %}` was unexpected", "path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html", "spans": [{"file": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 133}}, {"file": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/christiaangoossens_hass-oidc-auth/.github/workflows/hacs.yaml", "downloaded_repos/christiaangoossens_hass-oidc-auth/.github/workflows/hassfest.yaml", "downloaded_repos/christiaangoossens_hass-oidc-auth/.github/workflows/lint.yaml", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/.gitignore", "downloaded_repos/christiaangoossens_hass-oidc-auth/.pylintrc", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/.python-version", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/CODE_OF_CONDUCT.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/CONTRIBUTING.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/FUNDING.yml", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/LICENSE.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/README.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/SECURITY.md", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/__init__.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/config.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/endpoints/callback.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/endpoints/finish.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/endpoints/injected_auth_page.py", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/custom_components/auth_oidc/endpoints/redirect.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/endpoints/welcome.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/helpers.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/manifest.json", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/oidc_client.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/provider.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/static/injection.js", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/stores/code_store.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/types.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/loader.py", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/base.html", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/finish.html", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/docs/configuration.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/docs/provider-configurations/authelia.md", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/docs/provider-configurations/authentik.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/docs/provider-configurations/kanidm.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/docs/provider-configurations/microsoft-entra.md", "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/docs/provider-configurations/pocket-id.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/docs/usage.md", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/hacs.json", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/logo.png", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/pyproject.toml", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/renovate.json", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/requirements-dev.lock", "downloaded_repos/christia<PERSON>oossens_hass-oidc-auth/requirements.lock"], "skipped": [{"path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/error.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/christia<PERSON><PERSON>sens_hass-oidc-auth/custom_components/auth_oidc/views/templates/welcome.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.2465221881866455, "profiling_times": {"config_time": 6.680654048919678, "core_time": 3.765272617340088, "ignores_time": 0.003366231918334961, "total_time": 10.45055079460144}, "parsing_time": {"total_time": 0.5638971328735352, "per_file_time": {"mean": 0.024517266646675438, "std_dev": 0.00031855590443043033}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.219926595687866, "per_file_time": {"mean": 0.03734448314768023, "std_dev": 0.008675611630033615}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.2599506378173828, "per_file_and_rule_time": {"mean": 0.003818032235810251, "std_dev": 7.552004720371097e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.5131559371948242, "per_def_and_rule_time": {"mean": 0.0006962767126117018, "std_dev": 2.7106498235813237e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}