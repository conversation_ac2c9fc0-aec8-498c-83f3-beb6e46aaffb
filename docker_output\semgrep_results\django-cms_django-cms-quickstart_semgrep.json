{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/django-cms_django-cms-quickstart/Dockerfile", "start": {"line": 14, "col": 1, "offset": 256}, "end": {"line": 14, "col": 50, "offset": 305}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD uwsgi --http=0.0.0.0:80 --module=backend.wsgi", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "start": {"line": 1, "col": 16, "offset": 0}, "end": {"line": 1, "col": 85, "offset": 69}}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "start": {"line": 10, "col": 81, "offset": 0}, "end": {"line": 10, "col": 86, "offset": 5}}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "start": {"line": 34, "col": 10, "offset": 0}, "end": {"line": 34, "col": 28, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html:1:\n `{% load cms_tags menu_tags sekizai_tags static i18n %}{% spaceless %}` was unexpected", "path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "spans": [{"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "start": {"line": 1, "col": 16, "offset": 0}, "end": {"line": 1, "col": 85, "offset": 69}}, {"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "start": {"line": 10, "col": 81, "offset": 0}, "end": {"line": 10, "col": 86, "offset": 5}}, {"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "start": {"line": 34, "col": 10, "offset": 0}, "end": {"line": 34, "col": 28, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 21, "offset": 75}}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 4, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 34}}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 8, "col": 19, "offset": 33}}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html:1:\n `{% extends \"base.html\" %}{% load cms_tags menu_tags %}\n{% block base_css %}` was unexpected", "path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "spans": [{"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 21, "offset": 75}}, {"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 4, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 34}}, {"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 8, "col": 19, "offset": 33}}, {"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 66, "offset": 233}}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html", "start": {"line": 6, "col": 105, "offset": 0}, "end": {"line": 7, "col": 13, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html:1:\n `{% load i18n menu_tags cache %}\n{% for child in children %}\n    {% if child.get_menu_title|first == ' ' %}\n        <div class=\"dropdown-divider\"></div>\n    {% endif %}\n    <a class=\"dropdown-item\" href=\"{{ child.get_absolute_url }}\">` was unexpected", "path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html", "spans": [{"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 66, "offset": 233}}, {"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html", "start": {"line": 6, "col": 105, "offset": 0}, "end": {"line": 7, "col": 13, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 32, "offset": 78}}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html", "start": {"line": 12, "col": 5, "offset": 0}, "end": {"line": 13, "col": 19, "offset": 31}}]], "message": "Syntax error at line downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html:1:\n `{% load i18n menu_tags cache %}{% spaceless %}\n    {% for child in children %}` was unexpected", "path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html", "spans": [{"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 32, "offset": 78}}, {"file": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html", "start": {"line": 12, "col": 5, "offset": 0}, "end": {"line": 13, "col": 19, "offset": 31}}]}], "paths": {"scanned": ["downloaded_repos/django-cms_django-cms-quickstart/.dockerignore", "downloaded_repos/django-cms_django-cms-quickstart/.env-local", "downloaded_repos/django-cms_django-cms-quickstart/.github/FUNDING.yml", "downloaded_repos/django-cms_django-cms-quickstart/.github/workflows/docker.yml", "downloaded_repos/django-cms_django-cms-quickstart/.gitignore", "downloaded_repos/django-cms_django-cms-quickstart/Dockerfile", "downloaded_repos/django-cms_django-cms-quickstart/LICENSE", "downloaded_repos/django-cms_django-cms-quickstart/README.rst", "downloaded_repos/django-cms_django-cms-quickstart/backend/__init__.py", "downloaded_repos/django-cms_django-cms-quickstart/backend/asgi.py", "downloaded_repos/django-cms_django-cms-quickstart/backend/settings.py", "downloaded_repos/django-cms_django-cms-quickstart/backend/static/django-cms-logo.png", "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html", "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html", "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/minimal.html", "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/whitenoise-static-files-demo.html", "downloaded_repos/django-cms_django-cms-quickstart/backend/urls.py", "downloaded_repos/django-cms_django-cms-quickstart/backend/wsgi.py", "downloaded_repos/django-cms_django-cms-quickstart/compose.yaml", "downloaded_repos/django-cms_django-cms-quickstart/manage.py", "downloaded_repos/django-cms_django-cms-quickstart/requirements.in", "downloaded_repos/django-cms_django-cms-quickstart/requirements.txt"], "skipped": [{"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/bootstrap5.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/dropdown.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/django-cms_django-cms-quickstart/backend/templates/menu/menu.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.6043620109558105, "profiling_times": {"config_time": 6.616859197616577, "core_time": 3.49656081199646, "ignores_time": 0.001974344253540039, "total_time": 10.117040395736694}, "parsing_time": {"total_time": 0.2536170482635498, "per_file_time": {"mean": 0.01811550344739642, "std_dev": 3.632399769640193e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.9291977882385254, "per_file_time": {"mean": 0.01451871544122696, "std_dev": 0.0007732479839250293}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.16062426567077637, "per_file_and_rule_time": {"mean": 0.001100166203224496, "std_dev": 6.688331170193234e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.007336616516113281, "per_def_and_rule_time": {"mean": 0.0006669651378284802, "std_dev": 5.4877500964015e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}