{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/main/basic/index.js", "start": {"line": 30, "col": 21, "offset": 867}, "end": {"line": 30, "col": 33, "offset": 879}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/main/basic/index.js", "start": {"line": 35, "col": 34, "offset": 988}, "end": {"line": 35, "col": 46, "offset": 1000}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/modules/handler/electron/index.js", "start": {"line": 38, "col": 21, "offset": 1080}, "end": {"line": 38, "col": 33, "offset": 1092}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/modules/handler/electron/index.js", "start": {"line": 46, "col": 34, "offset": 1294}, "end": {"line": 46, "col": 46, "offset": 1306}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/modules/main/electron/index.js", "start": {"line": 30, "col": 21, "offset": 1054}, "end": {"line": 30, "col": 33, "offset": 1066}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/modules/main/electron/index.js", "start": {"line": 40, "col": 34, "offset": 1296}, "end": {"line": 40, "col": 46, "offset": 1308}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/modules/tray/electron/icon.js", "start": {"line": 9, "col": 30, "offset": 226}, "end": {"line": 9, "col": 42, "offset": 238}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/greymass_anchor/app/shared/reducers/producers.js", "start": {"line": 72, "col": 14, "offset": 1915}, "end": {"line": 74, "col": 9, "offset": 1972}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/app/shared/store/shared/i18n.js", "start": {"line": 32, "col": 29, "offset": 858}, "end": {"line": 32, "col": 41, "offset": 870}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/greymass_anchor/app/shared/utils/Anchor/Wallet.js", "start": {"line": 46, "col": 21, "offset": 1210}, "end": {"line": 46, "col": 55, "offset": 1244}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/webpack.config.base.js", "start": {"line": 16, "col": 44, "offset": 588}, "end": {"line": 16, "col": 73, "offset": 617}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/greymass_anchor/webpack.config.base.js", "start": {"line": 20, "col": 42, "offset": 716}, "end": {"line": 20, "col": 75, "offset": 749}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "path": "downloaded_repos/greymass_anchor/webpack.config.renderer.dev.js", "start": {"line": 288, "col": 9, "offset": 6948}, "end": {"line": 292, "col": 11, "offset": 7079}, "extra": {"message": "Found '$SPAWN' with '{shell: true}'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use '{shell: false}' instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.spawn-shell-true.spawn-shell-true", "shortlink": "https://sg.run/Wgeo"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Lexical error", "message": "Lexical error at line downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/transaction.json:18:\n WIERD end of file in quoted string", "path": "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/transaction.json"}, {"code": 3, "level": "warn", "type": "Lexical error", "message": "Lexical error at line downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/rex.json:35:\n unrecognised symbol, in token rule:�", "path": "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/rex.json"}, {"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/greymass_anchor/app/shared/components/Tools/Button/CreateAccount.js:1:\n `// @flow\nimport React, { Component } from 'react';\nimport { withTranslation } from 'react-i18next';\n\nimport GlobalTransactionModal from '../../Global/Transaction/Modal';\nimport ToolsFormCreateAccount from '../Form/CreateAccount';\nimport ToolsFormCreateBitsharesEosAccount from '../Form/CreateBitsharesEosAccount';\n\nclass ToolsButtonCreateAccount extends Component<Props> {\n  props: Props;\n\n  render() {\n    const {\n      account,\n      actions,\n      balance,\n      blockExplorers,\n      connection,\n      globals,\n      settings,\n      system,\n      t\n    } = this.props;\n\n    return (\n      <GlobalTransactionModal\n        actionName=\"CREATEACCOUNT\"\n        actions={actions}\n        blockExplorers={blockExplorers}\n        button={{\n          color: 'blue',\n          content: t('tools_button_create_account'),\n          icon: 'share square'\n        }}\n        content={ connection.chain === \"BEOS\" ? (\n          <ToolsFormCreateBitsharesEosAccount\n            account={account}\n            actions={actions}\n            balance={balance}\n            connection={connection}\n            contacts={settings.contacts}\n            globals={globals}\n            key=\"CreateAccountForm\"\n            system={system}\n          /> : (\n            <ToolsFormCreateAccount\n            account={account}\n            actions={actions}\n            balance={balance}\n            connection={connection}\n            contacts={settings.contacts}\n            globals={globals}\n            key=\"CreateAccountForm\"\n            system={system}\n          />\n          )\n        )}\n        icon=\"share square\"\n        title={t('tools_create_account_header')}\n        settings={settings}\n        size=\"large\"\n        system={system}\n      />\n    );\n  }\n}\n\nexport default withTranslation('tools')(ToolsButtonCreateAccount);\n` was unexpected", "path": "downloaded_repos/greymass_anchor/app/shared/components/Tools/Button/CreateAccount.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 87, "col": 30, "offset": 0}, "end": {"line": 87, "col": 38, "offset": 8}}, {"path": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 92, "col": 12, "offset": 0}, "end": {"line": 92, "col": 13, "offset": 1}}, {"path": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 101, "col": 9, "offset": 0}, "end": {"line": 103, "col": 8, "offset": 39}}, {"path": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 158, "col": 1, "offset": 0}, "end": {"line": 158, "col": 2, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js:87:\n `current.` was unexpected", "path": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "spans": [{"file": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 87, "col": 30, "offset": 0}, "end": {"line": 87, "col": 38, "offset": 8}}, {"file": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 92, "col": 12, "offset": 0}, "end": {"line": 92, "col": 13, "offset": 1}}, {"file": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 101, "col": 9, "offset": 0}, "end": {"line": 103, "col": 8, "offset": 39}}, {"file": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "start": {"line": 158, "col": 1, "offset": 0}, "end": {"line": 158, "col": 2, "offset": 1}}]}], "paths": {"scanned": ["downloaded_repos/greymass_anchor/.circleci/config.yml", "downloaded_repos/greymass_anchor/.dockerignore", "downloaded_repos/greymass_anchor/.editorconfig", "downloaded_repos/greymass_anchor/.eslintignore", "downloaded_repos/greymass_anchor/.eslintrc", "downloaded_repos/greymass_anchor/.flowconfig", "downloaded_repos/greymass_anchor/.gitattributes", "downloaded_repos/greymass_anchor/.github/ISSUE_TEMPLATE/bug_report.yaml", "downloaded_repos/greymass_anchor/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/greymass_anchor/.github/ISSUE_TEMPLATE/feature_request.yaml", "downloaded_repos/greymass_anchor/.gitignore", "downloaded_repos/greymass_anchor/.prettierrc", "downloaded_repos/greymass_anchor/.stylelintrc", "downloaded_repos/greymass_anchor/.vscode/settings.json", "downloaded_repos/greymass_anchor/LICENSE", "downloaded_repos/greymass_anchor/README.md", "downloaded_repos/greymass_anchor/app/.eslintrc", "downloaded_repos/greymass_anchor/app/main/basic/index.js", "downloaded_repos/greymass_anchor/app/main/main.dev.js", "downloaded_repos/greymass_anchor/app/modules/basic/index.js", "downloaded_repos/greymass_anchor/app/modules/basic/routes.js", "downloaded_repos/greymass_anchor/app/modules/handler/actions/uri.js", "downloaded_repos/greymass_anchor/app/modules/handler/actions/whitelist.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Broadcast.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Callback.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Cancel.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Complete.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Download.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Hardware/Ledger.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Identity.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Recreate.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Share.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Sign.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/SignBroadcast.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Actions/Unlock.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Placeholder/Transaction/Action.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Review/Actions.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Review/Fee.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Review/Overview.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Transaction/Action/Fuel/Fee.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Transaction/Action/Fuel.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Transaction/Action/ResourceProvider.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Fragment/Transaction/Action.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Header.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Review/Controls.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/Share.js", "downloaded_repos/greymass_anchor/app/modules/handler/components/error.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Prompt.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Root.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Broadcast.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Callback.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Error.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Expired.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Forbidden.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Hardware/Ledger.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Identity.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/NotConfigured.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Review.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage/Success.js", "downloaded_repos/greymass_anchor/app/modules/handler/containers/Stage.js", "downloaded_repos/greymass_anchor/app/modules/handler/electron/index.js", "downloaded_repos/greymass_anchor/app/modules/handler/index.js", "downloaded_repos/greymass_anchor/app/modules/main/actions/account.js", "downloaded_repos/greymass_anchor/app/modules/main/actions/default-token.js", "downloaded_repos/greymass_anchor/app/modules/main/actions/navigation.js", "downloaded_repos/greymass_anchor/app/modules/main/components/Account/Actions/Resources.js", "downloaded_repos/greymass_anchor/app/modules/main/components/Overview/Menu.js", "downloaded_repos/greymass_anchor/app/modules/main/components/Overview/Sidebar/Prompt.js", "downloaded_repos/greymass_anchor/app/modules/main/components/Overview/Sidebar/Support.js", "downloaded_repos/greymass_anchor/app/modules/main/components/Overview/Sidebar/Update.js", "downloaded_repos/greymass_anchor/app/modules/main/components/Overview/Table/Header.js", "downloaded_repos/greymass_anchor/app/modules/main/components/Overview/Table.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Content.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/ContentError.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Index.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Menu.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Navigation/Governance.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Navigation/Wallet.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Root.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Account/Header.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Account/Overview/Ram/Buy.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Account/Overview/Ram/Sell.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Account/Overview/Ram.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Account/Overview/Resource.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Account/Overview.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Account.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/ColdWallet.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/DevTest.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Disabled.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Governance/Disabled.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Governance/Producers.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Governance/Proposals.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Governance/Proxies.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Governance.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Accounts.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Initialize.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Password.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Account.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Backup.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Code.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Create.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/AccountName.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/CodeError.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Complete.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Confirm.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Generating.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Ledger/Options/LedgerAll.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Ledger/Options/LedgerRecover.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Ledger/Options/LedgerUse.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Ledger/Options.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Ledger/Setup.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Ledger/Verify.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Ledger.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/Account.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/EncryptionWords.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/Manual/AccountName.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/Manual/MnemonicWords.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/Manual/Network.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/Manual/Words.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/Manual.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Recover/Scan.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Words/Complete.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Words/Confirm.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Words/List.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Words/Wordlist.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Words/Write.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Words.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Import.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Recover.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/SigningRequestPrompt.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Upgrade.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Overview/Sidebar/Backup.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Overview/Sidebar.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Overview.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Pending.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/ResourceProviders/Overview.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/ResourceProviders.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Settings.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tests.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/ABICache.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Accounts.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Airgrabs.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/ApiPing.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/ApiTrafficLog.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/BidName.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Blockchains.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/ChainState.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Contacts.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/CreateAccount.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/CrosschainTransfer.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/CustomTokens.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Delegations.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Dfuse.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Display.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/GlobalState.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Home.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/KeyConverter.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/KeyGenerator.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/KeyValidator.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Keys.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Ledger.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/LinkService.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Permissions.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Proxy.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Recommendations.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/ResetApplication.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Sessions.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/SmartContracts.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/WalletState.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools/Wallets.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Tools.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Version.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Wallet/Ram.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Wallet/Stake.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Wallet/Transfer.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Wallet.js", "downloaded_repos/greymass_anchor/app/modules/main/containers/Sidebar.js", "downloaded_repos/greymass_anchor/app/modules/main/electron/index.js", "downloaded_repos/greymass_anchor/app/modules/main/index.js", "downloaded_repos/greymass_anchor/app/modules/main/routes.js", "downloaded_repos/greymass_anchor/app/modules/tray/electron/icon.js", "downloaded_repos/greymass_anchor/app/modules/tray/electron/index.js", "downloaded_repos/greymass_anchor/app/package.json", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/mac/icon.icns", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/1024x1024.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/128x128.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/16x16.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/24x24.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/256x256.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/32x32.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/<EMAIL>", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/48x48.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/512x512.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/png/64x64.png", "downloaded_repos/greymass_anchor/app/renderer/assets/icons/win/icon.ico", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-blue-bg.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-blue.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-dark-bg.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-dark-text.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-full-blue.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-full-blue.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-icon-2.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-icon-3.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-icon-4.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-icon.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-light-bg.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-light-bg.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-logo-blue.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-logo-white.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-logo.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-shape-color.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-shape.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-solid.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-text-blue.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-text-light.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-text-white.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-text.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/anchor-white.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/beos-logo.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/beos.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/bos.jpg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/bos.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/certificate.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/eos-logo.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/eos-text.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/eos.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/eos.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/europechain.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/europechain.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fio.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/fuel-square-fulllogo.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/fuel-square-logo-grey.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/fuel-square-logo.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/greymassfuel-horizontal-light.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/greymassfuel-horizontal.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/greymassfuel-logo.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/greymassfuel-square-light.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/greymassfuel-square.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/greymassfuel-text-light.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/fuel/greymassfuel-text.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/geometric-background.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/geometric-stripe.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/greymass-horizontal.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/greymass-logo.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/greymass-logoonly.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/greymass-vertical.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/greymass.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/greymass_logo_only.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/greymasstext.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/insights.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/jungle.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/keybase.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/ledger.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/libre.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/lynx.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/meetone.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/meetone.svg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/placeholder.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/print-arrow.jpg", "downloaded_repos/greymass_anchor/app/renderer/assets/images/proton-testnet.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/proton.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/remme.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/steem.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/telos.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/ux.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/wax.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/wechat.png", "downloaded_repos/greymass_anchor/app/renderer/assets/images/worbli.png", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/de-DE/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/el-GR/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/fuel.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/handler.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/main.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/rex.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/wax.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/en-US/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/es-ES/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/et-EE/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/basic.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/fuel.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/handler.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/main.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/rex.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/id-ID/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/it-IT/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ja-JP/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/main.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ru-RU/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/about.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/actions.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/app.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/beos_withdraw.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/coldwallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/common.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/constitution.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/contract.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/contracts.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/crosschaintransfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/global.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/ledger.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/menu.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/ping.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/producers.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/ram.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/recommendations.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/stake.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/tools.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/transaction.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/transfer.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/wallet.json", "downloaded_repos/greymass_anchor/app/renderer/assets/locales/zh-CN/welcome.json", "downloaded_repos/greymass_anchor/app/renderer/assets/stylesheets/loading-widget.css", "downloaded_repos/greymass_anchor/app/renderer/basic/index.html", "downloaded_repos/greymass_anchor/app/renderer/handler/index.html", "downloaded_repos/greymass_anchor/app/renderer/main/index.html", "downloaded_repos/greymass_anchor/app/renderer/tray/index.html", "downloaded_repos/greymass_anchor/app/renderer/tray/index.js", "downloaded_repos/greymass_anchor/app/renderer/tray/routes.js", "downloaded_repos/greymass_anchor/app/shared/actions/accounts.js", "downloaded_repos/greymass_anchor/app/shared/actions/accounts.test.js", "downloaded_repos/greymass_anchor/app/shared/actions/address.js", "downloaded_repos/greymass_anchor/app/shared/actions/app.js", "downloaded_repos/greymass_anchor/app/shared/actions/blockchains/beos/withdraw.js", "downloaded_repos/greymass_anchor/app/shared/actions/blockchains/eos/delphioracle.js", "downloaded_repos/greymass_anchor/app/shared/actions/blockchains/wax/claimgbmrewards.js", "downloaded_repos/greymass_anchor/app/shared/actions/blockchains.js", "downloaded_repos/greymass_anchor/app/shared/actions/blockexplorers.js", "downloaded_repos/greymass_anchor/app/shared/actions/chain.js", "downloaded_repos/greymass_anchor/app/shared/actions/connection.js", "downloaded_repos/greymass_anchor/app/shared/actions/contracts.js", "downloaded_repos/greymass_anchor/app/shared/actions/createaccount.js", "downloaded_repos/greymass_anchor/app/shared/actions/customtokens.js", "downloaded_repos/greymass_anchor/app/shared/actions/fuel.js", "downloaded_repos/greymass_anchor/app/shared/actions/globals.js", "downloaded_repos/greymass_anchor/app/shared/actions/governance/proposals.js", "downloaded_repos/greymass_anchor/app/shared/actions/hardware/ledger.js", "downloaded_repos/greymass_anchor/app/shared/actions/helpers/eos.js", "downloaded_repos/greymass_anchor/app/shared/actions/helpers/eos.test.js", "downloaded_repos/greymass_anchor/app/shared/actions/helpers/hardware/ledger.js", "downloaded_repos/greymass_anchor/app/shared/actions/helpers/ledger/serialize.js", "downloaded_repos/greymass_anchor/app/shared/actions/namebids.js", "downloaded_repos/greymass_anchor/app/shared/actions/pending.js", "downloaded_repos/greymass_anchor/app/shared/actions/ping.js", "downloaded_repos/greymass_anchor/app/shared/actions/producers.js", "downloaded_repos/greymass_anchor/app/shared/actions/rex.js", "downloaded_repos/greymass_anchor/app/shared/actions/sessions.js", "downloaded_repos/greymass_anchor/app/shared/actions/settings.js", "downloaded_repos/greymass_anchor/app/shared/actions/stake.js", "downloaded_repos/greymass_anchor/app/shared/actions/storage.js", "downloaded_repos/greymass_anchor/app/shared/actions/sync.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/bidname.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/bidname.test.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/buyram.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/buyrambytes.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/claimairgrab.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/community/regproxyinfo.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/delegatebw.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/powerup.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/regproducer.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/regproxy.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/rexcombo.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/rexi.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/sellram.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/systemstate.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/undelegatebw.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/unregproxy.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/updateauth.js", "downloaded_repos/greymass_anchor/app/shared/actions/system/voteproducer.js", "downloaded_repos/greymass_anchor/app/shared/actions/table.js", "downloaded_repos/greymass_anchor/app/shared/actions/transaction.js", "downloaded_repos/greymass_anchor/app/shared/actions/transfer.js", "downloaded_repos/greymass_anchor/app/shared/actions/types.js", "downloaded_repos/greymass_anchor/app/shared/actions/validate.js", "downloaded_repos/greymass_anchor/app/shared/actions/wallet.js", "downloaded_repos/greymass_anchor/app/shared/actions/wallets.js", "downloaded_repos/greymass_anchor/app/shared/app.global.css", "downloaded_repos/greymass_anchor/app/shared/components/About.js", "downloaded_repos/greymass_anchor/app/shared/components/ColdWallet/Wallet/Info.js", "downloaded_repos/greymass_anchor/app/shared/components/ColdWallet/Wallet/Panel/Button/SignTransaction.js", "downloaded_repos/greymass_anchor/app/shared/components/ColdWallet/Wallet/Panel/Unlocked.js", "downloaded_repos/greymass_anchor/app/shared/components/ColdWallet/Wallet/Panel.js", "downloaded_repos/greymass_anchor/app/shared/components/ColdWallet/Wallet/Transaction.js", "downloaded_repos/greymass_anchor/app/shared/components/ColdWallet/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Component.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Form/Action.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Selector/Action.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Selector/Contract.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Selector/Table.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Tab/Actions.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Tab/Data.js", "downloaded_repos/greymass_anchor/app/shared/components/Contract/Interface/Tab/Tables.js", "downloaded_repos/greymass_anchor/app/shared/components/Dev/State/Tabs.js", "downloaded_repos/greymass_anchor/app/shared/components/Dev/Test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Button/Account/Import.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Button/Account/Import.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Button/Account/__snapshots__/Import.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Button/Blockchain/Import.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Button/Blockchain/__snapshots__/import.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Button/Blockchain/import.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Data/Bytes.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Data/Bytes.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Data/Resource/Percentage.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Data/Resource/Percentage.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Data/Resource/__snapshots__/Percentage.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Data/__snapshots__/Bytes.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Account.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Account.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/AccountName.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Generic.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Generic.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Key/Private.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Key/Public.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Memo.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Memo.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/MultiToken.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/MultiToken.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/PublicKeys.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Ram.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Ram.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Server.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Server.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/String.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/String.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Token.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Token.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Url.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Url.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/Words.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/Account.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/Generic.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/Memo.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/MultiToken.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/Ram.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/Server.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/String.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/Token.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Field/__snapshots__/Url.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Message/Error.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Message/Error.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Message/__snapshots__/Error.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Token/PowerUp.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Token/Rent.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Token/Stake.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Form/Token/Unstake.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/Authorization.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/Authorization.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/Blockchain.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/Blockchain.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/ChainLogo.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/Wallet.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/WalletType.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/WalletType.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/WalletTypes.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/WalletTypes.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/__snapshots__/Authorization.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/__snapshots__/Blockchain.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/__snapshots__/Wallet.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/__snapshots__/WalletType.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Fragment/__snapshots__/WalletTypes.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/App/Error.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/App/Upgrade/Modal.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/App/Upgrade/Modal.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/App/Upgrade/__snapshots__/Modal.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/App/Upgrade.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/App/Upgrade.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/App/__snapshots__/Upgrade.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/Base.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/Base.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/BuyRamBytes.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/BuyRamBytes.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/SellRamBytes.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/SellRamBytes.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/Transfer.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/Transfer.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/VoteProducer.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/VoteProducer.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/__snapshots__/Base.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/__snapshots__/BuyRamBytes.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/__snapshots__/SellRamBytes.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/__snapshots__/Transfer.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Message/Contract/__snapshots__/VoteProducer.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Account/Import.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Account/Import.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Account/__snapshots__/Import.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Blockchain/Import.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Blockchain/Import.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Blockchain/__snapshots__/Import.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Constitution.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Constitution.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/DangerLink.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/DangerLink.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/ExplorerLink.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/ExplorerLink.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens/Confirm.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens/Confirm.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens/Form.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens/Form.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens/__snapshots__/Confirm.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens/__snapshots__/Form.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/CustomTokens.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/Settings/__snapshots__/CustomTokens.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/__snapshots__/Constitution.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/__snapshots__/DangerLink.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Modal/__snapshots__/ExplorerLink.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/ScrollToTop.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/ScrollToTop.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/AdvancedOptions.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/AllowDangerousTransactions.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/AnchorLinkService.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/Background.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/BlockExplorer.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/BlockExplorer.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/Dfuse.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/FilterSpamTransfers.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/FilterSpamTransfers.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/IdleTimeout.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/IdleTimeout.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/Language.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/Language.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/OverviewToken.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/OverviewToken.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/RefreshRate.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/ResourceDisplayFormat.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/ResourceDisplayFormat.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/ShowTestnets.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/ShowTestnets.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/SigningRequests.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/SkipLinkModal.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/TransactionFees.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/__snapshots__/BlockExplorer.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/__snapshots__/FilterSpamTransfers.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/__snapshots__/IdleTimeout.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/__snapshots__/Language.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/__snapshots__/OverviewToken.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/__snapshots__/ResourceDisplayFormat.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Settings/__snapshots__/ShowTestnets.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Handler.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Handler.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/Authorization.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/Authorization.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/CpuUsage.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/Default.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/Default.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/Ledger/Busy.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/Ledger/Data.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/NetUsage.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/ResourceUsage.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/__snapshots__/Authorization.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error/__snapshots__/Default.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Error.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Hardware/Ledger.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Hardware/Ledger.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Hardware/__snapshots__/Ledger.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Signed/Broadcast.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Signed/Broadcast.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Signed/__snapshots__/Broadcast.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Success.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Success.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Unsigned/Download.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Unsigned/Download.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Unsigned/Sign.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Unsigned/Sign.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Unsigned/__snapshots__/Download.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/Unsigned/__snapshots__/Sign.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/__snapshots__/Error.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Message/__snapshots__/Success.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Modal.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/Modal.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Action.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Action.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Actions.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Actions.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Detail.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Detail.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Full.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Full.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Overview.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/Overview.test.js", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/__snapshots__/Action.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/__snapshots__/Actions.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/__snapshots__/Detail.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/__snapshots__/Full.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/View/__snapshots__/Overview.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/__snapshots__/Handler.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/Transaction/__snapshots__/Modal.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Global/__snapshots__/ScrollToTop.test.js.snap", "downloaded_repos/greymass_anchor/app/shared/components/Notifications.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Link.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Modal/Info/Data.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Modal/Info/Details.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Modal/Info.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Modal/Preview/Selection/ProducersTable.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Modal/Preview/Selection.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Modal/Preview.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Selector/Item/Empty.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Selector/Item.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Selector.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Table/Placeholder.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Table/Row.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Table.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Vote/Weight.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/VotingAccount.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Welcome.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxies/Modal/ProxyInfo.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxies/Table/Row.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxies/Table.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxies.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxy/Button/Proxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxy/Form/Proxy/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxy/Form/Proxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers/Proxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Producers.js", "downloaded_repos/greymass_anchor/app/shared/components/Recommendation/Interface/List/ActiveAndOwnerSame.js", "downloaded_repos/greymass_anchor/app/shared/components/Recommendation/Interface/List/BandwidthAvailableLow.js", "downloaded_repos/greymass_anchor/app/shared/components/Recommendation/Interface/List/BandwidthStakedLow.js", "downloaded_repos/greymass_anchor/app/shared/components/Recommendation/Interface/List/CpuAvailableLow.js", "downloaded_repos/greymass_anchor/app/shared/components/Recommendation/Interface/List/CpuStakedLow.js", "downloaded_repos/greymass_anchor/app/shared/components/Recommendation/Interface/List.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/Lend/About.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/Lend/Manage.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/Lend/Savings.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/Rent/About.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/Rent/Loans.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/Rent/Manage.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/shared/Fund.js", "downloaded_repos/greymass_anchor/app/shared/components/Rex/utils/fetchMaturedBalance.js", "downloaded_repos/greymass_anchor/app/shared/components/TabMenu.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Airgrabs.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/BidName.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Blockchains/BEOS/CrosschainTransfer.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Blockchains.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Button/CreateAccount.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Button/RegisterProxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Button/UnregisterProxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Contacts.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/CreateAccount.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/CustomTokens.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Delegations.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/BidName/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/BidName.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/Contact.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/CreateAccount/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/CreateAccount.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/CreateBitsharesEosAccount.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/Delegation.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/Permissions/Auth/Set.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/Permissions/Auth/WeightedKey.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/Permissions/Auth.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/ProxyInfo/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/ProxyInfo.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/RegisterProxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Form/UnregisterProxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Governance/Proposals/Proposal/Vote.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Governance/Proposals/Proposal.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Governance/Proposals.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Governance/Table.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Hardware/Ledger/Permissions.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Hardware/Ledger/Status/Message.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Hardware/Ledger/Status.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Hardware/Ledger.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/KeyConverter.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/KeyGenerator.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/KeyImport.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Keys/Validator.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Keys.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/LinkService.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Modal/Airgrab.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Modal/BidName.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Modal/Blockchain.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Modal/Contact.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Modal/Delegation.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Modal/Permissions/Auth/Set.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Modal/Permissions/Auth.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Permissions.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Ping/Controls/Start.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Ping/Controls.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Ping/Header.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Ping/Loader.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Ping/Result.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Ping/Results.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Ping.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Proxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Reset.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Sessions.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/State/Chain.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/State/Globals.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/State/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/System/Log.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Table/Row/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools/Wallets.js", "downloaded_repos/greymass_anchor/app/shared/components/Tools.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Language.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/LockState/Locked.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/LockState.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Modal/Account/Lookup.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Modal/Content/Broadcast.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Modal/Unlock.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Mode.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Account/Request.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Broadcast.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/CrosschainTransfer.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Lock.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Ram/Buy.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Ram/Sell.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Stake.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Transfer/Receive.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Transfer/Send.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Unlock.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Wax/Claim.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Button/Withdraw.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Account/Request.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Account.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/CrosschainTransfer/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/CrosschainTransfer/CrosschainTransfer.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Encrypt.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Hash.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Key.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Modal/Confirm.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Node.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Ram/Buy.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Ram/ByAmount.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Ram/ByCost.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Ram/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Ram/Sell.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Ram/Stats.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Stake/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Stake/Stats.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Stake.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Submit.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Transfer/Send/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Transfer/Send.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Withdraw/Confirming.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Form/Withdraw/Withdraw.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Locked.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Modal/Account/Request/Backup.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Modal/Account/Request/Code.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Modal/Account/Request/Import.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Modal/Account/Request.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Modal/Transfer/Receive.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Unlocked.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel/Waiting.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Panel.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Buyram.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Claim.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Claimrewards.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Delegatebw.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Generic.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Newaccount.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Refund.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Regproxy.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Sellram.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Transfer.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Undelegatebw.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Updateauth.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row/Voteproducer.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table/Row.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions/Table.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Actions.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Balances.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Button/ClaimUnstaked.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Resources.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Staked.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status/Waiting.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Status.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet/Upgrade.js", "downloaded_repos/greymass_anchor/app/shared/components/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Account.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Advanced.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Breadcrumb.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Connection.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Hardware/Ledger.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Key.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Path.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/components/Welcome.js", "downloaded_repos/greymass_anchor/app/shared/components/helpers/calculateAmountOfRam.js", "downloaded_repos/greymass_anchor/app/shared/components/helpers/calculatePriceOfRam.js", "downloaded_repos/greymass_anchor/app/shared/containers/BasicVoter.js", "downloaded_repos/greymass_anchor/app/shared/containers/ColdWallet.js", "downloaded_repos/greymass_anchor/app/shared/containers/Contract/Interface.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Convert/Ledger.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Dropdown/Select.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Dropdown.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Edit.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/PowerUp/Price.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/REX/Balance.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/REX/Price.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Ram/Max.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Ram/Percent.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Ram/Price.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Ram/Progress.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Ram/Units.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Ram/Usage.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Ram/Value.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Max.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Percent.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Progress.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Staked/Delegated.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Staked/Self.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Staked.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Units.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Resource/Usage.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/Staleness.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/SystemTokenBalance.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/SystemTokenValue.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/TokenBalance.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/TokenDelegated.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/TokenRefunding.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/TokenRefundingClaim.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/TokenStaked.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/VoterInfo/Effectiveness.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/VoterInfo/Proxy.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/VoterInfo/Votes.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Fragment/VoterInfo/WeightValue.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Cert.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Cold.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Contracts/SetupAccount.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Contracts.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Create.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Detect.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Elements/AccountList.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Existing.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Hot.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Keys.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Ledger/Accounts.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Ledger/Keys.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Manual.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Password.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Percent.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Request/Code.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Request.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Watch.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/Welcome.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Import/index.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Key.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Link.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Required.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Select/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Account/Select.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/App/Authorization.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/App/Disconnected.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/Choose.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/Dropdown.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/ExplorerLink.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/Form.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/Import.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/Manage/Enable.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/Manage/Resync.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Blockchain/Manage.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Broadcast.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Elevate.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/PowerUp.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Rent.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Reset.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Stake.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Unstake.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/DangerLink.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Fuel/Dropdown.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Hardware/Ledger/Error.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Hardware/Ledger/Status.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Unlock.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Utils/Ping.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Wallet/Dropdown/Keys.js", "downloaded_repos/greymass_anchor/app/shared/containers/Global/Wallet/Unlocked.js", "downloaded_repos/greymass_anchor/app/shared/containers/Main.js", "downloaded_repos/greymass_anchor/app/shared/containers/Recommendation/Interface.js", "downloaded_repos/greymass_anchor/app/shared/containers/Rex/Lend.js", "downloaded_repos/greymass_anchor/app/shared/containers/Rex/Rent.js", "downloaded_repos/greymass_anchor/app/shared/containers/Root/Idle.js", "downloaded_repos/greymass_anchor/app/shared/containers/Root/Updater.js", "downloaded_repos/greymass_anchor/app/shared/containers/Root.js", "downloaded_repos/greymass_anchor/app/shared/containers/Sidebar/Account.js", "downloaded_repos/greymass_anchor/app/shared/containers/Sidebar/Connection.js", "downloaded_repos/greymass_anchor/app/shared/containers/Test.js", "downloaded_repos/greymass_anchor/app/shared/containers/Tools.js", "downloaded_repos/greymass_anchor/app/shared/containers/Tray/Menu.js", "downloaded_repos/greymass_anchor/app/shared/containers/Tray/index.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Account.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Advanced.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Connection.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Hardware/Ledger.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Import.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Key.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Path.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/containers/Welcome.js", "downloaded_repos/greymass_anchor/app/shared/electron/ipc/keycert/font.js", "downloaded_repos/greymass_anchor/app/shared/electron/ipc/keycert.js", "downloaded_repos/greymass_anchor/app/shared/electron/ipc.js", "downloaded_repos/greymass_anchor/app/shared/electron/menu/index.js", "downloaded_repos/greymass_anchor/app/shared/electron/saveFile.js", "downloaded_repos/greymass_anchor/app/shared/electron/updater.js", "downloaded_repos/greymass_anchor/app/shared/electron/windowStateKeeper.js", "downloaded_repos/greymass_anchor/app/shared/i18n.js", "downloaded_repos/greymass_anchor/app/shared/reducers/accountcreate.js", "downloaded_repos/greymass_anchor/app/shared/reducers/accounts.js", "downloaded_repos/greymass_anchor/app/shared/reducers/accounts.test.js", "downloaded_repos/greymass_anchor/app/shared/reducers/actions.js", "downloaded_repos/greymass_anchor/app/shared/reducers/app.js", "downloaded_repos/greymass_anchor/app/shared/reducers/auths.js", "downloaded_repos/greymass_anchor/app/shared/reducers/balances.js", "downloaded_repos/greymass_anchor/app/shared/reducers/blockchains.js", "downloaded_repos/greymass_anchor/app/shared/reducers/blockexplorers.js", "downloaded_repos/greymass_anchor/app/shared/reducers/chain.js", "downloaded_repos/greymass_anchor/app/shared/reducers/connection.js", "downloaded_repos/greymass_anchor/app/shared/reducers/contracts.js", "downloaded_repos/greymass_anchor/app/shared/reducers/customtokens.js", "downloaded_repos/greymass_anchor/app/shared/reducers/fuel.js", "downloaded_repos/greymass_anchor/app/shared/reducers/globals.js", "downloaded_repos/greymass_anchor/app/shared/reducers/hardware/ledger.js", "downloaded_repos/greymass_anchor/app/shared/reducers/index.js", "downloaded_repos/greymass_anchor/app/shared/reducers/keys.js", "downloaded_repos/greymass_anchor/app/shared/reducers/pending.js", "downloaded_repos/greymass_anchor/app/shared/reducers/producers.js", "downloaded_repos/greymass_anchor/app/shared/reducers/proposals.js", "downloaded_repos/greymass_anchor/app/shared/reducers/sessions.js", "downloaded_repos/greymass_anchor/app/shared/reducers/settings.js", "downloaded_repos/greymass_anchor/app/shared/reducers/storage.js", "downloaded_repos/greymass_anchor/app/shared/reducers/system/features.js", "downloaded_repos/greymass_anchor/app/shared/reducers/system/log.js", "downloaded_repos/greymass_anchor/app/shared/reducers/system/navigation.js", "downloaded_repos/greymass_anchor/app/shared/reducers/system.js", "downloaded_repos/greymass_anchor/app/shared/reducers/tables.js", "downloaded_repos/greymass_anchor/app/shared/reducers/transaction.js", "downloaded_repos/greymass_anchor/app/shared/reducers/utils/ping.js", "downloaded_repos/greymass_anchor/app/shared/reducers/utils/prompt.js", "downloaded_repos/greymass_anchor/app/shared/reducers/validate.js", "downloaded_repos/greymass_anchor/app/shared/reducers/wallet.js", "downloaded_repos/greymass_anchor/app/shared/reducers/wallets.js", "downloaded_repos/greymass_anchor/app/shared/reducers/whitelist.js", "downloaded_repos/greymass_anchor/app/shared/selectors/getKeysUnlocked.js", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/LICENSE", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/font/lato-v14-latin-regular.eot", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/font/lato-v14-latin-regular.svg", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/font/lato-v14-latin-regular.ttf", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/font/lato-v14-latin-regular.woff", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/font/lato-v14-latin-regular.woff2", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/semantic.min.css", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/brand-icons.eot", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/brand-icons.svg", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/brand-icons.ttf", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/brand-icons.woff", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/brand-icons.woff2", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/icons.eot", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/icons.otf", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/icons.svg", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/icons.ttf", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/icons.woff", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/icons.woff2", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/outline-icons.eot", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/outline-icons.svg", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/outline-icons.ttf", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/outline-icons.woff", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/fonts/outline-icons.woff2", "downloaded_repos/greymass_anchor/app/shared/semantic-ui/themes/default/assets/images/flags.png", "downloaded_repos/greymass_anchor/app/shared/store/main/configureStore.dev.js", "downloaded_repos/greymass_anchor/app/shared/store/main/configureStore.js", "downloaded_repos/greymass_anchor/app/shared/store/main/configureStore.prod.js", "downloaded_repos/greymass_anchor/app/shared/store/renderer/configureStore.dev.js", "downloaded_repos/greymass_anchor/app/shared/store/renderer/configureStore.js", "downloaded_repos/greymass_anchor/app/shared/store/renderer/configureStore.prod.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/i18n.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/logger.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/migrations/009-updateSettings.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/migrations/010-updateBlockchains.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/migrations/011-updateBlockchains.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/migrations/012-updateBlockchains.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/migrations/013-updateBlockchains.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/migrations/014-updateBlockchains.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/migrations/016-updateBlockchains.js", "downloaded_repos/greymass_anchor/app/shared/store/shared/persist.js", "downloaded_repos/greymass_anchor/app/shared/utils/.gitkeep", "downloaded_repos/greymass_anchor/app/shared/utils/Anchor/Unlocked.js", "downloaded_repos/greymass_anchor/app/shared/utils/Anchor/Wallet.js", "downloaded_repos/greymass_anchor/app/shared/utils/EOS/Account/Stats.js", "downloaded_repos/greymass_anchor/app/shared/utils/EOS/Account.js", "downloaded_repos/greymass_anchor/app/shared/utils/EOS/Contract.js", "downloaded_repos/greymass_anchor/app/shared/utils/EOS/Handler.js", "downloaded_repos/greymass_anchor/app/shared/utils/EOS/Transaction.js", "downloaded_repos/greymass_anchor/app/shared/utils/EOS/ValidateFuel.js", "downloaded_repos/greymass_anchor/app/shared/utils/Hardware/Ledger/Adapter.js", "downloaded_repos/greymass_anchor/app/shared/utils/Hardware/Ledger.js", "downloaded_repos/greymass_anchor/app/shared/utils/SessionManager.js", "downloaded_repos/greymass_anchor/app/shared/utils/UriHandler.js", "downloaded_repos/greymass_anchor/app/shared/utils/http/generic.js", "downloaded_repos/greymass_anchor/app/shared/utils/http/handler.js", "downloaded_repos/greymass_anchor/app/yarn.lock", "downloaded_repos/greymass_anchor/babel.config.js", "downloaded_repos/greymass_anchor/config/jest/fileTransform.js", "downloaded_repos/greymass_anchor/crowdin.yml", "downloaded_repos/greymass_anchor/docs/blockchains.json", "downloaded_repos/greymass_anchor/enzyme.config.js", "downloaded_repos/greymass_anchor/flow-typed/module_vx.x.x.js", "downloaded_repos/greymass_anchor/internals/electron-builder.json", "downloaded_repos/greymass_anchor/internals/electron-builder.mas.json", "downloaded_repos/greymass_anchor/internals/flow/CSSModule.js.flow", "downloaded_repos/greymass_anchor/internals/flow/WebpackAsset.js.flow", "downloaded_repos/greymass_anchor/internals/img/anchor-ss1.png", "downloaded_repos/greymass_anchor/internals/img/anchor.png", "downloaded_repos/greymass_anchor/internals/img/eslint-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/eslint-padded.png", "downloaded_repos/greymass_anchor/internals/img/eslint.png", "downloaded_repos/greymass_anchor/internals/img/flow-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/flow-padded.png", "downloaded_repos/greymass_anchor/internals/img/flow.png", "downloaded_repos/greymass_anchor/internals/img/jest-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/jest-padded.png", "downloaded_repos/greymass_anchor/internals/img/jest.png", "downloaded_repos/greymass_anchor/internals/img/js-padded.png", "downloaded_repos/greymass_anchor/internals/img/js.png", "downloaded_repos/greymass_anchor/internals/img/npm.png", "downloaded_repos/greymass_anchor/internals/img/react-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/react-padded.png", "downloaded_repos/greymass_anchor/internals/img/react-router-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/react-router-padded.png", "downloaded_repos/greymass_anchor/internals/img/react-router.png", "downloaded_repos/greymass_anchor/internals/img/react.png", "downloaded_repos/greymass_anchor/internals/img/redux-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/redux-padded.png", "downloaded_repos/greymass_anchor/internals/img/redux.png", "downloaded_repos/greymass_anchor/internals/img/webpack-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/webpack-padded.png", "downloaded_repos/greymass_anchor/internals/img/webpack.png", "downloaded_repos/greymass_anchor/internals/img/yarn-padded-90.png", "downloaded_repos/greymass_anchor/internals/img/yarn-padded.png", "downloaded_repos/greymass_anchor/internals/img/yarn.png", "downloaded_repos/greymass_anchor/internals/macos/entitlements.mac.inherit.plist", "downloaded_repos/greymass_anchor/internals/macos/entitlements.mac.plist", "downloaded_repos/greymass_anchor/internals/macos/entitlements.mas.inherit.plist", "downloaded_repos/greymass_anchor/internals/macos/entitlements.mas.loginhelper.plist", "downloaded_repos/greymass_anchor/internals/macos/entitlements.mas.plist", "downloaded_repos/greymass_anchor/internals/macos/notarize.js", "downloaded_repos/greymass_anchor/internals/mocks/fileMock.js", "downloaded_repos/greymass_anchor/internals/scripts/BabelRegister.js", "downloaded_repos/greymass_anchor/internals/scripts/CheckBuiltsExist.js", "downloaded_repos/greymass_anchor/internals/scripts/CheckNativeDep.js", "downloaded_repos/greymass_anchor/internals/scripts/CheckNodeEnv.js", "downloaded_repos/greymass_anchor/internals/scripts/CheckPortInUse.js", "downloaded_repos/greymass_anchor/internals/scripts/DeleteSourceMaps.js", "downloaded_repos/greymass_anchor/internals/scripts/ElectronRebuild.js", "downloaded_repos/greymass_anchor/internals/scripts/RunTests.js", "downloaded_repos/greymass_anchor/jest.config.js", "downloaded_repos/greymass_anchor/nodes.md", "downloaded_repos/greymass_anchor/package.json", "downloaded_repos/greymass_anchor/resources/icon.icns", "downloaded_repos/greymass_anchor/resources/icon.ico", "downloaded_repos/greymass_anchor/resources/icon.png", "downloaded_repos/greymass_anchor/resources/icons/1024x1024.png", "downloaded_repos/greymass_anchor/resources/icons/128x128.png", "downloaded_repos/greymass_anchor/resources/icons/16x16.png", "downloaded_repos/greymass_anchor/resources/icons/24x24.png", "downloaded_repos/greymass_anchor/resources/icons/256x256.png", "downloaded_repos/greymass_anchor/resources/icons/32x32.png", "downloaded_repos/greymass_anchor/resources/icons/48x48.png", "downloaded_repos/greymass_anchor/resources/icons/512x512.png", "downloaded_repos/greymass_anchor/resources/icons/64x64.png", "downloaded_repos/greymass_anchor/resources/icons/96x96.png", "downloaded_repos/greymass_anchor/shasum.sh", "downloaded_repos/greymass_anchor/webpack.config.base.js", "downloaded_repos/greymass_anchor/webpack.config.eslint.js", "downloaded_repos/greymass_anchor/webpack.config.main.prod.js", "downloaded_repos/greymass_anchor/webpack.config.renderer.dev.dll.js", "downloaded_repos/greymass_anchor/webpack.config.renderer.dev.js", "downloaded_repos/greymass_anchor/webpack.config.renderer.prod.js", "downloaded_repos/greymass_anchor/yarn.lock"], "skipped": [{"path": "downloaded_repos/greymass_anchor/app/renderer/assets/locales/fr-FR/rex.json", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/greymass_anchor/app/renderer/assets/locales/ko-KR/transaction.json", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/greymass_anchor/app/shared/components/Tools/Button/CreateAccount.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/greymass_anchor/app/shared/containers/Global/Button/Wallet/Upgrade/Watch.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/greymass_anchor/app/shared/electron/ipc/keycert/template.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/greymass_anchor/app/shared/semantic-ui/semantic.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/build/installer.nsh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/.eslintrc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/actions/__snapshots__/counter.spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/actions/counter.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/components/Counter.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/components/__snapshots__/Counter.spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/containers/CounterPage.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/e2e/e2e.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/example.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/reducers/__snapshots__/counter.spec.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/greymass_anchor/test/reducers/counter.spec.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.718949794769287, "profiling_times": {"config_time": 5.7804388999938965, "core_time": 11.30479621887207, "ignores_time": 0.0034728050231933594, "total_time": 17.08975315093994}, "parsing_time": {"total_time": 17.43219757080078, "per_file_time": {"mean": 0.016140923676667376, "std_dev": 0.0006488383379376241}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 69.**************, "per_file_time": {"mean": 0.018804822795965462, "std_dev": 0.008831864826108421}, "very_slow_stats": {"time_ratio": 0.*****************, "count_ratio": 0.0008075370121130552}, "very_slow_files": [{"fpath": "downloaded_repos/greymass_anchor/app/shared/electron/ipc/keycert.js", "ftime": 1.***************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/actions/accounts.js", "ftime": 2.***************}, {"fpath": "downloaded_repos/greymass_anchor/yarn.lock", "ftime": 2.***************}]}, "matching_time": {"total_time": 22.***************, "per_file_and_rule_time": {"mean": 0.006527626607756085, "std_dev": 0.00032425590614289907}, "very_slow_stats": {"time_ratio": 0.*****************, "count_ratio": 0.005508843142939983}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/greymass_anchor/app/shared/electron/ipc/keycert.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/actions/wallets.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/actions/accounts.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/main/main.dev.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/modules/main/containers/Sections/Home/Setup/Elements/Words/Wordlist.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/utils/EOS/Handler.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/actions/accounts.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/actions/accounts.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/actions/wallet.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/modules/handler/actions/uri.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.****************}]}, "tainting_time": {"total_time": 7.***************, "per_def_and_rule_time": {"mean": 0.0022852006969685127, "std_dev": 3.4650743914704484e-05}, "very_slow_stats": {"time_ratio": 0.*****************, "count_ratio": 0.000894721145243066}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/greymass_anchor/app/shared/actions/accounts.js", "fline": 193, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/components/Producers/BlockProducers/Selector.js", "fline": 15, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}, {"fpath": "downloaded_repos/greymass_anchor/app/shared/components/Tools/Governance/Proposals.js", "fline": 56, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": **********}, "engine_requested": "OSS", "skipped_rules": []}