{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "start": {"line": 1197, "col": 255, "offset": 42492}, "end": {"line": 1197, "col": 279, "offset": 42516}, "extra": {"message": "RegExp() called with a `t` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "start": {"line": 1276, "col": 17, "offset": 46383}, "end": {"line": 1276, "col": 42, "offset": 46408}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/iamtherufus_Homio/LICENSE", "downloaded_repos/iamtherufus_Homio/README.md", "downloaded_repos/iamtherufus_Homio/configuration.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/homio/homio.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/base/homio_default.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/base/homio_entity.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/base/homio_logo.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/base/homio_menu_icon.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/base/homio_mobile_logo.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/base/homio_nav_button.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/base/homio_time.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/cards/homio_light.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/cards/homio_room.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/button_cards/cards/homio_thermostat.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/includes/homio_entity_layout.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/includes/homio_navigation.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/includes/homio_navigation_list.yaml", "downloaded_repos/iamtherufus_Homio/dashboards/templates/includes/homio_screen_layout.yaml", "downloaded_repos/iamtherufus_Homio/homio_icons.zip", "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "downloaded_repos/iamtherufus_Homio/packages/homio_helpers.yaml", "downloaded_repos/iamtherufus_Homio/sensors.yaml", "downloaded_repos/iamtherufus_Homio/themes/homio/homio.yaml"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.568317174911499, "profiling_times": {"config_time": 5.56759238243103, "core_time": 11.774410963058472, "ignores_time": 0.001676321029663086, "total_time": 17.34442710876465}, "parsing_time": {"total_time": 1.051058053970337, "per_file_time": {"mean": 0.05255290269851685, "std_dev": 0.032152460207443445}, "very_slow_stats": {"time_ratio": 0.7932682568980485, "count_ratio": 0.05}, "very_slow_files": [{"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "ftime": 0.8337709903717041}]}, "scanning_time": {"total_time": 10.295493364334106, "per_file_time": {"mean": 0.1559923237020319, "std_dev": 1.3568514713490534}, "very_slow_stats": {"time_ratio": 0.9272208379014529, "count_ratio": 0.015151515151515152}, "very_slow_files": [{"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "ftime": 9.546195983886719}]}, "matching_time": {"total_time": 3.310926914215088, "per_file_and_rule_time": {"mean": 0.005748137003845641, "std_dev": 0.0014871979025670907}, "very_slow_stats": {"time_ratio": 0.721000179015908, "count_ratio": 0.015625}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.11588215827941895}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.17257094383239746}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.17264485359191895}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.2070009708404541}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.20726585388183594}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.21513104438781738}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.28893017768859863}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.3403298854827881}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6674230098724365}]}, "tainting_time": {"total_time": 2.652524471282959, "per_def_and_rule_time": {"mean": 0.0024026489776113754, "std_dev": 0.000401957805465982}, "very_slow_stats": {"time_ratio": 0.8394491965929782, "count_ratio": 0.014492753623188406}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.08641982078552246}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.09263300895690918}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.09716391563415527}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.09819984436035156}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "time": 0.10712909698486328}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "time": 0.11820507049560547}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.1624279022216797}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.2644321918487549}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.3480830192565918}, {"fpath": "downloaded_repos/iamtherufus_<PERSON>/layout-card-modified.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.3613159656524658}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}