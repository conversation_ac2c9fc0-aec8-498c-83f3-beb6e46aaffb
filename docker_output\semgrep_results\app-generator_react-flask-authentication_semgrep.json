{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/Dockerfile", "start": {"line": 14, "col": 1, "offset": 232}, "end": {"line": 14, "col": 59, "offset": 290}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"gunicorn\", \"--config\", \"gunicorn-cfg.py\", \"run:app\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/api/routes.py", "start": {"line": 109, "col": 9, "offset": 3372}, "end": {"line": 109, "col": 41, "offset": 3404}, "extra": {"message": "The password on 'new_user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(_password, user=new_user):\n            new_user.set_password(_password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/docker-compose.yml", "start": {"line": 11, "col": 3, "offset": 175}, "end": {"line": 11, "col": 8, "offset": 180}, "extra": {"message": "Service 'nginx' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/docker-compose.yml", "start": {"line": 11, "col": 3, "offset": 175}, "end": {"line": 11, "col": 8, "offset": 180}, "extra": {"message": "Service 'nginx' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "path": "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/run.py", "start": {"line": 15, "col": 5, "offset": 246}, "end": {"line": 15, "col": 40, "offset": 281}, "extra": {"message": "Running flask app with host 0.0.0.0 could expose the server publicly.", "metadata": {"cwe": ["CWE-668: Exposure of Resource to Wrong Sphere"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.flask.security.audit.app-run-param-config.avoid_app_run_with_bad_host", "shortlink": "https://sg.run/eLby"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/app-generator_react-flask-authentication/react-ui/Dockerfile", "start": {"line": 22, "col": 1, "offset": 347}, "end": {"line": 22, "col": 21, "offset": 367}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"npm\", \"start\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/app-generator_react-flask-authentication/CHANGELOG.md", "downloaded_repos/app-generator_react-flask-authentication/LICENSE.md", "downloaded_repos/app-generator_react-flask-authentication/README.md", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/.env", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/.gitignore", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/CHANGELOG.md", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/Dockerfile", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/LICENSE.md", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/api/__init__.py", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/api/config.py", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/api/models.py", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/api/routes.py", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/docker-compose.yml", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/gunicorn-cfg.py", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/nginx/flask_api.conf", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/package.json", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/requirements.txt", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/run.py", "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/tests.py", "downloaded_repos/app-generator_react-flask-authentication/package.json", "downloaded_repos/app-generator_react-flask-authentication/react-ui/.dockerignore", "downloaded_repos/app-generator_react-flask-authentication/react-ui/.gitignore", "downloaded_repos/app-generator_react-flask-authentication/react-ui/.prettierrc", "downloaded_repos/app-generator_react-flask-authentication/react-ui/CHANGELOG.md", "downloaded_repos/app-generator_react-flask-authentication/react-ui/Dockerfile", "downloaded_repos/app-generator_react-flask-authentication/react-ui/LICENSE.md", "downloaded_repos/app-generator_react-flask-authentication/react-ui/docker-compose.yml", "downloaded_repos/app-generator_react-flask-authentication/react-ui/env.sample", "downloaded_repos/app-generator_react-flask-authentication/react-ui/package.json", "downloaded_repos/app-generator_react-flask-authentication/react-ui/public/favicon.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/public/index.html", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/App.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/auth/auth-blue-card.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/auth/auth-pattern-dark.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/auth/auth-pattern.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/auth/auth-purple-card.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/auth/auth-signup-blue-card.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/auth/auth-signup-white-card.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/icons/earning.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/icons/social-google.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/logo-dark.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/logo.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/images/users/user-round.svg", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/scss/_themes-vars.module.scss", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/assets/scss/style.scss", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/config.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/hooks/useScriptRef.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/Customization/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/NotificationSection/NotificationList.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/NotificationSection/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/ProfileSection/UpgradePlanCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/ProfileSection/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/SearchSection/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/LogoSection/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Sidebar/MenuCard/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Sidebar/MenuList/NavCollapse/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Sidebar/MenuList/NavGroup/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Sidebar/MenuList/NavItem/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Sidebar/MenuList/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Sidebar/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MinimalLayout/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/NavMotion.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/NavigationScroll.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/menu-items/dashboard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/menu-items/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/menu-items/other.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/menu-items/pages.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/menu-items/utilities.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/routes/AuthenticationRoutes.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/routes/LoginRoutes.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/routes/MainRoutes.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/routes/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/serviceWorker.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/store/accountReducer.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/store/actions.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/store/constant.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/store/customizationReducer.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/store/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/store/reducer.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/themes/compStyleOverride.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/themes/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/themes/palette.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/themes/typography.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/Loadable.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/Loader.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/Logo.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/AuthFooter.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/CardSecondaryAction.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/MainCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/Skeleton/EarningCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/Skeleton/ImagePlaceholder.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/Skeleton/PopularCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/Skeleton/TotalGrowthBarChart.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/Skeleton/TotalIncomeCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/cards/SubCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/extended/AnimateButton.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/extended/Avatar.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/extended/Breadcrumbs.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/ui-component/extended/Transitions.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/utils/password-strength.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/utils/route-guard/AuthGuard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/utils/route-guard/GuestGuard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/BajajAreaChartCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/EarningCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/PopularCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/TotalGrowthBarChart.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/TotalIncomeDarkCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/TotalIncomeLightCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/TotalOrderLineChartCard.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/chart-data/bajaj-area-chart.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/chart-data/total-growth-bar-chart.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/chart-data/total-order-month-line-chart.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/chart-data/total-order-year-line-chart.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/dashboard/Default/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/AuthCardWrapper.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/AuthWrapper1.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/authentication3/Login3.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/authentication3/Register3.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/firebase-forms/FirebaseLogin.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/firebase-forms/FirebaseRegister.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/login/RestLogin.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/login/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/register/RestRegister.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/register/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/sample-page/index.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/utilities/Color.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/utilities/MaterialIcons.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/utilities/Shadow.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/utilities/TablerIcons.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/utilities/Typography.js", "downloaded_repos/app-generator_react-flask-authentication/react-ui/web.config", "downloaded_repos/app-generator_react-flask-authentication/react-ui/yarn.lock"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.518280029296875, "profiling_times": {"config_time": 6.84723687171936, "core_time": 4.980444669723511, "ignores_time": 0.0025033950805664062, "total_time": 11.831785440444946}, "parsing_time": {"total_time": 2.0007576942443848, "per_file_time": {"mean": 0.01923805475234985, "std_dev": 0.0008130765648232884}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 10.371008157730103, "per_file_time": {"mean": 0.027656021753946956, "std_dev": 0.00909394139462588}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.0889527797698975, "per_file_and_rule_time": {"mean": 0.007268124187693876, "std_dev": 0.0003458701262187142}, "very_slow_stats": {"time_ratio": 0.15345592202906344, "count_ratio": 0.007058823529411765}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/ProfileSection/index.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10250711441040039}, {"fpath": "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/firebase-forms/FirebaseRegister.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10925793647766113}, {"fpath": "downloaded_repos/app-generator_react-flask-authentication/api-server-flask/api/routes.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.2622530460357666}]}, "tainting_time": {"total_time": 0.9694089889526367, "per_def_and_rule_time": {"mean": 0.0037573991819869652, "std_dev": 8.714789858335835e-05}, "very_slow_stats": {"time_ratio": 0.1774431160286434, "count_ratio": 0.011627906976744186}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/firebase-forms/FirebaseRegister.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.05435895919799805}, {"fpath": "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/views/pages/authentication/firebase-forms/FirebaseRegister.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.057775020599365234}, {"fpath": "downloaded_repos/app-generator_react-flask-authentication/react-ui/src/layout/MainLayout/Header/ProfileSection/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.059880971908569336}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}