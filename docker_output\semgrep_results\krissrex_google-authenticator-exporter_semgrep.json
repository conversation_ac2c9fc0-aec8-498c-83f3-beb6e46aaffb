{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/krissrex_google-authenticator-exporter/Dockerfile", "start": {"line": 8, "col": 1, "offset": 99}, "end": {"line": 8, "col": 26, "offset": 124}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"npm\",\"run\",\"start\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/krissrex_google-authenticator-exporter/src/index.js", "start": {"line": 107, "col": 23, "offset": 3241}, "end": {"line": 107, "col": 66, "offset": 3284}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/krissrex_google-authenticator-exporter/.dockerignore", "downloaded_repos/krissrex_google-authenticator-exporter/.gitignore", "downloaded_repos/krissrex_google-authenticator-exporter/Dockerfile", "downloaded_repos/krissrex_google-authenticator-exporter/LICENSE", "downloaded_repos/krissrex_google-authenticator-exporter/example.png", "downloaded_repos/krissrex_google-authenticator-exporter/package-lock.json", "downloaded_repos/krissrex_google-authenticator-exporter/package.json", "downloaded_repos/krissrex_google-authenticator-exporter/readme.md", "downloaded_repos/krissrex_google-authenticator-exporter/src/edbase32.js", "downloaded_repos/krissrex_google-authenticator-exporter/src/google_auth.proto", "downloaded_repos/krissrex_google-authenticator-exporter/src/index.js", "downloaded_repos/krissrex_google-authenticator-exporter/src/index.test.js", "downloaded_repos/krissrex_google-authenticator-exporter/test-assets/test-qr-codes.json"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6646759510040283, "profiling_times": {"config_time": 6.046876907348633, "core_time": 2.3823904991149902, "ignores_time": 0.0016734600067138672, "total_time": 8.432043075561523}, "parsing_time": {"total_time": 0.147139310836792, "per_file_time": {"mean": 0.021019901548113142, "std_dev": 0.00019776932365124484}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.4437894821166992, "per_file_time": {"mean": 0.01344816612474846, "std_dev": 0.0006787418003723252}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.12087416648864746, "per_file_and_rule_time": {"mean": 0.004168074706505084, "std_dev": 2.442171349821743e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.013143301010131836, "per_def_and_rule_time": {"mean": 0.00024339446315058954, "std_dev": 5.0758189913335405e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}