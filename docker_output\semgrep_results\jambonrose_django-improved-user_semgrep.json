{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/tests.py", "start": {"line": 33, "col": 9, "offset": 1115}, "end": {"line": 33, "col": 36, "offset": 1142}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n            user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/tests.py", "start": {"line": 35, "col": 9, "offset": 1206}, "end": {"line": 35, "col": 32, "offset": 1229}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(None, user=user):\n            user.set_password(None)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/tests.py", "start": {"line": 28, "col": 9, "offset": 912}, "end": {"line": 28, "col": 36, "offset": 939}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n            user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/tests.py", "start": {"line": 30, "col": 9, "offset": 1003}, "end": {"line": 30, "col": 32, "offset": 1026}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(None, user=user):\n            user.set_password(None)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/jambonrose_django-improved-user/src/improved_user/forms.py", "start": {"line": 85, "col": 9, "offset": 3117}, "end": {"line": 85, "col": 58, "offset": 3166}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(self.cleaned_data[\"password1\"], user=user):\n            user.set_password(self.cleaned_data[\"password1\"])", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/jambonrose_django-improved-user/src/improved_user/managers.py", "start": {"line": 38, "col": 9, "offset": 1221}, "end": {"line": 38, "col": 36, "offset": 1248}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n            user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "start": {"line": 56, "col": 37, "offset": 1471}, "end": {"line": 56, "col": 40, "offset": 1474}}]], "message": "Syntax error at line downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml:56:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "spans": [{"file": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "start": {"line": 56, "col": 37, "offset": 1471}, "end": {"line": 56, "col": 40, "offset": 1474}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "start": {"line": 60, "col": 54, "offset": 1745}, "end": {"line": 60, "col": 77, "offset": 1768}}]], "message": "Syntax error at line downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml:60:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.django-version` was unexpected", "path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "spans": [{"file": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "start": {"line": 60, "col": 54, "offset": 1745}, "end": {"line": 60, "col": 77, "offset": 1768}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "start": {"line": 64, "col": 16, "offset": 1875}, "end": {"line": 64, "col": 39, "offset": 1898}}]], "message": "Syntax error at line downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml:64:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.django-version` was unexpected", "path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "spans": [{"file": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "start": {"line": 64, "col": 16, "offset": 1875}, "end": {"line": 64, "col": 39, "offset": 1898}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "start": {"line": 55, "col": 54, "offset": 1594}, "end": {"line": 55, "col": 77, "offset": 1617}}]], "message": "Syntax error at line downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml:55:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.django-version` was unexpected", "path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "start": {"line": 55, "col": 54, "offset": 1594}, "end": {"line": 55, "col": 77, "offset": 1617}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "start": {"line": 58, "col": 36, "offset": 1724}, "end": {"line": 58, "col": 59, "offset": 1747}}, {"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "start": {"line": 58, "col": 74, "offset": 1724}, "end": {"line": 58, "col": 97, "offset": 1747}}]], "message": "Syntax error at line downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml:58:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.python-version` was unexpected", "path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "start": {"line": 58, "col": 36, "offset": 1724}, "end": {"line": 58, "col": 59, "offset": 1747}}, {"file": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "start": {"line": 58, "col": 74, "offset": 1724}, "end": {"line": 58, "col": 97, "offset": 1747}}]}], "paths": {"scanned": ["downloaded_repos/jambonrose_django-improved-user/.bumpversion.cfg", "downloaded_repos/jambonrose_django-improved-user/.coveragerc", "downloaded_repos/jambonrose_django-improved-user/.editorconfig", "downloaded_repos/jambonrose_django-improved-user/.flake8", "downloaded_repos/jambonrose_django-improved-user/.github/ISSUE_TEMPLATE.md", "downloaded_repos/jambonrose_django-improved-user/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/jambonrose_django-improved-user/.github/dependabot.yml", "downloaded_repos/jambonrose_django-improved-user/.github/labels.yml", "downloaded_repos/jambonrose_django-improved-user/.github/workflows/define-labels.yml", "downloaded_repos/jambonrose_django-improved-user/.github/workflows/publish-package.yml", "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "downloaded_repos/jambonrose_django-improved-user/.gitignore", "downloaded_repos/jambonrose_django-improved-user/.ignore", "downloaded_repos/jambonrose_django-improved-user/.isort.cfg", "downloaded_repos/jambonrose_django-improved-user/.pre-commit-config.yaml", "downloaded_repos/jambonrose_django-improved-user/.prettierignore", "downloaded_repos/jambonrose_django-improved-user/.prettierrc.toml", "downloaded_repos/jambonrose_django-improved-user/.pylintrc", "downloaded_repos/jambonrose_django-improved-user/.readthedocs.yaml", "downloaded_repos/jambonrose_django-improved-user/AUTHORS.rst", "downloaded_repos/jambonrose_django-improved-user/CODE_OF_CONDUCT.md", "downloaded_repos/jambonrose_django-improved-user/CONTRIBUTING.rst", "downloaded_repos/jambonrose_django-improved-user/HISTORY.rst", "downloaded_repos/jambonrose_django-improved-user/LICENSE", "downloaded_repos/jambonrose_django-improved-user/MANIFEST.in", "downloaded_repos/jambonrose_django-improved-user/Makefile", "downloaded_repos/jambonrose_django-improved-user/README.rst", "downloaded_repos/jambonrose_django-improved-user/codecov.yml", "downloaded_repos/jambonrose_django-improved-user/doc-requirements.txt", "downloaded_repos/jambonrose_django-improved-user/docs/Makefile", "downloaded_repos/jambonrose_django-improved-user/docs/admin_usage.rst", "downloaded_repos/jambonrose_django-improved-user/docs/change_log.rst", "downloaded_repos/jambonrose_django-improved-user/docs/conf.py", "downloaded_repos/jambonrose_django-improved-user/docs/contributing.rst", "downloaded_repos/jambonrose_django-improved-user/docs/create_custom_user_with_mixins.rst", "downloaded_repos/jambonrose_django-improved-user/docs/data_migrations.rst", "downloaded_repos/jambonrose_django-improved-user/docs/email_warning.rst", "downloaded_repos/jambonrose_django-improved-user/docs/index.rst", "downloaded_repos/jambonrose_django-improved-user/docs/integration.rst", "downloaded_repos/jambonrose_django-improved-user/docs/make.bat", "downloaded_repos/jambonrose_django-improved-user/docs/quickstart.rst", "downloaded_repos/jambonrose_django-improved-user/docs/quickstart_contrib.rst", "downloaded_repos/jambonrose_django-improved-user/docs/rationale.rst", "downloaded_repos/jambonrose_django-improved-user/docs/select_configuration_method.rst", "downloaded_repos/jambonrose_django-improved-user/docs/source/admin.rst", "downloaded_repos/jambonrose_django-improved-user/docs/source/factories.rst", "downloaded_repos/jambonrose_django-improved-user/docs/source/forms.rst", "downloaded_repos/jambonrose_django-improved-user/docs/source/managers.rst", "downloaded_repos/jambonrose_django-improved-user/docs/source/model_mixins.rst", "downloaded_repos/jambonrose_django-improved-user/docs/source/models.rst", "downloaded_repos/jambonrose_django-improved-user/docs/source/modules.rst", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/.coveragerc", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/config/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/config/settings.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/config/urls.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/config/wsgi.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/manage.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/requirements.txt", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/admin.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/apps.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/factories.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/migrations/0001_initial.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/migrations/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/models.py", "downloaded_repos/jambonrose_django-improved-user/example_extension_project/user_extension/tests.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/.coveragerc", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/config/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/config/settings.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/config/urls.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/config/wsgi.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/manage.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/requirements.txt", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/user_integration/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/user_integration/apps.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/user_integration/migrations/0001_add_user.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/user_integration/migrations/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/user_integration/templates/base.html", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/user_integration/templates/home.html", "downloaded_repos/jambonrose_django-improved-user/example_integration_project/user_integration/tests.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/.coveragerc", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/config/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/config/settings.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/config/urls.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/config/wsgi.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/manage.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/requirements.txt", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/apps.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/migrations/0001_initial.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/migrations/__init__.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/models.py", "downloaded_repos/jambonrose_django-improved-user/example_replacement_project/user_replacement/tests.py", "downloaded_repos/jambonrose_django-improved-user/pyproject.toml", "downloaded_repos/jambonrose_django-improved-user/requirements.txt", "downloaded_repos/jambonrose_django-improved-user/runtests.py", "downloaded_repos/jambonrose_django-improved-user/setup.cfg", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/__init__.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/admin.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/apps.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/factories.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/forms.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/managers.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/migrations/0001_initial.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/migrations/__init__.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/model_mixins.py", "downloaded_repos/jambonrose_django-improved-user/src/improved_user/models.py", "downloaded_repos/jambonrose_django-improved-user/tox.ini"], "skipped": [{"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-project-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jambonrose_django-improved-user/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_auth_backends.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_basic.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_factories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_management.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_managers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/test_signals.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jambonrose_django-improved-user/tests/urls.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.98642897605896, "profiling_times": {"config_time": 6.581907033920288, "core_time": 3.242586374282837, "ignores_time": 0.002531766891479492, "total_time": 9.828426599502563}, "parsing_time": {"total_time": 0.5022416114807129, "per_file_time": {"mean": 0.00913166566328569, "std_dev": 0.00019348259919729755}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.15852952003479, "per_file_time": {"mean": 0.011485561891035601, "std_dev": 0.0012284147211539562}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.681610107421875, "per_file_and_rule_time": {"mean": 0.0011572327800031836, "std_dev": 1.3366076853133589e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.10134148597717285, "per_def_and_rule_time": {"mean": 0.0006893978637902916, "std_dev": 3.1157551032563174e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}