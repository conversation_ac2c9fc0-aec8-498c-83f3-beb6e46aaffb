{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/venth_aws-adfs/aws_adfs/run_command.py", "start": {"line": 13, "col": 19, "offset": 305}, "end": {"line": 13, "col": 23, "offset": 309}, "extra": {"message": "Found 'subprocess' function 'run' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "start": {"line": 41, "col": 35, "offset": 1191}, "end": {"line": 41, "col": 38, "offset": 1194}}]], "message": "Syntax error at line downloaded_repos/venth_aws-adfs/.github/workflows/build.yml:41:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "spans": [{"file": "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "start": {"line": 41, "col": 35, "offset": 1191}, "end": {"line": 41, "col": 38, "offset": 1194}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "start": {"line": 71, "col": 35, "offset": 2034}, "end": {"line": 71, "col": 38, "offset": 2037}}]], "message": "Syntax error at line downloaded_repos/venth_aws-adfs/.github/workflows/build.yml:71:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "spans": [{"file": "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "start": {"line": 71, "col": 35, "offset": 2034}, "end": {"line": 71, "col": 38, "offset": 2037}}]}], "paths": {"scanned": ["downloaded_repos/venth_aws-adfs/.github/dependabot.yml", "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "downloaded_repos/venth_aws-adfs/.gitignore", "downloaded_repos/venth_aws-adfs/.pre-commit-config.yaml", "downloaded_repos/venth_aws-adfs/.travis.yml", "downloaded_repos/venth_aws-adfs/CHANGELOG.md", "downloaded_repos/venth_aws-adfs/LICENSE", "downloaded_repos/venth_aws-adfs/MANIFEST.in", "downloaded_repos/venth_aws-adfs/README.md", "downloaded_repos/venth_aws-adfs/aws_adfs/__init__.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_azure_cloud_mfa_authenticator.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_azure_mfa_authenticator.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_duo_authenticator.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_duo_universal_prompt_authenticator.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_rsa_authenticator.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_safenet_mfa.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_silverfort_authenticator.py", "downloaded_repos/venth_aws-adfs/aws_adfs/_symantec_vip_access.py", "downloaded_repos/venth_aws-adfs/aws_adfs/account_aliases_fetcher.py", "downloaded_repos/venth_aws-adfs/aws_adfs/authenticator.py", "downloaded_repos/venth_aws-adfs/aws_adfs/commands.py", "downloaded_repos/venth_aws-adfs/aws_adfs/consts.py", "downloaded_repos/venth_aws-adfs/aws_adfs/helpers.py", "downloaded_repos/venth_aws-adfs/aws_adfs/html_roles_fetcher.py", "downloaded_repos/venth_aws-adfs/aws_adfs/list_profiles.py", "downloaded_repos/venth_aws-adfs/aws_adfs/login.py", "downloaded_repos/venth_aws-adfs/aws_adfs/prepare.py", "downloaded_repos/venth_aws-adfs/aws_adfs/reset.py", "downloaded_repos/venth_aws-adfs/aws_adfs/role_chooser.py", "downloaded_repos/venth_aws-adfs/aws_adfs/roles_assertion_extractor.py", "downloaded_repos/venth_aws-adfs/aws_adfs/run_command.py", "downloaded_repos/venth_aws-adfs/poetry.lock", "downloaded_repos/venth_aws-adfs/pyproject.toml", "downloaded_repos/venth_aws-adfs/scripts/merge-dependabot-prs.sh", "downloaded_repos/venth_aws-adfs/scripts/release.sh", "downloaded_repos/venth_aws-adfs/scripts/update-readme-help.sh"], "skipped": [{"path": "downloaded_repos/venth_aws-adfs/.github/workflows/build.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/venth_aws-adfs/test/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_account_aliases_fetcher.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_assertion_extractor.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_authenticator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_click_parses_command_line_arguments.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_config_preparation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_credential_process_json.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_fetch_html_encoded_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_helpers_memset_zero.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_login_session_cache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_role_chooser.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/venth_aws-adfs/test/test_version.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.0962278842926025, "profiling_times": {"config_time": 6.49903416633606, "core_time": 4.534885406494141, "ignores_time": 0.0025353431701660156, "total_time": 11.03749680519104}, "parsing_time": {"total_time": 0.5733931064605713, "per_file_time": {"mean": 0.019772176084847284, "std_dev": 0.00032628676610086635}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 6.118854761123657, "per_file_time": {"mean": 0.06058272040716495, "std_dev": 0.034380723721249624}, "very_slow_stats": {"time_ratio": 0.26459479549384035, "count_ratio": 0.009900990099009901}, "very_slow_files": [{"fpath": "downloaded_repos/venth_aws-adfs/aws_adfs/login.py", "ftime": 1.6190171241760254}]}, "matching_time": {"total_time": 2.751500129699707, "per_file_and_rule_time": {"mean": 0.006325287654482086, "std_dev": 0.0012981910279959595}, "very_slow_stats": {"time_ratio": 0.3223033443240697, "count_ratio": 0.004597701149425287}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/venth_aws-adfs/aws_adfs/login.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.1698617935180664}, {"fpath": "downloaded_repos/venth_aws-adfs/aws_adfs/login.py", "rule_id": "python.flask.security.injection.ssrf-requests.ssrf-requests", "time": 0.7169559001922607}]}, "tainting_time": {"total_time": 0.6026849746704102, "per_def_and_rule_time": {"mean": 0.0005728944626144584, "std_dev": 2.1657598204163383e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}