{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/veliovgroup_Meteor-Files/lib.js", "start": {"line": 58, "col": 7, "offset": 1478}, "end": {"line": 58, "col": 25, "offset": 1496}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "path": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "start": {"line": 532, "col": 61, "offset": 21596}, "end": {"line": 532, "col": 83, "offset": 21618}, "extra": {"message": "By letting user input control CORS parameters, there is a risk that software does not properly verify that the source of data or communication is valid. Use literal values for CORS settings.", "metadata": {"owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-346: Origin Validation Error"], "category": "security", "references": ["https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS"], "technology": ["express"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.express.security.cors-misconfiguration.cors-misconfiguration", "shortlink": "https://sg.run/nKXO"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "path": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "start": {"line": 598, "col": 53, "offset": 23942}, "end": {"line": 598, "col": 89, "offset": 23978}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "references": ["https://en.wikipedia.org/wiki/Mass_assignment_vulnerability", "https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html"], "category": "security", "technology": ["express"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.express.security.express-data-exfiltration.express-data-exfiltration", "shortlink": "https://sg.run/pkpL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/veliovgroup_Meteor-Files/.eslintrc", "downloaded_repos/veliovgroup_Meteor-Files/.github/FUNDING.yml", "downloaded_repos/veliovgroup_Meteor-Files/.github/ISSUE_TEMPLATE", "downloaded_repos/veliovgroup_Meteor-Files/.github/PULL_REQUEST_TEMPLATE", "downloaded_repos/veliovgroup_Meteor-Files/.gitignore", "downloaded_repos/veliovgroup_Meteor-Files/.markdownlintrc", "downloaded_repos/veliovgroup_Meteor-Files/.meteorignore", "downloaded_repos/veliovgroup_Meteor-Files/.npmignore", "downloaded_repos/veliovgroup_Meteor-Files/.versions", "downloaded_repos/veliovgroup_Meteor-Files/CHANGELOG.md", "downloaded_repos/veliovgroup_Meteor-Files/CODE_OF_CONDUCT.md", "downloaded_repos/veliovgroup_Meteor-Files/CONTRIBUTING.md", "downloaded_repos/veliovgroup_Meteor-Files/HISTORY.md", "downloaded_repos/veliovgroup_Meteor-Files/LICENSE", "downloaded_repos/veliovgroup_Meteor-Files/README.md", "downloaded_repos/veliovgroup_Meteor-Files/client.js", "downloaded_repos/veliovgroup_Meteor-Files/core.js", "downloaded_repos/veliovgroup_Meteor-Files/cursor.js", "downloaded_repos/veliovgroup_Meteor-Files/demo/README.md", "downloaded_repos/veliovgroup_Meteor-Files/demo-simplest-download-button/README.md", "downloaded_repos/veliovgroup_Meteor-Files/demo-simplest-streaming/README.md", "downloaded_repos/veliovgroup_Meteor-Files/demo-simplest-upload/README.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/3rd-party-storage.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/FileCursor.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/FilesCursor.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/about-transports.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/addFile.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/aws-s3-integration.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/collection-instances.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/collection.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/constructor.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/convert-from-cfs-to-meteor-files.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/custom-response-headers.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/dropbox-integration.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/file-subversions.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/find.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/findOne.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/google-cloud-storage-integration.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/gridfs-bucket-integration.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/gridfs-integration.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/gridfs-migration.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/gridfs-streaming.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/image-processing.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/insert.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/link.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/load.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/meteorup-usage.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/react-example.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/readme.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/remove.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/schema.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/template-helper.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/toc.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/typescript-definitions.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/unlink.md", "downloaded_repos/veliovgroup_Meteor-Files/docs/write.md", "downloaded_repos/veliovgroup_Meteor-Files/index.d.ts", "downloaded_repos/veliovgroup_Meteor-Files/lib.js", "downloaded_repos/veliovgroup_Meteor-Files/logo-bw.png", "downloaded_repos/veliovgroup_Meteor-Files/logo.png", "downloaded_repos/veliovgroup_Meteor-Files/package.js", "downloaded_repos/veliovgroup_Meteor-Files/server.js", "downloaded_repos/veliovgroup_Meteor-Files/upload.js", "downloaded_repos/veliovgroup_Meteor-Files/worker.js", "downloaded_repos/veliovgroup_Meteor-Files/write-stream.js"], "skipped": [{"path": "downloaded_repos/veliovgroup_Meteor-Files/.npm/package/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/veliovgroup_Meteor-Files/.npm/package/README", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/veliovgroup_Meteor-Files/.npm/package/npm-shrinkwrap.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/veliovgroup_Meteor-Files/tests/helpers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/veliovgroup_Meteor-Files/worker.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6517231464385986, "profiling_times": {"config_time": 5.892770051956177, "core_time": 4.811844825744629, "ignores_time": 0.0020148754119873047, "total_time": 10.707412481307983}, "parsing_time": {"total_time": 0.7731938362121582, "per_file_time": {"mean": 0.07029034874655983, "std_dev": 0.00358250025344716}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.749130010604858, "per_file_time": {"mean": 0.03368177312485713, "std_dev": 0.04301630022398397}, "very_slow_stats": {"time_ratio": 0.4978962357854532, "count_ratio": 0.0070921985815602835}, "very_slow_files": [{"fpath": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "ftime": 2.3645739555358887}]}, "matching_time": {"total_time": 1.807633876800537, "per_file_and_rule_time": {"mean": 0.0127298160338066, "std_dev": 0.0012023303109957848}, "very_slow_stats": {"time_ratio": 0.3851832145703257, "count_ratio": 0.028169014084507043}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.11024117469787598}, {"fpath": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.12048912048339844}, {"fpath": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.137099027633667}, {"fpath": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.32844090461730957}]}, "tainting_time": {"total_time": 1.0675883293151855, "per_def_and_rule_time": {"mean": 0.001247182627704656, "std_dev": 1.6863545921674804e-05}, "very_slow_stats": {"time_ratio": 0.057158776985968524, "count_ratio": 0.0011682242990654205}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/veliovgroup_Meteor-Files/server.js", "fline": 122, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.061022043228149414}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}