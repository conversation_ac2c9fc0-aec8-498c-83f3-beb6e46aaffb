{"version": "1.130.0", "results": [{"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/dlib/embeddings.py", "start": {"line": 65, "col": 9, "offset": 1693}, "end": {"line": 65, "col": 27, "offset": 1711}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/dlib/face_recognition_test.py", "start": {"line": 27, "col": 14, "offset": 780}, "end": {"line": 28, "col": 24, "offset": 877}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "trailofbits.python.automatic-memory-pinning.automatic-memory-pinning", "path": "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/main.py", "start": {"line": 17, "col": 12, "offset": 440}, "end": {"line": 18, "col": 64, "offset": 567}, "extra": {"message": "If possible, it is better to rely on automatic pinning in PyTorch to avoid undefined behavior and for efficiency", "metadata": {"category": "security", "cwe": "CWE-676: Use of Potentially Dangerous Function", "subcategory": ["audit"], "confidence": "HIGH", "likelihood": "LOW", "impact": "LOW", "technology": ["pytorch"], "description": "`PyTorch` memory not automatically pinned", "references": ["https://pytorch.org/docs/stable/data.html#memory-pinning"], "license": "AGPL-3.0 license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/trailofbits.python.automatic-memory-pinning.automatic-memory-pinning", "shortlink": "https://sg.run/jz5N"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "trailofbits.python.pickles-in-pytorch.pickles-in-pytorch", "path": "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/main.py", "start": {"line": 293, "col": 23, "offset": 12481}, "end": {"line": 293, "col": 39, "offset": 12497}, "extra": {"message": "Functions reliant on pickle can result in arbitrary code execution.  Consider loading from `state_dict`, using fickling, or switching to a safer serialization method like ONNX", "metadata": {"category": "security", "cwe": "CWE-502: Deserialization of Untrusted Data", "subcategory": ["vuln"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "HIGH", "technology": ["pytorch"], "description": "Potential arbitrary code execution from `PyTorch` functions reliant on pickling", "references": ["https://blog.trailofbits.com/2021/03/15/never-a-dill-moment-exploiting-machine-learning-pickle-files/"], "license": "AGPL-3.0 license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/trailofbits.python.pickles-in-pytorch.pickles-in-pytorch", "shortlink": "https://sg.run/NwQy"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/IS2AI_SpeakingFaces/.idea/.gitignore", "downloaded_repos/IS2AI_SpeakingFaces/.idea/SpeakingFaces.iml", "downloaded_repos/IS2AI_SpeakingFaces/.idea/inspectionProfiles/profiles_settings.xml", "downloaded_repos/IS2AI_SpeakingFaces/.idea/misc.xml", "downloaded_repos/IS2AI_SpeakingFaces/.idea/modules.xml", "downloaded_repos/IS2AI_SpeakingFaces/.idea/vcs.xml", "downloaded_repos/IS2AI_SpeakingFaces/LICENSE", "downloaded_repos/IS2AI_SpeakingFaces/README.md", "downloaded_repos/IS2AI_SpeakingFaces/align_crop_session_two.py", "downloaded_repos/IS2AI_SpeakingFaces/align_session_one.py", "downloaded_repos/IS2AI_SpeakingFaces/align_session_one_all.py", "downloaded_repos/IS2AI_SpeakingFaces/align_session_two_all.py", "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/artifact_detector_v3.py", "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/find_rgb_shifts.py", "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/missing_positions_v2.py", "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/salvage_audio.py", "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/salvage_video.py", "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/trim_beginning.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/README.md", "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/arcface/arcface_embeddings.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/arcface/arcface_face_rec_test.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/dlib/embeddings.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/dlib/face_recognition_test.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/README.md", "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/dataset.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/main.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/model.py", "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/options.py", "downloaded_repos/IS2AI_SpeakingFaces/build_cycleGAN_data.py", "downloaded_repos/IS2AI_SpeakingFaces/build_face_img_data.py", "downloaded_repos/IS2AI_SpeakingFaces/build_pix2pix_data.py", "downloaded_repos/IS2AI_SpeakingFaces/calibration/aruco_detection.py", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/10_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/10_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/1_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/1_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/2_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/2_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/3_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/3_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/4_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/4_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/5_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/5_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/6_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/6_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/7_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/7_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/8_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/8_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/9_0.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/images/9_1.png", "downloaded_repos/IS2AI_SpeakingFaces/calibration/matched_features.xlsx", "downloaded_repos/IS2AI_SpeakingFaces/extract_images_by_commands.py", "downloaded_repos/IS2AI_SpeakingFaces/figures/aligned_session_one.png", "downloaded_repos/IS2AI_SpeakingFaces/figures/aligned_session_two.png", "downloaded_repos/IS2AI_SpeakingFaces/figures/aligned_session_two_manual.png", "downloaded_repos/IS2AI_SpeakingFaces/figures/speakingface.jpg", "downloaded_repos/IS2AI_SpeakingFaces/imgs2vid_session_1.ipynb", "downloaded_repos/IS2AI_SpeakingFaces/imgs2vid_session_2.ipynb", "downloaded_repos/IS2AI_SpeakingFaces/metadata/session_1/alignment_info_test.csv", "downloaded_repos/IS2AI_SpeakingFaces/metadata/session_1/alignment_info_train.csv", "downloaded_repos/IS2AI_SpeakingFaces/metadata/session_1/alignment_info_valid.csv", "downloaded_repos/IS2AI_SpeakingFaces/metadata/session_2/train.csv", "downloaded_repos/IS2AI_SpeakingFaces/metadata/session_2/valid.csv", "downloaded_repos/IS2AI_SpeakingFaces/metadata/subjects.csv", "downloaded_repos/IS2AI_SpeakingFaces/models/deploy.prototxt.txt", "downloaded_repos/IS2AI_SpeakingFaces/record_matlab/extract_images_from_videos.m", "downloaded_repos/IS2AI_SpeakingFaces/record_matlab/extract_video_audio_by_commands.m", "downloaded_repos/IS2AI_SpeakingFaces/record_matlab/record_audio_video.m", "downloaded_repos/IS2AI_SpeakingFaces/record_matlab/record_only_video.m", "downloaded_repos/IS2AI_SpeakingFaces/speakingfacespy/__init__.py", "downloaded_repos/IS2AI_SpeakingFaces/speakingfacespy/__pycache__/imtools.cpython-37.pyc", "downloaded_repos/IS2AI_SpeakingFaces/speakingfacespy/imtools.py", "downloaded_repos/IS2AI_SpeakingFaces/trim_audio.py"], "skipped": [{"path": "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/arcface/embeddings/real_trial_1_emb.pkl", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/IS2AI_SpeakingFaces/baseline_domain_transfer/dlib/embeddings/embeddings_128.pickle", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/IS2AI_SpeakingFaces/figures/aligned_markers_v3.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/IS2AI_SpeakingFaces/figures/nine_positions_v5.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/IS2AI_SpeakingFaces/figures/setup.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/IS2AI_SpeakingFaces/figures/thermal2visible.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/IS2AI_SpeakingFaces/figures/timelapse_v3.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/IS2AI_SpeakingFaces/models/res10_300x300_ssd_iter_140000.caffemodel", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.9073078632354736, "profiling_times": {"config_time": 6.4321608543396, "core_time": 3.3930718898773193, "ignores_time": 0.0022094249725341797, "total_time": 9.828593015670776}, "parsing_time": {"total_time": 0.7901732921600342, "per_file_time": {"mean": 0.02822047472000122, "std_dev": 0.0004544008073936416}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.949361562728882, "per_file_time": {"mean": 0.027805402037802705, "std_dev": 0.005470713590294725}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6836230754852295, "per_file_and_rule_time": {"mean": 0.003862277262628415, "std_dev": 4.23887787331698e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 1.4790558815002441, "per_def_and_rule_time": {"mean": 0.02175082178676829, "std_dev": 0.002055577543269636}, "very_slow_stats": {"time_ratio": 0.8177625608315159, "count_ratio": 0.14705882352941177}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/IS2AI_SpeakingFaces/build_pix2pix_data.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.056633949279785156}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/find_rgb_shifts.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.07179808616638184}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/build_cycleGAN_data.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.08125710487365723}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/align_crop_session_two.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.09218311309814453}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/main.py", "fline": 26, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.10083699226379395}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/trim_beginning.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.14227700233459473}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/artifact_detector/missing_positions_v2.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.14466309547424316}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/main.py", "fline": 108, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.1571030616760254}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/baseline_gender/dataset.py", "fline": 13, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.15770411491394043}, {"fpath": "downloaded_repos/IS2AI_SpeakingFaces/build_face_img_data.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20506000518798828}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}