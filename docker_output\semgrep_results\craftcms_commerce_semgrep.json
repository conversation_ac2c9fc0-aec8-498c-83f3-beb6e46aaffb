{"version": "1.130.0", "results": [{"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/CartController.php", "start": {"line": 386, "col": 49, "offset": 15287}, "end": {"line": 386, "col": 75, "offset": 15313}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/CartController.php", "start": {"line": 399, "col": 49, "offset": 15708}, "end": {"line": 399, "col": 75, "offset": 15734}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/CartController.php", "start": {"line": 412, "col": 45, "offset": 16150}, "end": {"line": 412, "col": 71, "offset": 16176}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/CatalogPricingRulesController.php", "start": {"line": 300, "col": 16, "offset": 12340}, "end": {"line": 300, "col": 62, "offset": 12386}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/DiscountsController.php", "start": {"line": 491, "col": 16, "offset": 18708}, "end": {"line": 491, "col": 62, "offset": 18754}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/InventoryController.php", "start": {"line": 158, "col": 24, "offset": 5950}, "end": {"line": 158, "col": 102, "offset": 6028}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/OrdersController.php", "start": {"line": 201, "col": 16, "offset": 6531}, "end": {"line": 201, "col": 64, "offset": 6579}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/PaymentsController.php", "start": {"line": 525, "col": 16, "offset": 21343}, "end": {"line": 525, "col": 42, "offset": 21369}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/PaymentsController.php", "start": {"line": 562, "col": 20, "offset": 22487}, "end": {"line": 562, "col": 67, "offset": 22534}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/PaymentsController.php", "start": {"line": 573, "col": 16, "offset": 22766}, "end": {"line": 573, "col": 63, "offset": 22813}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/SalesController.php", "start": {"line": 65, "col": 20, "offset": 1735}, "end": {"line": 65, "col": 99, "offset": 1814}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/SalesController.php", "start": {"line": 286, "col": 16, "offset": 9443}, "end": {"line": 286, "col": 62, "offset": 9489}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/StoreManagementController.php", "start": {"line": 43, "col": 20, "offset": 1204}, "end": {"line": 43, "col": 77, "offset": 1261}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/StoreManagementController.php", "start": {"line": 47, "col": 20, "offset": 1367}, "end": {"line": 47, "col": 97, "offset": 1444}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/StoreManagementController.php", "start": {"line": 51, "col": 20, "offset": 1543}, "end": {"line": 51, "col": 88, "offset": 1611}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/StoreManagementController.php", "start": {"line": 55, "col": 20, "offset": 1708}, "end": {"line": 55, "col": 87, "offset": 1775}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/StoreManagementController.php", "start": {"line": 59, "col": 20, "offset": 1869}, "end": {"line": 59, "col": 87, "offset": 1936}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/StoreManagementController.php", "start": {"line": 90, "col": 24, "offset": 3116}, "end": {"line": 90, "col": 81, "offset": 3173}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/craftcms_commerce/src/controllers/SubscriptionsController.php", "start": {"line": 253, "col": 24, "offset": 9631}, "end": {"line": 253, "col": 116, "offset": 9723}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/craftcms_commerce/src/services/Emails.php", "start": {"line": 915, "col": 13, "offset": 34307}, "end": {"line": 915, "col": 30, "offset": 34324}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/src/js/CatalogPricing.js", "start": {"line": 161, "col": 64, "offset": 4232}, "end": {"line": 161, "col": 78, "offset": 4246}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderCustomer.vue", "start": {"line": 44, "col": 21, "offset": 1606}, "end": {"line": 46, "col": 32, "offset": 1725}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderCustomer.vue", "start": {"line": 59, "col": 21, "offset": 2257}, "end": {"line": 61, "col": 32, "offset": 2376}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderMeta.vue", "start": {"line": 166, "col": 21, "offset": 6365}, "end": {"line": 168, "col": 32, "offset": 6440}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderMeta.vue", "start": {"line": 237, "col": 17, "offset": 9140}, "end": {"line": 237, "col": 44, "offset": 9167}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/customer/AddressEdit.vue", "start": {"line": 5, "col": 17, "offset": 135}, "end": {"line": 7, "col": 28, "offset": 200}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/customer/AddressSelect.vue", "start": {"line": 35, "col": 29, "offset": 1335}, "end": {"line": 35, "col": 40, "offset": 1346}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/store/index.js", "start": {"line": 227, "col": 23, "offset": 4957}, "end": {"line": 227, "col": 48, "offset": 4982}, "extra": {"message": "RegExp() called with a `key` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/src/CommerceChart.js", "start": {"line": 227, "col": 15, "offset": 6281}, "end": {"line": 227, "col": 47, "offset": 6313}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/craftcms_commerce/src/models/Settings.php", "start": {"line": 260, "col": 47, "offset": 0}, "end": {"line": 260, "col": 48, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/craftcms_commerce/src/models/Settings.php:260:\n `,` was unexpected", "path": "downloaded_repos/craftcms_commerce/src/models/Settings.php", "spans": [{"file": "downloaded_repos/craftcms_commerce/src/models/Settings.php", "start": {"line": 260, "col": 47, "offset": 0}, "end": {"line": 260, "col": 48, "offset": 1}}]}], "paths": {"scanned": ["downloaded_repos/craftcms_commerce/.codecov.yml", "downloaded_repos/craftcms_commerce/.editorconfig", "downloaded_repos/craftcms_commerce/.env.example", "downloaded_repos/craftcms_commerce/.gitattributes", "downloaded_repos/craftcms_commerce/.github/CODEOWNERS", "downloaded_repos/craftcms_commerce/.github/ISSUE_TEMPLATE/BUG-REPORT-V3.yml", "downloaded_repos/craftcms_commerce/.github/ISSUE_TEMPLATE/BUG-REPORT-V4.yml", "downloaded_repos/craftcms_commerce/.github/ISSUE_TEMPLATE/BUG-REPORT-V5.yml", "downloaded_repos/craftcms_commerce/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/craftcms_commerce/.github/workflows/ci.yml", "downloaded_repos/craftcms_commerce/.github/workflows/create-release.yml", "downloaded_repos/craftcms_commerce/.gitignore", "downloaded_repos/craftcms_commerce/.nvmrc", "downloaded_repos/craftcms_commerce/.prettierignore", "downloaded_repos/craftcms_commerce/.prettierrc.json", "downloaded_repos/craftcms_commerce/CHANGELOG.md", "downloaded_repos/craftcms_commerce/LICENSE.md", "downloaded_repos/craftcms_commerce/README.md", "downloaded_repos/craftcms_commerce/codeception.yml", "downloaded_repos/craftcms_commerce/composer.json", "downloaded_repos/craftcms_commerce/composer.lock", "downloaded_repos/craftcms_commerce/crowdin.yml", "downloaded_repos/craftcms_commerce/ecs.php", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/address/fields.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/address/fieldset.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/address/list.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/images/placeholder.svg", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/images/spinner.svg", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/layouts/includes/footer.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/layouts/includes/header.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/layouts/includes/nav-checkout.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/layouts/includes/nav-customer.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/layouts/includes/nav-main.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/layouts/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/payments/payment-post-redirect.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/_private/receipt/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/cart/_includes/shipping-estimator.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/cart/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/cart/load.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/_includes/currencies.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/_includes/gateways.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/_includes/order-summary.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/_includes/partial-payment.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/_paymentLegacyStripeExample.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/addresses.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/complete.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/email.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/options.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/pay-static.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/payment-method.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/payment.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/register-signin.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/checkout/shipping.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/_includes/register.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/_includes/sign-in.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/addresses/edit.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/addresses/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/cards.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/forgot-password.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/order.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/orders.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/customer/sign-in.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/donations/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/plans/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/plans/subscription/index.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/plans/update-billing-details.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/products/_includes/grid.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/products/_includes/pagination.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/products/_product.twig", "downloaded_repos/craftcms_commerce/example-templates/src/shop/products/index.twig", "downloaded_repos/craftcms_commerce/package-lock.json", "downloaded_repos/craftcms_commerce/package.json", "downloaded_repos/craftcms_commerce/phpstan.neon", "downloaded_repos/craftcms_commerce/rector.php", "downloaded_repos/craftcms_commerce/src/Plugin.php", "downloaded_repos/craftcms_commerce/src/adjusters/Discount.php", "downloaded_repos/craftcms_commerce/src/adjusters/Shipping.php", "downloaded_repos/craftcms_commerce/src/adjusters/Tax.php", "downloaded_repos/craftcms_commerce/src/base/AdjusterInterface.php", "downloaded_repos/craftcms_commerce/src/base/CatalogPricingConditionRuleInterface.php", "downloaded_repos/craftcms_commerce/src/base/EnumHelpersTrait.php", "downloaded_repos/craftcms_commerce/src/base/Gateway.php", "downloaded_repos/craftcms_commerce/src/base/GatewayInterface.php", "downloaded_repos/craftcms_commerce/src/base/GatewayTrait.php", "downloaded_repos/craftcms_commerce/src/base/HasStoreInterface.php", "downloaded_repos/craftcms_commerce/src/base/InventoryItemTrait.php", "downloaded_repos/craftcms_commerce/src/base/InventoryLocationTrait.php", "downloaded_repos/craftcms_commerce/src/base/InventoryMovement.php", "downloaded_repos/craftcms_commerce/src/base/InventoryMovementInterface.php", "downloaded_repos/craftcms_commerce/src/base/Model.php", "downloaded_repos/craftcms_commerce/src/base/Plan.php", "downloaded_repos/craftcms_commerce/src/base/PlanInterface.php", "downloaded_repos/craftcms_commerce/src/base/PlanTrait.php", "downloaded_repos/craftcms_commerce/src/base/Purchasable.php", "downloaded_repos/craftcms_commerce/src/base/PurchasableInterface.php", "downloaded_repos/craftcms_commerce/src/base/RequestResponseInterface.php", "downloaded_repos/craftcms_commerce/src/base/ShippingMethod.php", "downloaded_repos/craftcms_commerce/src/base/ShippingMethodInterface.php", "downloaded_repos/craftcms_commerce/src/base/ShippingRuleInterface.php", "downloaded_repos/craftcms_commerce/src/base/Stat.php", "downloaded_repos/craftcms_commerce/src/base/StatInterface.php", "downloaded_repos/craftcms_commerce/src/base/StatTrait.php", "downloaded_repos/craftcms_commerce/src/base/StatWidgetTrait.php", "downloaded_repos/craftcms_commerce/src/base/StoreRecordTrait.php", "downloaded_repos/craftcms_commerce/src/base/StoreTrait.php", "downloaded_repos/craftcms_commerce/src/base/SubscriptionGateway.php", "downloaded_repos/craftcms_commerce/src/base/SubscriptionGatewayInterface.php", "downloaded_repos/craftcms_commerce/src/base/SubscriptionResponseInterface.php", "downloaded_repos/craftcms_commerce/src/base/TaxEngineInterface.php", "downloaded_repos/craftcms_commerce/src/base/TaxIdValidatorInterface.php", "downloaded_repos/craftcms_commerce/src/base/Zone.php", "downloaded_repos/craftcms_commerce/src/base/ZoneInterface.php", "downloaded_repos/craftcms_commerce/src/behaviors/CurrencyAttributeBehavior.php", "downloaded_repos/craftcms_commerce/src/behaviors/CustomerAddressBehavior.php", "downloaded_repos/craftcms_commerce/src/behaviors/CustomerBehavior.php", "downloaded_repos/craftcms_commerce/src/behaviors/StoreBehavior.php", "downloaded_repos/craftcms_commerce/src/behaviors/StoreLocationBehavior.php", "downloaded_repos/craftcms_commerce/src/behaviors/ValidateOrganizationTaxIdBehavior.php", "downloaded_repos/craftcms_commerce/src/collections/InventoryMovementCollection.php", "downloaded_repos/craftcms_commerce/src/collections/UpdateInventoryLevelCollection.php", "downloaded_repos/craftcms_commerce/src/console/Controller.php", "downloaded_repos/craftcms_commerce/src/console/controllers/ExampleTemplatesController.php", "downloaded_repos/craftcms_commerce/src/console/controllers/GatewaysController.php", "downloaded_repos/craftcms_commerce/src/console/controllers/PricingCatalogController.php", "downloaded_repos/craftcms_commerce/src/console/controllers/ResetDataController.php", "downloaded_repos/craftcms_commerce/src/console/controllers/TransferCustomerDataController.php", "downloaded_repos/craftcms_commerce/src/controllers/BaseAdminController.php", "downloaded_repos/craftcms_commerce/src/controllers/BaseController.php", "downloaded_repos/craftcms_commerce/src/controllers/BaseCpController.php", "downloaded_repos/craftcms_commerce/src/controllers/BaseFrontEndController.php", "downloaded_repos/craftcms_commerce/src/controllers/BaseShippingSettingsController.php", "downloaded_repos/craftcms_commerce/src/controllers/BaseStoreManagementController.php", "downloaded_repos/craftcms_commerce/src/controllers/BaseTaxSettingsController.php", "downloaded_repos/craftcms_commerce/src/controllers/CartController.php", "downloaded_repos/craftcms_commerce/src/controllers/CatalogPricingController.php", "downloaded_repos/craftcms_commerce/src/controllers/CatalogPricingRulesController.php", "downloaded_repos/craftcms_commerce/src/controllers/DiscountsController.php", "downloaded_repos/craftcms_commerce/src/controllers/DonationsController.php", "downloaded_repos/craftcms_commerce/src/controllers/DownloadsController.php", "downloaded_repos/craftcms_commerce/src/controllers/EmailPreviewController.php", "downloaded_repos/craftcms_commerce/src/controllers/EmailsController.php", "downloaded_repos/craftcms_commerce/src/controllers/FormulasController.php", "downloaded_repos/craftcms_commerce/src/controllers/GatewaysController.php", "downloaded_repos/craftcms_commerce/src/controllers/InventoryController.php", "downloaded_repos/craftcms_commerce/src/controllers/InventoryLocationsController.php", "downloaded_repos/craftcms_commerce/src/controllers/LineItemStatusesController.php", "downloaded_repos/craftcms_commerce/src/controllers/OrderSettingsController.php", "downloaded_repos/craftcms_commerce/src/controllers/OrderStatusesController.php", "downloaded_repos/craftcms_commerce/src/controllers/OrdersController.php", "downloaded_repos/craftcms_commerce/src/controllers/PaymentCurrenciesController.php", "downloaded_repos/craftcms_commerce/src/controllers/PaymentSourcesController.php", "downloaded_repos/craftcms_commerce/src/controllers/PaymentsController.php", "downloaded_repos/craftcms_commerce/src/controllers/PdfsController.php", "downloaded_repos/craftcms_commerce/src/controllers/PlansController.php", "downloaded_repos/craftcms_commerce/src/controllers/ProductTypesController.php", "downloaded_repos/craftcms_commerce/src/controllers/ProductsController.php", "downloaded_repos/craftcms_commerce/src/controllers/PromotionsController.php", "downloaded_repos/craftcms_commerce/src/controllers/SalesController.php", "downloaded_repos/craftcms_commerce/src/controllers/SettingsController.php", "downloaded_repos/craftcms_commerce/src/controllers/ShippingCategoriesController.php", "downloaded_repos/craftcms_commerce/src/controllers/ShippingMethodsController.php", "downloaded_repos/craftcms_commerce/src/controllers/ShippingRulesController.php", "downloaded_repos/craftcms_commerce/src/controllers/ShippingZonesController.php", "downloaded_repos/craftcms_commerce/src/controllers/StoreManagementController.php", "downloaded_repos/craftcms_commerce/src/controllers/StoresController.php", "downloaded_repos/craftcms_commerce/src/controllers/SubscriptionsController.php", "downloaded_repos/craftcms_commerce/src/controllers/TaxCategoriesController.php", "downloaded_repos/craftcms_commerce/src/controllers/TaxRatesController.php", "downloaded_repos/craftcms_commerce/src/controllers/TaxZonesController.php", "downloaded_repos/craftcms_commerce/src/controllers/TransfersController.php", "downloaded_repos/craftcms_commerce/src/controllers/UserOrdersController.php", "downloaded_repos/craftcms_commerce/src/controllers/UsersController.php", "downloaded_repos/craftcms_commerce/src/controllers/VariantsController.php", "downloaded_repos/craftcms_commerce/src/controllers/WebhooksController.php", "downloaded_repos/craftcms_commerce/src/db/Table.php", "downloaded_repos/craftcms_commerce/src/debug/CommercePanel.php", "downloaded_repos/craftcms_commerce/src/elements/Donation.php", "downloaded_repos/craftcms_commerce/src/elements/Order.php", "downloaded_repos/craftcms_commerce/src/elements/Product.php", "downloaded_repos/craftcms_commerce/src/elements/Subscription.php", "downloaded_repos/craftcms_commerce/src/elements/Transfer.php", "downloaded_repos/craftcms_commerce/src/elements/Variant.php", "downloaded_repos/craftcms_commerce/src/elements/VariantCollection.php", "downloaded_repos/craftcms_commerce/src/elements/actions/CopyLoadCartUrl.php", "downloaded_repos/craftcms_commerce/src/elements/actions/CreateDiscount.php", "downloaded_repos/craftcms_commerce/src/elements/actions/CreateSale.php", "downloaded_repos/craftcms_commerce/src/elements/actions/DownloadOrderPdfAction.php", "downloaded_repos/craftcms_commerce/src/elements/actions/SetDefaultVariant.php", "downloaded_repos/craftcms_commerce/src/elements/actions/UpdateOrderStatus.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/addresses/DiscountAddressCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/addresses/PostalCodeFormulaConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/addresses/ZoneAddressCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/customers/CatalogPricingRuleCustomerCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/customers/DiscountCustomerCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/customers/HasOrdersConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/customers/ShippingMethodCustomerCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/customers/ShippingRuleCustomerCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/customers/SignedInConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/CompletedConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/CouponCodeConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/CustomerConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/DateOrderedConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/DiscountOrderCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/DiscountedItemSubtotalConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/GatewayOrderCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/HasPurchasableConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/ItemSubtotalConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/ItemTotalConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/OrderCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/OrderCurrencyValuesAttributeConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/OrderSiteConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/OrderStatusConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/OrderTextValuesAttributeConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/OrderValuesAttributeConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/PaidConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/PaymentGatewayConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/ReferenceConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/ShippingAddressZoneConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/ShippingMethodConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/ShippingMethodOrderCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/ShippingRuleOrderCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/TotalConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/TotalDiscountConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/TotalPaidConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/TotalPriceConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/TotalQtyConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/TotalTaxConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/orders/TotalWeightConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/CatalogPricingRuleProductCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductTypeConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductVariantHasUnlimitedStockConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductVariantInventoryTrackedConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductVariantPriceConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductVariantSearchConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductVariantSkuConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/products/ProductVariantStockConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/CatalogPricingCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/CatalogPricingCustomerConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/CatalogPricingPurchasableConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/CatalogPricingRulePurchasableCategoryConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/CatalogPricingRulePurchasableCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/PurchasableConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/PurchasableTypeConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/purchasables/SkuConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/transfers/TransferCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/users/DiscountGroupConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/variants/CatalogPricingRuleVariantCondition.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/variants/ProductConditionRule.php", "downloaded_repos/craftcms_commerce/src/elements/conditions/variants/VariantCondition.php", "downloaded_repos/craftcms_commerce/src/elements/db/DonationQuery.php", "downloaded_repos/craftcms_commerce/src/elements/db/OrderQuery.php", "downloaded_repos/craftcms_commerce/src/elements/db/ProductQuery.php", "downloaded_repos/craftcms_commerce/src/elements/db/PurchasableQuery.php", "downloaded_repos/craftcms_commerce/src/elements/db/SubscriptionQuery.php", "downloaded_repos/craftcms_commerce/src/elements/db/TransferQuery.php", "downloaded_repos/craftcms_commerce/src/elements/db/VariantQuery.php", "downloaded_repos/craftcms_commerce/src/elements/traits/OrderElementTrait.php", "downloaded_repos/craftcms_commerce/src/elements/traits/OrderNoticesTrait.php", "downloaded_repos/craftcms_commerce/src/elements/traits/OrderValidatorsTrait.php", "downloaded_repos/craftcms_commerce/src/engines/Tax.php", "downloaded_repos/craftcms_commerce/src/enums/InventoryTransactionType.php", "downloaded_repos/craftcms_commerce/src/enums/InventoryUpdateQuantityType.php", "downloaded_repos/craftcms_commerce/src/enums/LineItemType.php", "downloaded_repos/craftcms_commerce/src/enums/TransferStatusType.php", "downloaded_repos/craftcms_commerce/src/errors/CurrencyException.php", "downloaded_repos/craftcms_commerce/src/errors/EmailException.php", "downloaded_repos/craftcms_commerce/src/errors/GatewayException.php", "downloaded_repos/craftcms_commerce/src/errors/LineItemException.php", "downloaded_repos/craftcms_commerce/src/errors/LineItemNotFoundException.php", "downloaded_repos/craftcms_commerce/src/errors/NotImplementedException.php", "downloaded_repos/craftcms_commerce/src/errors/OrderAdjustmentNotFoundException.php", "downloaded_repos/craftcms_commerce/src/errors/OrderStatusException.php", "downloaded_repos/craftcms_commerce/src/errors/PaymentException.php", "downloaded_repos/craftcms_commerce/src/errors/PaymentSourceCreatedLaterException.php", "downloaded_repos/craftcms_commerce/src/errors/PaymentSourceException.php", "downloaded_repos/craftcms_commerce/src/errors/ProductTypeNotFoundException.php", "downloaded_repos/craftcms_commerce/src/errors/RefundException.php", "downloaded_repos/craftcms_commerce/src/errors/ShippingMethodException.php", "downloaded_repos/craftcms_commerce/src/errors/StoreNotFoundException.php", "downloaded_repos/craftcms_commerce/src/errors/SubscriptionException.php", "downloaded_repos/craftcms_commerce/src/errors/TransactionException.php", "downloaded_repos/craftcms_commerce/src/etc/commands.php", "downloaded_repos/craftcms_commerce/src/etc/currencies.php", "downloaded_repos/craftcms_commerce/src/events/AddLineItemEvent.php", "downloaded_repos/craftcms_commerce/src/events/CancelSubscriptionEvent.php", "downloaded_repos/craftcms_commerce/src/events/CartEvent.php", "downloaded_repos/craftcms_commerce/src/events/CartPurgeEvent.php", "downloaded_repos/craftcms_commerce/src/events/CommerceDebugPanelDataEvent.php", "downloaded_repos/craftcms_commerce/src/events/CreateSubscriptionEvent.php", "downloaded_repos/craftcms_commerce/src/events/CustomizeProductSnapshotDataEvent.php", "downloaded_repos/craftcms_commerce/src/events/CustomizeProductSnapshotFieldsEvent.php", "downloaded_repos/craftcms_commerce/src/events/CustomizeVariantSnapshotDataEvent.php", "downloaded_repos/craftcms_commerce/src/events/CustomizeVariantSnapshotFieldsEvent.php", "downloaded_repos/craftcms_commerce/src/events/DefaultLineItemStatusEvent.php", "downloaded_repos/craftcms_commerce/src/events/DefaultOrderStatusEvent.php", "downloaded_repos/craftcms_commerce/src/events/DeleteStoreEvent.php", "downloaded_repos/craftcms_commerce/src/events/DiscountAdjustmentsEvent.php", "downloaded_repos/craftcms_commerce/src/events/DiscountEvent.php", "downloaded_repos/craftcms_commerce/src/events/EmailEvent.php", "downloaded_repos/craftcms_commerce/src/events/LineItemEvent.php", "downloaded_repos/craftcms_commerce/src/events/MailEvent.php", "downloaded_repos/craftcms_commerce/src/events/MatchLineItemEvent.php", "downloaded_repos/craftcms_commerce/src/events/MatchOrderEvent.php", "downloaded_repos/craftcms_commerce/src/events/ModifyCartInfoEvent.php", "downloaded_repos/craftcms_commerce/src/events/ModifyPurchasablesTableQueryEvent.php", "downloaded_repos/craftcms_commerce/src/events/OrderLineItemsRefreshEvent.php", "downloaded_repos/craftcms_commerce/src/events/OrderNoticeEvent.php", "downloaded_repos/craftcms_commerce/src/events/OrderStatusEmailsEvent.php", "downloaded_repos/craftcms_commerce/src/events/OrderStatusEvent.php", "downloaded_repos/craftcms_commerce/src/events/PaymentSourceEvent.php", "downloaded_repos/craftcms_commerce/src/events/PdfEvent.php", "downloaded_repos/craftcms_commerce/src/events/PdfRenderEvent.php", "downloaded_repos/craftcms_commerce/src/events/PdfRenderOptionsEvent.php", "downloaded_repos/craftcms_commerce/src/events/PlanEvent.php", "downloaded_repos/craftcms_commerce/src/events/ProcessPaymentEvent.php", "downloaded_repos/craftcms_commerce/src/events/ProductEvent.php", "downloaded_repos/craftcms_commerce/src/events/ProductTypeEvent.php", "downloaded_repos/craftcms_commerce/src/events/PurchasableAvailableEvent.php", "downloaded_repos/craftcms_commerce/src/events/PurchasableOutOfStockPurchasesAllowedEvent.php", "downloaded_repos/craftcms_commerce/src/events/PurchasableShippableEvent.php", "downloaded_repos/craftcms_commerce/src/events/PurchaseVariantEvent.php", "downloaded_repos/craftcms_commerce/src/events/PurgeAddressesEvent.php", "downloaded_repos/craftcms_commerce/src/events/RefundTransactionEvent.php", "downloaded_repos/craftcms_commerce/src/events/RegisterAvailableShippingMethodsEvent.php", "downloaded_repos/craftcms_commerce/src/events/ReportEvent.php", "downloaded_repos/craftcms_commerce/src/events/SaleEvent.php", "downloaded_repos/craftcms_commerce/src/events/SaleMatchEvent.php", "downloaded_repos/craftcms_commerce/src/events/StoreEvent.php", "downloaded_repos/craftcms_commerce/src/events/SubscriptionEvent.php", "downloaded_repos/craftcms_commerce/src/events/SubscriptionPaymentEvent.php", "downloaded_repos/craftcms_commerce/src/events/SubscriptionSwitchPlansEvent.php", "downloaded_repos/craftcms_commerce/src/events/TaxEngineEvent.php", "downloaded_repos/craftcms_commerce/src/events/TaxIdValidatorsEvent.php", "downloaded_repos/craftcms_commerce/src/events/TransactionEvent.php", "downloaded_repos/craftcms_commerce/src/events/UpdatePrimaryPaymentSourceEvent.php", "downloaded_repos/craftcms_commerce/src/events/UpgradeEvent.php", "downloaded_repos/craftcms_commerce/src/events/WebhookEvent.php", "downloaded_repos/craftcms_commerce/src/exports/Expanded.php", "downloaded_repos/craftcms_commerce/src/exports/LineItemExport.php", "downloaded_repos/craftcms_commerce/src/exports/OrderExport.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/ProductTitleField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasableAllowedQtyField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasableAvailableForPurchaseField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasableDimensionsField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasableFreeShippingField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasablePriceField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasablePromotableField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasableSkuField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasableStockField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/PurchasableWeightField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/TransferManagementField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/UserAddressSettings.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/VariantTitleField.php", "downloaded_repos/craftcms_commerce/src/fieldlayoutelements/VariantsField.php", "downloaded_repos/craftcms_commerce/src/fields/Products.php", "downloaded_repos/craftcms_commerce/src/fields/Variants.php", "downloaded_repos/craftcms_commerce/src/gateways/Dummy.php", "downloaded_repos/craftcms_commerce/src/gateways/Manual.php", "downloaded_repos/craftcms_commerce/src/gateways/MissingGateway.php", "downloaded_repos/craftcms_commerce/src/gql/arguments/elements/Product.php", "downloaded_repos/craftcms_commerce/src/gql/arguments/elements/Variant.php", "downloaded_repos/craftcms_commerce/src/gql/interfaces/elements/Product.php", "downloaded_repos/craftcms_commerce/src/gql/interfaces/elements/Variant.php", "downloaded_repos/craftcms_commerce/src/gql/queries/Product.php", "downloaded_repos/craftcms_commerce/src/gql/queries/Variant.php", "downloaded_repos/craftcms_commerce/src/gql/resolvers/elements/Product.php", "downloaded_repos/craftcms_commerce/src/gql/resolvers/elements/Variant.php", "downloaded_repos/craftcms_commerce/src/gql/types/SaleType.php", "downloaded_repos/craftcms_commerce/src/gql/types/elements/Product.php", "downloaded_repos/craftcms_commerce/src/gql/types/elements/Variant.php", "downloaded_repos/craftcms_commerce/src/gql/types/generators/ProductType.php", "downloaded_repos/craftcms_commerce/src/gql/types/generators/VariantType.php", "downloaded_repos/craftcms_commerce/src/gql/types/input/IntFalse.php", "downloaded_repos/craftcms_commerce/src/gql/types/input/Product.php", "downloaded_repos/craftcms_commerce/src/gql/types/input/Variant.php", "downloaded_repos/craftcms_commerce/src/helpers/Cp.php", "downloaded_repos/craftcms_commerce/src/helpers/Currency.php", "downloaded_repos/craftcms_commerce/src/helpers/DebugPanel.php", "downloaded_repos/craftcms_commerce/src/helpers/Gql.php", "downloaded_repos/craftcms_commerce/src/helpers/LineItem.php", "downloaded_repos/craftcms_commerce/src/helpers/Locale.php", "downloaded_repos/craftcms_commerce/src/helpers/Localization.php", "downloaded_repos/craftcms_commerce/src/helpers/Order.php", "downloaded_repos/craftcms_commerce/src/helpers/PaymentForm.php", "downloaded_repos/craftcms_commerce/src/helpers/ProjectConfigData.php", "downloaded_repos/craftcms_commerce/src/helpers/Purchasable.php", "downloaded_repos/craftcms_commerce/src/icon-mask.svg", "downloaded_repos/craftcms_commerce/src/icon.svg", "downloaded_repos/craftcms_commerce/src/linktypes/Product.php", "downloaded_repos/craftcms_commerce/src/migrations/Install.php", "downloaded_repos/craftcms_commerce/src/migrations/m210614_073359_detailed_permission.php", "downloaded_repos/craftcms_commerce/src/migrations/m210831_080542_rename_variant_title_format_field.php", "downloaded_repos/craftcms_commerce/src/migrations/m210901_211323_not_null_booleans.php", "downloaded_repos/craftcms_commerce/src/migrations/m210922_133729_add_discount_order_condition_builder.php", "downloaded_repos/craftcms_commerce/src/migrations/m211118_101920_split_coupon_codes.php", "downloaded_repos/craftcms_commerce/src/migrations/m220301_022054_user_addresses.php", "downloaded_repos/craftcms_commerce/src/migrations/m220302_133730_add_discount_user_addresses_condition_builders.php", "downloaded_repos/craftcms_commerce/src/migrations/m220304_094835_discount_conditions.php", "downloaded_repos/craftcms_commerce/src/migrations/m220308_221717_orderhistory_name.php", "downloaded_repos/craftcms_commerce/src/migrations/m220329_075053_convert_gateway_frontend_enabled_column.php", "downloaded_repos/craftcms_commerce/src/migrations/m220706_132118_add_purchasable_tax_type.php", "downloaded_repos/craftcms_commerce/src/migrations/m220812_104819_add_primary_payment_source_column.php", "downloaded_repos/craftcms_commerce/src/migrations/m220817_135050_add_purchase_total_back_if_missing.php", "downloaded_repos/craftcms_commerce/src/migrations/m220912_111800_add_order_total_qty_column.php", "downloaded_repos/craftcms_commerce/src/migrations/m221025_083940_add_purchasables_stores_table.php", "downloaded_repos/craftcms_commerce/src/migrations/m221026_105212_add_catalog_pricing_table.php", "downloaded_repos/craftcms_commerce/src/migrations/m221027_070322_add_tax_shipping_category_soft_delete.php", "downloaded_repos/craftcms_commerce/src/migrations/m221027_074805_update_shipping_tax_category_indexes.php", "downloaded_repos/craftcms_commerce/src/migrations/m221028_192112_add_indexes_to_address_columns_on_orders.php", "downloaded_repos/craftcms_commerce/src/migrations/m221122_055724_move_general_settings_to_per_store_settings.php", "downloaded_repos/craftcms_commerce/src/migrations/m221122_055725_multi_store.php", "downloaded_repos/craftcms_commerce/src/migrations/m221122_155735_update_orders_shippingMethodHandle_default.php", "downloaded_repos/craftcms_commerce/src/migrations/m221124_114239_add_date_deleted_to_stores.php", "downloaded_repos/craftcms_commerce/src/migrations/m221206_094303_add_store_to_order.php", "downloaded_repos/craftcms_commerce/src/migrations/m221213_052623_drop_lite.php", "downloaded_repos/craftcms_commerce/src/migrations/m221213_070807_initial_storeId_records_transition.php", "downloaded_repos/craftcms_commerce/src/migrations/m230103_122549_add_product_type_max_variants.php", "downloaded_repos/craftcms_commerce/src/migrations/m230110_052712_site_stores.php", "downloaded_repos/craftcms_commerce/src/migrations/m230111_112916_update_lineitems_table.php", "downloaded_repos/craftcms_commerce/src/migrations/m230113_110914_remove_soft_delete.php", "downloaded_repos/craftcms_commerce/src/migrations/m230118_114424_add_purchasables_stores_indexes.php", "downloaded_repos/craftcms_commerce/src/migrations/m230126_105337_rename_discount_sales_references.php", "downloaded_repos/craftcms_commerce/src/migrations/m230126_114655_add_catalog_pricing_rule_metadata_column.php", "downloaded_repos/craftcms_commerce/src/migrations/m230208_130445_add_store_id_to_shipping_categories.php", "downloaded_repos/craftcms_commerce/src/migrations/m230210_093749_add_store_id_to_shipping_methods.php", "downloaded_repos/craftcms_commerce/src/migrations/m230210_141514_add_store_id_to_shipping_zones.php", "downloaded_repos/craftcms_commerce/src/migrations/m230214_094122_add_total_weight_column_to_orders.php", "downloaded_repos/craftcms_commerce/src/migrations/m230214_095055_update_name_index_on_shipping_zones.php", "downloaded_repos/craftcms_commerce/src/migrations/m230215_083820_add_order_condition_to_shipping_rules.php", "downloaded_repos/craftcms_commerce/src/migrations/m230215_114552_migrate_shipping_rule_conditions_to_condition_builder.php", "downloaded_repos/craftcms_commerce/src/migrations/m230217_095845_remove_shipping_rules_columns.php", "downloaded_repos/craftcms_commerce/src/migrations/m230217_143255_add_shipping_method_order_condition.php", "downloaded_repos/craftcms_commerce/src/migrations/m230220_075106_add_store_id_to_tax_rates.php", "downloaded_repos/craftcms_commerce/src/migrations/m230220_080107_add_store_id_to_tax_zones.php", "downloaded_repos/craftcms_commerce/src/migrations/m230307_091520_add_sort_order_to_stores.php", "downloaded_repos/craftcms_commerce/src/migrations/m230308_084340_add_store_id_to_order_statuses.php", "downloaded_repos/craftcms_commerce/src/migrations/m230310_102639_add_store_id_to_line_item_statuses.php", "downloaded_repos/craftcms_commerce/src/migrations/m230313_095359_add_store_id_to_emails.php", "downloaded_repos/craftcms_commerce/src/migrations/m230317_102521_add_store_id_to_pdfs.php", "downloaded_repos/craftcms_commerce/src/migrations/m230322_091615_move_email_settings_to_model.php", "downloaded_repos/craftcms_commerce/src/migrations/m230328_130343_move_pdf_settings_to_model.php", "downloaded_repos/craftcms_commerce/src/migrations/m230525_081243_add_has_update_pending_property.php", "downloaded_repos/craftcms_commerce/src/migrations/m230530_100604_add_complete_email_column.php", "downloaded_repos/craftcms_commerce/src/migrations/m230705_124845_add_save_address_columns.php", "downloaded_repos/craftcms_commerce/src/migrations/m230719_082348_discount_nullable_conditions.php", "downloaded_repos/craftcms_commerce/src/migrations/m230724_080855_entrify_promotions.php", "downloaded_repos/craftcms_commerce/src/migrations/m230920_051125_move_primary_currency_to_store_settings.php", "downloaded_repos/craftcms_commerce/src/migrations/m230928_095544_fix_unique_on_some_tables.php", "downloaded_repos/craftcms_commerce/src/migrations/m230928_155052_move_shipping_category_id_to_purchasable_stores.php", "downloaded_repos/craftcms_commerce/src/migrations/m231006_034833_add_indexes_for_source_address_on_order.php", "downloaded_repos/craftcms_commerce/src/migrations/m231019_110814_update_variant_ownership.php", "downloaded_repos/craftcms_commerce/src/migrations/m231110_081143_inventory_movement_table.php", "downloaded_repos/craftcms_commerce/src/migrations/m231201_100454_update_discount_base_discount_type.php", "downloaded_repos/craftcms_commerce/src/migrations/m240119_073924_content_refactor_elements.php", "downloaded_repos/craftcms_commerce/src/migrations/m240119_075036_content_refactor_subscription_elements.php", "downloaded_repos/craftcms_commerce/src/migrations/m240208_083054_add_purchasable_stores_purchasable_fk.php", "downloaded_repos/craftcms_commerce/src/migrations/m240219_194855_donation_multi_store.php", "downloaded_repos/craftcms_commerce/src/migrations/m240220_045806_product_versioning.php", "downloaded_repos/craftcms_commerce/src/migrations/m240220_105746_remove_store_from_donations_table.php", "downloaded_repos/craftcms_commerce/src/migrations/m240221_030027_transfer_items.php", "downloaded_repos/craftcms_commerce/src/migrations/m240223_101158_update_recent_orders_widget_settings.php", "downloaded_repos/craftcms_commerce/src/migrations/m240226_002943_remove_lite.php", "downloaded_repos/craftcms_commerce/src/migrations/m240228_054005_rename_movements_table.php", "downloaded_repos/craftcms_commerce/src/migrations/m240228_060604_add_fufilled_type_to_inventorytransactions.php", "downloaded_repos/craftcms_commerce/src/migrations/m240228_120911_drop_order_id_and_make_line_item_cascade.php", "downloaded_repos/craftcms_commerce/src/migrations/m240301_113924_add_line_item_types.php", "downloaded_repos/craftcms_commerce/src/migrations/m240306_091057_move_element_ids_on_discount_to_columns.php", "downloaded_repos/craftcms_commerce/src/migrations/m240308_133451_tidy_shipping_categories.php", "downloaded_repos/craftcms_commerce/src/migrations/m240313_131445_tidy_shipping_methods.php", "downloaded_repos/craftcms_commerce/src/migrations/m240315_072659_add_fk_cascade_fixes.php", "downloaded_repos/craftcms_commerce/src/migrations/m240430_161804_add_index_to_transaction_hash.php", "downloaded_repos/craftcms_commerce/src/migrations/m240507_081904_fix_store_pc_location.php", "downloaded_repos/craftcms_commerce/src/migrations/m240516_035616_update_permissions.php", "downloaded_repos/craftcms_commerce/src/migrations/m240516_035617_update_currency_and_store_general_permissions.php", "downloaded_repos/craftcms_commerce/src/migrations/m240528_124101_add_extra_lineitem_columns.php", "downloaded_repos/craftcms_commerce/src/migrations/m240529_095819_remove_commerce_user_field.php", "downloaded_repos/craftcms_commerce/src/migrations/m240605_110755_add_title_translations_product_types.php", "downloaded_repos/craftcms_commerce/src/migrations/m240619_082224_add_product_and_variant_conditions_to_catalog_pricing_rules.php", "downloaded_repos/craftcms_commerce/src/migrations/m240710_125204_ensure_shippingCategoryId_column_is_nullable.php", "downloaded_repos/craftcms_commerce/src/migrations/m240711_092240_fix_fks.php", "downloaded_repos/craftcms_commerce/src/migrations/m240715_045506_drop_available_if_exists.php", "downloaded_repos/craftcms_commerce/src/migrations/m240717_044256_add_return_url_to_subscription.php", "downloaded_repos/craftcms_commerce/src/migrations/m240718_073046_remove_sortOrder_variants_column_if_exists.php", "downloaded_repos/craftcms_commerce/src/migrations/m240808_090256_cascade_delete_variants_on_product_delete.php", "downloaded_repos/craftcms_commerce/src/migrations/m240808_093934_product_type_propagation.php", "downloaded_repos/craftcms_commerce/src/migrations/m240812_025615_add_transfer_details_table.php", "downloaded_repos/craftcms_commerce/src/migrations/m240815_035618_fix_transfer_permission.php", "downloaded_repos/craftcms_commerce/src/migrations/m240830_081410_add_extra_indexes_to_catalog_pricing.php", "downloaded_repos/craftcms_commerce/src/migrations/m240905_130549_add_require_coupon_code_discount_setting.php", "downloaded_repos/craftcms_commerce/src/migrations/m240906_105809_update_existing_coupon_discounts.php", "downloaded_repos/craftcms_commerce/src/migrations/m240906_115901_add_orderable_to_product_types.php", "downloaded_repos/craftcms_commerce/src/migrations/m240923_132625_remove_orphaned_variants_sites.php", "downloaded_repos/craftcms_commerce/src/migrations/m241010_061430_rename_orderable_product_type_type.php", "downloaded_repos/craftcms_commerce/src/migrations/m241017_072151_fix_temp_skus.php", "downloaded_repos/craftcms_commerce/src/migrations/m241022_075144_add_missing_variant_revision_records.php", "downloaded_repos/craftcms_commerce/src/migrations/m241128_174712_fix_maxLevels_structured_productTypes.php", "downloaded_repos/craftcms_commerce/src/migrations/m241204_045158_enable_tax_rate.php", "downloaded_repos/craftcms_commerce/src/migrations/m241204_091901_fix_store_environment_variables.php", "downloaded_repos/craftcms_commerce/src/migrations/m241213_083338_update_promotional_price_in_line_items.php", "downloaded_repos/craftcms_commerce/src/migrations/m241219_071723_add_inventory_backorder.php", "downloaded_repos/craftcms_commerce/src/migrations/m241220_082900_remove_inventory_for_non_inventory_purchasables.php", "downloaded_repos/craftcms_commerce/src/migrations/m250120_080035_move_to_tax_id_validators.php", "downloaded_repos/craftcms_commerce/src/migrations/m250128_083515_add_make_primary_addresses_to_orders.php", "downloaded_repos/craftcms_commerce/src/migrations/m250129_080909_fix_discount_conditions.php", "downloaded_repos/craftcms_commerce/src/migrations/m250210_125139_fix_cart_recalculation_modes.php", "downloaded_repos/craftcms_commerce/src/migrations/m250301_120000_add_gateway_order_condition.php", "downloaded_repos/craftcms_commerce/src/migrations/m250401_091214_add_shipping_method_customer_condition.php", "downloaded_repos/craftcms_commerce/src/migrations/m250403_134328_add_shipping_rule_customer_condition.php", "downloaded_repos/craftcms_commerce/src/migrations/m250616_042356_fix_field_layout_id.php", "downloaded_repos/craftcms_commerce/src/migrations/m250701_054128_add_defaultVariant_idex_to_products.php", "downloaded_repos/craftcms_commerce/src/migrations/m250721_130616_fix_gateway_order_condition_pc.php", "downloaded_repos/craftcms_commerce/src/models/CatalogPricing.php", "downloaded_repos/craftcms_commerce/src/models/CatalogPricingRule.php", "downloaded_repos/craftcms_commerce/src/models/Coupon.php", "downloaded_repos/craftcms_commerce/src/models/Discount.php", "downloaded_repos/craftcms_commerce/src/models/Email.php", "downloaded_repos/craftcms_commerce/src/models/InventoryFulfillmentLevel.php", "downloaded_repos/craftcms_commerce/src/models/InventoryItem.php", "downloaded_repos/craftcms_commerce/src/models/InventoryLevel.php", "downloaded_repos/craftcms_commerce/src/models/InventoryLocation.php", "downloaded_repos/craftcms_commerce/src/models/InventoryTransaction.php", "downloaded_repos/craftcms_commerce/src/models/LineItem.php", "downloaded_repos/craftcms_commerce/src/models/LineItemStatus.php", "downloaded_repos/craftcms_commerce/src/models/OrderAdjustment.php", "downloaded_repos/craftcms_commerce/src/models/OrderHistory.php", "downloaded_repos/craftcms_commerce/src/models/OrderNotice.php", "downloaded_repos/craftcms_commerce/src/models/OrderStatus.php", "downloaded_repos/craftcms_commerce/src/models/PaymentCurrency.php", "downloaded_repos/craftcms_commerce/src/models/PaymentSource.php", "downloaded_repos/craftcms_commerce/src/models/Pdf.php", "downloaded_repos/craftcms_commerce/src/models/ProductType.php", "downloaded_repos/craftcms_commerce/src/models/ProductTypeSite.php", "downloaded_repos/craftcms_commerce/src/models/PurchasableStore.php", "downloaded_repos/craftcms_commerce/src/models/Sale.php", "downloaded_repos/craftcms_commerce/src/models/Settings.php", "downloaded_repos/craftcms_commerce/src/models/ShippingAddressZone.php", "downloaded_repos/craftcms_commerce/src/models/ShippingCategory.php", "downloaded_repos/craftcms_commerce/src/models/ShippingMethod.php", "downloaded_repos/craftcms_commerce/src/models/ShippingMethodOption.php", "downloaded_repos/craftcms_commerce/src/models/ShippingRule.php", "downloaded_repos/craftcms_commerce/src/models/ShippingRuleCategory.php", "downloaded_repos/craftcms_commerce/src/models/SiteStore.php", "downloaded_repos/craftcms_commerce/src/models/Store.php", "downloaded_repos/craftcms_commerce/src/models/StoreSettings.php", "downloaded_repos/craftcms_commerce/src/models/TaxAddressZone.php", "downloaded_repos/craftcms_commerce/src/models/TaxCategory.php", "downloaded_repos/craftcms_commerce/src/models/TaxRate.php", "downloaded_repos/craftcms_commerce/src/models/Transaction.php", "downloaded_repos/craftcms_commerce/src/models/TransferDetail.php", "downloaded_repos/craftcms_commerce/src/models/inventory/DeactivateInventoryLocation.php", "downloaded_repos/craftcms_commerce/src/models/inventory/InventoryCommittedMovement.php", "downloaded_repos/craftcms_commerce/src/models/inventory/InventoryFulfillMovement.php", "downloaded_repos/craftcms_commerce/src/models/inventory/InventoryLocationDeactivatedMovement.php", "downloaded_repos/craftcms_commerce/src/models/inventory/InventoryManualMovement.php", "downloaded_repos/craftcms_commerce/src/models/inventory/InventoryRestockMovement.php", "downloaded_repos/craftcms_commerce/src/models/inventory/InventoryTransferMovement.php", "downloaded_repos/craftcms_commerce/src/models/inventory/UpdateInventoryLevel.php", "downloaded_repos/craftcms_commerce/src/models/inventory/UpdateInventoryLevelInTransfer.php", "downloaded_repos/craftcms_commerce/src/models/payments/BasePaymentForm.php", "downloaded_repos/craftcms_commerce/src/models/payments/CreditCardPaymentForm.php", "downloaded_repos/craftcms_commerce/src/models/payments/DummyPaymentForm.php", "downloaded_repos/craftcms_commerce/src/models/payments/OffsitePaymentForm.php", "downloaded_repos/craftcms_commerce/src/models/responses/Dummy.php", "downloaded_repos/craftcms_commerce/src/models/responses/DummySubscriptionResponse.php", "downloaded_repos/craftcms_commerce/src/models/responses/Manual.php", "downloaded_repos/craftcms_commerce/src/models/subscriptions/CancelSubscriptionForm.php", "downloaded_repos/craftcms_commerce/src/models/subscriptions/DummyPlan.php", "downloaded_repos/craftcms_commerce/src/models/subscriptions/SubscriptionForm.php", "downloaded_repos/craftcms_commerce/src/models/subscriptions/SubscriptionPayment.php", "downloaded_repos/craftcms_commerce/src/models/subscriptions/SwitchPlansForm.php", "downloaded_repos/craftcms_commerce/src/plugin/Routes.php", "downloaded_repos/craftcms_commerce/src/plugin/Services.php", "downloaded_repos/craftcms_commerce/src/plugin/Variables.php", "downloaded_repos/craftcms_commerce/src/queue/jobs/CatalogPricing.php", "downloaded_repos/craftcms_commerce/src/queue/jobs/SendEmail.php", "downloaded_repos/craftcms_commerce/src/records/CatalogPricing.php", "downloaded_repos/craftcms_commerce/src/records/CatalogPricingRule.php", "downloaded_repos/craftcms_commerce/src/records/CatalogPricingRuleUser.php", "downloaded_repos/craftcms_commerce/src/records/Coupon.php", "downloaded_repos/craftcms_commerce/src/records/Customer.php", "downloaded_repos/craftcms_commerce/src/records/CustomerDiscountUse.php", "downloaded_repos/craftcms_commerce/src/records/Discount.php", "downloaded_repos/craftcms_commerce/src/records/DiscountCategory.php", "downloaded_repos/craftcms_commerce/src/records/DiscountPurchasable.php", "downloaded_repos/craftcms_commerce/src/records/Donation.php", "downloaded_repos/craftcms_commerce/src/records/Email.php", "downloaded_repos/craftcms_commerce/src/records/EmailDiscountUse.php", "downloaded_repos/craftcms_commerce/src/records/Gateway.php", "downloaded_repos/craftcms_commerce/src/records/InventoryItem.php", "downloaded_repos/craftcms_commerce/src/records/InventoryLocation.php", "downloaded_repos/craftcms_commerce/src/records/LineItem.php", "downloaded_repos/craftcms_commerce/src/records/LineItemStatus.php", "downloaded_repos/craftcms_commerce/src/records/Order.php", "downloaded_repos/craftcms_commerce/src/records/OrderAdjustment.php", "downloaded_repos/craftcms_commerce/src/records/OrderHistory.php", "downloaded_repos/craftcms_commerce/src/records/OrderNotice.php", "downloaded_repos/craftcms_commerce/src/records/OrderStatus.php", "downloaded_repos/craftcms_commerce/src/records/OrderStatusEmail.php", "downloaded_repos/craftcms_commerce/src/records/PaymentCurrency.php", "downloaded_repos/craftcms_commerce/src/records/PaymentSource.php", "downloaded_repos/craftcms_commerce/src/records/Pdf.php", "downloaded_repos/craftcms_commerce/src/records/Plan.php", "downloaded_repos/craftcms_commerce/src/records/Product.php", "downloaded_repos/craftcms_commerce/src/records/ProductType.php", "downloaded_repos/craftcms_commerce/src/records/ProductTypeShippingCategory.php", "downloaded_repos/craftcms_commerce/src/records/ProductTypeSite.php", "downloaded_repos/craftcms_commerce/src/records/ProductTypeTaxCategory.php", "downloaded_repos/craftcms_commerce/src/records/Purchasable.php", "downloaded_repos/craftcms_commerce/src/records/PurchasableStore.php", "downloaded_repos/craftcms_commerce/src/records/Sale.php", "downloaded_repos/craftcms_commerce/src/records/SaleCategory.php", "downloaded_repos/craftcms_commerce/src/records/SalePurchasable.php", "downloaded_repos/craftcms_commerce/src/records/SaleUserGroup.php", "downloaded_repos/craftcms_commerce/src/records/ShippingCategory.php", "downloaded_repos/craftcms_commerce/src/records/ShippingMethod.php", "downloaded_repos/craftcms_commerce/src/records/ShippingRule.php", "downloaded_repos/craftcms_commerce/src/records/ShippingRuleCategory.php", "downloaded_repos/craftcms_commerce/src/records/ShippingZone.php", "downloaded_repos/craftcms_commerce/src/records/SiteStore.php", "downloaded_repos/craftcms_commerce/src/records/Store.php", "downloaded_repos/craftcms_commerce/src/records/StoreSettings.php", "downloaded_repos/craftcms_commerce/src/records/Subscription.php", "downloaded_repos/craftcms_commerce/src/records/TaxCategory.php", "downloaded_repos/craftcms_commerce/src/records/TaxRate.php", "downloaded_repos/craftcms_commerce/src/records/TaxZone.php", "downloaded_repos/craftcms_commerce/src/records/Transaction.php", "downloaded_repos/craftcms_commerce/src/records/Transfer.php", "downloaded_repos/craftcms_commerce/src/records/TransferDetail.php", "downloaded_repos/craftcms_commerce/src/records/Variant.php", "downloaded_repos/craftcms_commerce/src/services/Carts.php", "downloaded_repos/craftcms_commerce/src/services/CatalogPricing.php", "downloaded_repos/craftcms_commerce/src/services/CatalogPricingRules.php", "downloaded_repos/craftcms_commerce/src/services/Coupons.php", "downloaded_repos/craftcms_commerce/src/services/Currencies.php", "downloaded_repos/craftcms_commerce/src/services/Customers.php", "downloaded_repos/craftcms_commerce/src/services/Discounts.php", "downloaded_repos/craftcms_commerce/src/services/Emails.php", "downloaded_repos/craftcms_commerce/src/services/Formulas.php", "downloaded_repos/craftcms_commerce/src/services/Gateways.php", "downloaded_repos/craftcms_commerce/src/services/Inventory.php", "downloaded_repos/craftcms_commerce/src/services/InventoryLocations.php", "downloaded_repos/craftcms_commerce/src/services/LineItemStatuses.php", "downloaded_repos/craftcms_commerce/src/services/LineItems.php", "downloaded_repos/craftcms_commerce/src/services/OrderAdjustments.php", "downloaded_repos/craftcms_commerce/src/services/OrderHistories.php", "downloaded_repos/craftcms_commerce/src/services/OrderNotices.php", "downloaded_repos/craftcms_commerce/src/services/OrderStatuses.php", "downloaded_repos/craftcms_commerce/src/services/Orders.php", "downloaded_repos/craftcms_commerce/src/services/PaymentCurrencies.php", "downloaded_repos/craftcms_commerce/src/services/PaymentSources.php", "downloaded_repos/craftcms_commerce/src/services/Payments.php", "downloaded_repos/craftcms_commerce/src/services/Pdfs.php", "downloaded_repos/craftcms_commerce/src/services/Plans.php", "downloaded_repos/craftcms_commerce/src/services/ProductTypes.php", "downloaded_repos/craftcms_commerce/src/services/Products.php", "downloaded_repos/craftcms_commerce/src/services/Purchasables.php", "downloaded_repos/craftcms_commerce/src/services/Sales.php", "downloaded_repos/craftcms_commerce/src/services/ShippingCategories.php", "downloaded_repos/craftcms_commerce/src/services/ShippingMethods.php", "downloaded_repos/craftcms_commerce/src/services/ShippingRuleCategories.php", "downloaded_repos/craftcms_commerce/src/services/ShippingRules.php", "downloaded_repos/craftcms_commerce/src/services/ShippingZones.php", "downloaded_repos/craftcms_commerce/src/services/Store.php", "downloaded_repos/craftcms_commerce/src/services/StoreSettings.php", "downloaded_repos/craftcms_commerce/src/services/Stores.php", "downloaded_repos/craftcms_commerce/src/services/Subscriptions.php", "downloaded_repos/craftcms_commerce/src/services/TaxCategories.php", "downloaded_repos/craftcms_commerce/src/services/TaxRates.php", "downloaded_repos/craftcms_commerce/src/services/TaxZones.php", "downloaded_repos/craftcms_commerce/src/services/Taxes.php", "downloaded_repos/craftcms_commerce/src/services/Transactions.php", "downloaded_repos/craftcms_commerce/src/services/Transfers.php", "downloaded_repos/craftcms_commerce/src/services/Variants.php", "downloaded_repos/craftcms_commerce/src/services/Vat.php", "downloaded_repos/craftcms_commerce/src/services/Webhooks.php", "downloaded_repos/craftcms_commerce/src/stats/AverageOrderTotal.php", "downloaded_repos/craftcms_commerce/src/stats/NewCustomers.php", "downloaded_repos/craftcms_commerce/src/stats/RepeatCustomers.php", "downloaded_repos/craftcms_commerce/src/stats/TopCustomers.php", "downloaded_repos/craftcms_commerce/src/stats/TopProductTypes.php", "downloaded_repos/craftcms_commerce/src/stats/TopProducts.php", "downloaded_repos/craftcms_commerce/src/stats/TopPurchasables.php", "downloaded_repos/craftcms_commerce/src/stats/TotalOrders.php", "downloaded_repos/craftcms_commerce/src/stats/TotalOrdersByCountry.php", "downloaded_repos/craftcms_commerce/src/stats/TotalRevenue.php", "downloaded_repos/craftcms_commerce/src/taxidvalidators/EuVatIdValidator.php", "downloaded_repos/craftcms_commerce/src/templates/_components/elementactions/DownloadOrderPdf/trigger.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/gateways/_creditCardFields.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/gateways/_modalWrapper.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/_includes/_dateRangeField.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/_includes/_orderStatusesField.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/_includes/_storeField.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/customers/new/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/customers/new/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/customers/repeat/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/customers/repeat/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/customers/top/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/customers/top/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/average/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/average/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/country/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/country/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/recent/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/recent/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/revenue/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/revenue/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/total/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/orders/total/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/products/top/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/products/top/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/producttypes/top/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/producttypes/top/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/purchasables/top/body.twig", "downloaded_repos/craftcms_commerce/src/templates/_components/widgets/purchasables/top/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_data/data.tsv", "downloaded_repos/craftcms_commerce/src/templates/_data/tsv.tsv", "downloaded_repos/craftcms_commerce/src/templates/_includes/_storeManagementNav.twig", "downloaded_repos/craftcms_commerce/src/templates/_includes/forms/commerceForms.twig", "downloaded_repos/craftcms_commerce/src/templates/_includes/forms/inventoryLocationSelect.twig", "downloaded_repos/craftcms_commerce/src/templates/_includes/orders/address.twig", "downloaded_repos/craftcms_commerce/src/templates/_includes/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_layouts/cp.twig", "downloaded_repos/craftcms_commerce/src/templates/_layouts/settings.twig", "downloaded_repos/craftcms_commerce/src/templates/_layouts/store-management.twig", "downloaded_repos/craftcms_commerce/src/templates/_layouts/tax.twig", "downloaded_repos/craftcms_commerce/src/templates/donation/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/gateways/manualGatewaySettings.twig", "downloaded_repos/craftcms_commerce/src/templates/index.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory/item/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory/levels/_index.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory/levels/_inventoryMovementModal.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory/levels/_unfulfilledOrdersModal.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory/levels/_updateInventoryLevelModal.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory/transfers/_index.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory-locations/_deleteModal.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory-locations/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory-locations/_index.twig", "downloaded_repos/craftcms_commerce/src/templates/inventory-locations/_sidebar.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/_history.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/_index.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/_paymentForms.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/_paymentmodal.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/_transactions.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/includes/_capture.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/includes/_refund.twig", "downloaded_repos/craftcms_commerce/src/templates/orders/modals/_fulfillmentModal.twig", "downloaded_repos/craftcms_commerce/src/templates/prices/_index.twig", "downloaded_repos/craftcms_commerce/src/templates/prices/_polling.twig", "downloaded_repos/craftcms_commerce/src/templates/prices/_status.twig", "downloaded_repos/craftcms_commerce/src/templates/prices/_table.twig", "downloaded_repos/craftcms_commerce/src/templates/products/_index.twig", "downloaded_repos/craftcms_commerce/src/templates/promotions/index.twig", "downloaded_repos/craftcms_commerce/src/templates/promotions/sales/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/promotions/sales/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/emails/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/emails/_previewError.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/emails/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/gateways/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/gateways/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/general/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/lineitemstatuses/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/lineitemstatuses/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/ordersettings/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/orderstatuses/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/orderstatuses/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/pdfs/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/pdfs/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/producttypes/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/producttypes/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/stores/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/stores/_siteStore.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/stores/index.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/subscriptions/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/settings/transfers/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/discounts/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/discounts/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/general/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/paymentcurrencies/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/paymentcurrencies/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/pricing-rules/_actions-fields.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/pricing-rules/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/pricing-rules/_sidebar.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/pricing-rules/_slideout.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/pricing-rules/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingcategories/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingcategories/_fields.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingcategories/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingmethods/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingmethods/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingrules/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingzones/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingzones/_fields.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/shipping/shippingzones/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxcategories/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxcategories/_fields.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxcategories/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxrates/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxrates/_fields.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxrates/index.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxzones/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxzones/_fields.twig", "downloaded_repos/craftcms_commerce/src/templates/store-management/tax/taxzones/index.twig", "downloaded_repos/craftcms_commerce/src/templates/subscriptions/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/subscriptions/_index.twig", "downloaded_repos/craftcms_commerce/src/templates/subscriptions/plans/_edit.twig", "downloaded_repos/craftcms_commerce/src/templates/subscriptions/plans/index.twig", "downloaded_repos/craftcms_commerce/src/templates/variants/_index.twig", "downloaded_repos/craftcms_commerce/src/translations/de/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/en/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/en-GB/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/fr/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/fr-CA/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/it/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/ja/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/nb/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/nl/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/pt/commerce.php", "downloaded_repos/craftcms_commerce/src/translations/sk/commerce.php", "downloaded_repos/craftcms_commerce/src/validators/CouponsValidator.php", "downloaded_repos/craftcms_commerce/src/views/debug/commerce/detail.php", "downloaded_repos/craftcms_commerce/src/views/debug/commerce/model.php", "downloaded_repos/craftcms_commerce/src/views/debug/commerce/summary.php", "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/CatalogPricingAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/src/css/catalogpricing.scss", "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/src/js/CatalogPricing.js", "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/chartjs/ChartJsAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/chartjs/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/CommerceCpAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/commercecp.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/images/error.png", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/images/promotional_price.png", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/images/spinner_big.gif", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/Commerce.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/CommerceOrderEdit.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/CommerceOrderIndex.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/CommercePaymentModal.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/CommerceProductSalesModal.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/CommerceSubscriptionIndex.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/CommerceUpdateOrderStatusModal.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/DownloadOrderPdf.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/TableRowAdditionalInfoIcon.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/addresses.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/commerce.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/order.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/prices.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/purchasables.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/registration.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/stores.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/scss/subscriptions.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/.env.example", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/.gitignore", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/CommerceOrderAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/CommerceUiAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/base/components/BtnLink.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/base/components/Field.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/base/components/Lightswitch.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/base/components/Modal.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/base/components/SelectInput.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/base/filters/craft.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/api/addresses.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/api/orders.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/app.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderActions.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderCustomer.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderDetails.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderErrors.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderMeta.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderNotices.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/apps/OrderSecondaryActions.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/InputError.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/OrderBlock.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/OrderTitle.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/actions/OptionShortcutLabel.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/actions/UpdateOrderBtn.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/customer/AddressEdit.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/customer/AddressSelect.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/customer/Customer.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/AddLineItem.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/Adjustment.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/Adjustments.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItem.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItemAdjustments.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItemNotes.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItemOptions.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItemProperty.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItemStatus.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItemStatusInput.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/LineItems.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/OrderAdjustments.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/QtyInput.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/Snapshot.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/details/Total.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/meta/CustomerSelect.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/meta/DateOrderedInput.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/meta/OpenIndicator.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/meta/OrderSite.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/meta/OrderStatus.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/components/meta/ShippingMethod.vue", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/helpers/utils.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/mixins/index.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/store/index.js", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/sass/base/_common.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/sass/base/_craft-classes.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/sass/base/_vue-select.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/sass/order/_modal.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/sass/order/app.scss", "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercewidgets/CommerceWidgetsAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/commercewidgets/src/CommerceWidgets.js", "downloaded_repos/craftcms_commerce/src/web/assets/commercewidgets/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/coupons/CouponsAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/coupons/src/css/coupons.scss", "downloaded_repos/craftcms_commerce/src/web/assets/coupons/src/js/coupons.js", "downloaded_repos/craftcms_commerce/src/web/assets/coupons/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/deepmerge/DeepMergeAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/deepmerge/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/EditProductAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/src/CommerceProductEdit.js", "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/src/product.css", "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/inventory/InventoryAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/inventory/src/css/inventory.scss", "downloaded_repos/craftcms_commerce/src/web/assets/inventory/src/inventory.js", "downloaded_repos/craftcms_commerce/src/web/assets/inventory/src/js/InventoryLevelsManager.js", "downloaded_repos/craftcms_commerce/src/web/assets/inventory/src/js/InventoryMovementModal.js", "downloaded_repos/craftcms_commerce/src/web/assets/inventory/src/js/UpdateInventoryLevelModal.js", "downloaded_repos/craftcms_commerce/src/web/assets/inventory/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/orderswidget/OrdersWidgetAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/orderswidget/src/OrdersWidgetSettings.js", "downloaded_repos/craftcms_commerce/src/web/assets/orderswidget/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/productindex/ProductIndexAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/productindex/src/CommerceProductIndex.js", "downloaded_repos/craftcms_commerce/src/web/assets/productindex/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/purchasablepricefield/PurchasablePriceFieldAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/purchasablepricefield/src/js/PurchasablePriceField.js", "downloaded_repos/craftcms_commerce/src/web/assets/purchasablepricefield/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/StatWidgetsAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/src/CommerceChart.js", "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/src/scss/stat-widgets.scss", "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/transfers/TransfersAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/transfers/src/css/transfers.scss", "downloaded_repos/craftcms_commerce/src/web/assets/transfers/src/js/ReceiveTransferScreen.js", "downloaded_repos/craftcms_commerce/src/web/assets/transfers/src/js/TransferEdit.js", "downloaded_repos/craftcms_commerce/src/web/assets/transfers/src/transfers.js", "downloaded_repos/craftcms_commerce/src/web/assets/transfers/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/VariantMatrixAsset.php", "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/webpack.config.js", "downloaded_repos/craftcms_commerce/src/web/twig/CraftVariableBehavior.php", "downloaded_repos/craftcms_commerce/src/web/twig/Extension.php", "downloaded_repos/craftcms_commerce/src/widgets/AverageOrderTotal.php", "downloaded_repos/craftcms_commerce/src/widgets/NewCustomers.php", "downloaded_repos/craftcms_commerce/src/widgets/Orders.php", "downloaded_repos/craftcms_commerce/src/widgets/RepeatCustomers.php", "downloaded_repos/craftcms_commerce/src/widgets/TopCustomers.php", "downloaded_repos/craftcms_commerce/src/widgets/TopProductTypes.php", "downloaded_repos/craftcms_commerce/src/widgets/TopProducts.php", "downloaded_repos/craftcms_commerce/src/widgets/TopPurchasables.php", "downloaded_repos/craftcms_commerce/src/widgets/TotalOrders.php", "downloaded_repos/craftcms_commerce/src/widgets/TotalOrdersByCountry.php", "downloaded_repos/craftcms_commerce/src/widgets/TotalRevenue.php", "downloaded_repos/craftcms_commerce/webpack.config.js"], "skipped": [{"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/address/fields.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/address/fieldset.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/address/list.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/images/placeholder.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/images/spinner.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/layouts/includes/footer.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/layouts/includes/header.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/layouts/includes/nav-checkout.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/layouts/includes/nav-customer.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/layouts/includes/nav-main.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/layouts/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/payments/payment-post-redirect.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/_private/receipt/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/cart/_includes/shipping-estimator.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/cart/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/cart/load.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/_includes/currencies.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/_includes/gateways.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/_includes/order-summary.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/_includes/partial-payment.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/_paymentLegacyStripeExample.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/addresses.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/complete.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/email.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/options.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/pay-static.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/payment-method.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/payment.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/register-signin.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/checkout/shipping.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/_includes/register.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/_includes/sign-in.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/addresses/edit.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/addresses/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/cards.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/forgot-password.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/order.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/orders.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/customer/sign-in.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/donations/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/plans/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/plans/subscription/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/plans/update-billing-details.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/products/_includes/grid.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/products/_includes/pagination.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/products/_product.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/example-templates/dist/shop/products/index.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/models/Settings.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/craftcms_commerce/src/test/fixtures/elements/ProductFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/test/mockclasses/Purchasable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/dist/CatalogPricing.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/dist/CatalogPricing.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/dist/css/CatalogPricing.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/dist/css/CatalogPricing.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/chartjs/dist/Chart.bundle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/chartjs/dist/Chart.bundle.min.js.LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/chartjs/dist/chartjs-adapter-moment.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/chartjs/dist/chartjs-adapter-moment.min.js.LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/chartjs/dist/moment-with-locales.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/dist/commercecp.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/dist/commercecp.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/dist/css/commercecp.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/dist/css/commercecp.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/dist/images/error.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/dist/images/promotional_price.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/dist/images/spinner_big.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/dist/css/order.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/dist/css/order.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/dist/js/app.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/dist/js/app.js.LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/dist/js/app.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/dist/manifest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercewidgets/dist/CommerceWidgets.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/commercewidgets/dist/CommerceWidgets.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/coupons/dist/coupons.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/coupons/dist/coupons.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/coupons/dist/css/coupons.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/coupons/dist/css/coupons.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/deepmerge/dist/umd.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/dist/CommerceProductEdit.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/dist/CommerceProductEdit.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/dist/css/CommerceProductEdit.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/editproduct/dist/css/CommerceProductEdit.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/inventory/dist/css/inventory.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/inventory/dist/css/inventory.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/inventory/dist/inventory.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/inventory/dist/inventory.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/orderswidget/dist/OrdersWidgetSettings.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/orderswidget/dist/OrdersWidgetSettings.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/productindex/dist/CommerceProductIndex.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/productindex/dist/CommerceProductIndex.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/purchasablepricefield/dist/purchasablepricefield.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/purchasablepricefield/dist/purchasablepricefield.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/dist/CommerceChart.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/dist/CommerceChart.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/dist/css/CommerceChart.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/statwidgets/dist/css/CommerceChart.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/transfers/dist/css/transfers.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/transfers/dist/transfers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/transfers/dist/transfers.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/dist/VariantMatrix.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/dist/VariantMatrix.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/.env.example.mysql", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/.env.example.pgsql", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_craft/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_craft/config/db.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_craft/config/test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_craft/storage/config-backups/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_craft/storage/runtime/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_craft/templates/emails/order-confirmation.twig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_data/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_envs/installed.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_output/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/AcceptanceTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/FunctionalTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/GqlTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/Helper/Acceptance.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/Helper/Functional.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/Helper/Gql.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/Helper/Unit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/UnitTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/_support/_generated/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/acceptance/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/acceptance.suite.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/BaseModelFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/CategoriesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/CustomerAddressFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/CustomerFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/DiscountsFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/EmailsFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/FieldLayoutFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/GqlSchemasFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/OrderStatusesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/OrdersFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/PaymentCurrenciesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ProductFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ProductTypeFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ProductTypeSitesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ProductTypesShippingCategoriesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ProductTypesTaxCategoriesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/SalesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ShippingCategoryFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ShippingFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ShippingMethodsFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/ShippingZonesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/SitesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/StoreFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/SubscriptionPlansFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/SubscriptionsFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/TaxCategoryFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/UserGroupsFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/categories.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/customer-addresses.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/customers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/discounts.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/emails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/field-layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/gql-schemas.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/inventory-items.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/order-statuses.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/orders.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/payment-currencies.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/product-types-shipping-categories.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/product-types-sites.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/product-types-tax-categories.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/product-types.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/products.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/sales.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/shipping-category.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/shipping-methods.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/shipping-rules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/shipping-zones.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/sites.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/stores.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/subscription-plans.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/subscriptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/tax-category.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/user-addresses.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/fixtures/data/user-groups.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/functional/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/functional.suite.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/gql/GqlCest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/gql/_bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/gql/data/gql.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/gql.suite.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/adjusters/DiscountTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/adjusters/TaxTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/controllers/CartTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/controllers/EmailPreviewControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/controllers/OrdersControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/controllers/ShippingRulesControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/address/CustomerAddressBehaviorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/donation/DonationQueryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderAddressesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderCustomerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderMarkAsCompleteTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderNoticesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderPaymentAmountTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderQueryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderTotalsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/OrderValidationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/conditions/CouponCodeConditionRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/conditions/CustomerConditionRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/order/conditions/OrderConditionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/ProductPricingCatalogTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/ProductQueryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/ProductTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/conditions/ProductConditionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/conditions/ProductTypeConditionRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/conditions/ProductVariantHasUnlimitedStockConditionRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/conditions/ProductVariantPriceConditionRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/conditions/ProductVariantSkuConditionRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/product/conditions/ProductVariantStockConditionRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/subscriptions/SubscriptionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/user/CustomerBehaviorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/user/UserEmailTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/variant/PricingCatalogTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/variant/PricingSalesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/variant/VariantCollectionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/elements/variant/VariantQueryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/gql/ProductResolverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/helpers/CurrencyHelperTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/helpers/DebugPanelHelperTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/helpers/LocaleHelperTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/helpers/LocalizationHelperTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/models/DiscountTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/models/LineItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/models/SaleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/models/StoreTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/models/TaxRateTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/CartsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/CatalogPricingTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/CouponsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/CustomersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/DiscountsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/GatewaysTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/InventoryMovementTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/InventoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/LineItemsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/OrdersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/PaymentCurrenciesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/PlansTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/ProductPermissionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/SalesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/ShippingCategoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/ShippingMethodsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/StoreTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/StoresTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/TaxCategoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/services/UserGroupConditionDiscountTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/AverageOrderTotalTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/NewCustomersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/RepeatCustomersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/StatTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/TopCustomersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/TopProductTypesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/TopProductsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/TopPurchasablesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/TotalOrdersByCountryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/TotalOrdersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit/stats/TotalRevenueTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/craftcms_commerce/tests/unit.suite.yml", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9453520774841309, "profiling_times": {"config_time": 6.21622896194458, "core_time": 9.00078797340393, "ignores_time": 0.0017898082733154297, "total_time": 15.219657182693481}, "parsing_time": {"total_time": 20.509749174118042, "per_file_time": {"mean": 0.028525381327006983, "std_dev": 0.0075848966914522226}, "very_slow_stats": {"time_ratio": 0.3342092775534602, "count_ratio": 0.01808066759388039}, "very_slow_files": [{"fpath": "downloaded_repos/craftcms_commerce/src/services/Discounts.php", "ftime": 0.34958600997924805}, {"fpath": "downloaded_repos/craftcms_commerce/src/services/Emails.php", "ftime": 0.39030003547668457}, {"fpath": "downloaded_repos/craftcms_commerce/src/elements/traits/OrderElementTrait.php", "ftime": 0.40313100814819336}, {"fpath": "downloaded_repos/craftcms_commerce/src/Plugin.php", "ftime": 0.41471004486083984}, {"fpath": "downloaded_repos/craftcms_commerce/src/elements/Variant.php", "ftime": 0.454146146774292}, {"fpath": "downloaded_repos/craftcms_commerce/src/base/Purchasable.php", "ftime": 0.46270298957824707}, {"fpath": "downloaded_repos/craftcms_commerce/src/elements/Product.php", "ftime": 0.5623009204864502}, {"fpath": "downloaded_repos/craftcms_commerce/src/controllers/OrdersController.php", "ftime": 0.7315430641174316}, {"fpath": "downloaded_repos/craftcms_commerce/src/elements/Order.php", "ftime": 0.8704450130462646}, {"fpath": "downloaded_repos/craftcms_commerce/package-lock.json", "ftime": 1.2405729293823242}]}, "scanning_time": {"total_time": 53.058889389038086, "per_file_time": {"mean": 0.01990952697524885, "std_dev": 0.011659945611512063}, "very_slow_stats": {"time_ratio": 0.20344160153919713, "count_ratio": 0.00225140712945591}, "very_slow_files": [{"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/store/index.js", "ftime": 1.506748914718628}, {"fpath": "downloaded_repos/craftcms_commerce/src/elements/Order.php", "ftime": 1.5547330379486084}, {"fpath": "downloaded_repos/craftcms_commerce/composer.lock", "ftime": 1.6536571979522705}, {"fpath": "downloaded_repos/craftcms_commerce/package-lock.json", "ftime": 1.9022810459136963}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "ftime": 2.04008412361145}, {"fpath": "downloaded_repos/craftcms_commerce/package-lock.json", "ftime": 2.136881113052368}]}, "matching_time": {"total_time": 9.985127925872803, "per_file_and_rule_time": {"mean": 0.007604819440877984, "std_dev": 0.001098373007264833}, "very_slow_stats": {"time_ratio": 0.4986773370487792, "count_ratio": 0.01827875095201828}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/productindex/src/CommerceProductIndex.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.20858001708984375}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/store/index.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2152698040008545}, {"fpath": "downloaded_repos/craftcms_commerce/src/controllers/OrdersController.php", "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.21918201446533203}, {"fpath": "downloaded_repos/craftcms_commerce/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.25513792037963867}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.27046799659729004}, {"fpath": "downloaded_repos/craftcms_commerce/src/migrations/Install.php", "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.2760891914367676}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/store/index.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.2791571617126465}, {"fpath": "downloaded_repos/craftcms_commerce/src/elements/Order.php", "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.28807687759399414}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/commercecp/src/js/TableRowAdditionalInfoIcon.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.4193229675292969}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6076929569244385}]}, "tainting_time": {"total_time": 2.570003032684326, "per_def_and_rule_time": {"mean": 0.0024154163841018108, "std_dev": 6.652107007598081e-05}, "very_slow_stats": {"time_ratio": 0.2400348109268683, "count_ratio": 0.007518796992481203}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/catalogpricing/src/js/CatalogPricing.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.05525994300842285}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.060810089111328125}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.06580901145935059}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.06734991073608398}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/commerceui/src/js/order/store/index.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.08143115043640137}, {"fpath": "downloaded_repos/craftcms_commerce/src/migrations/Install.php", "fline": 83, "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.0822439193725586}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.0846099853515625}, {"fpath": "downloaded_repos/craftcms_commerce/src/web/assets/variantmatrix/src/VariantMatrix.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.11937618255615234}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1120288640}, "engine_requested": "OSS", "skipped_rules": []}