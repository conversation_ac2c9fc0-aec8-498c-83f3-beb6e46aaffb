{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/saade_filament-laravel-log/bin/build.js", "start": {"line": 37, "col": 33, "offset": 1003}, "end": {"line": 37, "col": 126, "offset": 1096}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 38, "offset": 973}, "end": {"line": 39, "col": 57, "offset": 992}}, {"path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 40, "col": 24, "offset": 973}, "end": {"line": 40, "col": 43, "offset": 992}}]], "message": "Syntax error at line downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml:39:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 38, "offset": 973}, "end": {"line": 39, "col": 57, "offset": 992}}, {"file": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 40, "col": 24, "offset": 973}, "end": {"line": 40, "col": 43, "offset": 992}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 53, "offset": 1160}, "end": {"line": 44, "col": 69, "offset": 1176}}, {"path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 97, "offset": 1160}, "end": {"line": 44, "col": 115, "offset": 1178}}, {"path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 45, "col": 19, "offset": 1160}, "end": {"line": 45, "col": 22, "offset": 1163}}]], "message": "Syntax error at line downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml:44:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 53, "offset": 1160}, "end": {"line": 44, "col": 69, "offset": 1176}}, {"file": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 97, "offset": 1160}, "end": {"line": 44, "col": 115, "offset": 1178}}, {"file": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "start": {"line": 45, "col": 19, "offset": 1160}, "end": {"line": 45, "col": 22, "offset": 1163}}]}], "paths": {"scanned": ["downloaded_repos/saade_filament-laravel-log/.editorconfig", "downloaded_repos/saade_filament-laravel-log/.gitattributes", "downloaded_repos/saade_filament-laravel-log/.github/CONTRIBUTING.md", "downloaded_repos/saade_filament-laravel-log/.github/FUNDING.yml", "downloaded_repos/saade_filament-laravel-log/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/saade_filament-laravel-log/.github/SECURITY.md", "downloaded_repos/saade_filament-laravel-log/.github/dependabot.yml", "downloaded_repos/saade_filament-laravel-log/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/saade_filament-laravel-log/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/saade_filament-laravel-log/.github/workflows/phpstan.yml", "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "downloaded_repos/saade_filament-laravel-log/.github/workflows/update-changelog.yml", "downloaded_repos/saade_filament-laravel-log/.gitignore", "downloaded_repos/saade_filament-laravel-log/CHANGELOG.md", "downloaded_repos/saade_filament-laravel-log/LICENSE.md", "downloaded_repos/saade_filament-laravel-log/README.md", "downloaded_repos/saade_filament-laravel-log/art/cover1.png", "downloaded_repos/saade_filament-laravel-log/art/sponsor.png", "downloaded_repos/saade_filament-laravel-log/bin/build.js", "downloaded_repos/saade_filament-laravel-log/composer.json", "downloaded_repos/saade_filament-laravel-log/config/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/package-lock.json", "downloaded_repos/saade_filament-laravel-log/package.json", "downloaded_repos/saade_filament-laravel-log/phpstan-baseline.neon", "downloaded_repos/saade_filament-laravel-log/phpstan.neon.dist", "downloaded_repos/saade_filament-laravel-log/pint.json", "downloaded_repos/saade_filament-laravel-log/postcss.config.cjs", "downloaded_repos/saade_filament-laravel-log/resources/css/index.css", "downloaded_repos/saade_filament-laravel-log/resources/js/index.js", "downloaded_repos/saade_filament-laravel-log/resources/lang/ar/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/resources/lang/en/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/resources/lang/es/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/resources/lang/fa/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/resources/lang/hu/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/resources/lang/it/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/resources/lang/nl/filament-laravel-log.php", "downloaded_repos/saade_filament-laravel-log/resources/views/.gitkeep", "downloaded_repos/saade_filament-laravel-log/resources/views/view-log.blade.php", "downloaded_repos/saade_filament-laravel-log/src/Commands/UpgradeFilamentLaravelLogCommand.php", "downloaded_repos/saade_filament-laravel-log/src/FilamentLaravelLogPlugin.php", "downloaded_repos/saade_filament-laravel-log/src/FilamentLaravelLogServiceProvider.php", "downloaded_repos/saade_filament-laravel-log/src/Pages/Actions/ClearAction.php", "downloaded_repos/saade_filament-laravel-log/src/Pages/Actions/JumpToEndAction.php", "downloaded_repos/saade_filament-laravel-log/src/Pages/Actions/JumpToStartAction.php", "downloaded_repos/saade_filament-laravel-log/src/Pages/Actions/RefreshAction.php", "downloaded_repos/saade_filament-laravel-log/src/Pages/Concerns/HasActions.php", "downloaded_repos/saade_filament-laravel-log/src/Pages/ViewLog.php", "downloaded_repos/saade_filament-laravel-log/tailwind.config.js"], "skipped": [{"path": "downloaded_repos/saade_filament-laravel-log/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/saade_filament-laravel-log/dist/css/filament-laravel-log.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/saade_filament-laravel-log/resources/dist/filament-laravel-log.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/saade_filament-laravel-log/resources/dist/filament-laravel-log.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6511399745941162, "profiling_times": {"config_time": 6.5968406200408936, "core_time": 2.4345436096191406, "ignores_time": 0.0019121170043945312, "total_time": 9.034420728683472}, "parsing_time": {"total_time": 0.4599747657775879, "per_file_time": {"mean": 0.013528669581693764, "std_dev": 0.0003365159706293283}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.1804859638214111, "per_file_time": {"mean": 0.009080661260164704, "std_dev": 0.00035561653358759333}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.23212933540344238, "per_file_and_rule_time": {"mean": 0.000841048316679139, "std_dev": 5.411657458415368e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0015766620635986328, "per_def_and_rule_time": {"mean": 0.0007883310317993164, "std_dev": 4.090342287099702e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1085456896}, "engine_requested": "OSS", "skipped_rules": []}