{"version": "1.130.0", "results": [{"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/src/app.js", "start": {"line": 34, "col": 7, "offset": 1030}, "end": {"line": 34, "col": 22, "offset": 1045}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/modules/dynamodb.tf", "start": {"line": 9, "col": 1, "offset": 232}, "end": {"line": 52, "col": 2, "offset": 1388}, "extra": {"message": "By default, AWS DynamoDB Table is encrypted using AWS-managed keys. However, for added security, it's recommended to configure your own AWS KMS encryption key to protect your data in the DynamoDB table. You can either create a new aws_kms_key resource or use the ARN of an existing key in your AWS account to do so.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "shortlink": "https://sg.run/Ay4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/modules/dynamodb.tf", "start": {"line": 54, "col": 1, "offset": 1390}, "end": {"line": 142, "col": 2, "offset": 3692}, "extra": {"message": "By default, AWS DynamoDB Table is encrypted using AWS-managed keys. However, for added security, it's recommended to configure your own AWS KMS encryption key to protect your data in the DynamoDB table. You can either create a new aws_kms_key resource or use the ARN of an existing key in your AWS account to do so.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "shortlink": "https://sg.run/Ay4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/modules/dynamodb.tf", "start": {"line": 144, "col": 1, "offset": 3694}, "end": {"line": 177, "col": 2, "offset": 4527}, "extra": {"message": "By default, AWS DynamoDB Table is encrypted using AWS-managed keys. However, for added security, it's recommended to configure your own AWS KMS encryption key to protect your data in the DynamoDB table. You can either create a new aws_kms_key resource or use the ARN of an existing key in your AWS account to do so.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "shortlink": "https://sg.run/Ay4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/DetailEvent.test.jsx", "start": {"line": 39, "col": 9, "offset": 1551}, "end": {"line": 39, "col": 34, "offset": 1576}, "extra": {"message": "AWS Account ID detected. While not considered sensitive information, it is important to use them and share them carefully. For that reason it would be preferrable avoiding to hardcoded it here. Instead, read the value from an environment variable or keep the value in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "aws"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "shortlink": "https://sg.run/Ro22"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverViewLeases.test.jsx", "start": {"line": 25, "col": 9, "offset": 1138}, "end": {"line": 25, "col": 34, "offset": 1163}, "extra": {"message": "AWS Account ID detected. While not considered sensitive information, it is important to use them and share them carefully. For that reason it would be preferrable avoiding to hardcoded it here. Instead, read the value from an environment variable or keep the value in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "aws"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "shortlink": "https://sg.run/Ro22"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewAccounts.test.jsx", "start": {"line": 34, "col": 9, "offset": 1554}, "end": {"line": 34, "col": 34, "offset": 1579}, "extra": {"message": "AWS Account ID detected. While not considered sensitive information, it is important to use them and share them carefully. For that reason it would be preferrable avoiding to hardcoded it here. Instead, read the value from an environment variable or keep the value in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "aws"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "shortlink": "https://sg.run/Ro22"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewEvents.test.jsx", "start": {"line": 39, "col": 9, "offset": 1560}, "end": {"line": 39, "col": 34, "offset": 1585}, "extra": {"message": "AWS Account ID detected. While not considered sensitive information, it is important to use them and share them carefully. For that reason it would be preferrable avoiding to hardcoded it here. Instead, read the value from an environment variable or keep the value in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "aws"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "shortlink": "https://sg.run/Ro22"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsage.test.jsx", "start": {"line": 20, "col": 9, "offset": 840}, "end": {"line": 20, "col": 34, "offset": 865}, "extra": {"message": "AWS Account ID detected. While not considered sensitive information, it is important to use them and share them carefully. For that reason it would be preferrable avoiding to hardcoded it here. Instead, read the value from an environment variable or keep the value in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "aws"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "shortlink": "https://sg.run/Ro22"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsage.test.jsx", "start": {"line": 33, "col": 9, "offset": 1398}, "end": {"line": 33, "col": 34, "offset": 1423}, "extra": {"message": "AWS Account ID detected. While not considered sensitive information, it is important to use them and share them carefully. For that reason it would be preferrable avoiding to hardcoded it here. Instead, read the value from an environment variable or keep the value in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "aws"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "shortlink": "https://sg.run/Ro22"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsers.test.jsx", "start": {"line": 41, "col": 9, "offset": 1735}, "end": {"line": 41, "col": 34, "offset": 1760}, "extra": {"message": "AWS Account ID detected. While not considered sensitive information, it is important to use them and share them carefully. For that reason it would be preferrable avoiding to hardcoded it here. Instead, read the value from an environment variable or keep the value in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "aws"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-aws-account-id.detected-aws-account-id", "shortlink": "https://sg.run/Ro22"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/leases.js", "start": {"line": 18, "col": 13, "offset": 557}, "end": {"line": 18, "col": 76, "offset": 620}, "extra": {"message": "RegExp() called with a `action` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/default-nuke-config-template.yml:2:\n (approximate error location; error nearby after) error calling parser: block sequence entries are not allowed in this context character 0 position 0 returned: 0", "path": "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/default-nuke-config-template.yml"}], "paths": {"scanned": ["downloaded_repos/awslabs_sandbox-accounts-for-events/.eslintignore", "downloaded_repos/awslabs_sandbox-accounts-for-events/.gitignore", "downloaded_repos/awslabs_sandbox-accounts-for-events/.graphqlconfig.yml", "downloaded_repos/awslabs_sandbox-accounts-for-events/CHANGELOG.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/CODE_OF_CONDUCT.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/CONTRIBUTING.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/LICENSE", "downloaded_repos/awslabs_sandbox-accounts-for-events/Makefile", "downloaded_repos/awslabs_sandbox-accounts-for-events/NOTICE", "downloaded_repos/awslabs_sandbox-accounts-for-events/README.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/.config/project-config.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/api/AdminQueries/cli-inputs.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/api/safegraphql/cli-inputs.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/api/safegraphql/parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/api/safegraphql/resolvers/README.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/api/safegraphql/schema.graphql", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/api/safegraphql/stacks/CustomResources.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/api/safegraphql/transform.conf.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/auth/safeafe5208f/cli-inputs.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/auth/userPoolGroups/parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/auth/userPoolGroups/template.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/auth/userPoolGroups/user-pool-group-precedence.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/backend-config.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/custom/cfnAdminUser/cfnAdminUser-cloudformation-template.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/custom/cfnAdminUser/parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/AdminQueries67383b4c-cloudformation-template.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/amplify.state", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/src/app.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/src/cognitoActions.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/src/index.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/src/package-lock.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/src/package.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/amplify.state", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/custom-policies.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/function-parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/safeAdminApi-cloudformation-template.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/src/event.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/src/index.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/src/package-lock.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/src/package.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/amplify.state", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/custom-policies.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/function-parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/safeLoginApi-cloudformation-template.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/src/event.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/src/index.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/src/package-lock.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/src/package.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/amplify.state", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/custom-policies.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/function-parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/parameters.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/safeOperatorApi-cloudformation-template.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/src/event.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/src/index.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/src/package-lock.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/src/package.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/tags.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/types/amplify-dependent-resources-ref.d.ts", "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/cli.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/buildspec-deploy.yaml", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/buildspec-destroy.yaml", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/buildspec.yml", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/default-nuke-config-template.yml", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/fix-cleanup.sh", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/post-cleanup.sh", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/pre-cleanup.sh", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/modules/dce.tfvars", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/modules/dynamodb.tf", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/modules/fixtures/policies/principal_policy.tmpl", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/scripts/build.sh", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/scripts/terraform-deploy.sh", "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/scripts/terraform-destroy.sh", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/FAQ.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/accounts.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/admin.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/concept.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/cleanup-process.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/config.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/create-event.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/create-lease.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/create-user.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/details-event.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/details-leases.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/edit-account.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/edit-event.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/edit-lease.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/edit-user.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/event-login.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/lease-login.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/login-options.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/register-accounts.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/stats-account-pool.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/stats-average.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/stats-events.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/stats-lease-termination.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/stats-leases.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/stats-spend.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-accounts.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-events.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-leases-accounts.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-leases-events.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-leases-users.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-leases.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-usage.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/images/table-users.png", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/login.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/operator.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/docs/user.md", "downloaded_repos/awslabs_sandbox-accounts-for-events/install/buildspec-amplify.yaml", "downloaded_repos/awslabs_sandbox-accounts-for-events/install/cfn-lambda/dceHandleAmplifyDeployment/cfnresponse.py", "downloaded_repos/awslabs_sandbox-accounts-for-events/install/cfn-lambda/dceHandleAmplifyDeployment/index.py", "downloaded_repos/awslabs_sandbox-accounts-for-events/install/cfn-lambda/dceHandleTerraFormDeployment/cfnresponse.py", "downloaded_repos/awslabs_sandbox-accounts-for-events/install/cfn-lambda/dceHandleTerraFormDeployment/index.py", "downloaded_repos/awslabs_sandbox-accounts-for-events/install/sandbox-accounts-for-events-install.yaml", "downloaded_repos/awslabs_sandbox-accounts-for-events/package-lock.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/package.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/public/favicon.ico", "downloaded_repos/awslabs_sandbox-accounts-for-events/public/index.html", "downloaded_repos/awslabs_sandbox-accounts-for-events/public/manifest.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/public/robots.txt", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/App.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/App.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/graphql/mutations.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/graphql/queries.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/graphql/schema.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/graphql/subscriptions.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/index.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/AdminConfig.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/AdminConfig.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/DetailEvent.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/DetailEvent.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/Home.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/Home.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverViewLeases.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewAccounts.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewAccounts.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewEvents.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewEvents.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewLeases.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsage.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsage.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsers.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsers.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/Statistics.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/Statistics.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/CopyClipboardIconButton.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/EmptyState.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/EventLogin.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/GitHubLinks.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/LeaseLogin.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/Navigation.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/NotificationFlashbar.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/TopMenuBar.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/labels.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/table-config-leases.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/termsAndConditionsText.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/components/utils.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/AwsLoginModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/ConfirmationModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/ConfirmationModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/CreateEventModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/CreateEventModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/CreateLeaseModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/CreateLeaseModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/CreateUserModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/CreateUserModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditAccountModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditAccountModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditEventModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditEventModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditLeaseModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditLeaseModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditUserModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/EditUserModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/LogModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/RegisterAccountsModal.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/modals/RegisterAccountsModal.test.jsx", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/accounts.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/aws_login.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/config.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/current_user.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/leases.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/logs.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/modal.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/notification.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/selection.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/statistics.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/usage.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/users.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/store.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/setupTests.js", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/styles/home-page.scss", "downloaded_repos/awslabs_sandbox-accounts-for-events/src/styles/index.css", "downloaded_repos/awslabs_sandbox-accounts-for-events/templates/children-scp.json", "downloaded_repos/awslabs_sandbox-accounts-for-events/templates/destroy-dce-manually.sh", "downloaded_repos/awslabs_sandbox-accounts-for-events/templates/principal_policy.tmpl", "downloaded_repos/awslabs_sandbox-accounts-for-events/vite.config.js"], "skipped": [{"path": "downloaded_repos/awslabs_sandbox-accounts-for-events/dce-integration/cmd/codebuild/reset/default-nuke-config-template.yml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.****************, "profiling_times": {"config_time": 5.***************, "core_time": 6.***************, "ignores_time": 0.001870870590209961, "total_time": 12.***************}, "parsing_time": {"total_time": 6.***************, "per_file_time": {"mean": 0.*****************, "std_dev": 0.*****************}, "very_slow_stats": {"time_ratio": 0.*****************, "count_ratio": 0.006993006993006993}, "very_slow_files": [{"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/package-lock.json", "ftime": 1.****************}]}, "scanning_time": {"total_time": 42.**************, "per_file_time": {"mean": 0.*****************, "std_dev": 0.*****************}, "very_slow_stats": {"time_ratio": 0.****************, "count_ratio": 0.*****************}, "very_slow_files": [{"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/accounts.js", "ftime": 1.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeAdminApi/src/index.js", "ftime": 2.***************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/package-lock.json", "ftime": 2.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/src/index.js", "ftime": 2.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/src/index.js", "ftime": 2.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "ftime": 3.***************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/package-lock.json", "ftime": 3.****************}]}, "matching_time": {"total_time": 16.**************, "per_file_and_rule_time": {"mean": 0.020050228442610098, "std_dev": 0.0023002816272315006}, "very_slow_stats": {"time_ratio": 0.****************, "count_ratio": 0.*****************}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewUsers.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewEvents.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/DetailEvent.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeOperatorApi/src/index.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/OverviewEvents.jsx", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/pages/DetailEvent.jsx", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/package-lock.json", "rule_id": "json.aws.security.public-s3-policy-statement.public-s3-policy-statement", "time": 0.*****************}]}, "tainting_time": {"total_time": 5.***************, "per_def_and_rule_time": {"mean": 0.017683690844607268, "std_dev": 0.0006789405543758633}, "very_slow_stats": {"time_ratio": 0.****************, "count_ratio": 0.*****************}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/AdminQueries67383b4c/src/app.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/statistics.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/amplify/backend/function/safeLoginApi/src/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.*****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/events.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.****************}, {"fpath": "downloaded_repos/awslabs_sandbox-accounts-for-events/src/redux/actions/accounts.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.*****************}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": **********}, "engine_requested": "OSS", "skipped_rules": []}