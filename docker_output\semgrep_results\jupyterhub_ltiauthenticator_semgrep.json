{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/jupyterhub_ltiauthenticator/.flake8", "downloaded_repos/jupyterhub_ltiauthenticator/.github/dependabot.yaml", "downloaded_repos/jupyterhub_ltiauthenticator/.github/workflows/publish.yaml", "downloaded_repos/jupyterhub_ltiauthenticator/.github/workflows/test-docs.yaml", "downloaded_repos/jupyterhub_ltiauthenticator/.github/workflows/test-package.yaml", "downloaded_repos/jupyterhub_ltiauthenticator/.gitignore", "downloaded_repos/jupyterhub_ltiauthenticator/.pre-commit-config.yaml", "downloaded_repos/jupyterhub_ltiauthenticator/.readthedocs.yaml", "downloaded_repos/jupyterhub_ltiauthenticator/CHANGELOG.md", "downloaded_repos/jupyterhub_ltiauthenticator/CONTRIBUTING.md", "downloaded_repos/jupyterhub_ltiauthenticator/LICENSE", "downloaded_repos/jupyterhub_ltiauthenticator/README.md", "downloaded_repos/jupyterhub_ltiauthenticator/RELEASE.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/Makefile", "downloaded_repos/jupyterhub_ltiauthenticator/docs/README.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/make.bat", "downloaded_repos/jupyterhub_ltiauthenticator/docs/requirements.txt", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/_static/custom.css", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/_static/images/logo/favicon.ico", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/_static/images/logo/logo.png", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/conf.py", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/index.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti11/getting-started.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti11/index.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti11/lms-integration.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti11/reference.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti13/getting-started.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti13/index.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti13/lms-integration.md", "downloaded_repos/jupyterhub_ltiauthenticator/docs/source/lti13/reference.md", "downloaded_repos/jupyterhub_ltiauthenticator/examples/README.md", "downloaded_repos/jupyterhub_ltiauthenticator/examples/jupyterhub_config_lti11.py", "downloaded_repos/jupyterhub_ltiauthenticator/examples/jupyterhub_config_lti13.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/__init__.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/_version.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti11/__init__.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti11/auth.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti11/constants.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti11/handlers.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti11/templates.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti11/validator.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti13/__init__.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti13/auth.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti13/constants.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti13/error.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti13/handlers.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/lti13/validator.py", "downloaded_repos/jupyterhub_ltiauthenticator/ltiauthenticator/utils.py", "downloaded_repos/jupyterhub_ltiauthenticator/pyproject.toml"], "skipped": [{"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti11/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti11/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti11/mocking.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti11/test_lti11_authenticator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti11/test_lti11_handlers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti11/test_lti11_validator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti13/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti13/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti13/mocking.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti13/stash_of_unused_fixtures.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti13/test_lti13_authenticator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti13/test_lti13_handlers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/lti13/test_lti13_validator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_ltiauthenticator/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8875298500061035, "profiling_times": {"config_time": 6.722822904586792, "core_time": 3.1191728115081787, "ignores_time": 0.0018138885498046875, "total_time": 9.8448646068573}, "parsing_time": {"total_time": 0.41805076599121094, "per_file_time": {"mean": 0.017418781916300457, "std_dev": 0.0001590550086171384}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.4019668102264404, "per_file_time": {"mean": 0.027884973854315084, "std_dev": 0.005049358456645384}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.0908517837524414, "per_file_and_rule_time": {"mean": 0.0026606141067132714, "std_dev": 4.933229200761972e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.21076631546020508, "per_def_and_rule_time": {"mean": 0.0003372261047363281, "std_dev": 7.244358773351995e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089102016}, "engine_requested": "OSS", "skipped_rules": []}