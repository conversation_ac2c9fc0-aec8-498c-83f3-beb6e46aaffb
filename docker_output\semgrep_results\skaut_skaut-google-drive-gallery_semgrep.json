{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 41, "offset": 2546}, "end": {"line": 93, "col": 44, "offset": 2549}}, {"path": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 81, "offset": 2546}, "end": {"line": 93, "col": 84, "offset": 2549}}, {"path": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 105, "offset": 2546}, "end": {"line": 93, "col": 108, "offset": 2549}}, {"path": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 141, "offset": 2546}, "end": {"line": 93, "col": 144, "offset": 2549}}]], "message": "Syntax error at line downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml:93:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 41, "offset": 2546}, "end": {"line": 93, "col": 44, "offset": 2549}}, {"file": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 81, "offset": 2546}, "end": {"line": 93, "col": 84, "offset": 2549}}, {"file": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 105, "offset": 2546}, "end": {"line": 93, "col": 108, "offset": 2549}}, {"file": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "start": {"line": 93, "col": 141, "offset": 2546}, "end": {"line": 93, "col": 144, "offset": 2549}}]}], "paths": {"scanned": ["downloaded_repos/skaut_skaut-google-drive-gallery/.eslintrc.json", "downloaded_repos/skaut_skaut-google-drive-gallery/.github/dependabot.yml", "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/CI.yml", "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/asset-update.yml", "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/wordpress-version-checker.yml", "downloaded_repos/skaut_skaut-google-drive-gallery/.gitignore", "downloaded_repos/skaut_skaut-google-drive-gallery/.phan/config.php", "downloaded_repos/skaut_skaut-google-drive-gallery/.phan/stubs/php-scoper/class-directorynotfoundexception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/.phan/stubs/php-scoper/class-finder.php", "downloaded_repos/skaut_skaut-google-drive-gallery/.project-info.json", "downloaded_repos/skaut_skaut-google-drive-gallery/.wordpress-version-checker.json", "downloaded_repos/skaut_skaut-google-drive-gallery/LICENSE", "downloaded_repos/skaut_skaut-google-drive-gallery/README.md", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/banner-1544x500.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/banner-772x250.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/banner.svg", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/icon-128x128.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/icon-256x256.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/icon.svg", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/screenshot-1.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/screenshot-2.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/screenshot-3.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/screenshot-4.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/screenshot-5.png", "downloaded_repos/skaut_skaut-google-drive-gallery/assets/screenshot-6.png", "downloaded_repos/skaut_skaut-google-drive-gallery/bin/install-wp-tests.sh", "downloaded_repos/skaut_skaut-google-drive-gallery/block.vite.config.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/codecov.yml", "downloaded_repos/skaut_skaut-google-drive-gallery/composer.json", "downloaded_repos/skaut_skaut-google-drive-gallery/composer.lock", "downloaded_repos/skaut_skaut-google-drive-gallery/gulpfile.js", "downloaded_repos/skaut_skaut-google-drive-gallery/package-lock.json", "downloaded_repos/skaut_skaut-google-drive-gallery/package.json", "downloaded_repos/skaut_skaut-google-drive-gallery/phpcs.xml", "downloaded_repos/skaut_skaut-google-drive-gallery/phpmd.xml", "downloaded_repos/skaut_skaut-google-drive-gallery/phpstan.neon", "downloaded_repos/skaut_skaut-google-drive-gallery/phpunit.xml", "downloaded_repos/skaut_skaut-google-drive-gallery/root_selection.vite.config.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/scoper.inc.php", "downloaded_repos/skaut_skaut-google-drive-gallery/shortcode.vite.config.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/css/admin/options-grid.css", "downloaded_repos/skaut_skaut-google-drive-gallery/src/css/admin/options-root.css", "downloaded_repos/skaut_skaut-google-drive-gallery/src/css/admin/tinymce.css", "downloaded_repos/skaut_skaut-google-drive-gallery/src/css/frontend/block.css", "downloaded_repos/skaut_skaut-google-drive-gallery/src/css/frontend/shortcode.css", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/Directory.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/ErrorResponse.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/GalleryResponse.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/Image.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/ListGalleryDirResponse.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/ListGdriveDirResponse.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/PageResponse.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/PartialDirectory.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/ShortcodeToBlockTransformAttributes.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/Video.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/sgdgBlockLocalize.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/sgdgRootpathLocalize.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/sgdgShortcodeLocalize.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/sgdgTinymceLocalize.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/d.ts/thickbox.d.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/class-oauth-helpers.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/class-readonly-string-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/class-settings-pages.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/class-tinymce-plugin.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/advanced/class-grid.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/advanced/class-lightbox.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/basic/class-oauth-grant.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/basic/class-oauth-revoke.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/basic/class-root-selection.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/basic/root-selection/class-list-ajax-endpoint.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/class-advanced-settings.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/admin/settings-pages/class-basic-settings.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/class-api-client.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/class-api-facade.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/class-main.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/class-options.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-api-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-api-rate-limit-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-cant-edit-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-cant-manage-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-directory-not-found-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-drive-not-found-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-file-not-found-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-gallery-expired-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-internal-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-not-found-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-path-not-found-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-plugin-not-authorized-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-root-not-found-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/exceptions/class-unsupported-value-exception.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-api-fields.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-array-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-block.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-boolean-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-bounded-integer-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-code-string-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-gallery-context.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-gallery.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-integer-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-options-proxy.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-ordering-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-page.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-paging-pagination-helper.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-root-path-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-shortcode.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-single-page-pagination-helper.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-string-option.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/class-video-proxy.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/interface-pagination-helper.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/page/class-directories.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/page/class-images.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/frontend/page/class-videos.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/helpers/class-get-helpers.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/helpers/class-helpers.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/helpers/class-script-and-style-helpers.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/skaut-google-drive-gallery.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/php/uninstall.php", "downloaded_repos/skaut_skaut-google-drive-gallery/src/png/icon.png", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/admin/root_selection.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/admin/tinymce.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block/SgdgBlockIconComponent.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block/SgdgBooleanSettingsComponent.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block/SgdgEditorComponent.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block/SgdgIntegerSettingsComponent.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block/SgdgOrderingSettingsComponent.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block/SgdgSettingsComponent.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block/SgdgSettingsOverrideComponent.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/block.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/interfaces/Attributes.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/shortcode/QueryParameter.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/shortcode/Shortcode.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/shortcode/ShortcodeRegistry.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/shortcode.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/isError.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/printError.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/src/txt/license.txt", "downloaded_repos/skaut_skaut-google-drive-gallery/src/txt/readme.txt", "downloaded_repos/skaut_skaut-google-drive-gallery/stylelint.config.js", "downloaded_repos/skaut_skaut-google-drive-gallery/tinymce.vite.config.ts", "downloaded_repos/skaut_skaut-google-drive-gallery/tsconfig.json", "downloaded_repos/skaut_skaut-google-drive-gallery/vite-builder.config.ts"], "skipped": [{"path": "downloaded_repos/skaut_skaut-google-drive-gallery/.github/workflows/release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/skaut_skaut-google-drive-gallery/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/skaut_skaut-google-drive-gallery/tests/unit/Readonly_String_Option_Test.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7846601009368896, "profiling_times": {"config_time": 6.547712564468384, "core_time": 3.6368377208709717, "ignores_time": 0.0019006729125976562, "total_time": 10.187309980392456}, "parsing_time": {"total_time": 1.4865422248840332, "per_file_time": {"mean": 0.01281501918003477, "std_dev": 0.003093431454339517}, "very_slow_stats": {"time_ratio": 0.3985161210647617, "count_ratio": 0.008620689655172414}, "very_slow_files": [{"fpath": "downloaded_repos/skaut_skaut-google-drive-gallery/package-lock.json", "ftime": 0.5924110412597656}]}, "scanning_time": {"total_time": 6.0551838874816895, "per_file_time": {"mean": 0.014988078929410113, "std_dev": 0.003913790559955874}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.4482417106628418, "per_file_and_rule_time": {"mean": 0.003023469124557081, "std_dev": 9.966067751830484e-05}, "very_slow_stats": {"time_ratio": 0.16030545410006575, "count_ratio": 0.0041753653444676405}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/skaut_skaut-google-drive-gallery/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.10294103622436523}, {"fpath": "downloaded_repos/skaut_skaut-google-drive-gallery/src/ts/frontend/shortcode/Shortcode.ts", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.12922000885009766}]}, "tainting_time": {"total_time": 0.27149152755737305, "per_def_and_rule_time": {"mean": 0.0005644314502232287, "std_dev": 8.145001528860912e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}